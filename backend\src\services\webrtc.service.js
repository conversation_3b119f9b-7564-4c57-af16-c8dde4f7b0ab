const logger = require('../utils/logger');
const Call = require('../models/call.model');
const User = require('../models/user.model');
const { pubsub } = require('../graphql/pubsub');

class WebRTCService {
  constructor() {
    // Map pour stocker les connexions actives
    this.activeConnections = new Map();
    // Map pour stocker les appels en cours
    this.activeCalls = new Map();
    // Map pour stocker les candidats ICE en attente
    this.pendingIceCandidates = new Map();
  }

  // ✅ Initier un appel
  async initiateCall(callerId, recipientId, type = 'video') {
    logger.info(`[WebRTC] Initiating ${type} call from ${callerId} to ${recipientId}`);

    try {
      // Vérifier que les utilisateurs existent
      const [caller, recipient] = await Promise.all([
        User.findById(callerId).select('_id username image isOnline'),
        User.findById(recipientId).select('_id username image isOnline')
      ]);

      if (!caller || !recipient) {
        throw new Error('Caller or recipient not found');
      }

      // Vérifier que le destinataire est en ligne
      if (!recipient.isOnline) {
        throw new Error('Recipient is offline');
      }

      // Créer l'enregistrement d'appel
      const call = new Call({
        caller: callerId,
        recipient: recipientId,
        type,
        status: 'ringing',
        startTime: new Date()
      });

      await call.save();

      // Stocker l'appel actif
      this.activeCalls.set(call._id.toString(), {
        id: call._id.toString(),
        callerId,
        recipientId,
        type,
        status: 'ringing',
        startTime: new Date(),
        offer: null,
        answer: null
      });

      // Publier l'événement d'appel entrant
      await pubsub.publish('INCOMING_CALL', {
        incomingCall: {
          id: call._id.toString(),
          caller: {
            id: caller._id.toString(),
            username: caller.username,
            image: caller.image
          },
          type,
          status: 'ringing'
        },
        userId: recipientId
      });

      logger.info(`[WebRTC] Call initiated successfully: ${call._id}`);

      return {
        id: call._id.toString(),
        callerId,
        recipientId,
        type,
        status: 'ringing',
        caller: {
          id: caller._id.toString(),
          username: caller.username,
          image: caller.image
        }
      };

    } catch (error) {
      logger.error(`[WebRTC] Error initiating call: ${error.message}`);
      throw error;
    }
  }

  // ✅ Accepter un appel
  async acceptCall(callId, userId) {
    logger.info(`[WebRTC] User ${userId} accepting call ${callId}`);

    try {
      const call = await Call.findById(callId);
      if (!call) {
        throw new Error('Call not found');
      }

      if (call.recipient.toString() !== userId) {
        throw new Error('Unauthorized to accept this call');
      }

      if (call.status !== 'ringing') {
        throw new Error('Call is not in ringing state');
      }

      // Mettre à jour le statut
      call.status = 'accepted';
      await call.save();

      // Mettre à jour l'appel actif
      const activeCall = this.activeCalls.get(callId);
      if (activeCall) {
        activeCall.status = 'accepted';
      }

      // Publier l'événement d'appel accepté
      await pubsub.publish('CALL_ACCEPTED', {
        callAccepted: {
          id: callId,
          status: 'accepted'
        },
        userId: call.caller.toString()
      });

      logger.info(`[WebRTC] Call accepted: ${callId}`);
      return { id: callId, status: 'accepted' };

    } catch (error) {
      logger.error(`[WebRTC] Error accepting call: ${error.message}`);
      throw error;
    }
  }

  // ✅ Rejeter un appel
  async rejectCall(callId, userId) {
    logger.info(`[WebRTC] User ${userId} rejecting call ${callId}`);

    try {
      const call = await Call.findById(callId);
      if (!call) {
        throw new Error('Call not found');
      }

      if (call.recipient.toString() !== userId) {
        throw new Error('Unauthorized to reject this call');
      }

      // Mettre à jour le statut
      call.status = 'rejected';
      call.endTime = new Date();
      await call.save();

      // Supprimer l'appel actif
      this.activeCalls.delete(callId);

      // Publier l'événement d'appel rejeté
      await pubsub.publish('CALL_REJECTED', {
        callRejected: {
          id: callId,
          status: 'rejected'
        },
        userId: call.caller.toString()
      });

      logger.info(`[WebRTC] Call rejected: ${callId}`);
      return { id: callId, status: 'rejected' };

    } catch (error) {
      logger.error(`[WebRTC] Error rejecting call: ${error.message}`);
      throw error;
    }
  }

  // ✅ Terminer un appel
  async endCall(callId, userId) {
    logger.info(`[WebRTC] User ${userId} ending call ${callId}`);

    try {
      const call = await Call.findById(callId);
      if (!call) {
        throw new Error('Call not found');
      }

      const isParticipant = call.caller.toString() === userId || call.recipient.toString() === userId;
      if (!isParticipant) {
        throw new Error('Unauthorized to end this call');
      }

      // Mettre à jour le statut
      call.status = 'ended';
      call.endTime = new Date();
      
      // Calculer la durée si l'appel était connecté
      if (call.connectedTime) {
        call.duration = Math.floor((call.endTime - call.connectedTime) / 1000);
      }

      await call.save();

      // Supprimer l'appel actif
      this.activeCalls.delete(callId);
      this.pendingIceCandidates.delete(callId);

      // Publier l'événement d'appel terminé
      const otherUserId = call.caller.toString() === userId ? call.recipient.toString() : call.caller.toString();
      
      await pubsub.publish('CALL_ENDED', {
        callEnded: {
          id: callId,
          status: 'ended',
          duration: call.duration || 0
        },
        userId: otherUserId
      });

      logger.info(`[WebRTC] Call ended: ${callId}, duration: ${call.duration || 0}s`);
      return { id: callId, status: 'ended', duration: call.duration || 0 };

    } catch (error) {
      logger.error(`[WebRTC] Error ending call: ${error.message}`);
      throw error;
    }
  }

  // ✅ Gérer l'offre SDP
  async handleOffer(callId, userId, offer) {
    logger.info(`[WebRTC] Handling offer for call ${callId} from user ${userId}`);

    try {
      const activeCall = this.activeCalls.get(callId);
      if (!activeCall) {
        throw new Error('Call not found');
      }

      if (activeCall.callerId !== userId) {
        throw new Error('Only caller can send offer');
      }

      // Stocker l'offre
      activeCall.offer = offer;

      // Mettre à jour en base de données
      await Call.findByIdAndUpdate(callId, { offer: JSON.stringify(offer) });

      // Publier l'offre au destinataire
      await pubsub.publish('WEBRTC_OFFER', {
        webrtcOffer: {
          callId,
          offer
        },
        userId: activeCall.recipientId
      });

      logger.info(`[WebRTC] Offer sent for call ${callId}`);
      return { success: true };

    } catch (error) {
      logger.error(`[WebRTC] Error handling offer: ${error.message}`);
      throw error;
    }
  }

  // ✅ Gérer la réponse SDP
  async handleAnswer(callId, userId, answer) {
    logger.info(`[WebRTC] Handling answer for call ${callId} from user ${userId}`);

    try {
      const activeCall = this.activeCalls.get(callId);
      if (!activeCall) {
        throw new Error('Call not found');
      }

      if (activeCall.recipientId !== userId) {
        throw new Error('Only recipient can send answer');
      }

      // Stocker la réponse
      activeCall.answer = answer;
      activeCall.status = 'connected';

      // Mettre à jour en base de données
      await Call.findByIdAndUpdate(callId, { 
        answer: JSON.stringify(answer),
        status: 'connected',
        connectedTime: new Date()
      });

      // Publier la réponse à l'appelant
      await pubsub.publish('WEBRTC_ANSWER', {
        webrtcAnswer: {
          callId,
          answer
        },
        userId: activeCall.callerId
      });

      // Publier l'événement de connexion établie
      await pubsub.publish('CALL_CONNECTED', {
        callConnected: {
          id: callId,
          status: 'connected'
        },
        userId: activeCall.callerId
      });

      logger.info(`[WebRTC] Answer sent and call connected: ${callId}`);
      return { success: true };

    } catch (error) {
      logger.error(`[WebRTC] Error handling answer: ${error.message}`);
      throw error;
    }
  }

  // ✅ Gérer les candidats ICE
  async handleIceCandidate(callId, userId, candidate) {
    logger.debug(`[WebRTC] Handling ICE candidate for call ${callId} from user ${userId}`);

    try {
      const activeCall = this.activeCalls.get(callId);
      if (!activeCall) {
        throw new Error('Call not found');
      }

      const isParticipant = activeCall.callerId === userId || activeCall.recipientId === userId;
      if (!isParticipant) {
        throw new Error('Unauthorized to send ICE candidate');
      }

      // Déterminer le destinataire
      const recipientId = activeCall.callerId === userId ? activeCall.recipientId : activeCall.callerId;

      // Publier le candidat ICE
      await pubsub.publish('WEBRTC_ICE_CANDIDATE', {
        webrtcIceCandidate: {
          callId,
          candidate
        },
        userId: recipientId
      });

      logger.debug(`[WebRTC] ICE candidate sent for call ${callId}`);
      return { success: true };

    } catch (error) {
      logger.error(`[WebRTC] Error handling ICE candidate: ${error.message}`);
      throw error;
    }
  }

  // ✅ Obtenir l'état d'un appel
  getCallState(callId) {
    return this.activeCalls.get(callId) || null;
  }

  // ✅ Nettoyer les appels expirés
  cleanupExpiredCalls() {
    const now = new Date();
    const CALL_TIMEOUT = 60 * 1000; // 1 minute

    for (const [callId, call] of this.activeCalls.entries()) {
      if (call.status === 'ringing' && (now - call.startTime) > CALL_TIMEOUT) {
        logger.info(`[WebRTC] Cleaning up expired call: ${callId}`);
        this.endCall(callId, call.callerId).catch(err => {
          logger.error(`[WebRTC] Error cleaning up call ${callId}: ${err.message}`);
        });
      }
    }
  }
}

// Nettoyer les appels expirés toutes les 30 secondes
const webrtcService = new WebRTCService();
setInterval(() => {
  webrtcService.cleanupExpiredCalls();
}, 30000);

module.exports = webrtcService;
