{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nexport let MessageChatComponent = class MessageChatComponent {\n  constructor(route, router, authUserService, MessageService, toastService, fb, cdr, userStatusService) {\n    this.route = route;\n    this.router = router;\n    this.authUserService = authUserService;\n    this.MessageService = MessageService;\n    this.toastService = toastService;\n    this.fb = fb;\n    this.cdr = cdr;\n    this.userStatusService = userStatusService;\n    // === PROPRIÉTÉS PRINCIPALES ===\n    this.messages = [];\n    this.conversation = null;\n    this.loading = true;\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    this.isAdmin = false;\n    this.selectedFile = null;\n    this.previewUrl = null;\n    this.isUploading = false;\n    this.isTyping = false;\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    // === PAGINATION ET CHARGEMENT ===\n    this.MAX_MESSAGES_PER_SIDE = 5;\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.MAX_TOTAL_MESSAGES = 100;\n    this.currentPage = 1;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.subscriptions = new Subscription();\n    // === INTERFACE ET THÈMES ===\n    this.selectedTheme = 'theme-default';\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n    this.showEmojiPicker = false;\n    this.showSearchBar = false;\n    this.showPinnedMessages = false;\n    this.showStatusSelector = false;\n    this.showNotificationPanel = false;\n    this.showNotificationSettings = false;\n    this.showUserStatusPanel = false;\n    this.showCallHistoryPanel = false;\n    this.showCallStatsPanel = false;\n    this.showVoiceMessagesPanel = false;\n    // === APPELS ===\n    this.incomingCall = null;\n    this.activeCall = null;\n    this.showCallModal = false;\n    this.showActiveCallModal = false;\n    this.isCallMuted = false;\n    this.isVideoEnabled = true;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // === NOTIFICATIONS ET MESSAGES ===\n    this.notifications = [];\n    this.unreadNotificationCount = 0;\n    this.selectedNotifications = new Set();\n    this.showDeleteConfirmModal = false;\n    this.editingMessageId = null;\n    this.editingContent = '';\n    this.replyingToMessage = null;\n    // === RECHERCHE ===\n    this.searchQuery = '';\n    this.isSearching = false;\n    this.searchMode = false;\n    this.searchResults = [];\n    this.showSearch = false;\n    this.showAdvancedSearch = false;\n    // === PANNEAUX ===\n    this.pinnedMessages = [];\n    this.showReactionPicker = {};\n    this.showDeleteConfirm = {};\n    this.showPinConfirm = {};\n    this.isPinning = {};\n    this.showMessageOptions = {};\n    // === TRANSFERT ===\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n    this.availableConversations = [];\n    this.isForwarding = false;\n    this.isLoadingConversations = false;\n    // === ÉMOJIS ET AUTOCOLLANTS ===\n    this.emojiCategories = [{\n      id: 'recent',\n      name: 'Récents',\n      icon: 'fas fa-clock'\n    }, {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: 'fas fa-smile'\n    }, {\n      id: 'people',\n      name: 'Personnes',\n      icon: 'fas fa-user'\n    }, {\n      id: 'animals',\n      name: 'Animaux',\n      icon: 'fas fa-paw'\n    }, {\n      id: 'food',\n      name: 'Nourriture',\n      icon: 'fas fa-apple-alt'\n    }, {\n      id: 'travel',\n      name: 'Voyage',\n      icon: 'fas fa-plane'\n    }, {\n      id: 'activities',\n      name: 'Activités',\n      icon: 'fas fa-football-ball'\n    }, {\n      id: 'objects',\n      name: 'Objets',\n      icon: 'fas fa-lightbulb'\n    }, {\n      id: 'symbols',\n      name: 'Symboles',\n      icon: 'fas fa-heart'\n    }, {\n      id: 'flags',\n      name: 'Drapeaux',\n      icon: 'fas fa-flag'\n    }];\n    this.selectedEmojiCategory = 'recent';\n    this.emojiSearchQuery = '';\n    this.recentEmojis = [];\n    this.previewedEmoji = null;\n    this.showStickerPicker = false;\n    this.stickerPacks = [];\n    this.selectedStickerPack = '';\n    // === GIFS ===\n    this.showGifPicker = false;\n    this.gifSearchQuery = '';\n    this.gifCategories = ['Trending', 'Reactions', 'Animals', 'Sports', 'TV & Movies'];\n    this.selectedGifCategory = 'Trending';\n    // === OUTIL DE DESSIN ===\n    this.showDrawingTool = false;\n    this.selectedDrawingTool = 'pen';\n    this.drawingColors = ['#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#FFFFFF'];\n    this.selectedDrawingColor = '#000000';\n    this.customDrawingColor = '#000000';\n    this.drawingSize = 5;\n    // === CAMÉRA ===\n    this.showCamera = false;\n    this.cameraMode = 'photo';\n    this.flashEnabled = false;\n    this.showCameraGrid = false;\n    this.showFocusIndicator = false;\n    this.focusX = 0;\n    this.focusY = 0;\n    this.lastCapturedImage = '';\n    this.isRecordingVideo = false;\n    this.videoRecordingDuration = 0;\n    // === ÉDITEUR D'IMAGES ===\n    this.showImageEditor = false;\n    this.imageEditorMode = 'crop';\n    this.cropArea = {\n      x: 0,\n      y: 0,\n      width: 100,\n      height: 100\n    };\n    this.cropRatio = 'free';\n    this.imageFilters = [{\n      name: 'none',\n      label: 'Aucun',\n      css: 'none'\n    }, {\n      name: 'grayscale',\n      label: 'Noir & Blanc',\n      css: 'grayscale(100%)'\n    }, {\n      name: 'sepia',\n      label: 'Sépia',\n      css: 'sepia(100%)'\n    }, {\n      name: 'vintage',\n      label: 'Vintage',\n      css: 'sepia(50%) contrast(1.2)'\n    }, {\n      name: 'bright',\n      label: 'Lumineux',\n      css: 'brightness(1.3)'\n    }, {\n      name: 'contrast',\n      label: 'Contraste',\n      css: 'contrast(1.5)'\n    }];\n    this.selectedImageFilter = 'none';\n    this.imageAdjustments = {\n      brightness: 0,\n      contrast: 0,\n      saturation: 0,\n      hue: 0\n    };\n    this.imageTextElements = [];\n    this.newTextContent = '';\n    this.textFontFamily = 'Arial';\n    this.textFontSize = 24;\n    this.textColor = '#000000';\n    this.availableFonts = [{\n      value: 'Arial',\n      label: 'Arial'\n    }, {\n      value: 'Helvetica',\n      label: 'Helvetica'\n    }, {\n      value: 'Times New Roman',\n      label: 'Times New Roman'\n    }, {\n      value: 'Courier New',\n      label: 'Courier New'\n    }];\n    // === GESTIONNAIRE DE FICHIERS ===\n    this.showFileManager = false;\n    this.fileViewMode = 'grid';\n    this.fileFolders = [];\n    this.selectedFolder = null;\n    this.fileBreadcrumbs = [];\n    this.fileSearchQuery = '';\n    this.fileTypeFilter = '';\n    this.fileSortBy = 'name';\n    this.selectedFiles = [];\n    // === ANALYTICS ===\n    this.showAnalyticsDashboard = false;\n    this.analyticsTimeRange = '7d';\n    this.analyticsData = {\n      totalMessages: 0,\n      activeUsers: 0,\n      avgResponseTime: '0s',\n      filesShared: 0,\n      messagesChange: 0,\n      usersChange: 0,\n      responseTimeChange: 0,\n      filesChange: 0,\n      topUsers: [],\n      messageTypes: [],\n      longestConversation: 0,\n      avgConversationDuration: '0m',\n      topEmojis: []\n    };\n    // === INTÉGRATIONS ===\n    this.showIntegrations = false;\n    this.integrationCategories = ['CRM', 'Productivité', 'Communication', 'Analytics', 'Sécurité'];\n    this.selectedIntegrationCategory = 'CRM';\n    this.webhooks = [];\n    // === ZONE DE SAISIE AVANCÉE ===\n    this.showQuickReplies = false;\n    this.quickReplies = ['Merci !', \"D'accord\", 'Parfait', 'À bientôt'];\n    this.showMentionSuggestions = false;\n    this.mentionSuggestions = [];\n    this.selectedMentionIndex = 0;\n    this.activeMentions = [];\n    this.showHashtagSuggestions = false;\n    this.hashtagSuggestions = [];\n    this.selectedHashtagIndex = 0;\n    this.activeHashtags = [];\n    this.showCommandSuggestions = false;\n    this.commandSuggestions = [];\n    this.selectedCommandIndex = 0;\n    this.activeLinks = [];\n    this.inputHeight = 40;\n    this.showFormattingToolbar = false;\n    this.detectedLanguage = 'fr';\n    this.autoCorrections = [];\n    this.showAutoCorrectSuggestions = false;\n    this.autoCorrectSuggestions = [];\n    // === TRADUCTION ===\n    this.showTranslationPanel = false;\n    this.translationFrom = 'auto';\n    this.translationTo = 'fr';\n    this.originalTextForTranslation = '';\n    this.translatedText = '';\n    this.isTranslating = false;\n    this.supportedLanguages = [{\n      code: 'fr',\n      name: 'Français'\n    }, {\n      code: 'en',\n      name: 'English'\n    }, {\n      code: 'es',\n      name: 'Español'\n    }, {\n      code: 'de',\n      name: 'Deutsch'\n    }, {\n      code: 'it',\n      name: 'Italiano'\n    }];\n    // === SONDAGES ===\n    this.showPollCreator = false;\n    this.pollQuestion = '';\n    this.pollOptions = [{\n      text: ''\n    }, {\n      text: ''\n    }];\n    this.pollSettings = {\n      allowMultiple: false,\n      anonymous: false,\n      showResults: true,\n      showVoters: true\n    };\n    this.pollExpiry = '';\n    this.customPollExpiry = '';\n    // === LOCALISATION ===\n    this.showLocationPicker = false;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n    this.locationSearchResults = [];\n    this.selectedLocation = null;\n    this.showLocationMessage = false;\n    this.locationMessage = '';\n    // === CONTACTS ===\n    this.showContactPicker = false;\n    this.contactSearchQuery = '';\n    this.selectedContactForSharing = null;\n    // === PROGRAMMATION D'ENVOI ===\n    this.showScheduleMessage = false;\n    this.customScheduleDate = '';\n    this.customScheduleTime = '';\n    this.scheduleTimezone = 'Europe/Paris';\n    this.availableTimezones = [{\n      value: 'Europe/Paris',\n      label: 'Paris (CET)'\n    }, {\n      value: 'America/New_York',\n      label: 'New York (EST)'\n    }, {\n      value: 'Asia/Tokyo',\n      label: 'Tokyo (JST)'\n    }];\n    // === ÉTATS DES MESSAGES ===\n    this.highlightedMessageId = '';\n    this.searchResultIds = [];\n    this.selectionMode = false;\n    this.selectedMessages = new Set();\n    this.hoveredMessageId = '';\n    this.playingVoiceId = '';\n    this.showScrollToBottom = false;\n    this.unreadMessagesCount = 0;\n    // === VISUALISEUR D'IMAGES ===\n    this.showImageViewer = false;\n    this.currentImageIndex = 0;\n    this.imageGallery = [];\n    // === TOAST ===\n    this.showToast = false;\n    this.toastMessage = '';\n    this.toastType = 'info';\n    // === PANNEAUX D'INFORMATIONS ===\n    this.showConversationInfo = false;\n    this.showConversationSettings = false;\n    this.conversationSettings = {\n      notifications: true,\n      soundNotifications: true,\n      readReceipts: true,\n      typingIndicators: true\n    };\n    // === VARIABLES D'ÉTAT POUR LES NOUVELLES FONCTIONNALITÉS ===\n    this.isSendingMessage = false;\n    this.isUserTyping = false;\n    this.voiceRecordingState = 'idle';\n    this.voiceRecordingSize = 0;\n    this.recordingWaveform = [];\n    this.voiceRecordingQuality = 'medium';\n    this.voiceEffects = [];\n    this.selectedContacts = [];\n    this.isUpdatingStatus = false;\n    // === VARIABLES POUR LES PIÈCES JOINTES ===\n    this.showAttachmentMenu = false;\n    this.attachmentFiles = [];\n    this.fileCaptions = [];\n    this.fileUploadProgress = [];\n    // === PROPRIÉTÉS POUR LE TEMPLATE ===\n    this.messageContent = '';\n    // === CONSTANTES OPTIMISÉES ===\n    this.c = {\n      reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n      delays: {\n        away: 300000,\n        typing: 3000,\n        scroll: 100,\n        anim: 300\n      },\n      error: 'Erreur. Veuillez réessayer.',\n      trackById: (i, item) => item?.id || i.toString(),\n      notifications: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      status: {\n        online: {\n          text: 'En ligne',\n          color: 'text-green-500',\n          icon: 'fas fa-circle'\n        },\n        offline: {\n          text: 'Hors ligne',\n          color: 'text-gray-500',\n          icon: 'far fa-circle'\n        },\n        away: {\n          text: 'Absent',\n          color: 'text-yellow-500',\n          icon: 'fas fa-clock'\n        },\n        busy: {\n          text: 'Occupé',\n          color: 'text-red-500',\n          icon: 'fas fa-minus-circle'\n        }\n      },\n      themes: [{\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad'\n      }, {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d'\n      }, {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6'\n      }, {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f'\n      }],\n      calls: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      }\n    };\n    this._selectedMessages = [];\n    // === MÉTHODES POUR LES MODALS ===\n    this.isCallMinimized = false;\n    this.isInCall = false;\n    this.inputFocused = false;\n    this.forwardSearchQuery = '';\n    this.showCallControls = true;\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // === GETTERS ===\n  get availableReactions() {\n    return this.c.reactions;\n  }\n  get commonEmojis() {\n    return this.MessageService.getCommonEmojis();\n  }\n  get unreadNotificationsCount() {\n    return this.unreadNotificationCount;\n  }\n  get currentUserStatus() {\n    return 'online';\n  }\n  get onlineUsersCount() {\n    return 5;\n  }\n  // === LIFECYCLE HOOKS ===\n  ngOnInit() {\n    this.initializeComponent();\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    if (this.callTimer) clearInterval(this.callTimer);\n    if (this.typingTimeout) clearTimeout(this.typingTimeout);\n  }\n  ngAfterViewChecked() {\n    this.cdr.detectChanges();\n  }\n  // === INITIALISATION ===\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n    // Initialiser des données de test pour la démo\n    this.initializeTestData();\n  }\n  loadConversation() {\n    this.route.params.subscribe(params => {\n      const conversationId = params['id'];\n      if (conversationId) {\n        this.getConversation(conversationId);\n        this.getMessages();\n      }\n    });\n  }\n  setupSubscriptions() {\n    // Configuration des abonnements WebSocket et autres\n  }\n  // === GESTION DES CONVERSATIONS ===\n  getConversation(conversationId) {\n    this.loading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        this.conversation = conversation;\n        this.otherParticipant = conversation.participants?.find(p => p.id !== this.currentUserId) || null;\n        this.loading = false;\n      },\n      error: error => {\n        this.error = error;\n        this.loading = false;\n        console.error('Erreur lors du chargement de la conversation:', error);\n      }\n    });\n  }\n  // === GESTION DES MESSAGES ===\n  getMessages() {\n    if (!this.conversation?.id) return;\n    this.MessageService.getMessages(this.conversation.id, this.currentPage.toString(), this.MAX_MESSAGES_TO_LOAD.toString()).subscribe({\n      next: messages => {\n        if (this.currentPage === 1) {\n          this.messages = messages;\n        } else {\n          this.messages = [...messages, ...this.messages];\n        }\n        this.hasMoreMessages = messages.length === this.MAX_MESSAGES_TO_LOAD;\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n      }\n    });\n  }\n  sendMessage() {\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content || !this.conversation?.id) return;\n    this.isSendingMessage = true;\n    // Créer un message temporaire pour l'affichage immédiat\n    const tempMessage = {\n      id: Date.now().toString(),\n      content,\n      sender: {\n        id: this.currentUserId,\n        username: this.currentUsername\n      },\n      timestamp: new Date(),\n      conversationId: this.conversation.id,\n      type: 'text'\n    };\n    this.messages.push(tempMessage);\n    this.messageForm.reset();\n    this.scrollToBottom();\n    this.isSendingMessage = false;\n    // Simuler l'envoi réel (à remplacer par l'appel API réel)\n    /*\n    this.MessageService.sendMessage(\n      this.conversation.id,\n      content,\n      'text' as MessageType,\n      this.currentUserId || ''\n    ).subscribe({\n      next: (message) => {\n        // Remplacer le message temporaire par le vrai message\n        const index = this.messages.findIndex(m => m.id === tempMessage.id);\n        if (index > -1) {\n          this.messages[index] = message;\n        }\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        // Supprimer le message temporaire en cas d'erreur\n        this.messages = this.messages.filter(m => m.id !== tempMessage.id);\n      },\n    });\n    */\n  }\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.attachmentFiles = Array.from(files);\n      this.generatePreviews();\n    }\n  }\n  removeAttachment(index) {\n    this.attachmentFiles.splice(index, 1);\n    if (this.attachmentFiles.length === 0) {\n      this.previewUrl = null;\n    }\n  }\n  // === GESTION DES RÉACTIONS ===\n  toggleReaction(messageId, emoji) {\n    // Simulation de la réaction - à implémenter avec le vrai service\n    const message = this.messages.find(m => m.id === messageId);\n    if (message) {\n      if (!message.reactions) message.reactions = [];\n      const existingReaction = message.reactions.find(r => r.emoji === emoji);\n      if (existingReaction) {\n        existingReaction.count = (existingReaction.count || 0) + 1;\n      } else {\n        message.reactions.push({\n          emoji,\n          count: 1,\n          users: [this.currentUserId]\n        });\n      }\n    }\n  }\n  addReaction(messageId, emoji) {\n    this.toggleReaction(messageId, emoji);\n    this.showReactionPicker = {};\n  }\n  // === GESTION DE L'ÉDITION ===\n  startEditMessage(message) {\n    this.editingMessageId = message.id;\n    this.editingContent = message.content || '';\n    this.showMessageOptions = {};\n  }\n  cancelEdit() {\n    this.editingMessageId = null;\n    this.editingContent = '';\n  }\n  saveEdit(messageId) {\n    if (this.editingContent?.trim()) {\n      this.MessageService.editMessage(messageId, this.editingContent.trim()).subscribe({\n        next: () => {\n          this.cancelEdit();\n          this.getMessages();\n        },\n        error: error => {\n          console.error('Erreur lors de la modification:', error);\n        }\n      });\n    }\n  }\n  onEditKeyDown(event, messageId) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.saveEdit(messageId);\n    } else if (event.key === 'Escape') {\n      this.cancelEdit();\n    }\n  }\n  // === GESTION DE LA SUPPRESSION ===\n  deleteMessage(messageId) {\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n      this.MessageService.deleteMessage(messageId).subscribe({\n        next: () => {\n          this.getMessages();\n          this.showMessageOptions = {};\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression:', error);\n        }\n      });\n    }\n  }\n  // === GESTION DES APPELS ===\n  startCall(type) {\n    if (!this.otherParticipant?.id) return;\n    this.MessageService.initiateCall(this.otherParticipant.id, type).subscribe({\n      next: call => {\n        this.activeCall = call;\n        this.showActiveCallModal = true;\n        this.startCallTimer();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'initiation de l'appel:\", error);\n        this.toastService.showError(\"Impossible d'initier l'appel\");\n      }\n    });\n  }\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n    }, 1000);\n  }\n  endCall() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.activeCall = null;\n    this.showActiveCallModal = false;\n    this.callDuration = 0;\n  }\n  toggleMute() {\n    this.isCallMuted = !this.isCallMuted;\n  }\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n  }\n  // === MÉTHODES UTILITAIRES ===\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  trackByMessageId(index, message) {\n    return message?.id || index.toString();\n  }\n  // === GESTION DE L'INTERFACE ===\n  toggleSearch() {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.clearSearch();\n    }\n  }\n  toggleNotifications() {\n    this.showNotificationPanel = !this.showNotificationPanel;\n  }\n  toggleConversationInfo() {\n    this.showConversationInfo = !this.showConversationInfo;\n  }\n  toggleThemeSelector() {\n    this.showThemeSelector = !this.showThemeSelector;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  toggleAdvancedSearch() {\n    this.showAdvancedSearch = !this.showAdvancedSearch;\n  }\n  toggleStatusSelector() {\n    this.showStatusSelector = !this.showStatusSelector;\n  }\n  toggleUserStatusPanel() {\n    this.showUserStatusPanel = !this.showUserStatusPanel;\n  }\n  // === GESTION DE LA RECHERCHE ===\n  performSearch() {\n    this.isSearching = true;\n    // Simulation de recherche\n    setTimeout(() => {\n      this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n      this.isSearching = false;\n    }, 500);\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchMode = false;\n    this.searchResults = [];\n    this.searchResultIds = [];\n  }\n  highlightSearchTerm(content) {\n    if (!this.searchQuery || !content) return content;\n    const regex = new RegExp(`(${this.searchQuery})`, 'gi');\n    return content.replace(regex, '<mark class=\"whatsapp-search-highlight\">$1</mark>');\n  }\n  scrollToMessage(messageId) {\n    this.highlightedMessageId = messageId;\n    // Logique pour faire défiler vers le message\n    setTimeout(() => {\n      this.highlightedMessageId = '';\n    }, 3000);\n  }\n  // === GESTION DES MESSAGES ÉPINGLÉS ===\n  showAllPinnedMessages() {\n    // Afficher tous les messages épinglés\n  }\n  closePinnedMessages() {\n    this.pinnedMessages = [];\n  }\n  togglePinMessage(messageId) {\n    const message = this.messages.find(m => m.id === messageId);\n    if (message) {\n      message.pinned = !message.pinned;\n      if (message.pinned) {\n        this.pinnedMessages.push(message);\n      } else {\n        this.pinnedMessages = this.pinnedMessages.filter(m => m.id !== messageId);\n      }\n    }\n    this.showMessageOptions = {};\n  }\n  // === GESTION DES OPTIONS DE MESSAGE ===\n  toggleMessageOptions(messageId) {\n    if (this.showMessageOptions[messageId]) {\n      this.showMessageOptions = {};\n    } else {\n      this.showMessageOptions = {\n        [messageId]: true\n      };\n    }\n  }\n  toggleReactionPicker(messageId) {\n    if (this.showReactionPicker[messageId]) {\n      this.showReactionPicker = {};\n    } else {\n      this.showReactionPicker = {\n        [messageId]: true\n      };\n    }\n  }\n  // === GESTION DES ACTIONS DE MESSAGE ===\n  replyToMessage(message) {\n    this.replyingToMessage = message;\n    this.showMessageOptions = {};\n  }\n  forwardMessage(message) {\n    this.forwardingMessage = message;\n    this.showForwardModal = true;\n    this.showMessageOptions = {};\n  }\n  copyMessage(message) {\n    if (navigator.clipboard && message.content) {\n      navigator.clipboard.writeText(message.content);\n      this.toastService.showSuccess('Message copié');\n    }\n    this.showMessageOptions = {};\n  }\n  selectMessage(messageId) {\n    this.selectionMode = true;\n    this.toggleMessageSelection(messageId);\n    this.showMessageOptions = {};\n  }\n  toggleMessageSelection(messageId) {\n    if (this.selectedMessages.has(messageId)) {\n      this.selectedMessages.delete(messageId);\n    } else {\n      this.selectedMessages.add(messageId);\n    }\n    if (this.selectedMessages.size === 0) {\n      this.selectionMode = false;\n    }\n  }\n  // === GESTION DES ÉMOJIS ===\n  openEmojiPicker(messageId) {\n    this.showEmojiPicker = true;\n    if (messageId) {\n      this.showReactionPicker = {\n        [messageId]: true\n      };\n    }\n  }\n  closeEmojiPicker() {\n    this.showEmojiPicker = false;\n    this.showReactionPicker = {};\n  }\n  selectEmojiCategory(categoryId) {\n    this.selectedEmojiCategory = categoryId;\n  }\n  addEmojiToMessage(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    this.messageForm.get('content')?.setValue(currentContent + emoji);\n  }\n  // === GESTION DES PIÈCES JOINTES ===\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  openFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  // === GESTION DES MÉDIAS ===\n  openImageViewer(message) {\n    this.showImageViewer = true;\n    this.currentImageIndex = 0;\n    this.imageGallery = [message];\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.imageGallery = [];\n  }\n  // === GESTION DES MESSAGES VOCAUX ===\n  toggleVoicePlayback(messageId) {\n    if (this.playingVoiceId === messageId) {\n      this.playingVoiceId = '';\n    } else {\n      this.playingVoiceId = messageId;\n    }\n  }\n  startVoiceRecording() {\n    this.isRecordingVoice = true;\n    this.voiceRecordingDuration = 0;\n    // Logique d'enregistrement vocal\n  }\n\n  stopVoiceRecording() {\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n  }\n  // === GESTION DES THÈMES ===\n  changeTheme(theme) {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n  }\n  getThemeOptions() {\n    return this.c.themes;\n  }\n  // === GESTION DU STATUT ===\n  updateUserStatus(status) {\n    this.isUpdatingStatus = true;\n    // Simuler une mise à jour\n    setTimeout(() => {\n      this.isUpdatingStatus = false;\n    }, 1000);\n  }\n  getStatusOptions() {\n    return Object.entries(this.c.status).map(([key, value]) => ({\n      key,\n      ...value\n    }));\n  }\n  getStatusIcon(status) {\n    return this.c.status[status]?.icon || 'fas fa-circle';\n  }\n  getStatusColor(status) {\n    return this.c.status[status]?.color || 'text-gray-500';\n  }\n  getStatusText(status) {\n    return this.c.status[status]?.text || 'Inconnu';\n  }\n  // === MÉTHODES POUR LE TEMPLATE ===\n  formatMessageContent(content) {\n    if (!content) return '';\n    return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/`(.*?)`/g, '<code>$1</code>').replace(/\\n/g, '<br>');\n  }\n  onImageLoad(event) {\n    // Gérer le chargement d'image\n  }\n  onImageError(event) {\n    event.target.src = 'assets/images/image-error.png';\n  }\n  onVideoLoaded(event, message) {\n    // Gérer le chargement vidéo\n  }\n  // === MÉTHODES POUR LES ANALYTICS ===\n  openAnalyticsDashboard() {\n    this.showAnalyticsDashboard = true;\n  }\n  closeAnalyticsDashboard() {\n    this.showAnalyticsDashboard = false;\n  }\n  // === MÉTHODES POUR LES INTÉGRATIONS ===\n  openIntegrations() {\n    this.showIntegrations = true;\n  }\n  closeIntegrations() {\n    this.showIntegrations = false;\n  }\n  // === MÉTHODES POUR LES RÉPONSES RAPIDES ===\n  sendQuickMessage(content) {\n    const control = this.messageForm.get('content');\n    if (control) {\n      control.setValue(content);\n      this.sendMessage();\n    }\n  }\n  // === MÉTHODES POUR LA COMPATIBILITÉ ===\n  initiateCall(type) {\n    this.startCall(type);\n  }\n  // === CORRECTION DES MÉTHODES MANQUANTES ===\n  generatePreviews() {\n    this.attachmentFiles.forEach(file => {\n      if (file.type.startsWith('image/')) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.previewUrl = e.target?.result || null;\n        };\n        reader.readAsDataURL(file);\n      }\n    });\n  }\n  loadCurrentUser() {\n    // Utiliser getCurrentUser au lieu de getUser\n    this.authUserService.getCurrentUser().subscribe({\n      next: user => {\n        this.currentUserId = user?.id || null;\n        this.currentUsername = user?.username || 'You';\n      },\n      error: error => console.error(\"Erreur lors du chargement de l'utilisateur:\", error)\n    });\n  }\n  // === MÉTHODES SUPPLÉMENTAIRES POUR LE TEMPLATE ===\n  // Méthodes pour les types de messages\n  getMessageType(message) {\n    if (message.content && !message.attachments?.length) return 'text';\n    if (message.attachments?.some(att => att.type === 'image')) return 'image';\n    if (message.attachments?.some(att => att.type === 'voice')) return 'voice';\n    if (message.attachments?.some(att => att.type === 'file')) return 'file';\n    if (message.attachments?.some(att => att.type === 'video')) return 'video';\n    if (message.type === 'location') return 'location';\n    if (message.type === 'contact') return 'contact';\n    return 'text';\n  }\n  // Méthodes pour les médias\n  getImageUrl(message) {\n    return message.imageUrl || message.attachments?.find(att => att.type === 'image')?.url || '';\n  }\n  getVideoUrl(message) {\n    return message.videoUrl || message.attachments?.find(att => att.type === 'video')?.url || '';\n  }\n  getVideoThumbnail(message) {\n    return message.thumbnailUrl || '';\n  }\n  getVideoDuration(message) {\n    return message.duration || '00:00';\n  }\n  // Méthodes pour les fichiers\n  getFileSize(message) {\n    return this.formatFileSize(message.size || 0);\n  }\n  getFileIcon(message) {\n    const fileType = message.fileType || message.attachments?.[0]?.type || '';\n    const icons = {\n      'application/pdf': 'fas fa-file-pdf',\n      'application/msword': 'fas fa-file-word',\n      'application/vnd.ms-excel': 'fas fa-file-excel',\n      'application/vnd.ms-powerpoint': 'fas fa-file-powerpoint',\n      'text/': 'fas fa-file-alt',\n      'image/': 'fas fa-file-image',\n      'video/': 'fas fa-file-video',\n      'audio/': 'fas fa-file-audio'\n    };\n    for (const [type, icon] of Object.entries(icons)) {\n      if (fileType.startsWith(type)) return icon;\n    }\n    return 'fas fa-file';\n  }\n  getFileType(message) {\n    const fileType = message.fileType || message.attachments?.[0]?.type || '';\n    return fileType.split('/')[1]?.toUpperCase() || 'FICHIER';\n  }\n  // Méthodes pour les messages vocaux\n  formatVoiceDuration(current, total) {\n    const formatTime = seconds => {\n      const mins = Math.floor(seconds / 60);\n      const secs = seconds % 60;\n      return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return `${formatTime(current)} / ${formatTime(total)}`;\n  }\n  getVoiceWaveform(message) {\n    return Array.from({\n      length: 20\n    }, (_, i) => ({\n      height: Math.random() * 20 + 5,\n      active: false,\n      played: false\n    }));\n  }\n  getVoiceProgress(messageId) {\n    return 0;\n  }\n  getVoiceCurrentTime(messageId) {\n    return 0;\n  }\n  getVoiceTotalDuration(messageId) {\n    return 0;\n  }\n  getVoiceSpeed(messageId) {\n    return '1x';\n  }\n  // Méthodes pour les états\n  isImageLoading(message) {\n    return false;\n  }\n  isVoiceLoading(messageId) {\n    return false;\n  }\n  isFileDownloading(message) {\n    return false;\n  }\n  getImageLoadingProgress(message) {\n    return 0;\n  }\n  getFileDownloadProgress(message) {\n    return 0;\n  }\n  getImageDimensions(message) {\n    return '';\n  }\n  // Méthodes pour les actions\n  downloadFile(message) {\n    // Télécharger le fichier\n  }\n  previewFile(message) {\n    // Prévisualiser le fichier\n  }\n  canPreviewFile(message) {\n    const previewableTypes = ['image/', 'video/', 'audio/', 'text/', 'application/pdf'];\n    return previewableTypes.some(type => message.type?.startsWith(type));\n  }\n  hasVideo(message) {\n    return message.type === 'video' || message.attachments && message.attachments.some(att => att.type === 'video');\n  }\n  getEmojiName(emoji) {\n    const emojiNames = {\n      '👍': 'Pouce levé',\n      '❤️': 'Cœur',\n      '😂': 'Rire',\n      '😮': 'Surprise',\n      '😢': 'Triste',\n      '😡': 'Colère'\n    };\n    return emojiNames[emoji] || emoji;\n  }\n  // Méthodes pour la localisation\n  openLocationViewer(message) {\n    // Ouvrir le visualiseur de localisation\n  }\n  getLocationMapUrl(message) {\n    return message.mapUrl || '';\n  }\n  getLocationName(message) {\n    return message.locationName || 'Localisation';\n  }\n  getLocationAddress(message) {\n    return message.address || '';\n  }\n  // Méthodes pour les contacts\n  openContactViewer(message) {\n    // Ouvrir le visualiseur de contact\n  }\n  getContactAvatar(message) {\n    return message.contactAvatar || 'assets/images/default-avatar.png';\n  }\n  getContactName(message) {\n    return message.contactName || 'Contact';\n  }\n  getContactPhone(message) {\n    return message.contactPhone || '';\n  }\n  // Méthodes pour les lecteurs\n  openVideoPlayer(message) {\n    // Ouvrir le lecteur vidéo\n  }\n  seekVoiceMessage(messageId, event) {\n    // Implémentation de la recherche dans le message vocal\n  }\n  changeVoiceSpeed(messageId) {\n    // Changer la vitesse de lecture\n  }\n  // Méthodes pour les événements\n  onScroll(event) {\n    // Gérer le défilement\n    const element = event.target;\n    this.showScrollToBottom = element.scrollTop < element.scrollHeight - element.clientHeight - 100;\n  }\n  loadMoreMessages() {\n    this.isLoadingMore = true;\n    this.currentPage++;\n    this.getMessages();\n    setTimeout(() => {\n      this.isLoadingMore = false;\n    }, 1000);\n  }\n  clearConversation() {\n    if (confirm('Êtes-vous sûr de vouloir vider cette conversation ?')) {\n      this.messages = [];\n    }\n  }\n  exportConversation() {\n    // Exporter la conversation\n  }\n  // Méthodes pour les en-têtes\n  getHeaderActions() {\n    return [{\n      icon: 'fas fa-search',\n      title: 'Rechercher',\n      onClick: () => this.toggleSearch(),\n      class: 'search-btn',\n      isActive: this.showSearch\n    }, {\n      icon: 'fas fa-bell',\n      title: 'Notifications',\n      onClick: () => this.toggleNotifications(),\n      class: 'notification-btn',\n      badge: this.unreadNotificationsCount > 0 ? {\n        count: this.unreadNotificationsCount,\n        class: 'bg-red-500',\n        animate: true\n      } : null\n    }, {\n      icon: 'fas fa-phone',\n      title: 'Appel audio',\n      onClick: () => this.startCall('AUDIO'),\n      class: 'call-btn'\n    }, {\n      icon: 'fas fa-video',\n      title: 'Appel vidéo',\n      onClick: () => this.startCall('VIDEO'),\n      class: 'video-btn'\n    }];\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  goBackToConversations() {\n    this.router.navigate(['/front/messages']);\n  }\n  openUserProfile(userId) {\n    // Ouvrir le profil utilisateur\n  }\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const date = new Date(lastActive);\n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const minutes = Math.floor(diff / 60000);\n    if (minutes < 1) return 'En ligne';\n    if (minutes < 60) return `Il y a ${minutes} min`;\n    if (minutes < 1440) return `Il y a ${Math.floor(minutes / 60)} h`;\n    return `Il y a ${Math.floor(minutes / 1440)} j`;\n  }\n  toggleConversationSettings() {\n    this.showConversationSettings = !this.showConversationSettings;\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) return \"Aujourd'hui\";\n    if (date.toDateString() === yesterday.toDateString()) return 'Hier';\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  getSystemMessageIcon(message) {\n    const icons = {\n      user_joined: 'fas fa-user-plus',\n      user_left: 'fas fa-user-minus',\n      call_started: 'fas fa-phone',\n      call_ended: 'fas fa-phone-slash',\n      message_deleted: 'fas fa-trash'\n    };\n    return icons[message.systemType] || 'fas fa-info-circle';\n  }\n  onMessageClick(message, event) {\n    if (this.selectionMode) {\n      this.toggleMessageSelection(message.id);\n    }\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.toggleMessageOptions(message.id);\n  }\n  onMessageHover(messageId, isHovering) {\n    this.hoveredMessageId = isHovering ? messageId : '';\n  }\n  shouldShowAvatar(index) {\n    if (index === this.messages.length - 1) return true;\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    return currentMessage?.sender?.id !== nextMessage?.sender?.id;\n  }\n  onAvatarError(event) {\n    event.target.src = 'assets/images/default-avatar.png';\n  }\n  isGroupConversation() {\n    return this.conversation?.type === 'group';\n  }\n  shouldShowSenderName(index) {\n    if (!this.isGroupConversation()) return false;\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    return currentMessage?.sender?.id !== previousMessage?.sender?.id;\n  }\n  getUserColor(userId) {\n    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // Propriété Math pour le template\n  get Math() {\n    return Math;\n  }\n  // Méthodes pour les fichiers\n  downloadSelectedFiles() {\n    // Télécharger les fichiers sélectionnés\n  }\n  shareSelectedFiles() {\n    // Partager les fichiers sélectionnés\n  }\n  deleteSelectedFiles() {\n    // Supprimer les fichiers sélectionnés\n  }\n  // Méthodes pour les analytics\n  updateAnalytics() {\n    // Mettre à jour les analytics\n  }\n  exportAnalytics() {\n    // Exporter les analytics\n  }\n  getMessageTypeIcon(type) {\n    const icons = {\n      text: 'fas fa-comment',\n      image: 'fas fa-image',\n      video: 'fas fa-video',\n      voice: 'fas fa-microphone',\n      file: 'fas fa-file',\n      location: 'fas fa-map-marker-alt',\n      contact: 'fas fa-user'\n    };\n    return icons[type] || 'fas fa-comment';\n  }\n  // Méthodes pour les intégrations\n  createNewIntegration() {\n    // Créer une nouvelle intégration\n  }\n  selectIntegrationCategory(category) {\n    this.selectedIntegrationCategory = category;\n  }\n  getIntegrationCategoryIcon(category) {\n    const icons = {\n      CRM: 'fas fa-users',\n      Productivité: 'fas fa-tasks',\n      Communication: 'fas fa-comments',\n      Analytics: 'fas fa-chart-bar',\n      Sécurité: 'fas fa-shield-alt'\n    };\n    return icons[category] || 'fas fa-puzzle-piece';\n  }\n  getAvailableIntegrations() {\n    return [{\n      id: 1,\n      name: 'Slack',\n      category: 'Communication',\n      icon: 'fab fa-slack'\n    }, {\n      id: 2,\n      name: 'Trello',\n      category: 'Productivité',\n      icon: 'fab fa-trello'\n    }, {\n      id: 3,\n      name: 'Google Analytics',\n      category: 'Analytics',\n      icon: 'fab fa-google'\n    }];\n  }\n  configureIntegration(integration) {\n    // Configurer l'intégration\n  }\n  toggleIntegration(integration) {\n    // Basculer l'intégration\n  }\n  getActiveIntegrations() {\n    return this.getAvailableIntegrations().filter(i => i.active);\n  }\n  getIntegrationStatusText(status) {\n    const texts = {\n      active: 'Actif',\n      inactive: 'Inactif',\n      error: 'Erreur',\n      pending: 'En attente'\n    };\n    return texts[status] || 'Inconnu';\n  }\n  formatLastActivity(lastActivity) {\n    return this.formatLastActive(lastActivity);\n  }\n  viewIntegrationLogs(integration) {\n    // Voir les logs d'intégration\n  }\n  editIntegration(integration) {\n    // Modifier l'intégration\n  }\n  testIntegration(integration) {\n    // Tester l'intégration\n  }\n  removeIntegration(integration) {\n    // Supprimer l'intégration\n  }\n  // Méthodes pour les webhooks\n  editWebhook(webhook) {\n    // Modifier le webhook\n  }\n  testWebhook(webhook) {\n    // Tester le webhook\n  }\n  deleteWebhook(webhook) {\n    // Supprimer le webhook\n  }\n  createWebhook() {\n    // Créer un webhook\n  }\n  // Méthodes pour les modales\n  hasActiveModal() {\n    return this.showImageViewer || this.showForwardModal || this.showAnalyticsDashboard || this.showIntegrations || this.showEmojiPicker || this.showCamera || this.showDrawingTool || this.showFileManager;\n  }\n  closeActiveModal() {\n    this.showImageViewer = false;\n    this.showForwardModal = false;\n    this.showAnalyticsDashboard = false;\n    this.showIntegrations = false;\n    this.showEmojiPicker = false;\n    this.showCamera = false;\n    this.showDrawingTool = false;\n    this.showFileManager = false;\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE HTML ===\n  // Méthodes pour l'éditeur d'images\n  flipImage(direction) {\n    console.log('Flip image:', direction);\n  }\n  applyImageFilter(filterName) {\n    this.selectedImageFilter = filterName;\n  }\n  updateImageAdjustments() {\n    // Mettre à jour les ajustements d'image\n  }\n  resetImageAdjustments() {\n    this.imageAdjustments = {\n      brightness: 0,\n      contrast: 0,\n      saturation: 0,\n      hue: 0\n    };\n  }\n  addTextToImage() {\n    if (this.newTextContent.trim()) {\n      this.imageTextElements.push({\n        id: Date.now().toString(),\n        content: this.newTextContent,\n        x: 50,\n        y: 50,\n        fontFamily: this.textFontFamily,\n        fontSize: this.textFontSize,\n        color: this.textColor\n      });\n      this.newTextContent = '';\n    }\n  }\n  // Méthodes pour le gestionnaire de fichiers\n  createNewFolder() {\n    const folderName = prompt('Nom du nouveau dossier:');\n    if (folderName) {\n      this.fileFolders.push({\n        id: Date.now().toString(),\n        name: folderName,\n        type: 'folder',\n        size: 0,\n        modifiedAt: new Date()\n      });\n    }\n  }\n  uploadFiles() {\n    this.openFileInput();\n  }\n  toggleFileView() {\n    this.fileViewMode = this.fileViewMode === 'grid' ? 'list' : 'grid';\n  }\n  closeFileManager() {\n    this.showFileManager = false;\n  }\n  selectFolder(folder) {\n    this.selectedFolder = folder;\n  }\n  renameFolder(folder) {\n    const newName = prompt('Nouveau nom:', folder.name);\n    if (newName) {\n      folder.name = newName;\n    }\n  }\n  deleteFolder(folder) {\n    if (confirm('Supprimer ce dossier ?')) {\n      this.fileFolders = this.fileFolders.filter(f => f.id !== folder.id);\n    }\n  }\n  getStorageUsagePercentage() {\n    return 65; // Exemple\n  }\n\n  getStorageUsed() {\n    return '6.5 GB';\n  }\n  getStorageTotal() {\n    return '10 GB';\n  }\n  getTotalFilesCount() {\n    return 1247;\n  }\n  navigateToFolder(crumb) {\n    this.selectedFolder = crumb;\n  }\n  onFileSearch() {\n    // Rechercher dans les fichiers\n  }\n  applyFileFilters() {\n    // Appliquer les filtres de fichiers\n  }\n  sortFiles() {\n    // Trier les fichiers\n  }\n  getFilteredFiles() {\n    return [{\n      id: '1',\n      name: 'Document.pdf',\n      type: 'file',\n      size: 1024000,\n      modifiedAt: new Date()\n    }, {\n      id: '2',\n      name: 'Image.jpg',\n      type: 'image',\n      size: 2048000,\n      modifiedAt: new Date()\n    }];\n  }\n  toggleFileSelection(file) {\n    const index = this.selectedFiles.indexOf(file.id);\n    if (index > -1) {\n      this.selectedFiles.splice(index, 1);\n    } else {\n      this.selectedFiles.push(file.id);\n    }\n  }\n  openFile(file) {\n    console.log('Ouvrir fichier:', file.name);\n  }\n  formatFileDate(date) {\n    return new Date(date).toLocaleDateString('fr-FR');\n  }\n  shareFile(file) {\n    console.log('Partager fichier:', file.name);\n  }\n  deleteFile(file) {\n    if (confirm('Supprimer ce fichier ?')) {\n      console.log('Fichier supprimé:', file.name);\n    }\n  }\n  // Méthodes pour les propriétés manquantes dans le template\n  get forwarded() {\n    return false; // Propriété pour les messages transférés\n  }\n  // Méthodes pour corriger les erreurs du template\n  getOnlineUsersCount() {\n    return this.onlineUsersCount;\n  }\n  // Méthodes manquantes pour le template\n  truncateText(text, maxLength) {\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  }\n  hasImage(message) {\n    return message.type === 'image' || message.attachments && message.attachments.some(a => a.type.startsWith('image/'));\n  }\n  isVoiceMessage(message) {\n    return message.type === 'voice' || message.type === 'audio';\n  }\n  hasFile(message) {\n    return message.type === 'file' || message.attachments && message.attachments.length > 0;\n  }\n  getFileName(message) {\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments[0].name || 'Fichier';\n    }\n    return 'Fichier';\n  }\n  getMessageBubbleClass(message) {\n    const isOwn = message.sender?.id === this.currentUserId;\n    return `whatsapp-message-bubble ${isOwn ? 'own' : 'other'}`;\n  }\n  isLocationMessage(message) {\n    return message.type === 'location';\n  }\n  isContactMessage(message) {\n    return message.type === 'contact';\n  }\n  // Méthodes pour la caméra\n  closeCamera() {\n    this.showCamera = false;\n  }\n  setCameraMode(mode) {\n    this.cameraMode = mode;\n  }\n  toggleCameraFlash() {\n    this.flashEnabled = !this.flashEnabled;\n  }\n  switchCamera() {\n    // Basculer entre caméra avant et arrière\n  }\n  openGallery() {\n    this.openFileInput();\n  }\n  capturePhoto() {\n    // Capturer une photo\n  }\n  startVideoRecording() {\n    this.isRecordingVideo = true;\n    this.videoRecordingDuration = 0;\n  }\n  stopVideoRecording() {\n    this.isRecordingVideo = false;\n  }\n  toggleCameraGrid() {\n    this.showCameraGrid = !this.showCameraGrid;\n  }\n  formatRecordingTime(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // Méthodes pour l'éditeur d'images\n  closeImageEditor() {\n    this.showImageEditor = false;\n  }\n  saveEditedImage() {\n    // Sauvegarder l'image éditée\n  }\n  startCropResize(corner, event) {\n    // Commencer le redimensionnement de crop\n  }\n  editTextElement(index) {\n    // Éditer un élément de texte\n  }\n  setImageEditorMode(mode) {\n    this.imageEditorMode = mode;\n  }\n  setCropRatio(ratio) {\n    this.cropRatio = ratio;\n  }\n  rotateImage(angle) {\n    // Faire tourner l'image\n  }\n  // Initialiser des données de test\n  initializeTestData() {\n    // Créer une conversation de test\n    this.conversation = {\n      id: 'test-conversation',\n      participants: [{\n        id: 'user1',\n        username: 'Alice'\n      }, {\n        id: 'user2',\n        username: 'Bob'\n      }]\n    };\n    // Créer des messages de test\n    this.messages = [{\n      id: '1',\n      content: 'Salut ! Comment ça va ?',\n      sender: {\n        id: 'user2',\n        username: 'Bob'\n      },\n      timestamp: new Date(Date.now() - 3600000),\n      conversationId: 'test-conversation',\n      type: 'text'\n    }, {\n      id: '2',\n      content: 'Ça va bien, merci ! Et toi ?',\n      sender: {\n        id: this.currentUserId,\n        username: this.currentUsername\n      },\n      timestamp: new Date(Date.now() - 3000000),\n      conversationId: 'test-conversation',\n      type: 'text'\n    }, {\n      id: '3',\n      content: \"Super ! Tu veux qu'on se voit ce soir ?\",\n      sender: {\n        id: 'user2',\n        username: 'Bob'\n      },\n      timestamp: new Date(Date.now() - 1800000),\n      conversationId: 'test-conversation',\n      type: 'text'\n    }];\n    this.otherParticipant = {\n      id: 'user2',\n      username: 'Bob'\n    };\n    this.loading = false;\n  }\n  // === MÉTHODES MANQUANTES POUR CORRIGER LES ERREURS DU TEMPLATE ===\n  // Méthodes pour les emojis et stickers\n  insertGif(gif) {\n    // Insérer un GIF dans le message\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + `[GIF:${gif.id}]`\n    });\n  }\n  manageStickerPacks() {\n    // Gérer les packs de stickers\n  }\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  // Méthodes pour l'outil de dessin\n  openDrawingTool() {\n    this.showDrawingTool = true;\n  }\n  closeDrawingTool() {\n    this.showDrawingTool = false;\n  }\n  selectDrawingTool(tool) {\n    this.selectedDrawingTool = tool;\n  }\n  selectDrawingColor(color) {\n    this.selectedDrawingColor = color;\n  }\n  undoDrawing() {\n    // Annuler le dernier trait\n  }\n  redoDrawing() {\n    // Refaire le dernier trait annulé\n  }\n  clearDrawing() {\n    // Effacer tout le dessin\n  }\n  startDrawing(event) {\n    // Commencer à dessiner\n  }\n  draw(event) {\n    // Dessiner\n  }\n  stopDrawing() {\n    // Arrêter de dessiner\n  }\n  saveDrawing() {\n    // Sauvegarder le dessin\n  }\n  // === MÉTHODES POUR LES RÉACTIONS ===\n  hasUserReacted(reaction, userId) {\n    if (!userId) return false;\n    return reaction.userId === userId;\n  }\n  getReactionTooltip(reaction) {\n    return `${reaction.user?.username || 'Utilisateur'} a réagi avec ${reaction.emoji}`;\n  }\n  // === MÉTHODES POUR LES PERMISSIONS ===\n  canDeleteMessage(message) {\n    // L'utilisateur peut supprimer ses propres messages ou s'il est admin\n    return message.sender?.id === this.currentUserId || this.isAdmin;\n  }\n  canEditMessage(message) {\n    // L'utilisateur peut modifier ses propres messages\n    return message.sender?.id === this.currentUserId;\n  }\n  // === MÉTHODES POUR LA SÉLECTION DE MESSAGES ===\n  exitSelectionMode() {\n    this.selectionMode = false;\n    this.selectedMessages.clear();\n  }\n  deleteSelectedMessages() {\n    if (this.selectedMessages.size === 0) return;\n    if (confirm(`Supprimer ${this.selectedMessages.size} message(s) ?`)) {\n      // Supprimer les messages sélectionnés\n      this.messages = this.messages.filter(m => !m.id || !this.selectedMessages.has(m.id));\n      this.exitSelectionMode();\n      this.toastService.showSuccess('Messages supprimés');\n    }\n  }\n  forwardSelectedMessages() {\n    if (this.selectedMessages.size === 0) return;\n    // Ouvrir le modal de transfert\n    this.showForwardModal = true;\n    this.forwardingMessage = Array.from(this.selectedMessages);\n  }\n  copySelectedMessages() {\n    if (this.selectedMessages.size === 0) return;\n    const selectedMessagesArray = this.messages.filter(m => m.id && this.selectedMessages.has(m.id));\n    const textToCopy = selectedMessagesArray.map(m => `${m.sender?.username}: ${m.content}`).join('\\n');\n    navigator.clipboard.writeText(textToCopy).then(() => {\n      this.toastService.showSuccess('Messages copiés');\n      this.exitSelectionMode();\n    }).catch(() => {\n      this.toastService.showError('Erreur lors de la copie');\n    });\n  }\n  // === MÉTHODES POUR LES RÉPONSES ===\n  cancelReply() {\n    this.replyingToMessage = null;\n  }\n  // === MÉTHODES POUR LES FICHIERS ===\n  removeSelectedFile() {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n  // === MÉTHODES POUR L'ENREGISTREMENT VOCAL ===\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  cancelVoiceRecording() {\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n  }\n  // === MÉTHODES POUR LES SONDAGES ===\n  canCreatePoll() {\n    return this.pollQuestion.trim().length > 0 && this.pollOptions.filter(opt => opt.text.trim().length > 0).length >= 2;\n  }\n  // === MÉTHODES POUR LA LOCALISATION ===\n  closeLocationPicker() {\n    this.showLocationPicker = false;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n    this.locationSearchResults = [];\n    this.selectedLocation = null;\n  }\n  getCurrentLocation() {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        this.selectedLocation = {\n          latitude: position.coords.latitude,\n          longitude: position.coords.longitude,\n          name: 'Ma position actuelle'\n        };\n      }, error => {\n        this.toastService.showError(\"Impossible d'obtenir votre position\");\n      });\n    } else {\n      this.toastService.showError('Géolocalisation non supportée');\n    }\n  }\n  searchLocation() {\n    this.showLocationSearch = true;\n    // Simuler une recherche de lieux\n    this.locationSearchResults = [{\n      name: 'Paris, France',\n      latitude: 48.8566,\n      longitude: 2.3522\n    }, {\n      name: 'Lyon, France',\n      latitude: 45.764,\n      longitude: 4.8357\n    }, {\n      name: 'Marseille, France',\n      latitude: 43.2965,\n      longitude: 5.3698\n    }];\n  }\n  onLocationSearch() {\n    // Filtrer les résultats selon la recherche\n    if (this.locationSearchQuery.trim()) {\n      this.locationSearchResults = this.locationSearchResults.filter(location => location.name.toLowerCase().includes(this.locationSearchQuery.toLowerCase()));\n    }\n  }\n  selectLocationResult(result) {\n    this.selectedLocation = result;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n  }\n  editLocationMessage() {\n    this.showLocationMessage = true;\n  }\n  shareLocation() {\n    if (this.selectedLocation) {\n      // Créer un message de localisation\n      const locationMessage = {\n        id: Date.now().toString(),\n        type: 'location',\n        location: this.selectedLocation,\n        locationMessage: this.locationMessage,\n        sender: {\n          id: this.currentUserId,\n          username: this.currentUsername\n        },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id\n      };\n      this.messages.push(locationMessage);\n      this.closeLocationPicker();\n      this.locationMessage = '';\n      this.toastService.showSuccess('Localisation partagée');\n    }\n  }\n  // === MÉTHODES POUR LES CONTACTS ===\n  closeContactPicker() {\n    this.showContactPicker = false;\n    this.contactSearchQuery = '';\n    this.selectedContactForSharing = null;\n  }\n  onContactSearch() {\n    // Filtrer les contacts selon la recherche\n    // Implémentation de la recherche de contacts\n  }\n  getFilteredContactsForSharing() {\n    // Simuler une liste de contacts\n    const contacts = [{\n      id: '1',\n      name: 'Alice Martin',\n      phone: '+33123456789',\n      email: '<EMAIL>'\n    }, {\n      id: '2',\n      name: 'Bob Dupont',\n      phone: '+33987654321',\n      email: '<EMAIL>'\n    }, {\n      id: '3',\n      name: 'Claire Durand',\n      phone: '+33555666777',\n      email: '<EMAIL>'\n    }];\n    if (this.contactSearchQuery.trim()) {\n      return contacts.filter(contact => contact.name.toLowerCase().includes(this.contactSearchQuery.toLowerCase()) || contact.phone.includes(this.contactSearchQuery) || contact.email.toLowerCase().includes(this.contactSearchQuery.toLowerCase()));\n    }\n    return contacts;\n  }\n  selectContactForSharing(contact) {\n    this.selectedContactForSharing = contact;\n  }\n  viewContactDetails(contact) {\n    // Ouvrir les détails du contact\n    this.toastService.showInfo(`Détails de ${contact.name}`);\n  }\n  createNewContact() {\n    // Ouvrir le formulaire de création de contact\n    this.toastService.showInfo(\"Création d'un nouveau contact\");\n  }\n  shareContact() {\n    if (this.selectedContactForSharing) {\n      // Créer un message de contact\n      const contactMessage = {\n        id: Date.now().toString(),\n        type: 'contact',\n        contact: this.selectedContactForSharing,\n        sender: {\n          id: this.currentUserId,\n          username: this.currentUsername\n        },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id\n      };\n      this.messages.push(contactMessage);\n      this.closeContactPicker();\n      this.toastService.showSuccess('Contact partagé');\n    }\n  }\n  // === MÉTHODES POUR LES EMOJIS ET STICKERS ===\n  openStickerPicker() {\n    this.showStickerPicker = true;\n    this.showEmojiPicker = false;\n  }\n  openGifPicker() {\n    this.showGifPicker = true;\n    this.showEmojiPicker = false;\n  }\n  onEmojiSearch() {\n    // Filtrer les emojis selon la recherche\n    // Implémentation de la recherche d'emojis\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: currentContent + emoji\n    });\n  }\n  getFilteredEmojiCategories() {\n    return this.emojiCategories.filter(category => {\n      if (!this.emojiSearchQuery.trim()) return true;\n      return category.name.toLowerCase().includes(this.emojiSearchQuery.toLowerCase());\n    });\n  }\n  previewEmoji(emoji) {\n    this.previewedEmoji = emoji;\n  }\n  selectStickerPack(packId) {\n    this.selectedStickerPack = packId;\n  }\n  getSelectedStickerPack() {\n    return this.stickerPacks.find(pack => pack.id === this.selectedStickerPack);\n  }\n  insertSticker(sticker) {\n    // Créer un message sticker\n    const stickerMessage = {\n      id: Date.now().toString(),\n      type: 'sticker',\n      sticker: sticker,\n      sender: {\n        id: this.currentUserId,\n        username: this.currentUsername\n      },\n      timestamp: new Date(),\n      conversationId: this.conversation?.id\n    };\n    this.messages.push(stickerMessage);\n    this.showStickerPicker = false;\n    this.toastService.showSuccess('Sticker envoyé');\n  }\n  onGifSearch() {\n    // Filtrer les GIFs selon la recherche\n    // Implémentation de la recherche de GIFs\n  }\n  selectGifCategory(category) {\n    this.selectedGifCategory = category;\n  }\n  getFilteredGifs() {\n    // Simuler une liste de GIFs\n    return [{\n      id: '1',\n      url: 'gif1.gif',\n      title: 'Happy'\n    }, {\n      id: '2',\n      url: 'gif2.gif',\n      title: 'Funny'\n    }, {\n      id: '3',\n      url: 'gif3.gif',\n      title: 'Love'\n    }];\n  }\n  cancelDelete() {\n    this.showDeleteConfirmModal = false;\n  }\n  confirmDelete() {\n    // Logique de suppression\n    this.showDeleteConfirmModal = false;\n    this.toastService.showSuccess('Message supprimé');\n  }\n  closeForwardModal() {\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n  }\n  getFilteredContacts() {\n    const contacts = [{\n      id: '1',\n      name: 'Alice Martin',\n      avatar: 'avatar1.jpg'\n    }, {\n      id: '2',\n      name: 'Bob Dupont',\n      avatar: 'avatar2.jpg'\n    }, {\n      id: '3',\n      name: 'Claire Durand',\n      avatar: 'avatar3.jpg'\n    }];\n    if (this.forwardSearchQuery.trim()) {\n      return contacts.filter(contact => contact.name.toLowerCase().includes(this.forwardSearchQuery.toLowerCase()));\n    }\n    return contacts;\n  }\n  toggleContactSelection(contactId) {\n    const index = this.selectedConversations.indexOf(contactId);\n    if (index > -1) {\n      this.selectedConversations.splice(index, 1);\n    } else {\n      this.selectedConversations.push(contactId);\n    }\n  }\n  confirmForward() {\n    if (this.selectedConversations.length > 0) {\n      this.toastService.showSuccess(`Message transféré à ${this.selectedConversations.length} contact(s)`);\n      this.closeForwardModal();\n    }\n  }\n  // === MÉTHODES POUR LE VISUALISEUR D'IMAGES ===\n  previousImage() {\n    if (this.currentImageIndex > 0) {\n      this.currentImageIndex--;\n    }\n  }\n  nextImage() {\n    if (this.currentImageIndex < this.imageGallery.length - 1) {\n      this.currentImageIndex++;\n    }\n  }\n  // === MÉTHODES POUR LES TOASTS ===\n  getToastIcon() {\n    const icons = {\n      success: 'fas fa-check-circle',\n      error: 'fas fa-exclamation-circle',\n      warning: 'fas fa-exclamation-triangle',\n      info: 'fas fa-info-circle'\n    };\n    return icons[this.toastType] || 'fas fa-info-circle';\n  }\n  // === MÉTHODES POUR LES STATISTIQUES ===\n  getTotalMessagesCount() {\n    return this.messages.length;\n  }\n  getPhotosCount() {\n    return this.messages.filter(m => m.attachments?.some(att => att.type === 'image')).length;\n  }\n  getFilesCount() {\n    return this.messages.filter(m => m.attachments?.some(att => att.type === 'file')).length;\n  }\n  // === MÉTHODES POUR LES PARAMÈTRES DE CONVERSATION ===\n  updateConversationSettings() {\n    // Sauvegarder les paramètres de conversation\n    this.toastService.showSuccess('Paramètres mis à jour');\n  }\n  // === MÉTHODES POUR LA TRADUCTION ===\n  closeTranslationPanel() {\n    this.showTranslationPanel = false;\n    this.originalTextForTranslation = '';\n    this.translatedText = '';\n  }\n  swapLanguages() {\n    const temp = this.translationFrom;\n    this.translationFrom = this.translationTo;\n    this.translationTo = temp;\n  }\n  copyTranslation() {\n    if (this.translatedText) {\n      navigator.clipboard.writeText(this.translatedText).then(() => {\n        this.toastService.showSuccess('Traduction copiée');\n      });\n    }\n  }\n  insertTranslation() {\n    if (this.translatedText) {\n      const currentContent = this.messageForm.get('content')?.value || '';\n      this.messageForm.patchValue({\n        content: currentContent + this.translatedText\n      });\n      this.closeTranslationPanel();\n    }\n  }\n  shareTranslation() {\n    if (this.translatedText) {\n      // Créer un message avec la traduction\n      const translationMessage = {\n        id: Date.now().toString(),\n        content: this.translatedText,\n        sender: {\n          id: this.currentUserId,\n          username: this.currentUsername\n        },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id\n      };\n      this.messages.push(translationMessage);\n      this.closeTranslationPanel();\n      this.toastService.showSuccess('Traduction partagée');\n    }\n  }\n  // === MÉTHODES POUR LES SONDAGES ===\n  closePollCreator() {\n    this.showPollCreator = false;\n    this.pollQuestion = '';\n    this.pollOptions = [{\n      text: ''\n    }, {\n      text: ''\n    }];\n    this.pollSettings = {\n      allowMultiple: false,\n      anonymous: false,\n      showResults: true,\n      showVoters: true\n    };\n  }\n  addPollOption() {\n    if (this.pollOptions.length < 10) {\n      this.pollOptions.push({\n        text: ''\n      });\n    }\n  }\n  removePollOption(index) {\n    if (this.pollOptions.length > 2) {\n      this.pollOptions.splice(index, 1);\n    }\n  }\n  createPoll() {\n    if (this.canCreatePoll()) {\n      // Créer un message sondage\n      const pollMessage = {\n        id: Date.now().toString(),\n        type: 'poll',\n        poll: {\n          question: this.pollQuestion,\n          options: this.pollOptions.filter(opt => opt.text.trim()),\n          settings: this.pollSettings,\n          votes: [],\n          totalVotes: 0\n        },\n        sender: {\n          id: this.currentUserId,\n          username: this.currentUsername\n        },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id\n      };\n      this.messages.push(pollMessage);\n      this.closePollCreator();\n      this.toastService.showSuccess('Sondage créé');\n    }\n  }\n  // === MÉTHODES POUR LES FONCTIONNALITÉS DE BASE ===\n  hasMessageContent() {\n    const content = this.messageForm.get('content')?.value;\n    return content && content.trim().length > 0;\n  }\n  canSendMessage() {\n    return this.hasMessageContent() || this.selectedFile !== null;\n  }\n  searchEmojis() {\n    // Rechercher des emojis selon emojiSearchQuery\n  }\n  getFilteredEmojis() {\n    // Simuler une liste d'emojis\n    const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'];\n    if (this.emojiSearchQuery.trim()) {\n      return emojis.filter(emoji => emoji.includes(this.emojiSearchQuery));\n    }\n    return emojis;\n  }\n  // === MÉTHODES POUR LES NOTIFICATIONS ===\n  toggleNotificationPanel() {\n    this.showNotificationPanel = !this.showNotificationPanel;\n  }\n  onSearchKeyPress(event) {\n    if (event.key === 'Enter') {\n      this.performSearch();\n    }\n  }\n  toggleSearchBar() {\n    this.showSearchBar = !this.showSearchBar;\n    if (!this.showSearchBar) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n  navigateToMessage(messageId) {\n    // Naviguer vers un message spécifique\n    this.highlightedMessageId = messageId;\n  }\n  highlightSearchTerms(content, query) {\n    if (!query) return content;\n    const regex = new RegExp(`(${query})`, 'gi');\n    return content.replace(regex, '<mark>$1</mark>');\n  }\n  // === MÉTHODES POUR LES MESSAGES ÉPINGLÉS ===\n  getPinnedMessagesCount() {\n    return this.messages.filter(m => m.pinned).length;\n  }\n  togglePinnedMessages() {\n    this.showPinnedMessages = !this.showPinnedMessages;\n  }\n  scrollToPinnedMessage(messageId) {\n    this.scrollToMessage(messageId);\n  }\n  formatMessageDate(date) {\n    return this.formatMessageTime(date);\n  }\n  // === MÉTHODES POUR LES APPELS ===\n  declineCall() {\n    this.incomingCall = null;\n    this.toastService.showInfo('Appel refusé');\n  }\n  acceptCall() {\n    this.incomingCall = null;\n    this.isInCall = true;\n    this.toastService.showSuccess('Appel accepté');\n  }\n  formatCallDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  getCallStatusText() {\n    if (this.isInCall) return 'En cours...';\n    return 'Connexion...';\n  }\n  toggleCallMinimize() {\n    this.isCallMinimized = !this.isCallMinimized;\n  }\n  // === MÉTHODES POUR LE FORMATAGE DE TEXTE ===\n  applyFormatting(type) {\n    const content = this.messageForm.get('content')?.value || '';\n    let formattedContent = content;\n    switch (type) {\n      case 'bold':\n        formattedContent = `*${content}*`;\n        break;\n      case 'italic':\n        formattedContent = `_${content}_`;\n        break;\n      case 'strikethrough':\n        formattedContent = `~${content}~`;\n        break;\n      case 'underline':\n        formattedContent = `__${content}__`;\n        break;\n      case 'code':\n        formattedContent = `\\`${content}\\``;\n        break;\n      case 'quote':\n        formattedContent = `> ${content}`;\n        break;\n      case 'spoiler':\n        formattedContent = `||${content}||`;\n        break;\n    }\n    this.messageForm.patchValue({\n      content: formattedContent\n    });\n  }\n  hasFormatting(type) {\n    const content = this.messageForm.get('content')?.value || '';\n    switch (type) {\n      case 'bold':\n        return content.includes('*');\n      case 'italic':\n        return content.includes('_');\n      case 'strikethrough':\n        return content.includes('~');\n      case 'underline':\n        return content.includes('__');\n      case 'code':\n        return content.includes('`');\n      case 'quote':\n        return content.includes('>');\n      case 'spoiler':\n        return content.includes('||');\n      default:\n        return false;\n    }\n  }\n  insertLink() {\n    const url = prompt(\"Entrez l'URL du lien :\");\n    if (url) {\n      const text = prompt('Entrez le texte du lien :') || url;\n      const content = this.messageForm.get('content')?.value || '';\n      this.messageForm.patchValue({\n        content: content + `[${text}](${url})`\n      });\n    }\n  }\n  insertTable() {\n    const table = '\\n| Colonne 1 | Colonne 2 |\\n|-----------|----------|\\n| Cellule 1 | Cellule 2 |\\n';\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + table\n    });\n  }\n  insertList(type) {\n    const content = this.messageForm.get('content')?.value || '';\n    const listItem = type === 'ul' ? '• Élément de liste\\n' : '1. Élément de liste\\n';\n    this.messageForm.patchValue({\n      content: content + '\\n' + listItem\n    });\n  }\n  insertMentionSymbol() {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + '@'\n    });\n  }\n  insertHashtagSymbol() {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + '#'\n    });\n  }\n  clearFormatting() {\n    const content = this.messageForm.get('content')?.value || '';\n    const cleanContent = content.replace(/\\*([^*]+)\\*/g, '$1') // Bold\n    .replace(/_([^_]+)_/g, '$1') // Italic\n    .replace(/~([^~]+)~/g, '$1') // Strikethrough\n    .replace(/__([^_]+)__/g, '$1') // Underline\n    .replace(/`([^`]+)`/g, '$1') // Code\n    .replace(/> /g, '') // Quote\n    .replace(/\\|\\|([^|]+)\\|\\|/g, '$1'); // Spoiler\n    this.messageForm.patchValue({\n      content: cleanContent\n    });\n  }\n  toggleFormattingToolbar() {\n    this.showFormattingToolbar = !this.showFormattingToolbar;\n  }\n  // === MÉTHODES POUR LES CORRECTIONS AUTOMATIQUES ===\n  hideAutoCorrectSuggestions() {\n    this.autoCorrectSuggestions = [];\n  }\n  applyCorrection(original, correction) {\n    const content = this.messageForm.get('content')?.value || '';\n    const correctedContent = content.replace(original, correction);\n    this.messageForm.patchValue({\n      content: correctedContent\n    });\n    this.hideAutoCorrectSuggestions();\n  }\n  ignoreCorrection(original) {\n    this.autoCorrectSuggestions = this.autoCorrectSuggestions.filter(s => s.original !== original);\n  }\n  // === MÉTHODES POUR LES FICHIERS ===\n  triggerFileInput(type) {\n    const input = document.createElement('input');\n    input.type = 'file';\n    switch (type) {\n      case 'image':\n        input.accept = 'image/*';\n        break;\n      case 'video':\n        input.accept = 'video/*';\n        break;\n      case 'audio':\n        input.accept = 'audio/*';\n        break;\n      case 'document':\n        input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx';\n        break;\n    }\n    input.onchange = event => {\n      this.onFileSelected(event);\n    };\n    input.click();\n  }\n  // === MÉTHODES POUR L'INPUT DE MESSAGE ===\n  getInputPlaceholder() {\n    if (this.replyingToMessage) {\n      return `Répondre à ${this.replyingToMessage.sender?.username}...`;\n    }\n    if (this.isRecordingVoice) {\n      return 'Enregistrement en cours...';\n    }\n    return 'Tapez votre message...';\n  }\n  onInputChange(event) {\n    const content = event.target.value;\n    this.messageForm.patchValue({\n      content\n    });\n    // Détecter les mentions, hashtags, etc.\n    this.detectMentions(content);\n    this.detectHashtags(content);\n    this.detectCommands(content);\n    // Gestion de la frappe\n    this.handleTyping();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    } else if (event.key === 'ArrowUp' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.selectedMentionIndex = Math.max(0, this.selectedMentionIndex - 1);\n    } else if (event.key === 'ArrowDown' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.selectedMentionIndex = Math.min(this.mentionSuggestions.length - 1, this.selectedMentionIndex + 1);\n    } else if (event.key === 'Tab' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.insertMention(this.mentionSuggestions[this.selectedMentionIndex]);\n    }\n  }\n  onInputKeyUp(event) {\n    // Gestion des suggestions en temps réel\n    this.updateSuggestions();\n  }\n  onInputFocus() {\n    this.inputFocused = true;\n  }\n  onInputBlur() {\n    this.inputFocused = false;\n    // Masquer les suggestions après un délai\n    setTimeout(() => {\n      this.mentionSuggestions = [];\n      this.hashtagSuggestions = [];\n      this.commandSuggestions = [];\n    }, 200);\n  }\n  onInputPaste(event) {\n    const clipboardData = event.clipboardData || window.clipboardData;\n    const pastedData = clipboardData.getData('text');\n    // Traiter le contenu collé\n    if (pastedData.includes('http')) {\n      // Détecter les liens\n      this.detectLinks(pastedData);\n    }\n  }\n  onInputScroll(event) {\n    // Gérer le défilement de l'input si nécessaire\n  }\n  // === MÉTHODES POUR LES MENTIONS ET SUGGESTIONS ===\n  detectMentions(content) {\n    const mentionMatch = content.match(/@(\\w*)$/);\n    if (mentionMatch) {\n      const query = mentionMatch[1];\n      this.mentionSuggestions = this.getFilteredUsers(query);\n      this.selectedMentionIndex = 0;\n    } else {\n      this.mentionSuggestions = [];\n    }\n  }\n  detectHashtags(content) {\n    const hashtagMatch = content.match(/#(\\w*)$/);\n    if (hashtagMatch) {\n      const query = hashtagMatch[1];\n      this.hashtagSuggestions = this.getFilteredHashtags(query);\n    } else {\n      this.hashtagSuggestions = [];\n    }\n  }\n  detectCommands(content) {\n    const commandMatch = content.match(/\\/(\\w*)$/);\n    if (commandMatch) {\n      const query = commandMatch[1];\n      this.commandSuggestions = this.getFilteredCommands(query);\n    } else {\n      this.commandSuggestions = [];\n    }\n  }\n  detectLinks(content) {\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    const links = content.match(urlRegex);\n    if (links) {\n      // Traiter les liens détectés\n      console.log('Liens détectés:', links);\n    }\n  }\n  insertMention(user) {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/@\\w*$/, `@${user.username} `);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.mentionSuggestions = [];\n  }\n  insertHashtag(hashtag) {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/#\\w*$/, `#${hashtag.name} `);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.hashtagSuggestions = [];\n  }\n  insertCommand(command) {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/\\/\\w*$/, `/${command.name} `);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.commandSuggestions = [];\n  }\n  setSelectedMentionIndex(index) {\n    this.selectedMentionIndex = index;\n  }\n  updateSuggestions() {\n    const content = this.messageForm.get('content')?.value || '';\n    this.detectMentions(content);\n    this.detectHashtags(content);\n    this.detectCommands(content);\n  }\n  // === MÉTHODES POUR LE COMPTAGE DE CARACTÈRES ===\n  getCharacterCount() {\n    const content = this.messageForm.get('content')?.value || '';\n    return content.length;\n  }\n  hasFormattedText() {\n    const content = this.messageForm.get('content')?.value || '';\n    return content.includes('*') || content.includes('_') || content.includes('`') || content.includes('~');\n  }\n  // === MÉTHODES POUR LA TRADUCTION ===\n  getLanguageName(languageCode) {\n    const languages = {\n      fr: 'Français',\n      en: 'Anglais',\n      es: 'Espagnol',\n      de: 'Allemand',\n      it: 'Italien'\n    };\n    return languages[languageCode] || languageCode;\n  }\n  translateMessage() {\n    this.showTranslationPanel = true;\n    const content = this.messageForm.get('content')?.value || '';\n    this.originalTextForTranslation = content;\n    // Simuler une traduction\n    this.translatedText = `[Traduction] ${content}`;\n  }\n  showAutoCorrections() {\n    // Afficher les corrections automatiques\n    this.autoCorrectSuggestions = [{\n      original: 'salut',\n      corrections: ['Salut']\n    }, {\n      original: 'ca va',\n      corrections: ['Ça va']\n    }];\n  }\n  // === MÉTHODES UTILITAIRES MANQUANTES ===\n  handleTyping() {\n    // Gérer l'indicateur de frappe\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'événement de début de frappe\n    }\n    // Réinitialiser le timer\n    clearTimeout(this.typingTimeout);\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Envoyer l'événement de fin de frappe\n    }, 1000);\n  }\n  getFilteredUsers(query) {\n    const users = [{\n      id: '1',\n      username: 'alice',\n      name: 'Alice Martin'\n    }, {\n      id: '2',\n      username: 'bob',\n      name: 'Bob Dupont'\n    }, {\n      id: '3',\n      username: 'claire',\n      name: 'Claire Durand'\n    }];\n    if (!query) return users;\n    return users.filter(user => user.username.toLowerCase().includes(query.toLowerCase()) || user.name.toLowerCase().includes(query.toLowerCase()));\n  }\n  getFilteredHashtags(query) {\n    const hashtags = [{\n      id: '1',\n      name: 'important',\n      count: 15\n    }, {\n      id: '2',\n      name: 'urgent',\n      count: 8\n    }, {\n      id: '3',\n      name: 'meeting',\n      count: 12\n    }];\n    if (!query) return hashtags;\n    return hashtags.filter(hashtag => hashtag.name.toLowerCase().includes(query.toLowerCase()));\n  }\n  getFilteredCommands(query) {\n    const commands = [{\n      id: '1',\n      name: 'help',\n      description: \"Afficher l'aide\"\n    }, {\n      id: '2',\n      name: 'clear',\n      description: 'Effacer la conversation'\n    }, {\n      id: '3',\n      name: 'status',\n      description: 'Changer le statut'\n    }];\n    if (!query) return commands;\n    return commands.filter(command => command.name.toLowerCase().includes(query.toLowerCase()));\n  }\n  // === MÉTHODES MANQUANTES POUR LES MODALS ET FONCTIONNALITÉS ===\n  openCamera() {\n    // Ouvrir la caméra\n    this.toastService.showInfo('Ouverture de la caméra...');\n  }\n  openLocationPicker() {\n    this.showLocationPicker = true;\n  }\n  openContactPicker() {\n    this.showContactPicker = true;\n  }\n  openPollCreator() {\n    this.showPollCreator = true;\n  }\n  hideQuickReplies() {\n    this.showQuickReplies = false;\n  }\n  insertQuickReply(reply) {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + reply\n    });\n    this.hideQuickReplies();\n  }\n  // === CORRECTION DES MÉTHODES AVEC PARAMÈTRES ===\n  onSearchInput(event) {\n    // Rechercher dans les messages\n    if (event) {\n      this.searchQuery = event.target.value;\n    }\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  getEmojisForCategory(category) {\n    // Retourner les émojis pour une catégorie\n    return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'];\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n  }\n  startVideoCall() {\n    this.startCall('video');\n  }\n  startVoiceCall() {\n    this.startCall('voice');\n  }\n};\n__decorate([ViewChild('messagesContainer')], MessageChatComponent.prototype, \"messagesContainer\", void 0);\n__decorate([ViewChild('fileInput', {\n  static: false\n})], MessageChatComponent.prototype, \"fileInput\", void 0);\nMessageChatComponent = __decorate([Component({\n  selector: 'app-message-chat',\n  templateUrl: 'message-chat.component.html',\n  styleUrls: ['./message-chat.component.css']\n})], MessageChatComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "Validators", "Subscription", "MessageChatComponent", "constructor", "route", "router", "authUserService", "MessageService", "toastService", "fb", "cdr", "userStatusService", "messages", "conversation", "loading", "currentUserId", "currentUsername", "otherParticipant", "isAdmin", "selectedFile", "previewUrl", "isUploading", "isTyping", "isRecordingVoice", "voiceRecordingDuration", "MAX_MESSAGES_PER_SIDE", "MAX_MESSAGES_TO_LOAD", "MAX_TOTAL_MESSAGES", "currentPage", "isLoadingMore", "hasMoreMessages", "subscriptions", "selectedTheme", "showThemeSelector", "showMainMenu", "showEmojiPicker", "showSearchBar", "showPinnedMessages", "showStatusSelector", "showNotificationPanel", "showNotificationSettings", "showUserStatusPanel", "showCallHistoryPanel", "showCallStatsPanel", "showVoiceMessagesPanel", "incomingCall", "activeCall", "showCallModal", "showActiveCallModal", "isCallMuted", "isVideoEnabled", "callDuration", "callTimer", "notifications", "unreadNotificationCount", "selectedNotifications", "Set", "showDeleteConfirmModal", "editingMessageId", "<PERSON><PERSON><PERSON><PERSON>", "replyingToMessage", "searchQuery", "isSearching", "searchMode", "searchResults", "showSearch", "showAdvancedSearch", "pinnedMessages", "showReactionPicker", "showDeleteConfirm", "showPinConfirm", "isPinning", "showMessageOptions", "showForwardModal", "forwardingMessage", "selectedConversations", "availableConversations", "isForwarding", "isLoadingConversations", "emojiCategories", "id", "name", "icon", "selectedEmojiCategory", "emojiSearchQuery", "recentEmojis", "previewedEmoji", "showStickerPicker", "stickerPacks", "selected<PERSON><PERSON>er<PERSON>ack", "showGifPicker", "gifSearch<PERSON>uery", "gifCategories", "selectedGifCategory", "showDrawingTool", "selectedDrawingTool", "drawingColors", "selectedDrawingColor", "customDrawingColor", "drawingSize", "showCamera", "cameraMode", "flashEnabled", "showCameraGrid", "showFocusIndicator", "focusX", "focusY", "lastCapturedImage", "isRecordingVideo", "videoRecordingDuration", "showImageEditor", "imageEditorMode", "cropArea", "x", "y", "width", "height", "cropRatio", "imageFilters", "label", "css", "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "imageAdjustments", "brightness", "contrast", "saturation", "hue", "imageTextElements", "newTextContent", "textFontFamily", "textFontSize", "textColor", "availableFonts", "value", "showFileManager", "fileViewMode", "fileFolders", "selectedFolder", "fileBreadcrumbs", "fileSearchQuery", "fileTypeFilter", "fileSortBy", "selectedFiles", "showAnalyticsDashboard", "analyticsTimeRange", "analyticsData", "totalMessages", "activeUsers", "avgResponseTime", "filesShared", "messagesChange", "usersChange", "responseTimeChange", "filesChange", "topUsers", "messageTypes", "longestConversation", "avgConversationDuration", "topEmojis", "showIntegrations", "integrationCategories", "selectedIntegrationCategory", "webhooks", "showQuickReplies", "quickReplies", "showMentionSuggestions", "mentionSuggestions", "selectedMentionIndex", "activeMentions", "showHashtagSuggestions", "hashtagSuggestions", "selectedHashtagIndex", "activeHashtags", "showCommandSuggestions", "commandSuggestions", "selectedCommandIndex", "activeLinks", "inputHeight", "showFormattingToolbar", "detectedLanguage", "autoCorrections", "showAutoCorrectSuggestions", "autoCorrectSuggestions", "showTranslationPanel", "translationFrom", "translationTo", "originalTextForTranslation", "translatedText", "isTranslating", "supportedLanguages", "code", "showPollCreator", "pollQuestion", "pollOptions", "text", "pollSettings", "allowMultiple", "anonymous", "showResults", "showVoters", "pollExpiry", "customPollExpiry", "showLocationPicker", "showLocationSearch", "locationSearchQuery", "locationSearchResults", "selectedLocation", "showLocationMessage", "locationMessage", "showContactPicker", "contactSearchQuery", "selectedContactForSharing", "showScheduleMessage", "customScheduleDate", "customScheduleTime", "scheduleTimezone", "availableTimezones", "highlightedMessageId", "searchResultIds", "selectionMode", "selectedMessages", "hoveredMessageId", "playingVoiceId", "showScrollToBottom", "unreadMessagesCount", "showImageViewer", "currentImageIndex", "imageGallery", "showToast", "toastMessage", "toastType", "showConversationInfo", "showConversationSettings", "conversationSettings", "soundNotifications", "readReceipts", "typingIndicators", "isSendingMessage", "isUserTyping", "voiceRecordingState", "voiceRecordingSize", "recordingWaveform", "voiceRecordingQuality", "voiceEffects", "selectedContacts", "isUpdatingStatus", "showAttachmentMenu", "attachmentFiles", "fileCaptions", "fileUploadProgress", "messageContent", "c", "reactions", "delays", "away", "typing", "scroll", "anim", "error", "trackById", "i", "item", "toString", "NEW_MESSAGE", "color", "CALL_MISSED", "SYSTEM", "status", "online", "offline", "busy", "themes", "key", "calls", "COMPLETED", "MISSED", "REJECTED", "_selectedMessages", "isCallMinimized", "isInCall", "inputFocused", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showCallControls", "messageForm", "group", "content", "required", "<PERSON><PERSON><PERSON><PERSON>", "availableReactions", "commonEmojis", "getCommonEmojis", "unreadNotificationsCount", "currentUserStatus", "onlineUsersCount", "ngOnInit", "initializeComponent", "ngOnDestroy", "unsubscribe", "clearInterval", "typingTimeout", "clearTimeout", "ngAfterViewChecked", "detectChanges", "loadCurrentUser", "loadConversation", "setupSubscriptions", "initializeTestData", "params", "subscribe", "conversationId", "getConversation", "getMessages", "next", "participants", "find", "p", "console", "length", "scrollToBottom", "showError", "sendMessage", "get", "trim", "tempMessage", "Date", "now", "sender", "username", "timestamp", "type", "push", "reset", "onFileSelected", "event", "files", "target", "Array", "from", "generatePreviews", "removeAttachment", "index", "splice", "toggleReaction", "messageId", "emoji", "message", "m", "existingReaction", "r", "count", "users", "addReaction", "startEditMessage", "cancelEdit", "saveEdit", "editMessage", "onEditKeyDown", "shift<PERSON>ey", "preventDefault", "deleteMessage", "confirm", "startCall", "initiateCall", "call", "startCallTimer", "setInterval", "endCall", "toggleMute", "toggleVideo", "setTimeout", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "formatMessageTime", "date", "toLocaleTimeString", "hour", "minute", "formatFileSize", "bytes", "k", "sizes", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "trackByMessageId", "toggleSearch", "clearSearch", "toggleNotifications", "toggleConversationInfo", "toggleThemeSelector", "toggleMainMenu", "toggleAdvancedSearch", "toggleStatusSelector", "toggleUserStatusPanel", "performSearch", "filter", "toLowerCase", "includes", "highlightSearchTerm", "regex", "RegExp", "replace", "scrollToMessage", "showAllPinnedMessages", "closePinnedMessages", "togglePinMessage", "pinned", "toggleMessageOptions", "toggleReactionPicker", "replyToMessage", "forwardMessage", "copyMessage", "navigator", "clipboard", "writeText", "showSuccess", "selectMessage", "toggleMessageSelection", "has", "delete", "add", "size", "openEmojiPicker", "closeEmojiPicker", "selectEmojiCategory", "categoryId", "addEmojiToMessage", "currentC<PERSON>nt", "setValue", "toggleAttachmentMenu", "openFileInput", "fileInput", "click", "openImageViewer", "closeImageViewer", "toggleVoicePlayback", "startVoiceRecording", "stopVoiceRecording", "changeTheme", "theme", "getThemeOptions", "updateUserStatus", "getStatusOptions", "Object", "entries", "map", "getStatusIcon", "getStatusColor", "getStatusText", "formatMessageContent", "onImageLoad", "onImageError", "src", "onVideoLoaded", "openAnalyticsDashboard", "closeAnalyticsDashboard", "openIntegrations", "closeIntegrations", "sendQuickMessage", "control", "for<PERSON>ach", "file", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "getCurrentUser", "user", "getMessageType", "attachments", "some", "att", "getImageUrl", "imageUrl", "url", "getVideoUrl", "videoUrl", "getVideoThumbnail", "thumbnailUrl", "getVideoDuration", "duration", "getFileSize", "getFileIcon", "fileType", "icons", "getFileType", "split", "toUpperCase", "formatVoiceDuration", "current", "total", "formatTime", "seconds", "mins", "secs", "padStart", "getVoiceWaveform", "_", "random", "active", "played", "getVoiceProgress", "getVoiceCurrentTime", "getVoiceTotalDuration", "getVoiceSpeed", "isImageLoading", "isVoiceLoading", "isFileDownloading", "getImageLoadingProgress", "getFileDownloadProgress", "getImageDimensions", "downloadFile", "previewFile", "canPreviewFile", "previewableTypes", "hasVideo", "getEmojiName", "emojiNames", "openLocationViewer", "getLocationMapUrl", "mapUrl", "getLocationName", "locationName", "getLocationAddress", "address", "openContactViewer", "getContactAvatar", "contactAvatar", "getContactName", "contactName", "getContactPhone", "contactPhone", "openVideoPlayer", "seekVoiceMessage", "changeVoiceSpeed", "onScroll", "clientHeight", "loadMoreMessages", "clearConversation", "exportConversation", "getHeaderActions", "title", "onClick", "class", "isActive", "badge", "animate", "goBackToConversations", "navigate", "openUserProfile", "userId", "formatLastActive", "lastActive", "diff", "getTime", "minutes", "toggleConversationSettings", "shouldShowDateSeparator", "currentMessage", "previousMessage", "currentDate", "toDateString", "previousDate", "formatDateSeparator", "today", "yesterday", "setDate", "getDate", "toLocaleDateString", "weekday", "year", "month", "day", "getSystemMessageIcon", "user_joined", "user_left", "call_started", "call_ended", "message_deleted", "systemType", "onMessageClick", "onMessageContextMenu", "onMessageHover", "isHovering", "shouldShowAvatar", "nextMessage", "onAvatarError", "isGroupConversation", "shouldShowSenderName", "getUserColor", "colors", "charCodeAt", "downloadSelectedFiles", "shareSelectedFiles", "deleteSelectedFiles", "updateAnalytics", "exportAnalytics", "getMessageTypeIcon", "image", "video", "voice", "location", "contact", "createNewIntegration", "selectIntegrationCategory", "category", "getIntegrationCategoryIcon", "CRM", "Productivité", "Communication", "Analytics", "Sécurité", "getAvailableIntegrations", "configureIntegration", "integration", "toggleIntegration", "getActiveIntegrations", "getIntegrationStatusText", "texts", "inactive", "pending", "formatLastActivity", "lastActivity", "viewIntegrationLogs", "editIntegration", "testIntegration", "removeIntegration", "editWebhook", "webhook", "testWebhook", "deleteWebhook", "createWebhook", "hasActiveModal", "closeActiveModal", "flipImage", "direction", "applyImageFilter", "filterName", "updateImageAdjustments", "resetImageAdjustments", "addTextToImage", "fontFamily", "fontSize", "createNewFolder", "folderName", "prompt", "modifiedAt", "uploadFiles", "toggleFile<PERSON>iew", "closeFileManager", "selectFolder", "folder", "renameFolder", "newName", "deleteFolder", "f", "getStorageUsagePercentage", "getStorageUsed", "getStorageTotal", "getTotalFilesCount", "navigateToFolder", "crumb", "onFileSearch", "applyFileFilters", "sortFiles", "getFilteredFiles", "toggleFileSelection", "indexOf", "openFile", "formatFileDate", "shareFile", "deleteFile", "forwarded", "getOnlineUsersCount", "truncateText", "max<PERSON><PERSON><PERSON>", "substring", "hasImage", "a", "isVoiceMessage", "hasFile", "getFileName", "getMessageBubbleClass", "isOwn", "isLocationMessage", "isContactMessage", "closeCamera", "setCameraMode", "mode", "toggleCameraFlash", "switchCamera", "openGallery", "capturePhoto", "startVideoRecording", "stopVideoRecording", "toggleCameraGrid", "formatRecordingTime", "closeImageEditor", "saveEditedImage", "startCropResize", "corner", "editTextElement", "setImageEditorMode", "setCropRatio", "ratio", "rotateImage", "angle", "insertGif", "gif", "patchValue", "manageStickerPacks", "toggleEmojiPicker", "openDrawingTool", "closeDrawingTool", "selectDrawingTool", "tool", "selectDrawingColor", "undoDrawing", "redoDrawing", "clearDrawing", "startDrawing", "draw", "stopDrawing", "saveDrawing", "hasUserReacted", "reaction", "getReactionTooltip", "canDeleteMessage", "canEditMessage", "exitSelectionMode", "clear", "deleteSelectedMessages", "forwardSelectedMessages", "copySelectedMessages", "selectedMessagesArray", "textToCopy", "join", "then", "catch", "cancelReply", "removeSelectedFile", "formatRecordingDuration", "cancelVoiceRecording", "canCreatePoll", "opt", "closeLocationPicker", "getCurrentLocation", "geolocation", "getCurrentPosition", "position", "latitude", "coords", "longitude", "searchLocation", "onLocationSearch", "selectLocationResult", "editLocationMessage", "shareLocation", "closeContactPicker", "onContactSearch", "getFilteredContactsForSharing", "contacts", "phone", "email", "selectContactForSharing", "viewContactDetails", "showInfo", "createNewContact", "shareContact", "contactMessage", "openStickerPicker", "openGifPicker", "onEmojiSearch", "insert<PERSON><PERSON><PERSON>", "getFilteredEmojiCategories", "previewEmoji", "selectStickerPack", "packId", "getSelectedStickerPack", "pack", "insertSticker", "sticker", "stickerMessage", "onGifSearch", "selectGifCategory", "getFilteredGifs", "cancelDelete", "confirmDelete", "closeForwardModal", "getFilteredContacts", "avatar", "toggleContactSelection", "contactId", "confirmForward", "previousImage", "nextImage", "getToastIcon", "success", "warning", "info", "getTotalMessagesCount", "getPhotosCount", "getFilesCount", "updateConversationSettings", "closeTranslationPanel", "swapLanguages", "temp", "copyTranslation", "insertTranslation", "shareTranslation", "translationMessage", "closePollCreator", "addPollOption", "removePollOption", "createPoll", "pollMessage", "poll", "question", "options", "settings", "votes", "totalVotes", "hasMessageContent", "canSendMessage", "searchEmojis", "getFilteredEmojis", "emojis", "toggleNotificationPanel", "onSearchKeyPress", "toggleSearchBar", "navigateToMessage", "highlightSearchTerms", "query", "getPinnedMessagesCount", "togglePinnedMessages", "scrollToPinnedMessage", "formatMessageDate", "declineCall", "acceptCall", "formatCallDuration", "getCallStatusText", "toggleCallMinimize", "applyFormatting", "formattedContent", "hasFormatting", "insertLink", "insertTable", "table", "insertList", "listItem", "insertMentionSymbol", "insertHashtagSymbol", "clearFormatting", "cleanContent", "toggleFormattingToolbar", "hideAutoCorrectSuggestions", "applyCorrection", "original", "correction", "<PERSON><PERSON><PERSON>nt", "ignoreCorrection", "s", "triggerFileInput", "input", "document", "createElement", "accept", "onchange", "getInputPlaceholder", "onInputChange", "detectMentions", "detectHashtags", "detectCommands", "handleTyping", "onInputKeyDown", "max", "min", "insertMention", "onInputKeyUp", "updateSuggestions", "onInputFocus", "onInputBlur", "onInputPaste", "clipboardData", "window", "pastedData", "getData", "detectLinks", "onInputScroll", "mentionMatch", "match", "getFilteredUsers", "hashtagMatch", "getFilteredHashtags", "commandMatch", "getFilteredCommands", "urlRegex", "links", "newContent", "insertHashtag", "hashtag", "insertCommand", "command", "setSelectedMentionIndex", "getCharacterCount", "hasFormattedText", "getLanguageName", "languageCode", "languages", "fr", "en", "es", "de", "it", "translateMessage", "showAutoCorrections", "corrections", "hashtags", "commands", "description", "openCamera", "openLocationPicker", "openContactPicker", "openPollCreator", "hideQuickReplies", "insertQuickReply", "reply", "onSearchInput", "getEmojisForCategory", "getFileAcceptTypes", "closeAllMenus", "startVideoCall", "startVoiceCall", "__decorate", "static", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  On<PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  AfterViewChecked,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { User } from '@app/models/user.model';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport {\n  Message,\n  Conversation,\n  MessageType,\n  CallType,\n} from 'src/app/models/message.model';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { MessageService } from '@app/services/message.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: 'message-chat.component.html',\n  styleUrls: ['./message-chat.component.css'],\n})\nexport class MessageChatComponent\n  implements OnInit, On<PERSON><PERSON>roy, AfterViewChecked\n{\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === PROPRIÉTÉS PRINCIPALES ===\n  messages: Message[] = [];\n  messageForm: FormGroup;\n  conversation: Conversation | null = null;\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  currentUsername: string = 'You';\n  otherParticipant: User | null = null;\n  isAdmin = false;\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n  isUploading = false;\n  isTyping = false;\n  typingTimeout: any;\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n\n  // === PAGINATION ET CHARGEMENT ===\n  private readonly MAX_MESSAGES_PER_SIDE = 5;\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private readonly MAX_TOTAL_MESSAGES = 100;\n  private currentPage = 1;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  private subscriptions = new Subscription();\n\n  // === INTERFACE ET THÈMES ===\n  selectedTheme: string = 'theme-default';\n  showThemeSelector = false;\n  showMainMenu = false;\n  showEmojiPicker = false;\n  showSearchBar = false;\n  showPinnedMessages = false;\n  showStatusSelector = false;\n  showNotificationPanel = false;\n  showNotificationSettings = false;\n  showUserStatusPanel = false;\n  showCallHistoryPanel = false;\n  showCallStatsPanel = false;\n  showVoiceMessagesPanel = false;\n\n  // === APPELS ===\n  incomingCall: any = null;\n  activeCall: any = null;\n  showCallModal = false;\n  showActiveCallModal = false;\n  isCallMuted = false;\n  isVideoEnabled = true;\n  callDuration = 0;\n  callTimer: any = null;\n\n  // === NOTIFICATIONS ET MESSAGES ===\n  notifications: any[] = [];\n  unreadNotificationCount: number = 0;\n  selectedNotifications: Set<string> = new Set();\n  showDeleteConfirmModal: boolean = false;\n  editingMessageId: string | null = null;\n  editingContent = '';\n  replyingToMessage: any = null;\n\n  // === RECHERCHE ===\n  searchQuery = '';\n  isSearching = false;\n  searchMode = false;\n  searchResults: any[] = [];\n  showSearch = false;\n  showAdvancedSearch = false;\n\n  // === PANNEAUX ===\n  pinnedMessages: any[] = [];\n  showReactionPicker: any = {};\n  showDeleteConfirm: any = {};\n  showPinConfirm: any = {};\n  isPinning: any = {};\n  showMessageOptions: any = {};\n\n  // === TRANSFERT ===\n  showForwardModal = false;\n  forwardingMessage: any = null;\n  selectedConversations: string[] = [];\n  availableConversations: any[] = [];\n  isForwarding = false;\n  isLoadingConversations = false;\n\n  // === ÉMOJIS ET AUTOCOLLANTS ===\n  emojiCategories: any[] = [\n    { id: 'recent', name: 'Récents', icon: 'fas fa-clock' },\n    { id: 'smileys', name: 'Smileys', icon: 'fas fa-smile' },\n    { id: 'people', name: 'Personnes', icon: 'fas fa-user' },\n    { id: 'animals', name: 'Animaux', icon: 'fas fa-paw' },\n    { id: 'food', name: 'Nourriture', icon: 'fas fa-apple-alt' },\n    { id: 'travel', name: 'Voyage', icon: 'fas fa-plane' },\n    { id: 'activities', name: 'Activités', icon: 'fas fa-football-ball' },\n    { id: 'objects', name: 'Objets', icon: 'fas fa-lightbulb' },\n    { id: 'symbols', name: 'Symboles', icon: 'fas fa-heart' },\n    { id: 'flags', name: 'Drapeaux', icon: 'fas fa-flag' },\n  ];\n  selectedEmojiCategory = 'recent';\n  emojiSearchQuery = '';\n  recentEmojis: any[] = [];\n  previewedEmoji: any = null;\n  showStickerPicker = false;\n  stickerPacks: any[] = [];\n  selectedStickerPack = '';\n\n  // === GIFS ===\n  showGifPicker = false;\n  gifSearchQuery = '';\n  gifCategories = ['Trending', 'Reactions', 'Animals', 'Sports', 'TV & Movies'];\n  selectedGifCategory = 'Trending';\n\n  // === OUTIL DE DESSIN ===\n  showDrawingTool = false;\n  selectedDrawingTool = 'pen';\n  drawingColors = [\n    '#000000',\n    '#FF0000',\n    '#00FF00',\n    '#0000FF',\n    '#FFFF00',\n    '#FF00FF',\n    '#00FFFF',\n    '#FFFFFF',\n  ];\n  selectedDrawingColor = '#000000';\n  customDrawingColor = '#000000';\n  drawingSize = 5;\n\n  // === CAMÉRA ===\n  showCamera = false;\n  cameraMode = 'photo';\n  flashEnabled = false;\n  showCameraGrid = false;\n  showFocusIndicator = false;\n  focusX = 0;\n  focusY = 0;\n  lastCapturedImage = '';\n  isRecordingVideo = false;\n  videoRecordingDuration = 0;\n\n  // === ÉDITEUR D'IMAGES ===\n  showImageEditor = false;\n  imageEditorMode = 'crop';\n  cropArea = { x: 0, y: 0, width: 100, height: 100 };\n  cropRatio = 'free';\n  imageFilters: any[] = [\n    { name: 'none', label: 'Aucun', css: 'none' },\n    { name: 'grayscale', label: 'Noir & Blanc', css: 'grayscale(100%)' },\n    { name: 'sepia', label: 'Sépia', css: 'sepia(100%)' },\n    { name: 'vintage', label: 'Vintage', css: 'sepia(50%) contrast(1.2)' },\n    { name: 'bright', label: 'Lumineux', css: 'brightness(1.3)' },\n    { name: 'contrast', label: 'Contraste', css: 'contrast(1.5)' },\n  ];\n  selectedImageFilter = 'none';\n  imageAdjustments = { brightness: 0, contrast: 0, saturation: 0, hue: 0 };\n  imageTextElements: any[] = [];\n  newTextContent = '';\n  textFontFamily = 'Arial';\n  textFontSize = 24;\n  textColor = '#000000';\n  availableFonts = [\n    { value: 'Arial', label: 'Arial' },\n    { value: 'Helvetica', label: 'Helvetica' },\n    { value: 'Times New Roman', label: 'Times New Roman' },\n    { value: 'Courier New', label: 'Courier New' },\n  ];\n\n  // === GESTIONNAIRE DE FICHIERS ===\n  showFileManager = false;\n  fileViewMode = 'grid';\n  fileFolders: any[] = [];\n  selectedFolder: any = null;\n  fileBreadcrumbs: any[] = [];\n  fileSearchQuery = '';\n  fileTypeFilter = '';\n  fileSortBy = 'name';\n  selectedFiles: string[] = [];\n\n  // === ANALYTICS ===\n  showAnalyticsDashboard = false;\n  analyticsTimeRange = '7d';\n  analyticsData: any = {\n    totalMessages: 0,\n    activeUsers: 0,\n    avgResponseTime: '0s',\n    filesShared: 0,\n    messagesChange: 0,\n    usersChange: 0,\n    responseTimeChange: 0,\n    filesChange: 0,\n    topUsers: [],\n    messageTypes: [],\n    longestConversation: 0,\n    avgConversationDuration: '0m',\n    topEmojis: [],\n  };\n\n  // === INTÉGRATIONS ===\n  showIntegrations = false;\n  integrationCategories = [\n    'CRM',\n    'Productivité',\n    'Communication',\n    'Analytics',\n    'Sécurité',\n  ];\n  selectedIntegrationCategory = 'CRM';\n  webhooks: any[] = [];\n\n  // === ZONE DE SAISIE AVANCÉE ===\n  showQuickReplies = false;\n  quickReplies: string[] = ['Merci !', \"D'accord\", 'Parfait', 'À bientôt'];\n  showMentionSuggestions = false;\n  mentionSuggestions: any[] = [];\n  selectedMentionIndex = 0;\n  activeMentions: any[] = [];\n  showHashtagSuggestions = false;\n  hashtagSuggestions: any[] = [];\n  selectedHashtagIndex = 0;\n  activeHashtags: any[] = [];\n  showCommandSuggestions = false;\n  commandSuggestions: any[] = [];\n  selectedCommandIndex = 0;\n  activeLinks: any[] = [];\n  inputHeight = 40;\n  showFormattingToolbar = false;\n  detectedLanguage = 'fr';\n  autoCorrections: any[] = [];\n  showAutoCorrectSuggestions = false;\n  autoCorrectSuggestions: any[] = [];\n\n  // === TRADUCTION ===\n  showTranslationPanel = false;\n  translationFrom = 'auto';\n  translationTo = 'fr';\n  originalTextForTranslation = '';\n  translatedText = '';\n  isTranslating = false;\n  supportedLanguages = [\n    { code: 'fr', name: 'Français' },\n    { code: 'en', name: 'English' },\n    { code: 'es', name: 'Español' },\n    { code: 'de', name: 'Deutsch' },\n    { code: 'it', name: 'Italiano' },\n  ];\n\n  // === SONDAGES ===\n  showPollCreator = false;\n  pollQuestion = '';\n  pollOptions: any[] = [{ text: '' }, { text: '' }];\n  pollSettings = {\n    allowMultiple: false,\n    anonymous: false,\n    showResults: true,\n    showVoters: true,\n  };\n  pollExpiry = '';\n  customPollExpiry = '';\n\n  // === LOCALISATION ===\n  showLocationPicker = false;\n  showLocationSearch = false;\n  locationSearchQuery = '';\n  locationSearchResults: any[] = [];\n  selectedLocation: any = null;\n  showLocationMessage = false;\n  locationMessage = '';\n\n  // === CONTACTS ===\n  showContactPicker = false;\n  contactSearchQuery = '';\n  selectedContactForSharing: any = null;\n\n  // === PROGRAMMATION D'ENVOI ===\n  showScheduleMessage = false;\n  customScheduleDate = '';\n  customScheduleTime = '';\n  scheduleTimezone = 'Europe/Paris';\n  availableTimezones = [\n    { value: 'Europe/Paris', label: 'Paris (CET)' },\n    { value: 'America/New_York', label: 'New York (EST)' },\n    { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },\n  ];\n\n  // === ÉTATS DES MESSAGES ===\n  highlightedMessageId = '';\n  searchResultIds: string[] = [];\n  selectionMode = false;\n  selectedMessages: Set<string> = new Set();\n  hoveredMessageId = '';\n  playingVoiceId = '';\n  showScrollToBottom = false;\n  unreadMessagesCount = 0;\n\n  // === VISUALISEUR D'IMAGES ===\n  showImageViewer = false;\n  currentImageIndex = 0;\n  imageGallery: any[] = [];\n\n  // === TOAST ===\n  showToast = false;\n  toastMessage = '';\n  toastType = 'info';\n\n  // === PANNEAUX D'INFORMATIONS ===\n  showConversationInfo = false;\n  showConversationSettings = false;\n  conversationSettings = {\n    notifications: true,\n    soundNotifications: true,\n    readReceipts: true,\n    typingIndicators: true,\n  };\n\n  // === VARIABLES D'ÉTAT POUR LES NOUVELLES FONCTIONNALITÉS ===\n  isSendingMessage = false;\n  isUserTyping = false;\n  voiceRecordingState = 'idle';\n  voiceRecordingSize = 0;\n  recordingWaveform: number[] = [];\n  voiceRecordingQuality = 'medium';\n  voiceEffects: string[] = [];\n  selectedContacts: string[] = [];\n  isUpdatingStatus = false;\n\n  // === VARIABLES POUR LES PIÈCES JOINTES ===\n  showAttachmentMenu = false;\n  attachmentFiles: File[] = [];\n  fileCaptions: string[] = [];\n  fileUploadProgress: number[] = [];\n\n  // === PROPRIÉTÉS POUR LE TEMPLATE ===\n  messageContent = '';\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private authUserService: AuthuserService,\n    private MessageService: MessageService,\n    private toastService: ToastService,\n    private fb: FormBuilder,\n    private cdr: ChangeDetectorRef,\n    private userStatusService: UserStatusService\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  // === CONSTANTES OPTIMISÉES ===\n  readonly c = {\n    reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n    delays: { away: 300000, typing: 3000, scroll: 100, anim: 300 },\n    error: 'Erreur. Veuillez réessayer.',\n    trackById: (i: number, item: any): string => item?.id || i.toString(),\n    notifications: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    status: {\n      online: {\n        text: 'En ligne',\n        color: 'text-green-500',\n        icon: 'fas fa-circle',\n      },\n      offline: {\n        text: 'Hors ligne',\n        color: 'text-gray-500',\n        icon: 'far fa-circle',\n      },\n      away: { text: 'Absent', color: 'text-yellow-500', icon: 'fas fa-clock' },\n      busy: {\n        text: 'Occupé',\n        color: 'text-red-500',\n        icon: 'fas fa-minus-circle',\n      },\n    },\n    themes: [\n      { key: 'theme-default', label: 'Défaut', color: '#4f5fad' },\n      { key: 'theme-feminine', label: 'Rose', color: '#ff6b9d' },\n      { key: 'theme-masculine', label: 'Bleu', color: '#3d85c6' },\n      { key: 'theme-neutral', label: 'Vert', color: '#6aa84f' },\n    ],\n    calls: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n  };\n\n  // === GETTERS ===\n  get availableReactions(): string[] {\n    return this.c.reactions;\n  }\n  get commonEmojis(): string[] {\n    return this.MessageService.getCommonEmojis();\n  }\n  get unreadNotificationsCount(): number {\n    return this.unreadNotificationCount;\n  }\n  get currentUserStatus(): string {\n    return 'online';\n  }\n  get onlineUsersCount(): number {\n    return 5;\n  }\n\n  // === LIFECYCLE HOOKS ===\n  ngOnInit(): void {\n    this.initializeComponent();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n    if (this.callTimer) clearInterval(this.callTimer);\n    if (this.typingTimeout) clearTimeout(this.typingTimeout);\n  }\n\n  ngAfterViewChecked(): void {\n    this.cdr.detectChanges();\n  }\n\n  // === INITIALISATION ===\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n    // Initialiser des données de test pour la démo\n    this.initializeTestData();\n  }\n\n  private loadConversation(): void {\n    this.route.params.subscribe((params) => {\n      const conversationId = params['id'];\n      if (conversationId) {\n        this.getConversation(conversationId);\n        this.getMessages();\n      }\n    });\n  }\n\n  private setupSubscriptions(): void {\n    // Configuration des abonnements WebSocket et autres\n  }\n\n  // === GESTION DES CONVERSATIONS ===\n  getConversation(conversationId: string): void {\n    this.loading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: (conversation) => {\n        this.conversation = conversation;\n        this.otherParticipant =\n          conversation.participants?.find((p) => p.id !== this.currentUserId) ||\n          null;\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = error;\n        this.loading = false;\n        console.error('Erreur lors du chargement de la conversation:', error);\n      },\n    });\n  }\n\n  // === GESTION DES MESSAGES ===\n  getMessages(): void {\n    if (!this.conversation?.id) return;\n\n    this.MessageService.getMessages(\n      this.conversation.id,\n      this.currentPage.toString(),\n      this.MAX_MESSAGES_TO_LOAD.toString()\n    ).subscribe({\n      next: (messages) => {\n        if (this.currentPage === 1) {\n          this.messages = messages;\n        } else {\n          this.messages = [...messages, ...this.messages];\n        }\n        this.hasMoreMessages = messages.length === this.MAX_MESSAGES_TO_LOAD;\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n      },\n    });\n  }\n\n  sendMessage(): void {\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content || !this.conversation?.id) return;\n\n    this.isSendingMessage = true;\n\n    // Créer un message temporaire pour l'affichage immédiat\n    const tempMessage: Message = {\n      id: Date.now().toString(),\n      content,\n      sender: { id: this.currentUserId!, username: this.currentUsername },\n      timestamp: new Date(),\n      conversationId: this.conversation.id,\n      type: 'text' as MessageType,\n    };\n\n    this.messages.push(tempMessage);\n    this.messageForm.reset();\n    this.scrollToBottom();\n    this.isSendingMessage = false;\n\n    // Simuler l'envoi réel (à remplacer par l'appel API réel)\n    /*\n    this.MessageService.sendMessage(\n      this.conversation.id,\n      content,\n      'text' as MessageType,\n      this.currentUserId || ''\n    ).subscribe({\n      next: (message) => {\n        // Remplacer le message temporaire par le vrai message\n        const index = this.messages.findIndex(m => m.id === tempMessage.id);\n        if (index > -1) {\n          this.messages[index] = message;\n        }\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        // Supprimer le message temporaire en cas d'erreur\n        this.messages = this.messages.filter(m => m.id !== tempMessage.id);\n      },\n    });\n    */\n  }\n\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event: any): void {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.attachmentFiles = Array.from(files);\n      this.generatePreviews();\n    }\n  }\n\n  removeAttachment(index: number): void {\n    this.attachmentFiles.splice(index, 1);\n    if (this.attachmentFiles.length === 0) {\n      this.previewUrl = null;\n    }\n  }\n\n  // === GESTION DES RÉACTIONS ===\n  toggleReaction(messageId: string, emoji: string): void {\n    // Simulation de la réaction - à implémenter avec le vrai service\n    const message = this.messages.find((m) => m.id === messageId);\n    if (message) {\n      if (!(message as any).reactions) (message as any).reactions = [];\n      const existingReaction = (message as any).reactions.find(\n        (r: any) => r.emoji === emoji\n      );\n      if (existingReaction) {\n        existingReaction.count = (existingReaction.count || 0) + 1;\n      } else {\n        (message as any).reactions.push({\n          emoji,\n          count: 1,\n          users: [this.currentUserId],\n        });\n      }\n    }\n  }\n\n  addReaction(messageId: string, emoji: string): void {\n    this.toggleReaction(messageId, emoji);\n    this.showReactionPicker = {};\n  }\n\n  // === GESTION DE L'ÉDITION ===\n  startEditMessage(message: any): void {\n    this.editingMessageId = message.id;\n    this.editingContent = message.content || '';\n    this.showMessageOptions = {};\n  }\n\n  cancelEdit(): void {\n    this.editingMessageId = null;\n    this.editingContent = '';\n  }\n\n  saveEdit(messageId: string): void {\n    if (this.editingContent?.trim()) {\n      this.MessageService.editMessage(\n        messageId,\n        this.editingContent.trim()\n      ).subscribe({\n        next: () => {\n          this.cancelEdit();\n          this.getMessages();\n        },\n        error: (error) => {\n          console.error('Erreur lors de la modification:', error);\n        },\n      });\n    }\n  }\n\n  onEditKeyDown(event: KeyboardEvent, messageId: string): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.saveEdit(messageId);\n    } else if (event.key === 'Escape') {\n      this.cancelEdit();\n    }\n  }\n\n  // === GESTION DE LA SUPPRESSION ===\n  deleteMessage(messageId: string): void {\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n      this.MessageService.deleteMessage(messageId).subscribe({\n        next: () => {\n          this.getMessages();\n          this.showMessageOptions = {};\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression:', error);\n        },\n      });\n    }\n  }\n\n  // === GESTION DES APPELS ===\n  startCall(type: CallType): void {\n    if (!this.otherParticipant?.id) return;\n\n    this.MessageService.initiateCall(this.otherParticipant.id, type).subscribe({\n      next: (call) => {\n        this.activeCall = call;\n        this.showActiveCallModal = true;\n        this.startCallTimer();\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'initiation de l'appel:\", error);\n        this.toastService.showError(\"Impossible d'initier l'appel\");\n      },\n    });\n  }\n\n  private startCallTimer(): void {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n    }, 1000);\n  }\n\n  endCall(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.activeCall = null;\n    this.showActiveCallModal = false;\n    this.callDuration = 0;\n  }\n\n  toggleMute(): void {\n    this.isCallMuted = !this.isCallMuted;\n  }\n\n  toggleVideo(): void {\n    this.isVideoEnabled = !this.isVideoEnabled;\n  }\n\n  // === MÉTHODES UTILITAIRES ===\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  formatMessageTime(timestamp: any): string {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  trackByMessageId(index: number, message: any): string {\n    return message?.id || index.toString();\n  }\n\n  // === GESTION DE L'INTERFACE ===\n  toggleSearch(): void {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.clearSearch();\n    }\n  }\n\n  toggleNotifications(): void {\n    this.showNotificationPanel = !this.showNotificationPanel;\n  }\n\n  toggleConversationInfo(): void {\n    this.showConversationInfo = !this.showConversationInfo;\n  }\n\n  toggleThemeSelector(): void {\n    this.showThemeSelector = !this.showThemeSelector;\n  }\n\n  toggleMainMenu(): void {\n    this.showMainMenu = !this.showMainMenu;\n  }\n\n  toggleAdvancedSearch(): void {\n    this.showAdvancedSearch = !this.showAdvancedSearch;\n  }\n\n  toggleStatusSelector(): void {\n    this.showStatusSelector = !this.showStatusSelector;\n  }\n\n  toggleUserStatusPanel(): void {\n    this.showUserStatusPanel = !this.showUserStatusPanel;\n  }\n\n  // === GESTION DE LA RECHERCHE ===\n\n  private performSearch(): void {\n    this.isSearching = true;\n    // Simulation de recherche\n    setTimeout(() => {\n      this.searchResults = this.messages.filter((message) =>\n        message.content?.toLowerCase().includes(this.searchQuery.toLowerCase())\n      );\n      this.isSearching = false;\n    }, 500);\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchMode = false;\n    this.searchResults = [];\n    this.searchResultIds = [];\n  }\n\n  highlightSearchTerm(content: string): string {\n    if (!this.searchQuery || !content) return content;\n    const regex = new RegExp(`(${this.searchQuery})`, 'gi');\n    return content.replace(\n      regex,\n      '<mark class=\"whatsapp-search-highlight\">$1</mark>'\n    );\n  }\n\n  scrollToMessage(messageId: string): void {\n    this.highlightedMessageId = messageId;\n    // Logique pour faire défiler vers le message\n    setTimeout(() => {\n      this.highlightedMessageId = '';\n    }, 3000);\n  }\n\n  // === GESTION DES MESSAGES ÉPINGLÉS ===\n  showAllPinnedMessages(): void {\n    // Afficher tous les messages épinglés\n  }\n\n  closePinnedMessages(): void {\n    this.pinnedMessages = [];\n  }\n\n  togglePinMessage(messageId: string): void {\n    const message = this.messages.find((m) => m.id === messageId);\n    if (message) {\n      message.pinned = !message.pinned;\n      if (message.pinned) {\n        this.pinnedMessages.push(message);\n      } else {\n        this.pinnedMessages = this.pinnedMessages.filter(\n          (m) => m.id !== messageId\n        );\n      }\n    }\n    this.showMessageOptions = {};\n  }\n\n  // === GESTION DES OPTIONS DE MESSAGE ===\n  toggleMessageOptions(messageId: string): void {\n    if (this.showMessageOptions[messageId]) {\n      this.showMessageOptions = {};\n    } else {\n      this.showMessageOptions = { [messageId]: true };\n    }\n  }\n\n  toggleReactionPicker(messageId: string): void {\n    if (this.showReactionPicker[messageId]) {\n      this.showReactionPicker = {};\n    } else {\n      this.showReactionPicker = { [messageId]: true };\n    }\n  }\n\n  // === GESTION DES ACTIONS DE MESSAGE ===\n  replyToMessage(message: any): void {\n    this.replyingToMessage = message;\n    this.showMessageOptions = {};\n  }\n\n  forwardMessage(message: any): void {\n    this.forwardingMessage = message;\n    this.showForwardModal = true;\n    this.showMessageOptions = {};\n  }\n\n  copyMessage(message: any): void {\n    if (navigator.clipboard && message.content) {\n      navigator.clipboard.writeText(message.content);\n      this.toastService.showSuccess('Message copié');\n    }\n    this.showMessageOptions = {};\n  }\n\n  selectMessage(messageId: string): void {\n    this.selectionMode = true;\n    this.toggleMessageSelection(messageId);\n    this.showMessageOptions = {};\n  }\n\n  toggleMessageSelection(messageId: string): void {\n    if (this.selectedMessages.has(messageId)) {\n      this.selectedMessages.delete(messageId);\n    } else {\n      this.selectedMessages.add(messageId);\n    }\n\n    if (this.selectedMessages.size === 0) {\n      this.selectionMode = false;\n    }\n  }\n\n  private _selectedMessages: string[] = [];\n\n  // === GESTION DES ÉMOJIS ===\n  openEmojiPicker(messageId?: string): void {\n    this.showEmojiPicker = true;\n    if (messageId) {\n      this.showReactionPicker = { [messageId]: true };\n    }\n  }\n\n  closeEmojiPicker(): void {\n    this.showEmojiPicker = false;\n    this.showReactionPicker = {};\n  }\n\n  selectEmojiCategory(categoryId: string): void {\n    this.selectedEmojiCategory = categoryId;\n  }\n\n  addEmojiToMessage(emoji: string): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    this.messageForm.get('content')?.setValue(currentContent + emoji);\n  }\n\n  // === GESTION DES PIÈCES JOINTES ===\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n\n  openFileInput(): void {\n    this.fileInput.nativeElement.click();\n  }\n\n  // === GESTION DES MÉDIAS ===\n  openImageViewer(message: any): void {\n    this.showImageViewer = true;\n    this.currentImageIndex = 0;\n    this.imageGallery = [message];\n  }\n\n  closeImageViewer(): void {\n    this.showImageViewer = false;\n    this.imageGallery = [];\n  }\n\n  // === GESTION DES MESSAGES VOCAUX ===\n  toggleVoicePlayback(messageId: string): void {\n    if (this.playingVoiceId === messageId) {\n      this.playingVoiceId = '';\n    } else {\n      this.playingVoiceId = messageId;\n    }\n  }\n\n  startVoiceRecording(): void {\n    this.isRecordingVoice = true;\n    this.voiceRecordingDuration = 0;\n    // Logique d'enregistrement vocal\n  }\n\n  stopVoiceRecording(): void {\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n  }\n\n  // === GESTION DES THÈMES ===\n  changeTheme(theme: string): void {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n  }\n\n  getThemeOptions(): any[] {\n    return this.c.themes;\n  }\n\n  // === GESTION DU STATUT ===\n  updateUserStatus(status: string): void {\n    this.isUpdatingStatus = true;\n    // Simuler une mise à jour\n    setTimeout(() => {\n      this.isUpdatingStatus = false;\n    }, 1000);\n  }\n\n  getStatusOptions(): any[] {\n    return Object.entries(this.c.status).map(([key, value]) => ({\n      key,\n      ...value,\n    }));\n  }\n\n  getStatusIcon(status: string): string {\n    return (\n      this.c.status[status as keyof typeof this.c.status]?.icon ||\n      'fas fa-circle'\n    );\n  }\n\n  getStatusColor(status: string): string {\n    return (\n      this.c.status[status as keyof typeof this.c.status]?.color ||\n      'text-gray-500'\n    );\n  }\n\n  getStatusText(status: string): string {\n    return (\n      this.c.status[status as keyof typeof this.c.status]?.text || 'Inconnu'\n    );\n  }\n\n  // === MÉTHODES POUR LE TEMPLATE ===\n  formatMessageContent(content: string | undefined): string {\n    if (!content) return '';\n    return content\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n      .replace(/`(.*?)`/g, '<code>$1</code>')\n      .replace(/\\n/g, '<br>');\n  }\n\n  onImageLoad(event: any): void {\n    // Gérer le chargement d'image\n  }\n\n  onImageError(event: any): void {\n    event.target.src = 'assets/images/image-error.png';\n  }\n\n  onVideoLoaded(event: any, message?: any): void {\n    // Gérer le chargement vidéo\n  }\n\n  // === MÉTHODES POUR LES ANALYTICS ===\n  openAnalyticsDashboard(): void {\n    this.showAnalyticsDashboard = true;\n  }\n\n  closeAnalyticsDashboard(): void {\n    this.showAnalyticsDashboard = false;\n  }\n\n  // === MÉTHODES POUR LES INTÉGRATIONS ===\n  openIntegrations(): void {\n    this.showIntegrations = true;\n  }\n\n  closeIntegrations(): void {\n    this.showIntegrations = false;\n  }\n\n  // === MÉTHODES POUR LES RÉPONSES RAPIDES ===\n  sendQuickMessage(content: string): void {\n    const control = this.messageForm.get('content');\n    if (control) {\n      control.setValue(content);\n      this.sendMessage();\n    }\n  }\n\n  // === MÉTHODES POUR LA COMPATIBILITÉ ===\n  initiateCall(type: CallType): void {\n    this.startCall(type);\n  }\n\n  // === CORRECTION DES MÉTHODES MANQUANTES ===\n  private generatePreviews(): void {\n    this.attachmentFiles.forEach((file) => {\n      if (file.type.startsWith('image/')) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          this.previewUrl = e.target?.result || null;\n        };\n        reader.readAsDataURL(file);\n      }\n    });\n  }\n\n  private loadCurrentUser(): void {\n    // Utiliser getCurrentUser au lieu de getUser\n    this.authUserService.getCurrentUser().subscribe({\n      next: (user: any) => {\n        this.currentUserId = user?.id || null;\n        this.currentUsername = user?.username || 'You';\n      },\n      error: (error: any) =>\n        console.error(\"Erreur lors du chargement de l'utilisateur:\", error),\n    });\n  }\n\n  // === MÉTHODES SUPPLÉMENTAIRES POUR LE TEMPLATE ===\n\n  // Méthodes pour les types de messages\n  getMessageType(message: any): string {\n    if (message.content && !message.attachments?.length) return 'text';\n    if (message.attachments?.some((att: any) => att.type === 'image'))\n      return 'image';\n    if (message.attachments?.some((att: any) => att.type === 'voice'))\n      return 'voice';\n    if (message.attachments?.some((att: any) => att.type === 'file'))\n      return 'file';\n    if (message.attachments?.some((att: any) => att.type === 'video'))\n      return 'video';\n    if (message.type === 'location') return 'location';\n    if (message.type === 'contact') return 'contact';\n    return 'text';\n  }\n\n  // Méthodes pour les médias\n  getImageUrl(message: any): string {\n    return (\n      message.imageUrl ||\n      message.attachments?.find((att: any) => att.type === 'image')?.url ||\n      ''\n    );\n  }\n\n  getVideoUrl(message: any): string {\n    return (\n      message.videoUrl ||\n      message.attachments?.find((att: any) => att.type === 'video')?.url ||\n      ''\n    );\n  }\n\n  getVideoThumbnail(message: any): string {\n    return message.thumbnailUrl || '';\n  }\n\n  getVideoDuration(message: any): string {\n    return message.duration || '00:00';\n  }\n\n  // Méthodes pour les fichiers\n  getFileSize(message: any): string {\n    return this.formatFileSize(message.size || 0);\n  }\n\n  getFileIcon(message: any): string {\n    const fileType = message.fileType || message.attachments?.[0]?.type || '';\n    const icons: any = {\n      'application/pdf': 'fas fa-file-pdf',\n      'application/msword': 'fas fa-file-word',\n      'application/vnd.ms-excel': 'fas fa-file-excel',\n      'application/vnd.ms-powerpoint': 'fas fa-file-powerpoint',\n      'text/': 'fas fa-file-alt',\n      'image/': 'fas fa-file-image',\n      'video/': 'fas fa-file-video',\n      'audio/': 'fas fa-file-audio',\n    };\n\n    for (const [type, icon] of Object.entries(icons)) {\n      if (fileType.startsWith(type)) return icon as string;\n    }\n    return 'fas fa-file';\n  }\n\n  getFileType(message: any): string {\n    const fileType = message.fileType || message.attachments?.[0]?.type || '';\n    return fileType.split('/')[1]?.toUpperCase() || 'FICHIER';\n  }\n\n  // Méthodes pour les messages vocaux\n  formatVoiceDuration(current: number, total: number): string {\n    const formatTime = (seconds: number) => {\n      const mins = Math.floor(seconds / 60);\n      const secs = seconds % 60;\n      return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return `${formatTime(current)} / ${formatTime(total)}`;\n  }\n\n  getVoiceWaveform(message: any): any[] {\n    return Array.from({ length: 20 }, (_, i) => ({\n      height: Math.random() * 20 + 5,\n      active: false,\n      played: false,\n    }));\n  }\n\n  getVoiceProgress(messageId: string): number {\n    return 0;\n  }\n\n  getVoiceCurrentTime(messageId: string): number {\n    return 0;\n  }\n\n  getVoiceTotalDuration(messageId: string): number {\n    return 0;\n  }\n\n  getVoiceSpeed(messageId: string): string {\n    return '1x';\n  }\n\n  // Méthodes pour les états\n  isImageLoading(message: any): boolean {\n    return false;\n  }\n\n  isVoiceLoading(messageId: string): boolean {\n    return false;\n  }\n\n  isFileDownloading(message: any): boolean {\n    return false;\n  }\n\n  getImageLoadingProgress(message: any): number {\n    return 0;\n  }\n\n  getFileDownloadProgress(message: any): number {\n    return 0;\n  }\n\n  getImageDimensions(message: any): string {\n    return '';\n  }\n\n  // Méthodes pour les actions\n  downloadFile(message: any): void {\n    // Télécharger le fichier\n  }\n\n  previewFile(message: any): void {\n    // Prévisualiser le fichier\n  }\n\n  canPreviewFile(message: any): boolean {\n    const previewableTypes = [\n      'image/',\n      'video/',\n      'audio/',\n      'text/',\n      'application/pdf',\n    ];\n    return previewableTypes.some((type) => message.type?.startsWith(type));\n  }\n\n  hasVideo(message: any): boolean {\n    return (\n      message.type === 'video' ||\n      (message.attachments &&\n        message.attachments.some((att: any) => att.type === 'video'))\n    );\n  }\n\n  getEmojiName(emoji: string): string {\n    const emojiNames: any = {\n      '👍': 'Pouce levé',\n      '❤️': 'Cœur',\n      '😂': 'Rire',\n      '😮': 'Surprise',\n      '😢': 'Triste',\n      '😡': 'Colère',\n    };\n    return emojiNames[emoji] || emoji;\n  }\n\n  // Méthodes pour la localisation\n  openLocationViewer(message: any): void {\n    // Ouvrir le visualiseur de localisation\n  }\n\n  getLocationMapUrl(message: any): string {\n    return message.mapUrl || '';\n  }\n\n  getLocationName(message: any): string {\n    return message.locationName || 'Localisation';\n  }\n\n  getLocationAddress(message: any): string {\n    return message.address || '';\n  }\n\n  // Méthodes pour les contacts\n  openContactViewer(message: any): void {\n    // Ouvrir le visualiseur de contact\n  }\n\n  getContactAvatar(message: any): string {\n    return message.contactAvatar || 'assets/images/default-avatar.png';\n  }\n\n  getContactName(message: any): string {\n    return message.contactName || 'Contact';\n  }\n\n  getContactPhone(message: any): string {\n    return message.contactPhone || '';\n  }\n\n  // Méthodes pour les lecteurs\n  openVideoPlayer(message: any): void {\n    // Ouvrir le lecteur vidéo\n  }\n\n  seekVoiceMessage(messageId: string, event: any): void {\n    // Implémentation de la recherche dans le message vocal\n  }\n\n  changeVoiceSpeed(messageId: string): void {\n    // Changer la vitesse de lecture\n  }\n\n  // Méthodes pour les événements\n  onScroll(event: any): void {\n    // Gérer le défilement\n    const element = event.target;\n    this.showScrollToBottom =\n      element.scrollTop < element.scrollHeight - element.clientHeight - 100;\n  }\n\n  loadMoreMessages(): void {\n    this.isLoadingMore = true;\n    this.currentPage++;\n    this.getMessages();\n    setTimeout(() => {\n      this.isLoadingMore = false;\n    }, 1000);\n  }\n\n  clearConversation(): void {\n    if (confirm('Êtes-vous sûr de vouloir vider cette conversation ?')) {\n      this.messages = [];\n    }\n  }\n\n  exportConversation(): void {\n    // Exporter la conversation\n  }\n\n  // Méthodes pour les en-têtes\n  getHeaderActions(): any[] {\n    return [\n      {\n        icon: 'fas fa-search',\n        title: 'Rechercher',\n        onClick: () => this.toggleSearch(),\n        class: 'search-btn',\n        isActive: this.showSearch,\n      },\n      {\n        icon: 'fas fa-bell',\n        title: 'Notifications',\n        onClick: () => this.toggleNotifications(),\n        class: 'notification-btn',\n        badge:\n          this.unreadNotificationsCount > 0\n            ? {\n                count: this.unreadNotificationsCount,\n                class: 'bg-red-500',\n                animate: true,\n              }\n            : null,\n      },\n      {\n        icon: 'fas fa-phone',\n        title: 'Appel audio',\n        onClick: () => this.startCall('AUDIO' as CallType),\n        class: 'call-btn',\n      },\n      {\n        icon: 'fas fa-video',\n        title: 'Appel vidéo',\n        onClick: () => this.startCall('VIDEO' as CallType),\n        class: 'video-btn',\n      },\n    ];\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n\n  goBackToConversations(): void {\n    this.router.navigate(['/front/messages']);\n  }\n\n  openUserProfile(userId: string): void {\n    // Ouvrir le profil utilisateur\n  }\n\n  formatLastActive(lastActive: any): string {\n    if (!lastActive) return 'Hors ligne';\n    const date = new Date(lastActive);\n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const minutes = Math.floor(diff / 60000);\n\n    if (minutes < 1) return 'En ligne';\n    if (minutes < 60) return `Il y a ${minutes} min`;\n    if (minutes < 1440) return `Il y a ${Math.floor(minutes / 60)} h`;\n    return `Il y a ${Math.floor(minutes / 1440)} j`;\n  }\n\n  toggleConversationSettings(): void {\n    this.showConversationSettings = !this.showConversationSettings;\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  formatDateSeparator(timestamp: any): string {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) return \"Aujourd'hui\";\n    if (date.toDateString() === yesterday.toDateString()) return 'Hier';\n\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  }\n\n  getSystemMessageIcon(message: any): string {\n    const icons: any = {\n      user_joined: 'fas fa-user-plus',\n      user_left: 'fas fa-user-minus',\n      call_started: 'fas fa-phone',\n      call_ended: 'fas fa-phone-slash',\n      message_deleted: 'fas fa-trash',\n    };\n    return icons[message.systemType] || 'fas fa-info-circle';\n  }\n\n  onMessageClick(message: any, event: any): void {\n    if (this.selectionMode) {\n      this.toggleMessageSelection(message.id);\n    }\n  }\n\n  onMessageContextMenu(message: any, event: any): void {\n    event.preventDefault();\n    this.toggleMessageOptions(message.id);\n  }\n\n  onMessageHover(messageId: string, isHovering: boolean): void {\n    this.hoveredMessageId = isHovering ? messageId : '';\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    if (index === this.messages.length - 1) return true;\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    return currentMessage?.sender?.id !== nextMessage?.sender?.id;\n  }\n\n  onAvatarError(event: any): void {\n    event.target.src = 'assets/images/default-avatar.png';\n  }\n\n  isGroupConversation(): boolean {\n    return (this.conversation as any)?.type === 'group';\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    if (!this.isGroupConversation()) return false;\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    return currentMessage?.sender?.id !== previousMessage?.sender?.id;\n  }\n\n  getUserColor(userId: string): string {\n    const colors = [\n      '#ff6b6b',\n      '#4ecdc4',\n      '#45b7d1',\n      '#96ceb4',\n      '#feca57',\n      '#ff9ff3',\n    ];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n\n  // Propriété Math pour le template\n  get Math(): typeof Math {\n    return Math;\n  }\n\n  // Méthodes pour les fichiers\n  downloadSelectedFiles(): void {\n    // Télécharger les fichiers sélectionnés\n  }\n\n  shareSelectedFiles(): void {\n    // Partager les fichiers sélectionnés\n  }\n\n  deleteSelectedFiles(): void {\n    // Supprimer les fichiers sélectionnés\n  }\n\n  // Méthodes pour les analytics\n  updateAnalytics(): void {\n    // Mettre à jour les analytics\n  }\n\n  exportAnalytics(): void {\n    // Exporter les analytics\n  }\n\n  getMessageTypeIcon(type: string): string {\n    const icons: any = {\n      text: 'fas fa-comment',\n      image: 'fas fa-image',\n      video: 'fas fa-video',\n      voice: 'fas fa-microphone',\n      file: 'fas fa-file',\n      location: 'fas fa-map-marker-alt',\n      contact: 'fas fa-user',\n    };\n    return icons[type] || 'fas fa-comment';\n  }\n\n  // Méthodes pour les intégrations\n  createNewIntegration(): void {\n    // Créer une nouvelle intégration\n  }\n\n  selectIntegrationCategory(category: string): void {\n    this.selectedIntegrationCategory = category;\n  }\n\n  getIntegrationCategoryIcon(category: string): string {\n    const icons: any = {\n      CRM: 'fas fa-users',\n      Productivité: 'fas fa-tasks',\n      Communication: 'fas fa-comments',\n      Analytics: 'fas fa-chart-bar',\n      Sécurité: 'fas fa-shield-alt',\n    };\n    return icons[category] || 'fas fa-puzzle-piece';\n  }\n\n  getAvailableIntegrations(): any[] {\n    return [\n      { id: 1, name: 'Slack', category: 'Communication', icon: 'fab fa-slack' },\n      {\n        id: 2,\n        name: 'Trello',\n        category: 'Productivité',\n        icon: 'fab fa-trello',\n      },\n      {\n        id: 3,\n        name: 'Google Analytics',\n        category: 'Analytics',\n        icon: 'fab fa-google',\n      },\n    ];\n  }\n\n  configureIntegration(integration: any): void {\n    // Configurer l'intégration\n  }\n\n  toggleIntegration(integration: any): void {\n    // Basculer l'intégration\n  }\n\n  getActiveIntegrations(): any[] {\n    return this.getAvailableIntegrations().filter((i) => i.active);\n  }\n\n  getIntegrationStatusText(status: string): string {\n    const texts: any = {\n      active: 'Actif',\n      inactive: 'Inactif',\n      error: 'Erreur',\n      pending: 'En attente',\n    };\n    return texts[status] || 'Inconnu';\n  }\n\n  formatLastActivity(lastActivity: any): string {\n    return this.formatLastActive(lastActivity);\n  }\n\n  viewIntegrationLogs(integration: any): void {\n    // Voir les logs d'intégration\n  }\n\n  editIntegration(integration: any): void {\n    // Modifier l'intégration\n  }\n\n  testIntegration(integration: any): void {\n    // Tester l'intégration\n  }\n\n  removeIntegration(integration: any): void {\n    // Supprimer l'intégration\n  }\n\n  // Méthodes pour les webhooks\n  editWebhook(webhook: any): void {\n    // Modifier le webhook\n  }\n\n  testWebhook(webhook: any): void {\n    // Tester le webhook\n  }\n\n  deleteWebhook(webhook: any): void {\n    // Supprimer le webhook\n  }\n\n  createWebhook(): void {\n    // Créer un webhook\n  }\n\n  // Méthodes pour les modales\n  hasActiveModal(): boolean {\n    return (\n      this.showImageViewer ||\n      this.showForwardModal ||\n      this.showAnalyticsDashboard ||\n      this.showIntegrations ||\n      this.showEmojiPicker ||\n      this.showCamera ||\n      this.showDrawingTool ||\n      this.showFileManager\n    );\n  }\n\n  closeActiveModal(): void {\n    this.showImageViewer = false;\n    this.showForwardModal = false;\n    this.showAnalyticsDashboard = false;\n    this.showIntegrations = false;\n    this.showEmojiPicker = false;\n    this.showCamera = false;\n    this.showDrawingTool = false;\n    this.showFileManager = false;\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE HTML ===\n\n  // Méthodes pour l'éditeur d'images\n  flipImage(direction: string): void {\n    console.log('Flip image:', direction);\n  }\n\n  applyImageFilter(filterName: string): void {\n    this.selectedImageFilter = filterName;\n  }\n\n  updateImageAdjustments(): void {\n    // Mettre à jour les ajustements d'image\n  }\n\n  resetImageAdjustments(): void {\n    this.imageAdjustments = {\n      brightness: 0,\n      contrast: 0,\n      saturation: 0,\n      hue: 0,\n    };\n  }\n\n  addTextToImage(): void {\n    if (this.newTextContent.trim()) {\n      this.imageTextElements.push({\n        id: Date.now().toString(),\n        content: this.newTextContent,\n        x: 50,\n        y: 50,\n        fontFamily: this.textFontFamily,\n        fontSize: this.textFontSize,\n        color: this.textColor,\n      });\n      this.newTextContent = '';\n    }\n  }\n\n  // Méthodes pour le gestionnaire de fichiers\n  createNewFolder(): void {\n    const folderName = prompt('Nom du nouveau dossier:');\n    if (folderName) {\n      this.fileFolders.push({\n        id: Date.now().toString(),\n        name: folderName,\n        type: 'folder',\n        size: 0,\n        modifiedAt: new Date(),\n      });\n    }\n  }\n\n  uploadFiles(): void {\n    this.openFileInput();\n  }\n\n  toggleFileView(): void {\n    this.fileViewMode = this.fileViewMode === 'grid' ? 'list' : 'grid';\n  }\n\n  closeFileManager(): void {\n    this.showFileManager = false;\n  }\n\n  selectFolder(folder: any): void {\n    this.selectedFolder = folder;\n  }\n\n  renameFolder(folder: any): void {\n    const newName = prompt('Nouveau nom:', folder.name);\n    if (newName) {\n      folder.name = newName;\n    }\n  }\n\n  deleteFolder(folder: any): void {\n    if (confirm('Supprimer ce dossier ?')) {\n      this.fileFolders = this.fileFolders.filter((f) => f.id !== folder.id);\n    }\n  }\n\n  getStorageUsagePercentage(): number {\n    return 65; // Exemple\n  }\n\n  getStorageUsed(): string {\n    return '6.5 GB';\n  }\n\n  getStorageTotal(): string {\n    return '10 GB';\n  }\n\n  getTotalFilesCount(): number {\n    return 1247;\n  }\n\n  navigateToFolder(crumb: any): void {\n    this.selectedFolder = crumb;\n  }\n\n  onFileSearch(): void {\n    // Rechercher dans les fichiers\n  }\n\n  applyFileFilters(): void {\n    // Appliquer les filtres de fichiers\n  }\n\n  sortFiles(): void {\n    // Trier les fichiers\n  }\n\n  getFilteredFiles(): any[] {\n    return [\n      {\n        id: '1',\n        name: 'Document.pdf',\n        type: 'file',\n        size: 1024000,\n        modifiedAt: new Date(),\n      },\n      {\n        id: '2',\n        name: 'Image.jpg',\n        type: 'image',\n        size: 2048000,\n        modifiedAt: new Date(),\n      },\n    ];\n  }\n\n  toggleFileSelection(file: any): void {\n    const index = this.selectedFiles.indexOf(file.id);\n    if (index > -1) {\n      this.selectedFiles.splice(index, 1);\n    } else {\n      this.selectedFiles.push(file.id);\n    }\n  }\n\n  openFile(file: any): void {\n    console.log('Ouvrir fichier:', file.name);\n  }\n\n  formatFileDate(date: any): string {\n    return new Date(date).toLocaleDateString('fr-FR');\n  }\n\n  shareFile(file: any): void {\n    console.log('Partager fichier:', file.name);\n  }\n\n  deleteFile(file: any): void {\n    if (confirm('Supprimer ce fichier ?')) {\n      console.log('Fichier supprimé:', file.name);\n    }\n  }\n\n  // Méthodes pour les propriétés manquantes dans le template\n  get forwarded(): boolean {\n    return false; // Propriété pour les messages transférés\n  }\n\n  // Méthodes pour corriger les erreurs du template\n  getOnlineUsersCount(): number {\n    return this.onlineUsersCount;\n  }\n\n  // Méthodes manquantes pour le template\n  truncateText(text: string, maxLength: number): string {\n    return text.length > maxLength\n      ? text.substring(0, maxLength) + '...'\n      : text;\n  }\n\n  hasImage(message: any): boolean {\n    return (\n      message.type === 'image' ||\n      (message.attachments &&\n        message.attachments.some((a: any) => a.type.startsWith('image/')))\n    );\n  }\n\n  isVoiceMessage(message: any): boolean {\n    return message.type === 'voice' || message.type === 'audio';\n  }\n\n  hasFile(message: any): boolean {\n    return (\n      message.type === 'file' ||\n      (message.attachments && message.attachments.length > 0)\n    );\n  }\n\n  getFileName(message: any): string {\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments[0].name || 'Fichier';\n    }\n    return 'Fichier';\n  }\n\n  getMessageBubbleClass(message: any): string {\n    const isOwn = message.sender?.id === this.currentUserId;\n    return `whatsapp-message-bubble ${isOwn ? 'own' : 'other'}`;\n  }\n\n  isLocationMessage(message: any): boolean {\n    return message.type === 'location';\n  }\n\n  isContactMessage(message: any): boolean {\n    return message.type === 'contact';\n  }\n\n  // Méthodes pour la caméra\n  closeCamera(): void {\n    this.showCamera = false;\n  }\n\n  setCameraMode(mode: string): void {\n    this.cameraMode = mode;\n  }\n\n  toggleCameraFlash(): void {\n    this.flashEnabled = !this.flashEnabled;\n  }\n\n  switchCamera(): void {\n    // Basculer entre caméra avant et arrière\n  }\n\n  openGallery(): void {\n    this.openFileInput();\n  }\n\n  capturePhoto(): void {\n    // Capturer une photo\n  }\n\n  startVideoRecording(): void {\n    this.isRecordingVideo = true;\n    this.videoRecordingDuration = 0;\n  }\n\n  stopVideoRecording(): void {\n    this.isRecordingVideo = false;\n  }\n\n  toggleCameraGrid(): void {\n    this.showCameraGrid = !this.showCameraGrid;\n  }\n\n  formatRecordingTime(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds\n      .toString()\n      .padStart(2, '0')}`;\n  }\n\n  // Méthodes pour l'éditeur d'images\n  closeImageEditor(): void {\n    this.showImageEditor = false;\n  }\n\n  saveEditedImage(): void {\n    // Sauvegarder l'image éditée\n  }\n\n  startCropResize(corner: string, event: any): void {\n    // Commencer le redimensionnement de crop\n  }\n\n  editTextElement(index: number): void {\n    // Éditer un élément de texte\n  }\n\n  setImageEditorMode(mode: string): void {\n    this.imageEditorMode = mode;\n  }\n\n  setCropRatio(ratio: string): void {\n    this.cropRatio = ratio;\n  }\n\n  rotateImage(angle: number): void {\n    // Faire tourner l'image\n  }\n\n  // Initialiser des données de test\n  private initializeTestData(): void {\n    // Créer une conversation de test\n    this.conversation = {\n      id: 'test-conversation',\n      participants: [\n        { id: 'user1', username: 'Alice' },\n        { id: 'user2', username: 'Bob' },\n      ],\n    } as Conversation;\n\n    // Créer des messages de test\n    this.messages = [\n      {\n        id: '1',\n        content: 'Salut ! Comment ça va ?',\n        sender: { id: 'user2', username: 'Bob' },\n        timestamp: new Date(Date.now() - 3600000),\n        conversationId: 'test-conversation',\n        type: 'text' as MessageType,\n      },\n      {\n        id: '2',\n        content: 'Ça va bien, merci ! Et toi ?',\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(Date.now() - 3000000),\n        conversationId: 'test-conversation',\n        type: 'text' as MessageType,\n      },\n      {\n        id: '3',\n        content: \"Super ! Tu veux qu'on se voit ce soir ?\",\n        sender: { id: 'user2', username: 'Bob' },\n        timestamp: new Date(Date.now() - 1800000),\n        conversationId: 'test-conversation',\n        type: 'text' as MessageType,\n      },\n    ];\n\n    this.otherParticipant = { id: 'user2', username: 'Bob' } as User;\n    this.loading = false;\n  }\n\n  // === MÉTHODES MANQUANTES POUR CORRIGER LES ERREURS DU TEMPLATE ===\n\n  // Méthodes pour les emojis et stickers\n  insertGif(gif: any): void {\n    // Insérer un GIF dans le message\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({ content: content + `[GIF:${gif.id}]` });\n  }\n\n  manageStickerPacks(): void {\n    // Gérer les packs de stickers\n  }\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n\n  // Méthodes pour l'outil de dessin\n  openDrawingTool(): void {\n    this.showDrawingTool = true;\n  }\n\n  closeDrawingTool(): void {\n    this.showDrawingTool = false;\n  }\n\n  selectDrawingTool(tool: string): void {\n    this.selectedDrawingTool = tool;\n  }\n\n  selectDrawingColor(color: string): void {\n    this.selectedDrawingColor = color;\n  }\n\n  undoDrawing(): void {\n    // Annuler le dernier trait\n  }\n\n  redoDrawing(): void {\n    // Refaire le dernier trait annulé\n  }\n\n  clearDrawing(): void {\n    // Effacer tout le dessin\n  }\n\n  startDrawing(event: any): void {\n    // Commencer à dessiner\n  }\n\n  draw(event: any): void {\n    // Dessiner\n  }\n\n  stopDrawing(): void {\n    // Arrêter de dessiner\n  }\n\n  saveDrawing(): void {\n    // Sauvegarder le dessin\n  }\n\n  // === MÉTHODES POUR LES RÉACTIONS ===\n  hasUserReacted(reaction: any, userId: string | null): boolean {\n    if (!userId) return false;\n    return reaction.userId === userId;\n  }\n\n  getReactionTooltip(reaction: any): string {\n    return `${reaction.user?.username || 'Utilisateur'} a réagi avec ${\n      reaction.emoji\n    }`;\n  }\n\n  // === MÉTHODES POUR LES PERMISSIONS ===\n  canDeleteMessage(message: any): boolean {\n    // L'utilisateur peut supprimer ses propres messages ou s'il est admin\n    return message.sender?.id === this.currentUserId || this.isAdmin;\n  }\n\n  canEditMessage(message: any): boolean {\n    // L'utilisateur peut modifier ses propres messages\n    return message.sender?.id === this.currentUserId;\n  }\n\n  // === MÉTHODES POUR LA SÉLECTION DE MESSAGES ===\n  exitSelectionMode(): void {\n    this.selectionMode = false;\n    this.selectedMessages.clear();\n  }\n\n  deleteSelectedMessages(): void {\n    if (this.selectedMessages.size === 0) return;\n\n    if (confirm(`Supprimer ${this.selectedMessages.size} message(s) ?`)) {\n      // Supprimer les messages sélectionnés\n      this.messages = this.messages.filter(\n        (m) => !m.id || !this.selectedMessages.has(m.id)\n      );\n      this.exitSelectionMode();\n      this.toastService.showSuccess('Messages supprimés');\n    }\n  }\n\n  forwardSelectedMessages(): void {\n    if (this.selectedMessages.size === 0) return;\n\n    // Ouvrir le modal de transfert\n    this.showForwardModal = true;\n    this.forwardingMessage = Array.from(this.selectedMessages);\n  }\n\n  copySelectedMessages(): void {\n    if (this.selectedMessages.size === 0) return;\n\n    const selectedMessagesArray = this.messages.filter(\n      (m) => m.id && this.selectedMessages.has(m.id)\n    );\n    const textToCopy = selectedMessagesArray\n      .map((m) => `${m.sender?.username}: ${m.content}`)\n      .join('\\n');\n\n    navigator.clipboard\n      .writeText(textToCopy)\n      .then(() => {\n        this.toastService.showSuccess('Messages copiés');\n        this.exitSelectionMode();\n      })\n      .catch(() => {\n        this.toastService.showError('Erreur lors de la copie');\n      });\n  }\n\n  // === MÉTHODES POUR LES RÉPONSES ===\n  cancelReply(): void {\n    this.replyingToMessage = null;\n  }\n\n  // === MÉTHODES POUR LES FICHIERS ===\n  removeSelectedFile(): void {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n\n  // === MÉTHODES POUR L'ENREGISTREMENT VOCAL ===\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  cancelVoiceRecording(): void {\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n  }\n\n  // === MÉTHODES POUR LES SONDAGES ===\n  canCreatePoll(): boolean {\n    return (\n      this.pollQuestion.trim().length > 0 &&\n      this.pollOptions.filter((opt) => opt.text.trim().length > 0).length >= 2\n    );\n  }\n\n  // === MÉTHODES POUR LA LOCALISATION ===\n  closeLocationPicker(): void {\n    this.showLocationPicker = false;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n    this.locationSearchResults = [];\n    this.selectedLocation = null;\n  }\n\n  getCurrentLocation(): void {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          this.selectedLocation = {\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude,\n            name: 'Ma position actuelle',\n          };\n        },\n        (error) => {\n          this.toastService.showError(\"Impossible d'obtenir votre position\");\n        }\n      );\n    } else {\n      this.toastService.showError('Géolocalisation non supportée');\n    }\n  }\n\n  searchLocation(): void {\n    this.showLocationSearch = true;\n    // Simuler une recherche de lieux\n    this.locationSearchResults = [\n      { name: 'Paris, France', latitude: 48.8566, longitude: 2.3522 },\n      { name: 'Lyon, France', latitude: 45.764, longitude: 4.8357 },\n      { name: 'Marseille, France', latitude: 43.2965, longitude: 5.3698 },\n    ];\n  }\n\n  onLocationSearch(): void {\n    // Filtrer les résultats selon la recherche\n    if (this.locationSearchQuery.trim()) {\n      this.locationSearchResults = this.locationSearchResults.filter(\n        (location) =>\n          location.name\n            .toLowerCase()\n            .includes(this.locationSearchQuery.toLowerCase())\n      );\n    }\n  }\n\n  selectLocationResult(result: any): void {\n    this.selectedLocation = result;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n  }\n\n  editLocationMessage(): void {\n    this.showLocationMessage = true;\n  }\n\n  shareLocation(): void {\n    if (this.selectedLocation) {\n      // Créer un message de localisation\n      const locationMessage = {\n        id: Date.now().toString(),\n        type: 'location',\n        location: this.selectedLocation,\n        locationMessage: this.locationMessage,\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id,\n      };\n\n      this.messages.push(locationMessage as any);\n      this.closeLocationPicker();\n      this.locationMessage = '';\n      this.toastService.showSuccess('Localisation partagée');\n    }\n  }\n\n  // === MÉTHODES POUR LES CONTACTS ===\n  closeContactPicker(): void {\n    this.showContactPicker = false;\n    this.contactSearchQuery = '';\n    this.selectedContactForSharing = null;\n  }\n\n  onContactSearch(): void {\n    // Filtrer les contacts selon la recherche\n    // Implémentation de la recherche de contacts\n  }\n\n  getFilteredContactsForSharing(): any[] {\n    // Simuler une liste de contacts\n    const contacts = [\n      {\n        id: '1',\n        name: 'Alice Martin',\n        phone: '+33123456789',\n        email: '<EMAIL>',\n      },\n      {\n        id: '2',\n        name: 'Bob Dupont',\n        phone: '+33987654321',\n        email: '<EMAIL>',\n      },\n      {\n        id: '3',\n        name: 'Claire Durand',\n        phone: '+33555666777',\n        email: '<EMAIL>',\n      },\n    ];\n\n    if (this.contactSearchQuery.trim()) {\n      return contacts.filter(\n        (contact) =>\n          contact.name\n            .toLowerCase()\n            .includes(this.contactSearchQuery.toLowerCase()) ||\n          contact.phone.includes(this.contactSearchQuery) ||\n          contact.email\n            .toLowerCase()\n            .includes(this.contactSearchQuery.toLowerCase())\n      );\n    }\n    return contacts;\n  }\n\n  selectContactForSharing(contact: any): void {\n    this.selectedContactForSharing = contact;\n  }\n\n  viewContactDetails(contact: any): void {\n    // Ouvrir les détails du contact\n    this.toastService.showInfo(`Détails de ${contact.name}`);\n  }\n\n  createNewContact(): void {\n    // Ouvrir le formulaire de création de contact\n    this.toastService.showInfo(\"Création d'un nouveau contact\");\n  }\n\n  shareContact(): void {\n    if (this.selectedContactForSharing) {\n      // Créer un message de contact\n      const contactMessage = {\n        id: Date.now().toString(),\n        type: 'contact',\n        contact: this.selectedContactForSharing,\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id,\n      };\n\n      this.messages.push(contactMessage as any);\n      this.closeContactPicker();\n      this.toastService.showSuccess('Contact partagé');\n    }\n  }\n\n  // === MÉTHODES POUR LES EMOJIS ET STICKERS ===\n  openStickerPicker(): void {\n    this.showStickerPicker = true;\n    this.showEmojiPicker = false;\n  }\n\n  openGifPicker(): void {\n    this.showGifPicker = true;\n    this.showEmojiPicker = false;\n  }\n\n  onEmojiSearch(): void {\n    // Filtrer les emojis selon la recherche\n    // Implémentation de la recherche d'emojis\n  }\n\n  insertEmoji(emoji: string): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: currentContent + emoji,\n    });\n  }\n\n  getFilteredEmojiCategories(): any[] {\n    return this.emojiCategories.filter((category) => {\n      if (!this.emojiSearchQuery.trim()) return true;\n      return category.name\n        .toLowerCase()\n        .includes(this.emojiSearchQuery.toLowerCase());\n    });\n  }\n\n  previewEmoji(emoji: any): void {\n    this.previewedEmoji = emoji;\n  }\n\n  selectStickerPack(packId: string): void {\n    this.selectedStickerPack = packId;\n  }\n\n  getSelectedStickerPack(): any {\n    return this.stickerPacks.find(\n      (pack) => pack.id === this.selectedStickerPack\n    );\n  }\n\n  insertSticker(sticker: any): void {\n    // Créer un message sticker\n    const stickerMessage = {\n      id: Date.now().toString(),\n      type: 'sticker',\n      sticker: sticker,\n      sender: { id: this.currentUserId!, username: this.currentUsername },\n      timestamp: new Date(),\n      conversationId: this.conversation?.id,\n    };\n\n    this.messages.push(stickerMessage as any);\n    this.showStickerPicker = false;\n    this.toastService.showSuccess('Sticker envoyé');\n  }\n\n  onGifSearch(): void {\n    // Filtrer les GIFs selon la recherche\n    // Implémentation de la recherche de GIFs\n  }\n\n  selectGifCategory(category: string): void {\n    this.selectedGifCategory = category;\n  }\n\n  getFilteredGifs(): any[] {\n    // Simuler une liste de GIFs\n    return [\n      { id: '1', url: 'gif1.gif', title: 'Happy' },\n      { id: '2', url: 'gif2.gif', title: 'Funny' },\n      { id: '3', url: 'gif3.gif', title: 'Love' },\n    ];\n  }\n\n  // === MÉTHODES POUR LES MODALS ===\n  isCallMinimized = false;\n  isInCall = false;\n  inputFocused = false;\n\n  cancelDelete(): void {\n    this.showDeleteConfirmModal = false;\n  }\n\n  confirmDelete(): void {\n    // Logique de suppression\n    this.showDeleteConfirmModal = false;\n    this.toastService.showSuccess('Message supprimé');\n  }\n\n  closeForwardModal(): void {\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n  }\n\n  forwardSearchQuery = '';\n\n  getFilteredContacts(): any[] {\n    const contacts = [\n      { id: '1', name: 'Alice Martin', avatar: 'avatar1.jpg' },\n      { id: '2', name: 'Bob Dupont', avatar: 'avatar2.jpg' },\n      { id: '3', name: 'Claire Durand', avatar: 'avatar3.jpg' },\n    ];\n\n    if (this.forwardSearchQuery.trim()) {\n      return contacts.filter((contact) =>\n        contact.name\n          .toLowerCase()\n          .includes(this.forwardSearchQuery.toLowerCase())\n      );\n    }\n    return contacts;\n  }\n\n  toggleContactSelection(contactId: string): void {\n    const index = this.selectedConversations.indexOf(contactId);\n    if (index > -1) {\n      this.selectedConversations.splice(index, 1);\n    } else {\n      this.selectedConversations.push(contactId);\n    }\n  }\n\n  confirmForward(): void {\n    if (this.selectedConversations.length > 0) {\n      this.toastService.showSuccess(\n        `Message transféré à ${this.selectedConversations.length} contact(s)`\n      );\n      this.closeForwardModal();\n    }\n  }\n\n  // === MÉTHODES POUR LE VISUALISEUR D'IMAGES ===\n  previousImage(): void {\n    if (this.currentImageIndex > 0) {\n      this.currentImageIndex--;\n    }\n  }\n\n  nextImage(): void {\n    if (this.currentImageIndex < this.imageGallery.length - 1) {\n      this.currentImageIndex++;\n    }\n  }\n\n  // === MÉTHODES POUR LES TOASTS ===\n  getToastIcon(): string {\n    const icons = {\n      success: 'fas fa-check-circle',\n      error: 'fas fa-exclamation-circle',\n      warning: 'fas fa-exclamation-triangle',\n      info: 'fas fa-info-circle',\n    };\n    return icons[this.toastType as keyof typeof icons] || 'fas fa-info-circle';\n  }\n\n  // === MÉTHODES POUR LES STATISTIQUES ===\n  getTotalMessagesCount(): number {\n    return this.messages.length;\n  }\n\n  getPhotosCount(): number {\n    return this.messages.filter((m) =>\n      m.attachments?.some((att) => att.type === 'image')\n    ).length;\n  }\n\n  getFilesCount(): number {\n    return this.messages.filter((m) =>\n      m.attachments?.some((att) => att.type === 'file')\n    ).length;\n  }\n\n  // === MÉTHODES POUR LES PARAMÈTRES DE CONVERSATION ===\n  updateConversationSettings(): void {\n    // Sauvegarder les paramètres de conversation\n    this.toastService.showSuccess('Paramètres mis à jour');\n  }\n\n  // === MÉTHODES POUR LA TRADUCTION ===\n  closeTranslationPanel(): void {\n    this.showTranslationPanel = false;\n    this.originalTextForTranslation = '';\n    this.translatedText = '';\n  }\n\n  swapLanguages(): void {\n    const temp = this.translationFrom;\n    this.translationFrom = this.translationTo;\n    this.translationTo = temp;\n  }\n\n  copyTranslation(): void {\n    if (this.translatedText) {\n      navigator.clipboard.writeText(this.translatedText).then(() => {\n        this.toastService.showSuccess('Traduction copiée');\n      });\n    }\n  }\n\n  insertTranslation(): void {\n    if (this.translatedText) {\n      const currentContent = this.messageForm.get('content')?.value || '';\n      this.messageForm.patchValue({\n        content: currentContent + this.translatedText,\n      });\n      this.closeTranslationPanel();\n    }\n  }\n\n  shareTranslation(): void {\n    if (this.translatedText) {\n      // Créer un message avec la traduction\n      const translationMessage = {\n        id: Date.now().toString(),\n        content: this.translatedText,\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id,\n      };\n\n      this.messages.push(translationMessage as any);\n      this.closeTranslationPanel();\n      this.toastService.showSuccess('Traduction partagée');\n    }\n  }\n\n  // === MÉTHODES POUR LES SONDAGES ===\n  closePollCreator(): void {\n    this.showPollCreator = false;\n    this.pollQuestion = '';\n    this.pollOptions = [{ text: '' }, { text: '' }];\n    this.pollSettings = {\n      allowMultiple: false,\n      anonymous: false,\n      showResults: true,\n      showVoters: true,\n    };\n  }\n\n  addPollOption(): void {\n    if (this.pollOptions.length < 10) {\n      this.pollOptions.push({ text: '' });\n    }\n  }\n\n  removePollOption(index: number): void {\n    if (this.pollOptions.length > 2) {\n      this.pollOptions.splice(index, 1);\n    }\n  }\n\n  createPoll(): void {\n    if (this.canCreatePoll()) {\n      // Créer un message sondage\n      const pollMessage = {\n        id: Date.now().toString(),\n        type: 'poll',\n        poll: {\n          question: this.pollQuestion,\n          options: this.pollOptions.filter((opt) => opt.text.trim()),\n          settings: this.pollSettings,\n          votes: [],\n          totalVotes: 0,\n        },\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id,\n      };\n\n      this.messages.push(pollMessage as any);\n      this.closePollCreator();\n      this.toastService.showSuccess('Sondage créé');\n    }\n  }\n\n  // === MÉTHODES POUR LES FONCTIONNALITÉS DE BASE ===\n  hasMessageContent(): boolean {\n    const content = this.messageForm.get('content')?.value;\n    return content && content.trim().length > 0;\n  }\n\n  canSendMessage(): boolean {\n    return this.hasMessageContent() || this.selectedFile !== null;\n  }\n\n  searchEmojis(): void {\n    // Rechercher des emojis selon emojiSearchQuery\n  }\n\n  getFilteredEmojis(): any[] {\n    // Simuler une liste d'emojis\n    const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'];\n    if (this.emojiSearchQuery.trim()) {\n      return emojis.filter((emoji) => emoji.includes(this.emojiSearchQuery));\n    }\n    return emojis;\n  }\n\n  // === MÉTHODES POUR LES NOTIFICATIONS ===\n  toggleNotificationPanel(): void {\n    this.showNotificationPanel = !this.showNotificationPanel;\n  }\n\n  onSearchKeyPress(event: any): void {\n    if (event.key === 'Enter') {\n      this.performSearch();\n    }\n  }\n\n  toggleSearchBar(): void {\n    this.showSearchBar = !this.showSearchBar;\n    if (!this.showSearchBar) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n\n  navigateToMessage(messageId: string): void {\n    // Naviguer vers un message spécifique\n    this.highlightedMessageId = messageId;\n  }\n\n  highlightSearchTerms(content: string, query: string): string {\n    if (!query) return content;\n    const regex = new RegExp(`(${query})`, 'gi');\n    return content.replace(regex, '<mark>$1</mark>');\n  }\n\n  // === MÉTHODES POUR LES MESSAGES ÉPINGLÉS ===\n  getPinnedMessagesCount(): number {\n    return this.messages.filter((m) => m.pinned).length;\n  }\n\n  togglePinnedMessages(): void {\n    this.showPinnedMessages = !this.showPinnedMessages;\n  }\n\n  scrollToPinnedMessage(messageId: string): void {\n    this.scrollToMessage(messageId);\n  }\n\n  formatMessageDate(date: Date | string): string {\n    return this.formatMessageTime(date);\n  }\n\n  // === MÉTHODES POUR LES APPELS ===\n  declineCall(): void {\n    this.incomingCall = null;\n    this.toastService.showInfo('Appel refusé');\n  }\n\n  acceptCall(): void {\n    this.incomingCall = null;\n    this.isInCall = true;\n    this.toastService.showSuccess('Appel accepté');\n  }\n\n  formatCallDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  getCallStatusText(): string {\n    if (this.isInCall) return 'En cours...';\n    return 'Connexion...';\n  }\n\n  toggleCallMinimize(): void {\n    this.isCallMinimized = !this.isCallMinimized;\n  }\n\n  showCallControls = true;\n\n  // === MÉTHODES POUR LE FORMATAGE DE TEXTE ===\n  applyFormatting(type: string): void {\n    const content = this.messageForm.get('content')?.value || '';\n    let formattedContent = content;\n\n    switch (type) {\n      case 'bold':\n        formattedContent = `*${content}*`;\n        break;\n      case 'italic':\n        formattedContent = `_${content}_`;\n        break;\n      case 'strikethrough':\n        formattedContent = `~${content}~`;\n        break;\n      case 'underline':\n        formattedContent = `__${content}__`;\n        break;\n      case 'code':\n        formattedContent = `\\`${content}\\``;\n        break;\n      case 'quote':\n        formattedContent = `> ${content}`;\n        break;\n      case 'spoiler':\n        formattedContent = `||${content}||`;\n        break;\n    }\n\n    this.messageForm.patchValue({ content: formattedContent });\n  }\n\n  hasFormatting(type: string): boolean {\n    const content = this.messageForm.get('content')?.value || '';\n    switch (type) {\n      case 'bold':\n        return content.includes('*');\n      case 'italic':\n        return content.includes('_');\n      case 'strikethrough':\n        return content.includes('~');\n      case 'underline':\n        return content.includes('__');\n      case 'code':\n        return content.includes('`');\n      case 'quote':\n        return content.includes('>');\n      case 'spoiler':\n        return content.includes('||');\n      default:\n        return false;\n    }\n  }\n\n  insertLink(): void {\n    const url = prompt(\"Entrez l'URL du lien :\");\n    if (url) {\n      const text = prompt('Entrez le texte du lien :') || url;\n      const content = this.messageForm.get('content')?.value || '';\n      this.messageForm.patchValue({\n        content: content + `[${text}](${url})`,\n      });\n    }\n  }\n\n  insertTable(): void {\n    const table =\n      '\\n| Colonne 1 | Colonne 2 |\\n|-----------|----------|\\n| Cellule 1 | Cellule 2 |\\n';\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + table,\n    });\n  }\n\n  insertList(type: string): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const listItem =\n      type === 'ul' ? '• Élément de liste\\n' : '1. Élément de liste\\n';\n    this.messageForm.patchValue({\n      content: content + '\\n' + listItem,\n    });\n  }\n\n  insertMentionSymbol(): void {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + '@',\n    });\n  }\n\n  insertHashtagSymbol(): void {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + '#',\n    });\n  }\n\n  clearFormatting(): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const cleanContent = content\n      .replace(/\\*([^*]+)\\*/g, '$1') // Bold\n      .replace(/_([^_]+)_/g, '$1') // Italic\n      .replace(/~([^~]+)~/g, '$1') // Strikethrough\n      .replace(/__([^_]+)__/g, '$1') // Underline\n      .replace(/`([^`]+)`/g, '$1') // Code\n      .replace(/> /g, '') // Quote\n      .replace(/\\|\\|([^|]+)\\|\\|/g, '$1'); // Spoiler\n\n    this.messageForm.patchValue({ content: cleanContent });\n  }\n\n  toggleFormattingToolbar(): void {\n    this.showFormattingToolbar = !this.showFormattingToolbar;\n  }\n\n  // === MÉTHODES POUR LES CORRECTIONS AUTOMATIQUES ===\n  hideAutoCorrectSuggestions(): void {\n    this.autoCorrectSuggestions = [];\n  }\n\n  applyCorrection(original: string, correction: string): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const correctedContent = content.replace(original, correction);\n    this.messageForm.patchValue({ content: correctedContent });\n    this.hideAutoCorrectSuggestions();\n  }\n\n  ignoreCorrection(original: string): void {\n    this.autoCorrectSuggestions = this.autoCorrectSuggestions.filter(\n      (s) => s.original !== original\n    );\n  }\n\n  // === MÉTHODES POUR LES FICHIERS ===\n  triggerFileInput(type: string): void {\n    const input = document.createElement('input');\n    input.type = 'file';\n\n    switch (type) {\n      case 'image':\n        input.accept = 'image/*';\n        break;\n      case 'video':\n        input.accept = 'video/*';\n        break;\n      case 'audio':\n        input.accept = 'audio/*';\n        break;\n      case 'document':\n        input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx';\n        break;\n    }\n\n    input.onchange = (event: any) => {\n      this.onFileSelected(event);\n    };\n\n    input.click();\n  }\n\n  // === MÉTHODES POUR L'INPUT DE MESSAGE ===\n  getInputPlaceholder(): string {\n    if (this.replyingToMessage) {\n      return `Répondre à ${this.replyingToMessage.sender?.username}...`;\n    }\n    if (this.isRecordingVoice) {\n      return 'Enregistrement en cours...';\n    }\n    return 'Tapez votre message...';\n  }\n\n  onInputChange(event: any): void {\n    const content = event.target.value;\n    this.messageForm.patchValue({ content });\n\n    // Détecter les mentions, hashtags, etc.\n    this.detectMentions(content);\n    this.detectHashtags(content);\n    this.detectCommands(content);\n\n    // Gestion de la frappe\n    this.handleTyping();\n  }\n\n  onInputKeyDown(event: any): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    } else if (event.key === 'ArrowUp' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.selectedMentionIndex = Math.max(0, this.selectedMentionIndex - 1);\n    } else if (\n      event.key === 'ArrowDown' &&\n      this.mentionSuggestions.length > 0\n    ) {\n      event.preventDefault();\n      this.selectedMentionIndex = Math.min(\n        this.mentionSuggestions.length - 1,\n        this.selectedMentionIndex + 1\n      );\n    } else if (event.key === 'Tab' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.insertMention(this.mentionSuggestions[this.selectedMentionIndex]);\n    }\n  }\n\n  onInputKeyUp(event: any): void {\n    // Gestion des suggestions en temps réel\n    this.updateSuggestions();\n  }\n\n  onInputFocus(): void {\n    this.inputFocused = true;\n  }\n\n  onInputBlur(): void {\n    this.inputFocused = false;\n    // Masquer les suggestions après un délai\n    setTimeout(() => {\n      this.mentionSuggestions = [];\n      this.hashtagSuggestions = [];\n      this.commandSuggestions = [];\n    }, 200);\n  }\n\n  onInputPaste(event: any): void {\n    const clipboardData = event.clipboardData || (window as any).clipboardData;\n    const pastedData = clipboardData.getData('text');\n\n    // Traiter le contenu collé\n    if (pastedData.includes('http')) {\n      // Détecter les liens\n      this.detectLinks(pastedData);\n    }\n  }\n\n  onInputScroll(event: any): void {\n    // Gérer le défilement de l'input si nécessaire\n  }\n\n  // === MÉTHODES POUR LES MENTIONS ET SUGGESTIONS ===\n  detectMentions(content: string): void {\n    const mentionMatch = content.match(/@(\\w*)$/);\n    if (mentionMatch) {\n      const query = mentionMatch[1];\n      this.mentionSuggestions = this.getFilteredUsers(query);\n      this.selectedMentionIndex = 0;\n    } else {\n      this.mentionSuggestions = [];\n    }\n  }\n\n  detectHashtags(content: string): void {\n    const hashtagMatch = content.match(/#(\\w*)$/);\n    if (hashtagMatch) {\n      const query = hashtagMatch[1];\n      this.hashtagSuggestions = this.getFilteredHashtags(query);\n    } else {\n      this.hashtagSuggestions = [];\n    }\n  }\n\n  detectCommands(content: string): void {\n    const commandMatch = content.match(/\\/(\\w*)$/);\n    if (commandMatch) {\n      const query = commandMatch[1];\n      this.commandSuggestions = this.getFilteredCommands(query);\n    } else {\n      this.commandSuggestions = [];\n    }\n  }\n\n  detectLinks(content: string): void {\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    const links = content.match(urlRegex);\n    if (links) {\n      // Traiter les liens détectés\n      console.log('Liens détectés:', links);\n    }\n  }\n\n  insertMention(user: any): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/@\\w*$/, `@${user.username} `);\n    this.messageForm.patchValue({ content: newContent });\n    this.mentionSuggestions = [];\n  }\n\n  insertHashtag(hashtag: any): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/#\\w*$/, `#${hashtag.name} `);\n    this.messageForm.patchValue({ content: newContent });\n    this.hashtagSuggestions = [];\n  }\n\n  insertCommand(command: any): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/\\/\\w*$/, `/${command.name} `);\n    this.messageForm.patchValue({ content: newContent });\n    this.commandSuggestions = [];\n  }\n\n  setSelectedMentionIndex(index: number): void {\n    this.selectedMentionIndex = index;\n  }\n\n  updateSuggestions(): void {\n    const content = this.messageForm.get('content')?.value || '';\n    this.detectMentions(content);\n    this.detectHashtags(content);\n    this.detectCommands(content);\n  }\n\n  // === MÉTHODES POUR LE COMPTAGE DE CARACTÈRES ===\n  getCharacterCount(): number {\n    const content = this.messageForm.get('content')?.value || '';\n    return content.length;\n  }\n\n  hasFormattedText(): boolean {\n    const content = this.messageForm.get('content')?.value || '';\n    return (\n      content.includes('*') ||\n      content.includes('_') ||\n      content.includes('`') ||\n      content.includes('~')\n    );\n  }\n\n  // === MÉTHODES POUR LA TRADUCTION ===\n  getLanguageName(languageCode: string): string {\n    const languages: { [key: string]: string } = {\n      fr: 'Français',\n      en: 'Anglais',\n      es: 'Espagnol',\n      de: 'Allemand',\n      it: 'Italien',\n    };\n    return languages[languageCode] || languageCode;\n  }\n\n  translateMessage(): void {\n    this.showTranslationPanel = true;\n    const content = this.messageForm.get('content')?.value || '';\n    this.originalTextForTranslation = content;\n    // Simuler une traduction\n    this.translatedText = `[Traduction] ${content}`;\n  }\n\n  showAutoCorrections(): void {\n    // Afficher les corrections automatiques\n    this.autoCorrectSuggestions = [\n      { original: 'salut', corrections: ['Salut'] },\n      { original: 'ca va', corrections: ['Ça va'] },\n    ];\n  }\n\n  // === MÉTHODES UTILITAIRES MANQUANTES ===\n  handleTyping(): void {\n    // Gérer l'indicateur de frappe\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'événement de début de frappe\n    }\n\n    // Réinitialiser le timer\n    clearTimeout(this.typingTimeout);\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Envoyer l'événement de fin de frappe\n    }, 1000);\n  }\n\n  getFilteredUsers(query: string): any[] {\n    const users = [\n      { id: '1', username: 'alice', name: 'Alice Martin' },\n      { id: '2', username: 'bob', name: 'Bob Dupont' },\n      { id: '3', username: 'claire', name: 'Claire Durand' },\n    ];\n\n    if (!query) return users;\n    return users.filter(\n      (user) =>\n        user.username.toLowerCase().includes(query.toLowerCase()) ||\n        user.name.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n\n  getFilteredHashtags(query: string): any[] {\n    const hashtags = [\n      { id: '1', name: 'important', count: 15 },\n      { id: '2', name: 'urgent', count: 8 },\n      { id: '3', name: 'meeting', count: 12 },\n    ];\n\n    if (!query) return hashtags;\n    return hashtags.filter((hashtag) =>\n      hashtag.name.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n\n  getFilteredCommands(query: string): any[] {\n    const commands = [\n      { id: '1', name: 'help', description: \"Afficher l'aide\" },\n      { id: '2', name: 'clear', description: 'Effacer la conversation' },\n      { id: '3', name: 'status', description: 'Changer le statut' },\n    ];\n\n    if (!query) return commands;\n    return commands.filter((command) =>\n      command.name.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n\n  // === MÉTHODES MANQUANTES POUR LES MODALS ET FONCTIONNALITÉS ===\n  openCamera(): void {\n    // Ouvrir la caméra\n    this.toastService.showInfo('Ouverture de la caméra...');\n  }\n\n  openLocationPicker(): void {\n    this.showLocationPicker = true;\n  }\n\n  openContactPicker(): void {\n    this.showContactPicker = true;\n  }\n\n  openPollCreator(): void {\n    this.showPollCreator = true;\n  }\n\n  hideQuickReplies(): void {\n    this.showQuickReplies = false;\n  }\n\n  insertQuickReply(reply: string): void {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + reply,\n    });\n    this.hideQuickReplies();\n  }\n\n  // === CORRECTION DES MÉTHODES AVEC PARAMÈTRES ===\n  onSearchInput(event?: any): void {\n    // Rechercher dans les messages\n    if (event) {\n      this.searchQuery = event.target.value;\n    }\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  getEmojisForCategory(category: any): any[] {\n    // Retourner les émojis pour une catégorie\n    return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'];\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n  }\n\n  startVideoCall(): void {\n    this.startCall('video' as CallType);\n  }\n\n  startVoiceCall(): void {\n    this.startCall('voice' as CallType);\n  }\n}\n"], "mappings": ";AAAA,SACEA,SAAS,EAGTC,SAAS,QAIJ,eAAe;AAGtB,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SAASC,YAAY,QAAQ,MAAM;AAiB5B,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAsV/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,cAA8B,EAC9BC,YAA0B,EAC1BC,EAAe,EACfC,GAAsB,EACtBC,iBAAoC;IAPpC,KAAAP,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAvV3B;IACA,KAAAC,QAAQ,GAAc,EAAE;IAExB,KAAAC,YAAY,GAAwB,IAAI;IACxC,KAAAC,OAAO,GAAG,IAAI;IAEd,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,eAAe,GAAW,KAAK;IAC/B,KAAAC,gBAAgB,GAAgB,IAAI;IACpC,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,UAAU,GAAgC,IAAI;IAC9C,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAE1B;IACiB,KAAAC,qBAAqB,GAAG,CAAC;IACzB,KAAAC,oBAAoB,GAAG,EAAE;IACzB,KAAAC,kBAAkB,GAAG,GAAG;IACjC,KAAAC,WAAW,GAAG,CAAC;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACd,KAAAC,aAAa,GAAG,IAAI9B,YAAY,EAAE;IAE1C;IACA,KAAA+B,aAAa,GAAW,eAAe;IACvC,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,SAAS,GAAQ,IAAI;IAErB;IACA,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,uBAAuB,GAAW,CAAC;IACnC,KAAAC,qBAAqB,GAAgB,IAAIC,GAAG,EAAE;IAC9C,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,iBAAiB,GAAQ,IAAI;IAE7B;IACA,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,kBAAkB,GAAQ,EAAE;IAC5B,KAAAC,iBAAiB,GAAQ,EAAE;IAC3B,KAAAC,cAAc,GAAQ,EAAE;IACxB,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,kBAAkB,GAAQ,EAAE;IAE5B;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,qBAAqB,GAAa,EAAE;IACpC,KAAAC,sBAAsB,GAAU,EAAE;IAClC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAC,eAAe,GAAU,CACvB;MAAEC,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAc,CAAE,EACvD;MAAEF,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAc,CAAE,EACxD;MAAEF,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAa,CAAE,EACxD;MAAEF,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAY,CAAE,EACtD;MAAEF,EAAE,EAAE,MAAM;MAAEC,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAkB,CAAE,EAC5D;MAAEF,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAc,CAAE,EACtD;MAAEF,EAAE,EAAE,YAAY;MAAEC,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAsB,CAAE,EACrE;MAAEF,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAkB,CAAE,EAC3D;MAAEF,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAc,CAAE,EACzD;MAAEF,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAa,CAAE,CACvD;IACD,KAAAC,qBAAqB,GAAG,QAAQ;IAChC,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,mBAAmB,GAAG,EAAE;IAExB;IACA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,aAAa,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,CAAC;IAC7E,KAAAC,mBAAmB,GAAG,UAAU;IAEhC;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,aAAa,GAAG,CACd,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,KAAAC,oBAAoB,GAAG,SAAS;IAChC,KAAAC,kBAAkB,GAAG,SAAS;IAC9B,KAAAC,WAAW,GAAG,CAAC;IAEf;IACA,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,OAAO;IACpB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,MAAM,GAAG,CAAC;IACV,KAAAC,MAAM,GAAG,CAAC;IACV,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAE1B;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,eAAe,GAAG,MAAM;IACxB,KAAAC,QAAQ,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAG,CAAE;IAClD,KAAAC,SAAS,GAAG,MAAM;IAClB,KAAAC,YAAY,GAAU,CACpB;MAAErC,IAAI,EAAE,MAAM;MAAEsC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAM,CAAE,EAC7C;MAAEvC,IAAI,EAAE,WAAW;MAAEsC,KAAK,EAAE,cAAc;MAAEC,GAAG,EAAE;IAAiB,CAAE,EACpE;MAAEvC,IAAI,EAAE,OAAO;MAAEsC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAa,CAAE,EACrD;MAAEvC,IAAI,EAAE,SAAS;MAAEsC,KAAK,EAAE,SAAS;MAAEC,GAAG,EAAE;IAA0B,CAAE,EACtE;MAAEvC,IAAI,EAAE,QAAQ;MAAEsC,KAAK,EAAE,UAAU;MAAEC,GAAG,EAAE;IAAiB,CAAE,EAC7D;MAAEvC,IAAI,EAAE,UAAU;MAAEsC,KAAK,EAAE,WAAW;MAAEC,GAAG,EAAE;IAAe,CAAE,CAC/D;IACD,KAAAC,mBAAmB,GAAG,MAAM;IAC5B,KAAAC,gBAAgB,GAAG;MAAEC,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAC,CAAE;IACxE,KAAAC,iBAAiB,GAAU,EAAE;IAC7B,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,cAAc,GAAG,OAAO;IACxB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,SAAS,GAAG,SAAS;IACrB,KAAAC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE,OAAO;MAAEd,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEc,KAAK,EAAE,WAAW;MAAEd,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEc,KAAK,EAAE,iBAAiB;MAAEd,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAEc,KAAK,EAAE,aAAa;MAAEd,KAAK,EAAE;IAAa,CAAE,CAC/C;IAED;IACA,KAAAe,eAAe,GAAG,KAAK;IACvB,KAAAC,YAAY,GAAG,MAAM;IACrB,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,UAAU,GAAG,MAAM;IACnB,KAAAC,aAAa,GAAa,EAAE;IAE5B;IACA,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,kBAAkB,GAAG,IAAI;IACzB,KAAAC,aAAa,GAAQ;MACnBC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,CAAC;MACdC,eAAe,EAAE,IAAI;MACrBC,WAAW,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC;MACdC,kBAAkB,EAAE,CAAC;MACrBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,mBAAmB,EAAE,CAAC;MACtBC,uBAAuB,EAAE,IAAI;MAC7BC,SAAS,EAAE;KACZ;IAED;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,qBAAqB,GAAG,CACtB,KAAK,EACL,cAAc,EACd,eAAe,EACf,WAAW,EACX,UAAU,CACX;IACD,KAAAC,2BAA2B,GAAG,KAAK;IACnC,KAAAC,QAAQ,GAAU,EAAE;IAEpB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,YAAY,GAAa,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC;IACxE,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,kBAAkB,GAAU,EAAE;IAC9B,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,kBAAkB,GAAU,EAAE;IAC9B,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,kBAAkB,GAAU,EAAE;IAC9B,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,gBAAgB,GAAG,IAAI;IACvB,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,0BAA0B,GAAG,KAAK;IAClC,KAAAC,sBAAsB,GAAU,EAAE;IAElC;IACA,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,eAAe,GAAG,MAAM;IACxB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,0BAA0B,GAAG,EAAE;IAC/B,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,IAAI,EAAE,IAAI;MAAE7G,IAAI,EAAE;IAAU,CAAE,EAChC;MAAE6G,IAAI,EAAE,IAAI;MAAE7G,IAAI,EAAE;IAAS,CAAE,EAC/B;MAAE6G,IAAI,EAAE,IAAI;MAAE7G,IAAI,EAAE;IAAS,CAAE,EAC/B;MAAE6G,IAAI,EAAE,IAAI;MAAE7G,IAAI,EAAE;IAAS,CAAE,EAC/B;MAAE6G,IAAI,EAAE,IAAI;MAAE7G,IAAI,EAAE;IAAU,CAAE,CACjC;IAED;IACA,KAAA8G,eAAe,GAAG,KAAK;IACvB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,WAAW,GAAU,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,EAAE;MAAEA,IAAI,EAAE;IAAE,CAAE,CAAC;IACjD,KAAAC,YAAY,GAAG;MACbC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE;KACb;IACD,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,gBAAgB,GAAG,EAAE;IAErB;IACA,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,mBAAmB,GAAG,EAAE;IACxB,KAAAC,qBAAqB,GAAU,EAAE;IACjC,KAAAC,gBAAgB,GAAQ,IAAI;IAC5B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,eAAe,GAAG,EAAE;IAEpB;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,yBAAyB,GAAQ,IAAI;IAErC;IACA,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,gBAAgB,GAAG,cAAc;IACjC,KAAAC,kBAAkB,GAAG,CACnB;MAAEnF,KAAK,EAAE,cAAc;MAAEd,KAAK,EAAE;IAAa,CAAE,EAC/C;MAAEc,KAAK,EAAE,kBAAkB;MAAEd,KAAK,EAAE;IAAgB,CAAE,EACtD;MAAEc,KAAK,EAAE,YAAY;MAAEd,KAAK,EAAE;IAAa,CAAE,CAC9C;IAED;IACA,KAAAkG,oBAAoB,GAAG,EAAE;IACzB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,gBAAgB,GAAgB,IAAIpK,GAAG,EAAE;IACzC,KAAAqK,gBAAgB,GAAG,EAAE;IACrB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,mBAAmB,GAAG,CAAC;IAEvB;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,YAAY,GAAU,EAAE;IAExB;IACA,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,SAAS,GAAG,MAAM;IAElB;IACA,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,oBAAoB,GAAG;MACrBpL,aAAa,EAAE,IAAI;MACnBqL,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAE,IAAI;MAClBC,gBAAgB,EAAE;KACnB;IAED;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,mBAAmB,GAAG,MAAM;IAC5B,KAAAC,kBAAkB,GAAG,CAAC;IACtB,KAAAC,iBAAiB,GAAa,EAAE;IAChC,KAAAC,qBAAqB,GAAG,QAAQ;IAChC,KAAAC,YAAY,GAAa,EAAE;IAC3B,KAAAC,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,YAAY,GAAa,EAAE;IAC3B,KAAAC,kBAAkB,GAAa,EAAE;IAEjC;IACA,KAAAC,cAAc,GAAG,EAAE;IAiBnB;IACS,KAAAC,CAAC,GAAG;MACXC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC/CC,MAAM,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAG,CAAE;MAC9DC,KAAK,EAAE,6BAA6B;MACpCC,SAAS,EAAEA,CAACC,CAAS,EAAEC,IAAS,KAAaA,IAAI,EAAErL,EAAE,IAAIoL,CAAC,CAACE,QAAQ,EAAE;MACrEjN,aAAa,EAAE;QACbkN,WAAW,EAAE;UAAErL,IAAI,EAAE,gBAAgB;UAAEsL,KAAK,EAAE;QAAe,CAAE;QAC/DC,WAAW,EAAE;UAAEvL,IAAI,EAAE,oBAAoB;UAAEsL,KAAK,EAAE;QAAc,CAAE;QAClEE,MAAM,EAAE;UAAExL,IAAI,EAAE,YAAY;UAAEsL,KAAK,EAAE;QAAe;OACrD;MACDG,MAAM,EAAE;QACNC,MAAM,EAAE;UACN1E,IAAI,EAAE,UAAU;UAChBsE,KAAK,EAAE,gBAAgB;UACvBtL,IAAI,EAAE;SACP;QACD2L,OAAO,EAAE;UACP3E,IAAI,EAAE,YAAY;UAClBsE,KAAK,EAAE,eAAe;UACtBtL,IAAI,EAAE;SACP;QACD4K,IAAI,EAAE;UAAE5D,IAAI,EAAE,QAAQ;UAAEsE,KAAK,EAAE,iBAAiB;UAAEtL,IAAI,EAAE;QAAc,CAAE;QACxE4L,IAAI,EAAE;UACJ5E,IAAI,EAAE,QAAQ;UACdsE,KAAK,EAAE,cAAc;UACrBtL,IAAI,EAAE;;OAET;MACD6L,MAAM,EAAE,CACN;QAAEC,GAAG,EAAE,eAAe;QAAEzJ,KAAK,EAAE,QAAQ;QAAEiJ,KAAK,EAAE;MAAS,CAAE,EAC3D;QAAEQ,GAAG,EAAE,gBAAgB;QAAEzJ,KAAK,EAAE,MAAM;QAAEiJ,KAAK,EAAE;MAAS,CAAE,EAC1D;QAAEQ,GAAG,EAAE,iBAAiB;QAAEzJ,KAAK,EAAE,MAAM;QAAEiJ,KAAK,EAAE;MAAS,CAAE,EAC3D;QAAEQ,GAAG,EAAE,eAAe;QAAEzJ,KAAK,EAAE,MAAM;QAAEiJ,KAAK,EAAE;MAAS,CAAE,CAC1D;MACDS,KAAK,EAAE;QACLC,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;;KAEb;IAmdO,KAAAC,iBAAiB,GAAa,EAAE;IA49CxC;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IAkBpB,KAAAC,kBAAkB,GAAG,EAAE;IAsRvB,KAAAC,gBAAgB,GAAG,IAAI;IAvwErB,IAAI,CAACC,WAAW,GAAG,IAAI,CAAClR,EAAE,CAACmR,KAAK,CAAC;MAC/BC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7R,UAAU,CAAC8R,QAAQ,EAAE9R,UAAU,CAAC+R,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EA4CA;EACA,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACrC,CAAC,CAACC,SAAS;EACzB;EACA,IAAIqC,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC1R,cAAc,CAAC2R,eAAe,EAAE;EAC9C;EACA,IAAIC,wBAAwBA,CAAA;IAC1B,OAAO,IAAI,CAAC7O,uBAAuB;EACrC;EACA,IAAI8O,iBAAiBA,CAAA;IACnB,OAAO,QAAQ;EACjB;EACA,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,CAAC;EACV;EAEA;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACzQ,aAAa,CAAC0Q,WAAW,EAAE;IAChC,IAAI,IAAI,CAACrP,SAAS,EAAEsP,aAAa,CAAC,IAAI,CAACtP,SAAS,CAAC;IACjD,IAAI,IAAI,CAACuP,aAAa,EAAEC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;EAC1D;EAEAE,kBAAkBA,CAAA;IAChB,IAAI,CAACnS,GAAG,CAACoS,aAAa,EAAE;EAC1B;EAEA;EACQP,mBAAmBA,CAAA;IACzB,IAAI,CAACQ,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,kBAAkB,EAAE;IACzB;IACA,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQF,gBAAgBA,CAAA;IACtB,IAAI,CAAC5S,KAAK,CAAC+S,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACrC,MAAME,cAAc,GAAGF,MAAM,CAAC,IAAI,CAAC;MACnC,IAAIE,cAAc,EAAE;QAClB,IAAI,CAACC,eAAe,CAACD,cAAc,CAAC;QACpC,IAAI,CAACE,WAAW,EAAE;;IAEtB,CAAC,CAAC;EACJ;EAEQN,kBAAkBA,CAAA;IACxB;EAAA;EAGF;EACAK,eAAeA,CAACD,cAAsB;IACpC,IAAI,CAACvS,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,cAAc,CAAC+S,eAAe,CAACD,cAAc,CAAC,CAACD,SAAS,CAAC;MAC5DI,IAAI,EAAG3S,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACI,gBAAgB,GACnBJ,YAAY,CAAC4S,YAAY,EAAEC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC3O,EAAE,KAAK,IAAI,CAACjE,aAAa,CAAC,IACnE,IAAI;QACN,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDoP,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACpP,OAAO,GAAG,KAAK;QACpB8S,OAAO,CAAC1D,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACvE;KACD,CAAC;EACJ;EAEA;EACAqD,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC1S,YAAY,EAAEmE,EAAE,EAAE;IAE5B,IAAI,CAACzE,cAAc,CAACgT,WAAW,CAC7B,IAAI,CAAC1S,YAAY,CAACmE,EAAE,EACpB,IAAI,CAACpD,WAAW,CAAC0O,QAAQ,EAAE,EAC3B,IAAI,CAAC5O,oBAAoB,CAAC4O,QAAQ,EAAE,CACrC,CAAC8C,SAAS,CAAC;MACVI,IAAI,EAAG5S,QAAQ,IAAI;QACjB,IAAI,IAAI,CAACgB,WAAW,KAAK,CAAC,EAAE;UAC1B,IAAI,CAAChB,QAAQ,GAAGA,QAAQ;SACzB,MAAM;UACL,IAAI,CAACA,QAAQ,GAAG,CAAC,GAAGA,QAAQ,EAAE,GAAG,IAAI,CAACA,QAAQ,CAAC;;QAEjD,IAAI,CAACkB,eAAe,GAAGlB,QAAQ,CAACiT,MAAM,KAAK,IAAI,CAACnS,oBAAoB;QACpE,IAAI,CAACoS,cAAc,EAAE;MACvB,CAAC;MACD5D,KAAK,EAAGA,KAAK,IAAI;QACf0D,OAAO,CAAC1D,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC1P,YAAY,CAACuT,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,MAAMnC,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,EAAE6L,IAAI,EAAE;IAC9D,IAAI,CAACrC,OAAO,IAAI,CAAC,IAAI,CAAChR,YAAY,EAAEmE,EAAE,EAAE;IAExC,IAAI,CAAC6J,gBAAgB,GAAG,IAAI;IAE5B;IACA,MAAMsF,WAAW,GAAY;MAC3BnP,EAAE,EAAEoP,IAAI,CAACC,GAAG,EAAE,CAAC/D,QAAQ,EAAE;MACzBuB,OAAO;MACPyC,MAAM,EAAE;QAAEtP,EAAE,EAAE,IAAI,CAACjE,aAAc;QAAEwT,QAAQ,EAAE,IAAI,CAACvT;MAAe,CAAE;MACnEwT,SAAS,EAAE,IAAIJ,IAAI,EAAE;MACrBf,cAAc,EAAE,IAAI,CAACxS,YAAY,CAACmE,EAAE;MACpCyP,IAAI,EAAE;KACP;IAED,IAAI,CAAC7T,QAAQ,CAAC8T,IAAI,CAACP,WAAW,CAAC;IAC/B,IAAI,CAACxC,WAAW,CAACgD,KAAK,EAAE;IACxB,IAAI,CAACb,cAAc,EAAE;IACrB,IAAI,CAACjF,gBAAgB,GAAG,KAAK;IAE7B;IACA;;;;;;;;;;;;;;;;;;;;;;EAsBF;EAEA;EACA+F,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAACjB,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACtE,eAAe,GAAGyF,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC;MACxC,IAAI,CAACI,gBAAgB,EAAE;;EAE3B;EAEAC,gBAAgBA,CAACC,KAAa;IAC5B,IAAI,CAAC7F,eAAe,CAAC8F,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI,CAAC7F,eAAe,CAACsE,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACzS,UAAU,GAAG,IAAI;;EAE1B;EAEA;EACAkU,cAAcA,CAACC,SAAiB,EAAEC,KAAa;IAC7C;IACA,MAAMC,OAAO,GAAG,IAAI,CAAC7U,QAAQ,CAAC8S,IAAI,CAAEgC,CAAC,IAAKA,CAAC,CAAC1Q,EAAE,KAAKuQ,SAAS,CAAC;IAC7D,IAAIE,OAAO,EAAE;MACX,IAAI,CAAEA,OAAe,CAAC7F,SAAS,EAAG6F,OAAe,CAAC7F,SAAS,GAAG,EAAE;MAChE,MAAM+F,gBAAgB,GAAIF,OAAe,CAAC7F,SAAS,CAAC8D,IAAI,CACrDkC,CAAM,IAAKA,CAAC,CAACJ,KAAK,KAAKA,KAAK,CAC9B;MACD,IAAIG,gBAAgB,EAAE;QACpBA,gBAAgB,CAACE,KAAK,GAAG,CAACF,gBAAgB,CAACE,KAAK,IAAI,CAAC,IAAI,CAAC;OAC3D,MAAM;QACJJ,OAAe,CAAC7F,SAAS,CAAC8E,IAAI,CAAC;UAC9Bc,KAAK;UACLK,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,CAAC,IAAI,CAAC/U,aAAa;SAC3B,CAAC;;;EAGR;EAEAgV,WAAWA,CAACR,SAAiB,EAAEC,KAAa;IAC1C,IAAI,CAACF,cAAc,CAACC,SAAS,EAAEC,KAAK,CAAC;IACrC,IAAI,CAACpR,kBAAkB,GAAG,EAAE;EAC9B;EAEA;EACA4R,gBAAgBA,CAACP,OAAY;IAC3B,IAAI,CAAC/R,gBAAgB,GAAG+R,OAAO,CAACzQ,EAAE;IAClC,IAAI,CAACrB,cAAc,GAAG8R,OAAO,CAAC5D,OAAO,IAAI,EAAE;IAC3C,IAAI,CAACrN,kBAAkB,GAAG,EAAE;EAC9B;EAEAyR,UAAUA,CAAA;IACR,IAAI,CAACvS,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B;EAEAuS,QAAQA,CAACX,SAAiB;IACxB,IAAI,IAAI,CAAC5R,cAAc,EAAEuQ,IAAI,EAAE,EAAE;MAC/B,IAAI,CAAC3T,cAAc,CAAC4V,WAAW,CAC7BZ,SAAS,EACT,IAAI,CAAC5R,cAAc,CAACuQ,IAAI,EAAE,CAC3B,CAACd,SAAS,CAAC;QACVI,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACyC,UAAU,EAAE;UACjB,IAAI,CAAC1C,WAAW,EAAE;QACpB,CAAC;QACDrD,KAAK,EAAGA,KAAK,IAAI;UACf0D,OAAO,CAAC1D,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACzD;OACD,CAAC;;EAEN;EAEAkG,aAAaA,CAACvB,KAAoB,EAAEU,SAAiB;IACnD,IAAIV,KAAK,CAAC7D,GAAG,KAAK,OAAO,IAAI,CAAC6D,KAAK,CAACwB,QAAQ,EAAE;MAC5CxB,KAAK,CAACyB,cAAc,EAAE;MACtB,IAAI,CAACJ,QAAQ,CAACX,SAAS,CAAC;KACzB,MAAM,IAAIV,KAAK,CAAC7D,GAAG,KAAK,QAAQ,EAAE;MACjC,IAAI,CAACiF,UAAU,EAAE;;EAErB;EAEA;EACAM,aAAaA,CAAChB,SAAiB;IAC7B,IAAIiB,OAAO,CAAC,iDAAiD,CAAC,EAAE;MAC9D,IAAI,CAACjW,cAAc,CAACgW,aAAa,CAAChB,SAAS,CAAC,CAACnC,SAAS,CAAC;QACrDI,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACD,WAAW,EAAE;UAClB,IAAI,CAAC/O,kBAAkB,GAAG,EAAE;QAC9B,CAAC;QACD0L,KAAK,EAAGA,KAAK,IAAI;UACf0D,OAAO,CAAC1D,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;OACD,CAAC;;EAEN;EAEA;EACAuG,SAASA,CAAChC,IAAc;IACtB,IAAI,CAAC,IAAI,CAACxT,gBAAgB,EAAE+D,EAAE,EAAE;IAEhC,IAAI,CAACzE,cAAc,CAACmW,YAAY,CAAC,IAAI,CAACzV,gBAAgB,CAAC+D,EAAE,EAAEyP,IAAI,CAAC,CAACrB,SAAS,CAAC;MACzEI,IAAI,EAAGmD,IAAI,IAAI;QACb,IAAI,CAAC7T,UAAU,GAAG6T,IAAI;QACtB,IAAI,CAAC3T,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAAC4T,cAAc,EAAE;MACvB,CAAC;MACD1G,KAAK,EAAGA,KAAK,IAAI;QACf0D,OAAO,CAAC1D,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC1P,YAAY,CAACuT,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEQ6C,cAAcA,CAAA;IACpB,IAAI,CAACzT,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAGyT,WAAW,CAAC,MAAK;MAChC,IAAI,CAAC1T,YAAY,EAAE;IACrB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA2T,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC1T,SAAS,EAAE;MAClBsP,aAAa,CAAC,IAAI,CAACtP,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAEvB,IAAI,CAACN,UAAU,GAAG,IAAI;IACtB,IAAI,CAACE,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACG,YAAY,GAAG,CAAC;EACvB;EAEA4T,UAAUA,CAAA;IACR,IAAI,CAAC9T,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;EACtC;EAEA+T,WAAWA,CAAA;IACT,IAAI,CAAC9T,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA;EACA4Q,cAAcA,CAAA;IACZmD,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,iBAAiBA,CAAC/C,SAAc;IAC9B,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,MAAMgD,IAAI,GAAG,IAAIpD,IAAI,CAACI,SAAS,CAAC;IAChC,OAAOgD,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAC,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAC7B,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,MAAM3H,CAAC,GAAG4H,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,KAAK,CAAC,GAAGG,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAGG,IAAI,CAACI,GAAG,CAACN,CAAC,EAAE1H,CAAC,CAAC,EAAEiI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAAC3H,CAAC,CAAC;EACzE;EAEAkI,gBAAgBA,CAAClD,KAAa,EAAEK,OAAY;IAC1C,OAAOA,OAAO,EAAEzQ,EAAE,IAAIoQ,KAAK,CAAC9E,QAAQ,EAAE;EACxC;EAEA;EACAiI,YAAYA,CAAA;IACV,IAAI,CAACtU,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB,IAAI,CAACuU,WAAW,EAAE;;EAEtB;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAAClW,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEAmW,sBAAsBA,CAAA;IACpB,IAAI,CAACnK,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;EACxD;EAEAoK,mBAAmBA,CAAA;IACjB,IAAI,CAAC1W,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAEA2W,cAAcA,CAAA;IACZ,IAAI,CAAC1W,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA2W,oBAAoBA,CAAA;IAClB,IAAI,CAAC3U,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA4U,oBAAoBA,CAAA;IAClB,IAAI,CAACxW,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEAyW,qBAAqBA,CAAA;IACnB,IAAI,CAACtW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA;EAEQuW,aAAaA,CAAA;IACnB,IAAI,CAAClV,WAAW,GAAG,IAAI;IACvB;IACAmT,UAAU,CAAC,MAAK;MACd,IAAI,CAACjT,aAAa,GAAG,IAAI,CAACpD,QAAQ,CAACqY,MAAM,CAAExD,OAAO,IAChDA,OAAO,CAAC5D,OAAO,EAAEqH,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtV,WAAW,CAACqV,WAAW,EAAE,CAAC,CACxE;MACD,IAAI,CAACpV,WAAW,GAAG,KAAK;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA0U,WAAWA,CAAA;IACT,IAAI,CAAC3U,WAAW,GAAG,EAAE;IACrB,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC0J,eAAe,GAAG,EAAE;EAC3B;EAEA0L,mBAAmBA,CAACvH,OAAe;IACjC,IAAI,CAAC,IAAI,CAAChO,WAAW,IAAI,CAACgO,OAAO,EAAE,OAAOA,OAAO;IACjD,MAAMwH,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAI,IAAI,CAACzV,WAAW,GAAG,EAAE,IAAI,CAAC;IACvD,OAAOgO,OAAO,CAAC0H,OAAO,CACpBF,KAAK,EACL,mDAAmD,CACpD;EACH;EAEAG,eAAeA,CAACjE,SAAiB;IAC/B,IAAI,CAAC9H,oBAAoB,GAAG8H,SAAS;IACrC;IACA0B,UAAU,CAAC,MAAK;MACd,IAAI,CAACxJ,oBAAoB,GAAG,EAAE;IAChC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACAgM,qBAAqBA,CAAA;IACnB;EAAA;EAGFC,mBAAmBA,CAAA;IACjB,IAAI,CAACvV,cAAc,GAAG,EAAE;EAC1B;EAEAwV,gBAAgBA,CAACpE,SAAiB;IAChC,MAAME,OAAO,GAAG,IAAI,CAAC7U,QAAQ,CAAC8S,IAAI,CAAEgC,CAAC,IAAKA,CAAC,CAAC1Q,EAAE,KAAKuQ,SAAS,CAAC;IAC7D,IAAIE,OAAO,EAAE;MACXA,OAAO,CAACmE,MAAM,GAAG,CAACnE,OAAO,CAACmE,MAAM;MAChC,IAAInE,OAAO,CAACmE,MAAM,EAAE;QAClB,IAAI,CAACzV,cAAc,CAACuQ,IAAI,CAACe,OAAO,CAAC;OAClC,MAAM;QACL,IAAI,CAACtR,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC8U,MAAM,CAC7CvD,CAAC,IAAKA,CAAC,CAAC1Q,EAAE,KAAKuQ,SAAS,CAC1B;;;IAGL,IAAI,CAAC/Q,kBAAkB,GAAG,EAAE;EAC9B;EAEA;EACAqV,oBAAoBA,CAACtE,SAAiB;IACpC,IAAI,IAAI,CAAC/Q,kBAAkB,CAAC+Q,SAAS,CAAC,EAAE;MACtC,IAAI,CAAC/Q,kBAAkB,GAAG,EAAE;KAC7B,MAAM;MACL,IAAI,CAACA,kBAAkB,GAAG;QAAE,CAAC+Q,SAAS,GAAG;MAAI,CAAE;;EAEnD;EAEAuE,oBAAoBA,CAACvE,SAAiB;IACpC,IAAI,IAAI,CAACnR,kBAAkB,CAACmR,SAAS,CAAC,EAAE;MACtC,IAAI,CAACnR,kBAAkB,GAAG,EAAE;KAC7B,MAAM;MACL,IAAI,CAACA,kBAAkB,GAAG;QAAE,CAACmR,SAAS,GAAG;MAAI,CAAE;;EAEnD;EAEA;EACAwE,cAAcA,CAACtE,OAAY;IACzB,IAAI,CAAC7R,iBAAiB,GAAG6R,OAAO;IAChC,IAAI,CAACjR,kBAAkB,GAAG,EAAE;EAC9B;EAEAwV,cAAcA,CAACvE,OAAY;IACzB,IAAI,CAAC/Q,iBAAiB,GAAG+Q,OAAO;IAChC,IAAI,CAAChR,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACD,kBAAkB,GAAG,EAAE;EAC9B;EAEAyV,WAAWA,CAACxE,OAAY;IACtB,IAAIyE,SAAS,CAACC,SAAS,IAAI1E,OAAO,CAAC5D,OAAO,EAAE;MAC1CqI,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC3E,OAAO,CAAC5D,OAAO,CAAC;MAC9C,IAAI,CAACrR,YAAY,CAAC6Z,WAAW,CAAC,eAAe,CAAC;;IAEhD,IAAI,CAAC7V,kBAAkB,GAAG,EAAE;EAC9B;EAEA8V,aAAaA,CAAC/E,SAAiB;IAC7B,IAAI,CAAC5H,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC4M,sBAAsB,CAAChF,SAAS,CAAC;IACtC,IAAI,CAAC/Q,kBAAkB,GAAG,EAAE;EAC9B;EAEA+V,sBAAsBA,CAAChF,SAAiB;IACtC,IAAI,IAAI,CAAC3H,gBAAgB,CAAC4M,GAAG,CAACjF,SAAS,CAAC,EAAE;MACxC,IAAI,CAAC3H,gBAAgB,CAAC6M,MAAM,CAAClF,SAAS,CAAC;KACxC,MAAM;MACL,IAAI,CAAC3H,gBAAgB,CAAC8M,GAAG,CAACnF,SAAS,CAAC;;IAGtC,IAAI,IAAI,CAAC3H,gBAAgB,CAAC+M,IAAI,KAAK,CAAC,EAAE;MACpC,IAAI,CAAChN,aAAa,GAAG,KAAK;;EAE9B;EAIA;EACAiN,eAAeA,CAACrF,SAAkB;IAChC,IAAI,CAACpT,eAAe,GAAG,IAAI;IAC3B,IAAIoT,SAAS,EAAE;MACb,IAAI,CAACnR,kBAAkB,GAAG;QAAE,CAACmR,SAAS,GAAG;MAAI,CAAE;;EAEnD;EAEAsF,gBAAgBA,CAAA;IACd,IAAI,CAAC1Y,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACiC,kBAAkB,GAAG,EAAE;EAC9B;EAEA0W,mBAAmBA,CAACC,UAAkB;IACpC,IAAI,CAAC5V,qBAAqB,GAAG4V,UAAU;EACzC;EAEAC,iBAAiBA,CAACxF,KAAa;IAC7B,MAAMyF,cAAc,GAAG,IAAI,CAACtJ,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IACnE,IAAI,CAACsJ,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAEiH,QAAQ,CAACD,cAAc,GAAGzF,KAAK,CAAC;EACnE;EAEA;EACA2F,oBAAoBA,CAAA;IAClB,IAAI,CAAC7L,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA8L,aAAaA,CAAA;IACX,IAAI,CAACC,SAAS,CAACjE,aAAa,CAACkE,KAAK,EAAE;EACtC;EAEA;EACAC,eAAeA,CAAC9F,OAAY;IAC1B,IAAI,CAACxH,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,YAAY,GAAG,CAACsH,OAAO,CAAC;EAC/B;EAEA+F,gBAAgBA,CAAA;IACd,IAAI,CAACvN,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACE,YAAY,GAAG,EAAE;EACxB;EAEA;EACAsN,mBAAmBA,CAAClG,SAAiB;IACnC,IAAI,IAAI,CAACzH,cAAc,KAAKyH,SAAS,EAAE;MACrC,IAAI,CAACzH,cAAc,GAAG,EAAE;KACzB,MAAM;MACL,IAAI,CAACA,cAAc,GAAGyH,SAAS;;EAEnC;EAEAmG,mBAAmBA,CAAA;IACjB,IAAI,CAACna,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,sBAAsB,GAAG,CAAC;IAC/B;EACF;;EAEAma,kBAAkBA,CAAA;IAChB,IAAI,CAACpa,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;EACjC;EAEA;EACAoa,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAC7Z,aAAa,GAAG6Z,KAAK;IAC1B,IAAI,CAAC5Z,iBAAiB,GAAG,KAAK;EAChC;EAEA6Z,eAAeA,CAAA;IACb,OAAO,IAAI,CAACnM,CAAC,CAACoB,MAAM;EACtB;EAEA;EACAgL,gBAAgBA,CAACpL,MAAc;IAC7B,IAAI,CAACtB,gBAAgB,GAAG,IAAI;IAC5B;IACA4H,UAAU,CAAC,MAAK;MACd,IAAI,CAAC5H,gBAAgB,GAAG,KAAK;IAC/B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA2M,gBAAgBA,CAAA;IACd,OAAOC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACvM,CAAC,CAACgB,MAAM,CAAC,CAACwL,GAAG,CAAC,CAAC,CAACnL,GAAG,EAAE3I,KAAK,CAAC,MAAM;MAC1D2I,GAAG;MACH,GAAG3I;KACJ,CAAC,CAAC;EACL;EAEA+T,aAAaA,CAACzL,MAAc;IAC1B,OACE,IAAI,CAAChB,CAAC,CAACgB,MAAM,CAACA,MAAoC,CAAC,EAAEzL,IAAI,IACzD,eAAe;EAEnB;EAEAmX,cAAcA,CAAC1L,MAAc;IAC3B,OACE,IAAI,CAAChB,CAAC,CAACgB,MAAM,CAACA,MAAoC,CAAC,EAAEH,KAAK,IAC1D,eAAe;EAEnB;EAEA8L,aAAaA,CAAC3L,MAAc;IAC1B,OACE,IAAI,CAAChB,CAAC,CAACgB,MAAM,CAACA,MAAoC,CAAC,EAAEzE,IAAI,IAAI,SAAS;EAE1E;EAEA;EACAqQ,oBAAoBA,CAAC1K,OAA2B;IAC9C,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IACvB,OAAOA,OAAO,CACX0H,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAChDA,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC,CACpCA,OAAO,CAAC,UAAU,EAAE,iBAAiB,CAAC,CACtCA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;EAC3B;EAEAiD,WAAWA,CAAC3H,KAAU;IACpB;EAAA;EAGF4H,YAAYA,CAAC5H,KAAU;IACrBA,KAAK,CAACE,MAAM,CAAC2H,GAAG,GAAG,+BAA+B;EACpD;EAEAC,aAAaA,CAAC9H,KAAU,EAAEY,OAAa;IACrC;EAAA;EAGF;EACAmH,sBAAsBA,CAAA;IACpB,IAAI,CAAC7T,sBAAsB,GAAG,IAAI;EACpC;EAEA8T,uBAAuBA,CAAA;IACrB,IAAI,CAAC9T,sBAAsB,GAAG,KAAK;EACrC;EAEA;EACA+T,gBAAgBA,CAAA;IACd,IAAI,CAAC/S,gBAAgB,GAAG,IAAI;EAC9B;EAEAgT,iBAAiBA,CAAA;IACf,IAAI,CAAChT,gBAAgB,GAAG,KAAK;EAC/B;EAEA;EACAiT,gBAAgBA,CAACnL,OAAe;IAC9B,MAAMoL,OAAO,GAAG,IAAI,CAACtL,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAIgJ,OAAO,EAAE;MACXA,OAAO,CAAC/B,QAAQ,CAACrJ,OAAO,CAAC;MACzB,IAAI,CAACmC,WAAW,EAAE;;EAEtB;EAEA;EACA0C,YAAYA,CAACjC,IAAc;IACzB,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAC;EACtB;EAEA;EACQS,gBAAgBA,CAAA;IACtB,IAAI,CAAC3F,eAAe,CAAC2N,OAAO,CAAEC,IAAI,IAAI;MACpC,IAAIA,IAAI,CAAC1I,IAAI,CAAC2I,UAAU,CAAC,QAAQ,CAAC,EAAE;QAClC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;UACpB,IAAI,CAACpc,UAAU,GAAGoc,CAAC,CAACzI,MAAM,EAAE0I,MAAM,IAAI,IAAI;QAC5C,CAAC;QACDJ,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;;IAE9B,CAAC,CAAC;EACJ;EAEQpK,eAAeA,CAAA;IACrB;IACA,IAAI,CAACzS,eAAe,CAACqd,cAAc,EAAE,CAACvK,SAAS,CAAC;MAC9CI,IAAI,EAAGoK,IAAS,IAAI;QAClB,IAAI,CAAC7c,aAAa,GAAG6c,IAAI,EAAE5Y,EAAE,IAAI,IAAI;QACrC,IAAI,CAAChE,eAAe,GAAG4c,IAAI,EAAErJ,QAAQ,IAAI,KAAK;MAChD,CAAC;MACDrE,KAAK,EAAGA,KAAU,IAChB0D,OAAO,CAAC1D,KAAK,CAAC,6CAA6C,EAAEA,KAAK;KACrE,CAAC;EACJ;EAEA;EAEA;EACA2N,cAAcA,CAACpI,OAAY;IACzB,IAAIA,OAAO,CAAC5D,OAAO,IAAI,CAAC4D,OAAO,CAACqI,WAAW,EAAEjK,MAAM,EAAE,OAAO,MAAM;IAClE,IAAI4B,OAAO,CAACqI,WAAW,EAAEC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACvJ,IAAI,KAAK,OAAO,CAAC,EAC/D,OAAO,OAAO;IAChB,IAAIgB,OAAO,CAACqI,WAAW,EAAEC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACvJ,IAAI,KAAK,OAAO,CAAC,EAC/D,OAAO,OAAO;IAChB,IAAIgB,OAAO,CAACqI,WAAW,EAAEC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACvJ,IAAI,KAAK,MAAM,CAAC,EAC9D,OAAO,MAAM;IACf,IAAIgB,OAAO,CAACqI,WAAW,EAAEC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACvJ,IAAI,KAAK,OAAO,CAAC,EAC/D,OAAO,OAAO;IAChB,IAAIgB,OAAO,CAAChB,IAAI,KAAK,UAAU,EAAE,OAAO,UAAU;IAClD,IAAIgB,OAAO,CAAChB,IAAI,KAAK,SAAS,EAAE,OAAO,SAAS;IAChD,OAAO,MAAM;EACf;EAEA;EACAwJ,WAAWA,CAACxI,OAAY;IACtB,OACEA,OAAO,CAACyI,QAAQ,IAChBzI,OAAO,CAACqI,WAAW,EAAEpK,IAAI,CAAEsK,GAAQ,IAAKA,GAAG,CAACvJ,IAAI,KAAK,OAAO,CAAC,EAAE0J,GAAG,IAClE,EAAE;EAEN;EAEAC,WAAWA,CAAC3I,OAAY;IACtB,OACEA,OAAO,CAAC4I,QAAQ,IAChB5I,OAAO,CAACqI,WAAW,EAAEpK,IAAI,CAAEsK,GAAQ,IAAKA,GAAG,CAACvJ,IAAI,KAAK,OAAO,CAAC,EAAE0J,GAAG,IAClE,EAAE;EAEN;EAEAG,iBAAiBA,CAAC7I,OAAY;IAC5B,OAAOA,OAAO,CAAC8I,YAAY,IAAI,EAAE;EACnC;EAEAC,gBAAgBA,CAAC/I,OAAY;IAC3B,OAAOA,OAAO,CAACgJ,QAAQ,IAAI,OAAO;EACpC;EAEA;EACAC,WAAWA,CAACjJ,OAAY;IACtB,OAAO,IAAI,CAACmC,cAAc,CAACnC,OAAO,CAACkF,IAAI,IAAI,CAAC,CAAC;EAC/C;EAEAgE,WAAWA,CAAClJ,OAAY;IACtB,MAAMmJ,QAAQ,GAAGnJ,OAAO,CAACmJ,QAAQ,IAAInJ,OAAO,CAACqI,WAAW,GAAG,CAAC,CAAC,EAAErJ,IAAI,IAAI,EAAE;IACzE,MAAMoK,KAAK,GAAQ;MACjB,iBAAiB,EAAE,iBAAiB;MACpC,oBAAoB,EAAE,kBAAkB;MACxC,0BAA0B,EAAE,mBAAmB;MAC/C,+BAA+B,EAAE,wBAAwB;MACzD,OAAO,EAAE,iBAAiB;MAC1B,QAAQ,EAAE,mBAAmB;MAC7B,QAAQ,EAAE,mBAAmB;MAC7B,QAAQ,EAAE;KACX;IAED,KAAK,MAAM,CAACpK,IAAI,EAAEvP,IAAI,CAAC,IAAI+W,MAAM,CAACC,OAAO,CAAC2C,KAAK,CAAC,EAAE;MAChD,IAAID,QAAQ,CAACxB,UAAU,CAAC3I,IAAI,CAAC,EAAE,OAAOvP,IAAc;;IAEtD,OAAO,aAAa;EACtB;EAEA4Z,WAAWA,CAACrJ,OAAY;IACtB,MAAMmJ,QAAQ,GAAGnJ,OAAO,CAACmJ,QAAQ,IAAInJ,OAAO,CAACqI,WAAW,GAAG,CAAC,CAAC,EAAErJ,IAAI,IAAI,EAAE;IACzE,OAAOmK,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE,IAAI,SAAS;EAC3D;EAEA;EACAC,mBAAmBA,CAACC,OAAe,EAAEC,KAAa;IAChD,MAAMC,UAAU,GAAIC,OAAe,IAAI;MACrC,MAAMC,IAAI,GAAGtH,IAAI,CAACC,KAAK,CAACoH,OAAO,GAAG,EAAE,CAAC;MACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;MACzB,OAAO,GAAGC,IAAI,IAAIC,IAAI,CAACjP,QAAQ,EAAE,CAACkP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IACtD,CAAC;IACD,OAAO,GAAGJ,UAAU,CAACF,OAAO,CAAC,MAAME,UAAU,CAACD,KAAK,CAAC,EAAE;EACxD;EAEAM,gBAAgBA,CAAChK,OAAY;IAC3B,OAAOT,KAAK,CAACC,IAAI,CAAC;MAAEpB,MAAM,EAAE;IAAE,CAAE,EAAE,CAAC6L,CAAC,EAAEtP,CAAC,MAAM;MAC3ChJ,MAAM,EAAE4Q,IAAI,CAAC2H,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;MAC9BC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;KACT,CAAC,CAAC;EACL;EAEAC,gBAAgBA,CAACvK,SAAiB;IAChC,OAAO,CAAC;EACV;EAEAwK,mBAAmBA,CAACxK,SAAiB;IACnC,OAAO,CAAC;EACV;EAEAyK,qBAAqBA,CAACzK,SAAiB;IACrC,OAAO,CAAC;EACV;EAEA0K,aAAaA,CAAC1K,SAAiB;IAC7B,OAAO,IAAI;EACb;EAEA;EACA2K,cAAcA,CAACzK,OAAY;IACzB,OAAO,KAAK;EACd;EAEA0K,cAAcA,CAAC5K,SAAiB;IAC9B,OAAO,KAAK;EACd;EAEA6K,iBAAiBA,CAAC3K,OAAY;IAC5B,OAAO,KAAK;EACd;EAEA4K,uBAAuBA,CAAC5K,OAAY;IAClC,OAAO,CAAC;EACV;EAEA6K,uBAAuBA,CAAC7K,OAAY;IAClC,OAAO,CAAC;EACV;EAEA8K,kBAAkBA,CAAC9K,OAAY;IAC7B,OAAO,EAAE;EACX;EAEA;EACA+K,YAAYA,CAAC/K,OAAY;IACvB;EAAA;EAGFgL,WAAWA,CAAChL,OAAY;IACtB;EAAA;EAGFiL,cAAcA,CAACjL,OAAY;IACzB,MAAMkL,gBAAgB,GAAG,CACvB,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,iBAAiB,CAClB;IACD,OAAOA,gBAAgB,CAAC5C,IAAI,CAAEtJ,IAAI,IAAKgB,OAAO,CAAChB,IAAI,EAAE2I,UAAU,CAAC3I,IAAI,CAAC,CAAC;EACxE;EAEAmM,QAAQA,CAACnL,OAAY;IACnB,OACEA,OAAO,CAAChB,IAAI,KAAK,OAAO,IACvBgB,OAAO,CAACqI,WAAW,IAClBrI,OAAO,CAACqI,WAAW,CAACC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACvJ,IAAI,KAAK,OAAO,CAAE;EAEnE;EAEAoM,YAAYA,CAACrL,KAAa;IACxB,MAAMsL,UAAU,GAAQ;MACtB,IAAI,EAAE,YAAY;MAClB,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,UAAU;MAChB,IAAI,EAAE,QAAQ;MACd,IAAI,EAAE;KACP;IACD,OAAOA,UAAU,CAACtL,KAAK,CAAC,IAAIA,KAAK;EACnC;EAEA;EACAuL,kBAAkBA,CAACtL,OAAY;IAC7B;EAAA;EAGFuL,iBAAiBA,CAACvL,OAAY;IAC5B,OAAOA,OAAO,CAACwL,MAAM,IAAI,EAAE;EAC7B;EAEAC,eAAeA,CAACzL,OAAY;IAC1B,OAAOA,OAAO,CAAC0L,YAAY,IAAI,cAAc;EAC/C;EAEAC,kBAAkBA,CAAC3L,OAAY;IAC7B,OAAOA,OAAO,CAAC4L,OAAO,IAAI,EAAE;EAC9B;EAEA;EACAC,iBAAiBA,CAAC7L,OAAY;IAC5B;EAAA;EAGF8L,gBAAgBA,CAAC9L,OAAY;IAC3B,OAAOA,OAAO,CAAC+L,aAAa,IAAI,kCAAkC;EACpE;EAEAC,cAAcA,CAAChM,OAAY;IACzB,OAAOA,OAAO,CAACiM,WAAW,IAAI,SAAS;EACzC;EAEAC,eAAeA,CAAClM,OAAY;IAC1B,OAAOA,OAAO,CAACmM,YAAY,IAAI,EAAE;EACnC;EAEA;EACAC,eAAeA,CAACpM,OAAY;IAC1B;EAAA;EAGFqM,gBAAgBA,CAACvM,SAAiB,EAAEV,KAAU;IAC5C;EAAA;EAGFkN,gBAAgBA,CAACxM,SAAiB;IAChC;EAAA;EAGF;EACAyM,QAAQA,CAACnN,KAAU;IACjB;IACA,MAAMsC,OAAO,GAAGtC,KAAK,CAACE,MAAM;IAC5B,IAAI,CAAChH,kBAAkB,GACrBoJ,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAAC8K,YAAY,GAAG,GAAG;EACzE;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACrgB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACD,WAAW,EAAE;IAClB,IAAI,CAAC2R,WAAW,EAAE;IAClB0D,UAAU,CAAC,MAAK;MACd,IAAI,CAACpV,aAAa,GAAG,KAAK;IAC5B,CAAC,EAAE,IAAI,CAAC;EACV;EAEAsgB,iBAAiBA,CAAA;IACf,IAAI3L,OAAO,CAAC,qDAAqD,CAAC,EAAE;MAClE,IAAI,CAAC5V,QAAQ,GAAG,EAAE;;EAEtB;EAEAwhB,kBAAkBA,CAAA;IAChB;EAAA;EAGF;EACAC,gBAAgBA,CAAA;IACd,OAAO,CACL;MACEnd,IAAI,EAAE,eAAe;MACrBod,KAAK,EAAE,YAAY;MACnBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChK,YAAY,EAAE;MAClCiK,KAAK,EAAE,YAAY;MACnBC,QAAQ,EAAE,IAAI,CAACxe;KAChB,EACD;MACEiB,IAAI,EAAE,aAAa;MACnBod,KAAK,EAAE,eAAe;MACtBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC9J,mBAAmB,EAAE;MACzC+J,KAAK,EAAE,kBAAkB;MACzBE,KAAK,EACH,IAAI,CAACvQ,wBAAwB,GAAG,CAAC,GAC7B;QACE0D,KAAK,EAAE,IAAI,CAAC1D,wBAAwB;QACpCqQ,KAAK,EAAE,YAAY;QACnBG,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEzd,IAAI,EAAE,cAAc;MACpBod,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC9L,SAAS,CAAC,OAAmB,CAAC;MAClD+L,KAAK,EAAE;KACR,EACD;MACEtd,IAAI,EAAE,cAAc;MACpBod,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC9L,SAAS,CAAC,OAAmB,CAAC;MAClD+L,KAAK,EAAE;KACR,CACF;EACH;EAEA;EAEAI,qBAAqBA,CAAA;IACnB,IAAI,CAACviB,MAAM,CAACwiB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAC,eAAeA,CAACC,MAAc;IAC5B;EAAA;EAGFC,gBAAgBA,CAACC,UAAe;IAC9B,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IACpC,MAAMzL,IAAI,GAAG,IAAIpD,IAAI,CAAC6O,UAAU,CAAC;IACjC,MAAM5O,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM8O,IAAI,GAAG7O,GAAG,CAAC8O,OAAO,EAAE,GAAG3L,IAAI,CAAC2L,OAAO,EAAE;IAC3C,MAAMC,OAAO,GAAGpL,IAAI,CAACC,KAAK,CAACiL,IAAI,GAAG,KAAK,CAAC;IAExC,IAAIE,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;IAClC,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,UAAUA,OAAO,MAAM;IAChD,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,UAAUpL,IAAI,CAACC,KAAK,CAACmL,OAAO,GAAG,EAAE,CAAC,IAAI;IACjE,OAAO,UAAUpL,IAAI,CAACC,KAAK,CAACmL,OAAO,GAAG,IAAI,CAAC,IAAI;EACjD;EAEAC,0BAA0BA,CAAA;IACxB,IAAI,CAAC7U,wBAAwB,GAAG,CAAC,IAAI,CAACA,wBAAwB;EAChE;EAEA8U,uBAAuBA,CAAClO,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5B,MAAMmO,cAAc,GAAG,IAAI,CAAC3iB,QAAQ,CAACwU,KAAK,CAAC;IAC3C,MAAMoO,eAAe,GAAG,IAAI,CAAC5iB,QAAQ,CAACwU,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACmO,cAAc,EAAE/O,SAAS,IAAI,CAACgP,eAAe,EAAEhP,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAMiP,WAAW,GAAG,IAAIrP,IAAI,CAACmP,cAAc,CAAC/O,SAAS,CAAC,CAACkP,YAAY,EAAE;IACrE,MAAMC,YAAY,GAAG,IAAIvP,IAAI,CAACoP,eAAe,CAAChP,SAAS,CAAC,CAACkP,YAAY,EAAE;IAEvE,OAAOD,WAAW,KAAKE,YAAY;EACrC;EAEAC,mBAAmBA,CAACpP,SAAc;IAChC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,MAAMgD,IAAI,GAAG,IAAIpD,IAAI,CAACI,SAAS,CAAC;IAChC,MAAMqP,KAAK,GAAG,IAAIzP,IAAI,EAAE;IACxB,MAAM0P,SAAS,GAAG,IAAI1P,IAAI,CAACyP,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIxM,IAAI,CAACkM,YAAY,EAAE,KAAKG,KAAK,CAACH,YAAY,EAAE,EAAE,OAAO,aAAa;IACtE,IAAIlM,IAAI,CAACkM,YAAY,EAAE,KAAKI,SAAS,CAACJ,YAAY,EAAE,EAAE,OAAO,MAAM;IAEnE,OAAOlM,IAAI,CAACyM,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAC,oBAAoBA,CAAC7O,OAAY;IAC/B,MAAMoJ,KAAK,GAAQ;MACjB0F,WAAW,EAAE,kBAAkB;MAC/BC,SAAS,EAAE,mBAAmB;MAC9BC,YAAY,EAAE,cAAc;MAC5BC,UAAU,EAAE,oBAAoB;MAChCC,eAAe,EAAE;KAClB;IACD,OAAO9F,KAAK,CAACpJ,OAAO,CAACmP,UAAU,CAAC,IAAI,oBAAoB;EAC1D;EAEAC,cAAcA,CAACpP,OAAY,EAAEZ,KAAU;IACrC,IAAI,IAAI,CAAClH,aAAa,EAAE;MACtB,IAAI,CAAC4M,sBAAsB,CAAC9E,OAAO,CAACzQ,EAAE,CAAC;;EAE3C;EAEA8f,oBAAoBA,CAACrP,OAAY,EAAEZ,KAAU;IAC3CA,KAAK,CAACyB,cAAc,EAAE;IACtB,IAAI,CAACuD,oBAAoB,CAACpE,OAAO,CAACzQ,EAAE,CAAC;EACvC;EAEA+f,cAAcA,CAACxP,SAAiB,EAAEyP,UAAmB;IACnD,IAAI,CAACnX,gBAAgB,GAAGmX,UAAU,GAAGzP,SAAS,GAAG,EAAE;EACrD;EAEA0P,gBAAgBA,CAAC7P,KAAa;IAC5B,IAAIA,KAAK,KAAK,IAAI,CAACxU,QAAQ,CAACiT,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IACnD,MAAM0P,cAAc,GAAG,IAAI,CAAC3iB,QAAQ,CAACwU,KAAK,CAAC;IAC3C,MAAM8P,WAAW,GAAG,IAAI,CAACtkB,QAAQ,CAACwU,KAAK,GAAG,CAAC,CAAC;IAC5C,OAAOmO,cAAc,EAAEjP,MAAM,EAAEtP,EAAE,KAAKkgB,WAAW,EAAE5Q,MAAM,EAAEtP,EAAE;EAC/D;EAEAmgB,aAAaA,CAACtQ,KAAU;IACtBA,KAAK,CAACE,MAAM,CAAC2H,GAAG,GAAG,kCAAkC;EACvD;EAEA0I,mBAAmBA,CAAA;IACjB,OAAQ,IAAI,CAACvkB,YAAoB,EAAE4T,IAAI,KAAK,OAAO;EACrD;EAEA4Q,oBAAoBA,CAACjQ,KAAa;IAChC,IAAI,CAAC,IAAI,CAACgQ,mBAAmB,EAAE,EAAE,OAAO,KAAK;IAC7C,IAAIhQ,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5B,MAAMmO,cAAc,GAAG,IAAI,CAAC3iB,QAAQ,CAACwU,KAAK,CAAC;IAC3C,MAAMoO,eAAe,GAAG,IAAI,CAAC5iB,QAAQ,CAACwU,KAAK,GAAG,CAAC,CAAC;IAChD,OAAOmO,cAAc,EAAEjP,MAAM,EAAEtP,EAAE,KAAKwe,eAAe,EAAElP,MAAM,EAAEtP,EAAE;EACnE;EAEAsgB,YAAYA,CAACvC,MAAc;IACzB,MAAMwC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAMnQ,KAAK,GAAG2N,MAAM,CAACyC,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAC1R,MAAM;IAClD,OAAO0R,MAAM,CAACnQ,KAAK,CAAC;EACtB;EAEA;EACA,IAAI4C,IAAIA,CAAA;IACN,OAAOA,IAAI;EACb;EAEA;EACAyN,qBAAqBA,CAAA;IACnB;EAAA;EAGFC,kBAAkBA,CAAA;IAChB;EAAA;EAGFC,mBAAmBA,CAAA;IACjB;EAAA;EAGF;EACAC,eAAeA,CAAA;IACb;EAAA;EAGFC,eAAeA,CAAA;IACb;EAAA;EAGFC,kBAAkBA,CAACrR,IAAY;IAC7B,MAAMoK,KAAK,GAAQ;MACjB3S,IAAI,EAAE,gBAAgB;MACtB6Z,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,mBAAmB;MAC1B9I,IAAI,EAAE,aAAa;MACnB+I,QAAQ,EAAE,uBAAuB;MACjCC,OAAO,EAAE;KACV;IACD,OAAOtH,KAAK,CAACpK,IAAI,CAAC,IAAI,gBAAgB;EACxC;EAEA;EACA2R,oBAAoBA,CAAA;IAClB;EAAA;EAGFC,yBAAyBA,CAACC,QAAgB;IACxC,IAAI,CAACrc,2BAA2B,GAAGqc,QAAQ;EAC7C;EAEAC,0BAA0BA,CAACD,QAAgB;IACzC,MAAMzH,KAAK,GAAQ;MACjB2H,GAAG,EAAE,cAAc;MACnBC,YAAY,EAAE,cAAc;MAC5BC,aAAa,EAAE,iBAAiB;MAChCC,SAAS,EAAE,kBAAkB;MAC7BC,QAAQ,EAAE;KACX;IACD,OAAO/H,KAAK,CAACyH,QAAQ,CAAC,IAAI,qBAAqB;EACjD;EAEAO,wBAAwBA,CAAA;IACtB,OAAO,CACL;MAAE7hB,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEqhB,QAAQ,EAAE,eAAe;MAAEphB,IAAI,EAAE;IAAc,CAAE,EACzE;MACEF,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdqhB,QAAQ,EAAE,cAAc;MACxBphB,IAAI,EAAE;KACP,EACD;MACEF,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,kBAAkB;MACxBqhB,QAAQ,EAAE,WAAW;MACrBphB,IAAI,EAAE;KACP,CACF;EACH;EAEA4hB,oBAAoBA,CAACC,WAAgB;IACnC;EAAA;EAGFC,iBAAiBA,CAACD,WAAgB;IAChC;EAAA;EAGFE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACJ,wBAAwB,EAAE,CAAC5N,MAAM,CAAE7I,CAAC,IAAKA,CAAC,CAACwP,MAAM,CAAC;EAChE;EAEAsH,wBAAwBA,CAACvW,MAAc;IACrC,MAAMwW,KAAK,GAAQ;MACjBvH,MAAM,EAAE,OAAO;MACfwH,QAAQ,EAAE,SAAS;MACnBlX,KAAK,EAAE,QAAQ;MACfmX,OAAO,EAAE;KACV;IACD,OAAOF,KAAK,CAACxW,MAAM,CAAC,IAAI,SAAS;EACnC;EAEA2W,kBAAkBA,CAACC,YAAiB;IAClC,OAAO,IAAI,CAACvE,gBAAgB,CAACuE,YAAY,CAAC;EAC5C;EAEAC,mBAAmBA,CAACT,WAAgB;IAClC;EAAA;EAGFU,eAAeA,CAACV,WAAgB;IAC9B;EAAA;EAGFW,eAAeA,CAACX,WAAgB;IAC9B;EAAA;EAGFY,iBAAiBA,CAACZ,WAAgB;IAChC;EAAA;EAGF;EACAa,WAAWA,CAACC,OAAY;IACtB;EAAA;EAGFC,WAAWA,CAACD,OAAY;IACtB;EAAA;EAGFE,aAAaA,CAACF,OAAY;IACxB;EAAA;EAGFG,aAAaA,CAAA;IACX;EAAA;EAGF;EACAC,cAAcA,CAAA;IACZ,OACE,IAAI,CAACha,eAAe,IACpB,IAAI,CAACxJ,gBAAgB,IACrB,IAAI,CAACsE,sBAAsB,IAC3B,IAAI,CAACgB,gBAAgB,IACrB,IAAI,CAAC5H,eAAe,IACpB,IAAI,CAACiE,UAAU,IACf,IAAI,CAACN,eAAe,IACpB,IAAI,CAACwC,eAAe;EAExB;EAEA4f,gBAAgBA,CAAA;IACd,IAAI,CAACja,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACxJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACsE,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACgB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC5H,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACiE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACN,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACwC,eAAe,GAAG,KAAK;EAC9B;EAEA;EAEA;EACA6f,SAASA,CAACC,SAAiB;IACzBxU,OAAO,CAACsE,GAAG,CAAC,aAAa,EAAEkQ,SAAS,CAAC;EACvC;EAEAC,gBAAgBA,CAACC,UAAkB;IACjC,IAAI,CAAC7gB,mBAAmB,GAAG6gB,UAAU;EACvC;EAEAC,sBAAsBA,CAAA;IACpB;EAAA;EAGFC,qBAAqBA,CAAA;IACnB,IAAI,CAAC9gB,gBAAgB,GAAG;MACtBC,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC;MACbC,GAAG,EAAE;KACN;EACH;EAEA2gB,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACzgB,cAAc,CAACkM,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACnM,iBAAiB,CAAC2M,IAAI,CAAC;QAC1B1P,EAAE,EAAEoP,IAAI,CAACC,GAAG,EAAE,CAAC/D,QAAQ,EAAE;QACzBuB,OAAO,EAAE,IAAI,CAAC7J,cAAc;QAC5Bf,CAAC,EAAE,EAAE;QACLC,CAAC,EAAE,EAAE;QACLwhB,UAAU,EAAE,IAAI,CAACzgB,cAAc;QAC/B0gB,QAAQ,EAAE,IAAI,CAACzgB,YAAY;QAC3BsI,KAAK,EAAE,IAAI,CAACrI;OACb,CAAC;MACF,IAAI,CAACH,cAAc,GAAG,EAAE;;EAE5B;EAEA;EACA4gB,eAAeA,CAAA;IACb,MAAMC,UAAU,GAAGC,MAAM,CAAC,yBAAyB,CAAC;IACpD,IAAID,UAAU,EAAE;MACd,IAAI,CAACrgB,WAAW,CAACkM,IAAI,CAAC;QACpB1P,EAAE,EAAEoP,IAAI,CAACC,GAAG,EAAE,CAAC/D,QAAQ,EAAE;QACzBrL,IAAI,EAAE4jB,UAAU;QAChBpU,IAAI,EAAE,QAAQ;QACdkG,IAAI,EAAE,CAAC;QACPoO,UAAU,EAAE,IAAI3U,IAAI;OACrB,CAAC;;EAEN;EAEA4U,WAAWA,CAAA;IACT,IAAI,CAAC5N,aAAa,EAAE;EACtB;EAEA6N,cAAcA,CAAA;IACZ,IAAI,CAAC1gB,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;EACpE;EAEA2gB,gBAAgBA,CAAA;IACd,IAAI,CAAC5gB,eAAe,GAAG,KAAK;EAC9B;EAEA6gB,YAAYA,CAACC,MAAW;IACtB,IAAI,CAAC3gB,cAAc,GAAG2gB,MAAM;EAC9B;EAEAC,YAAYA,CAACD,MAAW;IACtB,MAAME,OAAO,GAAGR,MAAM,CAAC,cAAc,EAAEM,MAAM,CAACnkB,IAAI,CAAC;IACnD,IAAIqkB,OAAO,EAAE;MACXF,MAAM,CAACnkB,IAAI,GAAGqkB,OAAO;;EAEzB;EAEAC,YAAYA,CAACH,MAAW;IACtB,IAAI5S,OAAO,CAAC,wBAAwB,CAAC,EAAE;MACrC,IAAI,CAAChO,WAAW,GAAG,IAAI,CAACA,WAAW,CAACyQ,MAAM,CAAEuQ,CAAC,IAAKA,CAAC,CAACxkB,EAAE,KAAKokB,MAAM,CAACpkB,EAAE,CAAC;;EAEzE;EAEAykB,yBAAyBA,CAAA;IACvB,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAC,cAAcA,CAAA;IACZ,OAAO,QAAQ;EACjB;EAEAC,eAAeA,CAAA;IACb,OAAO,OAAO;EAChB;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI;EACb;EAEAC,gBAAgBA,CAACC,KAAU;IACzB,IAAI,CAACrhB,cAAc,GAAGqhB,KAAK;EAC7B;EAEAC,YAAYA,CAAA;IACV;EAAA;EAGFC,gBAAgBA,CAAA;IACd;EAAA;EAGFC,SAASA,CAAA;IACP;EAAA;EAGFC,gBAAgBA,CAAA;IACd,OAAO,CACL;MACEllB,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,cAAc;MACpBwP,IAAI,EAAE,MAAM;MACZkG,IAAI,EAAE,OAAO;MACboO,UAAU,EAAE,IAAI3U,IAAI;KACrB,EACD;MACEpP,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,WAAW;MACjBwP,IAAI,EAAE,OAAO;MACbkG,IAAI,EAAE,OAAO;MACboO,UAAU,EAAE,IAAI3U,IAAI;KACrB,CACF;EACH;EAEA+V,mBAAmBA,CAAChN,IAAS;IAC3B,MAAM/H,KAAK,GAAG,IAAI,CAACtM,aAAa,CAACshB,OAAO,CAACjN,IAAI,CAACnY,EAAE,CAAC;IACjD,IAAIoQ,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACtM,aAAa,CAACuM,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;KACpC,MAAM;MACL,IAAI,CAACtM,aAAa,CAAC4L,IAAI,CAACyI,IAAI,CAACnY,EAAE,CAAC;;EAEpC;EAEAqlB,QAAQA,CAAClN,IAAS;IAChBvJ,OAAO,CAACsE,GAAG,CAAC,iBAAiB,EAAEiF,IAAI,CAAClY,IAAI,CAAC;EAC3C;EAEAqlB,cAAcA,CAAC9S,IAAS;IACtB,OAAO,IAAIpD,IAAI,CAACoD,IAAI,CAAC,CAACyM,kBAAkB,CAAC,OAAO,CAAC;EACnD;EAEAsG,SAASA,CAACpN,IAAS;IACjBvJ,OAAO,CAACsE,GAAG,CAAC,mBAAmB,EAAEiF,IAAI,CAAClY,IAAI,CAAC;EAC7C;EAEAulB,UAAUA,CAACrN,IAAS;IAClB,IAAI3G,OAAO,CAAC,wBAAwB,CAAC,EAAE;MACrC5C,OAAO,CAACsE,GAAG,CAAC,mBAAmB,EAAEiF,IAAI,CAAClY,IAAI,CAAC;;EAE/C;EAEA;EACA,IAAIwlB,SAASA,CAAA;IACX,OAAO,KAAK,CAAC,CAAC;EAChB;EAEA;EACAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACrY,gBAAgB;EAC9B;EAEA;EACAsY,YAAYA,CAACze,IAAY,EAAE0e,SAAiB;IAC1C,OAAO1e,IAAI,CAAC2H,MAAM,GAAG+W,SAAS,GAC1B1e,IAAI,CAAC2e,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK,GACpC1e,IAAI;EACV;EAEA4e,QAAQA,CAACrV,OAAY;IACnB,OACEA,OAAO,CAAChB,IAAI,KAAK,OAAO,IACvBgB,OAAO,CAACqI,WAAW,IAClBrI,OAAO,CAACqI,WAAW,CAACC,IAAI,CAAEgN,CAAM,IAAKA,CAAC,CAACtW,IAAI,CAAC2I,UAAU,CAAC,QAAQ,CAAC,CAAE;EAExE;EAEA4N,cAAcA,CAACvV,OAAY;IACzB,OAAOA,OAAO,CAAChB,IAAI,KAAK,OAAO,IAAIgB,OAAO,CAAChB,IAAI,KAAK,OAAO;EAC7D;EAEAwW,OAAOA,CAACxV,OAAY;IAClB,OACEA,OAAO,CAAChB,IAAI,KAAK,MAAM,IACtBgB,OAAO,CAACqI,WAAW,IAAIrI,OAAO,CAACqI,WAAW,CAACjK,MAAM,GAAG,CAAE;EAE3D;EAEAqX,WAAWA,CAACzV,OAAY;IACtB,IAAIA,OAAO,CAACqI,WAAW,IAAIrI,OAAO,CAACqI,WAAW,CAACjK,MAAM,GAAG,CAAC,EAAE;MACzD,OAAO4B,OAAO,CAACqI,WAAW,CAAC,CAAC,CAAC,CAAC7Y,IAAI,IAAI,SAAS;;IAEjD,OAAO,SAAS;EAClB;EAEAkmB,qBAAqBA,CAAC1V,OAAY;IAChC,MAAM2V,KAAK,GAAG3V,OAAO,CAACnB,MAAM,EAAEtP,EAAE,KAAK,IAAI,CAACjE,aAAa;IACvD,OAAO,2BAA2BqqB,KAAK,GAAG,KAAK,GAAG,OAAO,EAAE;EAC7D;EAEAC,iBAAiBA,CAAC5V,OAAY;IAC5B,OAAOA,OAAO,CAAChB,IAAI,KAAK,UAAU;EACpC;EAEA6W,gBAAgBA,CAAC7V,OAAY;IAC3B,OAAOA,OAAO,CAAChB,IAAI,KAAK,SAAS;EACnC;EAEA;EACA8W,WAAWA,CAAA;IACT,IAAI,CAACnlB,UAAU,GAAG,KAAK;EACzB;EAEAolB,aAAaA,CAACC,IAAY;IACxB,IAAI,CAACplB,UAAU,GAAGolB,IAAI;EACxB;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACplB,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAqlB,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT,IAAI,CAACxQ,aAAa,EAAE;EACtB;EAEAyQ,YAAYA,CAAA;IACV;EAAA;EAGFC,mBAAmBA,CAAA;IACjB,IAAI,CAACllB,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,sBAAsB,GAAG,CAAC;EACjC;EAEAklB,kBAAkBA,CAAA;IAChB,IAAI,CAACnlB,gBAAgB,GAAG,KAAK;EAC/B;EAEAolB,gBAAgBA,CAAA;IACd,IAAI,CAACzlB,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA0lB,mBAAmBA,CAACxN,QAAgB;IAClC,MAAM2E,OAAO,GAAGpL,IAAI,CAACC,KAAK,CAACwG,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMY,OAAO,GAAGZ,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAG2E,OAAO,CAAC9S,QAAQ,EAAE,CAACkP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,OAAO,CACrD/O,QAAQ,EAAE,CACVkP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACvB;EAEA;EACA0M,gBAAgBA,CAAA;IACd,IAAI,CAACplB,eAAe,GAAG,KAAK;EAC9B;EAEAqlB,eAAeA,CAAA;IACb;EAAA;EAGFC,eAAeA,CAACC,MAAc,EAAExX,KAAU;IACxC;EAAA;EAGFyX,eAAeA,CAAClX,KAAa;IAC3B;EAAA;EAGFmX,kBAAkBA,CAACd,IAAY;IAC7B,IAAI,CAAC1kB,eAAe,GAAG0kB,IAAI;EAC7B;EAEAe,YAAYA,CAACC,KAAa;IACxB,IAAI,CAACplB,SAAS,GAAGolB,KAAK;EACxB;EAEAC,WAAWA,CAACC,KAAa;IACvB;EAAA;EAGF;EACQzZ,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACrS,YAAY,GAAG;MAClBmE,EAAE,EAAE,mBAAmB;MACvByO,YAAY,EAAE,CACZ;QAAEzO,EAAE,EAAE,OAAO;QAAEuP,QAAQ,EAAE;MAAO,CAAE,EAClC;QAAEvP,EAAE,EAAE,OAAO;QAAEuP,QAAQ,EAAE;MAAK,CAAE;KAEnB;IAEjB;IACA,IAAI,CAAC3T,QAAQ,GAAG,CACd;MACEoE,EAAE,EAAE,GAAG;MACP6M,OAAO,EAAE,yBAAyB;MAClCyC,MAAM,EAAE;QAAEtP,EAAE,EAAE,OAAO;QAAEuP,QAAQ,EAAE;MAAK,CAAE;MACxCC,SAAS,EAAE,IAAIJ,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzChB,cAAc,EAAE,mBAAmB;MACnCoB,IAAI,EAAE;KACP,EACD;MACEzP,EAAE,EAAE,GAAG;MACP6M,OAAO,EAAE,8BAA8B;MACvCyC,MAAM,EAAE;QAAEtP,EAAE,EAAE,IAAI,CAACjE,aAAc;QAAEwT,QAAQ,EAAE,IAAI,CAACvT;MAAe,CAAE;MACnEwT,SAAS,EAAE,IAAIJ,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzChB,cAAc,EAAE,mBAAmB;MACnCoB,IAAI,EAAE;KACP,EACD;MACEzP,EAAE,EAAE,GAAG;MACP6M,OAAO,EAAE,yCAAyC;MAClDyC,MAAM,EAAE;QAAEtP,EAAE,EAAE,OAAO;QAAEuP,QAAQ,EAAE;MAAK,CAAE;MACxCC,SAAS,EAAE,IAAIJ,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzChB,cAAc,EAAE,mBAAmB;MACnCoB,IAAI,EAAE;KACP,CACF;IAED,IAAI,CAACxT,gBAAgB,GAAG;MAAE+D,EAAE,EAAE,OAAO;MAAEuP,QAAQ,EAAE;IAAK,CAAU;IAChE,IAAI,CAACzT,OAAO,GAAG,KAAK;EACtB;EAEA;EAEA;EACA8rB,SAASA,CAACC,GAAQ;IAChB;IACA,MAAMhb,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACsJ,WAAW,CAACmb,UAAU,CAAC;MAAEjb,OAAO,EAAEA,OAAO,GAAG,QAAQgb,GAAG,CAAC7nB,EAAE;IAAG,CAAE,CAAC;EACvE;EAEA+nB,kBAAkBA,CAAA;IAChB;EAAA;EAGFC,iBAAiBA,CAAA;IACf,IAAI,CAAC7qB,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA;EACA8qB,eAAeA,CAAA;IACb,IAAI,CAACnnB,eAAe,GAAG,IAAI;EAC7B;EAEAonB,gBAAgBA,CAAA;IACd,IAAI,CAACpnB,eAAe,GAAG,KAAK;EAC9B;EAEAqnB,iBAAiBA,CAACC,IAAY;IAC5B,IAAI,CAACrnB,mBAAmB,GAAGqnB,IAAI;EACjC;EAEAC,kBAAkBA,CAAC7c,KAAa;IAC9B,IAAI,CAACvK,oBAAoB,GAAGuK,KAAK;EACnC;EAEA8c,WAAWA,CAAA;IACT;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,YAAYA,CAAA;IACV;EAAA;EAGFC,YAAYA,CAAC5Y,KAAU;IACrB;EAAA;EAGF6Y,IAAIA,CAAC7Y,KAAU;IACb;EAAA;EAGF8Y,WAAWA,CAAA;IACT;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGF;EACAC,cAAcA,CAACC,QAAa,EAAE/K,MAAqB;IACjD,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK;IACzB,OAAO+K,QAAQ,CAAC/K,MAAM,KAAKA,MAAM;EACnC;EAEAgL,kBAAkBA,CAACD,QAAa;IAC9B,OAAO,GAAGA,QAAQ,CAAClQ,IAAI,EAAErJ,QAAQ,IAAI,aAAa,iBAChDuZ,QAAQ,CAACtY,KACX,EAAE;EACJ;EAEA;EACAwY,gBAAgBA,CAACvY,OAAY;IAC3B;IACA,OAAOA,OAAO,CAACnB,MAAM,EAAEtP,EAAE,KAAK,IAAI,CAACjE,aAAa,IAAI,IAAI,CAACG,OAAO;EAClE;EAEA+sB,cAAcA,CAACxY,OAAY;IACzB;IACA,OAAOA,OAAO,CAACnB,MAAM,EAAEtP,EAAE,KAAK,IAAI,CAACjE,aAAa;EAClD;EAEA;EACAmtB,iBAAiBA,CAAA;IACf,IAAI,CAACvgB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,CAACugB,KAAK,EAAE;EAC/B;EAEAC,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACxgB,gBAAgB,CAAC+M,IAAI,KAAK,CAAC,EAAE;IAEtC,IAAInE,OAAO,CAAC,aAAa,IAAI,CAAC5I,gBAAgB,CAAC+M,IAAI,eAAe,CAAC,EAAE;MACnE;MACA,IAAI,CAAC/Z,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACqY,MAAM,CACjCvD,CAAC,IAAK,CAACA,CAAC,CAAC1Q,EAAE,IAAI,CAAC,IAAI,CAAC4I,gBAAgB,CAAC4M,GAAG,CAAC9E,CAAC,CAAC1Q,EAAE,CAAC,CACjD;MACD,IAAI,CAACkpB,iBAAiB,EAAE;MACxB,IAAI,CAAC1tB,YAAY,CAAC6Z,WAAW,CAAC,oBAAoB,CAAC;;EAEvD;EAEAgU,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAACzgB,gBAAgB,CAAC+M,IAAI,KAAK,CAAC,EAAE;IAEtC;IACA,IAAI,CAAClW,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,iBAAiB,GAAGsQ,KAAK,CAACC,IAAI,CAAC,IAAI,CAACrH,gBAAgB,CAAC;EAC5D;EAEA0gB,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC1gB,gBAAgB,CAAC+M,IAAI,KAAK,CAAC,EAAE;IAEtC,MAAM4T,qBAAqB,GAAG,IAAI,CAAC3tB,QAAQ,CAACqY,MAAM,CAC/CvD,CAAC,IAAKA,CAAC,CAAC1Q,EAAE,IAAI,IAAI,CAAC4I,gBAAgB,CAAC4M,GAAG,CAAC9E,CAAC,CAAC1Q,EAAE,CAAC,CAC/C;IACD,MAAMwpB,UAAU,GAAGD,qBAAqB,CACrCpS,GAAG,CAAEzG,CAAC,IAAK,GAAGA,CAAC,CAACpB,MAAM,EAAEC,QAAQ,KAAKmB,CAAC,CAAC7D,OAAO,EAAE,CAAC,CACjD4c,IAAI,CAAC,IAAI,CAAC;IAEbvU,SAAS,CAACC,SAAS,CAChBC,SAAS,CAACoU,UAAU,CAAC,CACrBE,IAAI,CAAC,MAAK;MACT,IAAI,CAACluB,YAAY,CAAC6Z,WAAW,CAAC,iBAAiB,CAAC;MAChD,IAAI,CAAC6T,iBAAiB,EAAE;IAC1B,CAAC,CAAC,CACDS,KAAK,CAAC,MAAK;MACV,IAAI,CAACnuB,YAAY,CAACuT,SAAS,CAAC,yBAAyB,CAAC;IACxD,CAAC,CAAC;EACN;EAEA;EACA6a,WAAWA,CAAA;IACT,IAAI,CAAChrB,iBAAiB,GAAG,IAAI;EAC/B;EAEA;EACAirB,kBAAkBA,CAAA;IAChB,IAAI,CAAC1tB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAACia,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACjE,aAAa,CAAC/O,KAAK,GAAG,EAAE;;EAE3C;EAEA;EACAymB,uBAAuBA,CAACrQ,QAAgB;IACtC,MAAM2E,OAAO,GAAGpL,IAAI,CAACC,KAAK,CAACwG,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMY,OAAO,GAAGZ,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAG2E,OAAO,IAAI/D,OAAO,CAAC/O,QAAQ,EAAE,CAACkP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEAuP,oBAAoBA,CAAA;IAClB,IAAI,CAACxtB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACuN,mBAAmB,GAAG,MAAM;EACnC;EAEA;EACAigB,aAAaA,CAAA;IACX,OACE,IAAI,CAAChjB,YAAY,CAACkI,IAAI,EAAE,CAACL,MAAM,GAAG,CAAC,IACnC,IAAI,CAAC5H,WAAW,CAACgN,MAAM,CAAEgW,GAAG,IAAKA,GAAG,CAAC/iB,IAAI,CAACgI,IAAI,EAAE,CAACL,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,IAAI,CAAC;EAE5E;EAEA;EACAqb,mBAAmBA,CAAA;IACjB,IAAI,CAACxiB,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC9B;EAEAqiB,kBAAkBA,CAAA;IAChB,IAAIjV,SAAS,CAACkV,WAAW,EAAE;MACzBlV,SAAS,CAACkV,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAI;QACX,IAAI,CAACxiB,gBAAgB,GAAG;UACtByiB,QAAQ,EAAED,QAAQ,CAACE,MAAM,CAACD,QAAQ;UAClCE,SAAS,EAAEH,QAAQ,CAACE,MAAM,CAACC,SAAS;UACpCxqB,IAAI,EAAE;SACP;MACH,CAAC,EACAiL,KAAK,IAAI;QACR,IAAI,CAAC1P,YAAY,CAACuT,SAAS,CAAC,qCAAqC,CAAC;MACpE,CAAC,CACF;KACF,MAAM;MACL,IAAI,CAACvT,YAAY,CAACuT,SAAS,CAAC,+BAA+B,CAAC;;EAEhE;EAEA2b,cAAcA,CAAA;IACZ,IAAI,CAAC/iB,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,CAACE,qBAAqB,GAAG,CAC3B;MAAE5H,IAAI,EAAE,eAAe;MAAEsqB,QAAQ,EAAE,OAAO;MAAEE,SAAS,EAAE;IAAM,CAAE,EAC/D;MAAExqB,IAAI,EAAE,cAAc;MAAEsqB,QAAQ,EAAE,MAAM;MAAEE,SAAS,EAAE;IAAM,CAAE,EAC7D;MAAExqB,IAAI,EAAE,mBAAmB;MAAEsqB,QAAQ,EAAE,OAAO;MAAEE,SAAS,EAAE;IAAM,CAAE,CACpE;EACH;EAEAE,gBAAgBA,CAAA;IACd;IACA,IAAI,IAAI,CAAC/iB,mBAAmB,CAACsH,IAAI,EAAE,EAAE;MACnC,IAAI,CAACrH,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACoM,MAAM,CAC3DiN,QAAQ,IACPA,QAAQ,CAACjhB,IAAI,CACViU,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAACvM,mBAAmB,CAACsM,WAAW,EAAE,CAAC,CACtD;;EAEL;EAEA0W,oBAAoBA,CAACnS,MAAW;IAC9B,IAAI,CAAC3Q,gBAAgB,GAAG2Q,MAAM;IAC9B,IAAI,CAAC9Q,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,mBAAmB,GAAG,EAAE;EAC/B;EAEAijB,mBAAmBA,CAAA;IACjB,IAAI,CAAC9iB,mBAAmB,GAAG,IAAI;EACjC;EAEA+iB,aAAaA,CAAA;IACX,IAAI,IAAI,CAAChjB,gBAAgB,EAAE;MACzB;MACA,MAAME,eAAe,GAAG;QACtBhI,EAAE,EAAEoP,IAAI,CAACC,GAAG,EAAE,CAAC/D,QAAQ,EAAE;QACzBmE,IAAI,EAAE,UAAU;QAChByR,QAAQ,EAAE,IAAI,CAACpZ,gBAAgB;QAC/BE,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCsH,MAAM,EAAE;UAAEtP,EAAE,EAAE,IAAI,CAACjE,aAAc;UAAEwT,QAAQ,EAAE,IAAI,CAACvT;QAAe,CAAE;QACnEwT,SAAS,EAAE,IAAIJ,IAAI,EAAE;QACrBf,cAAc,EAAE,IAAI,CAACxS,YAAY,EAAEmE;OACpC;MAED,IAAI,CAACpE,QAAQ,CAAC8T,IAAI,CAAC1H,eAAsB,CAAC;MAC1C,IAAI,CAACkiB,mBAAmB,EAAE;MAC1B,IAAI,CAACliB,eAAe,GAAG,EAAE;MACzB,IAAI,CAACxM,YAAY,CAAC6Z,WAAW,CAAC,uBAAuB,CAAC;;EAE1D;EAEA;EACA0V,kBAAkBA,CAAA;IAChB,IAAI,CAAC9iB,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,yBAAyB,GAAG,IAAI;EACvC;EAEA6iB,eAAeA,CAAA;IACb;IACA;EAAA;EAGFC,6BAA6BA,CAAA;IAC3B;IACA,MAAMC,QAAQ,GAAG,CACf;MACElrB,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,cAAc;MACpBkrB,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE;KACR,EACD;MACEprB,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,YAAY;MAClBkrB,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE;KACR,EACD;MACEprB,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,eAAe;MACrBkrB,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE;KACR,CACF;IAED,IAAI,IAAI,CAACljB,kBAAkB,CAACgH,IAAI,EAAE,EAAE;MAClC,OAAOgc,QAAQ,CAACjX,MAAM,CACnBkN,OAAO,IACNA,OAAO,CAAClhB,IAAI,CACTiU,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAACjM,kBAAkB,CAACgM,WAAW,EAAE,CAAC,IAClDiN,OAAO,CAACgK,KAAK,CAAChX,QAAQ,CAAC,IAAI,CAACjM,kBAAkB,CAAC,IAC/CiZ,OAAO,CAACiK,KAAK,CACVlX,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAACjM,kBAAkB,CAACgM,WAAW,EAAE,CAAC,CACrD;;IAEH,OAAOgX,QAAQ;EACjB;EAEAG,uBAAuBA,CAAClK,OAAY;IAClC,IAAI,CAAChZ,yBAAyB,GAAGgZ,OAAO;EAC1C;EAEAmK,kBAAkBA,CAACnK,OAAY;IAC7B;IACA,IAAI,CAAC3lB,YAAY,CAAC+vB,QAAQ,CAAC,cAAcpK,OAAO,CAAClhB,IAAI,EAAE,CAAC;EAC1D;EAEAurB,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAChwB,YAAY,CAAC+vB,QAAQ,CAAC,+BAA+B,CAAC;EAC7D;EAEAE,YAAYA,CAAA;IACV,IAAI,IAAI,CAACtjB,yBAAyB,EAAE;MAClC;MACA,MAAMujB,cAAc,GAAG;QACrB1rB,EAAE,EAAEoP,IAAI,CAACC,GAAG,EAAE,CAAC/D,QAAQ,EAAE;QACzBmE,IAAI,EAAE,SAAS;QACf0R,OAAO,EAAE,IAAI,CAAChZ,yBAAyB;QACvCmH,MAAM,EAAE;UAAEtP,EAAE,EAAE,IAAI,CAACjE,aAAc;UAAEwT,QAAQ,EAAE,IAAI,CAACvT;QAAe,CAAE;QACnEwT,SAAS,EAAE,IAAIJ,IAAI,EAAE;QACrBf,cAAc,EAAE,IAAI,CAACxS,YAAY,EAAEmE;OACpC;MAED,IAAI,CAACpE,QAAQ,CAAC8T,IAAI,CAACgc,cAAqB,CAAC;MACzC,IAAI,CAACX,kBAAkB,EAAE;MACzB,IAAI,CAACvvB,YAAY,CAAC6Z,WAAW,CAAC,iBAAiB,CAAC;;EAEpD;EAEA;EACAsW,iBAAiBA,CAAA;IACf,IAAI,CAACprB,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACpD,eAAe,GAAG,KAAK;EAC9B;EAEAyuB,aAAaA,CAAA;IACX,IAAI,CAAClrB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACvD,eAAe,GAAG,KAAK;EAC9B;EAEA0uB,aAAaA,CAAA;IACX;IACA;EAAA;EAGFC,WAAWA,CAACtb,KAAa;IACvB,MAAMyF,cAAc,GAAG,IAAI,CAACtJ,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IACnE,IAAI,CAACsJ,WAAW,CAACmb,UAAU,CAAC;MAC1Bjb,OAAO,EAAEoJ,cAAc,GAAGzF;KAC3B,CAAC;EACJ;EAEAub,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAAChsB,eAAe,CAACkU,MAAM,CAAEqN,QAAQ,IAAI;MAC9C,IAAI,CAAC,IAAI,CAAClhB,gBAAgB,CAAC8O,IAAI,EAAE,EAAE,OAAO,IAAI;MAC9C,OAAOoS,QAAQ,CAACrhB,IAAI,CACjBiU,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAAC/T,gBAAgB,CAAC8T,WAAW,EAAE,CAAC;IAClD,CAAC,CAAC;EACJ;EAEA8X,YAAYA,CAACxb,KAAU;IACrB,IAAI,CAAClQ,cAAc,GAAGkQ,KAAK;EAC7B;EAEAyb,iBAAiBA,CAACC,MAAc;IAC9B,IAAI,CAACzrB,mBAAmB,GAAGyrB,MAAM;EACnC;EAEAC,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAC3rB,YAAY,CAACkO,IAAI,CAC1B0d,IAAI,IAAKA,IAAI,CAACpsB,EAAE,KAAK,IAAI,CAACS,mBAAmB,CAC/C;EACH;EAEA4rB,aAAaA,CAACC,OAAY;IACxB;IACA,MAAMC,cAAc,GAAG;MACrBvsB,EAAE,EAAEoP,IAAI,CAACC,GAAG,EAAE,CAAC/D,QAAQ,EAAE;MACzBmE,IAAI,EAAE,SAAS;MACf6c,OAAO,EAAEA,OAAO;MAChBhd,MAAM,EAAE;QAAEtP,EAAE,EAAE,IAAI,CAACjE,aAAc;QAAEwT,QAAQ,EAAE,IAAI,CAACvT;MAAe,CAAE;MACnEwT,SAAS,EAAE,IAAIJ,IAAI,EAAE;MACrBf,cAAc,EAAE,IAAI,CAACxS,YAAY,EAAEmE;KACpC;IAED,IAAI,CAACpE,QAAQ,CAAC8T,IAAI,CAAC6c,cAAqB,CAAC;IACzC,IAAI,CAAChsB,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC/E,YAAY,CAAC6Z,WAAW,CAAC,gBAAgB,CAAC;EACjD;EAEAmX,WAAWA,CAAA;IACT;IACA;EAAA;EAGFC,iBAAiBA,CAACnL,QAAgB;IAChC,IAAI,CAACzgB,mBAAmB,GAAGygB,QAAQ;EACrC;EAEAoL,eAAeA,CAAA;IACb;IACA,OAAO,CACL;MAAE1sB,EAAE,EAAE,GAAG;MAAEmZ,GAAG,EAAE,UAAU;MAAEmE,KAAK,EAAE;IAAO,CAAE,EAC5C;MAAEtd,EAAE,EAAE,GAAG;MAAEmZ,GAAG,EAAE,UAAU;MAAEmE,KAAK,EAAE;IAAO,CAAE,EAC5C;MAAEtd,EAAE,EAAE,GAAG;MAAEmZ,GAAG,EAAE,UAAU;MAAEmE,KAAK,EAAE;IAAM,CAAE,CAC5C;EACH;EAOAqP,YAAYA,CAAA;IACV,IAAI,CAACluB,sBAAsB,GAAG,KAAK;EACrC;EAEAmuB,aAAaA,CAAA;IACX;IACA,IAAI,CAACnuB,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACjD,YAAY,CAAC6Z,WAAW,CAAC,kBAAkB,CAAC;EACnD;EAEAwX,iBAAiBA,CAAA;IACf,IAAI,CAACptB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,qBAAqB,GAAG,EAAE;EACjC;EAIAmtB,mBAAmBA,CAAA;IACjB,MAAM5B,QAAQ,GAAG,CACf;MAAElrB,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAE8sB,MAAM,EAAE;IAAa,CAAE,EACxD;MAAE/sB,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAE8sB,MAAM,EAAE;IAAa,CAAE,EACtD;MAAE/sB,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,eAAe;MAAE8sB,MAAM,EAAE;IAAa,CAAE,CAC1D;IAED,IAAI,IAAI,CAACtgB,kBAAkB,CAACyC,IAAI,EAAE,EAAE;MAClC,OAAOgc,QAAQ,CAACjX,MAAM,CAAEkN,OAAO,IAC7BA,OAAO,CAAClhB,IAAI,CACTiU,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAAC1H,kBAAkB,CAACyH,WAAW,EAAE,CAAC,CACnD;;IAEH,OAAOgX,QAAQ;EACjB;EAEA8B,sBAAsBA,CAACC,SAAiB;IACtC,MAAM7c,KAAK,GAAG,IAAI,CAACzQ,qBAAqB,CAACylB,OAAO,CAAC6H,SAAS,CAAC;IAC3D,IAAI7c,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACzQ,qBAAqB,CAAC0Q,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;KAC5C,MAAM;MACL,IAAI,CAACzQ,qBAAqB,CAAC+P,IAAI,CAACud,SAAS,CAAC;;EAE9C;EAEAC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACvtB,qBAAqB,CAACkP,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACrT,YAAY,CAAC6Z,WAAW,CAC3B,uBAAuB,IAAI,CAAC1V,qBAAqB,CAACkP,MAAM,aAAa,CACtE;MACD,IAAI,CAACge,iBAAiB,EAAE;;EAE5B;EAEA;EACAM,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjkB,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;;EAE5B;EAEAkkB,SAASA,CAAA;IACP,IAAI,IAAI,CAAClkB,iBAAiB,GAAG,IAAI,CAACC,YAAY,CAAC0F,MAAM,GAAG,CAAC,EAAE;MACzD,IAAI,CAAC3F,iBAAiB,EAAE;;EAE5B;EAEA;EACAmkB,YAAYA,CAAA;IACV,MAAMxT,KAAK,GAAG;MACZyT,OAAO,EAAE,qBAAqB;MAC9BpiB,KAAK,EAAE,2BAA2B;MAClCqiB,OAAO,EAAE,6BAA6B;MACtCC,IAAI,EAAE;KACP;IACD,OAAO3T,KAAK,CAAC,IAAI,CAACvQ,SAA+B,CAAC,IAAI,oBAAoB;EAC5E;EAEA;EACAmkB,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC7xB,QAAQ,CAACiT,MAAM;EAC7B;EAEA6e,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC9xB,QAAQ,CAACqY,MAAM,CAAEvD,CAAC,IAC5BA,CAAC,CAACoI,WAAW,EAAEC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACvJ,IAAI,KAAK,OAAO,CAAC,CACnD,CAACZ,MAAM;EACV;EAEA8e,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC/xB,QAAQ,CAACqY,MAAM,CAAEvD,CAAC,IAC5BA,CAAC,CAACoI,WAAW,EAAEC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACvJ,IAAI,KAAK,MAAM,CAAC,CAClD,CAACZ,MAAM;EACV;EAEA;EACA+e,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACpyB,YAAY,CAAC6Z,WAAW,CAAC,uBAAuB,CAAC;EACxD;EAEA;EACAwY,qBAAqBA,CAAA;IACnB,IAAI,CAACtnB,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACG,0BAA0B,GAAG,EAAE;IACpC,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B;EAEAmnB,aAAaA,CAAA;IACX,MAAMC,IAAI,GAAG,IAAI,CAACvnB,eAAe;IACjC,IAAI,CAACA,eAAe,GAAG,IAAI,CAACC,aAAa;IACzC,IAAI,CAACA,aAAa,GAAGsnB,IAAI;EAC3B;EAEAC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACrnB,cAAc,EAAE;MACvBuO,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAACzO,cAAc,CAAC,CAAC+iB,IAAI,CAAC,MAAK;QAC3D,IAAI,CAACluB,YAAY,CAAC6Z,WAAW,CAAC,mBAAmB,CAAC;MACpD,CAAC,CAAC;;EAEN;EAEA4Y,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACtnB,cAAc,EAAE;MACvB,MAAMsP,cAAc,GAAG,IAAI,CAACtJ,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;MACnE,IAAI,CAACsJ,WAAW,CAACmb,UAAU,CAAC;QAC1Bjb,OAAO,EAAEoJ,cAAc,GAAG,IAAI,CAACtP;OAChC,CAAC;MACF,IAAI,CAACknB,qBAAqB,EAAE;;EAEhC;EAEAK,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACvnB,cAAc,EAAE;MACvB;MACA,MAAMwnB,kBAAkB,GAAG;QACzBnuB,EAAE,EAAEoP,IAAI,CAACC,GAAG,EAAE,CAAC/D,QAAQ,EAAE;QACzBuB,OAAO,EAAE,IAAI,CAAClG,cAAc;QAC5B2I,MAAM,EAAE;UAAEtP,EAAE,EAAE,IAAI,CAACjE,aAAc;UAAEwT,QAAQ,EAAE,IAAI,CAACvT;QAAe,CAAE;QACnEwT,SAAS,EAAE,IAAIJ,IAAI,EAAE;QACrBf,cAAc,EAAE,IAAI,CAACxS,YAAY,EAAEmE;OACpC;MAED,IAAI,CAACpE,QAAQ,CAAC8T,IAAI,CAACye,kBAAyB,CAAC;MAC7C,IAAI,CAACN,qBAAqB,EAAE;MAC5B,IAAI,CAACryB,YAAY,CAAC6Z,WAAW,CAAC,qBAAqB,CAAC;;EAExD;EAEA;EACA+Y,gBAAgBA,CAAA;IACd,IAAI,CAACrnB,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,EAAE;MAAEA,IAAI,EAAE;IAAE,CAAE,CAAC;IAC/C,IAAI,CAACC,YAAY,GAAG;MAClBC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE;KACb;EACH;EAEA8mB,aAAaA,CAAA;IACX,IAAI,IAAI,CAACpnB,WAAW,CAAC4H,MAAM,GAAG,EAAE,EAAE;MAChC,IAAI,CAAC5H,WAAW,CAACyI,IAAI,CAAC;QAAExI,IAAI,EAAE;MAAE,CAAE,CAAC;;EAEvC;EAEAonB,gBAAgBA,CAACle,KAAa;IAC5B,IAAI,IAAI,CAACnJ,WAAW,CAAC4H,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAAC5H,WAAW,CAACoJ,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;;EAErC;EAEAme,UAAUA,CAAA;IACR,IAAI,IAAI,CAACvE,aAAa,EAAE,EAAE;MACxB;MACA,MAAMwE,WAAW,GAAG;QAClBxuB,EAAE,EAAEoP,IAAI,CAACC,GAAG,EAAE,CAAC/D,QAAQ,EAAE;QACzBmE,IAAI,EAAE,MAAM;QACZgf,IAAI,EAAE;UACJC,QAAQ,EAAE,IAAI,CAAC1nB,YAAY;UAC3B2nB,OAAO,EAAE,IAAI,CAAC1nB,WAAW,CAACgN,MAAM,CAAEgW,GAAG,IAAKA,GAAG,CAAC/iB,IAAI,CAACgI,IAAI,EAAE,CAAC;UAC1D0f,QAAQ,EAAE,IAAI,CAACznB,YAAY;UAC3B0nB,KAAK,EAAE,EAAE;UACTC,UAAU,EAAE;SACb;QACDxf,MAAM,EAAE;UAAEtP,EAAE,EAAE,IAAI,CAACjE,aAAc;UAAEwT,QAAQ,EAAE,IAAI,CAACvT;QAAe,CAAE;QACnEwT,SAAS,EAAE,IAAIJ,IAAI,EAAE;QACrBf,cAAc,EAAE,IAAI,CAACxS,YAAY,EAAEmE;OACpC;MAED,IAAI,CAACpE,QAAQ,CAAC8T,IAAI,CAAC8e,WAAkB,CAAC;MACtC,IAAI,CAACJ,gBAAgB,EAAE;MACvB,IAAI,CAAC5yB,YAAY,CAAC6Z,WAAW,CAAC,cAAc,CAAC;;EAEjD;EAEA;EACA0Z,iBAAiBA,CAAA;IACf,MAAMliB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK;IACtD,OAAOwJ,OAAO,IAAIA,OAAO,CAACqC,IAAI,EAAE,CAACL,MAAM,GAAG,CAAC;EAC7C;EAEAmgB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACD,iBAAiB,EAAE,IAAI,IAAI,CAAC5yB,YAAY,KAAK,IAAI;EAC/D;EAEA8yB,YAAYA,CAAA;IACV;EAAA;EAGFC,iBAAiBA,CAAA;IACf;IACA,MAAMC,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3E,IAAI,IAAI,CAAC/uB,gBAAgB,CAAC8O,IAAI,EAAE,EAAE;MAChC,OAAOigB,MAAM,CAAClb,MAAM,CAAEzD,KAAK,IAAKA,KAAK,CAAC2D,QAAQ,CAAC,IAAI,CAAC/T,gBAAgB,CAAC,CAAC;;IAExE,OAAO+uB,MAAM;EACf;EAEA;EACAC,uBAAuBA,CAAA;IACrB,IAAI,CAAC7xB,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEA8xB,gBAAgBA,CAACxf,KAAU;IACzB,IAAIA,KAAK,CAAC7D,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACgI,aAAa,EAAE;;EAExB;EAEAsb,eAAeA,CAAA;IACb,IAAI,CAAClyB,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;MACvB,IAAI,CAACyB,WAAW,GAAG,EAAE;MACrB,IAAI,CAACG,aAAa,GAAG,EAAE;;EAE3B;EAEAuwB,iBAAiBA,CAAChf,SAAiB;IACjC;IACA,IAAI,CAAC9H,oBAAoB,GAAG8H,SAAS;EACvC;EAEAif,oBAAoBA,CAAC3iB,OAAe,EAAE4iB,KAAa;IACjD,IAAI,CAACA,KAAK,EAAE,OAAO5iB,OAAO;IAC1B,MAAMwH,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAImb,KAAK,GAAG,EAAE,IAAI,CAAC;IAC5C,OAAO5iB,OAAO,CAAC0H,OAAO,CAACF,KAAK,EAAE,iBAAiB,CAAC;EAClD;EAEA;EACAqb,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAC9zB,QAAQ,CAACqY,MAAM,CAAEvD,CAAC,IAAKA,CAAC,CAACkE,MAAM,CAAC,CAAC/F,MAAM;EACrD;EAEA8gB,oBAAoBA,CAAA;IAClB,IAAI,CAACtyB,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEAuyB,qBAAqBA,CAACrf,SAAiB;IACrC,IAAI,CAACiE,eAAe,CAACjE,SAAS,CAAC;EACjC;EAEAsf,iBAAiBA,CAACrd,IAAmB;IACnC,OAAO,IAAI,CAACD,iBAAiB,CAACC,IAAI,CAAC;EACrC;EAEA;EACAsd,WAAWA,CAAA;IACT,IAAI,CAACjyB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACrC,YAAY,CAAC+vB,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEAwE,UAAUA,CAAA;IACR,IAAI,CAAClyB,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC0O,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC/Q,YAAY,CAAC6Z,WAAW,CAAC,eAAe,CAAC;EAChD;EAEA2a,kBAAkBA,CAACvW,QAAgB;IACjC,MAAM2E,OAAO,GAAGpL,IAAI,CAACC,KAAK,CAACwG,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMY,OAAO,GAAGZ,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAG2E,OAAO,IAAI/D,OAAO,CAAC/O,QAAQ,EAAE,CAACkP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEAyV,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC1jB,QAAQ,EAAE,OAAO,aAAa;IACvC,OAAO,cAAc;EACvB;EAEA2jB,kBAAkBA,CAAA;IAChB,IAAI,CAAC5jB,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAIA;EACA6jB,eAAeA,CAAC1gB,IAAY;IAC1B,MAAM5C,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,IAAI+sB,gBAAgB,GAAGvjB,OAAO;IAE9B,QAAQ4C,IAAI;MACV,KAAK,MAAM;QACT2gB,gBAAgB,GAAG,IAAIvjB,OAAO,GAAG;QACjC;MACF,KAAK,QAAQ;QACXujB,gBAAgB,GAAG,IAAIvjB,OAAO,GAAG;QACjC;MACF,KAAK,eAAe;QAClBujB,gBAAgB,GAAG,IAAIvjB,OAAO,GAAG;QACjC;MACF,KAAK,WAAW;QACdujB,gBAAgB,GAAG,KAAKvjB,OAAO,IAAI;QACnC;MACF,KAAK,MAAM;QACTujB,gBAAgB,GAAG,KAAKvjB,OAAO,IAAI;QACnC;MACF,KAAK,OAAO;QACVujB,gBAAgB,GAAG,KAAKvjB,OAAO,EAAE;QACjC;MACF,KAAK,SAAS;QACZujB,gBAAgB,GAAG,KAAKvjB,OAAO,IAAI;QACnC;;IAGJ,IAAI,CAACF,WAAW,CAACmb,UAAU,CAAC;MAAEjb,OAAO,EAAEujB;IAAgB,CAAE,CAAC;EAC5D;EAEAC,aAAaA,CAAC5gB,IAAY;IACxB,MAAM5C,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,QAAQoM,IAAI;MACV,KAAK,MAAM;QACT,OAAO5C,OAAO,CAACsH,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,QAAQ;QACX,OAAOtH,OAAO,CAACsH,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,eAAe;QAClB,OAAOtH,OAAO,CAACsH,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,WAAW;QACd,OAAOtH,OAAO,CAACsH,QAAQ,CAAC,IAAI,CAAC;MAC/B,KAAK,MAAM;QACT,OAAOtH,OAAO,CAACsH,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,OAAO;QACV,OAAOtH,OAAO,CAACsH,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,SAAS;QACZ,OAAOtH,OAAO,CAACsH,QAAQ,CAAC,IAAI,CAAC;MAC/B;QACE,OAAO,KAAK;;EAElB;EAEAmc,UAAUA,CAAA;IACR,MAAMnX,GAAG,GAAG2K,MAAM,CAAC,wBAAwB,CAAC;IAC5C,IAAI3K,GAAG,EAAE;MACP,MAAMjS,IAAI,GAAG4c,MAAM,CAAC,2BAA2B,CAAC,IAAI3K,GAAG;MACvD,MAAMtM,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;MAC5D,IAAI,CAACsJ,WAAW,CAACmb,UAAU,CAAC;QAC1Bjb,OAAO,EAAEA,OAAO,GAAG,IAAI3F,IAAI,KAAKiS,GAAG;OACpC,CAAC;;EAEN;EAEAoX,WAAWA,CAAA;IACT,MAAMC,KAAK,GACT,oFAAoF;IACtF,MAAM3jB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACsJ,WAAW,CAACmb,UAAU,CAAC;MAC1Bjb,OAAO,EAAEA,OAAO,GAAG2jB;KACpB,CAAC;EACJ;EAEAC,UAAUA,CAAChhB,IAAY;IACrB,MAAM5C,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,MAAMqtB,QAAQ,GACZjhB,IAAI,KAAK,IAAI,GAAG,sBAAsB,GAAG,uBAAuB;IAClE,IAAI,CAAC9C,WAAW,CAACmb,UAAU,CAAC;MAC1Bjb,OAAO,EAAEA,OAAO,GAAG,IAAI,GAAG6jB;KAC3B,CAAC;EACJ;EAEAC,mBAAmBA,CAAA;IACjB,MAAM9jB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACsJ,WAAW,CAACmb,UAAU,CAAC;MAC1Bjb,OAAO,EAAEA,OAAO,GAAG;KACpB,CAAC;EACJ;EAEA+jB,mBAAmBA,CAAA;IACjB,MAAM/jB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACsJ,WAAW,CAACmb,UAAU,CAAC;MAC1Bjb,OAAO,EAAEA,OAAO,GAAG;KACpB,CAAC;EACJ;EAEAgkB,eAAeA,CAAA;IACb,MAAMhkB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,MAAMytB,YAAY,GAAGjkB,OAAO,CACzB0H,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAAA,CAC9BA,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5BA,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5BA,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAAA,CAC9BA,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5BA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAAA,CACnBA,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC;IAEtC,IAAI,CAAC5H,WAAW,CAACmb,UAAU,CAAC;MAAEjb,OAAO,EAAEikB;IAAY,CAAE,CAAC;EACxD;EAEAC,uBAAuBA,CAAA;IACrB,IAAI,CAAC7qB,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEA;EACA8qB,0BAA0BA,CAAA;IACxB,IAAI,CAAC1qB,sBAAsB,GAAG,EAAE;EAClC;EAEA2qB,eAAeA,CAACC,QAAgB,EAAEC,UAAkB;IAClD,MAAMtkB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,MAAM+tB,gBAAgB,GAAGvkB,OAAO,CAAC0H,OAAO,CAAC2c,QAAQ,EAAEC,UAAU,CAAC;IAC9D,IAAI,CAACxkB,WAAW,CAACmb,UAAU,CAAC;MAAEjb,OAAO,EAAEukB;IAAgB,CAAE,CAAC;IAC1D,IAAI,CAACJ,0BAA0B,EAAE;EACnC;EAEAK,gBAAgBA,CAACH,QAAgB;IAC/B,IAAI,CAAC5qB,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC2N,MAAM,CAC7Dqd,CAAC,IAAKA,CAAC,CAACJ,QAAQ,KAAKA,QAAQ,CAC/B;EACH;EAEA;EACAK,gBAAgBA,CAAC9hB,IAAY;IAC3B,MAAM+hB,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAAC/hB,IAAI,GAAG,MAAM;IAEnB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV+hB,KAAK,CAACG,MAAM,GAAG,SAAS;QACxB;MACF,KAAK,OAAO;QACVH,KAAK,CAACG,MAAM,GAAG,SAAS;QACxB;MACF,KAAK,OAAO;QACVH,KAAK,CAACG,MAAM,GAAG,SAAS;QACxB;MACF,KAAK,UAAU;QACbH,KAAK,CAACG,MAAM,GAAG,4CAA4C;QAC3D;;IAGJH,KAAK,CAACI,QAAQ,GAAI/hB,KAAU,IAAI;MAC9B,IAAI,CAACD,cAAc,CAACC,KAAK,CAAC;IAC5B,CAAC;IAED2hB,KAAK,CAAClb,KAAK,EAAE;EACf;EAEA;EACAub,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACjzB,iBAAiB,EAAE;MAC1B,OAAO,cAAc,IAAI,CAACA,iBAAiB,CAAC0Q,MAAM,EAAEC,QAAQ,KAAK;;IAEnE,IAAI,IAAI,CAAChT,gBAAgB,EAAE;MACzB,OAAO,4BAA4B;;IAErC,OAAO,wBAAwB;EACjC;EAEAu1B,aAAaA,CAACjiB,KAAU;IACtB,MAAMhD,OAAO,GAAGgD,KAAK,CAACE,MAAM,CAAC1M,KAAK;IAClC,IAAI,CAACsJ,WAAW,CAACmb,UAAU,CAAC;MAAEjb;IAAO,CAAE,CAAC;IAExC;IACA,IAAI,CAACklB,cAAc,CAACllB,OAAO,CAAC;IAC5B,IAAI,CAACmlB,cAAc,CAACnlB,OAAO,CAAC;IAC5B,IAAI,CAAColB,cAAc,CAACplB,OAAO,CAAC;IAE5B;IACA,IAAI,CAACqlB,YAAY,EAAE;EACrB;EAEAC,cAAcA,CAACtiB,KAAU;IACvB,IAAIA,KAAK,CAAC7D,GAAG,KAAK,OAAO,IAAI,CAAC6D,KAAK,CAACwB,QAAQ,EAAE;MAC5CxB,KAAK,CAACyB,cAAc,EAAE;MACtB,IAAI,CAACtC,WAAW,EAAE;KACnB,MAAM,IAAIa,KAAK,CAAC7D,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC1G,kBAAkB,CAACuJ,MAAM,GAAG,CAAC,EAAE;MACxEgB,KAAK,CAACyB,cAAc,EAAE;MACtB,IAAI,CAAC/L,oBAAoB,GAAGyN,IAAI,CAACof,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7sB,oBAAoB,GAAG,CAAC,CAAC;KACvE,MAAM,IACLsK,KAAK,CAAC7D,GAAG,KAAK,WAAW,IACzB,IAAI,CAAC1G,kBAAkB,CAACuJ,MAAM,GAAG,CAAC,EAClC;MACAgB,KAAK,CAACyB,cAAc,EAAE;MACtB,IAAI,CAAC/L,oBAAoB,GAAGyN,IAAI,CAACqf,GAAG,CAClC,IAAI,CAAC/sB,kBAAkB,CAACuJ,MAAM,GAAG,CAAC,EAClC,IAAI,CAACtJ,oBAAoB,GAAG,CAAC,CAC9B;KACF,MAAM,IAAIsK,KAAK,CAAC7D,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC1G,kBAAkB,CAACuJ,MAAM,GAAG,CAAC,EAAE;MACpEgB,KAAK,CAACyB,cAAc,EAAE;MACtB,IAAI,CAACghB,aAAa,CAAC,IAAI,CAAChtB,kBAAkB,CAAC,IAAI,CAACC,oBAAoB,CAAC,CAAC;;EAE1E;EAEAgtB,YAAYA,CAAC1iB,KAAU;IACrB;IACA,IAAI,CAAC2iB,iBAAiB,EAAE;EAC1B;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACjmB,YAAY,GAAG,IAAI;EAC1B;EAEAkmB,WAAWA,CAAA;IACT,IAAI,CAAClmB,YAAY,GAAG,KAAK;IACzB;IACAyF,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3M,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACI,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACI,kBAAkB,GAAG,EAAE;IAC9B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA6sB,YAAYA,CAAC9iB,KAAU;IACrB,MAAM+iB,aAAa,GAAG/iB,KAAK,CAAC+iB,aAAa,IAAKC,MAAc,CAACD,aAAa;IAC1E,MAAME,UAAU,GAAGF,aAAa,CAACG,OAAO,CAAC,MAAM,CAAC;IAEhD;IACA,IAAID,UAAU,CAAC3e,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC/B;MACA,IAAI,CAAC6e,WAAW,CAACF,UAAU,CAAC;;EAEhC;EAEAG,aAAaA,CAACpjB,KAAU;IACtB;EAAA;EAGF;EACAkiB,cAAcA,CAACllB,OAAe;IAC5B,MAAMqmB,YAAY,GAAGrmB,OAAO,CAACsmB,KAAK,CAAC,SAAS,CAAC;IAC7C,IAAID,YAAY,EAAE;MAChB,MAAMzD,KAAK,GAAGyD,YAAY,CAAC,CAAC,CAAC;MAC7B,IAAI,CAAC5tB,kBAAkB,GAAG,IAAI,CAAC8tB,gBAAgB,CAAC3D,KAAK,CAAC;MACtD,IAAI,CAAClqB,oBAAoB,GAAG,CAAC;KAC9B,MAAM;MACL,IAAI,CAACD,kBAAkB,GAAG,EAAE;;EAEhC;EAEA0sB,cAAcA,CAACnlB,OAAe;IAC5B,MAAMwmB,YAAY,GAAGxmB,OAAO,CAACsmB,KAAK,CAAC,SAAS,CAAC;IAC7C,IAAIE,YAAY,EAAE;MAChB,MAAM5D,KAAK,GAAG4D,YAAY,CAAC,CAAC,CAAC;MAC7B,IAAI,CAAC3tB,kBAAkB,GAAG,IAAI,CAAC4tB,mBAAmB,CAAC7D,KAAK,CAAC;KAC1D,MAAM;MACL,IAAI,CAAC/pB,kBAAkB,GAAG,EAAE;;EAEhC;EAEAusB,cAAcA,CAACplB,OAAe;IAC5B,MAAM0mB,YAAY,GAAG1mB,OAAO,CAACsmB,KAAK,CAAC,UAAU,CAAC;IAC9C,IAAII,YAAY,EAAE;MAChB,MAAM9D,KAAK,GAAG8D,YAAY,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACztB,kBAAkB,GAAG,IAAI,CAAC0tB,mBAAmB,CAAC/D,KAAK,CAAC;KAC1D,MAAM;MACL,IAAI,CAAC3pB,kBAAkB,GAAG,EAAE;;EAEhC;EAEAktB,WAAWA,CAACnmB,OAAe;IACzB,MAAM4mB,QAAQ,GAAG,sBAAsB;IACvC,MAAMC,KAAK,GAAG7mB,OAAO,CAACsmB,KAAK,CAACM,QAAQ,CAAC;IACrC,IAAIC,KAAK,EAAE;MACT;MACA9kB,OAAO,CAACsE,GAAG,CAAC,iBAAiB,EAAEwgB,KAAK,CAAC;;EAEzC;EAEApB,aAAaA,CAAC1Z,IAAS;IACrB,MAAM/L,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,MAAMswB,UAAU,GAAG9mB,OAAO,CAAC0H,OAAO,CAAC,OAAO,EAAE,IAAIqE,IAAI,CAACrJ,QAAQ,GAAG,CAAC;IACjE,IAAI,CAAC5C,WAAW,CAACmb,UAAU,CAAC;MAAEjb,OAAO,EAAE8mB;IAAU,CAAE,CAAC;IACpD,IAAI,CAACruB,kBAAkB,GAAG,EAAE;EAC9B;EAEAsuB,aAAaA,CAACC,OAAY;IACxB,MAAMhnB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,MAAMswB,UAAU,GAAG9mB,OAAO,CAAC0H,OAAO,CAAC,OAAO,EAAE,IAAIsf,OAAO,CAAC5zB,IAAI,GAAG,CAAC;IAChE,IAAI,CAAC0M,WAAW,CAACmb,UAAU,CAAC;MAAEjb,OAAO,EAAE8mB;IAAU,CAAE,CAAC;IACpD,IAAI,CAACjuB,kBAAkB,GAAG,EAAE;EAC9B;EAEAouB,aAAaA,CAACC,OAAY;IACxB,MAAMlnB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,MAAMswB,UAAU,GAAG9mB,OAAO,CAAC0H,OAAO,CAAC,QAAQ,EAAE,IAAIwf,OAAO,CAAC9zB,IAAI,GAAG,CAAC;IACjE,IAAI,CAAC0M,WAAW,CAACmb,UAAU,CAAC;MAAEjb,OAAO,EAAE8mB;IAAU,CAAE,CAAC;IACpD,IAAI,CAAC7tB,kBAAkB,GAAG,EAAE;EAC9B;EAEAkuB,uBAAuBA,CAAC5jB,KAAa;IACnC,IAAI,CAAC7K,oBAAoB,GAAG6K,KAAK;EACnC;EAEAoiB,iBAAiBA,CAAA;IACf,MAAM3lB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,IAAI,CAAC0uB,cAAc,CAACllB,OAAO,CAAC;IAC5B,IAAI,CAACmlB,cAAc,CAACnlB,OAAO,CAAC;IAC5B,IAAI,CAAColB,cAAc,CAACplB,OAAO,CAAC;EAC9B;EAEA;EACAonB,iBAAiBA,CAAA;IACf,MAAMpnB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,OAAOwJ,OAAO,CAACgC,MAAM;EACvB;EAEAqlB,gBAAgBA,CAAA;IACd,MAAMrnB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,OACEwJ,OAAO,CAACsH,QAAQ,CAAC,GAAG,CAAC,IACrBtH,OAAO,CAACsH,QAAQ,CAAC,GAAG,CAAC,IACrBtH,OAAO,CAACsH,QAAQ,CAAC,GAAG,CAAC,IACrBtH,OAAO,CAACsH,QAAQ,CAAC,GAAG,CAAC;EAEzB;EAEA;EACAggB,eAAeA,CAACC,YAAoB;IAClC,MAAMC,SAAS,GAA8B;MAC3CC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE,SAAS;MACbC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE;KACL;IACD,OAAOL,SAAS,CAACD,YAAY,CAAC,IAAIA,YAAY;EAChD;EAEAO,gBAAgBA,CAAA;IACd,IAAI,CAACpuB,oBAAoB,GAAG,IAAI;IAChC,MAAMsG,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACqD,0BAA0B,GAAGmG,OAAO;IACzC;IACA,IAAI,CAAClG,cAAc,GAAG,gBAAgBkG,OAAO,EAAE;EACjD;EAEA+nB,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACtuB,sBAAsB,GAAG,CAC5B;MAAE4qB,QAAQ,EAAE,OAAO;MAAE2D,WAAW,EAAE,CAAC,OAAO;IAAC,CAAE,EAC7C;MAAE3D,QAAQ,EAAE,OAAO;MAAE2D,WAAW,EAAE,CAAC,OAAO;IAAC,CAAE,CAC9C;EACH;EAEA;EACA3C,YAAYA,CAAA;IACV;IACA,IAAI,CAAC,IAAI,CAAC51B,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;;IAGF;IACAsR,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;IAChC,IAAI,CAACA,aAAa,GAAGsE,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC3V,QAAQ,GAAG,KAAK;MACrB;IACF,CAAC,EAAE,IAAI,CAAC;EACV;EAEA82B,gBAAgBA,CAAC3D,KAAa;IAC5B,MAAM3e,KAAK,GAAG,CACZ;MAAE9Q,EAAE,EAAE,GAAG;MAAEuP,QAAQ,EAAE,OAAO;MAAEtP,IAAI,EAAE;IAAc,CAAE,EACpD;MAAED,EAAE,EAAE,GAAG;MAAEuP,QAAQ,EAAE,KAAK;MAAEtP,IAAI,EAAE;IAAY,CAAE,EAChD;MAAED,EAAE,EAAE,GAAG;MAAEuP,QAAQ,EAAE,QAAQ;MAAEtP,IAAI,EAAE;IAAe,CAAE,CACvD;IAED,IAAI,CAACwvB,KAAK,EAAE,OAAO3e,KAAK;IACxB,OAAOA,KAAK,CAACmD,MAAM,CAChB2E,IAAI,IACHA,IAAI,CAACrJ,QAAQ,CAAC2E,WAAW,EAAE,CAACC,QAAQ,CAACsb,KAAK,CAACvb,WAAW,EAAE,CAAC,IACzD0E,IAAI,CAAC3Y,IAAI,CAACiU,WAAW,EAAE,CAACC,QAAQ,CAACsb,KAAK,CAACvb,WAAW,EAAE,CAAC,CACxD;EACH;EAEAof,mBAAmBA,CAAC7D,KAAa;IAC/B,MAAMqF,QAAQ,GAAG,CACf;MAAE90B,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,WAAW;MAAE4Q,KAAK,EAAE;IAAE,CAAE,EACzC;MAAE7Q,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,QAAQ;MAAE4Q,KAAK,EAAE;IAAC,CAAE,EACrC;MAAE7Q,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,SAAS;MAAE4Q,KAAK,EAAE;IAAE,CAAE,CACxC;IAED,IAAI,CAAC4e,KAAK,EAAE,OAAOqF,QAAQ;IAC3B,OAAOA,QAAQ,CAAC7gB,MAAM,CAAE4f,OAAO,IAC7BA,OAAO,CAAC5zB,IAAI,CAACiU,WAAW,EAAE,CAACC,QAAQ,CAACsb,KAAK,CAACvb,WAAW,EAAE,CAAC,CACzD;EACH;EAEAsf,mBAAmBA,CAAC/D,KAAa;IAC/B,MAAMsF,QAAQ,GAAG,CACf;MAAE/0B,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAE+0B,WAAW,EAAE;IAAiB,CAAE,EACzD;MAAEh1B,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,OAAO;MAAE+0B,WAAW,EAAE;IAAyB,CAAE,EAClE;MAAEh1B,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,QAAQ;MAAE+0B,WAAW,EAAE;IAAmB,CAAE,CAC9D;IAED,IAAI,CAACvF,KAAK,EAAE,OAAOsF,QAAQ;IAC3B,OAAOA,QAAQ,CAAC9gB,MAAM,CAAE8f,OAAO,IAC7BA,OAAO,CAAC9zB,IAAI,CAACiU,WAAW,EAAE,CAACC,QAAQ,CAACsb,KAAK,CAACvb,WAAW,EAAE,CAAC,CACzD;EACH;EAEA;EACA+gB,UAAUA,CAAA;IACR;IACA,IAAI,CAACz5B,YAAY,CAAC+vB,QAAQ,CAAC,2BAA2B,CAAC;EACzD;EAEA2J,kBAAkBA,CAAA;IAChB,IAAI,CAACxtB,kBAAkB,GAAG,IAAI;EAChC;EAEAytB,iBAAiBA,CAAA;IACf,IAAI,CAACltB,iBAAiB,GAAG,IAAI;EAC/B;EAEAmtB,eAAeA,CAAA;IACb,IAAI,CAACruB,eAAe,GAAG,IAAI;EAC7B;EAEAsuB,gBAAgBA,CAAA;IACd,IAAI,CAAClwB,gBAAgB,GAAG,KAAK;EAC/B;EAEAmwB,gBAAgBA,CAACC,KAAa;IAC5B,MAAM1oB,OAAO,GAAG,IAAI,CAACF,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAE5L,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACsJ,WAAW,CAACmb,UAAU,CAAC;MAC1Bjb,OAAO,EAAEA,OAAO,GAAG0oB;KACpB,CAAC;IACF,IAAI,CAACF,gBAAgB,EAAE;EACzB;EAEA;EACAG,aAAaA,CAAC3lB,KAAW;IACvB;IACA,IAAIA,KAAK,EAAE;MACT,IAAI,CAAChR,WAAW,GAAGgR,KAAK,CAACE,MAAM,CAAC1M,KAAK;;EAEzC;EAEA;EACAoyB,oBAAoBA,CAACnU,QAAa;IAChC;IACA,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrE;EAEAoU,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACx4B,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACmN,kBAAkB,GAAG,KAAK;EACjC;EAEAsrB,cAAcA,CAAA;IACZ,IAAI,CAACnkB,SAAS,CAAC,OAAmB,CAAC;EACrC;EAEAokB,cAAcA,CAAA;IACZ,IAAI,CAACpkB,SAAS,CAAC,OAAmB,CAAC;EACrC;CACD;AAhkGyCqkB,UAAA,EAAvC/6B,SAAS,CAAC,mBAAmB,CAAC,C,8DAAwC;AAEvE+6B,UAAA,EADC/6B,SAAS,CAAC,WAAW,EAAE;EAAEg7B,MAAM,EAAE;AAAK,CAAE,CAAC,C,sDACD;AAL9B76B,oBAAoB,GAAA46B,UAAA,EALhCh7B,SAAS,CAAC;EACTk7B,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,8BAA8B;CAC3C,CAAC,C,EACWh7B,oBAAoB,CAmkGhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}