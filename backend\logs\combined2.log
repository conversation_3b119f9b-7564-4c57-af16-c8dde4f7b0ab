{"level":"info","message":"WebSocket operation for user 6838d109588e9011d26e408b, operation: subscribe","timestamp":"2025-05-29 23:44:00"}
{"level":"info","message":"WebSocket operation for user 6838d109588e9011d26e408b, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"WebSocket operation for user 6838d109588e9011d26e408b, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"WebSocket operation for user 6838d109588e9011d26e408b, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"WebSocket operation for user 6838d109588e9011d26e408b, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"WebSocket operation for user 6838d109588e9011d26e408b, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"WebSocket operation for user 6838d109588e9011d26e408b, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"WebSocket operation for user 6838d109588e9011d26e408b, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-29 23:44:01","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-29 23:44:01","variables":{"conversationId":"6838d97b3ab249cacc4dcab7","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6838d97b3ab249cacc4dcab7, userId=6838d109588e9011d26e408b","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-29 23:44:01","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-29 23:44:01","variables":{"conversationId":"6838d97b3ab249cacc4dcab7","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6838d97b3ab249cacc4dcab7, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 92ms","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GraphQL anonymous completed in 157ms","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"POST / 200 - 165ms","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 81ms","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GraphQL anonymous completed in 189ms","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"POST / 200 - 196ms","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 82ms","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 19ms","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6838d97b3ab249cacc4dcab7, unread: 3, messages: 4","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6838d97b3ab249cacc4dcab7, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6838d109588e9011d26e408b, offset=0","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6838d97b3ab249cacc4dcab7, unread: 1, messages: 4","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6838d97b3ab249cacc4dcab7, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"[MessageService] Retrieved 4 messages","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GraphQL anonymous completed in 277ms","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"POST / 200 - 284ms","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"[MessageService] Retrieved 4 messages","timestamp":"2025-05-29 23:44:01"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"GraphQL anonymous completed in 273ms","timestamp":"2025-05-29 23:44:01"}
{"level":"http","message":"POST / 200 - 281ms","timestamp":"2025-05-29 23:44:01"}
