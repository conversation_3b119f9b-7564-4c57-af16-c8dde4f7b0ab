{"error":"Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"Error: Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com\n    at uploadFile (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\fileUpload.service.js:62:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MessageService.handleFileUpload (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:48:17)\n    at async MessageService.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:710:24)\n    at async Object.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\graphql\\messageResolvers.js:721:24)","timestamp":"2025-05-26 02:21:39"}
{"error":"Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"Error: Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com\n    at uploadFile (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\fileUpload.service.js:62:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MessageService.handleFileUpload (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:48:17)\n    at async MessageService.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:710:24)\n    at async Object.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\graphql\\messageResolvers.js:721:24)","timestamp":"2025-05-26 02:21:40"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:35"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:35"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:41"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:42"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:48"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:48"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:23"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:24"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:57"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:57"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:30:51"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:30:52"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682bb95fb9b407dd58126686","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 11:52:57"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682bb95fb9b407dd58126686","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 11:52:58"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 14:09:00"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 14:09:05"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 18:43:18"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 18:43:19"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 22:58:51"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 22:58:52"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:05"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:05"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:15"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:16"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:23"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:24"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:24"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:25"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:26"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"682944d0b5d5c86fd2e56e46","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-28 20:11:26"}
{"expiredAt":"2025-05-28T20:08:39.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-28 21:08:39"}
{"expiredAt":"2025-05-28T20:08:39.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-28 21:08:44"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 01:30:42"}
{"expiredAt":"2025-05-28T20:08:39.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-29 04:00:15"}
{"expiredAt":"2025-05-28T20:08:39.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-29 04:00:22"}
{"expiredAt":"2025-05-28T20:08:39.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-29 04:08:50"}
{"expiredAt":"2025-05-28T20:08:39.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-29 04:08:58"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 04:08:59"}
{"expiredAt":"2025-05-28T20:08:39.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-29 04:08:59"}
{"level":"error","message":"POST /login 500 - 125ms","timestamp":"2025-05-29 04:09:34"}
{"level":"error","message":"POST /login 500 - 75ms","timestamp":"2025-05-29 04:09:44"}
{"level":"error","message":"POST /login 500 - 110ms","timestamp":"2025-05-29 04:10:04"}
{"level":"error","message":"POST /login 500 - 80ms","timestamp":"2025-05-29 04:10:06"}
{"level":"error","message":"POST /login 500 - 78ms","timestamp":"2025-05-29 04:10:22"}
{"level":"error","message":"POST /login 500 - 84ms","timestamp":"2025-05-29 04:10:29"}
{"level":"error","message":"POST /login 500 - 77ms","timestamp":"2025-05-29 04:10:44"}
{"level":"error","message":"POST /login 500 - 77ms","timestamp":"2025-05-29 04:10:46"}
{"expiredAt":"2025-05-26T23:16:28.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-29 04:11:09"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 04:11:09"}
{"expiredAt":"2025-05-26T23:16:28.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-29 04:11:09"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 04:11:13"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 04:11:16"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 04:11:21"}
{"level":"error","message":"POST /login 500 - 88ms","timestamp":"2025-05-29 04:11:23"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 04:11:32"}
{"level":"error","message":"POST /login 500 - 82ms","timestamp":"2025-05-29 04:11:44"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 04:11:49"}
{"level":"error","message":"POST /login 500 - 77ms","timestamp":"2025-05-29 04:11:52"}
{"level":"error","message":"POST /login 500 - 98ms","timestamp":"2025-05-29 21:52:21"}
{"level":"error","message":"POST /login 500 - 89ms","timestamp":"2025-05-29 21:52:25"}
{"level":"error","message":"POST /login 500 - 109ms","timestamp":"2025-05-29 21:53:50"}
{"level":"error","message":"POST /login 500 - 111ms","timestamp":"2025-05-29 21:54:05"}
{"level":"error","message":"POST /login 500 - 110ms","timestamp":"2025-05-29 21:55:04"}
{"expiredAt":"2025-05-26T23:16:28.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-29 22:15:48"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 22:15:48"}
{"expiredAt":"2025-05-26T23:16:28.000Z","level":"error","message":"Auth middleware error: jwt expired","name":"TokenExpiredError","stack":"TokenExpiredError: jwt expired\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:190:21\n    at getSecret (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:97:14)\n    at module.exports [as verify] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\jsonwebtoken\\verify.js:101:10)\n    at verifyTokenGraphql (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\middlewares\\authUserMiddleware.js:115:23)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\server.js:305:36\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at graphqlUploadExpressMiddleware (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\graphql-upload\\graphqlUploadExpress.js:50:52)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-05-29 22:15:48"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 22:15:51"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 22:15:55"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 22:16:01"}
{"level":"error","message":"POST /login 500 - 98ms","timestamp":"2025-05-29 22:16:04"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 22:16:11"}
{"level":"error","message":"WebSocket authentication failed:","timestamp":"2025-05-29 22:16:29"}
{"level":"error","message":"POST /login 500 - 100ms","timestamp":"2025-05-29 22:17:31"}
{"level":"error","message":"POST /login 500 - 142ms","timestamp":"2025-05-29 22:21:22"}
{"level":"error","message":"POST /login 500 - 97ms","timestamp":"2025-05-29 22:21:47"}
{"level":"error","message":"POST /login 500 - 117ms","timestamp":"2025-05-29 22:22:34"}
{"level":"error","message":"POST /login 500 - 118ms","timestamp":"2025-05-29 22:25:05"}
{"level":"error","message":"POST /login 500 - 141ms","timestamp":"2025-05-29 22:25:40"}
