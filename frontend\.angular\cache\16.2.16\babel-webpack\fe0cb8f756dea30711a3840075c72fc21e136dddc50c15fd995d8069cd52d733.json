{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/toast.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 44);\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46);\n    i0.ɵɵelement(4, \"div\", 47)(5, \"div\", 48)(6, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.isOnline) ? \"En ligne\" : ctx_r2.formatLastActive(ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"div\", 51);\n    i0.ɵɵelementStart(2, \"span\", 52);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵelement(2, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 56);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r5.otherParticipant == null ? null : ctx_r5.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"span\", 73);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.formatDateSeparator(message_r16.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"img\", 75);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const message_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.openUserProfile(message_r16.sender == null ? null : message_r16.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r16.sender == null ? null : message_r16.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r16.sender == null ? null : message_r16.sender.username);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r20.getUserColor(message_r16.sender == null ? null : message_r16.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r16.sender == null ? null : message_r16.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"div\", 78);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r21.formatMessageContent(message_r16.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 84);\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r33.formatMessageContent(message_r16.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 79);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_7_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const message_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.openImageViewer(message_r16));\n    });\n    i0.ɵɵelement(2, \"img\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵelement(4, \"i\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, MessageChatComponent_div_27_ng_container_1_div_7_div_5_Template, 1, 1, \"div\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r22.getImageUrl(message_r16), i0.ɵɵsanitizeUrl)(\"alt\", message_r16.content || \"Image\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", message_r16.content);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const message_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.downloadFile(message_r16));\n    });\n    i0.ɵɵelementStart(1, \"div\", 86);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8)(4, \"div\", 87);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 73);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 88);\n    i0.ɵɵelement(9, \"i\", 89);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r23.getFileIcon(message_r16));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getFileName(message_r16), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getFileSize(message_r16), \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 95);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 96);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 97);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 98);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_div_12_i_1_Template, 1, 0, \"i\", 91);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_27_ng_container_1_div_12_i_2_Template, 1, 0, \"i\", 92);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_27_ng_container_1_div_12_i_3_Template, 1, 0, \"i\", 93);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_27_ng_container_1_div_12_i_4_Template, 1, 0, \"i\", 94);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const reaction_r49 = restoredCtx.$implicit;\n      const message_r16 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.toggleReaction(message_r16.id, reaction_r49.emoji));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"1\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reaction_r49 = ctx.$implicit;\n    const ctx_r48 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r48.hasUserReacted(reaction_r49, ctx_r48.currentUserId || \"\"))(\"text-green-600\", ctx_r48.hasUserReacted(reaction_r49, ctx_r48.currentUserId || \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r49.emoji);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template, 5, 5, \"button\", 100);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", message_r16.reactions);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_div_1_Template, 4, 1, \"div\", 61);\n    i0.ɵɵelementStart(2, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const message_r16 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.onMessageClick(message_r16, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_27_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const message_r16 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.onMessageContextMenu(message_r16, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_27_ng_container_1_div_3_Template, 2, 2, \"div\", 63);\n    i0.ɵɵelementStart(4, \"div\", 64);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_27_ng_container_1_div_5_Template, 2, 3, \"div\", 65);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_27_ng_container_1_div_6_Template, 2, 1, \"div\", 66);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_27_ng_container_1_div_7_Template, 6, 3, \"div\", 26);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_27_ng_container_1_div_8_Template, 10, 4, \"div\", 67);\n    i0.ɵɵelementStart(9, \"div\", 68)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, MessageChatComponent_div_27_ng_container_1_div_12_Template, 5, 4, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, MessageChatComponent_div_27_ng_container_1_div_13_Template, 2, 1, \"div\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = ctx.$implicit;\n    const i_r17 = ctx.index;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.shouldShowDateSeparator(i_r17));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"justify-end\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId)(\"justify-start\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId);\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r16.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId && ctx_r14.shouldShowAvatar(i_r17));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"bg-green-500\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId)(\"text-white\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId)(\"bg-white\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId)(\"text-gray-900\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId)(\"dark:bg-gray-700\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId)(\"dark:text-white\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isGroupConversation() && (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId && ctx_r14.shouldShowSenderName(i_r17));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.getMessageType(message_r16) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.hasImage(message_r16));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.hasFile(message_r16));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r14.formatMessageTime(message_r16.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.reactions && message_r16.reactions.length > 0);\n  }\n}\nfunction MessageChatComponent_div_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"img\", 103);\n    i0.ɵɵelementStart(2, \"div\", 104)(3, \"div\", 46);\n    i0.ɵɵelement(4, \"div\", 105)(5, \"div\", 106)(6, \"div\", 107);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r15.otherParticipant == null ? null : ctx_r15.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r15.otherParticipant == null ? null : ctx_r15.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_Template, 14, 26, \"ng-container\", 59);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_27_div_2_Template, 7, 2, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.messages)(\"ngForTrackBy\", ctx_r6.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isTyping);\n  }\n}\nfunction MessageChatComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_button_40_Template_button_mousedown_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.startVoiceRecording());\n    })(\"mouseup\", function MessageChatComponent_button_40_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.stopVoiceRecording());\n    })(\"mouseleave\", function MessageChatComponent_button_40_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.cancelVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"bg-red-500\", ctx_r8.isRecordingVoice)(\"hover:bg-red-600\", ctx_r8.isRecordingVoice)(\"animate-pulse\", ctx_r8.isRecordingVoice);\n  }\n}\nfunction MessageChatComponent_button_41_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 113);\n  }\n}\nfunction MessageChatComponent_button_41_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 114);\n  }\n}\nfunction MessageChatComponent_button_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_41_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.sendMessage());\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_button_41_i_1_Template, 1, 0, \"i\", 111);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_41_i_2_Template, 1, 0, \"i\", 112);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r9.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isSendingMessage);\n  }\n}\nfunction MessageChatComponent_div_42_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_42_button_3_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const category_r67 = restoredCtx.$implicit;\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.selectEmojiCategory(category_r67));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r67 = ctx.$implicit;\n    const ctx_r65 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r65.selectedEmojiCategory === category_r67)(\"text-green-600\", ctx_r65.selectedEmojiCategory === category_r67)(\"hover:bg-gray-100\", ctx_r65.selectedEmojiCategory !== category_r67)(\"dark:hover:bg-gray-700\", ctx_r65.selectedEmojiCategory !== category_r67);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r67.icon, \" \");\n  }\n}\nfunction MessageChatComponent_div_42_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_42_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r72);\n      const emoji_r70 = restoredCtx.$implicit;\n      const ctx_r71 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r71.insertEmoji(emoji_r70));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r70 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r70.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r70.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"div\", 116)(2, \"div\", 117);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_42_button_3_Template, 2, 9, \"button\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 119);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_42_button_5_Template, 2, 2, \"button\", 120);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.emojiCategories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.getEmojisForCategory(ctx_r10.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 116)(2, \"div\", 124)(3, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 126);\n    i0.ɵɵelement(5, \"i\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 128);\n    i0.ɵɵtext(7, \"Photos\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.triggerFileInput(\"video\"));\n    });\n    i0.ɵɵelementStart(9, \"div\", 129);\n    i0.ɵɵelement(10, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 128);\n    i0.ɵɵtext(12, \"Vid\\u00E9os\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 131);\n    i0.ɵɵelement(15, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 128);\n    i0.ɵɵtext(17, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.openCamera());\n    });\n    i0.ɵɵelementStart(19, \"div\", 133);\n    i0.ɵɵelement(20, \"i\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 128);\n    i0.ɵɵtext(22, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 135);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class MessageChatComponent {\n  constructor(fb, route, MessageService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.MessageService = MessageService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    // === ENREGISTREMENT VOCAL ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    // === APPELS ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  ngOnInit() {\n    this.initializeComponent();\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n  }\n  loadCurrentUser() {\n    try {\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      if (user && user._id) {\n        this.currentUserId = user._id;\n        this.currentUsername = user.username || 'You';\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement de l'utilisateur:\", error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    // Utiliser getConversation avec les paramètres de pagination\n    this.MessageService.getConversation(conversationId, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    this.messages = this.conversation.messages || [];\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  setOtherParticipant() {\n    if (this.conversation?.participants) {\n      console.log('Setting other participant...');\n      console.log('Current user ID:', this.currentUserId);\n      console.log('All participants:', this.conversation.participants);\n      this.otherParticipant = this.conversation.participants.find(p => (p.id || p._id) !== this.currentUserId);\n      console.log('Other participant found:', this.otherParticipant);\n      // Si otherParticipant n'est pas trouvé, essayons avec une logique différente\n      if (!this.otherParticipant && this.conversation.participants.length > 0) {\n        console.log('Fallback: using first participant');\n        this.otherParticipant = this.conversation.participants[0];\n      }\n    }\n  }\n  setupSubscriptions() {\n    // Abonnements GraphQL pour les mises à jour en temps réel\n    // À implémenter selon vos besoins\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const otherParticipant = this.conversation.participants?.find(p => (p.id || p._id) !== this.currentUserId);\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        this.messages.push(message);\n        this.messageForm.reset();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n      }\n    });\n  }\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n    for (let file of files) {\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    const otherParticipant = this.conversation?.participants?.find(p => (p.id || p._id) !== this.currentUserId);\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    this.MessageService.sendMessage(receiverId, '', file, undefined, this.conversation.id).subscribe({\n      next: message => {\n        this.messages.push(message);\n        this.scrollToBottom();\n        this.toastService.showSuccess('Fichier envoyé avec succès');\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'envoi du fichier:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n      }\n    });\n  }\n  // === ENREGISTREMENT VOCAL ===\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          }\n        });\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: 'audio/webm;codecs=opus'\n        });\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          _this.cdr.detectChanges();\n        }, 1000);\n        _this.mediaRecorder.ondataavailable = event => {\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.start(100);\n        _this.toastService.showSuccess('Enregistrement vocal démarré');\n      } catch (error) {\n        console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n        _this.toastService.showError(\"Impossible d'accéder au microphone\");\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this2.audioChunks.length === 0) {\n          _this2.toastService.showWarning('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: 'audio/webm;codecs=opus'\n        });\n        if (_this2.voiceRecordingDuration < 1) {\n          _this2.toastService.showWarning('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n          type: 'audio/webm;codecs=opus'\n        });\n        yield _this2.sendVoiceMessage(audioFile);\n        _this2.toastService.showSuccess('Message vocal envoyé');\n      } catch (error) {\n        console.error(\"Erreur lors du traitement de l'audio:\", error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n      } finally {\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const otherParticipant = _this3.conversation?.participants?.find(p => (p.id || p._id) !== _this3.currentUserId);\n      const receiverId = otherParticipant?.id || otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, undefined, _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === SYSTÈME D'ÉMOJIS ===\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + (emoji.emoji || emoji);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n    setTimeout(() => {\n      const textarea = document.querySelector('textarea[formControlName=\"content\"]');\n      if (textarea) {\n        textarea.focus();\n      }\n    }, 100);\n  }\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    this.showAttachmentMenu = false;\n  }\n  // === APPELS VIDÉO/AUDIO ===\n  startCall(type) {\n    this.callType = type;\n    this.isInCall = true;\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n    this.toastService.showSuccess(`Appel ${type === 'VIDEO' ? 'vidéo' : 'audio'} démarré`);\n  }\n  endCall() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.toastService.showSuccess('Appel terminé');\n  }\n  startVideoCall() {\n    this.startCall('VIDEO');\n  }\n  startVoiceCall() {\n    this.startCall('AUDIO');\n  }\n  // === RECHERCHE ===\n  toggleSearch() {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  // === PAGINATION ===\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        const newMessages = conversation.messages || [];\n        if (newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début (messages plus anciens)\n          this.messages = [...newMessages, ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n  // === GÉOLOCALISATION ===\n  shareLocation() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const position = yield _this4.getCurrentPosition();\n        const {\n          latitude,\n          longitude\n        } = position.coords;\n        const locationMessage = {\n          type: 'location',\n          latitude,\n          longitude,\n          address: yield _this4.getAddressFromCoordinates(latitude, longitude),\n          mapUrl: `https://maps.google.com/maps?q=${latitude},${longitude}&z=15`,\n          timestamp: new Date()\n        };\n        yield _this4.sendLocationMessage(locationMessage);\n        _this4.toastService.showSuccess('Position partagée avec succès');\n      } catch (error) {\n        console.error('Erreur lors du partage de localisation:', error);\n        _this4.toastService.showError(\"Impossible d'obtenir votre position\");\n      }\n    })();\n  }\n  getCurrentPosition() {\n    return new Promise((resolve, reject) => {\n      navigator.geolocation.getCurrentPosition(resolve, reject, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 60000\n      });\n    });\n  }\n  getAddressFromCoordinates(lat, lng) {\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);\n        if (response.ok) {\n          const data = yield response.json();\n          return data.display_name || `${lat}, ${lng}`;\n        }\n      } catch (error) {\n        console.error('Erreur lors du géocodage inverse:', error);\n      }\n      return `${lat}, ${lng}`;\n    })();\n  }\n  sendLocationMessage(locationData) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const otherParticipant = _this5.conversation?.participants?.find(p => (p.id || p._id) !== _this5.currentUserId);\n      const receiverId = otherParticipant?.id || otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this5.MessageService.sendMessage(receiverId, `📍 Position partagée: ${locationData.address}`, undefined, 'TEXT', _this5.conversation.id).subscribe({\n          next: message => {\n            _this5.messages.push(message);\n            _this5.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message de localisation:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  // === PARTAGE DE CONTACTS ===\n  shareContact() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const contact = yield _this6.selectContact();\n        if (contact) {\n          yield _this6.sendContactMessage(contact);\n          _this6.toastService.showSuccess('Contact partagé avec succès');\n        }\n      } catch (error) {\n        console.error('Erreur lors du partage de contact:', error);\n        _this6.toastService.showError('Erreur lors du partage du contact');\n      }\n    })();\n  }\n  selectContact() {\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        setTimeout(() => {\n          resolve({\n            name: 'John Doe',\n            phone: '+33 6 12 34 56 78',\n            email: '<EMAIL>',\n            avatar: 'assets/images/default-avatar.png'\n          });\n        }, 1000);\n      });\n    })();\n  }\n  sendContactMessage(contactData) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const otherParticipant = _this7.conversation?.participants?.find(p => (p.id || p._id) !== _this7.currentUserId);\n      const receiverId = otherParticipant?.id || otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this7.MessageService.sendMessage(receiverId, `👤 Contact partagé: ${contactData.name}\\n📞 ${contactData.phone}\\n📧 ${contactData.email}`, undefined, 'TEXT', _this7.conversation.id).subscribe({\n          next: message => {\n            _this7.messages.push(message);\n            _this7.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message de contact:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  // === UTILITAIRES ===\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n  triggerFileInput(type) {\n    // Optionnel : configurer le type de fichier accepté selon le type\n    if (type === 'image') {\n      this.fileInput.nativeElement.accept = 'image/*';\n    } else if (type === 'video') {\n      this.fileInput.nativeElement.accept = 'video/*';\n    } else if (type === 'document') {\n      this.fileInput.nativeElement.accept = '.pdf,.doc,.docx,.txt,.xlsx,.ppt,.pptx';\n    } else {\n      this.fileInput.nativeElement.accept = '*/*';\n    }\n    this.fileInput.nativeElement.click();\n  }\n  // === MÉTHODES POUR LE TEMPLATE ===\n  goBackToConversations() {\n    // Navigation vers la liste des conversations\n    window.history.back();\n  }\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const now = new Date();\n    const lastActiveDate = new Date(lastActive);\n    const diffInMinutes = Math.floor((now.getTime() - lastActiveDate.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'En ligne';\n    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;\n    if (diffInMinutes < 1440) return `Il y a ${Math.floor(diffInMinutes / 60)} h`;\n    return `Il y a ${Math.floor(diffInMinutes / 1440)} j`;\n  }\n  toggleMainMenu() {\n    // Toggle du menu principal\n    this.showAttachmentMenu = false;\n    this.showEmojiPicker = false;\n  }\n  onScroll(event) {\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  trackByMessageId(index, message) {\n    return message.id || index;\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) return \"Aujourd'hui\";\n    if (date.toDateString() === yesterday.toDateString()) return 'Hier';\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  onMessageClick(message, event) {\n    // Gestion du clic sur un message\n    console.log('Message clicked:', message);\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    // Gestion du menu contextuel\n    console.log('Message context menu:', message);\n  }\n  shouldShowAvatar(index) {\n    if (index === this.messages.length - 1) return true;\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    return !nextMessage || currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  openUserProfile(userId) {\n    // Navigation vers le profil utilisateur\n    console.log('Open user profile:', userId);\n  }\n  isGroupConversation() {\n    return this.conversation?.participants?.length > 2;\n  }\n  shouldShowSenderName(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getUserColor(userId) {\n    // Génère une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];\n    const hash = userId.split('').reduce((a, b) => {\n      a = (a << 5) - a + b.charCodeAt(0);\n      return a & a;\n    }, 0);\n    return colors[Math.abs(hash) % colors.length];\n  }\n  getMessageType(message) {\n    if (message.file) {\n      if (message.file.type?.startsWith('image/')) return 'image';\n      if (message.file.type?.startsWith('video/')) return 'video';\n      if (message.file.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    return 'text';\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Formatage basique du contenu (liens, emojis, etc.)\n    return content.replace(/\\n/g, '<br>');\n  }\n  hasImage(message) {\n    return message.file && message.file.type?.startsWith('image/');\n  }\n  openImageViewer(message) {\n    // Ouvre la visionneuse d'images\n    console.log('Open image viewer:', message);\n  }\n  getImageUrl(message) {\n    return message.file?.url || message.file?.path || '';\n  }\n  hasFile(message) {\n    return message.file && !message.file.type?.startsWith('image/');\n  }\n  downloadFile(message) {\n    if (message.file?.url) {\n      const link = document.createElement('a');\n      link.href = message.file.url;\n      link.download = message.file.name || 'file';\n      link.click();\n    }\n  }\n  getFileIcon(message) {\n    if (!message.file?.type) return 'fas fa-file';\n    if (message.file.type.startsWith('video/')) return 'fas fa-video';\n    if (message.file.type.startsWith('audio/')) return 'fas fa-music';\n    if (message.file.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (message.file.type.includes('word')) return 'fas fa-file-word';\n    if (message.file.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getFileName(message) {\n    return message.file?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    if (!message.file?.size) return '';\n    return this.formatFileSize(message.file.size);\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    return new Date(timestamp).toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // === MÉTHODES D'INTERACTION ===\n  toggleReaction(messageId, emoji) {\n    // Gestion des réactions aux messages\n    console.log('Toggle reaction:', messageId, emoji);\n  }\n  hasUserReacted(reaction, userId) {\n    return reaction.users?.includes(userId) || false;\n  }\n  onInputChange(event) {\n    // Gestion du changement de texte dans l'input\n    const value = event.target.value;\n    if (value.trim() && !this.isTyping) {\n      this.isTyping = true;\n      // Envoyer signal de frappe\n    } else if (!value.trim() && this.isTyping) {\n      this.isTyping = false;\n      // Arrêter signal de frappe\n    }\n  }\n\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Marquer les messages comme lus\n    this.markMessagesAsRead();\n  }\n  onInputBlur() {\n    // Arrêter le signal de frappe\n    this.isTyping = false;\n  }\n  markMessagesAsRead() {\n    // Marquer tous les messages non lus comme lus\n    console.log('Mark messages as read');\n  }\n  openCamera() {\n    // Ouvrir la caméra pour prendre une photo\n    console.log('Open camera');\n  }\n  // === GESTION DES ERREURS ===\n  handleError(error, context) {\n    console.error(`Erreur dans ${context}:`, error);\n    let message = \"Une erreur inattendue s'est produite\";\n    if (error?.message) {\n      message = error.message;\n    } else if (typeof error === 'string') {\n      message = error;\n    }\n    this.toastService.showError(message);\n  }\n  // === NETTOYAGE ===\n  ngOnDestroy() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    this.subscriptions.unsubscribe();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 47,\n      vars: 29,\n      consts: [[1, \"flex\", \"flex-col\", \"h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-green-50\", \"dark:from-gray-900\", \"dark:to-gray-800\"], [1, \"flex\", \"items-center\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-gray-800\", \"border-b\", \"border-gray-200\", \"dark:border-gray-700\", \"shadow-sm\"], [1, \"p-2\", \"mr-3\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"text-gray-600\", \"dark:text-gray-300\"], [1, \"flex\", \"items-center\", \"flex-1\", \"min-w-0\"], [1, \"relative\", \"mr-3\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-green-500\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [\"class\", \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\", 4, \"ngIf\"], [1, \"flex-1\", \"min-w-0\"], [1, \"font-semibold\", \"text-gray-900\", \"dark:text-white\", \"truncate\"], [1, \"text-sm\", \"text-gray-500\", \"dark:text-gray-400\"], [\"class\", \"flex items-center gap-1 text-green-600\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"items-center\", \"gap-2\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Appel vocal\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Rechercher\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Menu\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"flex-1\", \"overflow-y-auto\", \"p-4\", \"space-y-4\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"flex flex-col items-center justify-center py-8\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [1, \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"p-4\"], [1, \"flex\", \"items-end\", \"gap-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"gap-2\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Joindre un fichier\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [1, \"flex-1\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", \"rows\", \"1\", \"maxlength\", \"4096\", \"autocomplete\", \"off\", \"spellcheck\", \"true\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-gray-50\", \"dark:bg-gray-700\", \"border\", \"border-gray-200\", \"dark:border-gray-600\", \"rounded-2xl\", \"resize-none\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-green-500\", \"focus:border-transparent\", \"text-gray-900\", \"dark:text-white\", \"placeholder-gray-500\", \"dark:placeholder-gray-400\", 3, \"disabled\", \"input\", \"keydown\", \"focus\", \"blur\"], [\"messageTextarea\", \"\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\", \"title\", \"Maintenir pour enregistrer\", 3, \"bg-red-500\", \"hover:bg-red-600\", \"animate-pulse\", \"mousedown\", \"mouseup\", \"mouseleave\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\", \"title\", \"Envoyer\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 1, \"hidden\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-25 z-40\", 3, \"click\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"right-0\", \"w-3\", \"h-3\", \"bg-green-500\", \"border-2\", \"border-white\", \"dark:border-gray-800\", \"rounded-full\", \"animate-pulse\"], [1, \"flex\", \"items-center\", \"gap-1\", \"text-green-600\"], [1, \"flex\", \"gap-1\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-green-500\", \"mb-4\"], [1, \"text-gray-500\", \"dark:text-gray-400\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"text-6xl\", \"text-gray-300\", \"dark:text-gray-600\", \"mb-4\"], [1, \"fas\", \"fa-comments\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-700\", \"dark:text-gray-300\", \"mb-2\"], [1, \"text-gray-500\", \"dark:text-gray-400\", \"text-center\"], [1, \"space-y-2\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"flex items-start gap-2\", 4, \"ngIf\"], [\"class\", \"flex justify-center my-4\", 4, \"ngIf\"], [1, \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"class\", \"mr-2 flex-shrink-0\", 4, \"ngIf\"], [1, \"max-w-xs\", \"lg:max-w-md\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\", \"relative\", \"group\"], [\"class\", \"text-xs font-semibold mb-1 opacity-75\", 3, \"color\", 4, \"ngIf\"], [\"class\", \"break-words\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-end\", \"gap-1\", \"mt-1\", \"text-xs\", \"opacity-75\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex gap-1 mt-2\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-4\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-3\", \"py-1\", \"rounded-full\", \"shadow-sm\"], [1, \"text-xs\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"mr-2\", \"flex-shrink-0\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [1, \"text-xs\", \"font-semibold\", \"mb-1\", \"opacity-75\"], [1, \"break-words\"], [3, \"innerHTML\"], [1, \"relative\", \"cursor-pointer\", \"rounded-lg\", \"overflow-hidden\", 3, \"click\"], [1, \"max-w-full\", \"h-auto\", \"rounded-lg\", \"hover:opacity-90\", \"transition-opacity\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"hover:bg-opacity-10\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-expand\", \"text-white\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [\"class\", \"text-sm\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"text-sm\", 3, \"innerHTML\"], [1, \"flex\", \"items-center\", \"gap-3\", \"p-2\", \"bg-gray-50\", \"dark:bg-gray-600\", \"rounded-lg\", \"cursor-pointer\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"text-2xl\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"font-medium\", \"text-sm\", \"truncate\"], [1, \"p-1\", \"rounded-full\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-400\", \"transition-colors\"], [1, \"fas\", \"fa-download\", \"text-sm\"], [1, \"flex\", \"items-center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double text-blue-400\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", \"text-blue-400\"], [1, \"flex\", \"gap-1\", \"mt-2\"], [\"class\", \"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\", 3, \"bg-green-100\", \"text-green-600\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"gap-1\", \"px-2\", \"py-1\", \"bg-gray-100\", \"dark:bg-gray-600\", \"rounded-full\", \"text-xs\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", 3, \"mousedown\", \"mouseup\", \"mouseleave\"], [1, \"fas\", \"fa-microphone\"], [\"type\", \"button\", \"title\", \"Envoyer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"right-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"p-4\"], [1, \"flex\", \"gap-2\", \"mb-4\", \"overflow-x-auto\"], [\"class\", \"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\", 3, \"bg-green-100\", \"text-green-600\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-8\", \"gap-2\", \"max-h-48\", \"overflow-y-auto\"], [\"class\", \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-3\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex-shrink-0\", 3, \"click\"], [1, \"p-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-xl\", 3, \"title\", \"click\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"grid\", \"grid-cols-2\", \"gap-3\"], [1, \"flex\", \"flex-col\", \"items-center\", \"gap-2\", \"p-4\", \"rounded-xl\", \"hover:bg-gray-50\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"w-12\", \"h-12\", \"bg-blue-100\", \"dark:bg-blue-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-image\", \"text-blue-600\", \"dark:text-blue-400\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"dark:text-gray-300\"], [1, \"w-12\", \"h-12\", \"bg-purple-100\", \"dark:bg-purple-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-video\", \"text-purple-600\", \"dark:text-purple-400\"], [1, \"w-12\", \"h-12\", \"bg-orange-100\", \"dark:bg-orange-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-file\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"w-12\", \"h-12\", \"bg-green-100\", \"dark:bg-green-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-camera\", \"text-green-600\", \"dark:text-green-400\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-25\", \"z-40\", 3, \"click\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"img\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_6_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MessageChatComponent_div_7_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"h3\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 7, 0, \"div\", 11);\n          i0.ɵɵtemplate(13, MessageChatComponent_span_13_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_15_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_17_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(18, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"main\", 22, 23);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_23_listener($event) {\n            return ctx.onScroll($event);\n          });\n          i0.ɵɵtemplate(25, MessageChatComponent_div_25_Template, 4, 0, \"div\", 24);\n          i0.ɵɵtemplate(26, MessageChatComponent_div_26_Template, 7, 1, \"div\", 25);\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 3, 3, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"footer\", 27)(29, \"form\", 28);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_29_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(30, \"div\", 29)(31, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_31_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(32, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_33_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(34, \"i\", 33);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 34)(36, \"textarea\", 35, 36);\n          i0.ɵɵlistener(\"input\", function MessageChatComponent_Template_textarea_input_36_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"keydown\", function MessageChatComponent_Template_textarea_keydown_36_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_36_listener() {\n            return ctx.onInputFocus();\n          })(\"blur\", function MessageChatComponent_Template_textarea_blur_36_listener() {\n            return ctx.onInputBlur();\n          });\n          i0.ɵɵtext(38, \"        \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 29);\n          i0.ɵɵtemplate(40, MessageChatComponent_button_40_Template, 2, 6, \"button\", 37);\n          i0.ɵɵtemplate(41, MessageChatComponent_button_41_Template, 3, 3, \"button\", 38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(42, MessageChatComponent_div_42_Template, 6, 2, \"div\", 39);\n          i0.ɵɵtemplate(43, MessageChatComponent_div_43_Template, 23, 0, \"div\", 40);\n          i0.ɵɵelementStart(44, \"input\", 41, 42);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_44_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, MessageChatComponent_div_46_Template, 1, 0, \"div\", 43);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_17_0;\n          let tmp_18_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.searchMode)(\"text-green-600\", ctx.searchMode);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showEmojiPicker)(\"text-green-600\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showAttachmentMenu)(\"text-green-600\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.otherParticipant || ctx.isRecordingVoice || ctx.isSendingMessage);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_17_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_17_0.value == null ? null : tmp_17_0.value.trim()));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_18_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_18_0.value == null ? null : tmp_18_0.value.trim());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ctx_r5", "username", "ctx_r18", "formatDateSeparator", "message_r16", "timestamp", "ɵɵlistener", "MessageChatComponent_div_27_ng_container_1_div_3_Template_img_click_1_listener", "ɵɵrestoreView", "_r29", "ɵɵnextContext", "$implicit", "ctx_r27", "ɵɵresetView", "openUserProfile", "sender", "id", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r20", "getUserColor", "ctx_r21", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r33", "MessageChatComponent_div_27_ng_container_1_div_7_Template_div_click_1_listener", "_r37", "ctx_r35", "openImageViewer", "ɵɵtemplate", "MessageChatComponent_div_27_ng_container_1_div_7_div_5_Template", "ctx_r22", "getImageUrl", "MessageChatComponent_div_27_ng_container_1_div_8_Template_div_click_0_listener", "_r41", "ctx_r39", "downloadFile", "ɵɵclassMap", "ctx_r23", "getFileIcon", "getFileName", "getFileSize", "MessageChatComponent_div_27_ng_container_1_div_12_i_1_Template", "MessageChatComponent_div_27_ng_container_1_div_12_i_2_Template", "MessageChatComponent_div_27_ng_container_1_div_12_i_3_Template", "MessageChatComponent_div_27_ng_container_1_div_12_i_4_Template", "status", "MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template_button_click_0_listener", "restoredCtx", "_r52", "reaction_r49", "ctx_r50", "toggleReaction", "emoji", "ɵɵclassProp", "ctx_r48", "hasUserReacted", "currentUserId", "ɵɵtextInterpolate", "MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template", "reactions", "ɵɵelementContainerStart", "MessageChatComponent_div_27_ng_container_1_div_1_Template", "MessageChatComponent_div_27_ng_container_1_Template_div_click_2_listener", "$event", "_r55", "ctx_r54", "onMessageClick", "MessageChatComponent_div_27_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r56", "onMessageContextMenu", "MessageChatComponent_div_27_ng_container_1_div_3_Template", "MessageChatComponent_div_27_ng_container_1_div_5_Template", "MessageChatComponent_div_27_ng_container_1_div_6_Template", "MessageChatComponent_div_27_ng_container_1_div_7_Template", "MessageChatComponent_div_27_ng_container_1_div_8_Template", "MessageChatComponent_div_27_ng_container_1_div_12_Template", "MessageChatComponent_div_27_ng_container_1_div_13_Template", "ɵɵelementContainerEnd", "ctx_r14", "shouldShowDateSeparator", "i_r17", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "hasFile", "formatMessageTime", "length", "ctx_r15", "MessageChatComponent_div_27_ng_container_1_Template", "MessageChatComponent_div_27_div_2_Template", "ctx_r6", "messages", "trackByMessageId", "isTyping", "MessageChatComponent_button_40_Template_button_mousedown_0_listener", "_r58", "ctx_r57", "startVoiceRecording", "MessageChatComponent_button_40_Template_button_mouseup_0_listener", "ctx_r59", "stopVoiceRecording", "MessageChatComponent_button_40_Template_button_mouseleave_0_listener", "ctx_r60", "cancelVoiceRecording", "ctx_r8", "isRecordingVoice", "MessageChatComponent_button_41_Template_button_click_0_listener", "_r64", "ctx_r63", "sendMessage", "MessageChatComponent_button_41_i_1_Template", "MessageChatComponent_button_41_i_2_Template", "ctx_r9", "isSendingMessage", "MessageChatComponent_div_42_button_3_Template_button_click_0_listener", "_r69", "category_r67", "ctx_r68", "selectEmojiCategory", "ctx_r65", "selectedEmojiCategory", "icon", "MessageChatComponent_div_42_button_5_Template_button_click_0_listener", "_r72", "emoji_r70", "ctx_r71", "insert<PERSON><PERSON><PERSON>", "name", "MessageChatComponent_div_42_button_3_Template", "MessageChatComponent_div_42_button_5_Template", "ctx_r10", "emojiCategories", "getEmojisForCategory", "MessageChatComponent_div_43_Template_button_click_3_listener", "_r74", "ctx_r73", "triggerFileInput", "MessageChatComponent_div_43_Template_button_click_8_listener", "ctx_r75", "MessageChatComponent_div_43_Template_button_click_13_listener", "ctx_r76", "MessageChatComponent_div_43_Template_button_click_18_listener", "ctx_r77", "openCamera", "MessageChatComponent_div_46_Template_div_click_0_listener", "_r79", "ctx_r78", "closeAllMenus", "MessageChatComponent", "constructor", "fb", "route", "MessageService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "voiceRecordingDuration", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "isInCall", "callType", "callDuration", "callTimer", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isUserTyping", "searchMode", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "initializeComponent", "loadCurrentUser", "loadConversation", "setupSubscriptions", "user", "JSON", "parse", "localStorage", "getItem", "_id", "error", "console", "conversationId", "snapshot", "paramMap", "get", "showError", "getConversation", "subscribe", "next", "setOtherParticipant", "loadMessages", "scrollToBottom", "participants", "log", "find", "p", "valid", "value", "trim", "receiverId", "undefined", "message", "push", "reset", "onFileSelected", "event", "files", "target", "file", "uploadFile", "showSuccess", "_this", "_asyncToGenerator", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "MediaRecorder", "mimeType", "setInterval", "detectChanges", "ondataavailable", "data", "size", "onstop", "processRecordedAudio", "start", "state", "stop", "getTracks", "for<PERSON>ach", "track", "clearInterval", "_this2", "showWarning", "audioBlob", "Blob", "type", "audioFile", "File", "Date", "now", "sendVoiceMessage", "_this3", "Error", "Promise", "resolve", "reject", "formatRecordingDuration", "duration", "minutes", "Math", "floor", "seconds", "toString", "padStart", "category", "currentC<PERSON>nt", "newContent", "patchValue", "setTimeout", "textarea", "document", "querySelector", "focus", "toggleEmojiPicker", "startCall", "endCall", "startVideoCall", "startVoiceCall", "toggleSearch", "searchMessages", "filter", "toLowerCase", "includes", "loadMoreMessages", "newMessages", "shareLocation", "_this4", "position", "getCurrentPosition", "latitude", "longitude", "coords", "locationMessage", "address", "getAddressFromCoordinates", "mapUrl", "sendLocationMessage", "geolocation", "enableHighAccuracy", "timeout", "maximumAge", "lat", "lng", "response", "fetch", "ok", "json", "display_name", "locationData", "_this5", "shareContact", "_this6", "contact", "selectContact", "sendContactMessage", "phone", "email", "avatar", "contactData", "_this7", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "formatFileSize", "bytes", "k", "sizes", "i", "parseFloat", "pow", "toFixed", "formatDuration", "hours", "secs", "getFileAcceptTypes", "toggleAttachmentMenu", "fileInput", "accept", "click", "goBackToConversations", "window", "history", "back", "lastActiveDate", "diffInMinutes", "getTime", "toggleMainMenu", "onScroll", "index", "currentMessage", "previousMessage", "currentDate", "toDateString", "previousDate", "date", "today", "yesterday", "setDate", "getDate", "toLocaleDateString", "weekday", "year", "month", "day", "preventDefault", "nextMessage", "userId", "colors", "hash", "split", "reduce", "a", "b", "charCodeAt", "abs", "startsWith", "replace", "url", "path", "link", "createElement", "href", "download", "toLocaleTimeString", "hour", "minute", "messageId", "reaction", "users", "onInputChange", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "markMessagesAsRead", "onInputBlur", "handleError", "context", "ngOnDestroy", "clearTimeout", "unsubscribe", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "i3", "i4", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_Template_img_click_6_listener", "MessageChatComponent_div_7_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_span_13_Template", "MessageChatComponent_Template_button_click_15_listener", "MessageChatComponent_Template_button_click_17_listener", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_Template_main_scroll_23_listener", "MessageChatComponent_div_25_Template", "MessageChatComponent_div_26_Template", "MessageChatComponent_div_27_Template", "MessageChatComponent_Template_form_ngSubmit_29_listener", "MessageChatComponent_Template_button_click_31_listener", "MessageChatComponent_Template_button_click_33_listener", "MessageChatComponent_Template_textarea_input_36_listener", "MessageChatComponent_Template_textarea_keydown_36_listener", "MessageChatComponent_Template_textarea_focus_36_listener", "MessageChatComponent_Template_textarea_blur_36_listener", "MessageChatComponent_button_40_Template", "MessageChatComponent_button_41_Template", "MessageChatComponent_div_42_Template", "MessageChatComponent_div_43_Template", "MessageChatComponent_Template_input_change_44_listener", "MessageChatComponent_div_46_Template", "tmp_17_0", "tmp_18_0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Component,\n  On<PERSON>ni<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>hild,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { ToastService } from '../../../../services/toast.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent implements OnInit, OnDestroy {\n  // === RÉFÉRENCES DOM ===\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === DONNÉES PRINCIPALES ===\n  conversation: any = null;\n  messages: any[] = [];\n  currentUserId: string | null = null;\n  currentUsername = 'You';\n  messageForm: FormGroup;\n  otherParticipant: any = null;\n\n  // === ÉTATS DE L'INTERFACE ===\n  isLoading = false;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  showEmojiPicker = false;\n  showAttachmentMenu = false;\n  showSearch = false;\n  searchQuery = '';\n  searchResults: any[] = [];\n\n  // === ENREGISTREMENT VOCAL ===\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n\n  // === APPELS ===\n  isInCall = false;\n  callType: 'VIDEO' | 'AUDIO' | null = null;\n  callDuration = 0;\n  private callTimer: any = null;\n\n  // === ÉMOJIS ===\n  emojiCategories: any[] = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n      ],\n    },\n  ];\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  // === PAGINATION ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n\n  // === AUTRES ÉTATS ===\n  isTyping = false;\n  isUserTyping = false;\n  searchMode = false;\n  isSendingMessage = false;\n  private typingTimeout: any = null;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private MessageService: MessageService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  ngOnInit(): void {\n    this.initializeComponent();\n  }\n\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      const user = JSON.parse(localStorage.getItem('user') || '{}');\n      if (user && user._id) {\n        this.currentUserId = user._id;\n        this.currentUsername = user.username || 'You';\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement de l'utilisateur:\", error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  private loadConversation(): void {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n\n    this.isLoading = true;\n    // Utiliser getConversation avec les paramètres de pagination\n    this.MessageService.getConversation(\n      conversationId,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors du chargement de la conversation'\n        );\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private loadMessages(): void {\n    if (!this.conversation?.id) return;\n\n    // Les messages sont déjà chargés avec la conversation\n    this.messages = this.conversation.messages || [];\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n\n  private setOtherParticipant(): void {\n    if (this.conversation?.participants) {\n      console.log('Setting other participant...');\n      console.log('Current user ID:', this.currentUserId);\n      console.log('All participants:', this.conversation.participants);\n\n      this.otherParticipant = this.conversation.participants.find(\n        (p: any) => (p.id || p._id) !== this.currentUserId\n      );\n\n      console.log('Other participant found:', this.otherParticipant);\n\n      // Si otherParticipant n'est pas trouvé, essayons avec une logique différente\n      if (!this.otherParticipant && this.conversation.participants.length > 0) {\n        console.log('Fallback: using first participant');\n        this.otherParticipant = this.conversation.participants[0];\n      }\n    }\n  }\n\n  private setupSubscriptions(): void {\n    // Abonnements GraphQL pour les mises à jour en temps réel\n    // À implémenter selon vos besoins\n  }\n\n  // === ENVOI DE MESSAGES ===\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n\n    const otherParticipant = this.conversation.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    this.MessageService.sendMessage(\n      receiverId,\n      content,\n      undefined,\n      'TEXT' as any,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        this.messages.push(message);\n        this.messageForm.reset();\n        this.scrollToBottom();\n      },\n      error: (error: any) => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n      },\n    });\n  }\n\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event: any): void {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n\n    for (let file of files) {\n      this.uploadFile(file);\n    }\n  }\n\n  private uploadFile(file: File): void {\n    const otherParticipant = this.conversation?.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    this.MessageService.sendMessage(\n      receiverId,\n      '',\n      file,\n      undefined,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        this.messages.push(message);\n        this.scrollToBottom();\n        this.toastService.showSuccess('Fichier envoyé avec succès');\n      },\n      error: (error: any) => {\n        console.error(\"Erreur lors de l'envoi du fichier:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n      },\n    });\n  }\n\n  // === ENREGISTREMENT VOCAL ===\n  async startVoiceRecording(): Promise<void> {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n        },\n      });\n\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus',\n      });\n\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        this.cdr.detectChanges();\n      }, 1000);\n\n      this.mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      this.mediaRecorder.onstop = () => {\n        this.processRecordedAudio();\n      };\n\n      this.mediaRecorder.start(100);\n      this.toastService.showSuccess('Enregistrement vocal démarré');\n    } catch (error) {\n      console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n      this.toastService.showError(\"Impossible d'accéder au microphone\");\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    try {\n      if (this.audioChunks.length === 0) {\n        this.toastService.showWarning('Aucun audio enregistré');\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      const audioBlob = new Blob(this.audioChunks, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      if (this.voiceRecordingDuration < 1) {\n        this.toastService.showWarning(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      await this.sendVoiceMessage(audioFile);\n      this.toastService.showSuccess('Message vocal envoyé');\n    } catch (error) {\n      console.error(\"Erreur lors du traitement de l'audio:\", error);\n      this.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n    } finally {\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    const otherParticipant = this.conversation?.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        '',\n        audioFile,\n        undefined,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === SYSTÈME D'ÉMOJIS ===\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + (emoji.emoji || emoji);\n    this.messageForm.patchValue({ content: newContent });\n\n    this.showEmojiPicker = false;\n\n    setTimeout(() => {\n      const textarea = document.querySelector(\n        'textarea[formControlName=\"content\"]'\n      ) as HTMLTextAreaElement;\n      if (textarea) {\n        textarea.focus();\n      }\n    }, 100);\n  }\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    this.showAttachmentMenu = false;\n  }\n\n  // === APPELS VIDÉO/AUDIO ===\n  startCall(type: 'VIDEO' | 'AUDIO'): void {\n    this.callType = type;\n    this.isInCall = true;\n    this.callDuration = 0;\n\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n\n    this.toastService.showSuccess(\n      `Appel ${type === 'VIDEO' ? 'vidéo' : 'audio'} démarré`\n    );\n  }\n\n  endCall(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.toastService.showSuccess('Appel terminé');\n  }\n\n  startVideoCall(): void {\n    this.startCall('VIDEO');\n  }\n\n  startVoiceCall(): void {\n    this.startCall('AUDIO');\n  }\n\n  // === RECHERCHE ===\n  toggleSearch(): void {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n\n  searchMessages(): void {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n\n    this.searchResults = this.messages.filter(\n      (message) =>\n        message.content\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase()) ||\n        message.sender?.username\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase())\n    );\n  }\n\n  // === PAGINATION ===\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\n      return;\n\n    this.isLoadingMore = true;\n    this.currentPage++;\n\n    this.MessageService.getConversation(\n      this.conversation.id,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        const newMessages = conversation.messages || [];\n        if (newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début (messages plus anciens)\n          this.messages = [...newMessages, ...this.messages];\n          this.hasMoreMessages =\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      },\n    });\n  }\n\n  // === GÉOLOCALISATION ===\n  async shareLocation(): Promise<void> {\n    try {\n      const position = await this.getCurrentPosition();\n      const { latitude, longitude } = position.coords;\n\n      const locationMessage = {\n        type: 'location',\n        latitude,\n        longitude,\n        address: await this.getAddressFromCoordinates(latitude, longitude),\n        mapUrl: `https://maps.google.com/maps?q=${latitude},${longitude}&z=15`,\n        timestamp: new Date(),\n      };\n\n      await this.sendLocationMessage(locationMessage);\n      this.toastService.showSuccess('Position partagée avec succès');\n    } catch (error) {\n      console.error('Erreur lors du partage de localisation:', error);\n      this.toastService.showError(\"Impossible d'obtenir votre position\");\n    }\n  }\n\n  private getCurrentPosition(): Promise<GeolocationPosition> {\n    return new Promise((resolve, reject) => {\n      navigator.geolocation.getCurrentPosition(resolve, reject, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 60000,\n      });\n    });\n  }\n\n  private async getAddressFromCoordinates(\n    lat: number,\n    lng: number\n  ): Promise<string> {\n    try {\n      const response = await fetch(\n        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        return data.display_name || `${lat}, ${lng}`;\n      }\n    } catch (error) {\n      console.error('Erreur lors du géocodage inverse:', error);\n    }\n\n    return `${lat}, ${lng}`;\n  }\n\n  private async sendLocationMessage(locationData: any): Promise<void> {\n    const otherParticipant = this.conversation?.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        `📍 Position partagée: ${locationData.address}`,\n        undefined,\n        'TEXT' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\n            \"Erreur lors de l'envoi du message de localisation:\",\n            error\n          );\n          reject(error);\n        },\n      });\n    });\n  }\n\n  // === PARTAGE DE CONTACTS ===\n  async shareContact(): Promise<void> {\n    try {\n      const contact = await this.selectContact();\n\n      if (contact) {\n        await this.sendContactMessage(contact);\n        this.toastService.showSuccess('Contact partagé avec succès');\n      }\n    } catch (error) {\n      console.error('Erreur lors du partage de contact:', error);\n      this.toastService.showError('Erreur lors du partage du contact');\n    }\n  }\n\n  private async selectContact(): Promise<any> {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        resolve({\n          name: 'John Doe',\n          phone: '+33 6 12 34 56 78',\n          email: '<EMAIL>',\n          avatar: 'assets/images/default-avatar.png',\n        });\n      }, 1000);\n    });\n  }\n\n  private async sendContactMessage(contactData: any): Promise<void> {\n    const otherParticipant = this.conversation?.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        `👤 Contact partagé: ${contactData.name}\\n📞 ${contactData.phone}\\n📧 ${contactData.email}`,\n        undefined,\n        'TEXT' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message de contact:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  // === UTILITAIRES ===\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  formatDuration(seconds: number): string {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs\n        .toString()\n        .padStart(2, '0')}`;\n    }\n\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n\n  triggerFileInput(type?: string): void {\n    // Optionnel : configurer le type de fichier accepté selon le type\n    if (type === 'image') {\n      this.fileInput.nativeElement.accept = 'image/*';\n    } else if (type === 'video') {\n      this.fileInput.nativeElement.accept = 'video/*';\n    } else if (type === 'document') {\n      this.fileInput.nativeElement.accept =\n        '.pdf,.doc,.docx,.txt,.xlsx,.ppt,.pptx';\n    } else {\n      this.fileInput.nativeElement.accept = '*/*';\n    }\n\n    this.fileInput.nativeElement.click();\n  }\n\n  // === MÉTHODES POUR LE TEMPLATE ===\n  goBackToConversations(): void {\n    // Navigation vers la liste des conversations\n    window.history.back();\n  }\n\n  formatLastActive(lastActive: any): string {\n    if (!lastActive) return 'Hors ligne';\n    const now = new Date();\n    const lastActiveDate = new Date(lastActive);\n    const diffInMinutes = Math.floor(\n      (now.getTime() - lastActiveDate.getTime()) / (1000 * 60)\n    );\n\n    if (diffInMinutes < 1) return 'En ligne';\n    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;\n    if (diffInMinutes < 1440)\n      return `Il y a ${Math.floor(diffInMinutes / 60)} h`;\n    return `Il y a ${Math.floor(diffInMinutes / 1440)} j`;\n  }\n\n  toggleMainMenu(): void {\n    // Toggle du menu principal\n    this.showAttachmentMenu = false;\n    this.showEmojiPicker = false;\n  }\n\n  onScroll(event: any): void {\n    const element = event.target;\n    if (\n      element.scrollTop === 0 &&\n      this.hasMoreMessages &&\n      !this.isLoadingMore\n    ) {\n      this.loadMoreMessages();\n    }\n  }\n\n  trackByMessageId(index: number, message: any): any {\n    return message.id || index;\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  formatDateSeparator(timestamp: any): string {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) return \"Aujourd'hui\";\n    if (date.toDateString() === yesterday.toDateString()) return 'Hier';\n\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  }\n\n  onMessageClick(message: any, event: any): void {\n    // Gestion du clic sur un message\n    console.log('Message clicked:', message);\n  }\n\n  onMessageContextMenu(message: any, event: any): void {\n    event.preventDefault();\n    // Gestion du menu contextuel\n    console.log('Message context menu:', message);\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    if (index === this.messages.length - 1) return true;\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n\n    return !nextMessage || currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n\n  openUserProfile(userId: string): void {\n    // Navigation vers le profil utilisateur\n    console.log('Open user profile:', userId);\n  }\n\n  isGroupConversation(): boolean {\n    return this.conversation?.participants?.length > 2;\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n\n  getUserColor(userId: string): string {\n    // Génère une couleur basée sur l'ID utilisateur\n    const colors = [\n      '#FF6B6B',\n      '#4ECDC4',\n      '#45B7D1',\n      '#96CEB4',\n      '#FFEAA7',\n      '#DDA0DD',\n    ];\n    const hash = userId.split('').reduce((a, b) => {\n      a = (a << 5) - a + b.charCodeAt(0);\n      return a & a;\n    }, 0);\n    return colors[Math.abs(hash) % colors.length];\n  }\n\n  getMessageType(message: any): string {\n    if (message.file) {\n      if (message.file.type?.startsWith('image/')) return 'image';\n      if (message.file.type?.startsWith('video/')) return 'video';\n      if (message.file.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    return 'text';\n  }\n\n  formatMessageContent(content: string): string {\n    if (!content) return '';\n    // Formatage basique du contenu (liens, emojis, etc.)\n    return content.replace(/\\n/g, '<br>');\n  }\n\n  hasImage(message: any): boolean {\n    return message.file && message.file.type?.startsWith('image/');\n  }\n\n  openImageViewer(message: any): void {\n    // Ouvre la visionneuse d'images\n    console.log('Open image viewer:', message);\n  }\n\n  getImageUrl(message: any): string {\n    return message.file?.url || message.file?.path || '';\n  }\n\n  hasFile(message: any): boolean {\n    return message.file && !message.file.type?.startsWith('image/');\n  }\n\n  downloadFile(message: any): void {\n    if (message.file?.url) {\n      const link = document.createElement('a');\n      link.href = message.file.url;\n      link.download = message.file.name || 'file';\n      link.click();\n    }\n  }\n\n  getFileIcon(message: any): string {\n    if (!message.file?.type) return 'fas fa-file';\n\n    if (message.file.type.startsWith('video/')) return 'fas fa-video';\n    if (message.file.type.startsWith('audio/')) return 'fas fa-music';\n    if (message.file.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (message.file.type.includes('word')) return 'fas fa-file-word';\n    if (message.file.type.includes('excel')) return 'fas fa-file-excel';\n\n    return 'fas fa-file';\n  }\n\n  getFileName(message: any): string {\n    return message.file?.name || 'Fichier';\n  }\n\n  getFileSize(message: any): string {\n    if (!message.file?.size) return '';\n    return this.formatFileSize(message.file.size);\n  }\n\n  formatMessageTime(timestamp: any): string {\n    if (!timestamp) return '';\n    return new Date(timestamp).toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  toggleReaction(messageId: string, emoji: string): void {\n    // Gestion des réactions aux messages\n    console.log('Toggle reaction:', messageId, emoji);\n  }\n\n  hasUserReacted(reaction: any, userId: string): boolean {\n    return reaction.users?.includes(userId) || false;\n  }\n\n  onInputChange(event: any): void {\n    // Gestion du changement de texte dans l'input\n    const value = event.target.value;\n    if (value.trim() && !this.isTyping) {\n      this.isTyping = true;\n      // Envoyer signal de frappe\n    } else if (!value.trim() && this.isTyping) {\n      this.isTyping = false;\n      // Arrêter signal de frappe\n    }\n  }\n\n  onInputKeyDown(event: any): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  onInputFocus(): void {\n    // Marquer les messages comme lus\n    this.markMessagesAsRead();\n  }\n\n  onInputBlur(): void {\n    // Arrêter le signal de frappe\n    this.isTyping = false;\n  }\n\n  private markMessagesAsRead(): void {\n    // Marquer tous les messages non lus comme lus\n    console.log('Mark messages as read');\n  }\n\n  openCamera(): void {\n    // Ouvrir la caméra pour prendre une photo\n    console.log('Open camera');\n  }\n\n  // === GESTION DES ERREURS ===\n  handleError(error: any, context: string): void {\n    console.error(`Erreur dans ${context}:`, error);\n    let message = \"Une erreur inattendue s'est produite\";\n\n    if (error?.message) {\n      message = error.message;\n    } else if (typeof error === 'string') {\n      message = error;\n    }\n\n    this.toastService.showError(message);\n  }\n\n  // === NETTOYAGE ===\n  ngOnDestroy(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    this.subscriptions.unsubscribe();\n  }\n}\n", "<!-- Chat WhatsApp Moderne avec Tailwind CSS -->\n<div\n  class=\"flex flex-col h-screen bg-gradient-to-br from-gray-50 to-green-50 dark:from-gray-900 dark:to-gray-800\"\n>\n  <!-- En-tête -->\n  <header\n    class=\"flex items-center px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm\"\n  >\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      class=\"p-2 mr-3 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n    >\n      <i class=\"fas fa-arrow-left text-gray-600 dark:text-gray-300\"></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div class=\"flex items-center flex-1 min-w-0\">\n      <div class=\"relative mr-3\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-10 h-10 rounded-full object-cover border-2 border-green-500 cursor-pointer hover:scale-105 transition-transform\"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n        />\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\"\n        ></div>\n      </div>\n\n      <div class=\"flex-1 min-w-0\">\n        <h3 class=\"font-semibold text-gray-900 dark:text-white truncate\">\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n          <div\n            *ngIf=\"isUserTyping\"\n            class=\"flex items-center gap-1 text-green-600\"\n          >\n            <span>En train d'écrire</span>\n            <div class=\"flex gap-1\">\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.1s\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.2s\"\n              ></div>\n            </div>\n          </div>\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div class=\"flex items-center gap-2\">\n      <button\n        (click)=\"startVideoCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vidéo\"\n      >\n        <i class=\"fas fa-video\"></i>\n      </button>\n      <button\n        (click)=\"startVoiceCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vocal\"\n      >\n        <i class=\"fas fa-phone\"></i>\n      </button>\n      <button\n        (click)=\"toggleSearch()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        [class.bg-green-100]=\"searchMode\"\n        [class.text-green-600]=\"searchMode\"\n        title=\"Rechercher\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n      <button\n        (click)=\"toggleMainMenu()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Menu\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n  </header>\n\n  <!-- Zone de messages -->\n  <main\n    class=\"flex-1 overflow-y-auto p-4 space-y-4\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n  >\n    <!-- Chargement -->\n    <div\n      *ngIf=\"isLoading\"\n      class=\"flex flex-col items-center justify-center py-8\"\n    >\n      <div\n        class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-4\"\n      ></div>\n      <span class=\"text-gray-500 dark:text-gray-400\"\n        >Chargement des messages...</span\n      >\n    </div>\n\n    <!-- État vide -->\n    <div\n      *ngIf=\"!isLoading && messages.length === 0\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"text-6xl text-gray-300 dark:text-gray-600 mb-4\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3 class=\"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n        Aucun message\n      </h3>\n      <p class=\"text-gray-500 dark:text-gray-400 text-center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages -->\n    <div *ngIf=\"!isLoading && messages.length > 0\" class=\"space-y-2\">\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Séparateur de date -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          class=\"flex justify-center my-4\"\n        >\n          <div\n            class=\"bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-sm\"\n          >\n            <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message -->\n        <div\n          class=\"flex\"\n          [class.justify-end]=\"message.sender?.id === currentUserId\"\n          [class.justify-start]=\"message.sender?.id !== currentUserId\"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar pour les autres -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            class=\"mr-2 flex-shrink-0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              class=\"w-8 h-8 rounded-full object-cover cursor-pointer hover:scale-105 transition-transform\"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n            />\n          </div>\n\n          <!-- Bulle de message -->\n          <div\n            class=\"max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm relative group\"\n            [class.bg-green-500]=\"message.sender?.id === currentUserId\"\n            [class.text-white]=\"message.sender?.id === currentUserId\"\n            [class.bg-white]=\"message.sender?.id !== currentUserId\"\n            [class.text-gray-900]=\"message.sender?.id !== currentUserId\"\n            [class.dark:bg-gray-700]=\"message.sender?.id !== currentUserId\"\n            [class.dark:text-white]=\"message.sender?.id !== currentUserId\"\n          >\n            <!-- Nom expéditeur (groupes) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              class=\"text-xs font-semibold mb-1 opacity-75\"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Contenu texte -->\n            <div *ngIf=\"getMessageType(message) === 'text'\" class=\"break-words\">\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image -->\n            <div *ngIf=\"hasImage(message)\" class=\"space-y-2\">\n              <div\n                class=\"relative cursor-pointer rounded-lg overflow-hidden\"\n                (click)=\"openImageViewer(message)\"\n              >\n                <img\n                  [src]=\"getImageUrl(message)\"\n                  [alt]=\"message.content || 'Image'\"\n                  class=\"max-w-full h-auto rounded-lg hover:opacity-90 transition-opacity\"\n                />\n                <div\n                  class=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all flex items-center justify-center\"\n                >\n                  <i\n                    class=\"fas fa-expand text-white opacity-0 group-hover:opacity-100 transition-opacity\"\n                  ></i>\n                </div>\n              </div>\n              <div\n                *ngIf=\"message.content\"\n                class=\"text-sm\"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Fichier -->\n            <div\n              *ngIf=\"hasFile(message)\"\n              class=\"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\"\n              (click)=\"downloadFile(message)\"\n            >\n              <div class=\"text-2xl text-gray-500 dark:text-gray-400\">\n                <i [class]=\"getFileIcon(message)\"></i>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <div class=\"font-medium text-sm truncate\">\n                  {{ getFileName(message) }}\n                </div>\n                <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ getFileSize(message) }}\n                </div>\n              </div>\n              <button\n                class=\"p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-400 transition-colors\"\n              >\n                <i class=\"fas fa-download text-sm\"></i>\n              </button>\n            </div>\n\n            <!-- Métadonnées -->\n            <div\n              class=\"flex items-center justify-end gap-1 mt-1 text-xs opacity-75\"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                class=\"flex items-center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double text-blue-400\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n\n            <!-- Réactions -->\n            <div\n              *ngIf=\"message.reactions && message.reactions.length > 0\"\n              class=\"flex gap-1 mt-2\"\n            >\n              <button\n                *ngFor=\"let reaction of message.reactions\"\n                (click)=\"toggleReaction(message.id!, reaction.emoji)\"\n                class=\"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\"\n                [class.bg-green-100]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n                [class.text-green-600]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n              >\n                <span>{{ reaction.emoji }}</span>\n                <span>1</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Indicateur de frappe -->\n      <div *ngIf=\"isTyping\" class=\"flex items-start gap-2\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-8 h-8 rounded-full object-cover\"\n        />\n        <div class=\"bg-white dark:bg-gray-700 px-4 py-2 rounded-2xl shadow-sm\">\n          <div class=\"flex gap-1\">\n            <div class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.1s\"\n            ></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.2s\"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- Zone d'input -->\n  <footer\n    class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"flex items-end gap-3\"\n    >\n      <!-- Actions gauche -->\n      <div class=\"flex gap-2\">\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showEmojiPicker\"\n          [class.text-green-600]=\"showEmojiPicker\"\n          title=\"Émojis\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showAttachmentMenu\"\n          [class.text-green-600]=\"showAttachmentMenu\"\n          title=\"Joindre un fichier\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n      </div>\n\n      <!-- Champ de saisie -->\n      <div class=\"flex-1\">\n        <textarea\n          formControlName=\"content\"\n          #messageTextarea\n          placeholder=\"Tapez votre message...\"\n          class=\"w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400\"\n          [disabled]=\"!otherParticipant || isRecordingVoice || isSendingMessage\"\n          (input)=\"onInputChange($event)\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (focus)=\"onInputFocus()\"\n          (blur)=\"onInputBlur()\"\n          rows=\"1\"\n          maxlength=\"4096\"\n          autocomplete=\"off\"\n          spellcheck=\"true\"\n        >\n        </textarea>\n      </div>\n\n      <!-- Actions droite -->\n      <div class=\"flex gap-2\">\n        <!-- Enregistrement vocal -->\n        <button\n          *ngIf=\"!messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (mousedown)=\"startVoiceRecording()\"\n          (mouseup)=\"stopVoiceRecording()\"\n          (mouseleave)=\"cancelVoiceRecording()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\"\n          [class.bg-red-500]=\"isRecordingVoice\"\n          [class.hover:bg-red-600]=\"isRecordingVoice\"\n          [class.animate-pulse]=\"isRecordingVoice\"\n          title=\"Maintenir pour enregistrer\"\n        >\n          <i class=\"fas fa-microphone\"></i>\n        </button>\n\n        <!-- Bouton d'envoi -->\n        <button\n          *ngIf=\"messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (click)=\"sendMessage()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\"\n          [disabled]=\"isSendingMessage\"\n          title=\"Envoyer\"\n        >\n          <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n          <i class=\"fas fa-spinner fa-spin\" *ngIf=\"isSendingMessage\"></i>\n        </button>\n      </div>\n    </form>\n  </footer>\n\n  <!-- Sélecteur d'émojis -->\n  <div\n    *ngIf=\"showEmojiPicker\"\n    class=\"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"flex gap-2 mb-4 overflow-x-auto\">\n        <button\n          *ngFor=\"let category of emojiCategories\"\n          (click)=\"selectEmojiCategory(category)\"\n          class=\"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\"\n          [class.bg-green-100]=\"selectedEmojiCategory === category\"\n          [class.text-green-600]=\"selectedEmojiCategory === category\"\n          [class.hover:bg-gray-100]=\"selectedEmojiCategory !== category\"\n          [class.dark:hover:bg-gray-700]=\"selectedEmojiCategory !== category\"\n        >\n          {{ category.icon }}\n        </button>\n      </div>\n      <div class=\"grid grid-cols-8 gap-2 max-h-48 overflow-y-auto\">\n        <button\n          *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n          (click)=\"insertEmoji(emoji)\"\n          class=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\"\n          [title]=\"emoji.name\"\n        >\n          {{ emoji.emoji }}\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Menu des pièces jointes -->\n  <div\n    *ngIf=\"showAttachmentMenu\"\n    class=\"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"grid grid-cols-2 gap-3\">\n        <button\n          (click)=\"triggerFileInput('image')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-image text-blue-600 dark:text-blue-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Photos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('video')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-video text-purple-600 dark:text-purple-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Vidéos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('document')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-file text-orange-600 dark:text-orange-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Documents</span\n          >\n        </button>\n        <button\n          (click)=\"openCamera()\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-camera text-green-600 dark:text-green-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Caméra</span\n          >\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input caché pour fichiers -->\n  <input\n    #fileInput\n    type=\"file\"\n    class=\"hidden\"\n    (change)=\"onFileSelected($event)\"\n    [accept]=\"getFileAcceptTypes()\"\n    multiple\n  />\n\n  <!-- Overlay pour fermer les menus -->\n  <div\n    *ngIf=\"showEmojiPicker || showAttachmentMenu\"\n    class=\"fixed inset-0 bg-black bg-opacity-25 z-40\"\n    (click)=\"closeAllMenus()\"\n  ></div>\n</div>\n"], "mappings": ";AAQA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;;;;;;;;;;;ICe3BC,EAAA,CAAAC,SAAA,cAGO;;;;;IAQLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAwB;IACtBF,EAAA,CAAAC,SAAA,cAEO;IASTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAERJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;IA+CNX,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,eACG;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAC5B;;;;;IAIHJ,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAAwE;IACtEF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAAwD;IACtDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAM,MAAA,CAAAJ,gBAAA,kBAAAI,MAAA,CAAAJ,gBAAA,CAAAK,QAAA,MACF;;;;;IAaEb,EAAA,CAAAE,cAAA,cAGC;IAKKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAQ,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAcFjB,EAAA,CAAAE,cAAA,cAGC;IAOGF,EAAA,CAAAkB,UAAA,mBAAAC,+EAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,IAAA;MAAA,MAAAL,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAD,OAAA,CAAAE,eAAA,CAAAV,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,CAAoC;IAAA,EAAC;IANhD5B,EAAA,CAAAI,YAAA,EAOE;;;;IANAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAG,KAAA,yCAAA9B,EAAA,CAAA+B,aAAA,CAEC,QAAAf,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAd,QAAA;;;;;IAkBHb,EAAA,CAAAE,cAAA,cAQC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAgC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAlB,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,EAAiD;IAEjD5B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAU,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAd,QAAA,MACF;;;;;IAGAb,EAAA,CAAAE,cAAA,cAAoE;IAClEF,EAAA,CAAAC,SAAA,cAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA6B,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAApB,WAAA,CAAAqB,OAAA,GAAArC,EAAA,CAAAsC,cAAA,CAAmD;;;;;IAsBxDtC,EAAA,CAAAC,SAAA,cAIO;;;;;IADLD,EAAA,CAAA6B,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAApB,WAAA,CAAAqB,OAAA,GAAArC,EAAA,CAAAsC,cAAA,CAAmD;;;;;;IArBvDtC,EAAA,CAAAE,cAAA,cAAiD;IAG7CF,EAAA,CAAAkB,UAAA,mBAAAsB,+EAAA;MAAAxC,EAAA,CAAAoB,aAAA,CAAAqB,IAAA;MAAA,MAAAzB,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAmB,OAAA,GAAA1C,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAiB,OAAA,CAAAC,eAAA,CAAA3B,WAAA,CAAwB;IAAA,EAAC;IAElChB,EAAA,CAAAC,SAAA,cAIE;IACFD,EAAA,CAAAE,cAAA,cAEC;IACCF,EAAA,CAAAC,SAAA,YAEK;IACPD,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAA4C,UAAA,IAAAC,+DAAA,kBAIO;IACT7C,EAAA,CAAAI,YAAA,EAAM;;;;;IAjBAJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA6B,UAAA,QAAAiB,OAAA,CAAAC,WAAA,CAAA/B,WAAA,GAAAhB,EAAA,CAAA+B,aAAA,CAA4B,QAAAf,WAAA,CAAAqB,OAAA;IAa7BrC,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAAqB,OAAA,CAAqB;;;;;;IAO1BrC,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAkB,UAAA,mBAAA8B,+EAAA;MAAAhD,EAAA,CAAAoB,aAAA,CAAA6B,IAAA;MAAA,MAAAjC,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAA2B,OAAA,GAAAlD,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAyB,OAAA,CAAAC,YAAA,CAAAnC,WAAA,CAAqB;IAAA,EAAC;IAE/BhB,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAAC,SAAA,QAAsC;IACxCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAA4B;IAExBF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAAsD;IACpDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAE,cAAA,iBAEC;IACCF,EAAA,CAAAC,SAAA,YAAuC;IACzCD,EAAA,CAAAI,YAAA,EAAS;;;;;IAdJJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAoD,UAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAtC,WAAA,EAA8B;IAI/BhB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+C,OAAA,CAAAE,WAAA,CAAAvC,WAAA,OACF;IAEEhB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+C,OAAA,CAAAG,WAAA,CAAAxC,WAAA,OACF;;;;;IAkBAhB,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,YAIK;;;;;IAvBPD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAA4C,UAAA,IAAAa,8DAAA,gBAIK;IACLzD,EAAA,CAAA4C,UAAA,IAAAc,8DAAA,gBAIK;IACL1D,EAAA,CAAA4C,UAAA,IAAAe,8DAAA,gBAIK;IACL3D,EAAA,CAAA4C,UAAA,IAAAgB,8DAAA,gBAIK;IACP5D,EAAA,CAAAI,YAAA,EAAM;;;;IAlBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,eAAkC;IAKlC7D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,YAA+B;IAK/B7D,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,iBAAoC;IAKpC7D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,YAA+B;;;;;;IAWpC7D,EAAA,CAAAE,cAAA,kBAUC;IARCF,EAAA,CAAAkB,UAAA,mBAAA4C,4FAAA;MAAA,MAAAC,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAA4C,IAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAAxC,SAAA;MAAA,MAAAP,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,IAAAC,SAAA;MAAA,MAAA2C,OAAA,GAAAlE,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAyC,OAAA,CAAAC,cAAA,CAAAnD,WAAA,CAAAY,EAAA,EAAAqC,YAAA,CAAAG,KAAA,CAA2C;IAAA,EAAC;IASrDpE,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,QAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IARdJ,EAAA,CAAAqE,WAAA,iBAAAC,OAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAK,OAAA,CAAAE,aAAA,QAEC,mBAAAF,OAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAK,OAAA,CAAAE,aAAA;IAKKxE,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAyE,iBAAA,CAAAR,YAAA,CAAAG,KAAA,CAAoB;;;;;IAf9BpE,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAA4C,UAAA,IAAA8B,mEAAA,sBAaS;IACX1E,EAAA,CAAAI,YAAA,EAAM;;;;IAbmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA6B,UAAA,YAAAb,WAAA,CAAA2D,SAAA,CAAoB;;;;;;IAjKnD3E,EAAA,CAAA4E,uBAAA,GAMC;IAEC5E,EAAA,CAAA4C,UAAA,IAAAiC,yDAAA,kBAWM;IAGN7E,EAAA,CAAAE,cAAA,cAOC;IAFCF,EAAA,CAAAkB,UAAA,mBAAA4D,yEAAAC,MAAA;MAAA,MAAAhB,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAA4D,IAAA;MAAA,MAAAhE,WAAA,GAAA+C,WAAA,CAAAxC,SAAA;MAAA,MAAA0D,OAAA,GAAAjF,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAwD,OAAA,CAAAC,cAAA,CAAAlE,WAAA,EAAA+D,MAAA,CAA+B;IAAA,EAAC,yBAAAI,+EAAAJ,MAAA;MAAA,MAAAhB,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAA4D,IAAA;MAAA,MAAAhE,WAAA,GAAA+C,WAAA,CAAAxC,SAAA;MAAA,MAAA6D,OAAA,GAAApF,EAAA,CAAAsB,aAAA;MAAA,OAC1BtB,EAAA,CAAAyB,WAAA,CAAA2D,OAAA,CAAAC,oBAAA,CAAArE,WAAA,EAAA+D,MAAA,CAAqC;IAAA,EADX;IAIzC/E,EAAA,CAAA4C,UAAA,IAAA0C,yDAAA,kBAYM;IAGNtF,EAAA,CAAAE,cAAA,cAQC;IAECF,EAAA,CAAA4C,UAAA,IAAA2C,yDAAA,kBAUM;IAGNvF,EAAA,CAAA4C,UAAA,IAAA4C,yDAAA,kBAEM;IAGNxF,EAAA,CAAA4C,UAAA,IAAA6C,yDAAA,kBAuBM;IAGNzF,EAAA,CAAA4C,UAAA,IAAA8C,yDAAA,mBAqBM;IAGN1F,EAAA,CAAAE,cAAA,cAEC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAA4C,UAAA,KAAA+C,0DAAA,kBAwBM;IACR3F,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAA4C,UAAA,KAAAgD,0DAAA,kBAkBM;IACR5F,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAA6F,qBAAA,EAAe;;;;;;IAxKV7F,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAejChG,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAqE,WAAA,iBAAArD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,CAA0D,mBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA;IAE1DxE,EAAA,CAAA6B,UAAA,oBAAAb,WAAA,CAAAY,EAAA,CAA8B;IAM3B5B,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAA6B,UAAA,UAAAb,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,IAAAsB,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAgBlEhG,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAqE,WAAA,kBAAArD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,CAA2D,gBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,eAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,oBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,uBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,sBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA;IASxDxE,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAI,mBAAA,OAAAlF,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,IAAAsB,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAOkBhG,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAM,cAAA,CAAApF,WAAA,aAAwC;IAKxChB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAO,QAAA,CAAArF,WAAA,EAAuB;IA2B1BhB,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAQ,OAAA,CAAAtF,WAAA,EAAsB;IA0BjBhB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAyE,iBAAA,CAAAqB,OAAA,CAAAS,iBAAA,CAAAvF,WAAA,CAAAC,SAAA,EAA0C;IAE7CjB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA6B,UAAA,UAAAb,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,CAA0C;IA4B5CxE,EAAA,CAAAK,SAAA,GAAuD;IAAvDL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA2D,SAAA,IAAA3D,WAAA,CAAA2D,SAAA,CAAA6B,MAAA,KAAuD;;;;;IAuBhExG,EAAA,CAAAE,cAAA,eAAqD;IACnDF,EAAA,CAAAC,SAAA,eAIE;IACFD,EAAA,CAAAE,cAAA,eAAuE;IAEnEF,EAAA,CAAAC,SAAA,eAAmE;IASrED,EAAA,CAAAI,YAAA,EAAM;;;;IAfNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAA6B,UAAA,SAAA4E,OAAA,CAAAjG,gBAAA,kBAAAiG,OAAA,CAAAjG,gBAAA,CAAAsB,KAAA,yCAAA9B,EAAA,CAAA+B,aAAA,CAAqE,QAAA0E,OAAA,CAAAjG,gBAAA,kBAAAiG,OAAA,CAAAjG,gBAAA,CAAAK,QAAA;;;;;IAvL3Eb,EAAA,CAAAE,cAAA,cAAiE;IAC/DF,EAAA,CAAA4C,UAAA,IAAA8D,mDAAA,6BAiLe;IAGf1G,EAAA,CAAA4C,UAAA,IAAA+D,0CAAA,kBAmBM;IACR3G,EAAA,CAAAI,YAAA,EAAM;;;;IAtMuBJ,EAAA,CAAAK,SAAA,GACZ;IADYL,EAAA,CAAA6B,UAAA,YAAA+E,MAAA,CAAAC,QAAA,CACZ,iBAAAD,MAAA,CAAAE,gBAAA;IAiLT9G,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA6B,UAAA,SAAA+E,MAAA,CAAAG,QAAA,CAAc;;;;;;IA+ElB/G,EAAA,CAAAE,cAAA,kBAWC;IARCF,EAAA,CAAAkB,UAAA,uBAAA8F,oEAAA;MAAAhH,EAAA,CAAAoB,aAAA,CAAA6F,IAAA;MAAA,MAAAC,OAAA,GAAAlH,EAAA,CAAAsB,aAAA;MAAA,OAAatB,EAAA,CAAAyB,WAAA,CAAAyF,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC,qBAAAC,kEAAA;MAAApH,EAAA,CAAAoB,aAAA,CAAA6F,IAAA;MAAA,MAAAI,OAAA,GAAArH,EAAA,CAAAsB,aAAA;MAAA,OACxBtB,EAAA,CAAAyB,WAAA,CAAA4F,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EADI,wBAAAC,qEAAA;MAAAvH,EAAA,CAAAoB,aAAA,CAAA6F,IAAA;MAAA,MAAAO,OAAA,GAAAxH,EAAA,CAAAsB,aAAA;MAAA,OAErBtB,EAAA,CAAAyB,WAAA,CAAA+F,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAFD;IASnCzH,EAAA,CAAAC,SAAA,aAAiC;IACnCD,EAAA,CAAAI,YAAA,EAAS;;;;IANPJ,EAAA,CAAAqE,WAAA,eAAAqD,MAAA,CAAAC,gBAAA,CAAqC,qBAAAD,MAAA,CAAAC,gBAAA,mBAAAD,MAAA,CAAAC,gBAAA;;;;;IAiBrC3H,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,aAA+D;;;;;;IATjED,EAAA,CAAAE,cAAA,kBAOC;IAJCF,EAAA,CAAAkB,UAAA,mBAAA0G,gEAAA;MAAA5H,EAAA,CAAAoB,aAAA,CAAAyG,IAAA;MAAA,MAAAC,OAAA,GAAA9H,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAqG,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvB/H,EAAA,CAAA4C,UAAA,IAAAoF,2CAAA,iBAA4D;IAC5DhI,EAAA,CAAA4C,UAAA,IAAAqF,2CAAA,iBAA+D;IACjEjI,EAAA,CAAAI,YAAA,EAAS;;;;IALPJ,EAAA,CAAA6B,UAAA,aAAAqG,MAAA,CAAAC,gBAAA,CAA6B;IAGEnI,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,UAAAqG,MAAA,CAAAC,gBAAA,CAAuB;IACnBnI,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,SAAAqG,MAAA,CAAAC,gBAAA,CAAsB;;;;;;IAa3DnI,EAAA,CAAAE,cAAA,kBAQC;IANCF,EAAA,CAAAkB,UAAA,mBAAAkH,sEAAA;MAAA,MAAArE,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAAiH,IAAA;MAAA,MAAAC,YAAA,GAAAvE,WAAA,CAAAxC,SAAA;MAAA,MAAAgH,OAAA,GAAAvI,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAA8G,OAAA,CAAAC,mBAAA,CAAAF,YAAA,CAA6B;IAAA,EAAC;IAOvCtI,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IANPJ,EAAA,CAAAqE,WAAA,iBAAAoE,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,CAAyD,mBAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,uBAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,4BAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA;IAKzDtI,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAgI,YAAA,CAAAK,IAAA,MACF;;;;;;IAGA3I,EAAA,CAAAE,cAAA,kBAKC;IAHCF,EAAA,CAAAkB,UAAA,mBAAA0H,sEAAA;MAAA,MAAA7E,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAAyH,IAAA;MAAA,MAAAC,SAAA,GAAA/E,WAAA,CAAAxC,SAAA;MAAA,MAAAwH,OAAA,GAAA/I,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAsH,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAI5B9I,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAA6B,UAAA,UAAAiH,SAAA,CAAAG,IAAA,CAAoB;IAEpBjJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwI,SAAA,CAAA1E,KAAA,MACF;;;;;IA1BNpE,EAAA,CAAAE,cAAA,eAGC;IAGKF,EAAA,CAAA4C,UAAA,IAAAsG,6CAAA,sBAUS;IACXlJ,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA6D;IAC3DF,EAAA,CAAA4C,UAAA,IAAAuG,6CAAA,sBAOS;IACXnJ,EAAA,CAAAI,YAAA,EAAM;;;;IApBmBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA6B,UAAA,YAAAuH,OAAA,CAAAC,eAAA,CAAkB;IAarBrJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAA6B,UAAA,YAAAuH,OAAA,CAAAE,oBAAA,CAAAF,OAAA,CAAAV,qBAAA,EAA8C;;;;;;IAYxE1I,EAAA,CAAAE,cAAA,eAGC;IAIOF,EAAA,CAAAkB,UAAA,mBAAAqI,6DAAA;MAAAvJ,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAC,OAAA,GAAAzJ,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAgI,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC1J,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,aAA6D;IAC/DD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,kBAGC;IAFCF,EAAA,CAAAkB,UAAA,mBAAAyI,6DAAA;MAAA3J,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAI,OAAA,GAAA5J,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAmI,OAAA,CAAAF,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC1J,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,cAAiE;IACnED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAkB,UAAA,mBAAA2I,8DAAA;MAAA7J,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAM,OAAA,GAAA9J,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAqI,OAAA,CAAAJ,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAGtC1J,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAkB,UAAA,mBAAA6I,8DAAA;MAAA/J,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAQ,OAAA,GAAAhK,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAuI,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAGtBjK,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAiBTJ,EAAA,CAAAE,cAAA,eAIC;IADCF,EAAA,CAAAkB,UAAA,mBAAAgJ,0DAAA;MAAAlK,EAAA,CAAAoB,aAAA,CAAA+I,IAAA;MAAA,MAAAC,OAAA,GAAApK,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAA2I,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1BrK,EAAA,CAAAI,YAAA,EAAM;;;ADxgBT,OAAM,MAAOkK,oBAAoB;EAuG/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,cAA8B,EAC9BC,YAA0B,EAC1BC,GAAsB;IAJtB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IAtGb;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAhE,QAAQ,GAAU,EAAE;IACpB,KAAArC,aAAa,GAAkB,IAAI;IACnC,KAAAsG,eAAe,GAAG,KAAK;IAEvB,KAAAtK,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAAuK,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IAEzB;IACA,KAAA3D,gBAAgB,GAAG,KAAK;IACxB,KAAA4D,sBAAsB,GAAG,CAAC;IAC1B,KAAAC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAElC;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAA1C,eAAe,GAAU,CACvB;MACEzH,EAAE,EAAE,SAAS;MACbqH,IAAI,EAAE,SAAS;MACfN,IAAI,EAAE,IAAI;MACVqD,MAAM,EAAE,CACN;QAAE5H,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACErH,EAAE,EAAE,QAAQ;MACZqH,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVqD,MAAM,EAAE,CACN;QAAE5H,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACErH,EAAE,EAAE,QAAQ;MACZqH,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVqD,MAAM,EAAE,CACN;QAAE5H,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAP,qBAAqB,GAAG,IAAI,CAACW,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAA4C,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAnF,QAAQ,GAAG,KAAK;IAChB,KAAAoF,YAAY,GAAG,KAAK;IACpB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAjE,gBAAgB,GAAG,KAAK;IAChB,KAAAkE,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAIvM,YAAY,EAAE;IASxC,IAAI,CAACwM,WAAW,GAAG,IAAI,CAAC/B,EAAE,CAACgC,KAAK,CAAC;MAC/BnK,OAAO,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAAC2M,QAAQ,EAAE3M,UAAU,CAAC4M,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQF,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MAC7D,IAAIJ,IAAI,IAAIA,IAAI,CAACK,GAAG,EAAE;QACpB,IAAI,CAAC7I,aAAa,GAAGwI,IAAI,CAACK,GAAG;QAC7B,IAAI,CAACvC,eAAe,GAAGkC,IAAI,CAACnM,QAAQ,IAAI,KAAK;;KAEhD,CAAC,OAAOyM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,IAAI,CAAC9I,aAAa,GAAG,IAAI;MACzB,IAAI,CAACsG,eAAe,GAAG,KAAK;;EAEhC;EAEQgC,gBAAgBA,CAAA;IACtB,MAAMU,cAAc,GAAG,IAAI,CAAC/C,KAAK,CAACgD,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAC7D,IAAI,CAACH,cAAc,EAAE;MACnB,IAAI,CAAC7C,YAAY,CAACiD,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAAC7C,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACL,cAAc,CAACmD,eAAe,CACjCL,cAAc,EACd,IAAI,CAACvB,oBAAoB,EACzB,IAAI,CAACC,WAAW,CACjB,CAAC4B,SAAS,CAAC;MACVC,IAAI,EAAGlD,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACmD,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;MACrB,CAAC;MACDX,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAC3C,YAAY,CAACiD,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAAC7C,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQkD,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACpD,YAAY,EAAEjJ,EAAE,EAAE;IAE5B;IACA,IAAI,CAACiF,QAAQ,GAAG,IAAI,CAACgE,YAAY,CAAChE,QAAQ,IAAI,EAAE;IAChD,IAAI,CAACoE,eAAe,GAAG,IAAI,CAACpE,QAAQ,CAACL,MAAM,KAAK,IAAI,CAACyF,oBAAoB;IACzE,IAAI,CAAClB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACmD,cAAc,EAAE;EACvB;EAEQF,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACnD,YAAY,EAAEsD,YAAY,EAAE;MACnCZ,OAAO,CAACa,GAAG,CAAC,8BAA8B,CAAC;MAC3Cb,OAAO,CAACa,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC5J,aAAa,CAAC;MACnD+I,OAAO,CAACa,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACvD,YAAY,CAACsD,YAAY,CAAC;MAEhE,IAAI,CAAC3N,gBAAgB,GAAG,IAAI,CAACqK,YAAY,CAACsD,YAAY,CAACE,IAAI,CACxDC,CAAM,IAAK,CAACA,CAAC,CAAC1M,EAAE,IAAI0M,CAAC,CAACjB,GAAG,MAAM,IAAI,CAAC7I,aAAa,CACnD;MAED+I,OAAO,CAACa,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC5N,gBAAgB,CAAC;MAE9D;MACA,IAAI,CAAC,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACqK,YAAY,CAACsD,YAAY,CAAC3H,MAAM,GAAG,CAAC,EAAE;QACvE+G,OAAO,CAACa,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI,CAAC5N,gBAAgB,GAAG,IAAI,CAACqK,YAAY,CAACsD,YAAY,CAAC,CAAC,CAAC;;;EAG/D;EAEQpB,kBAAkBA,CAAA;IACxB;IACA;EAAA;EAGF;EACAhF,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACwE,WAAW,CAACgC,KAAK,IAAI,CAAC,IAAI,CAAC1D,YAAY,EAAEjJ,EAAE,EAAE;IAEvD,MAAMS,OAAO,GAAG,IAAI,CAACkK,WAAW,CAACoB,GAAG,CAAC,SAAS,CAAC,EAAEa,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAACpM,OAAO,EAAE;IAEd,MAAM7B,gBAAgB,GAAG,IAAI,CAACqK,YAAY,CAACsD,YAAY,EAAEE,IAAI,CAC1DC,CAAM,IAAK,CAACA,CAAC,CAAC1M,EAAE,IAAI0M,CAAC,CAACjB,GAAG,MAAM,IAAI,CAAC7I,aAAa,CACnD;IAED,MAAMkK,UAAU,GAAGlO,gBAAgB,EAAEoB,EAAE,IAAIpB,gBAAgB,EAAE6M,GAAG;IAEhE,IAAI,CAACqB,UAAU,EAAE;MACf,IAAI,CAAC/D,YAAY,CAACiD,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF,IAAI,CAAClD,cAAc,CAAC3C,WAAW,CAC7B2G,UAAU,EACVrM,OAAO,EACPsM,SAAS,EACT,MAAa,EACb,IAAI,CAAC9D,YAAY,CAACjJ,EAAE,CACrB,CAACkM,SAAS,CAAC;MACVC,IAAI,EAAGa,OAAY,IAAI;QACrB,IAAI,CAAC/H,QAAQ,CAACgI,IAAI,CAACD,OAAO,CAAC;QAC3B,IAAI,CAACrC,WAAW,CAACuC,KAAK,EAAE;QACxB,IAAI,CAACZ,cAAc,EAAE;MACvB,CAAC;MACDZ,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAAC3C,YAAY,CAACiD,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEA;EACAmB,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACzI,MAAM,KAAK,CAAC,EAAE;IAElC,KAAK,IAAI2I,IAAI,IAAIF,KAAK,EAAE;MACtB,IAAI,CAACG,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3B,MAAM3O,gBAAgB,GAAG,IAAI,CAACqK,YAAY,EAAEsD,YAAY,EAAEE,IAAI,CAC3DC,CAAM,IAAK,CAACA,CAAC,CAAC1M,EAAE,IAAI0M,CAAC,CAACjB,GAAG,MAAM,IAAI,CAAC7I,aAAa,CACnD;IAED,MAAMkK,UAAU,GAAGlO,gBAAgB,EAAEoB,EAAE,IAAIpB,gBAAgB,EAAE6M,GAAG;IAEhE,IAAI,CAACqB,UAAU,EAAE;MACf,IAAI,CAAC/D,YAAY,CAACiD,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF,IAAI,CAAClD,cAAc,CAAC3C,WAAW,CAC7B2G,UAAU,EACV,EAAE,EACFS,IAAI,EACJR,SAAS,EACT,IAAI,CAAC9D,YAAY,CAACjJ,EAAE,CACrB,CAACkM,SAAS,CAAC;MACVC,IAAI,EAAGa,OAAY,IAAI;QACrB,IAAI,CAAC/H,QAAQ,CAACgI,IAAI,CAACD,OAAO,CAAC;QAC3B,IAAI,CAACV,cAAc,EAAE;QACrB,IAAI,CAACvD,YAAY,CAAC0E,WAAW,CAAC,4BAA4B,CAAC;MAC7D,CAAC;MACD/B,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAAC3C,YAAY,CAACiD,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEA;EACMzG,mBAAmBA,CAAA;IAAA,IAAAmI,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDC,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE;;SAEpB,CAAC;QAEFT,KAAI,CAAC7D,aAAa,GAAG,IAAIuE,aAAa,CAACR,MAAM,EAAE;UAC7CS,QAAQ,EAAE;SACX,CAAC;QAEFX,KAAI,CAAC5D,WAAW,GAAG,EAAE;QACrB4D,KAAI,CAAC3H,gBAAgB,GAAG,IAAI;QAC5B2H,KAAI,CAAC/D,sBAAsB,GAAG,CAAC;QAC/B+D,KAAI,CAAC9D,mBAAmB,GAAG,WAAW;QAEtC8D,KAAI,CAAC3D,cAAc,GAAGuE,WAAW,CAAC,MAAK;UACrCZ,KAAI,CAAC/D,sBAAsB,EAAE;UAC7B+D,KAAI,CAAC1E,GAAG,CAACuF,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAERb,KAAI,CAAC7D,aAAa,CAAC2E,eAAe,GAAIpB,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAACqB,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;YACvBhB,KAAI,CAAC5D,WAAW,CAACmD,IAAI,CAACG,KAAK,CAACqB,IAAI,CAAC;;QAErC,CAAC;QAEDf,KAAI,CAAC7D,aAAa,CAAC8E,MAAM,GAAG,MAAK;UAC/BjB,KAAI,CAACkB,oBAAoB,EAAE;QAC7B,CAAC;QAEDlB,KAAI,CAAC7D,aAAa,CAACgF,KAAK,CAAC,GAAG,CAAC;QAC7BnB,KAAI,CAAC3E,YAAY,CAAC0E,WAAW,CAAC,8BAA8B,CAAC;OAC9D,CAAC,OAAO/B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrEgC,KAAI,CAAC3E,YAAY,CAACiD,SAAS,CAAC,oCAAoC,CAAC;QACjE0B,KAAI,CAAC7H,oBAAoB,EAAE;;IAC5B;EACH;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACmE,aAAa,IAAI,IAAI,CAACA,aAAa,CAACiF,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACjF,aAAa,CAACkF,IAAI,EAAE;MACzB,IAAI,CAAClF,aAAa,CAAC+D,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAAChF,cAAc,EAAE;MACvBoF,aAAa,CAAC,IAAI,CAACpF,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAAChE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC6D,mBAAmB,GAAG,YAAY;EACzC;EAEA/D,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACgE,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACiF,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACjF,aAAa,CAACkF,IAAI,EAAE;;MAE3B,IAAI,CAAClF,aAAa,CAAC+D,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;MACtE,IAAI,CAAClF,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvBoF,aAAa,CAAC,IAAI,CAACpF,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAAChE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC4D,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEc8E,oBAAoBA,CAAA;IAAA,IAAAQ,MAAA;IAAA,OAAAzB,iBAAA;MAChC,IAAI;QACF,IAAIyB,MAAI,CAACtF,WAAW,CAAClF,MAAM,KAAK,CAAC,EAAE;UACjCwK,MAAI,CAACrG,YAAY,CAACsG,WAAW,CAAC,wBAAwB,CAAC;UACvDD,MAAI,CAACvJ,oBAAoB,EAAE;UAC3B;;QAGF,MAAMyJ,SAAS,GAAG,IAAIC,IAAI,CAACH,MAAI,CAACtF,WAAW,EAAE;UAC3C0F,IAAI,EAAE;SACP,CAAC;QAEF,IAAIJ,MAAI,CAACzF,sBAAsB,GAAG,CAAC,EAAE;UACnCyF,MAAI,CAACrG,YAAY,CAACsG,WAAW,CAC3B,+CAA+C,CAChD;UACDD,MAAI,CAACvJ,oBAAoB,EAAE;UAC3B;;QAGF,MAAM4J,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACJ,SAAS,CAAC,EAAE,SAASK,IAAI,CAACC,GAAG,EAAE,OAAO,EAAE;UAClEJ,IAAI,EAAE;SACP,CAAC;QAEF,MAAMJ,MAAI,CAACS,gBAAgB,CAACJ,SAAS,CAAC;QACtCL,MAAI,CAACrG,YAAY,CAAC0E,WAAW,CAAC,sBAAsB,CAAC;OACtD,CAAC,OAAO/B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D0D,MAAI,CAACrG,YAAY,CAACiD,SAAS,CAAC,yCAAyC,CAAC;OACvE,SAAS;QACRoD,MAAI,CAACxF,mBAAmB,GAAG,MAAM;QACjCwF,MAAI,CAACzF,sBAAsB,GAAG,CAAC;QAC/ByF,MAAI,CAACtF,WAAW,GAAG,EAAE;;IACtB;EACH;EAEc+F,gBAAgBA,CAACJ,SAAe;IAAA,IAAAK,MAAA;IAAA,OAAAnC,iBAAA;MAC5C,MAAM/O,gBAAgB,GAAGkR,MAAI,CAAC7G,YAAY,EAAEsD,YAAY,EAAEE,IAAI,CAC3DC,CAAM,IAAK,CAACA,CAAC,CAAC1M,EAAE,IAAI0M,CAAC,CAACjB,GAAG,MAAMqE,MAAI,CAAClN,aAAa,CACnD;MAED,MAAMkK,UAAU,GAAGlO,gBAAgB,EAAEoB,EAAE,IAAIpB,gBAAgB,EAAE6M,GAAG;MAEhE,IAAI,CAACqB,UAAU,EAAE;QACf,MAAM,IAAIiD,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCJ,MAAI,CAAChH,cAAc,CAAC3C,WAAW,CAC7B2G,UAAU,EACV,EAAE,EACF2C,SAAS,EACT1C,SAAS,EACT+C,MAAI,CAAC7G,YAAY,CAACjJ,EAAE,CACrB,CAACkM,SAAS,CAAC;UACVC,IAAI,EAAGa,OAAY,IAAI;YACrB8C,MAAI,CAAC7K,QAAQ,CAACgI,IAAI,CAACD,OAAO,CAAC;YAC3B8C,MAAI,CAACxD,cAAc,EAAE;YACrB2D,OAAO,EAAE;UACX,CAAC;UACDvE,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChEwE,MAAM,CAACxE,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEAyE,uBAAuBA,CAACC,QAAgB;IACtC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMI,OAAO,GAAGJ,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGC,OAAO,IAAIG,OAAO,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACAhJ,oBAAoBA,CAACiJ,QAAa;IAChC,OAAOA,QAAQ,EAAEvG,MAAM,IAAI,EAAE;EAC/B;EAEAxD,mBAAmBA,CAAC+J,QAAa;IAC/B,IAAI,CAAC7J,qBAAqB,GAAG6J,QAAQ;EACvC;EAEAvJ,WAAWA,CAAC5E,KAAU;IACpB,MAAMoO,cAAc,GAAG,IAAI,CAACjG,WAAW,CAACoB,GAAG,CAAC,SAAS,CAAC,EAAEa,KAAK,IAAI,EAAE;IACnE,MAAMiE,UAAU,GAAGD,cAAc,IAAIpO,KAAK,CAACA,KAAK,IAAIA,KAAK,CAAC;IAC1D,IAAI,CAACmI,WAAW,CAACmG,UAAU,CAAC;MAAErQ,OAAO,EAAEoQ;IAAU,CAAE,CAAC;IAEpD,IAAI,CAACvH,eAAe,GAAG,KAAK;IAE5ByH,UAAU,CAAC,MAAK;MACd,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CACrC,qCAAqC,CACf;MACxB,IAAIF,QAAQ,EAAE;QACZA,QAAQ,CAACG,KAAK,EAAE;;IAEpB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC9H,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,CAACC,kBAAkB,GAAG,KAAK;EACjC;EAEA;EACA8H,SAASA,CAAC7B,IAAuB;IAC/B,IAAI,CAACvF,QAAQ,GAAGuF,IAAI;IACpB,IAAI,CAACxF,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACE,YAAY,GAAG,CAAC;IAErB,IAAI,CAACC,SAAS,GAAGmE,WAAW,CAAC,MAAK;MAChC,IAAI,CAACpE,YAAY,EAAE;MACnB,IAAI,CAAClB,GAAG,CAACuF,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;IAER,IAAI,CAACxF,YAAY,CAAC0E,WAAW,CAC3B,SAAS+B,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,UAAU,CACxD;EACH;EAEA8B,OAAOA,CAAA;IACL,IAAI,IAAI,CAACnH,SAAS,EAAE;MAClBgF,aAAa,CAAC,IAAI,CAAChF,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACnB,YAAY,CAAC0E,WAAW,CAAC,eAAe,CAAC;EAChD;EAEA8D,cAAcA,CAAA;IACZ,IAAI,CAACF,SAAS,CAAC,OAAO,CAAC;EACzB;EAEAG,cAAcA,CAAA;IACZ,IAAI,CAACH,SAAS,CAAC,OAAO,CAAC;EACzB;EAEA;EACAI,YAAYA,CAAA;IACV,IAAI,CAACjI,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB,IAAI,CAACC,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,EAAE;;EAE3B;EAEAgI,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACjI,WAAW,CAACoD,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACnD,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACzE,QAAQ,CAAC0M,MAAM,CACtC3E,OAAO,IACNA,OAAO,CAACvM,OAAO,EACXmR,WAAW,EAAE,CACdC,QAAQ,CAAC,IAAI,CAACpI,WAAW,CAACmI,WAAW,EAAE,CAAC,IAC3C5E,OAAO,CAACjN,MAAM,EAAEd,QAAQ,EACpB2S,WAAW,EAAE,CACdC,QAAQ,CAAC,IAAI,CAACpI,WAAW,CAACmI,WAAW,EAAE,CAAC,CAC9C;EACH;EAEA;EACAE,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC1I,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAEjJ,EAAE,EACvE;IAEF,IAAI,CAACoJ,aAAa,GAAG,IAAI;IACzB,IAAI,CAACkB,WAAW,EAAE;IAElB,IAAI,CAACxB,cAAc,CAACmD,eAAe,CACjC,IAAI,CAAChD,YAAY,CAACjJ,EAAE,EACpB,IAAI,CAACqK,oBAAoB,EACzB,IAAI,CAACC,WAAW,CACjB,CAAC4B,SAAS,CAAC;MACVC,IAAI,EAAGlD,YAAY,IAAI;QACrB,MAAM8I,WAAW,GAAG9I,YAAY,CAAChE,QAAQ,IAAI,EAAE;QAC/C,IAAI8M,WAAW,CAACnN,MAAM,GAAG,CAAC,EAAE;UAC1B;UACA,IAAI,CAACK,QAAQ,GAAG,CAAC,GAAG8M,WAAW,EAAE,GAAG,IAAI,CAAC9M,QAAQ,CAAC;UAClD,IAAI,CAACoE,eAAe,GAClB0I,WAAW,CAACnN,MAAM,KAAK,IAAI,CAACyF,oBAAoB;SACnD,MAAM;UACL,IAAI,CAAChB,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACDsC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACtC,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACkB,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;EAEA;EACM0H,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAtE,iBAAA;MACjB,IAAI;QACF,MAAMuE,QAAQ,SAASD,MAAI,CAACE,kBAAkB,EAAE;QAChD,MAAM;UAAEC,QAAQ;UAAEC;QAAS,CAAE,GAAGH,QAAQ,CAACI,MAAM;QAE/C,MAAMC,eAAe,GAAG;UACtB/C,IAAI,EAAE,UAAU;UAChB4C,QAAQ;UACRC,SAAS;UACTG,OAAO,QAAQP,MAAI,CAACQ,yBAAyB,CAACL,QAAQ,EAAEC,SAAS,CAAC;UAClEK,MAAM,EAAE,kCAAkCN,QAAQ,IAAIC,SAAS,OAAO;UACtEhT,SAAS,EAAE,IAAIsQ,IAAI;SACpB;QAED,MAAMsC,MAAI,CAACU,mBAAmB,CAACJ,eAAe,CAAC;QAC/CN,MAAI,CAAClJ,YAAY,CAAC0E,WAAW,CAAC,+BAA+B,CAAC;OAC/D,CAAC,OAAO/B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/DuG,MAAI,CAAClJ,YAAY,CAACiD,SAAS,CAAC,qCAAqC,CAAC;;IACnE;EACH;EAEQmG,kBAAkBA,CAAA;IACxB,OAAO,IAAInC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrCrC,SAAS,CAAC+E,WAAW,CAACT,kBAAkB,CAAClC,OAAO,EAAEC,MAAM,EAAE;QACxD2C,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;EACJ;EAEcN,yBAAyBA,CACrCO,GAAW,EACXC,GAAW;IAAA,OAAAtF,iBAAA;MAEX,IAAI;QACF,MAAMuF,QAAQ,SAASC,KAAK,CAC1B,+DAA+DH,GAAG,QAAQC,GAAG,2BAA2B,CACzG;QAED,IAAIC,QAAQ,CAACE,EAAE,EAAE;UACf,MAAM3E,IAAI,SAASyE,QAAQ,CAACG,IAAI,EAAE;UAClC,OAAO5E,IAAI,CAAC6E,YAAY,IAAI,GAAGN,GAAG,KAAKC,GAAG,EAAE;;OAE/C,CAAC,OAAOvH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;MAG3D,OAAO,GAAGsH,GAAG,KAAKC,GAAG,EAAE;IAAC;EAC1B;EAEcN,mBAAmBA,CAACY,YAAiB;IAAA,IAAAC,MAAA;IAAA,OAAA7F,iBAAA;MACjD,MAAM/O,gBAAgB,GAAG4U,MAAI,CAACvK,YAAY,EAAEsD,YAAY,EAAEE,IAAI,CAC3DC,CAAM,IAAK,CAACA,CAAC,CAAC1M,EAAE,IAAI0M,CAAC,CAACjB,GAAG,MAAM+H,MAAI,CAAC5Q,aAAa,CACnD;MAED,MAAMkK,UAAU,GAAGlO,gBAAgB,EAAEoB,EAAE,IAAIpB,gBAAgB,EAAE6M,GAAG;MAEhE,IAAI,CAACqB,UAAU,EAAE;QACf,MAAM,IAAIiD,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCsD,MAAI,CAAC1K,cAAc,CAAC3C,WAAW,CAC7B2G,UAAU,EACV,yBAAyByG,YAAY,CAACf,OAAO,EAAE,EAC/CzF,SAAS,EACT,MAAa,EACbyG,MAAI,CAACvK,YAAY,CAACjJ,EAAE,CACrB,CAACkM,SAAS,CAAC;UACVC,IAAI,EAAGa,OAAY,IAAI;YACrBwG,MAAI,CAACvO,QAAQ,CAACgI,IAAI,CAACD,OAAO,CAAC;YAC3BwG,MAAI,CAAClH,cAAc,EAAE;YACrB2D,OAAO,EAAE;UACX,CAAC;UACDvE,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;YACDwE,MAAM,CAACxE,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA;EACM+H,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA/F,iBAAA;MAChB,IAAI;QACF,MAAMgG,OAAO,SAASD,MAAI,CAACE,aAAa,EAAE;QAE1C,IAAID,OAAO,EAAE;UACX,MAAMD,MAAI,CAACG,kBAAkB,CAACF,OAAO,CAAC;UACtCD,MAAI,CAAC3K,YAAY,CAAC0E,WAAW,CAAC,6BAA6B,CAAC;;OAE/D,CAAC,OAAO/B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DgI,MAAI,CAAC3K,YAAY,CAACiD,SAAS,CAAC,mCAAmC,CAAC;;IACjE;EACH;EAEc4H,aAAaA,CAAA;IAAA,OAAAjG,iBAAA;MACzB,OAAO,IAAIqC,OAAO,CAAEC,OAAO,IAAI;QAC7Bc,UAAU,CAAC,MAAK;UACdd,OAAO,CAAC;YACN5I,IAAI,EAAE,UAAU;YAChByM,KAAK,EAAE,mBAAmB;YAC1BC,KAAK,EAAE,sBAAsB;YAC7BC,MAAM,EAAE;WACT,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IAAC;EACL;EAEcH,kBAAkBA,CAACI,WAAgB;IAAA,IAAAC,MAAA;IAAA,OAAAvG,iBAAA;MAC/C,MAAM/O,gBAAgB,GAAGsV,MAAI,CAACjL,YAAY,EAAEsD,YAAY,EAAEE,IAAI,CAC3DC,CAAM,IAAK,CAACA,CAAC,CAAC1M,EAAE,IAAI0M,CAAC,CAACjB,GAAG,MAAMyI,MAAI,CAACtR,aAAa,CACnD;MAED,MAAMkK,UAAU,GAAGlO,gBAAgB,EAAEoB,EAAE,IAAIpB,gBAAgB,EAAE6M,GAAG;MAEhE,IAAI,CAACqB,UAAU,EAAE;QACf,MAAM,IAAIiD,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCgE,MAAI,CAACpL,cAAc,CAAC3C,WAAW,CAC7B2G,UAAU,EACV,uBAAuBmH,WAAW,CAAC5M,IAAI,QAAQ4M,WAAW,CAACH,KAAK,QAAQG,WAAW,CAACF,KAAK,EAAE,EAC3FhH,SAAS,EACT,MAAa,EACbmH,MAAI,CAACjL,YAAY,CAACjJ,EAAE,CACrB,CAACkM,SAAS,CAAC;UACVC,IAAI,EAAGa,OAAY,IAAI;YACrBkH,MAAI,CAACjP,QAAQ,CAACgI,IAAI,CAACD,OAAO,CAAC;YAC3BkH,MAAI,CAAC5H,cAAc,EAAE;YACrB2D,OAAO,EAAE;UACX,CAAC;UACDvE,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;YACrEwE,MAAM,CAACxE,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA;EACAY,cAAcA,CAAA;IACZyE,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACoD,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGtE,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC9D,GAAG,CAACiI,KAAK,CAAC,GAAGnE,IAAI,CAAC9D,GAAG,CAACkI,CAAC,CAAC,CAAC;IACnD,OAAOG,UAAU,CAAC,CAACJ,KAAK,GAAGnE,IAAI,CAACwE,GAAG,CAACJ,CAAC,EAAEE,CAAC,CAAC,EAAEG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGJ,KAAK,CAACC,CAAC,CAAC;EACzE;EAEAI,cAAcA,CAACxE,OAAe;IAC5B,MAAMyE,KAAK,GAAG3E,IAAI,CAACC,KAAK,CAACC,OAAO,GAAG,IAAI,CAAC;IACxC,MAAMH,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAEC,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;IACjD,MAAM0E,IAAI,GAAG1E,OAAO,GAAG,EAAE;IAEzB,IAAIyE,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAI5E,OAAO,CAACI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIwE,IAAI,CAC3DzE,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAGvB,OAAO,GAAGL,OAAO,IAAI6E,IAAI,CAACzE,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACzD;EAEAyE,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEA1M,aAAaA,CAAA;IACX,IAAI,CAACa,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,UAAU,GAAG,KAAK;EACzB;EAEA4L,oBAAoBA,CAAA;IAClB,IAAI,CAAC7L,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,CAACD,eAAe,GAAG,KAAK;EAC9B;EAEAxB,gBAAgBA,CAAC0H,IAAa;IAC5B;IACA,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAAC6F,SAAS,CAAChB,aAAa,CAACiB,MAAM,GAAG,SAAS;KAChD,MAAM,IAAI9F,IAAI,KAAK,OAAO,EAAE;MAC3B,IAAI,CAAC6F,SAAS,CAAChB,aAAa,CAACiB,MAAM,GAAG,SAAS;KAChD,MAAM,IAAI9F,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAI,CAAC6F,SAAS,CAAChB,aAAa,CAACiB,MAAM,GACjC,uCAAuC;KAC1C,MAAM;MACL,IAAI,CAACD,SAAS,CAAChB,aAAa,CAACiB,MAAM,GAAG,KAAK;;IAG7C,IAAI,CAACD,SAAS,CAAChB,aAAa,CAACkB,KAAK,EAAE;EACtC;EAEA;EACAC,qBAAqBA,CAAA;IACnB;IACAC,MAAM,CAACC,OAAO,CAACC,IAAI,EAAE;EACvB;EAEA7W,gBAAgBA,CAACC,UAAe;IAC9B,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IACpC,MAAM6Q,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMiG,cAAc,GAAG,IAAIjG,IAAI,CAAC5Q,UAAU,CAAC;IAC3C,MAAM8W,aAAa,GAAGvF,IAAI,CAACC,KAAK,CAC9B,CAACX,GAAG,CAACkG,OAAO,EAAE,GAAGF,cAAc,CAACE,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CACzD;IAED,IAAID,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;IACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,UAAUA,aAAa,MAAM;IAC5D,IAAIA,aAAa,GAAG,IAAI,EACtB,OAAO,UAAUvF,IAAI,CAACC,KAAK,CAACsF,aAAa,GAAG,EAAE,CAAC,IAAI;IACrD,OAAO,UAAUvF,IAAI,CAACC,KAAK,CAACsF,aAAa,GAAG,IAAI,CAAC,IAAI;EACvD;EAEAE,cAAcA,CAAA;IACZ;IACA,IAAI,CAACxM,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACD,eAAe,GAAG,KAAK;EAC9B;EAEA0M,QAAQA,CAAC5I,KAAU;IACjB,MAAMgH,OAAO,GAAGhH,KAAK,CAACE,MAAM;IAC5B,IACE8G,OAAO,CAACE,SAAS,KAAK,CAAC,IACvB,IAAI,CAACjL,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAAC0I,gBAAgB,EAAE;;EAE3B;EAEA5M,gBAAgBA,CAAC+Q,KAAa,EAAEjJ,OAAY;IAC1C,OAAOA,OAAO,CAAChN,EAAE,IAAIiW,KAAK;EAC5B;EAEA9R,uBAAuBA,CAAC8R,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5B,MAAMC,cAAc,GAAG,IAAI,CAACjR,QAAQ,CAACgR,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAAClR,QAAQ,CAACgR,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACC,cAAc,EAAE7W,SAAS,IAAI,CAAC8W,eAAe,EAAE9W,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAM+W,WAAW,GAAG,IAAIzG,IAAI,CAACuG,cAAc,CAAC7W,SAAS,CAAC,CAACgX,YAAY,EAAE;IACrE,MAAMC,YAAY,GAAG,IAAI3G,IAAI,CAACwG,eAAe,CAAC9W,SAAS,CAAC,CAACgX,YAAY,EAAE;IAEvE,OAAOD,WAAW,KAAKE,YAAY;EACrC;EAEAnX,mBAAmBA,CAACE,SAAc;IAChC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,MAAMkX,IAAI,GAAG,IAAI5G,IAAI,CAACtQ,SAAS,CAAC;IAChC,MAAMmX,KAAK,GAAG,IAAI7G,IAAI,EAAE;IACxB,MAAM8G,SAAS,GAAG,IAAI9G,IAAI,CAAC6G,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIJ,IAAI,CAACF,YAAY,EAAE,KAAKG,KAAK,CAACH,YAAY,EAAE,EAAE,OAAO,aAAa;IACtE,IAAIE,IAAI,CAACF,YAAY,EAAE,KAAKI,SAAS,CAACJ,YAAY,EAAE,EAAE,OAAO,MAAM;IAEnE,OAAOE,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEA1T,cAAcA,CAAC0J,OAAY,EAAEI,KAAU;IACrC;IACAzB,OAAO,CAACa,GAAG,CAAC,kBAAkB,EAAEQ,OAAO,CAAC;EAC1C;EAEAvJ,oBAAoBA,CAACuJ,OAAY,EAAEI,KAAU;IAC3CA,KAAK,CAAC6J,cAAc,EAAE;IACtB;IACAtL,OAAO,CAACa,GAAG,CAAC,uBAAuB,EAAEQ,OAAO,CAAC;EAC/C;EAEA3I,gBAAgBA,CAAC4R,KAAa;IAC5B,IAAIA,KAAK,KAAK,IAAI,CAAChR,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IACnD,MAAMsR,cAAc,GAAG,IAAI,CAACjR,QAAQ,CAACgR,KAAK,CAAC;IAC3C,MAAMiB,WAAW,GAAG,IAAI,CAACjS,QAAQ,CAACgR,KAAK,GAAG,CAAC,CAAC;IAE5C,OAAO,CAACiB,WAAW,IAAIhB,cAAc,CAACnW,MAAM,EAAEC,EAAE,KAAKkX,WAAW,CAACnX,MAAM,EAAEC,EAAE;EAC7E;EAEAF,eAAeA,CAACqX,MAAc;IAC5B;IACAxL,OAAO,CAACa,GAAG,CAAC,oBAAoB,EAAE2K,MAAM,CAAC;EAC3C;EAEA7S,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC2E,YAAY,EAAEsD,YAAY,EAAE3H,MAAM,GAAG,CAAC;EACpD;EAEAL,oBAAoBA,CAAC0R,KAAa;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5B,MAAMC,cAAc,GAAG,IAAI,CAACjR,QAAQ,CAACgR,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAAClR,QAAQ,CAACgR,KAAK,GAAG,CAAC,CAAC;IAEhD,OAAOC,cAAc,CAACnW,MAAM,EAAEC,EAAE,KAAKmW,eAAe,CAACpW,MAAM,EAAEC,EAAE;EACjE;EAEAM,YAAYA,CAAC6W,MAAc;IACzB;IACA,MAAMC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAMC,IAAI,GAAGF,MAAM,CAACG,KAAK,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC5CD,CAAC,GAAG,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC;MAClC,OAAOF,CAAC,GAAGA,CAAC;IACd,CAAC,EAAE,CAAC,CAAC;IACL,OAAOJ,MAAM,CAAC9G,IAAI,CAACqH,GAAG,CAACN,IAAI,CAAC,GAAGD,MAAM,CAACxS,MAAM,CAAC;EAC/C;EAEAJ,cAAcA,CAACwI,OAAY;IACzB,IAAIA,OAAO,CAACO,IAAI,EAAE;MAChB,IAAIP,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEoI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MAC3D,IAAI5K,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEoI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MAC3D,IAAI5K,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEoI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MAC3D,OAAO,MAAM;;IAEf,OAAO,MAAM;EACf;EAEApX,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IACvB;IACA,OAAOA,OAAO,CAACoX,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;EACvC;EAEApT,QAAQA,CAACuI,OAAY;IACnB,OAAOA,OAAO,CAACO,IAAI,IAAIP,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEoI,UAAU,CAAC,QAAQ,CAAC;EAChE;EAEA7W,eAAeA,CAACiM,OAAY;IAC1B;IACArB,OAAO,CAACa,GAAG,CAAC,oBAAoB,EAAEQ,OAAO,CAAC;EAC5C;EAEA7L,WAAWA,CAAC6L,OAAY;IACtB,OAAOA,OAAO,CAACO,IAAI,EAAEuK,GAAG,IAAI9K,OAAO,CAACO,IAAI,EAAEwK,IAAI,IAAI,EAAE;EACtD;EAEArT,OAAOA,CAACsI,OAAY;IAClB,OAAOA,OAAO,CAACO,IAAI,IAAI,CAACP,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEoI,UAAU,CAAC,QAAQ,CAAC;EACjE;EAEArW,YAAYA,CAACyL,OAAY;IACvB,IAAIA,OAAO,CAACO,IAAI,EAAEuK,GAAG,EAAE;MACrB,MAAME,IAAI,GAAG/G,QAAQ,CAACgH,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGlL,OAAO,CAACO,IAAI,CAACuK,GAAG;MAC5BE,IAAI,CAACG,QAAQ,GAAGnL,OAAO,CAACO,IAAI,CAAClG,IAAI,IAAI,MAAM;MAC3C2Q,IAAI,CAACzC,KAAK,EAAE;;EAEhB;EAEA7T,WAAWA,CAACsL,OAAY;IACtB,IAAI,CAACA,OAAO,CAACO,IAAI,EAAEiC,IAAI,EAAE,OAAO,aAAa;IAE7C,IAAIxC,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACoI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,cAAc;IACjE,IAAI5K,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACoI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,cAAc;IACjE,IAAI5K,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACqC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IAC/D,IAAI7E,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACqC,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACjE,IAAI7E,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACqC,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IAEnE,OAAO,aAAa;EACtB;EAEAlQ,WAAWA,CAACqL,OAAY;IACtB,OAAOA,OAAO,CAACO,IAAI,EAAElG,IAAI,IAAI,SAAS;EACxC;EAEAzF,WAAWA,CAACoL,OAAY;IACtB,IAAI,CAACA,OAAO,CAACO,IAAI,EAAEmB,IAAI,EAAE,OAAO,EAAE;IAClC,OAAO,IAAI,CAAC8F,cAAc,CAACxH,OAAO,CAACO,IAAI,CAACmB,IAAI,CAAC;EAC/C;EAEA/J,iBAAiBA,CAACtF,SAAc;IAC9B,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,OAAO,IAAIsQ,IAAI,CAACtQ,SAAS,CAAC,CAAC+Y,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACA/V,cAAcA,CAACgW,SAAiB,EAAE/V,KAAa;IAC7C;IACAmJ,OAAO,CAACa,GAAG,CAAC,kBAAkB,EAAE+L,SAAS,EAAE/V,KAAK,CAAC;EACnD;EAEAG,cAAcA,CAAC6V,QAAa,EAAErB,MAAc;IAC1C,OAAOqB,QAAQ,CAACC,KAAK,EAAE5G,QAAQ,CAACsF,MAAM,CAAC,IAAI,KAAK;EAClD;EAEAuB,aAAaA,CAACtL,KAAU;IACtB;IACA,MAAMR,KAAK,GAAGQ,KAAK,CAACE,MAAM,CAACV,KAAK;IAChC,IAAIA,KAAK,CAACC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC1H,QAAQ,EAAE;MAClC,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;KACD,MAAM,IAAI,CAACyH,KAAK,CAACC,IAAI,EAAE,IAAI,IAAI,CAAC1H,QAAQ,EAAE;MACzC,IAAI,CAACA,QAAQ,GAAG,KAAK;MACrB;;EAEJ;;EAEAwT,cAAcA,CAACvL,KAAU;IACvB,IAAIA,KAAK,CAACwL,GAAG,KAAK,OAAO,IAAI,CAACxL,KAAK,CAACyL,QAAQ,EAAE;MAC5CzL,KAAK,CAAC6J,cAAc,EAAE;MACtB,IAAI,CAAC9Q,WAAW,EAAE;;EAEtB;EAEA2S,YAAYA,CAAA;IACV;IACA,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAAC7T,QAAQ,GAAG,KAAK;EACvB;EAEQ4T,kBAAkBA,CAAA;IACxB;IACApN,OAAO,CAACa,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAEAnE,UAAUA,CAAA;IACR;IACAsD,OAAO,CAACa,GAAG,CAAC,aAAa,CAAC;EAC5B;EAEA;EACAyM,WAAWA,CAACvN,KAAU,EAAEwN,OAAe;IACrCvN,OAAO,CAACD,KAAK,CAAC,eAAewN,OAAO,GAAG,EAAExN,KAAK,CAAC;IAC/C,IAAIsB,OAAO,GAAG,sCAAsC;IAEpD,IAAItB,KAAK,EAAEsB,OAAO,EAAE;MAClBA,OAAO,GAAGtB,KAAK,CAACsB,OAAO;KACxB,MAAM,IAAI,OAAOtB,KAAK,KAAK,QAAQ,EAAE;MACpCsB,OAAO,GAAGtB,KAAK;;IAGjB,IAAI,CAAC3C,YAAY,CAACiD,SAAS,CAACgB,OAAO,CAAC;EACtC;EAEA;EACAmM,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChP,SAAS,EAAE;MAClBgF,aAAa,CAAC,IAAI,CAAChF,SAAS,CAAC;;IAG/B,IAAI,IAAI,CAACJ,cAAc,EAAE;MACvBoF,aAAa,CAAC,IAAI,CAACpF,cAAc,CAAC;;IAGpC,IAAI,IAAI,CAACU,aAAa,EAAE;MACtB2O,YAAY,CAAC,IAAI,CAAC3O,aAAa,CAAC;;IAGlC,IAAI,IAAI,CAACZ,aAAa,IAAI,IAAI,CAACA,aAAa,CAACiF,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACjF,aAAa,CAACkF,IAAI,EAAE;MACzB,IAAI,CAAClF,aAAa,CAAC+D,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGxE,IAAI,CAACrE,aAAa,CAAC2O,WAAW,EAAE;EAClC;;;uBA3hCW3Q,oBAAoB,EAAAtK,EAAA,CAAAkb,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApb,EAAA,CAAAkb,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtb,EAAA,CAAAkb,iBAAA,CAAAK,EAAA,CAAA7Q,cAAA,GAAA1K,EAAA,CAAAkb,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAzb,EAAA,CAAAkb,iBAAA,CAAAlb,EAAA,CAAA0b,iBAAA;IAAA;EAAA;;;YAApBpR,oBAAoB;MAAAqR,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCjBjC9b,EAAA,CAAAE,cAAA,aAEC;UAOKF,EAAA,CAAAkB,UAAA,mBAAA8a,sDAAA;YAAA,OAASD,GAAA,CAAA3E,qBAAA,EAAuB;UAAA,EAAC;UAGjCpX,EAAA,CAAAC,SAAA,WAAkE;UACpED,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAA8C;UAMxCF,EAAA,CAAAkB,UAAA,mBAAA+a,mDAAA;YAAA,OAASF,GAAA,CAAAra,eAAA,CAAAqa,GAAA,CAAAvb,gBAAA,kBAAAub,GAAA,CAAAvb,gBAAA,CAAAoB,EAAA,CAAsC;UAAA,EAAC;UAJlD5B,EAAA,CAAAI,YAAA,EAKE;UACFJ,EAAA,CAAA4C,UAAA,IAAAsZ,mCAAA,iBAGO;UACTlc,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,aAA4B;UAExBF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAAsD;UACpDF,EAAA,CAAA4C,UAAA,KAAAuZ,oCAAA,kBAkBM;UACNnc,EAAA,CAAA4C,UAAA,KAAAwZ,qCAAA,mBAMO;UACTpc,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAAqC;UAEjCF,EAAA,CAAAkB,UAAA,mBAAAmb,uDAAA;YAAA,OAASN,GAAA,CAAA5I,cAAA,EAAgB;UAAA,EAAC;UAI1BnT,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAkB,UAAA,mBAAAob,uDAAA;YAAA,OAASP,GAAA,CAAA3I,cAAA,EAAgB;UAAA,EAAC;UAI1BpT,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAMC;UALCF,EAAA,CAAAkB,UAAA,mBAAAqb,uDAAA;YAAA,OAASR,GAAA,CAAA1I,YAAA,EAAc;UAAA,EAAC;UAMxBrT,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAkB,UAAA,mBAAAsb,uDAAA;YAAA,OAAST,GAAA,CAAApE,cAAA,EAAgB;UAAA,EAAC;UAI1B3X,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAE,cAAA,oBAIC;UADCF,EAAA,CAAAkB,UAAA,oBAAAub,sDAAA1X,MAAA;YAAA,OAAUgX,GAAA,CAAAnE,QAAA,CAAA7S,MAAA,CAAgB;UAAA,EAAC;UAG3B/E,EAAA,CAAA4C,UAAA,KAAA8Z,oCAAA,kBAUM;UAGN1c,EAAA,CAAA4C,UAAA,KAAA+Z,oCAAA,kBAaM;UAGN3c,EAAA,CAAA4C,UAAA,KAAAga,oCAAA,kBAyMM;UACR5c,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAkB,UAAA,sBAAA2b,wDAAA;YAAA,OAAYd,GAAA,CAAAhU,WAAA,EAAa;UAAA,EAAC;UAI1B/H,EAAA,CAAAE,cAAA,eAAwB;UAGpBF,EAAA,CAAAkB,UAAA,mBAAA4b,uDAAA;YAAA,OAASf,GAAA,CAAA/I,iBAAA,EAAmB;UAAA,EAAC;UAM7BhT,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAOC;UALCF,EAAA,CAAAkB,UAAA,mBAAA6b,uDAAA;YAAA,OAAShB,GAAA,CAAA/E,oBAAA,EAAsB;UAAA,EAAC;UAMhChX,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAoB;UAOhBF,EAAA,CAAAkB,UAAA,mBAAA8b,yDAAAjY,MAAA;YAAA,OAASgX,GAAA,CAAAzB,aAAA,CAAAvV,MAAA,CAAqB;UAAA,EAAC,qBAAAkY,2DAAAlY,MAAA;YAAA,OACpBgX,GAAA,CAAAxB,cAAA,CAAAxV,MAAA,CAAsB;UAAA,EADF,mBAAAmY,yDAAA;YAAA,OAEtBnB,GAAA,CAAArB,YAAA,EAAc;UAAA,EAFQ,kBAAAyC,wDAAA;YAAA,OAGvBpB,GAAA,CAAAnB,WAAA,EAAa;UAAA,EAHU;UASjC5a,EAAA,CAAAG,MAAA;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAIbJ,EAAA,CAAAE,cAAA,eAAwB;UAEtBF,EAAA,CAAA4C,UAAA,KAAAwa,uCAAA,qBAaS;UAGTpd,EAAA,CAAA4C,UAAA,KAAAya,uCAAA,qBAUS;UACXrd,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAA4C,UAAA,KAAA0a,oCAAA,kBA6BM;UAGNtd,EAAA,CAAA4C,UAAA,KAAA2a,oCAAA,mBA4DM;UAGNvd,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAkB,UAAA,oBAAAsc,uDAAAzY,MAAA;YAAA,OAAUgX,GAAA,CAAAhN,cAAA,CAAAhK,MAAA,CAAsB;UAAA,EAAC;UAJnC/E,EAAA,CAAAI,YAAA,EAOE;UAGFJ,EAAA,CAAA4C,UAAA,KAAA6a,oCAAA,kBAIO;UACTzd,EAAA,CAAAI,YAAA,EAAM;;;;;UAvgBIJ,EAAA,CAAAK,SAAA,GAAqE;UAArEL,EAAA,CAAA6B,UAAA,SAAAka,GAAA,CAAAvb,gBAAA,kBAAAub,GAAA,CAAAvb,gBAAA,CAAAsB,KAAA,yCAAA9B,EAAA,CAAA+B,aAAA,CAAqE,QAAAga,GAAA,CAAAvb,gBAAA,kBAAAub,GAAA,CAAAvb,gBAAA,CAAAK,QAAA;UAMpEb,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAA6B,UAAA,SAAAka,GAAA,CAAAvb,gBAAA,kBAAAub,GAAA,CAAAvb,gBAAA,CAAAC,QAAA,CAAgC;UAOjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAAyb,GAAA,CAAAvb,gBAAA,kBAAAub,GAAA,CAAAvb,gBAAA,CAAAK,QAAA,wBACF;UAGKb,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAA6B,UAAA,SAAAka,GAAA,CAAA5P,YAAA,CAAkB;UAkBdnM,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAA6B,UAAA,UAAAka,GAAA,CAAA5P,YAAA,CAAmB;UA8B5BnM,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAAqE,WAAA,iBAAA0X,GAAA,CAAA3P,UAAA,CAAiC,mBAAA2P,GAAA,CAAA3P,UAAA;UAwBlCpM,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAA6B,UAAA,SAAAka,GAAA,CAAAhR,SAAA,CAAe;UAaf/K,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAA6B,UAAA,UAAAka,GAAA,CAAAhR,SAAA,IAAAgR,GAAA,CAAAlV,QAAA,CAAAL,MAAA,OAAyC;UAetCxG,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAA6B,UAAA,UAAAka,GAAA,CAAAhR,SAAA,IAAAgR,GAAA,CAAAlV,QAAA,CAAAL,MAAA,KAAuC;UAiN3CxG,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAA6B,UAAA,cAAAka,GAAA,CAAAxP,WAAA,CAAyB;UAUrBvM,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAqE,WAAA,iBAAA0X,GAAA,CAAA7Q,eAAA,CAAsC,mBAAA6Q,GAAA,CAAA7Q,eAAA;UAUtClL,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAqE,WAAA,iBAAA0X,GAAA,CAAA5Q,kBAAA,CAAyC,mBAAA4Q,GAAA,CAAA5Q,kBAAA;UAezCnL,EAAA,CAAAK,SAAA,GAAsE;UAAtEL,EAAA,CAAA6B,UAAA,cAAAka,GAAA,CAAAvb,gBAAA,IAAAub,GAAA,CAAApU,gBAAA,IAAAoU,GAAA,CAAA5T,gBAAA,CAAsE;UAiBrEnI,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAA6B,UAAA,YAAA6b,QAAA,GAAA3B,GAAA,CAAAxP,WAAA,CAAAoB,GAAA,8BAAA+P,QAAA,CAAAlP,KAAA,kBAAAkP,QAAA,CAAAlP,KAAA,CAAAC,IAAA,IAAgD;UAgBhDzO,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAA6B,UAAA,UAAA8b,QAAA,GAAA5B,GAAA,CAAAxP,WAAA,CAAAoB,GAAA,8BAAAgQ,QAAA,CAAAnP,KAAA,kBAAAmP,QAAA,CAAAnP,KAAA,CAAAC,IAAA,GAA+C;UAgBrDzO,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAA6B,UAAA,SAAAka,GAAA,CAAA7Q,eAAA,CAAqB;UAgCrBlL,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAA6B,UAAA,SAAAka,GAAA,CAAA5Q,kBAAA,CAAwB;UAmEzBnL,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAA6B,UAAA,WAAAka,GAAA,CAAAhF,kBAAA,GAA+B;UAM9B/W,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAA6B,UAAA,SAAAka,GAAA,CAAA7Q,eAAA,IAAA6Q,GAAA,CAAA5Q,kBAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}