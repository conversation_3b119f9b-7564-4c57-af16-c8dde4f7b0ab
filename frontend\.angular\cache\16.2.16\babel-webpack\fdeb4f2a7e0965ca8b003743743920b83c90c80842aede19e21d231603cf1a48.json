{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/toast.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 46);\n  }\n}\nfunction MessageChatComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" DEBUG: otherParticipant is null/undefined \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" DEBUG: \", ctx_r2.otherParticipant.username, \" (ID: \", ctx_r2.otherParticipant.id, \") \");\n  }\n}\nfunction MessageChatComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 50);\n    i0.ɵɵelement(4, \"div\", 51)(5, \"div\", 52)(6, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.isOnline) ? \"En ligne\" : ctx_r4.formatLastActive(ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵelement(1, \"div\", 55);\n    i0.ɵɵelementStart(2, \"span\", 56);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵelement(2, \"i\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 60);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 61);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r7.otherParticipant == null ? null : ctx_r7.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"span\", 77);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r18 = i0.ɵɵnextContext().$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.formatDateSeparator(message_r18.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"img\", 79);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const message_r18 = i0.ɵɵnextContext().$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.openUserProfile(message_r18.sender == null ? null : message_r18.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r18.sender == null ? null : message_r18.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r18.sender == null ? null : message_r18.sender.username);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r18 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r22.getUserColor(message_r18.sender == null ? null : message_r18.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r18.sender == null ? null : message_r18.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵelement(1, \"div\", 82);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r18 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r23.formatMessageContent(message_r18.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 88);\n  }\n  if (rf & 2) {\n    const message_r18 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r35 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r35.formatMessageContent(message_r18.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 83);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_7_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const message_r18 = i0.ɵɵnextContext().$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.openImageViewer(message_r18));\n    });\n    i0.ɵɵelement(2, \"img\", 84);\n    i0.ɵɵelementStart(3, \"div\", 85);\n    i0.ɵɵelement(4, \"i\", 86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, MessageChatComponent_div_29_ng_container_1_div_7_div_5_Template, 1, 1, \"div\", 87);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r18 = i0.ɵɵnextContext().$implicit;\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r24.getImageUrl(message_r18), i0.ɵɵsanitizeUrl)(\"alt\", message_r18.content || \"Image\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", message_r18.content);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const message_r18 = i0.ɵɵnextContext().$implicit;\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.downloadFile(message_r18));\n    });\n    i0.ɵɵelementStart(1, \"div\", 90);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8)(4, \"div\", 91);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 77);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 92);\n    i0.ɵɵelement(9, \"i\", 93);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r18 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r25.getFileIcon(message_r18));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getFileName(message_r18), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getFileSize(message_r18), \" \");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 99);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 100);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 101);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 102);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_12_i_1_Template, 1, 0, \"i\", 95);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_ng_container_1_div_12_i_2_Template, 1, 0, \"i\", 96);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_12_i_3_Template, 1, 0, \"i\", 97);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_29_ng_container_1_div_12_i_4_Template, 1, 0, \"i\", 98);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r18.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r18.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r18.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r18.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_13_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_div_13_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const reaction_r51 = restoredCtx.$implicit;\n      const message_r18 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r52 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r52.toggleReaction(message_r18.id, reaction_r51.emoji));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"1\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reaction_r51 = ctx.$implicit;\n    const ctx_r50 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r50.hasUserReacted(reaction_r51, ctx_r50.currentUserId || \"\"))(\"text-green-600\", ctx_r50.hasUserReacted(reaction_r51, ctx_r50.currentUserId || \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r51.emoji);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_13_button_1_Template, 5, 5, \"button\", 104);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", message_r18.reactions);\n  }\n}\nfunction MessageChatComponent_div_29_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_div_1_Template, 4, 1, \"div\", 65);\n    i0.ɵɵelementStart(2, \"div\", 66);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r57);\n      const message_r18 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.onMessageClick(message_r18, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r57);\n      const message_r18 = restoredCtx.$implicit;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.onMessageContextMenu(message_r18, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_29_ng_container_1_div_3_Template, 2, 2, \"div\", 67);\n    i0.ɵɵelementStart(4, \"div\", 68);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_29_ng_container_1_div_5_Template, 2, 3, \"div\", 69);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_29_ng_container_1_div_6_Template, 2, 1, \"div\", 70);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_29_ng_container_1_div_7_Template, 6, 3, \"div\", 28);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_29_ng_container_1_div_8_Template, 10, 4, \"div\", 71);\n    i0.ɵɵelementStart(9, \"div\", 72)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, MessageChatComponent_div_29_ng_container_1_div_12_Template, 5, 4, \"div\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, MessageChatComponent_div_29_ng_container_1_div_13_Template, 2, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r18 = ctx.$implicit;\n    const i_r19 = ctx.index;\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.shouldShowDateSeparator(i_r19));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"justify-end\", (message_r18.sender == null ? null : message_r18.sender.id) === ctx_r16.currentUserId)(\"justify-start\", (message_r18.sender == null ? null : message_r18.sender.id) !== ctx_r16.currentUserId);\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r18.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r18.sender == null ? null : message_r18.sender.id) !== ctx_r16.currentUserId && ctx_r16.shouldShowAvatar(i_r19));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"bg-green-500\", (message_r18.sender == null ? null : message_r18.sender.id) === ctx_r16.currentUserId)(\"text-white\", (message_r18.sender == null ? null : message_r18.sender.id) === ctx_r16.currentUserId)(\"bg-white\", (message_r18.sender == null ? null : message_r18.sender.id) !== ctx_r16.currentUserId)(\"text-gray-900\", (message_r18.sender == null ? null : message_r18.sender.id) !== ctx_r16.currentUserId)(\"dark:bg-gray-700\", (message_r18.sender == null ? null : message_r18.sender.id) !== ctx_r16.currentUserId)(\"dark:text-white\", (message_r18.sender == null ? null : message_r18.sender.id) !== ctx_r16.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.isGroupConversation() && (message_r18.sender == null ? null : message_r18.sender.id) !== ctx_r16.currentUserId && ctx_r16.shouldShowSenderName(i_r19));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.getMessageType(message_r18) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.hasImage(message_r18));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.hasFile(message_r18));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r16.formatMessageTime(message_r18.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r18.sender == null ? null : message_r18.sender.id) === ctx_r16.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r18.reactions && message_r18.reactions.length > 0);\n  }\n}\nfunction MessageChatComponent_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"img\", 107);\n    i0.ɵɵelementStart(2, \"div\", 108)(3, \"div\", 50);\n    i0.ɵɵelement(4, \"div\", 109)(5, \"div\", 110)(6, \"div\", 111);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r17.otherParticipant == null ? null : ctx_r17.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r17.otherParticipant == null ? null : ctx_r17.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_ng_container_1_Template, 14, 26, \"ng-container\", 63);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_29_div_2_Template, 7, 2, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.messages)(\"ngForTrackBy\", ctx_r8.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isTyping);\n  }\n}\nfunction MessageChatComponent_button_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 112);\n    i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_button_42_Template_button_mousedown_0_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.startVoiceRecording());\n    })(\"mouseup\", function MessageChatComponent_button_42_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.stopVoiceRecording());\n    })(\"mouseleave\", function MessageChatComponent_button_42_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.cancelVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 113);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"bg-red-500\", ctx_r10.isRecordingVoice)(\"hover:bg-red-600\", ctx_r10.isRecordingVoice)(\"animate-pulse\", ctx_r10.isRecordingVoice);\n  }\n}\nfunction MessageChatComponent_button_43_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 117);\n  }\n}\nfunction MessageChatComponent_button_43_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 118);\n  }\n}\nfunction MessageChatComponent_button_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_43_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.sendMessage());\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_button_43_i_1_Template, 1, 0, \"i\", 115);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_43_i_2_Template, 1, 0, \"i\", 116);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r11.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.isSendingMessage);\n  }\n}\nfunction MessageChatComponent_div_44_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_44_button_3_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r71);\n      const category_r69 = restoredCtx.$implicit;\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.selectEmojiCategory(category_r69));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r69 = ctx.$implicit;\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r67.selectedEmojiCategory === category_r69)(\"text-green-600\", ctx_r67.selectedEmojiCategory === category_r69)(\"hover:bg-gray-100\", ctx_r67.selectedEmojiCategory !== category_r69)(\"dark:hover:bg-gray-700\", ctx_r67.selectedEmojiCategory !== category_r69);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r69.icon, \" \");\n  }\n}\nfunction MessageChatComponent_div_44_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 126);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_44_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r74);\n      const emoji_r72 = restoredCtx.$implicit;\n      const ctx_r73 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r73.insertEmoji(emoji_r72));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r72 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r72.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r72.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"div\", 120)(2, \"div\", 121);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_44_button_3_Template, 2, 9, \"button\", 122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 123);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_44_button_5_Template, 2, 2, \"button\", 124);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.emojiCategories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getEmojisForCategory(ctx_r12.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"div\", 120)(2, \"div\", 128)(3, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 130);\n    i0.ɵɵelement(5, \"i\", 131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 132);\n    i0.ɵɵtext(7, \"Photos\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.triggerFileInput(\"video\"));\n    });\n    i0.ɵɵelementStart(9, \"div\", 133);\n    i0.ɵɵelement(10, \"i\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 132);\n    i0.ɵɵtext(12, \"Vid\\u00E9os\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 135);\n    i0.ɵɵelement(15, \"i\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 132);\n    i0.ɵɵtext(17, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.openCamera());\n    });\n    i0.ɵɵelementStart(19, \"div\", 137);\n    i0.ɵɵelement(20, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 132);\n    i0.ɵɵtext(22, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r81 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 139);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_48_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r81);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class MessageChatComponent {\n  constructor(fb, route, MessageService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.MessageService = MessageService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    // === ENREGISTREMENT VOCAL ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    // === APPELS ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  ngOnInit() {\n    this.initializeComponent();\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n  }\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('Raw user from localStorage:', userString);\n      const user = JSON.parse(userString || '{}');\n      console.log('Parsed user object:', user);\n      if (user && user._id) {\n        this.currentUserId = user._id;\n        this.currentUsername = user.username || 'You';\n        console.log('Current user loaded:', {\n          id: this.currentUserId,\n          username: this.currentUsername\n        });\n      } else {\n        console.warn('No valid user found in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement de l'utilisateur:\", error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    // Utiliser getConversation avec les paramètres de pagination\n    this.MessageService.getConversation(conversationId, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        console.log('Conversation loaded successfully:', conversation);\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    this.messages = this.conversation.messages || [];\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  setOtherParticipant() {\n    if (this.conversation?.participants) {\n      console.log('Setting other participant...');\n      console.log('Current user ID:', this.currentUserId);\n      console.log('All participants:', this.conversation.participants);\n      // Essayer différentes méthodes pour trouver l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        // Comparaison stricte des IDs en string\n        return String(participantId) !== String(this.currentUserId);\n      });\n      console.log('Other participant found:', this.otherParticipant);\n      // Si otherParticipant n'est pas trouvé, essayons avec une logique différente\n      if (!this.otherParticipant && this.conversation.participants.length > 0) {\n        console.log('Fallback: using first participant');\n        this.otherParticipant = this.conversation.participants[0];\n        // Si le premier participant est l'utilisateur actuel, prendre le deuxième\n        if (this.conversation.participants.length > 1 && (String(this.otherParticipant.id) === String(this.currentUserId) || String(this.otherParticipant._id) === String(this.currentUserId))) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n      // Vérification finale\n      if (this.otherParticipant) {\n        console.log('Final other participant:', {\n          id: this.otherParticipant.id || this.otherParticipant._id,\n          username: this.otherParticipant.username,\n          image: this.otherParticipant.image,\n          isOnline: this.otherParticipant.isOnline\n        });\n      } else {\n        console.warn('No other participant found!');\n      }\n    }\n  }\n  setupSubscriptions() {\n    // Abonnements GraphQL pour les mises à jour en temps réel\n    // À implémenter selon vos besoins\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const otherParticipant = this.conversation.participants?.find(p => (p.id || p._id) !== this.currentUserId);\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        this.messages.push(message);\n        this.messageForm.reset();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n      }\n    });\n  }\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n    for (let file of files) {\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    const otherParticipant = this.conversation?.participants?.find(p => (p.id || p._id) !== this.currentUserId);\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    this.MessageService.sendMessage(receiverId, '', file, undefined, this.conversation.id).subscribe({\n      next: message => {\n        this.messages.push(message);\n        this.scrollToBottom();\n        this.toastService.showSuccess('Fichier envoyé avec succès');\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'envoi du fichier:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n      }\n    });\n  }\n  // === ENREGISTREMENT VOCAL ===\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          }\n        });\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: 'audio/webm;codecs=opus'\n        });\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          _this.cdr.detectChanges();\n        }, 1000);\n        _this.mediaRecorder.ondataavailable = event => {\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.start(100);\n        _this.toastService.showSuccess('Enregistrement vocal démarré');\n      } catch (error) {\n        console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n        _this.toastService.showError(\"Impossible d'accéder au microphone\");\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this2.audioChunks.length === 0) {\n          _this2.toastService.showWarning('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: 'audio/webm;codecs=opus'\n        });\n        if (_this2.voiceRecordingDuration < 1) {\n          _this2.toastService.showWarning('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n          type: 'audio/webm;codecs=opus'\n        });\n        yield _this2.sendVoiceMessage(audioFile);\n        _this2.toastService.showSuccess('Message vocal envoyé');\n      } catch (error) {\n        console.error(\"Erreur lors du traitement de l'audio:\", error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n      } finally {\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const otherParticipant = _this3.conversation?.participants?.find(p => (p.id || p._id) !== _this3.currentUserId);\n      const receiverId = otherParticipant?.id || otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, undefined, _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === SYSTÈME D'ÉMOJIS ===\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + (emoji.emoji || emoji);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n    setTimeout(() => {\n      const textarea = document.querySelector('textarea[formControlName=\"content\"]');\n      if (textarea) {\n        textarea.focus();\n      }\n    }, 100);\n  }\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    this.showAttachmentMenu = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n  // === MÉTHODES POUR LES MESSAGES ===\n  getMessageType(message) {\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    return 'text';\n  }\n  hasImage(message) {\n    return message.attachments?.some(att => att.type?.startsWith('image/')) || false;\n  }\n  hasFile(message) {\n    return message.attachments?.some(att => !att.type?.startsWith('image/')) || false;\n  }\n  getImageUrl(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    return imageAttachment?.url || '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  isGroupConversation() {\n    return this.conversation?.isGroup || false;\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      window.open(fileAttachment.url, '_blank');\n    }\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      window.open(imageAttachment.url, '_blank');\n    }\n  }\n  toggleReaction(messageId, emoji) {\n    console.log('Toggle reaction:', messageId, emoji);\n    // Implémenter la logique de réaction\n  }\n\n  hasUserReacted(reaction, userId) {\n    return reaction.users?.includes(userId) || false;\n  }\n  triggerFileInput(type) {\n    // Déclencher la sélection de fichier\n    const input = document.createElement('input');\n    input.type = 'file';\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    }\n    input.onchange = event => {\n      this.onFileSelected(event);\n    };\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  openCamera() {\n    console.log('Opening camera...');\n    // Implémenter l'ouverture de la caméra\n    this.showAttachmentMenu = false;\n  }\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    if (!this.searchMode) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n  toggleMainMenu() {\n    console.log('Toggle main menu');\n    // Implémenter le menu principal\n  }\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const date = new Date(lastActive);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    const diffHours = Math.floor(diffMins / 60);\n    if (diffHours < 24) return `Il y a ${diffHours}h`;\n    const diffDays = Math.floor(diffHours / 24);\n    return `Il y a ${diffDays}j`;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  // === MÉTHODES DE NAVIGATION ===\n  goBackToConversations() {\n    window.history.back();\n  }\n  openUserProfile(userId) {\n    console.log('Opening user profile for:', userId);\n    // Implémenter la navigation vers le profil utilisateur\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {\n    console.log('Message clicked:', message);\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    console.log('Message context menu:', message);\n  }\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n    const content = event.target.value;\n    if (content.trim()) {\n      // Démarrer l'indicateur de frappe\n    } else {\n      // Arrêter l'indicateur de frappe\n    }\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  trackByMessageId(index, message) {\n    return message.id || index;\n  }\n  // === APPELS VIDÉO/AUDIO ===\n  startCall(type) {\n    this.callType = type;\n    this.isInCall = true;\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n    this.toastService.showSuccess(`Appel ${type === 'VIDEO' ? 'vidéo' : 'audio'} démarré`);\n  }\n  endCall() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.toastService.showSuccess('Appel terminé');\n  }\n  startVideoCall() {\n    this.startCall('VIDEO');\n  }\n  startVoiceCall() {\n    this.startCall('AUDIO');\n  }\n  // === RECHERCHE ===\n  toggleSearch() {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  // === PAGINATION ===\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        const newMessages = conversation.messages || [];\n        if (newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début (messages plus anciens)\n          this.messages = [...newMessages, ...this.messages];\n          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      }\n    });\n  }\n  // === GÉOLOCALISATION ===\n  shareLocation() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const position = yield _this4.getCurrentPosition();\n        const {\n          latitude,\n          longitude\n        } = position.coords;\n        const locationMessage = {\n          type: 'location',\n          latitude,\n          longitude,\n          address: yield _this4.getAddressFromCoordinates(latitude, longitude),\n          mapUrl: `https://maps.google.com/maps?q=${latitude},${longitude}&z=15`,\n          timestamp: new Date()\n        };\n        yield _this4.sendLocationMessage(locationMessage);\n        _this4.toastService.showSuccess('Position partagée avec succès');\n      } catch (error) {\n        console.error('Erreur lors du partage de localisation:', error);\n        _this4.toastService.showError(\"Impossible d'obtenir votre position\");\n      }\n    })();\n  }\n  getCurrentPosition() {\n    return new Promise((resolve, reject) => {\n      navigator.geolocation.getCurrentPosition(resolve, reject, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 60000\n      });\n    });\n  }\n  getAddressFromCoordinates(lat, lng) {\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);\n        if (response.ok) {\n          const data = yield response.json();\n          return data.display_name || `${lat}, ${lng}`;\n        }\n      } catch (error) {\n        console.error('Erreur lors du géocodage inverse:', error);\n      }\n      return `${lat}, ${lng}`;\n    })();\n  }\n  sendLocationMessage(locationData) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const otherParticipant = _this5.conversation?.participants?.find(p => (p.id || p._id) !== _this5.currentUserId);\n      const receiverId = otherParticipant?.id || otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this5.MessageService.sendMessage(receiverId, `📍 Position partagée: ${locationData.address}`, undefined, 'TEXT', _this5.conversation.id).subscribe({\n          next: message => {\n            _this5.messages.push(message);\n            _this5.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message de localisation:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  // === PARTAGE DE CONTACTS ===\n  shareContact() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const contact = yield _this6.selectContact();\n        if (contact) {\n          yield _this6.sendContactMessage(contact);\n          _this6.toastService.showSuccess('Contact partagé avec succès');\n        }\n      } catch (error) {\n        console.error('Erreur lors du partage de contact:', error);\n        _this6.toastService.showError('Erreur lors du partage du contact');\n      }\n    })();\n  }\n  selectContact() {\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        setTimeout(() => {\n          resolve({\n            name: 'John Doe',\n            phone: '+33 6 12 34 56 78',\n            email: '<EMAIL>',\n            avatar: 'assets/images/default-avatar.png'\n          });\n        }, 1000);\n      });\n    })();\n  }\n  sendContactMessage(contactData) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const otherParticipant = _this7.conversation?.participants?.find(p => (p.id || p._id) !== _this7.currentUserId);\n      const receiverId = otherParticipant?.id || otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this7.MessageService.sendMessage(receiverId, `👤 Contact partagé: ${contactData.name}\\n📞 ${contactData.phone}\\n📧 ${contactData.email}`, undefined, 'TEXT', _this7.conversation.id).subscribe({\n          next: message => {\n            _this7.messages.push(message);\n            _this7.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message de contact:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  // === UTILITAIRES ===\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n  triggerFileInput(type) {\n    // Optionnel : configurer le type de fichier accepté selon le type\n    if (type === 'image') {\n      this.fileInput.nativeElement.accept = 'image/*';\n    } else if (type === 'video') {\n      this.fileInput.nativeElement.accept = 'video/*';\n    } else if (type === 'document') {\n      this.fileInput.nativeElement.accept = '.pdf,.doc,.docx,.txt,.xlsx,.ppt,.pptx';\n    } else {\n      this.fileInput.nativeElement.accept = '*/*';\n    }\n    this.fileInput.nativeElement.click();\n  }\n  // === MÉTHODES POUR LE TEMPLATE ===\n  goBackToConversations() {\n    // Navigation vers la liste des conversations\n    window.history.back();\n  }\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const now = new Date();\n    const lastActiveDate = new Date(lastActive);\n    const diffInMinutes = Math.floor((now.getTime() - lastActiveDate.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'En ligne';\n    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;\n    if (diffInMinutes < 1440) return `Il y a ${Math.floor(diffInMinutes / 60)} h`;\n    return `Il y a ${Math.floor(diffInMinutes / 1440)} j`;\n  }\n  toggleMainMenu() {\n    // Toggle du menu principal\n    this.showAttachmentMenu = false;\n    this.showEmojiPicker = false;\n  }\n  onScroll(event) {\n    const element = event.target;\n    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {\n      this.loadMoreMessages();\n    }\n  }\n  trackByMessageId(index, message) {\n    return message.id || index;\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) return \"Aujourd'hui\";\n    if (date.toDateString() === yesterday.toDateString()) return 'Hier';\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  onMessageClick(message, event) {\n    // Gestion du clic sur un message\n    console.log('Message clicked:', message);\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    // Gestion du menu contextuel\n    console.log('Message context menu:', message);\n  }\n  shouldShowAvatar(index) {\n    if (index === this.messages.length - 1) return true;\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    return !nextMessage || currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  openUserProfile(userId) {\n    // Navigation vers le profil utilisateur\n    console.log('Open user profile:', userId);\n  }\n  isGroupConversation() {\n    return this.conversation?.participants?.length > 2;\n  }\n  shouldShowSenderName(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getUserColor(userId) {\n    // Génère une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];\n    const hash = userId.split('').reduce((a, b) => {\n      a = (a << 5) - a + b.charCodeAt(0);\n      return a & a;\n    }, 0);\n    return colors[Math.abs(hash) % colors.length];\n  }\n  getMessageType(message) {\n    if (message.file) {\n      if (message.file.type?.startsWith('image/')) return 'image';\n      if (message.file.type?.startsWith('video/')) return 'video';\n      if (message.file.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    return 'text';\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Formatage basique du contenu (liens, emojis, etc.)\n    return content.replace(/\\n/g, '<br>');\n  }\n  hasImage(message) {\n    return message.file && message.file.type?.startsWith('image/');\n  }\n  openImageViewer(message) {\n    // Ouvre la visionneuse d'images\n    console.log('Open image viewer:', message);\n  }\n  getImageUrl(message) {\n    return message.file?.url || message.file?.path || '';\n  }\n  hasFile(message) {\n    return message.file && !message.file.type?.startsWith('image/');\n  }\n  downloadFile(message) {\n    if (message.file?.url) {\n      const link = document.createElement('a');\n      link.href = message.file.url;\n      link.download = message.file.name || 'file';\n      link.click();\n    }\n  }\n  getFileIcon(message) {\n    if (!message.file?.type) return 'fas fa-file';\n    if (message.file.type.startsWith('video/')) return 'fas fa-video';\n    if (message.file.type.startsWith('audio/')) return 'fas fa-music';\n    if (message.file.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (message.file.type.includes('word')) return 'fas fa-file-word';\n    if (message.file.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getFileName(message) {\n    return message.file?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    if (!message.file?.size) return '';\n    return this.formatFileSize(message.file.size);\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    return new Date(timestamp).toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // === MÉTHODES D'INTERACTION ===\n  toggleReaction(messageId, emoji) {\n    // Gestion des réactions aux messages\n    console.log('Toggle reaction:', messageId, emoji);\n  }\n  hasUserReacted(reaction, userId) {\n    return reaction.users?.includes(userId) || false;\n  }\n  onInputChange(event) {\n    // Gestion du changement de texte dans l'input\n    const value = event.target.value;\n    if (value.trim() && !this.isTyping) {\n      this.isTyping = true;\n      // Envoyer signal de frappe\n    } else if (!value.trim() && this.isTyping) {\n      this.isTyping = false;\n      // Arrêter signal de frappe\n    }\n  }\n\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Marquer les messages comme lus\n    this.markMessagesAsRead();\n  }\n  onInputBlur() {\n    // Arrêter le signal de frappe\n    this.isTyping = false;\n  }\n  markMessagesAsRead() {\n    // Marquer tous les messages non lus comme lus\n    console.log('Mark messages as read');\n  }\n  openCamera() {\n    // Ouvrir la caméra pour prendre une photo\n    console.log('Open camera');\n  }\n  // === GESTION DES ERREURS ===\n  handleError(error, context) {\n    console.error(`Erreur dans ${context}:`, error);\n    let message = \"Une erreur inattendue s'est produite\";\n    if (error?.message) {\n      message = error.message;\n    } else if (typeof error === 'string') {\n      message = error;\n    }\n    this.toastService.showError(message);\n  }\n  // === NETTOYAGE ===\n  ngOnDestroy() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    this.subscriptions.unsubscribe();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 49,\n      vars: 31,\n      consts: [[1, \"flex\", \"flex-col\", \"h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-green-50\", \"dark:from-gray-900\", \"dark:to-gray-800\"], [1, \"flex\", \"items-center\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-gray-800\", \"border-b\", \"border-gray-200\", \"dark:border-gray-700\", \"shadow-sm\"], [1, \"p-2\", \"mr-3\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"text-gray-600\", \"dark:text-gray-300\"], [1, \"flex\", \"items-center\", \"flex-1\", \"min-w-0\"], [1, \"relative\", \"mr-3\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-green-500\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [\"class\", \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\", 4, \"ngIf\"], [1, \"flex-1\", \"min-w-0\"], [1, \"font-semibold\", \"text-gray-900\", \"dark:text-white\", \"truncate\"], [\"class\", \"text-xs text-red-500\", 4, \"ngIf\"], [\"class\", \"text-xs text-blue-500\", 4, \"ngIf\"], [1, \"text-sm\", \"text-gray-500\", \"dark:text-gray-400\"], [\"class\", \"flex items-center gap-1 text-green-600\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"items-center\", \"gap-2\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Appel vocal\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Rechercher\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Menu\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"flex-1\", \"overflow-y-auto\", \"p-4\", \"space-y-4\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"flex flex-col items-center justify-center py-8\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [1, \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"p-4\"], [1, \"flex\", \"items-end\", \"gap-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"gap-2\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Joindre un fichier\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [1, \"flex-1\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", \"rows\", \"1\", \"maxlength\", \"4096\", \"autocomplete\", \"off\", \"spellcheck\", \"true\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-gray-50\", \"dark:bg-gray-700\", \"border\", \"border-gray-200\", \"dark:border-gray-600\", \"rounded-2xl\", \"resize-none\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-green-500\", \"focus:border-transparent\", \"text-gray-900\", \"dark:text-white\", \"placeholder-gray-500\", \"dark:placeholder-gray-400\", 3, \"disabled\", \"input\", \"keydown\", \"focus\", \"blur\"], [\"messageTextarea\", \"\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\", \"title\", \"Maintenir pour enregistrer\", 3, \"bg-red-500\", \"hover:bg-red-600\", \"animate-pulse\", \"mousedown\", \"mouseup\", \"mouseleave\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\", \"title\", \"Envoyer\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 1, \"hidden\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-25 z-40\", 3, \"click\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"right-0\", \"w-3\", \"h-3\", \"bg-green-500\", \"border-2\", \"border-white\", \"dark:border-gray-800\", \"rounded-full\", \"animate-pulse\"], [1, \"text-xs\", \"text-red-500\"], [1, \"text-xs\", \"text-blue-500\"], [1, \"flex\", \"items-center\", \"gap-1\", \"text-green-600\"], [1, \"flex\", \"gap-1\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-green-500\", \"mb-4\"], [1, \"text-gray-500\", \"dark:text-gray-400\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"text-6xl\", \"text-gray-300\", \"dark:text-gray-600\", \"mb-4\"], [1, \"fas\", \"fa-comments\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-700\", \"dark:text-gray-300\", \"mb-2\"], [1, \"text-gray-500\", \"dark:text-gray-400\", \"text-center\"], [1, \"space-y-2\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"flex items-start gap-2\", 4, \"ngIf\"], [\"class\", \"flex justify-center my-4\", 4, \"ngIf\"], [1, \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"class\", \"mr-2 flex-shrink-0\", 4, \"ngIf\"], [1, \"max-w-xs\", \"lg:max-w-md\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\", \"relative\", \"group\"], [\"class\", \"text-xs font-semibold mb-1 opacity-75\", 3, \"color\", 4, \"ngIf\"], [\"class\", \"break-words\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-end\", \"gap-1\", \"mt-1\", \"text-xs\", \"opacity-75\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex gap-1 mt-2\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-4\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-3\", \"py-1\", \"rounded-full\", \"shadow-sm\"], [1, \"text-xs\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"mr-2\", \"flex-shrink-0\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [1, \"text-xs\", \"font-semibold\", \"mb-1\", \"opacity-75\"], [1, \"break-words\"], [3, \"innerHTML\"], [1, \"relative\", \"cursor-pointer\", \"rounded-lg\", \"overflow-hidden\", 3, \"click\"], [1, \"max-w-full\", \"h-auto\", \"rounded-lg\", \"hover:opacity-90\", \"transition-opacity\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"hover:bg-opacity-10\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-expand\", \"text-white\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [\"class\", \"text-sm\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"text-sm\", 3, \"innerHTML\"], [1, \"flex\", \"items-center\", \"gap-3\", \"p-2\", \"bg-gray-50\", \"dark:bg-gray-600\", \"rounded-lg\", \"cursor-pointer\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"text-2xl\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"font-medium\", \"text-sm\", \"truncate\"], [1, \"p-1\", \"rounded-full\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-400\", \"transition-colors\"], [1, \"fas\", \"fa-download\", \"text-sm\"], [1, \"flex\", \"items-center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double text-blue-400\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", \"text-blue-400\"], [1, \"flex\", \"gap-1\", \"mt-2\"], [\"class\", \"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\", 3, \"bg-green-100\", \"text-green-600\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"gap-1\", \"px-2\", \"py-1\", \"bg-gray-100\", \"dark:bg-gray-600\", \"rounded-full\", \"text-xs\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", 3, \"mousedown\", \"mouseup\", \"mouseleave\"], [1, \"fas\", \"fa-microphone\"], [\"type\", \"button\", \"title\", \"Envoyer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"right-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"p-4\"], [1, \"flex\", \"gap-2\", \"mb-4\", \"overflow-x-auto\"], [\"class\", \"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\", 3, \"bg-green-100\", \"text-green-600\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-8\", \"gap-2\", \"max-h-48\", \"overflow-y-auto\"], [\"class\", \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-3\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex-shrink-0\", 3, \"click\"], [1, \"p-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-xl\", 3, \"title\", \"click\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"grid\", \"grid-cols-2\", \"gap-3\"], [1, \"flex\", \"flex-col\", \"items-center\", \"gap-2\", \"p-4\", \"rounded-xl\", \"hover:bg-gray-50\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"w-12\", \"h-12\", \"bg-blue-100\", \"dark:bg-blue-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-image\", \"text-blue-600\", \"dark:text-blue-400\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"dark:text-gray-300\"], [1, \"w-12\", \"h-12\", \"bg-purple-100\", \"dark:bg-purple-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-video\", \"text-purple-600\", \"dark:text-purple-400\"], [1, \"w-12\", \"h-12\", \"bg-orange-100\", \"dark:bg-orange-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-file\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"w-12\", \"h-12\", \"bg-green-100\", \"dark:bg-green-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-camera\", \"text-green-600\", \"dark:text-green-400\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-25\", \"z-40\", 3, \"click\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"img\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_6_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MessageChatComponent_div_7_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"h3\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, MessageChatComponent_div_11_Template, 2, 0, \"div\", 10);\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 2, 2, \"div\", 11);\n          i0.ɵɵelementStart(13, \"div\", 12);\n          i0.ɵɵtemplate(14, MessageChatComponent_div_14_Template, 7, 0, \"div\", 13);\n          i0.ɵɵtemplate(15, MessageChatComponent_span_15_Template, 2, 1, \"span\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 15)(17, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_17_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(18, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_23_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(24, \"i\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"main\", 24, 25);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_25_listener($event) {\n            return ctx.onScroll($event);\n          });\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 4, 0, \"div\", 26);\n          i0.ɵɵtemplate(28, MessageChatComponent_div_28_Template, 7, 1, \"div\", 27);\n          i0.ɵɵtemplate(29, MessageChatComponent_div_29_Template, 3, 3, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"footer\", 29)(31, \"form\", 30);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_31_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(32, \"div\", 31)(33, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_33_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(34, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_35_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(36, \"i\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 36)(38, \"textarea\", 37, 38);\n          i0.ɵɵlistener(\"input\", function MessageChatComponent_Template_textarea_input_38_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"keydown\", function MessageChatComponent_Template_textarea_keydown_38_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_38_listener() {\n            return ctx.onInputFocus();\n          })(\"blur\", function MessageChatComponent_Template_textarea_blur_38_listener() {\n            return ctx.onInputBlur();\n          });\n          i0.ɵɵtext(40, \"        \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 31);\n          i0.ɵɵtemplate(42, MessageChatComponent_button_42_Template, 2, 6, \"button\", 39);\n          i0.ɵɵtemplate(43, MessageChatComponent_button_43_Template, 3, 3, \"button\", 40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(44, MessageChatComponent_div_44_Template, 6, 2, \"div\", 41);\n          i0.ɵɵtemplate(45, MessageChatComponent_div_45_Template, 23, 0, \"div\", 42);\n          i0.ɵɵelementStart(46, \"input\", 43, 44);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_46_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, MessageChatComponent_div_48_Template, 1, 0, \"div\", 45);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_19_0;\n          let tmp_20_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.otherParticipant);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.searchMode)(\"text-green-600\", ctx.searchMode);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showEmojiPicker)(\"text-green-600\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showAttachmentMenu)(\"text-green-600\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.otherParticipant || ctx.isRecordingVoice || ctx.isSendingMessage);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_19_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_19_0.value == null ? null : tmp_19_0.value.trim()));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_20_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_20_0.value == null ? null : tmp_20_0.value.trim());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r2", "otherParticipant", "username", "id", "ɵɵtextInterpolate1", "ctx_r4", "isOnline", "formatLastActive", "lastActive", "ctx_r7", "ctx_r20", "formatDateSeparator", "message_r18", "timestamp", "ɵɵlistener", "MessageChatComponent_div_29_ng_container_1_div_3_Template_img_click_1_listener", "ɵɵrestoreView", "_r31", "ɵɵnextContext", "$implicit", "ctx_r29", "ɵɵresetView", "openUserProfile", "sender", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r22", "getUserColor", "ctx_r23", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r35", "MessageChatComponent_div_29_ng_container_1_div_7_Template_div_click_1_listener", "_r39", "ctx_r37", "openImageViewer", "ɵɵtemplate", "MessageChatComponent_div_29_ng_container_1_div_7_div_5_Template", "ctx_r24", "getImageUrl", "MessageChatComponent_div_29_ng_container_1_div_8_Template_div_click_0_listener", "_r43", "ctx_r41", "downloadFile", "ɵɵclassMap", "ctx_r25", "getFileIcon", "getFileName", "getFileSize", "MessageChatComponent_div_29_ng_container_1_div_12_i_1_Template", "MessageChatComponent_div_29_ng_container_1_div_12_i_2_Template", "MessageChatComponent_div_29_ng_container_1_div_12_i_3_Template", "MessageChatComponent_div_29_ng_container_1_div_12_i_4_Template", "status", "MessageChatComponent_div_29_ng_container_1_div_13_button_1_Template_button_click_0_listener", "restoredCtx", "_r54", "reaction_r51", "ctx_r52", "toggleReaction", "emoji", "ɵɵclassProp", "ctx_r50", "hasUserReacted", "currentUserId", "ɵɵtextInterpolate", "MessageChatComponent_div_29_ng_container_1_div_13_button_1_Template", "reactions", "ɵɵelementContainerStart", "MessageChatComponent_div_29_ng_container_1_div_1_Template", "MessageChatComponent_div_29_ng_container_1_Template_div_click_2_listener", "$event", "_r57", "ctx_r56", "onMessageClick", "MessageChatComponent_div_29_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r58", "onMessageContextMenu", "MessageChatComponent_div_29_ng_container_1_div_3_Template", "MessageChatComponent_div_29_ng_container_1_div_5_Template", "MessageChatComponent_div_29_ng_container_1_div_6_Template", "MessageChatComponent_div_29_ng_container_1_div_7_Template", "MessageChatComponent_div_29_ng_container_1_div_8_Template", "MessageChatComponent_div_29_ng_container_1_div_12_Template", "MessageChatComponent_div_29_ng_container_1_div_13_Template", "ɵɵelementContainerEnd", "ctx_r16", "shouldShowDateSeparator", "i_r19", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "hasFile", "formatMessageTime", "length", "ctx_r17", "MessageChatComponent_div_29_ng_container_1_Template", "MessageChatComponent_div_29_div_2_Template", "ctx_r8", "messages", "trackByMessageId", "isTyping", "MessageChatComponent_button_42_Template_button_mousedown_0_listener", "_r60", "ctx_r59", "startVoiceRecording", "MessageChatComponent_button_42_Template_button_mouseup_0_listener", "ctx_r61", "stopVoiceRecording", "MessageChatComponent_button_42_Template_button_mouseleave_0_listener", "ctx_r62", "cancelVoiceRecording", "ctx_r10", "isRecordingVoice", "MessageChatComponent_button_43_Template_button_click_0_listener", "_r66", "ctx_r65", "sendMessage", "MessageChatComponent_button_43_i_1_Template", "MessageChatComponent_button_43_i_2_Template", "ctx_r11", "isSendingMessage", "MessageChatComponent_div_44_button_3_Template_button_click_0_listener", "_r71", "category_r69", "ctx_r70", "selectEmojiCategory", "ctx_r67", "selectedEmojiCategory", "icon", "MessageChatComponent_div_44_button_5_Template_button_click_0_listener", "_r74", "emoji_r72", "ctx_r73", "insert<PERSON><PERSON><PERSON>", "name", "MessageChatComponent_div_44_button_3_Template", "MessageChatComponent_div_44_button_5_Template", "ctx_r12", "emojiCategories", "getEmojisForCategory", "MessageChatComponent_div_45_Template_button_click_3_listener", "_r76", "ctx_r75", "triggerFileInput", "MessageChatComponent_div_45_Template_button_click_8_listener", "ctx_r77", "MessageChatComponent_div_45_Template_button_click_13_listener", "ctx_r78", "MessageChatComponent_div_45_Template_button_click_18_listener", "ctx_r79", "openCamera", "MessageChatComponent_div_48_Template_div_click_0_listener", "_r81", "ctx_r80", "closeAllMenus", "MessageChatComponent", "constructor", "fb", "route", "MessageService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "voiceRecordingDuration", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "isInCall", "callType", "callDuration", "callTimer", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isUserTyping", "searchMode", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "initializeComponent", "loadCurrentUser", "loadConversation", "setupSubscriptions", "userString", "localStorage", "getItem", "console", "log", "user", "JSON", "parse", "_id", "warn", "error", "conversationId", "snapshot", "paramMap", "get", "showError", "getConversation", "subscribe", "next", "setOtherParticipant", "loadMessages", "scrollToBottom", "participants", "find", "p", "participantId", "String", "valid", "value", "trim", "receiverId", "undefined", "message", "push", "reset", "onFileSelected", "event", "files", "target", "file", "uploadFile", "showSuccess", "_this", "_asyncToGenerator", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "MediaRecorder", "mimeType", "setInterval", "detectChanges", "ondataavailable", "data", "size", "onstop", "processRecordedAudio", "start", "state", "stop", "getTracks", "for<PERSON>ach", "track", "clearInterval", "_this2", "showWarning", "audioBlob", "Blob", "type", "audioFile", "File", "Date", "now", "sendVoiceMessage", "_this3", "Error", "Promise", "resolve", "reject", "formatRecordingDuration", "duration", "minutes", "Math", "floor", "seconds", "toString", "padStart", "category", "currentC<PERSON>nt", "newContent", "patchValue", "setTimeout", "textarea", "document", "querySelector", "focus", "toggleEmojiPicker", "toggleAttachmentMenu", "attachments", "attachment", "startsWith", "some", "att", "imageAttachment", "url", "fileAttachment", "bytes", "round", "includes", "index", "currentMessage", "previousMessage", "currentDate", "toDateString", "previousDate", "nextMessage", "isGroup", "userId", "colors", "charCodeAt", "window", "open", "messageId", "reaction", "users", "input", "createElement", "accept", "onchange", "click", "toggleSearch", "toggleMainMenu", "date", "diffMs", "getTime", "diffMins", "diffHours", "diffDays", "toLocaleTimeString", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toLocaleDateString", "urlRegex", "replace", "goBackToConversations", "history", "back", "preventDefault", "onInputChange", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "element", "scrollTop", "loadMoreMessages", "startCall", "endCall", "startVideoCall", "startVoiceCall", "searchMessages", "filter", "toLowerCase", "newMessages", "shareLocation", "_this4", "position", "getCurrentPosition", "latitude", "longitude", "coords", "locationMessage", "address", "getAddressFromCoordinates", "mapUrl", "sendLocationMessage", "geolocation", "enableHighAccuracy", "timeout", "maximumAge", "lat", "lng", "response", "fetch", "ok", "json", "display_name", "locationData", "_this5", "shareContact", "_this6", "contact", "selectContact", "sendContactMessage", "phone", "email", "avatar", "contactData", "_this7", "messagesContainer", "nativeElement", "scrollHeight", "formatFileSize", "k", "sizes", "i", "parseFloat", "pow", "toFixed", "formatDuration", "hours", "secs", "getFileAcceptTypes", "fileInput", "lastActiveDate", "diffInMinutes", "weekday", "year", "month", "day", "hash", "split", "reduce", "a", "b", "abs", "path", "link", "href", "download", "markMessagesAsRead", "handleError", "context", "ngOnDestroy", "clearTimeout", "unsubscribe", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "i3", "i4", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_Template_img_click_6_listener", "MessageChatComponent_div_7_Template", "MessageChatComponent_div_11_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_div_14_Template", "MessageChatComponent_span_15_Template", "MessageChatComponent_Template_button_click_17_listener", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_Template_button_click_23_listener", "MessageChatComponent_Template_main_scroll_25_listener", "MessageChatComponent_div_27_Template", "MessageChatComponent_div_28_Template", "MessageChatComponent_div_29_Template", "MessageChatComponent_Template_form_ngSubmit_31_listener", "MessageChatComponent_Template_button_click_33_listener", "MessageChatComponent_Template_button_click_35_listener", "MessageChatComponent_Template_textarea_input_38_listener", "MessageChatComponent_Template_textarea_keydown_38_listener", "MessageChatComponent_Template_textarea_focus_38_listener", "MessageChatComponent_Template_textarea_blur_38_listener", "MessageChatComponent_button_42_Template", "MessageChatComponent_button_43_Template", "MessageChatComponent_div_44_Template", "MessageChatComponent_div_45_Template", "MessageChatComponent_Template_input_change_46_listener", "MessageChatComponent_div_48_Template", "tmp_19_0", "tmp_20_0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Component,\n  On<PERSON>ni<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>hild,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { ToastService } from '../../../../services/toast.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent implements OnInit, OnDestroy {\n  // === RÉFÉRENCES DOM ===\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === DONNÉES PRINCIPALES ===\n  conversation: any = null;\n  messages: any[] = [];\n  currentUserId: string | null = null;\n  currentUsername = 'You';\n  messageForm: FormGroup;\n  otherParticipant: any = null;\n\n  // === ÉTATS DE L'INTERFACE ===\n  isLoading = false;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  showEmojiPicker = false;\n  showAttachmentMenu = false;\n  showSearch = false;\n  searchQuery = '';\n  searchResults: any[] = [];\n\n  // === ENREGISTREMENT VOCAL ===\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n\n  // === APPELS ===\n  isInCall = false;\n  callType: 'VIDEO' | 'AUDIO' | null = null;\n  callDuration = 0;\n  private callTimer: any = null;\n\n  // === ÉMOJIS ===\n  emojiCategories: any[] = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n      ],\n    },\n  ];\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  // === PAGINATION ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n\n  // === AUTRES ÉTATS ===\n  isTyping = false;\n  isUserTyping = false;\n  searchMode = false;\n  isSendingMessage = false;\n  private typingTimeout: any = null;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private MessageService: MessageService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  ngOnInit(): void {\n    this.initializeComponent();\n  }\n\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('Raw user from localStorage:', userString);\n\n      const user = JSON.parse(userString || '{}');\n      console.log('Parsed user object:', user);\n\n      if (user && user._id) {\n        this.currentUserId = user._id;\n        this.currentUsername = user.username || 'You';\n        console.log('Current user loaded:', {\n          id: this.currentUserId,\n          username: this.currentUsername,\n        });\n      } else {\n        console.warn('No valid user found in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement de l'utilisateur:\", error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  private loadConversation(): void {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n\n    this.isLoading = true;\n    // Utiliser getConversation avec les paramètres de pagination\n    this.MessageService.getConversation(\n      conversationId,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        console.log('Conversation loaded successfully:', conversation);\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors du chargement de la conversation'\n        );\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private loadMessages(): void {\n    if (!this.conversation?.id) return;\n\n    // Les messages sont déjà chargés avec la conversation\n    this.messages = this.conversation.messages || [];\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n\n  private setOtherParticipant(): void {\n    if (this.conversation?.participants) {\n      console.log('Setting other participant...');\n      console.log('Current user ID:', this.currentUserId);\n      console.log('All participants:', this.conversation.participants);\n\n      // Essayer différentes méthodes pour trouver l'autre participant\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        console.log(\n          'Comparing participant ID:',\n          participantId,\n          'with current user ID:',\n          this.currentUserId\n        );\n        // Comparaison stricte des IDs en string\n        return String(participantId) !== String(this.currentUserId);\n      });\n\n      console.log('Other participant found:', this.otherParticipant);\n\n      // Si otherParticipant n'est pas trouvé, essayons avec une logique différente\n      if (!this.otherParticipant && this.conversation.participants.length > 0) {\n        console.log('Fallback: using first participant');\n        this.otherParticipant = this.conversation.participants[0];\n\n        // Si le premier participant est l'utilisateur actuel, prendre le deuxième\n        if (\n          this.conversation.participants.length > 1 &&\n          (String(this.otherParticipant.id) === String(this.currentUserId) ||\n            String(this.otherParticipant._id) === String(this.currentUserId))\n        ) {\n          console.log(\n            'First participant is current user, using second participant'\n          );\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n\n      // Vérification finale\n      if (this.otherParticipant) {\n        console.log('Final other participant:', {\n          id: this.otherParticipant.id || this.otherParticipant._id,\n          username: this.otherParticipant.username,\n          image: this.otherParticipant.image,\n          isOnline: this.otherParticipant.isOnline,\n        });\n      } else {\n        console.warn('No other participant found!');\n      }\n    }\n  }\n\n  private setupSubscriptions(): void {\n    // Abonnements GraphQL pour les mises à jour en temps réel\n    // À implémenter selon vos besoins\n  }\n\n  // === ENVOI DE MESSAGES ===\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n\n    const otherParticipant = this.conversation.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    this.MessageService.sendMessage(\n      receiverId,\n      content,\n      undefined,\n      'TEXT' as any,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        this.messages.push(message);\n        this.messageForm.reset();\n        this.scrollToBottom();\n      },\n      error: (error: any) => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n      },\n    });\n  }\n\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event: any): void {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n\n    for (let file of files) {\n      this.uploadFile(file);\n    }\n  }\n\n  private uploadFile(file: File): void {\n    const otherParticipant = this.conversation?.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    this.MessageService.sendMessage(\n      receiverId,\n      '',\n      file,\n      undefined,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        this.messages.push(message);\n        this.scrollToBottom();\n        this.toastService.showSuccess('Fichier envoyé avec succès');\n      },\n      error: (error: any) => {\n        console.error(\"Erreur lors de l'envoi du fichier:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n      },\n    });\n  }\n\n  // === ENREGISTREMENT VOCAL ===\n  async startVoiceRecording(): Promise<void> {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n        },\n      });\n\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus',\n      });\n\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        this.cdr.detectChanges();\n      }, 1000);\n\n      this.mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      this.mediaRecorder.onstop = () => {\n        this.processRecordedAudio();\n      };\n\n      this.mediaRecorder.start(100);\n      this.toastService.showSuccess('Enregistrement vocal démarré');\n    } catch (error) {\n      console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n      this.toastService.showError(\"Impossible d'accéder au microphone\");\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    try {\n      if (this.audioChunks.length === 0) {\n        this.toastService.showWarning('Aucun audio enregistré');\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      const audioBlob = new Blob(this.audioChunks, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      if (this.voiceRecordingDuration < 1) {\n        this.toastService.showWarning(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      await this.sendVoiceMessage(audioFile);\n      this.toastService.showSuccess('Message vocal envoyé');\n    } catch (error) {\n      console.error(\"Erreur lors du traitement de l'audio:\", error);\n      this.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n    } finally {\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    const otherParticipant = this.conversation?.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        '',\n        audioFile,\n        undefined,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === SYSTÈME D'ÉMOJIS ===\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + (emoji.emoji || emoji);\n    this.messageForm.patchValue({ content: newContent });\n\n    this.showEmojiPicker = false;\n\n    setTimeout(() => {\n      const textarea = document.querySelector(\n        'textarea[formControlName=\"content\"]'\n      ) as HTMLTextAreaElement;\n      if (textarea) {\n        textarea.focus();\n      }\n    }, 100);\n  }\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    this.showAttachmentMenu = false;\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n\n  // === MÉTHODES POUR LES MESSAGES ===\n  getMessageType(message: any): string {\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    return 'text';\n  }\n\n  hasImage(message: any): boolean {\n    return (\n      message.attachments?.some((att: any) => att.type?.startsWith('image/')) ||\n      false\n    );\n  }\n\n  hasFile(message: any): boolean {\n    return (\n      message.attachments?.some(\n        (att: any) => !att.type?.startsWith('image/')\n      ) || false\n    );\n  }\n\n  getImageUrl(message: any): string {\n    const imageAttachment = message.attachments?.find((att: any) =>\n      att.type?.startsWith('image/')\n    );\n    return imageAttachment?.url || '';\n  }\n\n  getFileName(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    return fileAttachment?.name || 'Fichier';\n  }\n\n  getFileSize(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.size) return '';\n\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  getFileIcon(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.type) return 'fas fa-file';\n\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n\n    if (!nextMessage) return true;\n\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!previousMessage) return true;\n\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n\n  isGroupConversation(): boolean {\n    return this.conversation?.isGroup || false;\n  }\n\n  getUserColor(userId: string): string {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = [\n      '#FF6B6B',\n      '#4ECDC4',\n      '#45B7D1',\n      '#96CEB4',\n      '#FFEAA7',\n      '#DDA0DD',\n      '#98D8C8',\n    ];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  downloadFile(message: any): void {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (fileAttachment?.url) {\n      window.open(fileAttachment.url, '_blank');\n    }\n  }\n\n  openImageViewer(message: any): void {\n    const imageAttachment = message.attachments?.find((att: any) =>\n      att.type?.startsWith('image/')\n    );\n    if (imageAttachment?.url) {\n      window.open(imageAttachment.url, '_blank');\n    }\n  }\n\n  toggleReaction(messageId: string, emoji: string): void {\n    console.log('Toggle reaction:', messageId, emoji);\n    // Implémenter la logique de réaction\n  }\n\n  hasUserReacted(reaction: any, userId: string): boolean {\n    return reaction.users?.includes(userId) || false;\n  }\n\n  triggerFileInput(type?: string): void {\n    // Déclencher la sélection de fichier\n    const input = document.createElement('input');\n    input.type = 'file';\n\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    }\n\n    input.onchange = (event: any) => {\n      this.onFileSelected(event);\n    };\n\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n\n  openCamera(): void {\n    console.log('Opening camera...');\n    // Implémenter l'ouverture de la caméra\n    this.showAttachmentMenu = false;\n  }\n\n  toggleSearch(): void {\n    this.searchMode = !this.searchMode;\n    if (!this.searchMode) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n\n  toggleMainMenu(): void {\n    console.log('Toggle main menu');\n    // Implémenter le menu principal\n  }\n\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\n  formatLastActive(lastActive: string | Date | null): string {\n    if (!lastActive) return 'Hors ligne';\n\n    const date = new Date(lastActive);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n\n    const diffHours = Math.floor(diffMins / 60);\n    if (diffHours < 24) return `Il y a ${diffHours}h`;\n\n    const diffDays = Math.floor(diffHours / 24);\n    return `Il y a ${diffDays}j`;\n  }\n\n  formatMessageTime(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  formatDateSeparator(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n\n  formatMessageContent(content: string): string {\n    if (!content) return '';\n\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(\n      urlRegex,\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\n    );\n  }\n\n  // === MÉTHODES DE NAVIGATION ===\n  goBackToConversations(): void {\n    window.history.back();\n  }\n\n  openUserProfile(userId: string): void {\n    console.log('Opening user profile for:', userId);\n    // Implémenter la navigation vers le profil utilisateur\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message: any, event: any): void {\n    console.log('Message clicked:', message);\n  }\n\n  onMessageContextMenu(message: any, event: any): void {\n    event.preventDefault();\n    console.log('Message context menu:', message);\n  }\n\n  onInputChange(event: any): void {\n    // Gérer les changements dans le champ de saisie\n    const content = event.target.value;\n    if (content.trim()) {\n      // Démarrer l'indicateur de frappe\n    } else {\n      // Arrêter l'indicateur de frappe\n    }\n  }\n\n  onInputKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  onInputFocus(): void {\n    // Gérer le focus sur le champ de saisie\n  }\n\n  onInputBlur(): void {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n\n  onScroll(event: any): void {\n    // Gérer le scroll pour charger plus de messages\n    const element = event.target;\n    if (\n      element.scrollTop === 0 &&\n      this.hasMoreMessages &&\n      !this.isLoadingMore\n    ) {\n      this.loadMoreMessages();\n    }\n  }\n\n  trackByMessageId(index: number, message: any): any {\n    return message.id || index;\n  }\n\n  // === APPELS VIDÉO/AUDIO ===\n  startCall(type: 'VIDEO' | 'AUDIO'): void {\n    this.callType = type;\n    this.isInCall = true;\n    this.callDuration = 0;\n\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n\n    this.toastService.showSuccess(\n      `Appel ${type === 'VIDEO' ? 'vidéo' : 'audio'} démarré`\n    );\n  }\n\n  endCall(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.toastService.showSuccess('Appel terminé');\n  }\n\n  startVideoCall(): void {\n    this.startCall('VIDEO');\n  }\n\n  startVoiceCall(): void {\n    this.startCall('AUDIO');\n  }\n\n  // === RECHERCHE ===\n  toggleSearch(): void {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n\n  searchMessages(): void {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n\n    this.searchResults = this.messages.filter(\n      (message) =>\n        message.content\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase()) ||\n        message.sender?.username\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase())\n    );\n  }\n\n  // === PAGINATION ===\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\n      return;\n\n    this.isLoadingMore = true;\n    this.currentPage++;\n\n    this.MessageService.getConversation(\n      this.conversation.id,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        const newMessages = conversation.messages || [];\n        if (newMessages.length > 0) {\n          // Ajouter les nouveaux messages au début (messages plus anciens)\n          this.messages = [...newMessages, ...this.messages];\n          this.hasMoreMessages =\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.isLoadingMore = false;\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\n      },\n    });\n  }\n\n  // === GÉOLOCALISATION ===\n  async shareLocation(): Promise<void> {\n    try {\n      const position = await this.getCurrentPosition();\n      const { latitude, longitude } = position.coords;\n\n      const locationMessage = {\n        type: 'location',\n        latitude,\n        longitude,\n        address: await this.getAddressFromCoordinates(latitude, longitude),\n        mapUrl: `https://maps.google.com/maps?q=${latitude},${longitude}&z=15`,\n        timestamp: new Date(),\n      };\n\n      await this.sendLocationMessage(locationMessage);\n      this.toastService.showSuccess('Position partagée avec succès');\n    } catch (error) {\n      console.error('Erreur lors du partage de localisation:', error);\n      this.toastService.showError(\"Impossible d'obtenir votre position\");\n    }\n  }\n\n  private getCurrentPosition(): Promise<GeolocationPosition> {\n    return new Promise((resolve, reject) => {\n      navigator.geolocation.getCurrentPosition(resolve, reject, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 60000,\n      });\n    });\n  }\n\n  private async getAddressFromCoordinates(\n    lat: number,\n    lng: number\n  ): Promise<string> {\n    try {\n      const response = await fetch(\n        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        return data.display_name || `${lat}, ${lng}`;\n      }\n    } catch (error) {\n      console.error('Erreur lors du géocodage inverse:', error);\n    }\n\n    return `${lat}, ${lng}`;\n  }\n\n  private async sendLocationMessage(locationData: any): Promise<void> {\n    const otherParticipant = this.conversation?.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        `📍 Position partagée: ${locationData.address}`,\n        undefined,\n        'TEXT' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\n            \"Erreur lors de l'envoi du message de localisation:\",\n            error\n          );\n          reject(error);\n        },\n      });\n    });\n  }\n\n  // === PARTAGE DE CONTACTS ===\n  async shareContact(): Promise<void> {\n    try {\n      const contact = await this.selectContact();\n\n      if (contact) {\n        await this.sendContactMessage(contact);\n        this.toastService.showSuccess('Contact partagé avec succès');\n      }\n    } catch (error) {\n      console.error('Erreur lors du partage de contact:', error);\n      this.toastService.showError('Erreur lors du partage du contact');\n    }\n  }\n\n  private async selectContact(): Promise<any> {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        resolve({\n          name: 'John Doe',\n          phone: '+33 6 12 34 56 78',\n          email: '<EMAIL>',\n          avatar: 'assets/images/default-avatar.png',\n        });\n      }, 1000);\n    });\n  }\n\n  private async sendContactMessage(contactData: any): Promise<void> {\n    const otherParticipant = this.conversation?.participants?.find(\n      (p: any) => (p.id || p._id) !== this.currentUserId\n    );\n\n    const receiverId = otherParticipant?.id || otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        `👤 Contact partagé: ${contactData.name}\\n📞 ${contactData.phone}\\n📧 ${contactData.email}`,\n        undefined,\n        'TEXT' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message de contact:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  // === UTILITAIRES ===\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  formatDuration(seconds: number): string {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs\n        .toString()\n        .padStart(2, '0')}`;\n    }\n\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n\n  triggerFileInput(type?: string): void {\n    // Optionnel : configurer le type de fichier accepté selon le type\n    if (type === 'image') {\n      this.fileInput.nativeElement.accept = 'image/*';\n    } else if (type === 'video') {\n      this.fileInput.nativeElement.accept = 'video/*';\n    } else if (type === 'document') {\n      this.fileInput.nativeElement.accept =\n        '.pdf,.doc,.docx,.txt,.xlsx,.ppt,.pptx';\n    } else {\n      this.fileInput.nativeElement.accept = '*/*';\n    }\n\n    this.fileInput.nativeElement.click();\n  }\n\n  // === MÉTHODES POUR LE TEMPLATE ===\n  goBackToConversations(): void {\n    // Navigation vers la liste des conversations\n    window.history.back();\n  }\n\n  formatLastActive(lastActive: any): string {\n    if (!lastActive) return 'Hors ligne';\n    const now = new Date();\n    const lastActiveDate = new Date(lastActive);\n    const diffInMinutes = Math.floor(\n      (now.getTime() - lastActiveDate.getTime()) / (1000 * 60)\n    );\n\n    if (diffInMinutes < 1) return 'En ligne';\n    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;\n    if (diffInMinutes < 1440)\n      return `Il y a ${Math.floor(diffInMinutes / 60)} h`;\n    return `Il y a ${Math.floor(diffInMinutes / 1440)} j`;\n  }\n\n  toggleMainMenu(): void {\n    // Toggle du menu principal\n    this.showAttachmentMenu = false;\n    this.showEmojiPicker = false;\n  }\n\n  onScroll(event: any): void {\n    const element = event.target;\n    if (\n      element.scrollTop === 0 &&\n      this.hasMoreMessages &&\n      !this.isLoadingMore\n    ) {\n      this.loadMoreMessages();\n    }\n  }\n\n  trackByMessageId(index: number, message: any): any {\n    return message.id || index;\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  formatDateSeparator(timestamp: any): string {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) return \"Aujourd'hui\";\n    if (date.toDateString() === yesterday.toDateString()) return 'Hier';\n\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  }\n\n  onMessageClick(message: any, event: any): void {\n    // Gestion du clic sur un message\n    console.log('Message clicked:', message);\n  }\n\n  onMessageContextMenu(message: any, event: any): void {\n    event.preventDefault();\n    // Gestion du menu contextuel\n    console.log('Message context menu:', message);\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    if (index === this.messages.length - 1) return true;\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n\n    return !nextMessage || currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n\n  openUserProfile(userId: string): void {\n    // Navigation vers le profil utilisateur\n    console.log('Open user profile:', userId);\n  }\n\n  isGroupConversation(): boolean {\n    return this.conversation?.participants?.length > 2;\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n\n  getUserColor(userId: string): string {\n    // Génère une couleur basée sur l'ID utilisateur\n    const colors = [\n      '#FF6B6B',\n      '#4ECDC4',\n      '#45B7D1',\n      '#96CEB4',\n      '#FFEAA7',\n      '#DDA0DD',\n    ];\n    const hash = userId.split('').reduce((a, b) => {\n      a = (a << 5) - a + b.charCodeAt(0);\n      return a & a;\n    }, 0);\n    return colors[Math.abs(hash) % colors.length];\n  }\n\n  getMessageType(message: any): string {\n    if (message.file) {\n      if (message.file.type?.startsWith('image/')) return 'image';\n      if (message.file.type?.startsWith('video/')) return 'video';\n      if (message.file.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    return 'text';\n  }\n\n  formatMessageContent(content: string): string {\n    if (!content) return '';\n    // Formatage basique du contenu (liens, emojis, etc.)\n    return content.replace(/\\n/g, '<br>');\n  }\n\n  hasImage(message: any): boolean {\n    return message.file && message.file.type?.startsWith('image/');\n  }\n\n  openImageViewer(message: any): void {\n    // Ouvre la visionneuse d'images\n    console.log('Open image viewer:', message);\n  }\n\n  getImageUrl(message: any): string {\n    return message.file?.url || message.file?.path || '';\n  }\n\n  hasFile(message: any): boolean {\n    return message.file && !message.file.type?.startsWith('image/');\n  }\n\n  downloadFile(message: any): void {\n    if (message.file?.url) {\n      const link = document.createElement('a');\n      link.href = message.file.url;\n      link.download = message.file.name || 'file';\n      link.click();\n    }\n  }\n\n  getFileIcon(message: any): string {\n    if (!message.file?.type) return 'fas fa-file';\n\n    if (message.file.type.startsWith('video/')) return 'fas fa-video';\n    if (message.file.type.startsWith('audio/')) return 'fas fa-music';\n    if (message.file.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (message.file.type.includes('word')) return 'fas fa-file-word';\n    if (message.file.type.includes('excel')) return 'fas fa-file-excel';\n\n    return 'fas fa-file';\n  }\n\n  getFileName(message: any): string {\n    return message.file?.name || 'Fichier';\n  }\n\n  getFileSize(message: any): string {\n    if (!message.file?.size) return '';\n    return this.formatFileSize(message.file.size);\n  }\n\n  formatMessageTime(timestamp: any): string {\n    if (!timestamp) return '';\n    return new Date(timestamp).toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  toggleReaction(messageId: string, emoji: string): void {\n    // Gestion des réactions aux messages\n    console.log('Toggle reaction:', messageId, emoji);\n  }\n\n  hasUserReacted(reaction: any, userId: string): boolean {\n    return reaction.users?.includes(userId) || false;\n  }\n\n  onInputChange(event: any): void {\n    // Gestion du changement de texte dans l'input\n    const value = event.target.value;\n    if (value.trim() && !this.isTyping) {\n      this.isTyping = true;\n      // Envoyer signal de frappe\n    } else if (!value.trim() && this.isTyping) {\n      this.isTyping = false;\n      // Arrêter signal de frappe\n    }\n  }\n\n  onInputKeyDown(event: any): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  onInputFocus(): void {\n    // Marquer les messages comme lus\n    this.markMessagesAsRead();\n  }\n\n  onInputBlur(): void {\n    // Arrêter le signal de frappe\n    this.isTyping = false;\n  }\n\n  private markMessagesAsRead(): void {\n    // Marquer tous les messages non lus comme lus\n    console.log('Mark messages as read');\n  }\n\n  openCamera(): void {\n    // Ouvrir la caméra pour prendre une photo\n    console.log('Open camera');\n  }\n\n  // === GESTION DES ERREURS ===\n  handleError(error: any, context: string): void {\n    console.error(`Erreur dans ${context}:`, error);\n    let message = \"Une erreur inattendue s'est produite\";\n\n    if (error?.message) {\n      message = error.message;\n    } else if (typeof error === 'string') {\n      message = error;\n    }\n\n    this.toastService.showError(message);\n  }\n\n  // === NETTOYAGE ===\n  ngOnDestroy(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    this.subscriptions.unsubscribe();\n  }\n}\n", "<!-- Chat WhatsApp Moderne avec Tailwind CSS -->\n<div\n  class=\"flex flex-col h-screen bg-gradient-to-br from-gray-50 to-green-50 dark:from-gray-900 dark:to-gray-800\"\n>\n  <!-- En-tête -->\n  <header\n    class=\"flex items-center px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm\"\n  >\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      class=\"p-2 mr-3 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n    >\n      <i class=\"fas fa-arrow-left text-gray-600 dark:text-gray-300\"></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div class=\"flex items-center flex-1 min-w-0\">\n      <div class=\"relative mr-3\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-10 h-10 rounded-full object-cover border-2 border-green-500 cursor-pointer hover:scale-105 transition-transform\"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n        />\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\"\n        ></div>\n      </div>\n\n      <div class=\"flex-1 min-w-0\">\n        <h3 class=\"font-semibold text-gray-900 dark:text-white truncate\">\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <!-- Debug temporaire -->\n        <div class=\"text-xs text-red-500\" *ngIf=\"!otherParticipant\">\n          DEBUG: otherParticipant is null/undefined\n        </div>\n        <div class=\"text-xs text-blue-500\" *ngIf=\"otherParticipant\">\n          DEBUG: {{ otherParticipant.username }} (ID: {{ otherParticipant.id }})\n        </div>\n        <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n          <div\n            *ngIf=\"isUserTyping\"\n            class=\"flex items-center gap-1 text-green-600\"\n          >\n            <span>En train d'écrire</span>\n            <div class=\"flex gap-1\">\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.1s\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.2s\"\n              ></div>\n            </div>\n          </div>\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div class=\"flex items-center gap-2\">\n      <button\n        (click)=\"startVideoCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vidéo\"\n      >\n        <i class=\"fas fa-video\"></i>\n      </button>\n      <button\n        (click)=\"startVoiceCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vocal\"\n      >\n        <i class=\"fas fa-phone\"></i>\n      </button>\n      <button\n        (click)=\"toggleSearch()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        [class.bg-green-100]=\"searchMode\"\n        [class.text-green-600]=\"searchMode\"\n        title=\"Rechercher\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n      <button\n        (click)=\"toggleMainMenu()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Menu\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n  </header>\n\n  <!-- Zone de messages -->\n  <main\n    class=\"flex-1 overflow-y-auto p-4 space-y-4\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n  >\n    <!-- Chargement -->\n    <div\n      *ngIf=\"isLoading\"\n      class=\"flex flex-col items-center justify-center py-8\"\n    >\n      <div\n        class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-4\"\n      ></div>\n      <span class=\"text-gray-500 dark:text-gray-400\"\n        >Chargement des messages...</span\n      >\n    </div>\n\n    <!-- État vide -->\n    <div\n      *ngIf=\"!isLoading && messages.length === 0\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"text-6xl text-gray-300 dark:text-gray-600 mb-4\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3 class=\"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n        Aucun message\n      </h3>\n      <p class=\"text-gray-500 dark:text-gray-400 text-center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages -->\n    <div *ngIf=\"!isLoading && messages.length > 0\" class=\"space-y-2\">\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Séparateur de date -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          class=\"flex justify-center my-4\"\n        >\n          <div\n            class=\"bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-sm\"\n          >\n            <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message -->\n        <div\n          class=\"flex\"\n          [class.justify-end]=\"message.sender?.id === currentUserId\"\n          [class.justify-start]=\"message.sender?.id !== currentUserId\"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar pour les autres -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            class=\"mr-2 flex-shrink-0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              class=\"w-8 h-8 rounded-full object-cover cursor-pointer hover:scale-105 transition-transform\"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n            />\n          </div>\n\n          <!-- Bulle de message -->\n          <div\n            class=\"max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm relative group\"\n            [class.bg-green-500]=\"message.sender?.id === currentUserId\"\n            [class.text-white]=\"message.sender?.id === currentUserId\"\n            [class.bg-white]=\"message.sender?.id !== currentUserId\"\n            [class.text-gray-900]=\"message.sender?.id !== currentUserId\"\n            [class.dark:bg-gray-700]=\"message.sender?.id !== currentUserId\"\n            [class.dark:text-white]=\"message.sender?.id !== currentUserId\"\n          >\n            <!-- Nom expéditeur (groupes) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              class=\"text-xs font-semibold mb-1 opacity-75\"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Contenu texte -->\n            <div *ngIf=\"getMessageType(message) === 'text'\" class=\"break-words\">\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image -->\n            <div *ngIf=\"hasImage(message)\" class=\"space-y-2\">\n              <div\n                class=\"relative cursor-pointer rounded-lg overflow-hidden\"\n                (click)=\"openImageViewer(message)\"\n              >\n                <img\n                  [src]=\"getImageUrl(message)\"\n                  [alt]=\"message.content || 'Image'\"\n                  class=\"max-w-full h-auto rounded-lg hover:opacity-90 transition-opacity\"\n                />\n                <div\n                  class=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all flex items-center justify-center\"\n                >\n                  <i\n                    class=\"fas fa-expand text-white opacity-0 group-hover:opacity-100 transition-opacity\"\n                  ></i>\n                </div>\n              </div>\n              <div\n                *ngIf=\"message.content\"\n                class=\"text-sm\"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Fichier -->\n            <div\n              *ngIf=\"hasFile(message)\"\n              class=\"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\"\n              (click)=\"downloadFile(message)\"\n            >\n              <div class=\"text-2xl text-gray-500 dark:text-gray-400\">\n                <i [class]=\"getFileIcon(message)\"></i>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <div class=\"font-medium text-sm truncate\">\n                  {{ getFileName(message) }}\n                </div>\n                <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ getFileSize(message) }}\n                </div>\n              </div>\n              <button\n                class=\"p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-400 transition-colors\"\n              >\n                <i class=\"fas fa-download text-sm\"></i>\n              </button>\n            </div>\n\n            <!-- Métadonnées -->\n            <div\n              class=\"flex items-center justify-end gap-1 mt-1 text-xs opacity-75\"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                class=\"flex items-center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double text-blue-400\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n\n            <!-- Réactions -->\n            <div\n              *ngIf=\"message.reactions && message.reactions.length > 0\"\n              class=\"flex gap-1 mt-2\"\n            >\n              <button\n                *ngFor=\"let reaction of message.reactions\"\n                (click)=\"toggleReaction(message.id!, reaction.emoji)\"\n                class=\"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\"\n                [class.bg-green-100]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n                [class.text-green-600]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n              >\n                <span>{{ reaction.emoji }}</span>\n                <span>1</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Indicateur de frappe -->\n      <div *ngIf=\"isTyping\" class=\"flex items-start gap-2\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-8 h-8 rounded-full object-cover\"\n        />\n        <div class=\"bg-white dark:bg-gray-700 px-4 py-2 rounded-2xl shadow-sm\">\n          <div class=\"flex gap-1\">\n            <div class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.1s\"\n            ></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.2s\"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- Zone d'input -->\n  <footer\n    class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"flex items-end gap-3\"\n    >\n      <!-- Actions gauche -->\n      <div class=\"flex gap-2\">\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showEmojiPicker\"\n          [class.text-green-600]=\"showEmojiPicker\"\n          title=\"Émojis\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showAttachmentMenu\"\n          [class.text-green-600]=\"showAttachmentMenu\"\n          title=\"Joindre un fichier\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n      </div>\n\n      <!-- Champ de saisie -->\n      <div class=\"flex-1\">\n        <textarea\n          formControlName=\"content\"\n          #messageTextarea\n          placeholder=\"Tapez votre message...\"\n          class=\"w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400\"\n          [disabled]=\"!otherParticipant || isRecordingVoice || isSendingMessage\"\n          (input)=\"onInputChange($event)\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (focus)=\"onInputFocus()\"\n          (blur)=\"onInputBlur()\"\n          rows=\"1\"\n          maxlength=\"4096\"\n          autocomplete=\"off\"\n          spellcheck=\"true\"\n        >\n        </textarea>\n      </div>\n\n      <!-- Actions droite -->\n      <div class=\"flex gap-2\">\n        <!-- Enregistrement vocal -->\n        <button\n          *ngIf=\"!messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (mousedown)=\"startVoiceRecording()\"\n          (mouseup)=\"stopVoiceRecording()\"\n          (mouseleave)=\"cancelVoiceRecording()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\"\n          [class.bg-red-500]=\"isRecordingVoice\"\n          [class.hover:bg-red-600]=\"isRecordingVoice\"\n          [class.animate-pulse]=\"isRecordingVoice\"\n          title=\"Maintenir pour enregistrer\"\n        >\n          <i class=\"fas fa-microphone\"></i>\n        </button>\n\n        <!-- Bouton d'envoi -->\n        <button\n          *ngIf=\"messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (click)=\"sendMessage()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\"\n          [disabled]=\"isSendingMessage\"\n          title=\"Envoyer\"\n        >\n          <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n          <i class=\"fas fa-spinner fa-spin\" *ngIf=\"isSendingMessage\"></i>\n        </button>\n      </div>\n    </form>\n  </footer>\n\n  <!-- Sélecteur d'émojis -->\n  <div\n    *ngIf=\"showEmojiPicker\"\n    class=\"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"flex gap-2 mb-4 overflow-x-auto\">\n        <button\n          *ngFor=\"let category of emojiCategories\"\n          (click)=\"selectEmojiCategory(category)\"\n          class=\"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\"\n          [class.bg-green-100]=\"selectedEmojiCategory === category\"\n          [class.text-green-600]=\"selectedEmojiCategory === category\"\n          [class.hover:bg-gray-100]=\"selectedEmojiCategory !== category\"\n          [class.dark:hover:bg-gray-700]=\"selectedEmojiCategory !== category\"\n        >\n          {{ category.icon }}\n        </button>\n      </div>\n      <div class=\"grid grid-cols-8 gap-2 max-h-48 overflow-y-auto\">\n        <button\n          *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n          (click)=\"insertEmoji(emoji)\"\n          class=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\"\n          [title]=\"emoji.name\"\n        >\n          {{ emoji.emoji }}\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Menu des pièces jointes -->\n  <div\n    *ngIf=\"showAttachmentMenu\"\n    class=\"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"grid grid-cols-2 gap-3\">\n        <button\n          (click)=\"triggerFileInput('image')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-image text-blue-600 dark:text-blue-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Photos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('video')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-video text-purple-600 dark:text-purple-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Vidéos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('document')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-file text-orange-600 dark:text-orange-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Documents</span\n          >\n        </button>\n        <button\n          (click)=\"openCamera()\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-camera text-green-600 dark:text-green-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Caméra</span\n          >\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input caché pour fichiers -->\n  <input\n    #fileInput\n    type=\"file\"\n    class=\"hidden\"\n    (change)=\"onFileSelected($event)\"\n    [accept]=\"getFileAcceptTypes()\"\n    multiple\n  />\n\n  <!-- Overlay pour fermer les menus -->\n  <div\n    *ngIf=\"showEmojiPicker || showAttachmentMenu\"\n    class=\"fixed inset-0 bg-black bg-opacity-25 z-40\"\n    (click)=\"closeAllMenus()\"\n  ></div>\n</div>\n"], "mappings": ";AAQA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;;;;;;;;;;;ICe3BC,EAAA,CAAAC,SAAA,cAGO;;;;;IAQPD,EAAA,CAAAE,cAAA,cAA4D;IAC1DF,EAAA,CAAAG,MAAA,kDACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,cAA4D;IAC1DF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,aAAAC,MAAA,CAAAC,gBAAA,CAAAC,QAAA,YAAAF,MAAA,CAAAC,gBAAA,CAAAE,EAAA,OACF;;;;;IAEEV,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAwB;IACtBF,EAAA,CAAAC,SAAA,cAEO;IASTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAERJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAW,kBAAA,OAAAC,MAAA,CAAAJ,gBAAA,kBAAAI,MAAA,CAAAJ,gBAAA,CAAAK,QAAA,iBAAAD,MAAA,CAAAE,gBAAA,CAAAF,MAAA,CAAAJ,gBAAA,kBAAAI,MAAA,CAAAJ,gBAAA,CAAAO,UAAA,OAKF;;;;;IA+CNf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,eACG;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAC5B;;;;;IAIHJ,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAAwE;IACtEF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAAwD;IACtDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,wCAAAK,MAAA,CAAAR,gBAAA,kBAAAQ,MAAA,CAAAR,gBAAA,CAAAC,QAAA,MACF;;;;;IAaET,EAAA,CAAAE,cAAA,cAGC;IAKKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAM,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAcFpB,EAAA,CAAAE,cAAA,cAGC;IAOGF,EAAA,CAAAqB,UAAA,mBAAAC,+EAAA;MAAAtB,EAAA,CAAAuB,aAAA,CAAAC,IAAA;MAAA,MAAAL,WAAA,GAAAnB,EAAA,CAAAyB,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAD,OAAA,CAAAE,eAAA,CAAAV,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,CAAoC;IAAA,EAAC;IANhDV,EAAA,CAAAI,YAAA,EAOE;;;;IANAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAA+B,UAAA,SAAAZ,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAE,KAAA,yCAAAhC,EAAA,CAAAiC,aAAA,CAEC,QAAAd,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAArB,QAAA;;;;;IAkBHT,EAAA,CAAAE,cAAA,cAQC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAkC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAjB,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,EAAiD;IAEjDV,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAQ,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAArB,QAAA,MACF;;;;;IAGAT,EAAA,CAAAE,cAAA,cAAoE;IAClEF,EAAA,CAAAC,SAAA,cAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA+B,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAAnB,WAAA,CAAAoB,OAAA,GAAAvC,EAAA,CAAAwC,cAAA,CAAmD;;;;;IAsBxDxC,EAAA,CAAAC,SAAA,cAIO;;;;;IADLD,EAAA,CAAA+B,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAAnB,WAAA,CAAAoB,OAAA,GAAAvC,EAAA,CAAAwC,cAAA,CAAmD;;;;;;IArBvDxC,EAAA,CAAAE,cAAA,cAAiD;IAG7CF,EAAA,CAAAqB,UAAA,mBAAAqB,+EAAA;MAAA1C,EAAA,CAAAuB,aAAA,CAAAoB,IAAA;MAAA,MAAAxB,WAAA,GAAAnB,EAAA,CAAAyB,aAAA,GAAAC,SAAA;MAAA,MAAAkB,OAAA,GAAA5C,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAgB,OAAA,CAAAC,eAAA,CAAA1B,WAAA,CAAwB;IAAA,EAAC;IAElCnB,EAAA,CAAAC,SAAA,cAIE;IACFD,EAAA,CAAAE,cAAA,cAEC;IACCF,EAAA,CAAAC,SAAA,YAEK;IACPD,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAA8C,UAAA,IAAAC,+DAAA,kBAIO;IACT/C,EAAA,CAAAI,YAAA,EAAM;;;;;IAjBAJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA+B,UAAA,QAAAiB,OAAA,CAAAC,WAAA,CAAA9B,WAAA,GAAAnB,EAAA,CAAAiC,aAAA,CAA4B,QAAAd,WAAA,CAAAoB,OAAA;IAa7BvC,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA+B,UAAA,SAAAZ,WAAA,CAAAoB,OAAA,CAAqB;;;;;;IAO1BvC,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAqB,UAAA,mBAAA6B,+EAAA;MAAAlD,EAAA,CAAAuB,aAAA,CAAA4B,IAAA;MAAA,MAAAhC,WAAA,GAAAnB,EAAA,CAAAyB,aAAA,GAAAC,SAAA;MAAA,MAAA0B,OAAA,GAAApD,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAwB,OAAA,CAAAC,YAAA,CAAAlC,WAAA,CAAqB;IAAA,EAAC;IAE/BnB,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAAC,SAAA,QAAsC;IACxCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAA4B;IAExBF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAAsD;IACpDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAE,cAAA,iBAEC;IACCF,EAAA,CAAAC,SAAA,YAAuC;IACzCD,EAAA,CAAAI,YAAA,EAAS;;;;;IAdJJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAsD,UAAA,CAAAC,OAAA,CAAAC,WAAA,CAAArC,WAAA,EAA8B;IAI/BnB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAA4C,OAAA,CAAAE,WAAA,CAAAtC,WAAA,OACF;IAEEnB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAA4C,OAAA,CAAAG,WAAA,CAAAvC,WAAA,OACF;;;;;IAkBAnB,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IACLD,EAAA,CAAAC,SAAA,aAIK;;;;;IAvBPD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAA8C,UAAA,IAAAa,8DAAA,gBAIK;IACL3D,EAAA,CAAA8C,UAAA,IAAAc,8DAAA,gBAIK;IACL5D,EAAA,CAAA8C,UAAA,IAAAe,8DAAA,gBAIK;IACL7D,EAAA,CAAA8C,UAAA,IAAAgB,8DAAA,gBAIK;IACP9D,EAAA,CAAAI,YAAA,EAAM;;;;IAlBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA+B,UAAA,SAAAZ,WAAA,CAAA4C,MAAA,eAAkC;IAKlC/D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA+B,UAAA,SAAAZ,WAAA,CAAA4C,MAAA,YAA+B;IAK/B/D,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAA+B,UAAA,SAAAZ,WAAA,CAAA4C,MAAA,iBAAoC;IAKpC/D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA+B,UAAA,SAAAZ,WAAA,CAAA4C,MAAA,YAA+B;;;;;;IAWpC/D,EAAA,CAAAE,cAAA,kBAUC;IARCF,EAAA,CAAAqB,UAAA,mBAAA2C,4FAAA;MAAA,MAAAC,WAAA,GAAAjE,EAAA,CAAAuB,aAAA,CAAA2C,IAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAAvC,SAAA;MAAA,MAAAP,WAAA,GAAAnB,EAAA,CAAAyB,aAAA,IAAAC,SAAA;MAAA,MAAA0C,OAAA,GAAApE,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAwC,OAAA,CAAAC,cAAA,CAAAlD,WAAA,CAAAT,EAAA,EAAAyD,YAAA,CAAAG,KAAA,CAA2C;IAAA,EAAC;IASrDtE,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,QAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IARdJ,EAAA,CAAAuE,WAAA,iBAAAC,OAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAK,OAAA,CAAAE,aAAA,QAEC,mBAAAF,OAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAK,OAAA,CAAAE,aAAA;IAKK1E,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA2E,iBAAA,CAAAR,YAAA,CAAAG,KAAA,CAAoB;;;;;IAf9BtE,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAA8C,UAAA,IAAA8B,mEAAA,sBAaS;IACX5E,EAAA,CAAAI,YAAA,EAAM;;;;IAbmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA+B,UAAA,YAAAZ,WAAA,CAAA0D,SAAA,CAAoB;;;;;;IAjKnD7E,EAAA,CAAA8E,uBAAA,GAMC;IAEC9E,EAAA,CAAA8C,UAAA,IAAAiC,yDAAA,kBAWM;IAGN/E,EAAA,CAAAE,cAAA,cAOC;IAFCF,EAAA,CAAAqB,UAAA,mBAAA2D,yEAAAC,MAAA;MAAA,MAAAhB,WAAA,GAAAjE,EAAA,CAAAuB,aAAA,CAAA2D,IAAA;MAAA,MAAA/D,WAAA,GAAA8C,WAAA,CAAAvC,SAAA;MAAA,MAAAyD,OAAA,GAAAnF,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAuD,OAAA,CAAAC,cAAA,CAAAjE,WAAA,EAAA8D,MAAA,CAA+B;IAAA,EAAC,yBAAAI,+EAAAJ,MAAA;MAAA,MAAAhB,WAAA,GAAAjE,EAAA,CAAAuB,aAAA,CAAA2D,IAAA;MAAA,MAAA/D,WAAA,GAAA8C,WAAA,CAAAvC,SAAA;MAAA,MAAA4D,OAAA,GAAAtF,EAAA,CAAAyB,aAAA;MAAA,OAC1BzB,EAAA,CAAA4B,WAAA,CAAA0D,OAAA,CAAAC,oBAAA,CAAApE,WAAA,EAAA8D,MAAA,CAAqC;IAAA,EADX;IAIzCjF,EAAA,CAAA8C,UAAA,IAAA0C,yDAAA,kBAYM;IAGNxF,EAAA,CAAAE,cAAA,cAQC;IAECF,EAAA,CAAA8C,UAAA,IAAA2C,yDAAA,kBAUM;IAGNzF,EAAA,CAAA8C,UAAA,IAAA4C,yDAAA,kBAEM;IAGN1F,EAAA,CAAA8C,UAAA,IAAA6C,yDAAA,kBAuBM;IAGN3F,EAAA,CAAA8C,UAAA,IAAA8C,yDAAA,mBAqBM;IAGN5F,EAAA,CAAAE,cAAA,cAEC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAA8C,UAAA,KAAA+C,0DAAA,kBAwBM;IACR7F,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAA8C,UAAA,KAAAgD,0DAAA,kBAkBM;IACR9F,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAA+F,qBAAA,EAAe;;;;;;IAxKV/F,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA+B,UAAA,SAAAiE,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAejClG,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAuE,WAAA,iBAAApD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA,CAA0D,mBAAAvD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA;IAE1D1E,EAAA,CAAA+B,UAAA,oBAAAZ,WAAA,CAAAT,EAAA,CAA8B;IAM3BV,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAA+B,UAAA,UAAAZ,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA,IAAAsB,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAgBlElG,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAuE,WAAA,kBAAApD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA,CAA2D,gBAAAvD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA,eAAAvD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA,oBAAAvD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA,uBAAAvD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA,sBAAAvD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA;IASxD1E,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAA+B,UAAA,SAAAiE,OAAA,CAAAI,mBAAA,OAAAjF,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA,IAAAsB,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAOkBlG,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAA+B,UAAA,SAAAiE,OAAA,CAAAM,cAAA,CAAAnF,WAAA,aAAwC;IAKxCnB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA+B,UAAA,SAAAiE,OAAA,CAAAO,QAAA,CAAApF,WAAA,EAAuB;IA2B1BnB,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA+B,UAAA,SAAAiE,OAAA,CAAAQ,OAAA,CAAArF,WAAA,EAAsB;IA0BjBnB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA2E,iBAAA,CAAAqB,OAAA,CAAAS,iBAAA,CAAAtF,WAAA,CAAAC,SAAA,EAA0C;IAE7CpB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA+B,UAAA,UAAAZ,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAApB,EAAA,MAAAsF,OAAA,CAAAtB,aAAA,CAA0C;IA4B5C1E,EAAA,CAAAK,SAAA,GAAuD;IAAvDL,EAAA,CAAA+B,UAAA,SAAAZ,WAAA,CAAA0D,SAAA,IAAA1D,WAAA,CAAA0D,SAAA,CAAA6B,MAAA,KAAuD;;;;;IAuBhE1G,EAAA,CAAAE,cAAA,eAAqD;IACnDF,EAAA,CAAAC,SAAA,eAIE;IACFD,EAAA,CAAAE,cAAA,eAAuE;IAEnEF,EAAA,CAAAC,SAAA,eAAmE;IASrED,EAAA,CAAAI,YAAA,EAAM;;;;IAfNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAA+B,UAAA,SAAA4E,OAAA,CAAAnG,gBAAA,kBAAAmG,OAAA,CAAAnG,gBAAA,CAAAwB,KAAA,yCAAAhC,EAAA,CAAAiC,aAAA,CAAqE,QAAA0E,OAAA,CAAAnG,gBAAA,kBAAAmG,OAAA,CAAAnG,gBAAA,CAAAC,QAAA;;;;;IAvL3ET,EAAA,CAAAE,cAAA,cAAiE;IAC/DF,EAAA,CAAA8C,UAAA,IAAA8D,mDAAA,6BAiLe;IAGf5G,EAAA,CAAA8C,UAAA,IAAA+D,0CAAA,kBAmBM;IACR7G,EAAA,CAAAI,YAAA,EAAM;;;;IAtMuBJ,EAAA,CAAAK,SAAA,GACZ;IADYL,EAAA,CAAA+B,UAAA,YAAA+E,MAAA,CAAAC,QAAA,CACZ,iBAAAD,MAAA,CAAAE,gBAAA;IAiLThH,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA+B,UAAA,SAAA+E,MAAA,CAAAG,QAAA,CAAc;;;;;;IA+ElBjH,EAAA,CAAAE,cAAA,kBAWC;IARCF,EAAA,CAAAqB,UAAA,uBAAA6F,oEAAA;MAAAlH,EAAA,CAAAuB,aAAA,CAAA4F,IAAA;MAAA,MAAAC,OAAA,GAAApH,EAAA,CAAAyB,aAAA;MAAA,OAAazB,EAAA,CAAA4B,WAAA,CAAAwF,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC,qBAAAC,kEAAA;MAAAtH,EAAA,CAAAuB,aAAA,CAAA4F,IAAA;MAAA,MAAAI,OAAA,GAAAvH,EAAA,CAAAyB,aAAA;MAAA,OACxBzB,EAAA,CAAA4B,WAAA,CAAA2F,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EADI,wBAAAC,qEAAA;MAAAzH,EAAA,CAAAuB,aAAA,CAAA4F,IAAA;MAAA,MAAAO,OAAA,GAAA1H,EAAA,CAAAyB,aAAA;MAAA,OAErBzB,EAAA,CAAA4B,WAAA,CAAA8F,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAFD;IASnC3H,EAAA,CAAAC,SAAA,aAAiC;IACnCD,EAAA,CAAAI,YAAA,EAAS;;;;IANPJ,EAAA,CAAAuE,WAAA,eAAAqD,OAAA,CAAAC,gBAAA,CAAqC,qBAAAD,OAAA,CAAAC,gBAAA,mBAAAD,OAAA,CAAAC,gBAAA;;;;;IAiBrC7H,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,aAA+D;;;;;;IATjED,EAAA,CAAAE,cAAA,kBAOC;IAJCF,EAAA,CAAAqB,UAAA,mBAAAyG,gEAAA;MAAA9H,EAAA,CAAAuB,aAAA,CAAAwG,IAAA;MAAA,MAAAC,OAAA,GAAAhI,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAoG,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvBjI,EAAA,CAAA8C,UAAA,IAAAoF,2CAAA,iBAA4D;IAC5DlI,EAAA,CAAA8C,UAAA,IAAAqF,2CAAA,iBAA+D;IACjEnI,EAAA,CAAAI,YAAA,EAAS;;;;IALPJ,EAAA,CAAA+B,UAAA,aAAAqG,OAAA,CAAAC,gBAAA,CAA6B;IAGErI,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA+B,UAAA,UAAAqG,OAAA,CAAAC,gBAAA,CAAuB;IACnBrI,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA+B,UAAA,SAAAqG,OAAA,CAAAC,gBAAA,CAAsB;;;;;;IAa3DrI,EAAA,CAAAE,cAAA,kBAQC;IANCF,EAAA,CAAAqB,UAAA,mBAAAiH,sEAAA;MAAA,MAAArE,WAAA,GAAAjE,EAAA,CAAAuB,aAAA,CAAAgH,IAAA;MAAA,MAAAC,YAAA,GAAAvE,WAAA,CAAAvC,SAAA;MAAA,MAAA+G,OAAA,GAAAzI,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAA6G,OAAA,CAAAC,mBAAA,CAAAF,YAAA,CAA6B;IAAA,EAAC;IAOvCxI,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IANPJ,EAAA,CAAAuE,WAAA,iBAAAoE,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,CAAyD,mBAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,uBAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,4BAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA;IAKzDxI,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAA6H,YAAA,CAAAK,IAAA,MACF;;;;;;IAGA7I,EAAA,CAAAE,cAAA,kBAKC;IAHCF,EAAA,CAAAqB,UAAA,mBAAAyH,sEAAA;MAAA,MAAA7E,WAAA,GAAAjE,EAAA,CAAAuB,aAAA,CAAAwH,IAAA;MAAA,MAAAC,SAAA,GAAA/E,WAAA,CAAAvC,SAAA;MAAA,MAAAuH,OAAA,GAAAjJ,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAqH,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAI5BhJ,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAA+B,UAAA,UAAAiH,SAAA,CAAAG,IAAA,CAAoB;IAEpBnJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAqI,SAAA,CAAA1E,KAAA,MACF;;;;;IA1BNtE,EAAA,CAAAE,cAAA,eAGC;IAGKF,EAAA,CAAA8C,UAAA,IAAAsG,6CAAA,sBAUS;IACXpJ,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA6D;IAC3DF,EAAA,CAAA8C,UAAA,IAAAuG,6CAAA,sBAOS;IACXrJ,EAAA,CAAAI,YAAA,EAAM;;;;IApBmBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA+B,UAAA,YAAAuH,OAAA,CAAAC,eAAA,CAAkB;IAarBvJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAA+B,UAAA,YAAAuH,OAAA,CAAAE,oBAAA,CAAAF,OAAA,CAAAV,qBAAA,EAA8C;;;;;;IAYxE5I,EAAA,CAAAE,cAAA,eAGC;IAIOF,EAAA,CAAAqB,UAAA,mBAAAoI,6DAAA;MAAAzJ,EAAA,CAAAuB,aAAA,CAAAmI,IAAA;MAAA,MAAAC,OAAA,GAAA3J,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAA+H,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC5J,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,aAA6D;IAC/DD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,kBAGC;IAFCF,EAAA,CAAAqB,UAAA,mBAAAwI,6DAAA;MAAA7J,EAAA,CAAAuB,aAAA,CAAAmI,IAAA;MAAA,MAAAI,OAAA,GAAA9J,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAkI,OAAA,CAAAF,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC5J,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,cAAiE;IACnED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAqB,UAAA,mBAAA0I,8DAAA;MAAA/J,EAAA,CAAAuB,aAAA,CAAAmI,IAAA;MAAA,MAAAM,OAAA,GAAAhK,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAoI,OAAA,CAAAJ,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAGtC5J,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAqB,UAAA,mBAAA4I,8DAAA;MAAAjK,EAAA,CAAAuB,aAAA,CAAAmI,IAAA;MAAA,MAAAQ,OAAA,GAAAlK,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAAsI,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAGtBnK,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAiBTJ,EAAA,CAAAE,cAAA,eAIC;IADCF,EAAA,CAAAqB,UAAA,mBAAA+I,0DAAA;MAAApK,EAAA,CAAAuB,aAAA,CAAA8I,IAAA;MAAA,MAAAC,OAAA,GAAAtK,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA4B,WAAA,CAAA0I,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1BvK,EAAA,CAAAI,YAAA,EAAM;;;AD/gBT,OAAM,MAAOoK,oBAAoB;EAuG/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,cAA8B,EAC9BC,YAA0B,EAC1BC,GAAsB;IAJtB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IAtGb;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAhE,QAAQ,GAAU,EAAE;IACpB,KAAArC,aAAa,GAAkB,IAAI;IACnC,KAAAsG,eAAe,GAAG,KAAK;IAEvB,KAAAxK,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAAyK,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IAEzB;IACA,KAAA3D,gBAAgB,GAAG,KAAK;IACxB,KAAA4D,sBAAsB,GAAG,CAAC;IAC1B,KAAAC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAElC;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAA1C,eAAe,GAAU,CACvB;MACE7I,EAAE,EAAE,SAAS;MACbyI,IAAI,EAAE,SAAS;MACfN,IAAI,EAAE,IAAI;MACVqD,MAAM,EAAE,CACN;QAAE5H,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACEzI,EAAE,EAAE,QAAQ;MACZyI,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVqD,MAAM,EAAE,CACN;QAAE5H,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACEzI,EAAE,EAAE,QAAQ;MACZyI,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVqD,MAAM,EAAE,CACN;QAAE5H,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAP,qBAAqB,GAAG,IAAI,CAACW,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAA4C,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAnF,QAAQ,GAAG,KAAK;IAChB,KAAAoF,YAAY,GAAG,KAAK;IACpB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAjE,gBAAgB,GAAG,KAAK;IAChB,KAAAkE,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAIzM,YAAY,EAAE;IASxC,IAAI,CAAC0M,WAAW,GAAG,IAAI,CAAC/B,EAAE,CAACgC,KAAK,CAAC;MAC/BnK,OAAO,EAAE,CAAC,EAAE,EAAE,CAACzC,UAAU,CAAC6M,QAAQ,EAAE7M,UAAU,CAAC8M,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQF,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEJ,UAAU,CAAC;MAEtD,MAAMK,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACP,UAAU,IAAI,IAAI,CAAC;MAC3CG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,IAAI,CAAC;MAExC,IAAIA,IAAI,IAAIA,IAAI,CAACG,GAAG,EAAE;QACpB,IAAI,CAAChJ,aAAa,GAAG6I,IAAI,CAACG,GAAG;QAC7B,IAAI,CAAC1C,eAAe,GAAGuC,IAAI,CAAC9M,QAAQ,IAAI,KAAK;QAC7C4M,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAClC5M,EAAE,EAAE,IAAI,CAACgE,aAAa;UACtBjE,QAAQ,EAAE,IAAI,CAACuK;SAChB,CAAC;OACH,MAAM;QACLqC,OAAO,CAACM,IAAI,CAAC,qCAAqC,CAAC;QACnD,IAAI,CAACjJ,aAAa,GAAG,IAAI;QACzB,IAAI,CAACsG,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAO4C,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,IAAI,CAAClJ,aAAa,GAAG,IAAI;MACzB,IAAI,CAACsG,eAAe,GAAG,KAAK;;EAEhC;EAEQgC,gBAAgBA,CAAA;IACtB,MAAMa,cAAc,GAAG,IAAI,CAAClD,KAAK,CAACmD,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAC7DX,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEO,cAAc,CAAC;IAE5D,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAChD,YAAY,CAACoD,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAAChD,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACL,cAAc,CAACsD,eAAe,CACjCL,cAAc,EACd,IAAI,CAAC1B,oBAAoB,EACzB,IAAI,CAACC,WAAW,CACjB,CAAC+B,SAAS,CAAC;MACVC,IAAI,EAAGrD,YAAY,IAAI;QACrBsC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEvC,YAAY,CAAC;QAC9D,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACsD,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;MACrB,CAAC;MACDV,KAAK,EAAGA,KAAK,IAAI;QACfP,OAAO,CAACO,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAC/C,YAAY,CAACoD,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAAChD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQqD,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACvD,YAAY,EAAErK,EAAE,EAAE;IAE5B;IACA,IAAI,CAACqG,QAAQ,GAAG,IAAI,CAACgE,YAAY,CAAChE,QAAQ,IAAI,EAAE;IAChD,IAAI,CAACoE,eAAe,GAAG,IAAI,CAACpE,QAAQ,CAACL,MAAM,KAAK,IAAI,CAACyF,oBAAoB;IACzE,IAAI,CAAClB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACsD,cAAc,EAAE;EACvB;EAEQF,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACtD,YAAY,EAAEyD,YAAY,EAAE;MACnCnB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC5I,aAAa,CAAC;MACnD2I,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACvC,YAAY,CAACyD,YAAY,CAAC;MAEhE;MACA,IAAI,CAAChO,gBAAgB,GAAG,IAAI,CAACuK,YAAY,CAACyD,YAAY,CAACC,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAChO,EAAE,IAAIgO,CAAC,CAAChB,GAAG;QACnCL,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3BqB,aAAa,EACb,uBAAuB,EACvB,IAAI,CAACjK,aAAa,CACnB;QACD;QACA,OAAOkK,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAClK,aAAa,CAAC;MAC7D,CAAC,CAAC;MAEF2I,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC9M,gBAAgB,CAAC;MAE9D;MACA,IAAI,CAAC,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACuK,YAAY,CAACyD,YAAY,CAAC9H,MAAM,GAAG,CAAC,EAAE;QACvE2G,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI,CAAC9M,gBAAgB,GAAG,IAAI,CAACuK,YAAY,CAACyD,YAAY,CAAC,CAAC,CAAC;QAEzD;QACA,IACE,IAAI,CAACzD,YAAY,CAACyD,YAAY,CAAC9H,MAAM,GAAG,CAAC,KACxCkI,MAAM,CAAC,IAAI,CAACpO,gBAAgB,CAACE,EAAE,CAAC,KAAKkO,MAAM,CAAC,IAAI,CAAClK,aAAa,CAAC,IAC9DkK,MAAM,CAAC,IAAI,CAACpO,gBAAgB,CAACkN,GAAG,CAAC,KAAKkB,MAAM,CAAC,IAAI,CAAClK,aAAa,CAAC,CAAC,EACnE;UACA2I,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAAC9M,gBAAgB,GAAG,IAAI,CAACuK,YAAY,CAACyD,YAAY,CAAC,CAAC,CAAC;;;MAI7D;MACA,IAAI,IAAI,CAAChO,gBAAgB,EAAE;QACzB6M,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;UACtC5M,EAAE,EAAE,IAAI,CAACF,gBAAgB,CAACE,EAAE,IAAI,IAAI,CAACF,gBAAgB,CAACkN,GAAG;UACzDjN,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC,QAAQ;UACxCuB,KAAK,EAAE,IAAI,CAACxB,gBAAgB,CAACwB,KAAK;UAClCnB,QAAQ,EAAE,IAAI,CAACL,gBAAgB,CAACK;SACjC,CAAC;OACH,MAAM;QACLwM,OAAO,CAACM,IAAI,CAAC,6BAA6B,CAAC;;;EAGjD;EAEQV,kBAAkBA,CAAA;IACxB;IACA;EAAA;EAGF;EACAhF,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACwE,WAAW,CAACoC,KAAK,IAAI,CAAC,IAAI,CAAC9D,YAAY,EAAErK,EAAE,EAAE;IAEvD,MAAM6B,OAAO,GAAG,IAAI,CAACkK,WAAW,CAACuB,GAAG,CAAC,SAAS,CAAC,EAAEc,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAACxM,OAAO,EAAE;IAEd,MAAM/B,gBAAgB,GAAG,IAAI,CAACuK,YAAY,CAACyD,YAAY,EAAEC,IAAI,CAC1DC,CAAM,IAAK,CAACA,CAAC,CAAChO,EAAE,IAAIgO,CAAC,CAAChB,GAAG,MAAM,IAAI,CAAChJ,aAAa,CACnD;IAED,MAAMsK,UAAU,GAAGxO,gBAAgB,EAAEE,EAAE,IAAIF,gBAAgB,EAAEkN,GAAG;IAEhE,IAAI,CAACsB,UAAU,EAAE;MACf,IAAI,CAACnE,YAAY,CAACoD,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF,IAAI,CAACrD,cAAc,CAAC3C,WAAW,CAC7B+G,UAAU,EACVzM,OAAO,EACP0M,SAAS,EACT,MAAa,EACb,IAAI,CAAClE,YAAY,CAACrK,EAAE,CACrB,CAACyN,SAAS,CAAC;MACVC,IAAI,EAAGc,OAAY,IAAI;QACrB,IAAI,CAACnI,QAAQ,CAACoI,IAAI,CAACD,OAAO,CAAC;QAC3B,IAAI,CAACzC,WAAW,CAAC2C,KAAK,EAAE;QACxB,IAAI,CAACb,cAAc,EAAE;MACvB,CAAC;MACDX,KAAK,EAAGA,KAAU,IAAI;QACpBP,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAAC/C,YAAY,CAACoD,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEA;EACAoB,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAC7I,MAAM,KAAK,CAAC,EAAE;IAElC,KAAK,IAAI+I,IAAI,IAAIF,KAAK,EAAE;MACtB,IAAI,CAACG,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3B,MAAMjP,gBAAgB,GAAG,IAAI,CAACuK,YAAY,EAAEyD,YAAY,EAAEC,IAAI,CAC3DC,CAAM,IAAK,CAACA,CAAC,CAAChO,EAAE,IAAIgO,CAAC,CAAChB,GAAG,MAAM,IAAI,CAAChJ,aAAa,CACnD;IAED,MAAMsK,UAAU,GAAGxO,gBAAgB,EAAEE,EAAE,IAAIF,gBAAgB,EAAEkN,GAAG;IAEhE,IAAI,CAACsB,UAAU,EAAE;MACf,IAAI,CAACnE,YAAY,CAACoD,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF,IAAI,CAACrD,cAAc,CAAC3C,WAAW,CAC7B+G,UAAU,EACV,EAAE,EACFS,IAAI,EACJR,SAAS,EACT,IAAI,CAAClE,YAAY,CAACrK,EAAE,CACrB,CAACyN,SAAS,CAAC;MACVC,IAAI,EAAGc,OAAY,IAAI;QACrB,IAAI,CAACnI,QAAQ,CAACoI,IAAI,CAACD,OAAO,CAAC;QAC3B,IAAI,CAACX,cAAc,EAAE;QACrB,IAAI,CAAC1D,YAAY,CAAC8E,WAAW,CAAC,4BAA4B,CAAC;MAC7D,CAAC;MACD/B,KAAK,EAAGA,KAAU,IAAI;QACpBP,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAAC/C,YAAY,CAACoD,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEA;EACM5G,mBAAmBA,CAAA;IAAA,IAAAuI,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDC,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE;;SAEpB,CAAC;QAEFT,KAAI,CAACjE,aAAa,GAAG,IAAI2E,aAAa,CAACR,MAAM,EAAE;UAC7CS,QAAQ,EAAE;SACX,CAAC;QAEFX,KAAI,CAAChE,WAAW,GAAG,EAAE;QACrBgE,KAAI,CAAC/H,gBAAgB,GAAG,IAAI;QAC5B+H,KAAI,CAACnE,sBAAsB,GAAG,CAAC;QAC/BmE,KAAI,CAAClE,mBAAmB,GAAG,WAAW;QAEtCkE,KAAI,CAAC/D,cAAc,GAAG2E,WAAW,CAAC,MAAK;UACrCZ,KAAI,CAACnE,sBAAsB,EAAE;UAC7BmE,KAAI,CAAC9E,GAAG,CAAC2F,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAERb,KAAI,CAACjE,aAAa,CAAC+E,eAAe,GAAIpB,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAACqB,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;YACvBhB,KAAI,CAAChE,WAAW,CAACuD,IAAI,CAACG,KAAK,CAACqB,IAAI,CAAC;;QAErC,CAAC;QAEDf,KAAI,CAACjE,aAAa,CAACkF,MAAM,GAAG,MAAK;UAC/BjB,KAAI,CAACkB,oBAAoB,EAAE;QAC7B,CAAC;QAEDlB,KAAI,CAACjE,aAAa,CAACoF,KAAK,CAAC,GAAG,CAAC;QAC7BnB,KAAI,CAAC/E,YAAY,CAAC8E,WAAW,CAAC,8BAA8B,CAAC;OAC9D,CAAC,OAAO/B,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrEgC,KAAI,CAAC/E,YAAY,CAACoD,SAAS,CAAC,oCAAoC,CAAC;QACjE2B,KAAI,CAACjI,oBAAoB,EAAE;;IAC5B;EACH;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACmE,aAAa,IAAI,IAAI,CAACA,aAAa,CAACqF,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACrF,aAAa,CAACsF,IAAI,EAAE;MACzB,IAAI,CAACtF,aAAa,CAACmE,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAACpF,cAAc,EAAE;MACvBwF,aAAa,CAAC,IAAI,CAACxF,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAAChE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC6D,mBAAmB,GAAG,YAAY;EACzC;EAEA/D,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACgE,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACqF,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACrF,aAAa,CAACsF,IAAI,EAAE;;MAE3B,IAAI,CAACtF,aAAa,CAACmE,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;MACtE,IAAI,CAACtF,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvBwF,aAAa,CAAC,IAAI,CAACxF,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAAChE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC4D,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEckF,oBAAoBA,CAAA;IAAA,IAAAQ,MAAA;IAAA,OAAAzB,iBAAA;MAChC,IAAI;QACF,IAAIyB,MAAI,CAAC1F,WAAW,CAAClF,MAAM,KAAK,CAAC,EAAE;UACjC4K,MAAI,CAACzG,YAAY,CAAC0G,WAAW,CAAC,wBAAwB,CAAC;UACvDD,MAAI,CAAC3J,oBAAoB,EAAE;UAC3B;;QAGF,MAAM6J,SAAS,GAAG,IAAIC,IAAI,CAACH,MAAI,CAAC1F,WAAW,EAAE;UAC3C8F,IAAI,EAAE;SACP,CAAC;QAEF,IAAIJ,MAAI,CAAC7F,sBAAsB,GAAG,CAAC,EAAE;UACnC6F,MAAI,CAACzG,YAAY,CAAC0G,WAAW,CAC3B,+CAA+C,CAChD;UACDD,MAAI,CAAC3J,oBAAoB,EAAE;UAC3B;;QAGF,MAAMgK,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACJ,SAAS,CAAC,EAAE,SAASK,IAAI,CAACC,GAAG,EAAE,OAAO,EAAE;UAClEJ,IAAI,EAAE;SACP,CAAC;QAEF,MAAMJ,MAAI,CAACS,gBAAgB,CAACJ,SAAS,CAAC;QACtCL,MAAI,CAACzG,YAAY,CAAC8E,WAAW,CAAC,sBAAsB,CAAC;OACtD,CAAC,OAAO/B,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D0D,MAAI,CAACzG,YAAY,CAACoD,SAAS,CAAC,yCAAyC,CAAC;OACvE,SAAS;QACRqD,MAAI,CAAC5F,mBAAmB,GAAG,MAAM;QACjC4F,MAAI,CAAC7F,sBAAsB,GAAG,CAAC;QAC/B6F,MAAI,CAAC1F,WAAW,GAAG,EAAE;;IACtB;EACH;EAEcmG,gBAAgBA,CAACJ,SAAe;IAAA,IAAAK,MAAA;IAAA,OAAAnC,iBAAA;MAC5C,MAAMrP,gBAAgB,GAAGwR,MAAI,CAACjH,YAAY,EAAEyD,YAAY,EAAEC,IAAI,CAC3DC,CAAM,IAAK,CAACA,CAAC,CAAChO,EAAE,IAAIgO,CAAC,CAAChB,GAAG,MAAMsE,MAAI,CAACtN,aAAa,CACnD;MAED,MAAMsK,UAAU,GAAGxO,gBAAgB,EAAEE,EAAE,IAAIF,gBAAgB,EAAEkN,GAAG;MAEhE,IAAI,CAACsB,UAAU,EAAE;QACf,MAAM,IAAIiD,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCJ,MAAI,CAACpH,cAAc,CAAC3C,WAAW,CAC7B+G,UAAU,EACV,EAAE,EACF2C,SAAS,EACT1C,SAAS,EACT+C,MAAI,CAACjH,YAAY,CAACrK,EAAE,CACrB,CAACyN,SAAS,CAAC;UACVC,IAAI,EAAGc,OAAY,IAAI;YACrB8C,MAAI,CAACjL,QAAQ,CAACoI,IAAI,CAACD,OAAO,CAAC;YAC3B8C,MAAI,CAACzD,cAAc,EAAE;YACrB4D,OAAO,EAAE;UACX,CAAC;UACDvE,KAAK,EAAGA,KAAU,IAAI;YACpBP,OAAO,CAACO,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChEwE,MAAM,CAACxE,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEAyE,uBAAuBA,CAACC,QAAgB;IACtC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMI,OAAO,GAAGJ,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGC,OAAO,IAAIG,OAAO,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACApJ,oBAAoBA,CAACqJ,QAAa;IAChC,OAAOA,QAAQ,EAAE3G,MAAM,IAAI,EAAE;EAC/B;EAEAxD,mBAAmBA,CAACmK,QAAa;IAC/B,IAAI,CAACjK,qBAAqB,GAAGiK,QAAQ;EACvC;EAEA3J,WAAWA,CAAC5E,KAAU;IACpB,MAAMwO,cAAc,GAAG,IAAI,CAACrG,WAAW,CAACuB,GAAG,CAAC,SAAS,CAAC,EAAEc,KAAK,IAAI,EAAE;IACnE,MAAMiE,UAAU,GAAGD,cAAc,IAAIxO,KAAK,CAACA,KAAK,IAAIA,KAAK,CAAC;IAC1D,IAAI,CAACmI,WAAW,CAACuG,UAAU,CAAC;MAAEzQ,OAAO,EAAEwQ;IAAU,CAAE,CAAC;IAEpD,IAAI,CAAC3H,eAAe,GAAG,KAAK;IAE5B6H,UAAU,CAAC,MAAK;MACd,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CACrC,qCAAqC,CACf;MACxB,IAAIF,QAAQ,EAAE;QACZA,QAAQ,CAACG,KAAK,EAAE;;IAEpB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAClI,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,CAACC,kBAAkB,GAAG,KAAK;EACjC;EAEAkI,oBAAoBA,CAAA;IAClB,IAAI,CAAClI,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,CAACD,eAAe,GAAG,KAAK;EAC9B;EAEA;EACA9E,cAAcA,CAAC4I,OAAY;IACzB,IAAIA,OAAO,CAACsE,WAAW,IAAItE,OAAO,CAACsE,WAAW,CAAC9M,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM+M,UAAU,GAAGvE,OAAO,CAACsE,WAAW,CAAC,CAAC,CAAC;MACzC,IAAIC,UAAU,CAAC/B,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAID,UAAU,CAAC/B,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAID,UAAU,CAAC/B,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAEf,OAAO,MAAM;EACf;EAEAnN,QAAQA,CAAC2I,OAAY;IACnB,OACEA,OAAO,CAACsE,WAAW,EAAEG,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAClC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,CAAC,IACvE,KAAK;EAET;EAEAlN,OAAOA,CAAC0I,OAAY;IAClB,OACEA,OAAO,CAACsE,WAAW,EAAEG,IAAI,CACtBC,GAAQ,IAAK,CAACA,GAAG,CAAClC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,CAC9C,IAAI,KAAK;EAEd;EAEAzQ,WAAWA,CAACiM,OAAY;IACtB,MAAM2E,eAAe,GAAG3E,OAAO,CAACsE,WAAW,EAAE/E,IAAI,CAAEmF,GAAQ,IACzDA,GAAG,CAAClC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,OAAOG,eAAe,EAAEC,GAAG,IAAI,EAAE;EACnC;EAEArQ,WAAWA,CAACyL,OAAY;IACtB,MAAM6E,cAAc,GAAG7E,OAAO,CAACsE,WAAW,EAAE/E,IAAI,CAC7CmF,GAAQ,IAAK,CAACA,GAAG,CAAClC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOK,cAAc,EAAE5K,IAAI,IAAI,SAAS;EAC1C;EAEAzF,WAAWA,CAACwL,OAAY;IACtB,MAAM6E,cAAc,GAAG7E,OAAO,CAACsE,WAAW,EAAE/E,IAAI,CAC7CmF,GAAQ,IAAK,CAACA,GAAG,CAAClC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACK,cAAc,EAAEnD,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAMoD,KAAK,GAAGD,cAAc,CAACnD,IAAI;IACjC,IAAIoD,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOxB,IAAI,CAACyB,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOxB,IAAI,CAACyB,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAxQ,WAAWA,CAAC0L,OAAY;IACtB,MAAM6E,cAAc,GAAG7E,OAAO,CAACsE,WAAW,EAAE/E,IAAI,CAC7CmF,GAAQ,IAAK,CAACA,GAAG,CAAClC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACK,cAAc,EAAErC,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAIqC,cAAc,CAACrC,IAAI,CAACgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAIK,cAAc,CAACrC,IAAI,CAACgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAIK,cAAc,CAACrC,IAAI,CAACwC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAIH,cAAc,CAACrC,IAAI,CAACwC,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAIH,cAAc,CAACrC,IAAI,CAACwC,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEAjO,uBAAuBA,CAACkO,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAMC,cAAc,GAAG,IAAI,CAACrN,QAAQ,CAACoN,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAACtN,QAAQ,CAACoN,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACC,cAAc,EAAEhT,SAAS,IAAI,CAACiT,eAAe,EAAEjT,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAMkT,WAAW,GAAG,IAAIzC,IAAI,CAACuC,cAAc,CAAChT,SAAS,CAAC,CAACmT,YAAY,EAAE;IACrE,MAAMC,YAAY,GAAG,IAAI3C,IAAI,CAACwC,eAAe,CAACjT,SAAS,CAAC,CAACmT,YAAY,EAAE;IAEvE,OAAOD,WAAW,KAAKE,YAAY;EACrC;EAEArO,gBAAgBA,CAACgO,KAAa;IAC5B,MAAMC,cAAc,GAAG,IAAI,CAACrN,QAAQ,CAACoN,KAAK,CAAC;IAC3C,MAAMM,WAAW,GAAG,IAAI,CAAC1N,QAAQ,CAACoN,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACM,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOL,cAAc,CAACtS,MAAM,EAAEpB,EAAE,KAAK+T,WAAW,CAAC3S,MAAM,EAAEpB,EAAE;EAC7D;EAEA2F,oBAAoBA,CAAC8N,KAAa;IAChC,MAAMC,cAAc,GAAG,IAAI,CAACrN,QAAQ,CAACoN,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAACtN,QAAQ,CAACoN,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACE,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAACtS,MAAM,EAAEpB,EAAE,KAAK2T,eAAe,CAACvS,MAAM,EAAEpB,EAAE;EACjE;EAEA0F,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC2E,YAAY,EAAE2J,OAAO,IAAI,KAAK;EAC5C;EAEAtS,YAAYA,CAACuS,MAAc;IACzB;IACA,MAAMC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAMT,KAAK,GAAGQ,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAClO,MAAM;IAClD,OAAOkO,MAAM,CAACT,KAAK,CAAC;EACtB;EAEA;EACA9Q,YAAYA,CAAC6L,OAAY;IACvB,MAAM6E,cAAc,GAAG7E,OAAO,CAACsE,WAAW,EAAE/E,IAAI,CAC7CmF,GAAQ,IAAK,CAACA,GAAG,CAAClC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAIK,cAAc,EAAED,GAAG,EAAE;MACvBgB,MAAM,CAACC,IAAI,CAAChB,cAAc,CAACD,GAAG,EAAE,QAAQ,CAAC;;EAE7C;EAEAjR,eAAeA,CAACqM,OAAY;IAC1B,MAAM2E,eAAe,GAAG3E,OAAO,CAACsE,WAAW,EAAE/E,IAAI,CAAEmF,GAAQ,IACzDA,GAAG,CAAClC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAIG,eAAe,EAAEC,GAAG,EAAE;MACxBgB,MAAM,CAACC,IAAI,CAAClB,eAAe,CAACC,GAAG,EAAE,QAAQ,CAAC;;EAE9C;EAEAzP,cAAcA,CAAC2Q,SAAiB,EAAE1Q,KAAa;IAC7C+I,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE0H,SAAS,EAAE1Q,KAAK,CAAC;IACjD;EACF;;EAEAG,cAAcA,CAACwQ,QAAa,EAAEN,MAAc;IAC1C,OAAOM,QAAQ,CAACC,KAAK,EAAEhB,QAAQ,CAACS,MAAM,CAAC,IAAI,KAAK;EAClD;EAEA/K,gBAAgBA,CAAC8H,IAAa;IAC5B;IACA,MAAMyD,KAAK,GAAGhC,QAAQ,CAACiC,aAAa,CAAC,OAAO,CAAC;IAC7CD,KAAK,CAACzD,IAAI,GAAG,MAAM;IAEnB,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpByD,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI3D,IAAI,KAAK,OAAO,EAAE;MAC3ByD,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI3D,IAAI,KAAK,UAAU,EAAE;MAC9ByD,KAAK,CAACE,MAAM,GAAG,iCAAiC;;IAGlDF,KAAK,CAACG,QAAQ,GAAIhG,KAAU,IAAI;MAC9B,IAAI,CAACD,cAAc,CAACC,KAAK,CAAC;IAC5B,CAAC;IAED6F,KAAK,CAACI,KAAK,EAAE;IACb,IAAI,CAAClK,kBAAkB,GAAG,KAAK;EACjC;EAEAlB,UAAUA,CAAA;IACRkD,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC;IACA,IAAI,CAACjC,kBAAkB,GAAG,KAAK;EACjC;EAEAmK,YAAYA,CAAA;IACV,IAAI,CAAClJ,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB,IAAI,CAACf,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,EAAE;;EAE3B;EAEAiK,cAAcA,CAAA;IACZpI,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;EACF;EAEA;EACAxM,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAM2U,IAAI,GAAG,IAAI7D,IAAI,CAAC9Q,UAAU,CAAC;IACjC,MAAM+Q,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM8D,MAAM,GAAG7D,GAAG,CAAC8D,OAAO,EAAE,GAAGF,IAAI,CAACE,OAAO,EAAE;IAC7C,MAAMC,QAAQ,GAAGrD,IAAI,CAACC,KAAK,CAACkD,MAAM,GAAG,KAAK,CAAC;IAE3C,IAAIE,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAElD,MAAMC,SAAS,GAAGtD,IAAI,CAACC,KAAK,CAACoD,QAAQ,GAAG,EAAE,CAAC;IAC3C,IAAIC,SAAS,GAAG,EAAE,EAAE,OAAO,UAAUA,SAAS,GAAG;IAEjD,MAAMC,QAAQ,GAAGvD,IAAI,CAACC,KAAK,CAACqD,SAAS,GAAG,EAAE,CAAC;IAC3C,OAAO,UAAUC,QAAQ,GAAG;EAC9B;EAEAtP,iBAAiBA,CAACrF,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMsU,IAAI,GAAG,IAAI7D,IAAI,CAACzQ,SAAS,CAAC;IAChC,OAAOsU,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAhV,mBAAmBA,CAACE,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMsU,IAAI,GAAG,IAAI7D,IAAI,CAACzQ,SAAS,CAAC;IAChC,MAAM+U,KAAK,GAAG,IAAItE,IAAI,EAAE;IACxB,MAAMuE,SAAS,GAAG,IAAIvE,IAAI,CAACsE,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIZ,IAAI,CAACnB,YAAY,EAAE,KAAK4B,KAAK,CAAC5B,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAImB,IAAI,CAACnB,YAAY,EAAE,KAAK6B,SAAS,CAAC7B,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOmB,IAAI,CAACa,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEAjU,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAMiU,QAAQ,GAAG,sBAAsB;IACvC,OAAOjU,OAAO,CAACkU,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEA;EACAE,qBAAqBA,CAAA;IACnB5B,MAAM,CAAC6B,OAAO,CAACC,IAAI,EAAE;EACvB;EAEA/U,eAAeA,CAAC8S,MAAc;IAC5BtH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEqH,MAAM,CAAC;IAChD;EACF;EAEA;EACAvP,cAAcA,CAAC8J,OAAY,EAAEI,KAAU;IACrCjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE4B,OAAO,CAAC;EAC1C;EAEA3J,oBAAoBA,CAAC2J,OAAY,EAAEI,KAAU;IAC3CA,KAAK,CAACuH,cAAc,EAAE;IACtBxJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4B,OAAO,CAAC;EAC/C;EAEA4H,aAAaA,CAACxH,KAAU;IACtB;IACA,MAAM/M,OAAO,GAAG+M,KAAK,CAACE,MAAM,CAACV,KAAK;IAClC,IAAIvM,OAAO,CAACwM,IAAI,EAAE,EAAE;MAClB;IAAA,CACD,MAAM;MACL;IAAA;EAEJ;EAEAgI,cAAcA,CAACzH,KAAoB;IACjC,IAAIA,KAAK,CAAC0H,GAAG,KAAK,OAAO,IAAI,CAAC1H,KAAK,CAAC2H,QAAQ,EAAE;MAC5C3H,KAAK,CAACuH,cAAc,EAAE;MACtB,IAAI,CAAC5O,WAAW,EAAE;;EAEtB;EAEAiP,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAAC9H,KAAU;IACjB;IACA,MAAM+H,OAAO,GAAG/H,KAAK,CAACE,MAAM;IAC5B,IACE6H,OAAO,CAACC,SAAS,KAAK,CAAC,IACvB,IAAI,CAACnM,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAACqM,gBAAgB,EAAE;;EAE3B;EAEAvQ,gBAAgBA,CAACmN,KAAa,EAAEjF,OAAY;IAC1C,OAAOA,OAAO,CAACxO,EAAE,IAAIyT,KAAK;EAC5B;EAEA;EACAqD,SAASA,CAAC9F,IAAuB;IAC/B,IAAI,CAAC3F,QAAQ,GAAG2F,IAAI;IACpB,IAAI,CAAC5F,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACE,YAAY,GAAG,CAAC;IAErB,IAAI,CAACC,SAAS,GAAGuE,WAAW,CAAC,MAAK;MAChC,IAAI,CAACxE,YAAY,EAAE;MACnB,IAAI,CAAClB,GAAG,CAAC2F,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;IAER,IAAI,CAAC5F,YAAY,CAAC8E,WAAW,CAC3B,SAAS+B,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,UAAU,CACxD;EACH;EAEA+F,OAAOA,CAAA;IACL,IAAI,IAAI,CAACxL,SAAS,EAAE;MAClBoF,aAAa,CAAC,IAAI,CAACpF,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACnB,YAAY,CAAC8E,WAAW,CAAC,eAAe,CAAC;EAChD;EAEA+H,cAAcA,CAAA;IACZ,IAAI,CAACF,SAAS,CAAC,OAAO,CAAC;EACzB;EAEAG,cAAcA,CAAA;IACZ,IAAI,CAACH,SAAS,CAAC,OAAO,CAAC;EACzB;EAEA;EACAhC,YAAYA,CAAA;IACV,IAAI,CAAClK,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB,IAAI,CAACC,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,EAAE;;EAE3B;EAEAoM,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACrM,WAAW,CAACwD,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACvD,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACzE,QAAQ,CAAC8Q,MAAM,CACtC3I,OAAO,IACNA,OAAO,CAAC3M,OAAO,EACXuV,WAAW,EAAE,CACd5D,QAAQ,CAAC,IAAI,CAAC3I,WAAW,CAACuM,WAAW,EAAE,CAAC,IAC3C5I,OAAO,CAACpN,MAAM,EAAErB,QAAQ,EACpBqX,WAAW,EAAE,CACd5D,QAAQ,CAAC,IAAI,CAAC3I,WAAW,CAACuM,WAAW,EAAE,CAAC,CAC9C;EACH;EAEA;EACAP,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACrM,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAErK,EAAE,EACvE;IAEF,IAAI,CAACwK,aAAa,GAAG,IAAI;IACzB,IAAI,CAACkB,WAAW,EAAE;IAElB,IAAI,CAACxB,cAAc,CAACsD,eAAe,CACjC,IAAI,CAACnD,YAAY,CAACrK,EAAE,EACpB,IAAI,CAACyL,oBAAoB,EACzB,IAAI,CAACC,WAAW,CACjB,CAAC+B,SAAS,CAAC;MACVC,IAAI,EAAGrD,YAAY,IAAI;QACrB,MAAMgN,WAAW,GAAGhN,YAAY,CAAChE,QAAQ,IAAI,EAAE;QAC/C,IAAIgR,WAAW,CAACrR,MAAM,GAAG,CAAC,EAAE;UAC1B;UACA,IAAI,CAACK,QAAQ,GAAG,CAAC,GAAGgR,WAAW,EAAE,GAAG,IAAI,CAAChR,QAAQ,CAAC;UAClD,IAAI,CAACoE,eAAe,GAClB4M,WAAW,CAACrR,MAAM,KAAK,IAAI,CAACyF,oBAAoB;SACnD,MAAM;UACL,IAAI,CAAChB,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD0C,KAAK,EAAGA,KAAK,IAAI;QACfP,OAAO,CAACO,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC1C,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACkB,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;EAEA;EACM4L,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApI,iBAAA;MACjB,IAAI;QACF,MAAMqI,QAAQ,SAASD,MAAI,CAACE,kBAAkB,EAAE;QAChD,MAAM;UAAEC,QAAQ;UAAEC;QAAS,CAAE,GAAGH,QAAQ,CAACI,MAAM;QAE/C,MAAMC,eAAe,GAAG;UACtB7G,IAAI,EAAE,UAAU;UAChB0G,QAAQ;UACRC,SAAS;UACTG,OAAO,QAAQP,MAAI,CAACQ,yBAAyB,CAACL,QAAQ,EAAEC,SAAS,CAAC;UAClEK,MAAM,EAAE,kCAAkCN,QAAQ,IAAIC,SAAS,OAAO;UACtEjX,SAAS,EAAE,IAAIyQ,IAAI;SACpB;QAED,MAAMoG,MAAI,CAACU,mBAAmB,CAACJ,eAAe,CAAC;QAC/CN,MAAI,CAACpN,YAAY,CAAC8E,WAAW,CAAC,+BAA+B,CAAC;OAC/D,CAAC,OAAO/B,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/DqK,MAAI,CAACpN,YAAY,CAACoD,SAAS,CAAC,qCAAqC,CAAC;;IACnE;EACH;EAEQkK,kBAAkBA,CAAA;IACxB,OAAO,IAAIjG,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrCrC,SAAS,CAAC6I,WAAW,CAACT,kBAAkB,CAAChG,OAAO,EAAEC,MAAM,EAAE;QACxDyG,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;EACJ;EAEcN,yBAAyBA,CACrCO,GAAW,EACXC,GAAW;IAAA,OAAApJ,iBAAA;MAEX,IAAI;QACF,MAAMqJ,QAAQ,SAASC,KAAK,CAC1B,+DAA+DH,GAAG,QAAQC,GAAG,2BAA2B,CACzG;QAED,IAAIC,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMzI,IAAI,SAASuI,QAAQ,CAACG,IAAI,EAAE;UAClC,OAAO1I,IAAI,CAAC2I,YAAY,IAAI,GAAGN,GAAG,KAAKC,GAAG,EAAE;;OAE/C,CAAC,OAAOrL,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;MAG3D,OAAO,GAAGoL,GAAG,KAAKC,GAAG,EAAE;IAAC;EAC1B;EAEcN,mBAAmBA,CAACY,YAAiB;IAAA,IAAAC,MAAA;IAAA,OAAA3J,iBAAA;MACjD,MAAMrP,gBAAgB,GAAGgZ,MAAI,CAACzO,YAAY,EAAEyD,YAAY,EAAEC,IAAI,CAC3DC,CAAM,IAAK,CAACA,CAAC,CAAChO,EAAE,IAAIgO,CAAC,CAAChB,GAAG,MAAM8L,MAAI,CAAC9U,aAAa,CACnD;MAED,MAAMsK,UAAU,GAAGxO,gBAAgB,EAAEE,EAAE,IAAIF,gBAAgB,EAAEkN,GAAG;MAEhE,IAAI,CAACsB,UAAU,EAAE;QACf,MAAM,IAAIiD,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCoH,MAAI,CAAC5O,cAAc,CAAC3C,WAAW,CAC7B+G,UAAU,EACV,yBAAyBuK,YAAY,CAACf,OAAO,EAAE,EAC/CvJ,SAAS,EACT,MAAa,EACbuK,MAAI,CAACzO,YAAY,CAACrK,EAAE,CACrB,CAACyN,SAAS,CAAC;UACVC,IAAI,EAAGc,OAAY,IAAI;YACrBsK,MAAI,CAACzS,QAAQ,CAACoI,IAAI,CAACD,OAAO,CAAC;YAC3BsK,MAAI,CAACjL,cAAc,EAAE;YACrB4D,OAAO,EAAE;UACX,CAAC;UACDvE,KAAK,EAAGA,KAAU,IAAI;YACpBP,OAAO,CAACO,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;YACDwE,MAAM,CAACxE,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA;EACM6L,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7J,iBAAA;MAChB,IAAI;QACF,MAAM8J,OAAO,SAASD,MAAI,CAACE,aAAa,EAAE;QAE1C,IAAID,OAAO,EAAE;UACX,MAAMD,MAAI,CAACG,kBAAkB,CAACF,OAAO,CAAC;UACtCD,MAAI,CAAC7O,YAAY,CAAC8E,WAAW,CAAC,6BAA6B,CAAC;;OAE/D,CAAC,OAAO/B,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D8L,MAAI,CAAC7O,YAAY,CAACoD,SAAS,CAAC,mCAAmC,CAAC;;IACjE;EACH;EAEc2L,aAAaA,CAAA;IAAA,OAAA/J,iBAAA;MACzB,OAAO,IAAIqC,OAAO,CAAEC,OAAO,IAAI;QAC7Bc,UAAU,CAAC,MAAK;UACdd,OAAO,CAAC;YACNhJ,IAAI,EAAE,UAAU;YAChB2Q,KAAK,EAAE,mBAAmB;YAC1BC,KAAK,EAAE,sBAAsB;YAC7BC,MAAM,EAAE;WACT,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IAAC;EACL;EAEcH,kBAAkBA,CAACI,WAAgB;IAAA,IAAAC,MAAA;IAAA,OAAArK,iBAAA;MAC/C,MAAMrP,gBAAgB,GAAG0Z,MAAI,CAACnP,YAAY,EAAEyD,YAAY,EAAEC,IAAI,CAC3DC,CAAM,IAAK,CAACA,CAAC,CAAChO,EAAE,IAAIgO,CAAC,CAAChB,GAAG,MAAMwM,MAAI,CAACxV,aAAa,CACnD;MAED,MAAMsK,UAAU,GAAGxO,gBAAgB,EAAEE,EAAE,IAAIF,gBAAgB,EAAEkN,GAAG;MAEhE,IAAI,CAACsB,UAAU,EAAE;QACf,MAAM,IAAIiD,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrC8H,MAAI,CAACtP,cAAc,CAAC3C,WAAW,CAC7B+G,UAAU,EACV,uBAAuBiL,WAAW,CAAC9Q,IAAI,QAAQ8Q,WAAW,CAACH,KAAK,QAAQG,WAAW,CAACF,KAAK,EAAE,EAC3F9K,SAAS,EACT,MAAa,EACbiL,MAAI,CAACnP,YAAY,CAACrK,EAAE,CACrB,CAACyN,SAAS,CAAC;UACVC,IAAI,EAAGc,OAAY,IAAI;YACrBgL,MAAI,CAACnT,QAAQ,CAACoI,IAAI,CAACD,OAAO,CAAC;YAC3BgL,MAAI,CAAC3L,cAAc,EAAE;YACrB4D,OAAO,EAAE;UACX,CAAC;UACDvE,KAAK,EAAGA,KAAU,IAAI;YACpBP,OAAO,CAACO,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;YACrEwE,MAAM,CAACxE,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA;EACAW,cAAcA,CAAA;IACZ0E,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACkH,iBAAiB,EAAE;QAC1B,MAAM9C,OAAO,GAAG,IAAI,CAAC8C,iBAAiB,CAACC,aAAa;QACpD/C,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACgD,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,cAAcA,CAACtG,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMuG,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGjI,IAAI,CAACC,KAAK,CAACD,IAAI,CAAClF,GAAG,CAAC0G,KAAK,CAAC,GAAGxB,IAAI,CAAClF,GAAG,CAACiN,CAAC,CAAC,CAAC;IACnD,OAAOG,UAAU,CAAC,CAAC1G,KAAK,GAAGxB,IAAI,CAACmI,GAAG,CAACJ,CAAC,EAAEE,CAAC,CAAC,EAAEG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGJ,KAAK,CAACC,CAAC,CAAC;EACzE;EAEAI,cAAcA,CAACnI,OAAe;IAC5B,MAAMoI,KAAK,GAAGtI,IAAI,CAACC,KAAK,CAACC,OAAO,GAAG,IAAI,CAAC;IACxC,MAAMH,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAEC,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;IACjD,MAAMqI,IAAI,GAAGrI,OAAO,GAAG,EAAE;IAEzB,IAAIoI,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIvI,OAAO,CAACI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAImI,IAAI,CAC3DpI,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAGvB,OAAO,GAAGL,OAAO,IAAIwI,IAAI,CAACpI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACzD;EAEAoI,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAzQ,aAAaA,CAAA;IACX,IAAI,CAACa,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,UAAU,GAAG,KAAK;EACzB;EAEAiI,oBAAoBA,CAAA;IAClB,IAAI,CAAClI,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,CAACD,eAAe,GAAG,KAAK;EAC9B;EAEAxB,gBAAgBA,CAAC8H,IAAa;IAC5B;IACA,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAACuJ,SAAS,CAACb,aAAa,CAAC/E,MAAM,GAAG,SAAS;KAChD,MAAM,IAAI3D,IAAI,KAAK,OAAO,EAAE;MAC3B,IAAI,CAACuJ,SAAS,CAACb,aAAa,CAAC/E,MAAM,GAAG,SAAS;KAChD,MAAM,IAAI3D,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAI,CAACuJ,SAAS,CAACb,aAAa,CAAC/E,MAAM,GACjC,uCAAuC;KAC1C,MAAM;MACL,IAAI,CAAC4F,SAAS,CAACb,aAAa,CAAC/E,MAAM,GAAG,KAAK;;IAG7C,IAAI,CAAC4F,SAAS,CAACb,aAAa,CAAC7E,KAAK,EAAE;EACtC;EAEA;EACAmB,qBAAqBA,CAAA;IACnB;IACA5B,MAAM,CAAC6B,OAAO,CAACC,IAAI,EAAE;EACvB;EAEA9V,gBAAgBA,CAACC,UAAe;IAC9B,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IACpC,MAAM+Q,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMqJ,cAAc,GAAG,IAAIrJ,IAAI,CAAC9Q,UAAU,CAAC;IAC3C,MAAMoa,aAAa,GAAG3I,IAAI,CAACC,KAAK,CAC9B,CAACX,GAAG,CAAC8D,OAAO,EAAE,GAAGsF,cAAc,CAACtF,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CACzD;IAED,IAAIuF,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;IACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,UAAUA,aAAa,MAAM;IAC5D,IAAIA,aAAa,GAAG,IAAI,EACtB,OAAO,UAAU3I,IAAI,CAACC,KAAK,CAAC0I,aAAa,GAAG,EAAE,CAAC,IAAI;IACrD,OAAO,UAAU3I,IAAI,CAACC,KAAK,CAAC0I,aAAa,GAAG,IAAI,CAAC,IAAI;EACvD;EAEA1F,cAAcA,CAAA;IACZ;IACA,IAAI,CAACpK,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACD,eAAe,GAAG,KAAK;EAC9B;EAEAgM,QAAQA,CAAC9H,KAAU;IACjB,MAAM+H,OAAO,GAAG/H,KAAK,CAACE,MAAM;IAC5B,IACE6H,OAAO,CAACC,SAAS,KAAK,CAAC,IACvB,IAAI,CAACnM,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAACqM,gBAAgB,EAAE;;EAE3B;EAEAvQ,gBAAgBA,CAACmN,KAAa,EAAEjF,OAAY;IAC1C,OAAOA,OAAO,CAACxO,EAAE,IAAIyT,KAAK;EAC5B;EAEAlO,uBAAuBA,CAACkO,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5B,MAAMC,cAAc,GAAG,IAAI,CAACrN,QAAQ,CAACoN,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAACtN,QAAQ,CAACoN,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACC,cAAc,EAAEhT,SAAS,IAAI,CAACiT,eAAe,EAAEjT,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAMkT,WAAW,GAAG,IAAIzC,IAAI,CAACuC,cAAc,CAAChT,SAAS,CAAC,CAACmT,YAAY,EAAE;IACrE,MAAMC,YAAY,GAAG,IAAI3C,IAAI,CAACwC,eAAe,CAACjT,SAAS,CAAC,CAACmT,YAAY,EAAE;IAEvE,OAAOD,WAAW,KAAKE,YAAY;EACrC;EAEAtT,mBAAmBA,CAACE,SAAc;IAChC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,MAAMsU,IAAI,GAAG,IAAI7D,IAAI,CAACzQ,SAAS,CAAC;IAChC,MAAM+U,KAAK,GAAG,IAAItE,IAAI,EAAE;IACxB,MAAMuE,SAAS,GAAG,IAAIvE,IAAI,CAACsE,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIZ,IAAI,CAACnB,YAAY,EAAE,KAAK4B,KAAK,CAAC5B,YAAY,EAAE,EAAE,OAAO,aAAa;IACtE,IAAImB,IAAI,CAACnB,YAAY,EAAE,KAAK6B,SAAS,CAAC7B,YAAY,EAAE,EAAE,OAAO,MAAM;IAEnE,OAAOmB,IAAI,CAACa,kBAAkB,CAAC,OAAO,EAAE;MACtC6E,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAnW,cAAcA,CAAC8J,OAAY,EAAEI,KAAU;IACrC;IACAjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE4B,OAAO,CAAC;EAC1C;EAEA3J,oBAAoBA,CAAC2J,OAAY,EAAEI,KAAU;IAC3CA,KAAK,CAACuH,cAAc,EAAE;IACtB;IACAxJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4B,OAAO,CAAC;EAC/C;EAEA/I,gBAAgBA,CAACgO,KAAa;IAC5B,IAAIA,KAAK,KAAK,IAAI,CAACpN,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IACnD,MAAM0N,cAAc,GAAG,IAAI,CAACrN,QAAQ,CAACoN,KAAK,CAAC;IAC3C,MAAMM,WAAW,GAAG,IAAI,CAAC1N,QAAQ,CAACoN,KAAK,GAAG,CAAC,CAAC;IAE5C,OAAO,CAACM,WAAW,IAAIL,cAAc,CAACtS,MAAM,EAAEpB,EAAE,KAAK+T,WAAW,CAAC3S,MAAM,EAAEpB,EAAE;EAC7E;EAEAmB,eAAeA,CAAC8S,MAAc;IAC5B;IACAtH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqH,MAAM,CAAC;EAC3C;EAEAvO,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC2E,YAAY,EAAEyD,YAAY,EAAE9H,MAAM,GAAG,CAAC;EACpD;EAEAL,oBAAoBA,CAAC8N,KAAa;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5B,MAAMC,cAAc,GAAG,IAAI,CAACrN,QAAQ,CAACoN,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAACtN,QAAQ,CAACoN,KAAK,GAAG,CAAC,CAAC;IAEhD,OAAOC,cAAc,CAACtS,MAAM,EAAEpB,EAAE,KAAK2T,eAAe,CAACvS,MAAM,EAAEpB,EAAE;EACjE;EAEA0B,YAAYA,CAACuS,MAAc;IACzB;IACA,MAAMC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAM4G,IAAI,GAAG7G,MAAM,CAAC8G,KAAK,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC5CD,CAAC,GAAG,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGC,CAAC,CAAC/G,UAAU,CAAC,CAAC,CAAC;MAClC,OAAO8G,CAAC,GAAGA,CAAC;IACd,CAAC,EAAE,CAAC,CAAC;IACL,OAAO/G,MAAM,CAACpC,IAAI,CAACqJ,GAAG,CAACL,IAAI,CAAC,GAAG5G,MAAM,CAAClO,MAAM,CAAC;EAC/C;EAEAJ,cAAcA,CAAC4I,OAAY;IACzB,IAAIA,OAAO,CAACO,IAAI,EAAE;MAChB,IAAIP,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MAC3D,IAAIxE,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MAC3D,IAAIxE,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MAC3D,OAAO,MAAM;;IAEf,OAAO,MAAM;EACf;EAEApR,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IACvB;IACA,OAAOA,OAAO,CAACkU,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;EACvC;EAEAlQ,QAAQA,CAAC2I,OAAY;IACnB,OAAOA,OAAO,CAACO,IAAI,IAAIP,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC;EAChE;EAEA7Q,eAAeA,CAACqM,OAAY;IAC1B;IACA7B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4B,OAAO,CAAC;EAC5C;EAEAjM,WAAWA,CAACiM,OAAY;IACtB,OAAOA,OAAO,CAACO,IAAI,EAAEqE,GAAG,IAAI5E,OAAO,CAACO,IAAI,EAAEqM,IAAI,IAAI,EAAE;EACtD;EAEAtV,OAAOA,CAAC0I,OAAY;IAClB,OAAOA,OAAO,CAACO,IAAI,IAAI,CAACP,OAAO,CAACO,IAAI,CAACiC,IAAI,EAAEgC,UAAU,CAAC,QAAQ,CAAC;EACjE;EAEArQ,YAAYA,CAAC6L,OAAY;IACvB,IAAIA,OAAO,CAACO,IAAI,EAAEqE,GAAG,EAAE;MACrB,MAAMiI,IAAI,GAAG5I,QAAQ,CAACiC,aAAa,CAAC,GAAG,CAAC;MACxC2G,IAAI,CAACC,IAAI,GAAG9M,OAAO,CAACO,IAAI,CAACqE,GAAG;MAC5BiI,IAAI,CAACE,QAAQ,GAAG/M,OAAO,CAACO,IAAI,CAACtG,IAAI,IAAI,MAAM;MAC3C4S,IAAI,CAACxG,KAAK,EAAE;;EAEhB;EAEA/R,WAAWA,CAAC0L,OAAY;IACtB,IAAI,CAACA,OAAO,CAACO,IAAI,EAAEiC,IAAI,EAAE,OAAO,aAAa;IAE7C,IAAIxC,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,cAAc;IACjE,IAAIxE,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACgC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,cAAc;IACjE,IAAIxE,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACwC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IAC/D,IAAIhF,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACwC,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACjE,IAAIhF,OAAO,CAACO,IAAI,CAACiC,IAAI,CAACwC,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IAEnE,OAAO,aAAa;EACtB;EAEAzQ,WAAWA,CAACyL,OAAY;IACtB,OAAOA,OAAO,CAACO,IAAI,EAAEtG,IAAI,IAAI,SAAS;EACxC;EAEAzF,WAAWA,CAACwL,OAAY;IACtB,IAAI,CAACA,OAAO,CAACO,IAAI,EAAEmB,IAAI,EAAE,OAAO,EAAE;IAClC,OAAO,IAAI,CAAC0J,cAAc,CAACpL,OAAO,CAACO,IAAI,CAACmB,IAAI,CAAC;EAC/C;EAEAnK,iBAAiBA,CAACrF,SAAc;IAC9B,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,OAAO,IAAIyQ,IAAI,CAACzQ,SAAS,CAAC,CAAC4U,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACA7R,cAAcA,CAAC2Q,SAAiB,EAAE1Q,KAAa;IAC7C;IACA+I,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE0H,SAAS,EAAE1Q,KAAK,CAAC;EACnD;EAEAG,cAAcA,CAACwQ,QAAa,EAAEN,MAAc;IAC1C,OAAOM,QAAQ,CAACC,KAAK,EAAEhB,QAAQ,CAACS,MAAM,CAAC,IAAI,KAAK;EAClD;EAEAmC,aAAaA,CAACxH,KAAU;IACtB;IACA,MAAMR,KAAK,GAAGQ,KAAK,CAACE,MAAM,CAACV,KAAK;IAChC,IAAIA,KAAK,CAACC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC9H,QAAQ,EAAE;MAClC,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;KACD,MAAM,IAAI,CAAC6H,KAAK,CAACC,IAAI,EAAE,IAAI,IAAI,CAAC9H,QAAQ,EAAE;MACzC,IAAI,CAACA,QAAQ,GAAG,KAAK;MACrB;;EAEJ;;EAEA8P,cAAcA,CAACzH,KAAU;IACvB,IAAIA,KAAK,CAAC0H,GAAG,KAAK,OAAO,IAAI,CAAC1H,KAAK,CAAC2H,QAAQ,EAAE;MAC5C3H,KAAK,CAACuH,cAAc,EAAE;MACtB,IAAI,CAAC5O,WAAW,EAAE;;EAEtB;EAEAiP,YAAYA,CAAA;IACV;IACA,IAAI,CAACgF,kBAAkB,EAAE;EAC3B;EAEA/E,WAAWA,CAAA;IACT;IACA,IAAI,CAAClQ,QAAQ,GAAG,KAAK;EACvB;EAEQiV,kBAAkBA,CAAA;IACxB;IACA7O,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAEAnD,UAAUA,CAAA;IACR;IACAkD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;EAC5B;EAEA;EACA6O,WAAWA,CAACvO,KAAU,EAAEwO,OAAe;IACrC/O,OAAO,CAACO,KAAK,CAAC,eAAewO,OAAO,GAAG,EAAExO,KAAK,CAAC;IAC/C,IAAIsB,OAAO,GAAG,sCAAsC;IAEpD,IAAItB,KAAK,EAAEsB,OAAO,EAAE;MAClBA,OAAO,GAAGtB,KAAK,CAACsB,OAAO;KACxB,MAAM,IAAI,OAAOtB,KAAK,KAAK,QAAQ,EAAE;MACpCsB,OAAO,GAAGtB,KAAK;;IAGjB,IAAI,CAAC/C,YAAY,CAACoD,SAAS,CAACiB,OAAO,CAAC;EACtC;EAEA;EACAmN,WAAWA,CAAA;IACT,IAAI,IAAI,CAACpQ,SAAS,EAAE;MAClBoF,aAAa,CAAC,IAAI,CAACpF,SAAS,CAAC;;IAG/B,IAAI,IAAI,CAACJ,cAAc,EAAE;MACvBwF,aAAa,CAAC,IAAI,CAACxF,cAAc,CAAC;;IAGpC,IAAI,IAAI,CAACU,aAAa,EAAE;MACtB+P,YAAY,CAAC,IAAI,CAAC/P,aAAa,CAAC;;IAGlC,IAAI,IAAI,CAACZ,aAAa,IAAI,IAAI,CAACA,aAAa,CAACqF,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACrF,aAAa,CAACsF,IAAI,EAAE;MACzB,IAAI,CAACtF,aAAa,CAACmE,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGxE,IAAI,CAACzE,aAAa,CAAC+P,WAAW,EAAE;EAClC;;;uBAj4CW/R,oBAAoB,EAAAxK,EAAA,CAAAwc,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1c,EAAA,CAAAwc,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5c,EAAA,CAAAwc,iBAAA,CAAAK,EAAA,CAAAjS,cAAA,GAAA5K,EAAA,CAAAwc,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAA/c,EAAA,CAAAwc,iBAAA,CAAAxc,EAAA,CAAAgd,iBAAA;IAAA;EAAA;;;YAApBxS,oBAAoB;MAAAyS,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCjBjCpd,EAAA,CAAAE,cAAA,aAEC;UAOKF,EAAA,CAAAqB,UAAA,mBAAAic,sDAAA;YAAA,OAASD,GAAA,CAAA3G,qBAAA,EAAuB;UAAA,EAAC;UAGjC1W,EAAA,CAAAC,SAAA,WAAkE;UACpED,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAA8C;UAMxCF,EAAA,CAAAqB,UAAA,mBAAAkc,mDAAA;YAAA,OAASF,GAAA,CAAAxb,eAAA,CAAAwb,GAAA,CAAA7c,gBAAA,kBAAA6c,GAAA,CAAA7c,gBAAA,CAAAE,EAAA,CAAsC;UAAA,EAAC;UAJlDV,EAAA,CAAAI,YAAA,EAKE;UACFJ,EAAA,CAAA8C,UAAA,IAAA0a,mCAAA,iBAGO;UACTxd,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,aAA4B;UAExBF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAELJ,EAAA,CAAA8C,UAAA,KAAA2a,oCAAA,kBAEM;UACNzd,EAAA,CAAA8C,UAAA,KAAA4a,oCAAA,kBAEM;UACN1d,EAAA,CAAAE,cAAA,eAAsD;UACpDF,EAAA,CAAA8C,UAAA,KAAA6a,oCAAA,kBAkBM;UACN3d,EAAA,CAAA8C,UAAA,KAAA8a,qCAAA,mBAMO;UACT5d,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAAqC;UAEjCF,EAAA,CAAAqB,UAAA,mBAAAwc,uDAAA;YAAA,OAASR,GAAA,CAAA3F,cAAA,EAAgB;UAAA,EAAC;UAI1B1X,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAqB,UAAA,mBAAAyc,uDAAA;YAAA,OAAST,GAAA,CAAA1F,cAAA,EAAgB;UAAA,EAAC;UAI1B3X,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAMC;UALCF,EAAA,CAAAqB,UAAA,mBAAA0c,uDAAA;YAAA,OAASV,GAAA,CAAA7H,YAAA,EAAc;UAAA,EAAC;UAMxBxV,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAqB,UAAA,mBAAA2c,uDAAA;YAAA,OAASX,GAAA,CAAA5H,cAAA,EAAgB;UAAA,EAAC;UAI1BzV,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAE,cAAA,oBAIC;UADCF,EAAA,CAAAqB,UAAA,oBAAA4c,sDAAAhZ,MAAA;YAAA,OAAUoY,GAAA,CAAAjG,QAAA,CAAAnS,MAAA,CAAgB;UAAA,EAAC;UAG3BjF,EAAA,CAAA8C,UAAA,KAAAob,oCAAA,kBAUM;UAGNle,EAAA,CAAA8C,UAAA,KAAAqb,oCAAA,kBAaM;UAGNne,EAAA,CAAA8C,UAAA,KAAAsb,oCAAA,kBAyMM;UACRpe,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAqB,UAAA,sBAAAgd,wDAAA;YAAA,OAAYhB,GAAA,CAAApV,WAAA,EAAa;UAAA,EAAC;UAI1BjI,EAAA,CAAAE,cAAA,eAAwB;UAGpBF,EAAA,CAAAqB,UAAA,mBAAAid,uDAAA;YAAA,OAASjB,GAAA,CAAA/J,iBAAA,EAAmB;UAAA,EAAC;UAM7BtT,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAOC;UALCF,EAAA,CAAAqB,UAAA,mBAAAkd,uDAAA;YAAA,OAASlB,GAAA,CAAA9J,oBAAA,EAAsB;UAAA,EAAC;UAMhCvT,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAoB;UAOhBF,EAAA,CAAAqB,UAAA,mBAAAmd,yDAAAvZ,MAAA;YAAA,OAASoY,GAAA,CAAAvG,aAAA,CAAA7R,MAAA,CAAqB;UAAA,EAAC,qBAAAwZ,2DAAAxZ,MAAA;YAAA,OACpBoY,GAAA,CAAAtG,cAAA,CAAA9R,MAAA,CAAsB;UAAA,EADF,mBAAAyZ,yDAAA;YAAA,OAEtBrB,GAAA,CAAAnG,YAAA,EAAc;UAAA,EAFQ,kBAAAyH,wDAAA;YAAA,OAGvBtB,GAAA,CAAAlG,WAAA,EAAa;UAAA,EAHU;UASjCnX,EAAA,CAAAG,MAAA;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAIbJ,EAAA,CAAAE,cAAA,eAAwB;UAEtBF,EAAA,CAAA8C,UAAA,KAAA8b,uCAAA,qBAaS;UAGT5e,EAAA,CAAA8C,UAAA,KAAA+b,uCAAA,qBAUS;UACX7e,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAA8C,UAAA,KAAAgc,oCAAA,kBA6BM;UAGN9e,EAAA,CAAA8C,UAAA,KAAAic,oCAAA,mBA4DM;UAGN/e,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAqB,UAAA,oBAAA2d,uDAAA/Z,MAAA;YAAA,OAAUoY,GAAA,CAAAhO,cAAA,CAAApK,MAAA,CAAsB;UAAA,EAAC;UAJnCjF,EAAA,CAAAI,YAAA,EAOE;UAGFJ,EAAA,CAAA8C,UAAA,KAAAmc,oCAAA,kBAIO;UACTjf,EAAA,CAAAI,YAAA,EAAM;;;;;UA9gBIJ,EAAA,CAAAK,SAAA,GAAqE;UAArEL,EAAA,CAAA+B,UAAA,SAAAsb,GAAA,CAAA7c,gBAAA,kBAAA6c,GAAA,CAAA7c,gBAAA,CAAAwB,KAAA,yCAAAhC,EAAA,CAAAiC,aAAA,CAAqE,QAAAob,GAAA,CAAA7c,gBAAA,kBAAA6c,GAAA,CAAA7c,gBAAA,CAAAC,QAAA;UAMpET,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAA+B,UAAA,SAAAsb,GAAA,CAAA7c,gBAAA,kBAAA6c,GAAA,CAAA7c,gBAAA,CAAAK,QAAA,CAAgC;UAOjCb,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAW,kBAAA,OAAA0c,GAAA,CAAA7c,gBAAA,kBAAA6c,GAAA,CAAA7c,gBAAA,CAAAC,QAAA,wBACF;UAEmCT,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAA+B,UAAA,UAAAsb,GAAA,CAAA7c,gBAAA,CAAuB;UAGtBR,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAA+B,UAAA,SAAAsb,GAAA,CAAA7c,gBAAA,CAAsB;UAKrDR,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAA+B,UAAA,SAAAsb,GAAA,CAAAhR,YAAA,CAAkB;UAkBdrM,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAA+B,UAAA,UAAAsb,GAAA,CAAAhR,YAAA,CAAmB;UA8B5BrM,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAAuE,WAAA,iBAAA8Y,GAAA,CAAA/Q,UAAA,CAAiC,mBAAA+Q,GAAA,CAAA/Q,UAAA;UAwBlCtM,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAA+B,UAAA,SAAAsb,GAAA,CAAApS,SAAA,CAAe;UAafjL,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAA+B,UAAA,UAAAsb,GAAA,CAAApS,SAAA,IAAAoS,GAAA,CAAAtW,QAAA,CAAAL,MAAA,OAAyC;UAetC1G,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAA+B,UAAA,UAAAsb,GAAA,CAAApS,SAAA,IAAAoS,GAAA,CAAAtW,QAAA,CAAAL,MAAA,KAAuC;UAiN3C1G,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAA+B,UAAA,cAAAsb,GAAA,CAAA5Q,WAAA,CAAyB;UAUrBzM,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAuE,WAAA,iBAAA8Y,GAAA,CAAAjS,eAAA,CAAsC,mBAAAiS,GAAA,CAAAjS,eAAA;UAUtCpL,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAuE,WAAA,iBAAA8Y,GAAA,CAAAhS,kBAAA,CAAyC,mBAAAgS,GAAA,CAAAhS,kBAAA;UAezCrL,EAAA,CAAAK,SAAA,GAAsE;UAAtEL,EAAA,CAAA+B,UAAA,cAAAsb,GAAA,CAAA7c,gBAAA,IAAA6c,GAAA,CAAAxV,gBAAA,IAAAwV,GAAA,CAAAhV,gBAAA,CAAsE;UAiBrErI,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAA+B,UAAA,YAAAmd,QAAA,GAAA7B,GAAA,CAAA5Q,WAAA,CAAAuB,GAAA,8BAAAkR,QAAA,CAAApQ,KAAA,kBAAAoQ,QAAA,CAAApQ,KAAA,CAAAC,IAAA,IAAgD;UAgBhD/O,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAA+B,UAAA,UAAAod,QAAA,GAAA9B,GAAA,CAAA5Q,WAAA,CAAAuB,GAAA,8BAAAmR,QAAA,CAAArQ,KAAA,kBAAAqQ,QAAA,CAAArQ,KAAA,CAAAC,IAAA,GAA+C;UAgBrD/O,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAA+B,UAAA,SAAAsb,GAAA,CAAAjS,eAAA,CAAqB;UAgCrBpL,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAA+B,UAAA,SAAAsb,GAAA,CAAAhS,kBAAA,CAAwB;UAmEzBrL,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAA+B,UAAA,WAAAsb,GAAA,CAAArC,kBAAA,GAA+B;UAM9Bhb,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAA+B,UAAA,SAAAsb,GAAA,CAAAjS,eAAA,IAAAiS,GAAA,CAAAhS,kBAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}