/* ========== STYLES ULTRA-AVANCÉS POUR MESSAGE-CHAT ========== */

/* Import du CSS ultra-avancé pour toutes les fonctionnalités */
@import './message-chat-ultra-advanced.component.scss';

/* ========== STYLES SPÉCIFIQUES AU COMPOSANT ========== */

/* Container principal optimisé */
.message-chat-container {
  @extend .whatsapp-chat-container;
  position: relative;
  overflow: hidden;
}

/* En-tête du chat */
.chat-header {
  @extend .whatsapp-header;
}

.header-info {
  @extend .whatsapp-header-info;
}

.header-avatar {
  @extend .whatsapp-header-avatar;
}

.avatar-img {
  @extend .whatsapp-avatar;
}

.online-indicator {
  @extend .whatsapp-online-indicator;
}

.header-details {
  @extend .whatsapp-header-details;
}

.header-name {
  @extend .whatsapp-header-name;
}

.header-status {
  @extend .whatsapp-header-status;
}

.typing-indicator {
  @extend .whatsapp-typing-indicator;
}

.typing-dot {
  @extend .whatsapp-typing-dot;
}

.header-actions {
  @extend .whatsapp-header-actions;
}

.header-action {
  @extend .whatsapp-header-action;
}

.action-badge {
  @extend .whatsapp-action-badge;
}

/* Barre de recherche */
.search-container {
  @extend .whatsapp-search-container;
}

.search-input-wrapper {
  @extend .whatsapp-search-input-wrapper;
}

.search-input {
  @extend .whatsapp-search-input;
}

.search-actions {
  @extend .whatsapp-search-actions;
}

.search-btn {
  @extend .whatsapp-search-btn;
}

.search-results {
  @extend .whatsapp-search-results;
}

.search-result {
  @extend .whatsapp-search-result;
}

.search-highlight {
  @extend .whatsapp-search-highlight;
}

/* Messages épinglés */
.pinned-messages {
  @extend .whatsapp-pinned-messages;
}

.pinned-icon {
  @extend .whatsapp-pinned-icon;
}

.pinned-content {
  @extend .whatsapp-pinned-content;
}

.pinned-text {
  @extend .whatsapp-pinned-text;
}

.pinned-count {
  @extend .whatsapp-pinned-count;
}

.pinned-actions {
  @extend .whatsapp-pinned-actions;
}

.pinned-btn {
  @extend .whatsapp-pinned-btn;
}

/* Zone de messages */
.messages-container {
  @extend .whatsapp-messages-container;
}

/* État vide */
.empty-state {
  @extend .whatsapp-empty-state;
}

.empty-content {
  @extend .whatsapp-empty-content;
}

.empty-icon {
  @extend .whatsapp-empty-icon;
}

.empty-title {
  @extend .whatsapp-empty-title;
}

.empty-description {
  @extend .whatsapp-empty-description;
}

.empty-suggestions {
  @extend .whatsapp-empty-suggestions;
}

.quick-message-btn {
  @extend .whatsapp-quick-message-btn;
}

/* Chargement */
.loading-state {
  @extend .whatsapp-loading-state;
}

.loading-content {
  @extend .whatsapp-loading-content;
}

.loading-spinner {
  @extend .whatsapp-loading-spinner;
}

.loading-text {
  @extend .whatsapp-loading-text;
}

/* Bouton charger plus */
.load-more-container {
  @extend .whatsapp-load-more-container;
}

.load-more-btn {
  @extend .whatsapp-load-more-btn;
}

/* Séparateurs de date */
.date-separator {
  @extend .whatsapp-date-separator;
}

.date-separator-content {
  @extend .whatsapp-date-separator-content;
}

/* Messages système */
.system-message {
  @extend .whatsapp-system-message;
}

.system-content {
  @extend .whatsapp-system-content;
}

/* Wrapper de message */
.message-wrapper {
  @extend .whatsapp-message-wrapper;
}

.message-wrapper.own-message {
  @extend .whatsapp-message-wrapper.own-message;
}

.message-wrapper.other-message {
  @extend .whatsapp-message-wrapper.other-message;
}

.message-wrapper.highlighted {
  @extend .whatsapp-message-wrapper.highlighted;
}

.message-wrapper.selected {
  @extend .whatsapp-message-wrapper.selected;
}

.message-wrapper.search-result {
  @extend .whatsapp-message-wrapper.search-result;
}

.message-wrapper.pinned {
  @extend .whatsapp-message-wrapper.pinned;
}

.message-wrapper.forwarded {
  @extend .whatsapp-message-wrapper.forwarded;
}

/* Checkbox de sélection */
.message-checkbox {
  @extend .whatsapp-message-checkbox;
}

.checkbox {
  @extend .whatsapp-checkbox;
}

/* Avatar de message */
.message-avatar {
  @extend .whatsapp-message-avatar;
}

.avatar-img {
  @extend .whatsapp-avatar-img;
}

/* Contenu de message */
.message-content {
  @extend .whatsapp-message-content;
}

/* Nom de l'expéditeur */
.sender-name {
  @extend .whatsapp-sender-name;
}

/* Indicateur de transfert */
.forwarded-indicator {
  @extend .whatsapp-forwarded-indicator;
}

/* Message de réponse */
.reply-message {
  @extend .whatsapp-reply-message;
}

.reply-border {
  @extend .whatsapp-reply-border;
}

.reply-content {
  @extend .whatsapp-reply-content;
}

.reply-sender {
  @extend .whatsapp-reply-sender;
}

.reply-text {
  @extend .whatsapp-reply-text;
}

.reply-media {
  @extend .whatsapp-reply-media;
}

/* Bulle de message */
.message-bubble {
  @extend .whatsapp-message-bubble;
}

/* Mode édition */
.edit-mode {
  @extend .whatsapp-edit-mode;
}

.edit-input-container {
  @extend .whatsapp-edit-input-container;
}

.edit-input {
  @extend .whatsapp-edit-input;
}

.edit-char-count {
  @extend .whatsapp-edit-char-count;
}

.edit-actions {
  @extend .whatsapp-edit-actions;
}

.edit-cancel {
  @extend .whatsapp-edit-cancel;
}

.edit-save {
  @extend .whatsapp-edit-save;
}

/* Contenu texte */
.text-content {
  @extend .whatsapp-text-content;
}

.text-body {
  @extend .whatsapp-text-body;
}

/* Contenu image */
.image-content {
  @extend .whatsapp-image-content;
}

.image-container {
  @extend .whatsapp-image-container;
}

.message-image {
  @extend .whatsapp-message-image;
}

.image-loading {
  @extend .whatsapp-image-loading;
}

.image-spinner {
  @extend .whatsapp-image-spinner;
}

.image-overlay {
  @extend .whatsapp-image-overlay;
}

.image-info {
  @extend .whatsapp-image-info;
}

.image-caption {
  @extend .whatsapp-image-caption;
}

/* Contenu vocal */
.voice-content {
  @extend .whatsapp-voice-content;
}

.voice-player {
  @extend .whatsapp-voice-player;
}

.voice-play-btn {
  @extend .whatsapp-voice-play-btn;
}

.voice-waveform {
  @extend .whatsapp-voice-waveform;
}

.voice-bar {
  @extend .whatsapp-voice-bar;
}

.voice-info {
  @extend .whatsapp-voice-info;
}

.voice-duration {
  @extend .whatsapp-voice-duration;
}

.voice-speed-btn {
  @extend .whatsapp-voice-speed-btn;
}

/* Contenu fichier */
.file-content {
  @extend .whatsapp-file-content;
}

.file-container {
  @extend .whatsapp-file-container;
}

.file-icon {
  @extend .whatsapp-file-icon;
}

.file-info {
  @extend .whatsapp-file-info;
}

.file-name {
  @extend .whatsapp-file-name;
}

.file-details {
  @extend .whatsapp-file-details;
}

.file-actions {
  @extend .whatsapp-file-actions;
}

.file-download {
  @extend .whatsapp-file-download;
}

.file-preview {
  @extend .whatsapp-file-preview;
}

.file-progress {
  @extend .whatsapp-file-progress;
}

.progress-bar {
  @extend .whatsapp-progress-bar;
}

.progress-fill {
  @extend .whatsapp-progress-fill;
}

.progress-text {
  @extend .whatsapp-progress-text;
}

/* Contenu vidéo */
.video-content {
  @extend .whatsapp-video-content;
}

.video-container {
  @extend .whatsapp-video-container;
}

.message-video {
  @extend .whatsapp-message-video;
}

.video-overlay {
  @extend .whatsapp-video-overlay;
}

.video-play-btn {
  @extend .whatsapp-video-play-btn;
}

.video-info {
  @extend .whatsapp-video-info;
}

.video-caption {
  @extend .whatsapp-video-caption;
}

/* Contenu localisation */
.location-content {
  @extend .whatsapp-location-content;
}

.location-container {
  @extend .whatsapp-location-container;
}

.location-map {
  @extend .whatsapp-location-map;
}

.location-image {
  @extend .whatsapp-location-image;
}

.location-overlay {
  @extend .whatsapp-location-overlay;
}

.location-info {
  @extend .whatsapp-location-info;
}

.location-name {
  @extend .whatsapp-location-name;
}

.location-address {
  @extend .whatsapp-location-address;
}

/* Contenu contact */
.contact-content {
  @extend .whatsapp-contact-content;
}

.contact-container {
  @extend .whatsapp-contact-container;
}

.contact-avatar {
  @extend .whatsapp-contact-avatar;
}

.contact-info {
  @extend .whatsapp-contact-info;
}

.contact-name {
  @extend .whatsapp-contact-name;
}

.contact-phone {
  @extend .whatsapp-contact-phone;
}

.contact-actions {
  @extend .whatsapp-contact-actions;
}

.contact-add {
  @extend .whatsapp-contact-add;
}
