{"ast": null, "code": "import { BehaviorSubject, Observable, of, throwError, retry, EMP<PERSON> } from 'rxjs';\nimport { map, catchError, tap, filter, switchMap } from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport { MessageType, CallType, CallStatus } from '../models/message.model';\nimport { GET_CONVERSATIONS_QUERY, GET_NOTIFICATIONS_QUERY, NOTIFICATION_SUBSCRIPTION, GET_CONVERSATION_QUERY, SEND_MESSAGE_MUTATION, MARK_AS_READ_MUTATION, MESSAGE_SENT_SUBSCRIPTION, USER_STATUS_SUBSCRIPTION, GET_USER_QUERY, GET_ALL_USER_QUERY, CONVERSATION_UPDATED_SUBSCRIPTION, SEARCH_MESSAGES_QUERY, GET_UNREAD_MESSAGES_QUERY, SET_USER_ONLINE_MUTATION, SET_USER_OFFLINE_MUTATION, START_TYPING_MUTATION, STOP_TYPING_MUTATION, TYPING_INDICATOR_SUBSCRIPTION, GET_CURRENT_USER_QUERY, REACT_TO_MESSAGE_MUTATION, FORWARD_MESSAGE_MUTATION, PIN_MESSAGE_MUTATION, CREATE_GROUP_MUTATION, UPDATE_GROUP_MUTATION, DELETE_GROUP_MUTATION, LEAVE_GROUP_MUTATION, GET_GROUP_QUERY, GET_USER_GROUPS_QUERY, EDIT_MESSAGE_MUTATION, DELETE_MESSAGE_MUTATION, GET_MESSAGES_QUERY, GET_NOTIFICATIONS_ATTACHAMENTS, MARK_NOTIFICATION_READ_MUTATION, NOTIFICATIONS_READ_SUBSCRIPTION, CREATE_CONVERSATION_MUTATION, DELETE_NOTIFICATION_MUTATION, DELETE_MULTIPLE_NOTIFICATIONS_MUTATION, DELETE_ALL_NOTIFICATIONS_MUTATION,\n// Requêtes et mutations pour les appels\nCALL_HISTORY_QUERY, CALL_DETAILS_QUERY, CALL_STATS_QUERY, INITIATE_CALL_MUTATION, SEND_CALL_SIGNAL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, CALL_SIGNAL_SUBSCRIPTION, INCOMING_CALL_SUBSCRIPTION, GET_VOICE_MESSAGES_QUERY } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class MessageService {\n  constructor(apollo, logger, zone) {\n    this.apollo = apollo;\n    this.logger = logger;\n    this.zone = zone;\n    // État partagé\n    this.activeConversation = new BehaviorSubject(null);\n    this.notifications = new BehaviorSubject([]);\n    this.notificationCache = new Map();\n    this.notificationCount = new BehaviorSubject(0);\n    this.onlineUsers = new Map();\n    this.subscriptions = [];\n    this.CACHE_DURATION = 300000;\n    this.lastFetchTime = 0;\n    // Propriétés pour les appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    this.callSignals = new BehaviorSubject(null);\n    this.localStream = null;\n    this.remoteStream = null;\n    this.peerConnection = null;\n    // Observables publics pour les appels\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    this.callSignals$ = this.callSignals.asObservable();\n    this.localStream$ = new BehaviorSubject(null);\n    this.remoteStream$ = new BehaviorSubject(null);\n    // Configuration WebRTC\n    this.rtcConfig = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    this.usersCache = [];\n    // Pagination metadata for user list\n    this.currentUserPagination = {\n      totalCount: 0,\n      totalPages: 0,\n      currentPage: 1,\n      hasNextPage: false,\n      hasPreviousPage: false\n    };\n    // Observables publics\n    this.activeConversation$ = this.activeConversation.asObservable();\n    this.notifications$ = this.notifications.asObservable();\n    this.notificationCount$ = this.notificationCount.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // --------------------------------------------------------------------------\n    // Section 2: Méthodes pour les Notifications\n    // --------------------------------------------------------------------------\n    // Propriétés pour la pagination des notifications\n    this.notificationPagination = {\n      currentPage: 1,\n      limit: 10,\n      hasMoreNotifications: true\n    };\n    this.toSafeISOString = date => {\n      if (!date) return undefined;\n      return typeof date === 'string' ? date : date.toISOString();\n    };\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  loadNotificationsFromLocalStorage() {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications);\n        this.notificationCache.clear();\n        notifications.forEach(notification => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  initSubscriptions() {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n    });\n    this.subscribeToUserStatus();\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    return this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.incomingCall) {\n        return null;\n      }\n      // Gérer l'appel entrant\n      this.handleIncomingCall(data.incomingCall);\n      return data.incomingCall;\n    }), catchError(error => {\n      this.logger.error('Error in incoming call subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      return;\n    }\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(error => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted) {\n    this.muted = muted;\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted() {\n    return this.muted;\n  }\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound() {\n    console.log('MessageService: Tentative de lecture du son de notification');\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n    // Utiliser l'API Web Audio pour générer un son de notification simple\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Créer un oscillateur pour générer un son\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      // Configurer l'oscillateur\n      oscillator.type = 'sine';\n      oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // La note A5\n      // Configurer le volume\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.5, audioContext.currentTime + 0.01);\n      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);\n      // Connecter les nœuds\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      // Démarrer et arrêter l'oscillateur\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n      console.log('MessageService: Son de notification généré avec succès');\n    } catch (error) {\n      console.error('MessageService: Erreur lors de la génération du son:', error);\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 1.0; // Volume maximum\n        audio.play().catch(err => {\n          console.error('MessageService: Erreur lors de la lecture du fichier son:', err);\n        });\n      } catch (audioError) {\n        console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);\n      }\n    }\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n  /**\n   * Envoie un message vocal à un utilisateur\n   * @param receiverId ID de l'utilisateur destinataire\n   * @param audioBlob Blob audio à envoyer\n   * @param conversationId ID de la conversation (optionnel)\n   * @param duration Durée de l'enregistrement en secondes (optionnel)\n   * @returns Observable avec le message envoyé\n   */\n  sendVoiceMessage(receiverId, audioBlob, conversationId, duration) {\n    this.logger.debug(`[MessageService] Sending voice message to user ${receiverId}, duration: ${duration}s`);\n    // Vérifier que le blob audio est valide\n    if (!audioBlob || audioBlob.size === 0) {\n      this.logger.error('[MessageService] Invalid audio blob');\n      return throwError(() => new Error('Invalid audio blob'));\n    }\n    // Créer un fichier à partir du blob audio avec un nom unique\n    const timestamp = Date.now();\n    const audioFile = new File([audioBlob], `voice-message-${timestamp}.webm`, {\n      type: 'audio/webm',\n      lastModified: timestamp\n    });\n    // Vérifier que le fichier a été créé correctement\n    if (!audioFile || audioFile.size === 0) {\n      this.logger.error('[MessageService] Failed to create audio file');\n      return throwError(() => new Error('Failed to create audio file'));\n    }\n    this.logger.debug(`[MessageService] Created audio file: ${audioFile.name}, size: ${audioFile.size} bytes`);\n    // Créer des métadonnées pour le message vocal\n    const metadata = {\n      duration: duration || 0,\n      isVoiceMessage: true,\n      timestamp: timestamp\n    };\n    // Utiliser la méthode sendMessage avec le type VOICE_MESSAGE\n    // Utiliser une chaîne vide comme contenu pour éviter les problèmes de validation\n    return this.sendMessage(receiverId, ' ',\n    // Espace comme contenu minimal pour passer la validation\n    audioFile, MessageType.VOICE_MESSAGE, conversationId, undefined, metadata);\n  }\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl) {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n      audio.onended = () => {\n        resolve();\n      };\n      audio.onerror = error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n      audio.play().catch(error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages() {\n    this.logger.debug('[MessageService] Getting voice messages');\n    return this.apollo.watchQuery({\n      query: GET_VOICE_MESSAGES_QUERY,\n      fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête\n    }).valueChanges.pipe(map(result => {\n      const voiceMessages = result.data?.getVoiceMessages || [];\n      this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);\n      return voiceMessages;\n    }), catchError(error => {\n      this.logger.error('[MessageService] Error fetching voice messages:', error);\n      return throwError(() => new Error('Failed to fetch voice messages'));\n    }));\n  }\n  // Message methods\n  getMessages(senderId, receiverId, conversationId, page = 1, limit = 10) {\n    return this.apollo.watchQuery({\n      query: GET_MESSAGES_QUERY,\n      variables: {\n        senderId,\n        receiverId,\n        conversationId,\n        limit,\n        page\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const messages = result.data?.getMessages || [];\n      return messages.map(msg => this.normalizeMessage(msg));\n    }), catchError(error => {\n      this.logger.error('Error fetching messages:', error);\n      return throwError(() => new Error('Failed to fetch messages'));\n    }));\n  }\n  editMessage(messageId, newContent) {\n    return this.apollo.mutate({\n      mutation: EDIT_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        newContent\n      }\n    }).pipe(map(result => {\n      if (!result.data?.editMessage) {\n        throw new Error('Failed to edit message');\n      }\n      return this.normalizeMessage(result.data.editMessage);\n    }), catchError(error => {\n      this.logger.error('Error editing message:', error);\n      return throwError(() => new Error('Failed to edit message'));\n    }));\n  }\n  deleteMessage(messageId) {\n    return this.apollo.mutate({\n      mutation: DELETE_MESSAGE_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.deleteMessage) {\n        throw new Error('Failed to delete message');\n      }\n      return this.normalizeMessage(result.data.deleteMessage);\n    }), catchError(error => {\n      this.logger.error('Error deleting message:', error);\n      return throwError(() => new Error('Failed to delete message'));\n    }));\n  }\n  sendMessage(receiverId, content, file, messageType = MessageType.TEXT, conversationId, replyTo, metadata) {\n    this.logger.info(`[MessageService] Sending message to: ${receiverId}, hasFile: ${!!file}`);\n    this.logger.debug(`[MessageService] Message content: \"${content?.substring(0, 50)}${content?.length > 50 ? '...' : ''}\"`);\n    // Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    this.logger.debug(`[MessageService] Authentication check before sending message: token=${!!token}`);\n    // Utiliser le type de message fourni ou le déterminer automatiquement\n    let finalMessageType = messageType;\n    // Si le type n'est pas explicitement fourni et qu'il y a un fichier, déterminer le type\n    if (file) {\n      // Si le type est déjà VOICE_MESSAGE, le conserver\n      if (messageType === MessageType.VOICE_MESSAGE) {\n        finalMessageType = MessageType.VOICE_MESSAGE;\n        this.logger.debug(`[MessageService] Using explicit VOICE_MESSAGE type`);\n      }\n      // Sinon, déterminer le type en fonction du type de fichier\n      else if (messageType === MessageType.TEXT) {\n        if (file.type.startsWith('image/')) {\n          finalMessageType = MessageType.IMAGE;\n        } else if (file.type.startsWith('video/')) {\n          finalMessageType = MessageType.VIDEO;\n        } else if (file.type.startsWith('audio/')) {\n          // Vérifier si c'est un message vocal basé sur les métadonnées\n          if (metadata && metadata.isVoiceMessage) {\n            finalMessageType = MessageType.VOICE_MESSAGE;\n          } else {\n            finalMessageType = MessageType.AUDIO;\n          }\n        } else {\n          finalMessageType = MessageType.FILE;\n        }\n      }\n    }\n    this.logger.debug(`[MessageService] Message type determined: ${finalMessageType}`);\n    // Ajouter le type de message aux variables\n    // Utiliser directement la valeur de l'énumération sans conversion\n    const variables = {\n      receiverId,\n      content,\n      type: finalMessageType // Ajouter explicitement le type de message\n    };\n    // Forcer le type à être une valeur d'énumération GraphQL\n    // Cela empêche Apollo de convertir la valeur en minuscules\n    if (variables.type) {\n      Object.defineProperty(variables, 'type', {\n        value: finalMessageType,\n        enumerable: true,\n        writable: false\n      });\n    }\n    // Ajouter les métadonnées si elles sont fournies\n    if (metadata) {\n      variables.metadata = metadata;\n      this.logger.debug(`[MessageService] Metadata attached:`, metadata);\n    }\n    if (file) {\n      variables.file = file;\n      this.logger.debug(`[MessageService] File attached: ${file.name}, size: ${file.size}, type: ${file.type}, messageType: ${finalMessageType}`);\n    }\n    if (conversationId) {\n      variables.conversationId = conversationId;\n      this.logger.debug(`[MessageService] Using existing conversation: ${conversationId}`);\n    }\n    if (replyTo) {\n      variables.replyTo = replyTo;\n      this.logger.debug(`[MessageService] Replying to message: ${replyTo}`);\n    }\n    const context = file ? {\n      useMultipart: true,\n      file\n    } : undefined;\n    this.logger.debug(`[MessageService] Sending GraphQL mutation with variables:`, variables);\n    return this.apollo.mutate({\n      mutation: SEND_MESSAGE_MUTATION,\n      variables,\n      context\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] ⚡ INSTANT: Message send response received`);\n      if (!result.data?.sendMessage) {\n        this.logger.error(`[MessageService] ❌ Failed to send message: No data returned`);\n        throw new Error('Failed to send message');\n      }\n      try {\n        this.logger.debug(`[MessageService] 🚀 INSTANT: Normalizing sent message`, result.data.sendMessage);\n        const normalizedMessage = this.normalizeMessage(result.data.sendMessage);\n        this.logger.info(`[MessageService] ✅ INSTANT: Message sent successfully: ${normalizedMessage.id}`);\n        // OPTIMISATION: Mise à jour immédiate de l'UI\n        this.zone.run(() => {\n          // Émettre immédiatement le message dans le flux\n          this.logger.debug('📡 INSTANT: Updating UI immediately');\n        });\n        return normalizedMessage;\n      } catch (normalizationError) {\n        this.logger.error(`[MessageService] ❌ Error normalizing message:`, normalizationError);\n        // Retourner un message minimal mais valide plutôt que de lancer une erreur\n        const minimalMessage = {\n          id: result.data.sendMessage.id || 'temp-' + Date.now(),\n          content: result.data.sendMessage.content || '',\n          type: result.data.sendMessage.type || MessageType.TEXT,\n          timestamp: new Date(),\n          isRead: false,\n          sender: {\n            id: this.getCurrentUserId(),\n            username: 'You'\n          }\n        };\n        this.logger.info(`[MessageService] Returning minimal message: ${minimalMessage.id}`);\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error sending message:`, error);\n      return throwError(() => new Error('Failed to send message'));\n    }));\n  }\n  markMessageAsRead(messageId) {\n    return this.apollo.mutate({\n      mutation: MARK_AS_READ_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');\n      return {\n        ...result.data.markMessageAsRead,\n        readAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error marking message as read:', error);\n      return throwError(() => new Error('Failed to mark message as read'));\n    }));\n  }\n  reactToMessage(messageId, emoji) {\n    return this.apollo.mutate({\n      mutation: REACT_TO_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        emoji\n      }\n    }).pipe(map(result => {\n      if (!result.data?.reactToMessage) throw new Error('Failed to react to message');\n      return result.data.reactToMessage;\n    }), catchError(error => {\n      console.error('Error reacting to message:', error);\n      return throwError(() => new Error('Failed to react to message'));\n    }));\n  }\n  forwardMessage(messageId, conversationIds) {\n    return this.apollo.mutate({\n      mutation: FORWARD_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationIds\n      }\n    }).pipe(map(result => {\n      if (!result.data?.forwardMessage) throw new Error('Failed to forward message');\n      return result.data.forwardMessage.map(msg => ({\n        ...msg,\n        timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()\n      }));\n    }), catchError(error => {\n      console.error('Error forwarding message:', error);\n      return throwError(() => new Error('Failed to forward message'));\n    }));\n  }\n  pinMessage(messageId, conversationId) {\n    return this.apollo.mutate({\n      mutation: PIN_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.pinMessage) throw new Error('Failed to pin message');\n      return {\n        ...result.data.pinMessage,\n        pinnedAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error pinning message:', error);\n      return throwError(() => new Error('Failed to pin message'));\n    }));\n  }\n  searchMessages(query, conversationId, filters = {}) {\n    return this.apollo.watchQuery({\n      query: SEARCH_MESSAGES_QUERY,\n      variables: {\n        query,\n        conversationId,\n        ...filters,\n        dateFrom: this.toSafeISOString(filters.dateFrom),\n        dateTo: this.toSafeISOString(filters.dateTo)\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.searchMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error searching messages:', error);\n      return throwError(() => new Error('Failed to search messages'));\n    }));\n  }\n  getUnreadMessages(userId) {\n    return this.apollo.watchQuery({\n      query: GET_UNREAD_MESSAGES_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.getUnreadMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error fetching unread messages:', error);\n      return throwError(() => new Error('Failed to fetch unread messages'));\n    }));\n  }\n  setActiveConversation(conversationId) {\n    this.activeConversation.next(conversationId);\n  }\n  getConversations() {\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATIONS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const conversations = result.data?.getConversations || [];\n      return conversations.map(conv => this.normalizeConversation(conv));\n    }), catchError(error => {\n      console.error('Error fetching conversations:', error);\n      return throwError(() => new Error('Failed to load conversations'));\n    }));\n  }\n  getConversation(conversationId, limit, page) {\n    this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);\n    const variables = {\n      conversationId\n    };\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATION_QUERY,\n      variables: variables,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation response received:`, result);\n      const conv = result.data?.getConversation;\n      if (!conv) {\n        this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);\n        throw new Error('Conversation not found');\n      }\n      this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);\n      const normalizedConversation = this.normalizeConversation(conv);\n      this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);\n      return normalizedConversation;\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error fetching conversation:`, error);\n      return throwError(() => new Error('Failed to load conversation'));\n    }));\n  }\n  createConversation(userId) {\n    this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to create a conversation'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_CONVERSATION_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation creation response:`, result);\n      const conversation = result.data?.createConversation;\n      if (!conversation) {\n        this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);\n        throw new Error('Failed to create conversation');\n      }\n      try {\n        const normalizedConversation = this.normalizeConversation(conversation);\n        this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);\n        return normalizedConversation;\n      } catch (error) {\n        this.logger.error(`[MessageService] Error normalizing created conversation:`, error);\n        throw new Error('Error processing created conversation');\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);\n      return throwError(() => new Error(`Failed to create conversation: ${error.message}`));\n    }));\n  }\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId) {\n    this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to get/create a conversation'));\n    }\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(map(conversations => {\n      // Récupérer l'ID de l'utilisateur actuel\n      const currentUserId = this.getCurrentUserId();\n      // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n      const existingConversation = conversations.find(conv => {\n        if (conv.isGroup) return false;\n        // Vérifier si la conversation contient les deux utilisateurs\n        const participantIds = conv.participants?.map(p => p.id || p._id) || [];\n        return participantIds.includes(userId) && participantIds.includes(currentUserId);\n      });\n      if (existingConversation) {\n        this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);\n        return existingConversation;\n      }\n      // Si aucune conversation n'est trouvée, en créer une nouvelle\n      throw new Error('No existing conversation found');\n    }), catchError(error => {\n      this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);\n      return this.createConversation(userId);\n    }));\n  }\n  getNotifications(refresh = false, page = 1, limit = 10) {\n    this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY\n    });\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug('MessageService', 'Resetting pagination due to refresh');\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);\n    return this.apollo.watchQuery({\n      query: GET_NOTIFICATIONS_QUERY,\n      variables: {\n        page: page,\n        limit: limit\n      },\n      fetchPolicy: refresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Notifications response received');\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      const notifications = result.data?.getUserNotifications || [];\n      this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);\n      // Vérifier s'il y a plus de notifications à charger\n      this.notificationPagination.hasMoreNotifications = notifications.length >= limit;\n      if (notifications.length === 0) {\n        this.logger.info('MessageService', 'No notifications received from server');\n        this.notificationPagination.hasMoreNotifications = false;\n      }\n      // Filtrer les notifications supprimées\n      const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));\n      this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);\n      // Afficher les notifications reçues pour le débogage\n      filteredNotifications.forEach((notif, index) => {\n        console.log(`Notification ${index + 1} (page ${page}):`, {\n          id: notif.id || notif._id,\n          type: notif.type,\n          content: notif.content,\n          isRead: notif.isRead\n        });\n      });\n      // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n      // Mettre à jour le cache avec les nouvelles notifications\n      this.updateCache(filteredNotifications);\n      // Récupérer toutes les notifications du cache et les TRIER\n      const cachedNotifications = Array.from(this.notificationCache.values());\n      // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n      const sortedNotifications = this.sortNotificationsByDate(cachedNotifications);\n      console.log(`📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`);\n      // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n      this.notifications.next(sortedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage\n      this.saveNotificationsToLocalStorage();\n      return cachedNotifications;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error loading notifications:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      return throwError(() => new Error('Failed to load notifications'));\n    }));\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIds = new Set();\n      const savedNotifications = localStorage.getItem('notifications');\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications = this.apollo.client.readQuery({\n        query: GET_NOTIFICATIONS_QUERY\n      })?.getUserNotifications || [];\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach(notification => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n      return deletedIds;\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);\n      return new Set();\n    }\n  }\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications() {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications() {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(false, nextPage, this.notificationPagination.limit);\n  }\n  getNotificationById(id) {\n    return this.notifications$.pipe(map(notifications => notifications.find(n => n.id === id)), catchError(error => {\n      this.logger.error('Error finding notification:', error);\n      return throwError(() => new Error('Failed to find notification'));\n    }));\n  }\n  getNotificationCount() {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId) {\n    return this.apollo.query({\n      query: GET_NOTIFICATIONS_ATTACHAMENTS,\n      variables: {\n        id: notificationId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => result.data?.getNotificationAttachments || []), catchError(error => {\n      this.logger.error('Error fetching notification attachments:', error);\n      return throwError(() => new Error('Failed to fetch attachments'));\n    }));\n  }\n  getUnreadNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications.filter(n => !n.isRead)));\n  }\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(notificationId) {\n    this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n    // Appeler le backend pour supprimer la notification\n    return this.apollo.mutate({\n      mutation: DELETE_NOTIFICATION_MUTATION,\n      variables: {\n        notificationId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteNotification;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de la notification', {\n      success: true,\n      message: 'Notification supprimée localement (erreur serveur)'\n    })));\n  }\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  saveNotificationsToLocalStorage() {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug('MessageService', 'Notifications sauvegardées localement');\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);\n    }\n  }\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications() {\n    this.logger.debug('MessageService', 'Suppression de toutes les notifications');\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_ALL_NOTIFICATIONS_MUTATION\n    }).pipe(map(result => {\n      const response = result.data?.deleteAllNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de toutes les notifications', {\n      success: true,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(notificationIds) {\n    this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n      variables: {\n        notificationIds\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteMultipleNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression multiple de notifications', {\n      success: count > 0,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  groupNotificationsByType() {\n    return this.notifications$.pipe(map(notifications => {\n      const groups = new Map();\n      notifications.forEach(notif => {\n        if (!groups.has(notif.type)) {\n          groups.set(notif.type, []);\n        }\n        groups.get(notif.type)?.push(notif);\n      });\n      return groups;\n    }));\n  }\n  markAsRead(notificationIds) {\n    this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value\n      });\n    }\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n    this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      }\n    };\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n    return this.apollo.mutate({\n      mutation: MARK_NOTIFICATION_READ_MUTATION,\n      variables: {\n        notificationIds: validIds\n      },\n      optimisticResponse: optimisticResponse,\n      errorPolicy: 'all',\n      fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation\n    }).pipe(map(result => {\n      this.logger.debug('MessageService', 'Mutation result', result);\n      console.log('Mutation result:', result);\n      // Si nous avons des erreurs GraphQL, les logger mais continuer\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        console.error('GraphQL errors:', result.errors);\n      }\n      // Utiliser la réponse du serveur ou notre réponse optimiste\n      const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error marking notifications as read:', error);\n      console.error('Error in markAsRead:', error);\n      // En cas d'erreur, retourner quand même un succès simulé\n      // puisque nous avons déjà mis à jour l'interface utilisateur\n      return of({\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      });\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels\n  // --------------------------------------------------------------------------\n  /**\n   * Initie un appel avec un autre utilisateur\n   * @param recipientId ID de l'utilisateur à appeler\n   * @param callType Type d'appel (audio, vidéo)\n   * @param conversationId ID de la conversation (optionnel)\n   * @param options Options d'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  initiateCall(recipientId, callType, conversationId, options) {\n    return this.setupMediaDevices(callType).pipe(switchMap(stream => {\n      this.localStream = stream;\n      this.localStream$.next(stream);\n      // Créer une connexion peer\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      // Ajouter les pistes audio/vidéo\n      stream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, stream);\n      });\n      // Écouter les candidats ICE\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.sendCallSignal(this.generateCallId(), 'ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      // Écouter les pistes distantes\n      this.peerConnection.ontrack = event => {\n        if (!this.remoteStream) {\n          this.remoteStream = new MediaStream();\n          this.remoteStream$.next(this.remoteStream);\n        }\n        event.streams[0].getTracks().forEach(track => {\n          this.remoteStream.addTrack(track);\n        });\n      };\n      // Créer l'offre SDP\n      return from(this.peerConnection.createOffer()).pipe(switchMap(offer => {\n        return from(this.peerConnection.setLocalDescription(offer)).pipe(map(() => offer));\n      }));\n    }), switchMap(offer => {\n      // Générer un ID d'appel unique\n      const callId = this.generateCallId();\n      // Envoyer l'offre au serveur\n      return this.apollo.mutate({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType,\n          callId,\n          offer: JSON.stringify(offer),\n          conversationId,\n          options\n        }\n      }).pipe(map(result => {\n        const call = result.data?.initiateCall;\n        if (!call) {\n          throw new Error('Failed to initiate call');\n        }\n        // Mettre à jour l'état de l'appel actif\n        this.activeCall.next(call);\n        // S'abonner aux signaux d'appel\n        const signalSub = this.subscribeToCallSignals(call.id).subscribe();\n        this.subscriptions.push(signalSub);\n        return call;\n      }));\n    }), catchError(error => {\n      this.logger.error('Error initiating call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to initiate call'));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   * @param incomingCall Appel entrant à accepter\n   * @returns Observable avec les informations de l'appel\n   */\n  acceptCall(incomingCall) {\n    this.stop('ringtone');\n    return this.setupMediaDevices(incomingCall.type).pipe(switchMap(stream => {\n      this.localStream = stream;\n      this.localStream$.next(stream);\n      // Créer une connexion peer\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      // Ajouter les pistes audio/vidéo\n      stream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, stream);\n      });\n      // Écouter les candidats ICE\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.sendCallSignal(incomingCall.id, 'ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      // Écouter les pistes distantes\n      this.peerConnection.ontrack = event => {\n        if (!this.remoteStream) {\n          this.remoteStream = new MediaStream();\n          this.remoteStream$.next(this.remoteStream);\n        }\n        event.streams[0].getTracks().forEach(track => {\n          this.remoteStream.addTrack(track);\n        });\n      };\n      // Définir l'offre distante\n      const offer = JSON.parse(incomingCall.offer);\n      return from(this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer))).pipe(switchMap(() => from(this.peerConnection.createAnswer())), switchMap(answer => {\n        return from(this.peerConnection.setLocalDescription(answer)).pipe(map(() => answer));\n      }));\n    }), switchMap(answer => {\n      // Envoyer la réponse au serveur\n      return this.apollo.mutate({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer: JSON.stringify(answer)\n        }\n      }).pipe(map(result => {\n        const call = result.data?.acceptCall;\n        if (!call) {\n          throw new Error('Failed to accept call');\n        }\n        // Jouer le son de connexion\n        this.play('call-connected');\n        // Mettre à jour l'état de l'appel actif\n        this.activeCall.next({\n          ...call,\n          caller: incomingCall.caller,\n          type: incomingCall.type,\n          conversationId: incomingCall.conversationId\n        });\n        // S'abonner aux signaux d'appel\n        const signalSub = this.subscribeToCallSignals(incomingCall.id).subscribe();\n        this.subscriptions.push(signalSub);\n        // Effacer l'appel entrant\n        this.incomingCall.next(null);\n        return call;\n      }));\n    }), catchError(error => {\n      this.logger.error('Error accepting call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to accept call'));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   * @param callId ID de l'appel à rejeter\n   * @param reason Raison du rejet (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  rejectCall(callId, reason) {\n    this.stop('ringtone');\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason\n      }\n    }).pipe(map(result => {\n      const call = result.data?.rejectCall;\n      if (!call) {\n        throw new Error('Failed to reject call');\n      }\n      // Effacer l'appel entrant\n      this.incomingCall.next(null);\n      return call;\n    }), catchError(error => {\n      this.logger.error('Error rejecting call', error);\n      return throwError(() => new Error('Failed to reject call'));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   * @param callId ID de l'appel à terminer\n   * @param feedback Commentaires sur l'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  endCall(callId, feedback) {\n    this.stop('ringtone');\n    this.play('call-end');\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback\n      }\n    }).pipe(map(result => {\n      const call = result.data?.endCall;\n      if (!call) {\n        throw new Error('Failed to end call');\n      }\n      // Nettoyer les ressources\n      this.cleanupCall();\n      // Mettre à jour l'état de l'appel actif\n      this.activeCall.next(null);\n      return call;\n    }), catchError(error => {\n      this.logger.error('Error ending call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to end call'));\n    }));\n  }\n  /**\n   * Active ou désactive la caméra ou le micro\n   * @param callId ID de l'appel\n   * @param video État de la caméra (optionnel)\n   * @param audio État du micro (optionnel)\n   * @returns Observable avec le résultat de l'opération\n   */\n  toggleMedia(callId, video, audio) {\n    if (this.localStream) {\n      // Mettre à jour les pistes locales\n      if (video !== undefined) {\n        this.localStream.getVideoTracks().forEach(track => {\n          track.enabled = video;\n        });\n      }\n      if (audio !== undefined) {\n        this.localStream.getAudioTracks().forEach(track => {\n          track.enabled = audio;\n        });\n      }\n    }\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video,\n        audio\n      }\n    }).pipe(map(result => {\n      const success = result.data?.toggleCallMedia;\n      if (!success) {\n        throw new Error('Failed to toggle media');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error toggling media', error);\n      return throwError(() => new Error('Failed to toggle media'));\n    }));\n  }\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId) {\n    return this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      variables: {\n        callId\n      }\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.callSignal) {\n        throw new Error('No call signal received');\n      }\n      return data.callSignal;\n    }), tap(signal => {\n      this.callSignals.next(signal);\n      this.handleCallSignal(signal);\n    }), catchError(error => {\n      this.logger.error('Error in call signal subscription', error);\n      return throwError(() => new Error('Call signal subscription failed'));\n    }));\n  }\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(callId, signalType, signalData) {\n    return this.apollo.mutate({\n      mutation: SEND_CALL_SIGNAL_MUTATION,\n      variables: {\n        callId,\n        signalType,\n        signalData\n      }\n    }).pipe(map(result => {\n      const success = result.data?.sendCallSignal;\n      if (!success) {\n        throw new Error('Failed to send call signal');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error sending call signal', error);\n      return throwError(() => new Error('Failed to send call signal'));\n    }));\n  }\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(limit = 20, offset = 0, status, type, startDate, endDate) {\n    return this.apollo.watchQuery({\n      query: CALL_HISTORY_QUERY,\n      variables: {\n        limit,\n        offset,\n        status,\n        type,\n        startDate,\n        endDate\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const history = result.data?.callHistory || [];\n      this.logger.debug(`Retrieved ${history.length} call history items`);\n      return history;\n    }), catchError(error => {\n      this.logger.error('Error fetching call history:', error);\n      return throwError(() => new Error('Failed to fetch call history'));\n    }));\n  }\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId) {\n    return this.apollo.watchQuery({\n      query: CALL_DETAILS_QUERY,\n      variables: {\n        callId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const details = result.data?.callDetails;\n      if (!details) {\n        throw new Error('Call details not found');\n      }\n      this.logger.debug(`Retrieved call details for: ${callId}`);\n      return details;\n    }), catchError(error => {\n      this.logger.error('Error fetching call details:', error);\n      return throwError(() => new Error('Failed to fetch call details'));\n    }));\n  }\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats() {\n    return this.apollo.watchQuery({\n      query: CALL_STATS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const stats = result.data?.callStats;\n      if (!stats) {\n        throw new Error('Call stats not found');\n      }\n      this.logger.debug('Retrieved call stats:', stats);\n      return stats;\n    }), catchError(error => {\n      this.logger.error('Error fetching call stats:', error);\n      return throwError(() => new Error('Failed to fetch call stats'));\n    }));\n  }\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  handleCallSignal(signal) {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  handleIceCandidate(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {\n        this.logger.error('Error adding ICE candidate', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error);\n    }\n  }\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  handleAnswer(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {\n        this.logger.error('Error setting remote description', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error);\n    }\n  }\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  handleEndCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  handleRejectCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Nettoie les ressources d'appel\n   */\n  cleanupCall() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  setupMediaDevices(callType) {\n    const constraints = {\n      audio: true,\n      video: callType !== CallType.AUDIO ? {\n        width: {\n          ideal: 1280\n        },\n        height: {\n          ideal: 720\n        }\n      } : false\n    };\n    return new Observable(observer => {\n      navigator.mediaDevices.getUserMedia(constraints).then(stream => {\n        observer.next(stream);\n        observer.complete();\n      }).catch(error => {\n        this.logger.error('Error accessing media devices', error);\n        observer.error(new Error('Failed to access media devices'));\n      });\n    });\n  }\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  generateCallId() {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {\n    this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);\n    const now = Date.now();\n    const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);\n      return of([...this.usersCache]);\n    }\n    this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);\n    return this.apollo.watchQuery({\n      query: GET_ALL_USER_QUERY,\n      variables: {\n        search,\n        page,\n        limit,\n        sortBy,\n        sortOrder,\n        isOnline: isOnline !== undefined ? isOnline : null\n      },\n      fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Users response received', result);\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      if (!result.data?.getAllUsers) {\n        this.logger.warn('MessageService', 'No users data received from server');\n        return [];\n      }\n      const paginatedResponse = result.data.getAllUsers;\n      // Log pagination metadata\n      this.logger.debug('MessageService', 'Pagination metadata:', {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      });\n      // Normalize users with error handling\n      const users = [];\n      for (const user of paginatedResponse.users) {\n        try {\n          if (user) {\n            users.push(this.normalizeUser(user));\n          }\n        } catch (error) {\n          this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);\n        }\n      }\n      this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);\n      // Update cache only for first page with no filters\n      if (!search && page === 1 && !isOnline) {\n        this.usersCache = [...users];\n        this.lastFetchTime = Date.now();\n        this.logger.debug('MessageService', `User cache updated with ${users.length} users`);\n      }\n      // Store pagination metadata in a property for component access\n      this.currentUserPagination = {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      };\n      return users;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching users:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      // Return cache if available (only for first page)\n      if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {\n        this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);\n        return of([...this.usersCache]);\n      }\n      return throwError(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));\n    }));\n  }\n  getOneUser(userId) {\n    return this.apollo.watchQuery({\n      query: GET_USER_QUERY,\n      variables: {\n        id: userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getOneUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching user:', error);\n      return throwError(() => new Error('Failed to fetch user'));\n    }));\n  }\n  getCurrentUser() {\n    return this.apollo.watchQuery({\n      query: GET_CURRENT_USER_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getCurrentUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching current user:', error);\n      return throwError(() => new Error('Failed to fetch current user'));\n    }));\n  }\n  setUserOnline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_ONLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOnline) throw new Error('Failed to set user online');\n      return this.normalizeUser(result.data.setUserOnline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user online:', error);\n      return throwError(() => new Error('Failed to set user online'));\n    }));\n  }\n  setUserOffline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_OFFLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');\n      return this.normalizeUser(result.data.setUserOffline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user offline:', error);\n      return throwError(() => new Error('Failed to set user offline'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(name, participantIds, photo, description) {\n    this.logger.debug('MessageService', `Creating group: ${name} with ${participantIds.length} participants`);\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(() => new Error('Nom du groupe et participants requis'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_GROUP_MUTATION,\n      variables: {\n        name,\n        participantIds,\n        photo,\n        description\n      }\n    }).pipe(map(result => {\n      const group = result.data?.createGroup;\n      if (!group) {\n        throw new Error('Échec de la création du groupe');\n      }\n      this.logger.info('MessageService', `Group created successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error creating group:', error);\n      return throwError(() => new Error('Échec de la création du groupe'));\n    }));\n  }\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId, input) {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: UPDATE_GROUP_MUTATION,\n      variables: {\n        id: groupId,\n        input\n      }\n    }).pipe(map(result => {\n      const group = result.data?.updateGroup;\n      if (!group) {\n        throw new Error('Échec de la mise à jour du groupe');\n      }\n      this.logger.info('MessageService', `Group updated successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error updating group:', error);\n      return throwError(() => new Error('Échec de la mise à jour du groupe'));\n    }));\n  }\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(groupId) {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: DELETE_GROUP_MUTATION,\n      variables: {\n        id: groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteGroup;\n      if (!response) {\n        throw new Error('Échec de la suppression du groupe');\n      }\n      this.logger.info('MessageService', `Group deleted successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error deleting group:', error);\n      return throwError(() => new Error('Échec de la suppression du groupe'));\n    }));\n  }\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(groupId) {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: LEAVE_GROUP_MUTATION,\n      variables: {\n        groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.leaveGroup;\n      if (!response) {\n        throw new Error('Échec de la sortie du groupe');\n      }\n      this.logger.info('MessageService', `Left group successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error leaving group:', error);\n      return throwError(() => new Error('Échec de la sortie du groupe'));\n    }));\n  }\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId) {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.query({\n      query: GET_GROUP_QUERY,\n      variables: {\n        id: groupId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const group = result.data?.getGroup;\n      if (!group) {\n        throw new Error('Groupe non trouvé');\n      }\n      this.logger.info('MessageService', `Group retrieved successfully: ${groupId}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting group:', error);\n      return throwError(() => new Error('Échec de la récupération du groupe'));\n    }));\n  }\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId) {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n    return this.apollo.query({\n      query: GET_USER_GROUPS_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const groups = result.data?.getUserGroups || [];\n      this.logger.info('MessageService', `Retrieved ${groups.length} groups for user: ${userId}`);\n      return groups;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting user groups:', error);\n      return throwError(() => new Error('Échec de la récupération des groupes'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  subscribeToNewMessages(conversationId) {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux messages avec un token invalide ou expiré\");\n      return of(null);\n    }\n    this.logger.debug(`🚀 INSTANT MESSAGE: Setting up real-time subscription for conversation: ${conversationId}`);\n    const sub$ = this.apollo.subscribe({\n      query: MESSAGE_SENT_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const msg = result.data?.messageSent;\n      if (!msg) {\n        this.logger.warn('⚠️ No message payload received');\n        throw new Error('No message payload received');\n      }\n      this.logger.debug('⚡ INSTANT: New message received via WebSocket', msg);\n      // Vérifier que l'ID est présent\n      if (!msg.id && !msg._id) {\n        this.logger.warn('⚠️ Message without ID received, generating temp ID');\n        msg.id = `temp-${Date.now()}`;\n      }\n      try {\n        // NORMALISATION RAPIDE du message\n        const normalizedMessage = this.normalizeMessage(msg);\n        this.logger.debug('✅ INSTANT: Message normalized successfully', normalizedMessage);\n        // TRAITEMENT INSTANTANÉ selon le type\n        if (normalizedMessage.type === MessageType.AUDIO || normalizedMessage.type === MessageType.VOICE_MESSAGE || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'audio' || att.type === 'AUDIO')) {\n          this.logger.debug('🎤 INSTANT: Voice message received in real-time');\n        }\n        // MISE À JOUR IMMÉDIATE de l'UI\n        this.zone.run(() => {\n          this.logger.debug('📡 INSTANT: Updating conversation UI immediately');\n          this.updateConversationWithNewMessage(conversationId, normalizedMessage);\n        });\n        return normalizedMessage;\n      } catch (err) {\n        this.logger.error('❌ Error normalizing message:', err);\n        // Créer un message minimal mais valide pour éviter les erreurs\n        const minimalMessage = {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender ? this.normalizeUser(msg.sender) : {\n            id: this.getCurrentUserId(),\n            username: 'Unknown'\n          }\n        };\n        this.logger.debug('🔧 FALLBACK: Created minimal message', minimalMessage);\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Message subscription error:', error);\n      // Retourner un observable vide au lieu de null\n      return EMPTY;\n    }),\n    // Filtrer les valeurs null\n    filter(message => !!message),\n    // Réessayer après un délai en cas d'erreur\n    retry(3));\n    const sub = sub$.subscribe({\n      next: message => {\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: err => {\n        this.logger.error('Error in message subscription:', err);\n      }\n    });\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  updateConversationWithNewMessage(conversationId, message) {\n    this.logger.debug(`⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`);\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: conversation => {\n          this.logger.debug(`✅ BACKGROUND: Conversation ${conversationId} refreshed with ${conversation?.messages?.length || 0} messages`);\n        },\n        error: error => {\n          this.logger.error(`⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`, error);\n        }\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n\n  subscribeToUserStatus() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\");\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n    const sub$ = this.apollo.subscribe({\n      query: USER_STATUS_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement au statut utilisateur:\", result)), map(result => {\n      const user = result.data?.userStatusChanged;\n      if (!user) {\n        this.logger.error('No status payload received');\n        throw new Error('No status payload received');\n      }\n      return this.normalizeUser(user);\n    }), catchError(error => {\n      this.logger.error('Status subscription error:', error);\n      return throwError(() => new Error('Status subscription failed'));\n    }), retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: CONVERSATION_UPDATED_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const conv = result.data?.conversationUpdated;\n      if (!conv) throw new Error('No conversation payload received');\n      const normalizedConversation = {\n        ...conv,\n        participants: conv.participants?.map(p => this.normalizeUser(p)) || [],\n        lastMessage: conv.lastMessage ? {\n          ...conv.lastMessage,\n          sender: this.normalizeUser(conv.lastMessage.sender),\n          timestamp: this.safeDate(conv.lastMessage.timestamp),\n          readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,\n          // Conservez toutes les autres propriétés du message\n          id: conv.lastMessage.id,\n          content: conv.lastMessage.content,\n          type: conv.lastMessage.type,\n          isRead: conv.lastMessage.isRead\n          // ... autres propriétés nécessaires\n        } : null // On conserve null comme dans votre version originale\n      };\n\n      return normalizedConversation; // Assertion de type si nécessaire\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Conversation subscription error:', error);\n      return throwError(() => new Error('Conversation subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: TYPING_INDICATOR_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => result.data?.typingIndicator), filter(Boolean), catchError(error => {\n      this.logger.error('MessageService', 'Typing indicator subscription error:', error);\n      return throwError(() => new Error('Typing indicator subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  isTokenValid() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString()\n        });\n        return false;\n      }\n      return true;\n    } catch (error) {\n      this.logger.error('Erreur lors de la vérification du token:', error);\n      return false;\n    }\n  }\n  subscribeToNotificationsRead() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications avec un token invalide ou expiré\");\n      return of([]);\n    }\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n    const sub$ = this.apollo.subscribe({\n      query: NOTIFICATIONS_READ_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement aux notifications lues:\", result)), map(result => {\n      const notificationIds = result.data?.notificationsRead || [];\n      this.logger.debug('Notifications marquées comme lues:', notificationIds);\n      this.updateNotificationStatus(notificationIds, true);\n      return notificationIds;\n    }), catchError(err => {\n      this.logger.error('Notifications read subscription error:', err);\n      // Retourner un tableau vide au lieu de propager l'erreur\n      return of([]);\n    }),\n    // Réessayer après un délai en cas d'erreur\n    retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications() {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications sans être connecté\");\n      return EMPTY;\n    }\n    this.logger.debug('🚀 INSTANT NOTIFICATION: Setting up real-time subscription');\n    const source$ = this.apollo.subscribe({\n      query: NOTIFICATION_SUBSCRIPTION\n    });\n    const processed$ = source$.pipe(map(result => {\n      const notification = result.data?.notificationReceived;\n      if (!notification) {\n        throw new Error('No notification payload received');\n      }\n      this.logger.debug('⚡ INSTANT: New notification received', notification);\n      const normalized = this.normalizeNotification(notification);\n      // Vérification rapide du cache\n      if (this.notificationCache.has(normalized.id)) {\n        this.logger.debug(`🔄 Notification ${normalized.id} already in cache, skipping`);\n        throw new Error('Notification already exists in cache');\n      }\n      // TRAITEMENT INSTANTANÉ\n      this.logger.debug('📡 INSTANT: Processing notification immediately');\n      // Son de notification IMMÉDIAT\n      this.playNotificationSound();\n      // Mise à jour INSTANTANÉE du cache\n      this.updateNotificationCache(normalized);\n      // Émettre IMMÉDIATEMENT la notification EN PREMIER\n      this.zone.run(() => {\n        // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n        const currentNotifications = this.notifications.value;\n        const updatedNotifications = [normalized, ...currentNotifications];\n        this.logger.debug(`⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`);\n        this.notifications.next(updatedNotifications);\n        this.notificationCount.next(this.notificationCount.value + 1);\n      });\n      this.logger.debug('✅ INSTANT: Notification processed and emitted', normalized);\n      return normalized;\n    }),\n    // Gestion d'erreurs optimisée\n    catchError(err => {\n      if (err instanceof Error && err.message === 'Notification already exists in cache') {\n        return EMPTY;\n      }\n      this.logger.error('❌ Notification subscription error:', err);\n      return EMPTY;\n    }),\n    // Optimisation: traitement en temps réel\n    tap(notification => {\n      this.logger.debug('⚡ INSTANT: Notification ready for UI update', notification);\n    }));\n    const sub = processed$.subscribe({\n      next: notification => {\n        this.logger.debug('✅ INSTANT: Notification delivered to UI', notification);\n      },\n      error: error => {\n        this.logger.error('❌ CRITICAL: Notification subscription error', error);\n      }\n    });\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n  startCleanupInterval() {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  cleanupExpiredNotifications() {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    let expiredCount = 0;\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(this.notificationCache.values());\n      const sortedNotifications = this.sortNotificationsByDate(remainingNotifications);\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  sortNotificationsByDate(notifications) {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  getCurrentUserId() {\n    return localStorage.getItem('userId') || '';\n  }\n  normalizeMessage(message) {\n    if (!message) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined message');\n      throw new Error('Message object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error('[MessageService] Message ID is missing', undefined, message);\n        throw new Error('Message ID is required');\n      }\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;\n      } catch (error) {\n        this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true\n        };\n      }\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true\n          };\n        }\n      }\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments = message.attachments?.map(att => ({\n        id: att.id || att._id || `attachment-${Date.now()}`,\n        url: att.url || '',\n        type: att.type || 'unknown',\n        name: att.name || 'attachment',\n        size: att.size || 0,\n        duration: att.duration || 0\n      })) || [];\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null\n      };\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id\n      });\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);\n      throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeUser(user) {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;\n    const role = user.role || 'user';\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount\n    };\n  }\n  normalizeConversation(conv) {\n    if (!conv) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined conversation');\n      throw new Error('Conversation object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);\n        throw new Error('Conversation ID is required');\n      }\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing participant, skipping', error);\n          }\n        }\n      } else {\n        this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);\n      }\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length\n        });\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug('[MessageService] Successfully normalized message', {\n                messageId: normalizedMessage.id,\n                content: normalizedMessage.content?.substring(0, 20),\n                sender: normalizedMessage.sender?.username\n              });\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);\n          }\n        }\n      } else {\n        this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');\n      }\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing last message, using null', error);\n        }\n      }\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt)\n      };\n      this.logger.debug('[MessageService] Conversation normalized successfully', {\n        conversationId: normalizedConversation.id,\n        participantCount: normalizedParticipants.length,\n        messageCount: normalizedMessages.length\n      });\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);\n      throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  safeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  normalizeNotification(notification) {\n    this.logger.debug('MessageService', 'Normalizing notification', notification);\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || notification._id;\n    if (!notificationId) {\n      this.logger.error('MessageService', 'Notification ID is missing', notification);\n      throw new Error('Notification ID is required');\n    }\n    if (!notification.timestamp) {\n      this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);\n      notification.timestamp = new Date();\n    }\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId,\n        id: notificationId,\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId)\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message)\n        })\n      };\n      this.logger.debug('MessageService', 'Normalized notification result', normalized);\n      return normalized;\n    } catch (error) {\n      this.logger.error('MessageService', 'Error in normalizeNotification', error);\n      throw error;\n    }\n  }\n  normalizeSender(sender) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && {\n        image: sender.image\n      })\n    };\n  }\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  normalizeNotMessage(message) {\n    if (!message) return null;\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && {\n        sender: this.normalizeSender(message.sender)\n      })\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  updateCache(notifications, skipDuplicates = true) {\n    const notificationArray = Array.isArray(notifications) ? notifications : [notifications];\n    this.logger.debug('MessageService', `Updating notification cache with ${notificationArray.length} notifications`);\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(notif => notif && (notif.id || notif._id));\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn('MessageService', `Found ${notificationArray.length - validNotifications.length} notifications without valid IDs`);\n    }\n    let addedCount = 0;\n    let skippedCount = 0;\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || notif._id;\n        if (!notifId) {\n          this.logger.error('MessageService', 'Notification without ID:', notif);\n          return;\n        }\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);\n          skippedCount++;\n          return;\n        }\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n        this.logger.debug('MessageService', `Added notification ${normalized.id} to cache`);\n      } catch (error) {\n        this.logger.error('MessageService', `Error processing notification ${index + 1}:`, error);\n      }\n    });\n    this.logger.debug('MessageService', `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`);\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  refreshNotificationObservables() {\n    const allNotifications = Array.from(this.notificationCache.values());\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n    this.logger.debug(`📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`);\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  updateUnreadCount() {\n    const count = Array.from(this.notificationCache.values()).filter(n => !n.isRead).length;\n    this.notificationCount.next(count);\n  }\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  updateNotificationCache(notification) {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  updateNotificationStatus(ids, isRead) {\n    ids.forEach(id => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  removeNotificationsFromCache(notificationIds) {\n    let removedCount = 0;\n    notificationIds.forEach(id => {\n      if (this.notificationCache.has(id)) {\n        this.notificationCache.delete(id);\n        removedCount++;\n      }\n    });\n    if (removedCount > 0) {\n      this.refreshNotificationObservables();\n    }\n    return removedCount;\n  }\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  handleDeletionError(error, operation, fallbackResponse) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: START_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error starting typing indicator', error);\n      return throwError(() => new Error('Failed to start typing indicator'));\n    }));\n  }\n  stopTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: STOP_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error stopping typing indicator', error);\n      return throwError(() => new Error('Failed to stop typing indicator'));\n    }));\n  }\n  // ========================================\n  // MÉTHODES POUR LES MESSAGES\n  // ========================================\n  /**\n   * Récupère les messages d'une conversation avec pagination\n   * @param conversationId ID de la conversation\n   * @param page Page à récupérer (string)\n   * @param limit Nombre de messages par page (string)\n   * @returns Observable avec les messages\n   */\n  getMessages(conversationId, page = '1', limit = '20') {\n    this.logger.info('MessageService', `Getting messages for conversation ${conversationId}, page ${page}, limit ${limit}`);\n    return this.apollo.watchQuery({\n      query: GET_MESSAGES_QUERY,\n      variables: {\n        conversationId,\n        page: parseInt(page, 10),\n        limit: parseInt(limit, 10)\n      },\n      fetchPolicy: 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Messages response received', result);\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors in getMessages:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      if (!result.data?.getMessages) {\n        this.logger.warn('MessageService', 'No messages data received from server');\n        return [];\n      }\n      const messages = result.data.getMessages;\n      this.logger.debug('MessageService', `Retrieved ${messages.length} messages`);\n      // Normaliser les messages\n      return messages.map(message => this.normalizeMessage(message));\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching messages:', error);\n      return throwError(() => new Error('Failed to fetch messages'));\n    }));\n  }\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp) {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp) {\n    if (!timestamp) return 'Unknown date';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      }\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      }\n      const day = date.toLocaleDateString('fr-FR', {\n        weekday: 'short'\n      }).toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages, index) {\n    if (index === 0) return true;\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n  getDateFromTimestamp(timestamp) {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType) {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';\n    return 'fa-file';\n  }\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType) {\n    if (!mimeType) return 'File';\n    const typeMap = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive'\n    };\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message) {\n    if (!message) return false;\n    // Vérifier le type du message\n    if (message.type === MessageType.VOICE_MESSAGE || message.type === MessageType.VOICE_MESSAGE_LOWER) {\n      return true;\n    }\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');\n      });\n    }\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const voiceAttachment = message.attachments.find(att => {\n      const type = att.type?.toString();\n      return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n    });\n    return voiceAttachment?.url || '';\n  }\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message) {\n    if (!message) return 0;\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n      });\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n    return 0;\n  }\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index) {\n    const pattern = [8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18];\n    return pattern[index % pattern.length];\n  }\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds) {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message) {\n    if (!message) return MessageType.TEXT;\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {\n            return MessageType.FILE;\n          } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {\n            return MessageType.AUDIO;\n          } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {\n            return MessageType.VIDEO;\n          }\n        }\n        return MessageType.FILE;\n      }\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis() {\n    return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];\n  }\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message, currentUserId) {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n    try {\n      const isCurrentUser = message.sender?.id === currentUserId || message.sender?._id === currentUserId || message.senderId === currentUserId;\n      const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n      const messageType = this.getMessageType(message);\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n      if (messageType === MessageType.IMAGE || messageType === MessageType.IMAGE_LOWER) {\n        return `p-1 max-w-xs`;\n      } else if (messageType === MessageType.FILE || messageType === MessageType.FILE_LOWER) {\n        return `${baseClass} p-3`;\n      }\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n  // destroy\n  cleanupSubscriptions() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "of", "throwError", "retry", "EMPTY", "map", "catchError", "tap", "filter", "switchMap", "from", "MessageType", "CallType", "CallStatus", "GET_CONVERSATIONS_QUERY", "GET_NOTIFICATIONS_QUERY", "NOTIFICATION_SUBSCRIPTION", "GET_CONVERSATION_QUERY", "SEND_MESSAGE_MUTATION", "MARK_AS_READ_MUTATION", "MESSAGE_SENT_SUBSCRIPTION", "USER_STATUS_SUBSCRIPTION", "GET_USER_QUERY", "GET_ALL_USER_QUERY", "CONVERSATION_UPDATED_SUBSCRIPTION", "SEARCH_MESSAGES_QUERY", "GET_UNREAD_MESSAGES_QUERY", "SET_USER_ONLINE_MUTATION", "SET_USER_OFFLINE_MUTATION", "START_TYPING_MUTATION", "STOP_TYPING_MUTATION", "TYPING_INDICATOR_SUBSCRIPTION", "GET_CURRENT_USER_QUERY", "REACT_TO_MESSAGE_MUTATION", "FORWARD_MESSAGE_MUTATION", "PIN_MESSAGE_MUTATION", "CREATE_GROUP_MUTATION", "UPDATE_GROUP_MUTATION", "DELETE_GROUP_MUTATION", "LEAVE_GROUP_MUTATION", "GET_GROUP_QUERY", "GET_USER_GROUPS_QUERY", "EDIT_MESSAGE_MUTATION", "DELETE_MESSAGE_MUTATION", "GET_MESSAGES_QUERY", "GET_NOTIFICATIONS_ATTACHAMENTS", "MARK_NOTIFICATION_READ_MUTATION", "NOTIFICATIONS_READ_SUBSCRIPTION", "CREATE_CONVERSATION_MUTATION", "DELETE_NOTIFICATION_MUTATION", "DELETE_MULTIPLE_NOTIFICATIONS_MUTATION", "DELETE_ALL_NOTIFICATIONS_MUTATION", "CALL_HISTORY_QUERY", "CALL_DETAILS_QUERY", "CALL_STATS_QUERY", "INITIATE_CALL_MUTATION", "SEND_CALL_SIGNAL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "CALL_SIGNAL_SUBSCRIPTION", "INCOMING_CALL_SUBSCRIPTION", "GET_VOICE_MESSAGES_QUERY", "MessageService", "constructor", "apollo", "logger", "zone", "activeConversation", "notifications", "notificationCache", "Map", "notificationCount", "onlineUsers", "subscriptions", "CACHE_DURATION", "lastFetchTime", "activeCall", "incomingCall", "callSignals", "localStream", "remoteStream", "peerConnection", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "localStream$", "remoteStream$", "rtcConfig", "iceServers", "urls", "usersCache", "currentUserPagination", "totalCount", "totalPages", "currentPage", "hasNextPage", "hasPreviousPage", "activeConversation$", "notifications$", "notificationCount$", "sounds", "isPlaying", "muted", "notificationPagination", "limit", "hasMoreNotifications", "toSafeISOString", "date", "undefined", "toISOString", "loadNotificationsFromLocalStorage", "initSubscriptions", "startCleanupInterval", "preloadSounds", "savedNotifications", "localStorage", "getItem", "JSON", "parse", "clear", "for<PERSON>ach", "notification", "id", "set", "next", "Array", "values", "updateUnreadCount", "error", "runOutsideAngular", "subscribeToNewNotifications", "subscribe", "subscribeToNotificationsRead", "subscribeToIncomingCalls", "subscribeToUserStatus", "query", "pipe", "data", "handleIncomingCall", "call", "play", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "loop", "sound", "currentTime", "catch", "stop", "pause", "stopAllSounds", "Object", "keys", "setMuted", "isMuted", "playNotificationSound", "console", "log", "audioContext", "window", "AudioContext", "webkitAudioContext", "oscillator", "createOscillator", "gainNode", "createGain", "type", "frequency", "setValueAtTime", "gain", "linearRampToValueAtTime", "connect", "destination", "start", "volume", "err", "audioError", "sendVoiceMessage", "receiverId", "audioBlob", "conversationId", "duration", "debug", "size", "Error", "timestamp", "Date", "now", "audioFile", "File", "lastModified", "metadata", "isVoiceMessage", "sendMessage", "VOICE_MESSAGE", "playAudio", "audioUrl", "Promise", "resolve", "reject", "onended", "onerror", "getVoiceMessages", "watch<PERSON><PERSON>y", "fetchPolicy", "valueChanges", "result", "voiceMessages", "length", "getMessages", "senderId", "page", "variables", "messages", "msg", "normalizeMessage", "editMessage", "messageId", "newContent", "mutate", "mutation", "deleteMessage", "content", "file", "messageType", "TEXT", "replyTo", "info", "substring", "token", "finalMessageType", "startsWith", "IMAGE", "VIDEO", "AUDIO", "FILE", "defineProperty", "value", "enumerable", "writable", "context", "useMultipart", "normalizedMessage", "run", "normalizationError", "minimalMessage", "isRead", "sender", "getCurrentUserId", "username", "markMessageAsRead", "readAt", "reactToMessage", "emoji", "forwardMessage", "conversationIds", "normalizeDate", "pinMessage", "pinnedAt", "searchMessages", "filters", "dateFrom", "dateTo", "safeDate", "normalizeUser", "getUnreadMessages", "userId", "setActiveConversation", "getConversations", "conversations", "conv", "normalizeConversation", "getConversation", "offset", "normalizedConversation", "participants", "createConversation", "conversation", "message", "getOrCreateConversation", "currentUserId", "existingConversation", "find", "isGroup", "participantIds", "p", "_id", "includes", "getNotifications", "refresh", "deletedNotificationIds", "getDeletedNotificationIds", "errors", "e", "join", "getUserNotifications", "filteredNotifications", "notif", "has", "index", "updateCache", "cachedNotifications", "sortedNotifications", "sortNotificationsByDate", "saveNotificationsToLocalStorage", "graphQLErrors", "networkError", "deletedIds", "Set", "savedNotificationIds", "n", "serverNotifications", "client", "readQuery", "add", "loadMoreNotifications", "nextPage", "getNotificationById", "getNotificationCount", "getNotificationAttachments", "notificationId", "getUnreadNotifications", "deleteNotification", "warn", "removedCount", "removeNotificationsFromCache", "response", "handleDeletionError", "success", "setItem", "stringify", "deleteAllNotifications", "count", "allNotificationIds", "deleteMultipleNotifications", "notificationIds", "groupNotificationsByType", "groups", "get", "push", "mark<PERSON><PERSON><PERSON>", "readCount", "remainingCount", "validIds", "trim", "provided", "valid", "updateNotificationStatus", "optimisticResponse", "markNotificationsAsRead", "Math", "max", "errorPolicy", "initiateCall", "recipientId", "callType", "options", "setupMediaDevices", "stream", "RTCPeerConnection", "getTracks", "track", "addTrack", "onicecandidate", "event", "candidate", "sendCallSignal", "generateCallId", "ontrack", "MediaStream", "streams", "createOffer", "offer", "setLocalDescription", "callId", "signalSub", "subscribeToCallSignals", "cleanupCall", "acceptCall", "setRemoteDescription", "RTCSessionDescription", "createAnswer", "answer", "caller", "rejectCall", "reason", "endCall", "feedback", "toggleMedia", "video", "getVideoTracks", "enabled", "getAudioTracks", "toggleCallMedia", "callSignal", "signal", "handleCallSignal", "signalType", "signalData", "getCallHistory", "status", "startDate", "endDate", "history", "callHistory", "getCallDetails", "details", "callDetails", "getCallStats", "stats", "callStats", "handleIceCandidate", "handleAnswer", "handleEndCall", "handleRejectCall", "addIceCandidate", "RTCIceCandidate", "currentCall", "ENDED", "endTime", "REJECTED", "close", "constraints", "width", "ideal", "height", "observer", "navigator", "mediaDevices", "getUserMedia", "then", "complete", "toString", "random", "getAllUsers", "forceRefresh", "search", "sortBy", "sortOrder", "isOnline", "cacheValid", "paginatedResponse", "users", "user", "getOneUser", "getCurrentUser", "setUserOnline", "setUserOffline", "createGroup", "photo", "description", "group", "updateGroup", "groupId", "input", "deleteGroup", "leaveGroup", "getGroup", "getUserGroups", "subscribeToNewMessages", "isTokenValid", "sub$", "messageSent", "attachments", "some", "att", "updateConversationWithNewMessage", "sub", "setTimeout", "userStatusChanged", "subscribeToConversationUpdates", "conversationUpdated", "lastMessage", "subscribeToTypingIndicator", "typingIndicator", "Boolean", "parts", "split", "payload", "atob", "exp", "expirationDate", "expiration", "notificationsRead", "source$", "processed$", "notificationReceived", "normalized", "normalizeNotification", "updateNotificationCache", "currentNotifications", "updatedNotifications", "cleanupInterval", "setInterval", "cleanupExpiredNotifications", "thirtyDaysAgo", "getTime", "expiredCount", "notificationDate", "delete", "remainingNotifications", "sort", "a", "b", "dateA", "dateB", "normalizedSender", "email", "role", "isActive", "normalizedReceiver", "receiver", "normalizedAttachments", "url", "String", "image", "bio", "lastActive", "createdAt", "updatedAt", "followingCount", "followersCount", "postCount", "normalizedParticipants", "isArray", "participant", "normalizedMessages", "normalizedLastMessage", "unreadCount", "participantCount", "messageCount", "normalizeSender", "normalizeNotMessage", "skipDuplicates", "notificationArray", "validNotifications", "addedCount", "skippedCount", "notifId", "refreshNotificationObservables", "allNotifications", "ids", "operation", "fallbackResponse", "startTyping", "stopTyping", "parseInt", "formatMessageTime", "toLocaleTimeString", "hour", "minute", "hour12", "formatLastActive", "lastActiveDate", "diffHours", "abs", "toLocaleDateString", "formatMessageDate", "today", "toDateString", "yesterday", "setDate", "getDate", "day", "weekday", "toUpperCase", "shouldShowDateHeader", "currentMsg", "prevMsg", "currentDate", "getDateFromTimestamp", "prevDate", "getFileIcon", "mimeType", "getFileType", "typeMap", "key", "entries", "hasImage", "attachment", "VOICE_MESSAGE_LOWER", "getVoiceMessageUrl", "voiceAttachment", "getVoiceMessageDuration", "getVoiceBarHeight", "pattern", "formatVoiceDuration", "seconds", "minutes", "floor", "remainingSeconds", "padStart", "getImageUrl", "getMessageType", "msgType", "SYSTEM", "attachmentTypeStr", "getCommonEmojis", "getMessageTypeClass", "isCurrentUser", "baseClass", "IMAGE_LOWER", "FILE_LOWER", "cleanupSubscriptions", "unsubscribe", "clearInterval", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\message.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/core';\nimport { <PERSON> } from 'apollo-angular';\nimport {\n  BehaviorSubject,\n  Observable,\n  of,\n  Subscription,\n  throwError,\n  retry,\n  EMPTY,\n} from 'rxjs';\nimport {\n  map,\n  catchError,\n  tap,\n  filter,\n  switchMap,\n  concatMap,\n  toArray,\n} from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport {\n  MessageType,\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSignal,\n  CallOptions,\n  CallFeedback,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  GET_CONVERSATIONS_QUERY,\n  GET_NOTIFICATIONS_QUERY,\n  NOTIFICATION_SUBSCRIPTION,\n  GET_CONVERSATION_QUERY,\n  SEND_MESSAGE_MUTATION,\n  MARK_AS_READ_MUTATION,\n  MESSAGE_SENT_SUBSCRIPTION,\n  USER_STATUS_SUBSCRIPTION,\n  GET_USER_QUERY,\n  GET_ALL_USER_QUERY,\n  CONVERSATION_UPDATED_SUBSCRIPTION,\n  SEARCH_MESSAGES_QUERY,\n  GET_UNREAD_MESSAGES_QUERY,\n  SET_USER_ONLINE_MUTATION,\n  SET_USER_OFFLINE_MUTATION,\n  START_TYPING_MUTATION,\n  STOP_TYPING_MUTATION,\n  TYPING_INDICATOR_SUBSCRIPTION,\n  GET_CURRENT_USER_QUERY,\n  REACT_TO_MESSAGE_MUTATION,\n  FORWARD_MESSAGE_MUTATION,\n  PIN_MESSAGE_MUTATION,\n  CREATE_GROUP_MUTATION,\n  UPDATE_GROUP_MUTATION,\n  DELETE_GROUP_MUTATION,\n  ADD_GROUP_PARTICIPANTS_MUTATION,\n  REMOVE_GROUP_PARTICIPANTS_MUTATION,\n  LEAVE_GROUP_MUTATION,\n  GET_GROUP_QUERY,\n  GET_USER_GROUPS_QUERY,\n  EDIT_MESSAGE_MUTATION,\n  DELETE_MESSAGE_MUTATION,\n  GET_MESSAGES_QUERY,\n  GET_NOTIFICATIONS_ATTACHAMENTS,\n  MARK_NOTIFICATION_READ_MUTATION,\n  NOTIFICATIONS_READ_SUBSCRIPTION,\n  CREATE_CONVERSATION_MUTATION,\n  DELETE_NOTIFICATION_MUTATION,\n  DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n  DELETE_ALL_NOTIFICATIONS_MUTATION,\n  // Requêtes et mutations pour les appels\n  CALL_HISTORY_QUERY,\n  CALL_DETAILS_QUERY,\n  CALL_STATS_QUERY,\n  INITIATE_CALL_MUTATION,\n  SEND_CALL_SIGNAL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  GET_VOICE_MESSAGES_QUERY,\n} from '../graphql/message.graphql';\nimport {\n  Conversation,\n  Message,\n  Notification,\n  User,\n  Attachment,\n  getNotificationAttachmentsEvent,\n  Group,\n  MessageFilter,\n  TypingIndicatorEvent,\n  GetConversationsResponse,\n  GetConversationResponse,\n  MarkAsReadResponse,\n  ReactToMessageResponse,\n  ForwardMessageResponse,\n  PinMessageResponse,\n  SearchMessagesResponse,\n  SendMessageResponse,\n  GetUnreadMessagesResponse,\n  GetAllUsersResponse,\n  GetOneUserResponse,\n  getCurrentUserResponse,\n  SetUserOnlineResponse,\n  SetUserOfflineResponse,\n  GetGroupResponse,\n  GetUserGroupsResponse,\n  CreateGroupResponse,\n  UpdateGroupResponse,\n  StartTupingResponse,\n  StopTypingResponse,\n  TypingIndicatorEvents,\n  getUserNotificationsResponse,\n  NotificationType,\n  MarkNotificationsAsReadResponse,\n  NotificationReceivedEvent,\n  NotificationsReadEvent,\n} from '../models/message.model';\nimport { LoggerService } from './logger.service';\n@Injectable({\n  providedIn: 'root',\n})\nexport class MessageService implements OnDestroy {\n  // État partagé\n  private activeConversation = new BehaviorSubject<string | null>(null);\n  private notifications = new BehaviorSubject<Notification[]>([]);\n  private notificationCache = new Map<string, Notification>();\n  private cleanupInterval: any;\n  private notificationCount = new BehaviorSubject<number>(0);\n  private onlineUsers = new Map<string, User>();\n  private subscriptions: Subscription[] = [];\n  private readonly CACHE_DURATION = 300000;\n  private lastFetchTime = 0;\n\n  // Propriétés pour les appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private peerConnection: RTCPeerConnection | null = null;\n\n  // Observables publics pour les appels\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n  public localStream$ = new BehaviorSubject<MediaStream | null>(null);\n  public remoteStream$ = new BehaviorSubject<MediaStream | null>(null);\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n  private usersCache: User[] = [];\n\n  // Pagination metadata for user list\n  public currentUserPagination: {\n    totalCount: number;\n    totalPages: number;\n    currentPage: number;\n    hasNextPage: boolean;\n    hasPreviousPage: boolean;\n  } = {\n    totalCount: 0,\n    totalPages: 0,\n    currentPage: 1,\n    hasNextPage: false,\n    hasPreviousPage: false,\n  };\n\n  // Observables publics\n  public activeConversation$ = this.activeConversation.asObservable();\n  public notifications$ = this.notifications.asObservable();\n  public notificationCount$ = this.notificationCount.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  constructor(\n    private apollo: Apollo,\n    private logger: LoggerService,\n    private zone: NgZone\n  ) {\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  private loadNotificationsFromLocalStorage(): void {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications) as Notification[];\n\n        this.notificationCache.clear();\n\n        notifications.forEach((notification) => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  private initSubscriptions(): void {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n    });\n    this.subscribeToUserStatus();\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): Observable<IncomingCall | null> {\n    return this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.incomingCall) {\n            return null;\n          }\n\n          // Gérer l'appel entrant\n          this.handleIncomingCall(data.incomingCall);\n          return data.incomingCall;\n        }),\n        catchError((error) => {\n          this.logger.error('Error in incoming call subscription', error);\n          return of(null);\n        })\n      );\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      return;\n    }\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      sound.loop = loop;\n\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch((error) => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted: boolean): void {\n    this.muted = muted;\n\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted(): boolean {\n    return this.muted;\n  }\n\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound(): void {\n    console.log('MessageService: Tentative de lecture du son de notification');\n\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n\n    // Utiliser l'API Web Audio pour générer un son de notification simple\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // Créer un oscillateur pour générer un son\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      // Configurer l'oscillateur\n      oscillator.type = 'sine';\n      oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // La note A5\n\n      // Configurer le volume\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(\n        0.5,\n        audioContext.currentTime + 0.01\n      );\n      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);\n\n      // Connecter les nœuds\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      // Démarrer et arrêter l'oscillateur\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n\n      console.log('MessageService: Son de notification généré avec succès');\n    } catch (error) {\n      console.error(\n        'MessageService: Erreur lors de la génération du son:',\n        error\n      );\n\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 1.0; // Volume maximum\n        audio.play().catch((err) => {\n          console.error(\n            'MessageService: Erreur lors de la lecture du fichier son:',\n            err\n          );\n        });\n      } catch (audioError) {\n        console.error(\n          'MessageService: Exception lors de la lecture du fichier son:',\n          audioError\n        );\n      }\n    }\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n\n  /**\n   * Envoie un message vocal à un utilisateur\n   * @param receiverId ID de l'utilisateur destinataire\n   * @param audioBlob Blob audio à envoyer\n   * @param conversationId ID de la conversation (optionnel)\n   * @param duration Durée de l'enregistrement en secondes (optionnel)\n   * @returns Observable avec le message envoyé\n   */\n  sendVoiceMessage(\n    receiverId: string,\n    audioBlob: Blob,\n    conversationId?: string,\n    duration?: number\n  ): Observable<Message> {\n    this.logger.debug(\n      `[MessageService] Sending voice message to user ${receiverId}, duration: ${duration}s`\n    );\n\n    // Vérifier que le blob audio est valide\n    if (!audioBlob || audioBlob.size === 0) {\n      this.logger.error('[MessageService] Invalid audio blob');\n      return throwError(() => new Error('Invalid audio blob'));\n    }\n\n    // Créer un fichier à partir du blob audio avec un nom unique\n    const timestamp = Date.now();\n    const audioFile = new File([audioBlob], `voice-message-${timestamp}.webm`, {\n      type: 'audio/webm',\n      lastModified: timestamp,\n    });\n\n    // Vérifier que le fichier a été créé correctement\n    if (!audioFile || audioFile.size === 0) {\n      this.logger.error('[MessageService] Failed to create audio file');\n      return throwError(() => new Error('Failed to create audio file'));\n    }\n\n    this.logger.debug(\n      `[MessageService] Created audio file: ${audioFile.name}, size: ${audioFile.size} bytes`\n    );\n\n    // Créer des métadonnées pour le message vocal\n    const metadata = {\n      duration: duration || 0,\n      isVoiceMessage: true,\n      timestamp: timestamp,\n    };\n\n    // Utiliser la méthode sendMessage avec le type VOICE_MESSAGE\n    // Utiliser une chaîne vide comme contenu pour éviter les problèmes de validation\n    return this.sendMessage(\n      receiverId,\n      ' ', // Espace comme contenu minimal pour passer la validation\n      audioFile,\n      MessageType.VOICE_MESSAGE,\n      conversationId,\n      undefined,\n      metadata\n    );\n  }\n\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n\n      audio.onended = () => {\n        resolve();\n      };\n\n      audio.onerror = (error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n\n      audio.play().catch((error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages(): Observable<Call[]> {\n    this.logger.debug('[MessageService] Getting voice messages');\n\n    return this.apollo\n      .watchQuery<{ getVoiceMessages: Call[] }>({\n        query: GET_VOICE_MESSAGES_QUERY,\n        fetchPolicy: 'network-only', // Ne pas utiliser le cache pour cette requête\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const voiceMessages = result.data?.getVoiceMessages || [];\n          this.logger.debug(\n            `[MessageService] Retrieved ${voiceMessages.length} voice messages`\n          );\n          return voiceMessages;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            '[MessageService] Error fetching voice messages:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch voice messages'));\n        })\n      );\n  }\n  // Message methods\n  getMessages(\n    senderId: string,\n    receiverId: string,\n    conversationId: string,\n    page: number = 1,\n    limit: number = 10\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<{ getMessages: Message[] }>({\n        query: GET_MESSAGES_QUERY,\n        variables: { senderId, receiverId, conversationId, limit, page },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const messages = result.data?.getMessages || [];\n          return messages.map((msg) => this.normalizeMessage(msg));\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching messages:', error);\n          return throwError(() => new Error('Failed to fetch messages'));\n        })\n      );\n  }\n  editMessage(messageId: string, newContent: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ editMessage: Message }>({\n        mutation: EDIT_MESSAGE_MUTATION,\n        variables: { messageId, newContent },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.editMessage) {\n            throw new Error('Failed to edit message');\n          }\n          return this.normalizeMessage(result.data.editMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error editing message:', error);\n          return throwError(() => new Error('Failed to edit message'));\n        })\n      );\n  }\n\n  deleteMessage(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ deleteMessage: Message }>({\n        mutation: DELETE_MESSAGE_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.deleteMessage) {\n            throw new Error('Failed to delete message');\n          }\n          return this.normalizeMessage(result.data.deleteMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error deleting message:', error);\n          return throwError(() => new Error('Failed to delete message'));\n        })\n      );\n  }\n\n  sendMessage(\n    receiverId: string,\n    content: string,\n    file?: File,\n    messageType: MessageType = MessageType.TEXT,\n    conversationId?: string,\n    replyTo?: string,\n    metadata?: any\n  ): Observable<Message> {\n    this.logger.info(\n      `[MessageService] Sending message to: ${receiverId}, hasFile: ${!!file}`\n    );\n    this.logger.debug(\n      `[MessageService] Message content: \"${content?.substring(0, 50)}${\n        content?.length > 50 ? '...' : ''\n      }\"`\n    );\n\n    // Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    this.logger.debug(\n      `[MessageService] Authentication check before sending message: token=${!!token}`\n    );\n\n    // Utiliser le type de message fourni ou le déterminer automatiquement\n    let finalMessageType = messageType;\n\n    // Si le type n'est pas explicitement fourni et qu'il y a un fichier, déterminer le type\n    if (file) {\n      // Si le type est déjà VOICE_MESSAGE, le conserver\n      if (messageType === MessageType.VOICE_MESSAGE) {\n        finalMessageType = MessageType.VOICE_MESSAGE;\n        this.logger.debug(`[MessageService] Using explicit VOICE_MESSAGE type`);\n      }\n      // Sinon, déterminer le type en fonction du type de fichier\n      else if (messageType === MessageType.TEXT) {\n        if (file.type.startsWith('image/')) {\n          finalMessageType = MessageType.IMAGE;\n        } else if (file.type.startsWith('video/')) {\n          finalMessageType = MessageType.VIDEO;\n        } else if (file.type.startsWith('audio/')) {\n          // Vérifier si c'est un message vocal basé sur les métadonnées\n          if (metadata && metadata.isVoiceMessage) {\n            finalMessageType = MessageType.VOICE_MESSAGE;\n          } else {\n            finalMessageType = MessageType.AUDIO;\n          }\n        } else {\n          finalMessageType = MessageType.FILE;\n        }\n      }\n    }\n\n    this.logger.debug(\n      `[MessageService] Message type determined: ${finalMessageType}`\n    );\n\n    // Ajouter le type de message aux variables\n    // Utiliser directement la valeur de l'énumération sans conversion\n    const variables: any = {\n      receiverId,\n      content,\n      type: finalMessageType, // Ajouter explicitement le type de message\n    };\n\n    // Forcer le type à être une valeur d'énumération GraphQL\n    // Cela empêche Apollo de convertir la valeur en minuscules\n    if (variables.type) {\n      Object.defineProperty(variables, 'type', {\n        value: finalMessageType,\n        enumerable: true,\n        writable: false,\n      });\n    }\n\n    // Ajouter les métadonnées si elles sont fournies\n    if (metadata) {\n      variables.metadata = metadata;\n      this.logger.debug(`[MessageService] Metadata attached:`, metadata);\n    }\n\n    if (file) {\n      variables.file = file;\n      this.logger.debug(\n        `[MessageService] File attached: ${file.name}, size: ${file.size}, type: ${file.type}, messageType: ${finalMessageType}`\n      );\n    }\n    if (conversationId) {\n      variables.conversationId = conversationId;\n      this.logger.debug(\n        `[MessageService] Using existing conversation: ${conversationId}`\n      );\n    }\n    if (replyTo) {\n      variables.replyTo = replyTo;\n      this.logger.debug(`[MessageService] Replying to message: ${replyTo}`);\n    }\n\n    const context = file ? { useMultipart: true, file } : undefined;\n\n    this.logger.debug(\n      `[MessageService] Sending GraphQL mutation with variables:`,\n      variables\n    );\n\n    return this.apollo\n      .mutate<SendMessageResponse>({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables,\n        context,\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] ⚡ INSTANT: Message send response received`\n          );\n\n          if (!result.data?.sendMessage) {\n            this.logger.error(\n              `[MessageService] ❌ Failed to send message: No data returned`\n            );\n            throw new Error('Failed to send message');\n          }\n\n          try {\n            this.logger.debug(\n              `[MessageService] 🚀 INSTANT: Normalizing sent message`,\n              result.data.sendMessage\n            );\n            const normalizedMessage = this.normalizeMessage(\n              result.data.sendMessage\n            );\n\n            this.logger.info(\n              `[MessageService] ✅ INSTANT: Message sent successfully: ${normalizedMessage.id}`\n            );\n\n            // OPTIMISATION: Mise à jour immédiate de l'UI\n            this.zone.run(() => {\n              // Émettre immédiatement le message dans le flux\n              this.logger.debug('📡 INSTANT: Updating UI immediately');\n            });\n\n            return normalizedMessage;\n          } catch (normalizationError) {\n            this.logger.error(\n              `[MessageService] ❌ Error normalizing message:`,\n              normalizationError\n            );\n\n            // Retourner un message minimal mais valide plutôt que de lancer une erreur\n            const minimalMessage: Message = {\n              id: result.data.sendMessage.id || 'temp-' + Date.now(),\n              content: result.data.sendMessage.content || '',\n              type: result.data.sendMessage.type || MessageType.TEXT,\n              timestamp: new Date(),\n              isRead: false,\n              sender: {\n                id: this.getCurrentUserId(),\n                username: 'You',\n              },\n            };\n\n            this.logger.info(\n              `[MessageService] Returning minimal message: ${minimalMessage.id}`\n            );\n            return minimalMessage;\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(`[MessageService] Error sending message:`, error);\n          return throwError(() => new Error('Failed to send message'));\n        })\n      );\n  }\n\n  markMessageAsRead(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<MarkAsReadResponse>({\n        mutation: MARK_AS_READ_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.markMessageAsRead)\n            throw new Error('Failed to mark message as read');\n          return {\n            ...result.data.markMessageAsRead,\n            readAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error marking message as read:', error);\n          return throwError(() => new Error('Failed to mark message as read'));\n        })\n      );\n  }\n\n  reactToMessage(messageId: string, emoji: string): Observable<Message> {\n    return this.apollo\n      .mutate<ReactToMessageResponse>({\n        mutation: REACT_TO_MESSAGE_MUTATION,\n        variables: { messageId, emoji },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.reactToMessage)\n            throw new Error('Failed to react to message');\n          return result.data.reactToMessage;\n        }),\n        catchError((error) => {\n          console.error('Error reacting to message:', error);\n          return throwError(() => new Error('Failed to react to message'));\n        })\n      );\n  }\n\n  forwardMessage(\n    messageId: string,\n    conversationIds: string[]\n  ): Observable<Message[]> {\n    return this.apollo\n      .mutate<ForwardMessageResponse>({\n        mutation: FORWARD_MESSAGE_MUTATION,\n        variables: { messageId, conversationIds },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.forwardMessage)\n            throw new Error('Failed to forward message');\n          return result.data.forwardMessage.map((msg) => ({\n            ...msg,\n            timestamp: msg.timestamp\n              ? this.normalizeDate(msg.timestamp)\n              : new Date(),\n          }));\n        }),\n        catchError((error) => {\n          console.error('Error forwarding message:', error);\n          return throwError(() => new Error('Failed to forward message'));\n        })\n      );\n  }\n\n  pinMessage(messageId: string, conversationId: string): Observable<Message> {\n    return this.apollo\n      .mutate<PinMessageResponse>({\n        mutation: PIN_MESSAGE_MUTATION,\n        variables: { messageId, conversationId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.pinMessage)\n            throw new Error('Failed to pin message');\n          return {\n            ...result.data.pinMessage,\n            pinnedAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error pinning message:', error);\n          return throwError(() => new Error('Failed to pin message'));\n        })\n      );\n  }\n\n  searchMessages(\n    query: string,\n    conversationId?: string,\n    filters: MessageFilter = {}\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<SearchMessagesResponse>({\n        query: SEARCH_MESSAGES_QUERY,\n        variables: {\n          query,\n          conversationId,\n          ...filters,\n          dateFrom: this.toSafeISOString(filters.dateFrom),\n          dateTo: this.toSafeISOString(filters.dateTo),\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.searchMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error searching messages:', error);\n          return throwError(() => new Error('Failed to search messages'));\n        })\n      );\n  }\n\n  getUnreadMessages(userId: string): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<GetUnreadMessagesResponse>({\n        query: GET_UNREAD_MESSAGES_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.getUnreadMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error fetching unread messages:', error);\n          return throwError(() => new Error('Failed to fetch unread messages'));\n        })\n      );\n  }\n\n  setActiveConversation(conversationId: string): void {\n    this.activeConversation.next(conversationId);\n  }\n\n  getConversations(): Observable<Conversation[]> {\n    return this.apollo\n      .watchQuery<GetConversationsResponse>({\n        query: GET_CONVERSATIONS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const conversations = result.data?.getConversations || [];\n          return conversations.map((conv) => this.normalizeConversation(conv));\n        }),\n        catchError((error) => {\n          console.error('Error fetching conversations:', error);\n          return throwError(() => new Error('Failed to load conversations'));\n        })\n      );\n  }\n\n  getConversation(\n    conversationId: string,\n    limit?: number,\n    page?: number\n  ): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`\n    );\n\n    const variables: any = { conversationId };\n\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(\n        `[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`\n      );\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(\n      `[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`\n    );\n\n    return this.apollo\n      .watchQuery<GetConversationResponse>({\n        query: GET_CONVERSATION_QUERY,\n        variables: variables,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation response received:`,\n            result\n          );\n\n          const conv = result.data?.getConversation;\n          if (!conv) {\n            this.logger.error(\n              `[MessageService] Conversation not found: ${conversationId}`\n            );\n            throw new Error('Conversation not found');\n          }\n\n          this.logger.debug(\n            `[MessageService] Normalizing conversation: ${conversationId}`\n          );\n          const normalizedConversation = this.normalizeConversation(conv);\n\n          this.logger.info(\n            `[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${\n              normalizedConversation.participants?.length || 0\n            }, messages: ${normalizedConversation.messages?.length || 0}`\n          );\n          return normalizedConversation;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error fetching conversation:`,\n            error\n          );\n          return throwError(() => new Error('Failed to load conversation'));\n        })\n      );\n  }\n\n  createConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to create a conversation')\n      );\n    }\n\n    return this.apollo\n      .mutate<{ createConversation: Conversation }>({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation creation response:`,\n            result\n          );\n\n          const conversation = result.data?.createConversation;\n          if (!conversation) {\n            this.logger.error(\n              `[MessageService] Failed to create conversation with user: ${userId}`\n            );\n            throw new Error('Failed to create conversation');\n          }\n\n          try {\n            const normalizedConversation =\n              this.normalizeConversation(conversation);\n            this.logger.info(\n              `[MessageService] Conversation created successfully: ${normalizedConversation.id}`\n            );\n            return normalizedConversation;\n          } catch (error) {\n            this.logger.error(\n              `[MessageService] Error normalizing created conversation:`,\n              error\n            );\n            throw new Error('Error processing created conversation');\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error creating conversation with user ${userId}:`,\n            error\n          );\n          return throwError(\n            () => new Error(`Failed to create conversation: ${error.message}`)\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting or creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot get/create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to get/create a conversation')\n      );\n    }\n\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(\n      map((conversations) => {\n        // Récupérer l'ID de l'utilisateur actuel\n        const currentUserId = this.getCurrentUserId();\n\n        // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n        const existingConversation = conversations.find((conv) => {\n          if (conv.isGroup) return false;\n\n          // Vérifier si la conversation contient les deux utilisateurs\n          const participantIds =\n            conv.participants?.map((p) => p.id || p._id) || [];\n          return (\n            participantIds.includes(userId) &&\n            participantIds.includes(currentUserId)\n          );\n        });\n\n        if (existingConversation) {\n          this.logger.info(\n            `[MessageService] Found existing conversation: ${existingConversation.id}`\n          );\n          return existingConversation;\n        }\n\n        // Si aucune conversation n'est trouvée, en créer une nouvelle\n        throw new Error('No existing conversation found');\n      }),\n      catchError((error) => {\n        this.logger.info(\n          `[MessageService] No existing conversation found, creating new one: ${error.message}`\n        );\n        return this.createConversation(userId);\n      })\n    );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 2: Méthodes pour les Notifications\n  // --------------------------------------------------------------------------\n  // Propriétés pour la pagination des notifications\n  private notificationPagination = {\n    currentPage: 1,\n    limit: 10,\n    hasMoreNotifications: true,\n  };\n\n  getNotifications(\n    refresh = false,\n    page = 1,\n    limit = 10\n  ): Observable<Notification[]> {\n    this.logger.info(\n      'MessageService',\n      `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`\n    );\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY,\n    });\n\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug(\n        'MessageService',\n        'Resetting pagination due to refresh'\n      );\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug(\n      'MessageService',\n      `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`\n    );\n\n    return this.apollo\n      .watchQuery<getUserNotificationsResponse>({\n        query: GET_NOTIFICATIONS_QUERY,\n        variables: {\n          page: page,\n          limit: limit,\n        },\n        fetchPolicy: refresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Notifications response received'\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          const notifications = result.data?.getUserNotifications || [];\n          this.logger.debug(\n            'MessageService',\n            `Received ${notifications.length} notifications from server for page ${page}`\n          );\n\n          // Vérifier s'il y a plus de notifications à charger\n          this.notificationPagination.hasMoreNotifications =\n            notifications.length >= limit;\n\n          if (notifications.length === 0) {\n            this.logger.info(\n              'MessageService',\n              'No notifications received from server'\n            );\n            this.notificationPagination.hasMoreNotifications = false;\n          }\n\n          // Filtrer les notifications supprimées\n          const filteredNotifications = notifications.filter(\n            (notif) => !deletedNotificationIds.has(notif.id)\n          );\n\n          this.logger.debug(\n            'MessageService',\n            `Filtered out ${\n              notifications.length - filteredNotifications.length\n            } deleted notifications`\n          );\n\n          // Afficher les notifications reçues pour le débogage\n          filteredNotifications.forEach((notif, index) => {\n            console.log(`Notification ${index + 1} (page ${page}):`, {\n              id: notif.id || (notif as any)._id,\n              type: notif.type,\n              content: notif.content,\n              isRead: notif.isRead,\n            });\n          });\n\n          // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n          // Mettre à jour le cache avec les nouvelles notifications\n          this.updateCache(filteredNotifications);\n\n          // Récupérer toutes les notifications du cache et les TRIER\n          const cachedNotifications = Array.from(\n            this.notificationCache.values()\n          );\n\n          // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n          const sortedNotifications =\n            this.sortNotificationsByDate(cachedNotifications);\n\n          console.log(\n            `📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`\n          );\n\n          // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n          this.notifications.next(sortedNotifications);\n\n          // Mettre à jour le compteur de notifications non lues\n          this.updateUnreadCount();\n\n          // Sauvegarder les notifications dans le localStorage\n          this.saveNotificationsToLocalStorage();\n\n          return cachedNotifications;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error loading notifications:',\n            error\n          );\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          return throwError(() => new Error('Failed to load notifications'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  private getDeletedNotificationIds(): Set<string> {\n    try {\n      const deletedIds = new Set<string>();\n      const savedNotifications = localStorage.getItem('notifications');\n\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(\n        JSON.parse(savedNotifications).map((n: Notification) => n.id)\n      );\n\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications =\n        this.apollo.client.readQuery<getUserNotificationsResponse>({\n          query: GET_NOTIFICATIONS_QUERY,\n        })?.getUserNotifications || [];\n\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach((notification) => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n\n      return deletedIds;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la récupération des IDs de notifications supprimées:',\n        error\n      );\n      return new Set<string>();\n    }\n  }\n\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications(): boolean {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications(): Observable<Notification[]> {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(\n      false,\n      nextPage,\n      this.notificationPagination.limit\n    );\n  }\n  getNotificationById(id: string): Observable<Notification | undefined> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.find((n) => n.id === id)),\n      catchError((error) => {\n        this.logger.error('Error finding notification:', error);\n        return throwError(() => new Error('Failed to find notification'));\n      })\n    );\n  }\n  getNotificationCount(): number {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId: string): Observable<Attachment[]> {\n    return this.apollo\n      .query<getNotificationAttachmentsEvent>({\n        query: GET_NOTIFICATIONS_ATTACHAMENTS,\n        variables: { id: notificationId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result) => result.data?.getNotificationAttachments || []),\n        catchError((error) => {\n          this.logger.error('Error fetching notification attachments:', error);\n          return throwError(() => new Error('Failed to fetch attachments'));\n        })\n      );\n  }\n  getUnreadNotifications(): Observable<Notification[]> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.filter((n) => !n.isRead))\n    );\n  }\n\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(\n    notificationId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de la notification ${notificationId}`\n    );\n\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n\n    // Appeler le backend pour supprimer la notification\n    return this.apollo\n      .mutate<{ deleteNotification: { success: boolean; message: string } }>({\n        mutation: DELETE_NOTIFICATION_MUTATION,\n        variables: { notificationId },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteNotification;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(error, 'la suppression de la notification', {\n            success: true,\n            message: 'Notification supprimée localement (erreur serveur)',\n          })\n        )\n      );\n  }\n\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  private saveNotificationsToLocalStorage(): void {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug(\n        'MessageService',\n        'Notifications sauvegardées localement'\n      );\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la sauvegarde des notifications:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications(): Observable<{\n    success: boolean;\n    count: number;\n    message: string;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      'Suppression de toutes les notifications'\n    );\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo\n      .mutate<{\n        deleteAllNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_ALL_NOTIFICATIONS_MUTATION,\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteAllNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression de toutes les notifications:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression de toutes les notifications',\n            {\n              success: true,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(\n    notificationIds: string[]\n  ): Observable<{ success: boolean; count: number; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de ${notificationIds.length} notifications`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo\n      .mutate<{\n        deleteMultipleNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n        variables: { notificationIds },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteMultipleNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression multiple:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression multiple de notifications',\n            {\n              success: count > 0,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n  groupNotificationsByType(): Observable<\n    Map<NotificationType, Notification[]>\n  > {\n    return this.notifications$.pipe(\n      map((notifications) => {\n        const groups = new Map<NotificationType, Notification[]>();\n        notifications.forEach((notif) => {\n          if (!groups.has(notif.type)) {\n            groups.set(notif.type, []);\n          }\n          groups.get(notif.type)?.push(notif);\n        });\n        return groups;\n      })\n    );\n  }\n  markAsRead(notificationIds: string[]): Observable<{\n    success: boolean;\n    readCount: number;\n    remainingCount: number;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value,\n      });\n    }\n\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(\n      (id) => id && typeof id === 'string' && id.trim() !== ''\n    );\n\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds,\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n\n    this.logger.debug(\n      'MessageService',\n      'Sending mutation to mark notifications as read',\n      validIds\n    );\n\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(\n          0,\n          this.notificationCount.value - validIds.length\n        ),\n      },\n    };\n\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds,\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n\n    return this.apollo\n      .mutate<MarkNotificationsAsReadResponse>({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: { notificationIds: validIds },\n        optimisticResponse: optimisticResponse,\n        errorPolicy: 'all', // Continuer même en cas d'erreur\n        fetchPolicy: 'no-cache', // Ne pas utiliser le cache pour cette mutation\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug('MessageService', 'Mutation result', result);\n          console.log('Mutation result:', result);\n\n          // Si nous avons des erreurs GraphQL, les logger mais continuer\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            console.error('GraphQL errors:', result.errors);\n          }\n\n          // Utiliser la réponse du serveur ou notre réponse optimiste\n          const response =\n            result.data?.markNotificationsAsRead ??\n            optimisticResponse.markNotificationsAsRead;\n\n          return response;\n        }),\n        catchError((error: Error) => {\n          this.logger.error(\n            'MessageService',\n            'Error marking notifications as read:',\n            error\n          );\n          console.error('Error in markAsRead:', error);\n\n          // En cas d'erreur, retourner quand même un succès simulé\n          // puisque nous avons déjà mis à jour l'interface utilisateur\n          return of({\n            success: true,\n            readCount: validIds.length,\n            remainingCount: Math.max(\n              0,\n              this.notificationCount.value - validIds.length\n            ),\n          });\n        })\n      );\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels\n  // --------------------------------------------------------------------------\n\n  /**\n   * Initie un appel avec un autre utilisateur\n   * @param recipientId ID de l'utilisateur à appeler\n   * @param callType Type d'appel (audio, vidéo)\n   * @param conversationId ID de la conversation (optionnel)\n   * @param options Options d'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string,\n    options?: CallOptions\n  ): Observable<Call> {\n    return this.setupMediaDevices(callType).pipe(\n      switchMap((stream) => {\n        this.localStream = stream;\n        this.localStream$.next(stream);\n\n        // Créer une connexion peer\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n\n        // Ajouter les pistes audio/vidéo\n        stream.getTracks().forEach((track) => {\n          this.peerConnection!.addTrack(track, stream);\n        });\n\n        // Écouter les candidats ICE\n        this.peerConnection.onicecandidate = (event) => {\n          if (event.candidate) {\n            this.sendCallSignal(\n              this.generateCallId(),\n              'ice-candidate',\n              JSON.stringify(event.candidate)\n            );\n          }\n        };\n\n        // Écouter les pistes distantes\n        this.peerConnection.ontrack = (event) => {\n          if (!this.remoteStream) {\n            this.remoteStream = new MediaStream();\n            this.remoteStream$.next(this.remoteStream);\n          }\n          event.streams[0].getTracks().forEach((track) => {\n            this.remoteStream!.addTrack(track);\n          });\n        };\n\n        // Créer l'offre SDP\n        return from(this.peerConnection.createOffer()).pipe(\n          switchMap((offer) => {\n            return from(this.peerConnection!.setLocalDescription(offer)).pipe(\n              map(() => offer)\n            );\n          })\n        );\n      }),\n      switchMap((offer) => {\n        // Générer un ID d'appel unique\n        const callId = this.generateCallId();\n\n        // Envoyer l'offre au serveur\n        return this.apollo\n          .mutate<{ initiateCall: Call }>({\n            mutation: INITIATE_CALL_MUTATION,\n            variables: {\n              recipientId,\n              callType,\n              callId,\n              offer: JSON.stringify(offer),\n              conversationId,\n              options,\n            },\n          })\n          .pipe(\n            map((result) => {\n              const call = result.data?.initiateCall;\n              if (!call) {\n                throw new Error('Failed to initiate call');\n              }\n\n              // Mettre à jour l'état de l'appel actif\n              this.activeCall.next(call);\n\n              // S'abonner aux signaux d'appel\n              const signalSub = this.subscribeToCallSignals(\n                call.id\n              ).subscribe();\n              this.subscriptions.push(signalSub);\n\n              return call;\n            })\n          );\n      }),\n      catchError((error) => {\n        this.logger.error('Error initiating call', error);\n        this.cleanupCall();\n        return throwError(() => new Error('Failed to initiate call'));\n      })\n    );\n  }\n\n  /**\n   * Accepte un appel entrant\n   * @param incomingCall Appel entrant à accepter\n   * @returns Observable avec les informations de l'appel\n   */\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\n    this.stop('ringtone');\n\n    return this.setupMediaDevices(incomingCall.type).pipe(\n      switchMap((stream) => {\n        this.localStream = stream;\n        this.localStream$.next(stream);\n\n        // Créer une connexion peer\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n\n        // Ajouter les pistes audio/vidéo\n        stream.getTracks().forEach((track) => {\n          this.peerConnection!.addTrack(track, stream);\n        });\n\n        // Écouter les candidats ICE\n        this.peerConnection.onicecandidate = (event) => {\n          if (event.candidate) {\n            this.sendCallSignal(\n              incomingCall.id,\n              'ice-candidate',\n              JSON.stringify(event.candidate)\n            );\n          }\n        };\n\n        // Écouter les pistes distantes\n        this.peerConnection.ontrack = (event) => {\n          if (!this.remoteStream) {\n            this.remoteStream = new MediaStream();\n            this.remoteStream$.next(this.remoteStream);\n          }\n          event.streams[0].getTracks().forEach((track) => {\n            this.remoteStream!.addTrack(track);\n          });\n        };\n\n        // Définir l'offre distante\n        const offer = JSON.parse(incomingCall.offer);\n        return from(\n          this.peerConnection.setRemoteDescription(\n            new RTCSessionDescription(offer)\n          )\n        ).pipe(\n          switchMap(() => from(this.peerConnection!.createAnswer())),\n          switchMap((answer) => {\n            return from(this.peerConnection!.setLocalDescription(answer)).pipe(\n              map(() => answer)\n            );\n          })\n        );\n      }),\n      switchMap((answer) => {\n        // Envoyer la réponse au serveur\n        return this.apollo\n          .mutate<{ acceptCall: Call }>({\n            mutation: ACCEPT_CALL_MUTATION,\n            variables: {\n              callId: incomingCall.id,\n              answer: JSON.stringify(answer),\n            },\n          })\n          .pipe(\n            map((result) => {\n              const call = result.data?.acceptCall;\n              if (!call) {\n                throw new Error('Failed to accept call');\n              }\n\n              // Jouer le son de connexion\n              this.play('call-connected');\n\n              // Mettre à jour l'état de l'appel actif\n              this.activeCall.next({\n                ...call,\n                caller: incomingCall.caller,\n                type: incomingCall.type,\n                conversationId: incomingCall.conversationId,\n              });\n\n              // S'abonner aux signaux d'appel\n              const signalSub = this.subscribeToCallSignals(\n                incomingCall.id\n              ).subscribe();\n              this.subscriptions.push(signalSub);\n\n              // Effacer l'appel entrant\n              this.incomingCall.next(null);\n\n              return call;\n            })\n          );\n      }),\n      catchError((error) => {\n        this.logger.error('Error accepting call', error);\n        this.cleanupCall();\n        return throwError(() => new Error('Failed to accept call'));\n      })\n    );\n  }\n\n  /**\n   * Rejette un appel entrant\n   * @param callId ID de l'appel à rejeter\n   * @param reason Raison du rejet (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  rejectCall(callId: string, reason?: string): Observable<Call> {\n    this.stop('ringtone');\n\n    return this.apollo\n      .mutate<{ rejectCall: Call }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: {\n          callId,\n          reason,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.rejectCall;\n          if (!call) {\n            throw new Error('Failed to reject call');\n          }\n\n          // Effacer l'appel entrant\n          this.incomingCall.next(null);\n\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('Error rejecting call', error);\n          return throwError(() => new Error('Failed to reject call'));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel en cours\n   * @param callId ID de l'appel à terminer\n   * @param feedback Commentaires sur l'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  endCall(callId: string, feedback?: CallFeedback): Observable<Call> {\n    this.stop('ringtone');\n    this.play('call-end');\n\n    return this.apollo\n      .mutate<{ endCall: Call }>({\n        mutation: END_CALL_MUTATION,\n        variables: {\n          callId,\n          feedback,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.endCall;\n          if (!call) {\n            throw new Error('Failed to end call');\n          }\n\n          // Nettoyer les ressources\n          this.cleanupCall();\n\n          // Mettre à jour l'état de l'appel actif\n          this.activeCall.next(null);\n\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('Error ending call', error);\n          this.cleanupCall();\n          return throwError(() => new Error('Failed to end call'));\n        })\n      );\n  }\n\n  /**\n   * Active ou désactive la caméra ou le micro\n   * @param callId ID de l'appel\n   * @param video État de la caméra (optionnel)\n   * @param audio État du micro (optionnel)\n   * @returns Observable avec le résultat de l'opération\n   */\n  toggleMedia(\n    callId: string,\n    video?: boolean,\n    audio?: boolean\n  ): Observable<CallSuccess> {\n    if (this.localStream) {\n      // Mettre à jour les pistes locales\n      if (video !== undefined) {\n        this.localStream.getVideoTracks().forEach((track) => {\n          track.enabled = video;\n        });\n      }\n\n      if (audio !== undefined) {\n        this.localStream.getAudioTracks().forEach((track) => {\n          track.enabled = audio;\n        });\n      }\n    }\n\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: {\n          callId,\n          video,\n          audio,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.toggleCallMedia;\n          if (!success) {\n            throw new Error('Failed to toggle media');\n          }\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('Error toggling media', error);\n          return throwError(() => new Error('Failed to toggle media'));\n        })\n      );\n  }\n\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId: string): Observable<CallSignal> {\n    return this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: { callId },\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.callSignal) {\n            throw new Error('No call signal received');\n          }\n          return data.callSignal;\n        }),\n        tap((signal) => {\n          this.callSignals.next(signal);\n          this.handleCallSignal(signal);\n        }),\n        catchError((error) => {\n          this.logger.error('Error in call signal subscription', error);\n          return throwError(() => new Error('Call signal subscription failed'));\n        })\n      );\n  }\n\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(\n    callId: string,\n    signalType: string,\n    signalData: string\n  ): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ sendCallSignal: CallSuccess }>({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId,\n          signalType,\n          signalData,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.sendCallSignal;\n          if (!success) {\n            throw new Error('Failed to send call signal');\n          }\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('Error sending call signal', error);\n          return throwError(() => new Error('Failed to send call signal'));\n        })\n      );\n  }\n\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(\n    limit: number = 20,\n    offset: number = 0,\n    status?: string[],\n    type?: string[],\n    startDate?: string | null,\n    endDate?: string | null\n  ): Observable<Call[]> {\n    return this.apollo\n      .watchQuery<{ callHistory: Call[] }>({\n        query: CALL_HISTORY_QUERY,\n        variables: {\n          limit,\n          offset,\n          status,\n          type,\n          startDate,\n          endDate,\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const history = result.data?.callHistory || [];\n          this.logger.debug(`Retrieved ${history.length} call history items`);\n          return history;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call history:', error);\n          return throwError(() => new Error('Failed to fetch call history'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId: string): Observable<Call> {\n    return this.apollo\n      .watchQuery<{ callDetails: Call }>({\n        query: CALL_DETAILS_QUERY,\n        variables: { callId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const details = result.data?.callDetails;\n          if (!details) {\n            throw new Error('Call details not found');\n          }\n          this.logger.debug(`Retrieved call details for: ${callId}`);\n          return details;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call details:', error);\n          return throwError(() => new Error('Failed to fetch call details'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats(): Observable<any> {\n    return this.apollo\n      .watchQuery<{ callStats: any }>({\n        query: CALL_STATS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const stats = result.data?.callStats;\n          if (!stats) {\n            throw new Error('Call stats not found');\n          }\n          this.logger.debug('Retrieved call stats:', stats);\n          return stats;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call stats:', error);\n          return throwError(() => new Error('Failed to fetch call stats'));\n        })\n      );\n  }\n\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  private handleCallSignal(signal: CallSignal): void {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  private handleIceCandidate(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection\n        .addIceCandidate(new RTCIceCandidate(candidate))\n        .catch((error) => {\n          this.logger.error('Error adding ICE candidate', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error as Error);\n    }\n  }\n\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  private handleAnswer(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection\n        .setRemoteDescription(new RTCSessionDescription(answer))\n        .catch((error) => {\n          this.logger.error('Error setting remote description', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error as Error);\n    }\n  }\n\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  private handleEndCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  private handleRejectCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Nettoie les ressources d'appel\n   */\n  private cleanupCall(): void {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  private setupMediaDevices(callType: CallType): Observable<MediaStream> {\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video:\n        callType !== CallType.AUDIO\n          ? {\n              width: { ideal: 1280 },\n              height: { ideal: 720 },\n            }\n          : false,\n    };\n\n    return new Observable<MediaStream>((observer) => {\n      navigator.mediaDevices\n        .getUserMedia(constraints)\n        .then((stream) => {\n          observer.next(stream);\n          observer.complete();\n        })\n        .catch((error) => {\n          this.logger.error('Error accessing media devices', error);\n          observer.error(new Error('Failed to access media devices'));\n        });\n    });\n  }\n\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  private generateCallId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(\n    forceRefresh = false,\n    search?: string,\n    page: number = 1,\n    limit: number = 10,\n    sortBy: string = 'username',\n    sortOrder: string = 'asc',\n    isOnline?: boolean\n  ): Observable<User[]> {\n    this.logger.info(\n      'MessageService',\n      `Getting users with params: forceRefresh=${forceRefresh}, search=${\n        search || '(empty)'\n      }, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`\n    );\n\n    const now = Date.now();\n    const cacheValid =\n      !forceRefresh &&\n      this.usersCache.length > 0 &&\n      now - this.lastFetchTime <= this.CACHE_DURATION &&\n      !search &&\n      page === 1 &&\n      limit >= this.usersCache.length;\n\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug(\n        'MessageService',\n        `Using cached users (${this.usersCache.length} users)`\n      );\n      return of([...this.usersCache]);\n    }\n\n    this.logger.debug(\n      'MessageService',\n      `Fetching users from server with pagination, fetchPolicy=${\n        forceRefresh ? 'network-only' : 'cache-first'\n      }`\n    );\n\n    return this.apollo\n      .watchQuery<any>({\n        query: GET_ALL_USER_QUERY,\n        variables: {\n          search,\n          page,\n          limit,\n          sortBy,\n          sortOrder,\n          isOnline: isOnline !== undefined ? isOnline : null,\n        },\n        fetchPolicy: forceRefresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Users response received',\n            result\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors in getAllUsers:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          if (!result.data?.getAllUsers) {\n            this.logger.warn(\n              'MessageService',\n              'No users data received from server'\n            );\n            return [];\n          }\n\n          const paginatedResponse = result.data.getAllUsers;\n\n          // Log pagination metadata\n          this.logger.debug('MessageService', 'Pagination metadata:', {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          });\n\n          // Normalize users with error handling\n          const users: User[] = [];\n          for (const user of paginatedResponse.users) {\n            try {\n              if (user) {\n                users.push(this.normalizeUser(user));\n              }\n            } catch (error) {\n              this.logger.warn(\n                'MessageService',\n                `Error normalizing user, skipping:`,\n                error\n              );\n            }\n          }\n\n          this.logger.info(\n            'MessageService',\n            `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`\n          );\n\n          // Update cache only for first page with no filters\n          if (!search && page === 1 && !isOnline) {\n            this.usersCache = [...users];\n            this.lastFetchTime = Date.now();\n            this.logger.debug(\n              'MessageService',\n              `User cache updated with ${users.length} users`\n            );\n          }\n\n          // Store pagination metadata in a property for component access\n          this.currentUserPagination = {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          };\n\n          return users;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching users:', error);\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          // Return cache if available (only for first page)\n          if (\n            this.usersCache.length > 0 &&\n            page === 1 &&\n            !search &&\n            !isOnline\n          ) {\n            this.logger.warn(\n              'MessageService',\n              `Returning ${this.usersCache.length} cached users due to fetch error`\n            );\n            return of([...this.usersCache]);\n          }\n\n          return throwError(\n            () =>\n              new Error(\n                `Failed to fetch users: ${error.message || 'Unknown error'}`\n              )\n          );\n        })\n      );\n  }\n  getOneUser(userId: string): Observable<User> {\n    return this.apollo\n      .watchQuery<GetOneUserResponse>({\n        query: GET_USER_QUERY,\n        variables: { id: userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getOneUser)),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching user:', error);\n          return throwError(() => new Error('Failed to fetch user'));\n        })\n      );\n  }\n  getCurrentUser(): Observable<User> {\n    return this.apollo\n      .watchQuery<getCurrentUserResponse>({\n        query: GET_CURRENT_USER_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getCurrentUser)),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error fetching current user:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch current user'));\n        })\n      );\n  }\n  setUserOnline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOnlineResponse>({\n        mutation: SET_USER_ONLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOnline)\n            throw new Error('Failed to set user online');\n          return this.normalizeUser(result.data.setUserOnline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user online:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user online'));\n        })\n      );\n  }\n  setUserOffline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOfflineResponse>({\n        mutation: SET_USER_OFFLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOffline)\n            throw new Error('Failed to set user offline');\n          return this.normalizeUser(result.data.setUserOffline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user offline:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user offline'));\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(\n    name: string,\n    participantIds: string[],\n    photo?: File,\n    description?: string\n  ): Observable<any> {\n    this.logger.debug(\n      'MessageService',\n      `Creating group: ${name} with ${participantIds.length} participants`\n    );\n\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(\n        () => new Error('Nom du groupe et participants requis')\n      );\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: CREATE_GROUP_MUTATION,\n        variables: { name, participantIds, photo, description },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.createGroup;\n          if (!group) {\n            throw new Error('Échec de la création du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group created successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error creating group:', error);\n          return throwError(() => new Error('Échec de la création du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId: string, input: any): Observable<any> {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: UPDATE_GROUP_MUTATION,\n        variables: { id: groupId, input },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.updateGroup;\n          if (!group) {\n            throw new Error('Échec de la mise à jour du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group updated successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error updating group:', error);\n          return throwError(\n            () => new Error('Échec de la mise à jour du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: DELETE_GROUP_MUTATION,\n        variables: { id: groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.deleteGroup;\n          if (!response) {\n            throw new Error('Échec de la suppression du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group deleted successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error deleting group:', error);\n          return throwError(\n            () => new Error('Échec de la suppression du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: LEAVE_GROUP_MUTATION,\n        variables: { groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.leaveGroup;\n          if (!response) {\n            throw new Error('Échec de la sortie du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Left group successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error leaving group:', error);\n          return throwError(() => new Error('Échec de la sortie du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId: string): Observable<any> {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_GROUP_QUERY,\n        variables: { id: groupId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.getGroup;\n          if (!group) {\n            throw new Error('Groupe non trouvé');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group retrieved successfully: ${groupId}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error getting group:', error);\n          return throwError(\n            () => new Error('Échec de la récupération du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId: string): Observable<any[]> {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_USER_GROUPS_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const groups = result.data?.getUserGroups || [];\n          this.logger.info(\n            'MessageService',\n            `Retrieved ${groups.length} groups for user: ${userId}`\n          );\n          return groups;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error getting user groups:',\n            error\n          );\n          return throwError(\n            () => new Error('Échec de la récupération des groupes')\n          );\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  subscribeToNewMessages(conversationId: string): Observable<Message> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux messages avec un token invalide ou expiré\"\n      );\n      return of(null as unknown as Message);\n    }\n\n    this.logger.debug(\n      `🚀 INSTANT MESSAGE: Setting up real-time subscription for conversation: ${conversationId}`\n    );\n\n    const sub$ = this.apollo\n      .subscribe<{ messageSent: Message }>({\n        query: MESSAGE_SENT_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => {\n          const msg = result.data?.messageSent;\n          if (!msg) {\n            this.logger.warn('⚠️ No message payload received');\n            throw new Error('No message payload received');\n          }\n\n          this.logger.debug(\n            '⚡ INSTANT: New message received via WebSocket',\n            msg\n          );\n\n          // Vérifier que l'ID est présent\n          if (!msg.id && !msg._id) {\n            this.logger.warn(\n              '⚠️ Message without ID received, generating temp ID'\n            );\n            msg.id = `temp-${Date.now()}`;\n          }\n\n          try {\n            // NORMALISATION RAPIDE du message\n            const normalizedMessage = this.normalizeMessage(msg);\n\n            this.logger.debug(\n              '✅ INSTANT: Message normalized successfully',\n              normalizedMessage\n            );\n\n            // TRAITEMENT INSTANTANÉ selon le type\n            if (\n              normalizedMessage.type === MessageType.AUDIO ||\n              normalizedMessage.type === MessageType.VOICE_MESSAGE ||\n              (normalizedMessage.attachments &&\n                normalizedMessage.attachments.some(\n                  (att) => att.type === 'audio' || att.type === 'AUDIO'\n                ))\n            ) {\n              this.logger.debug(\n                '🎤 INSTANT: Voice message received in real-time'\n              );\n            }\n\n            // MISE À JOUR IMMÉDIATE de l'UI\n            this.zone.run(() => {\n              this.logger.debug(\n                '📡 INSTANT: Updating conversation UI immediately'\n              );\n              this.updateConversationWithNewMessage(\n                conversationId,\n                normalizedMessage\n              );\n            });\n\n            return normalizedMessage;\n          } catch (err) {\n            this.logger.error('❌ Error normalizing message:', err);\n\n            // Créer un message minimal mais valide pour éviter les erreurs\n            const minimalMessage: Message = {\n              id: msg.id || msg._id || `temp-${Date.now()}`,\n              content: msg.content || '',\n              type: msg.type || MessageType.TEXT,\n              timestamp: this.safeDate(msg.timestamp),\n              isRead: false,\n              sender: msg.sender\n                ? this.normalizeUser(msg.sender)\n                : {\n                    id: this.getCurrentUserId(),\n                    username: 'Unknown',\n                  },\n            };\n\n            this.logger.debug(\n              '🔧 FALLBACK: Created minimal message',\n              minimalMessage\n            );\n            return minimalMessage;\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Message subscription error:',\n            error\n          );\n          // Retourner un observable vide au lieu de null\n          return EMPTY;\n        }),\n        // Filtrer les valeurs null\n        filter((message) => !!message),\n        // Réessayer après un délai en cas d'erreur\n        retry(3)\n      );\n\n    const sub = sub$.subscribe({\n      next: (message) => {\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: (err) => {\n        this.logger.error('Error in message subscription:', err);\n      },\n    });\n\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  private updateConversationWithNewMessage(\n    conversationId: string,\n    message: Message\n  ): void {\n    this.logger.debug(\n      `⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`\n    );\n\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: (conversation) => {\n          this.logger.debug(\n            `✅ BACKGROUND: Conversation ${conversationId} refreshed with ${\n              conversation?.messages?.length || 0\n            } messages`\n          );\n        },\n        error: (error) => {\n          this.logger.error(\n            `⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`,\n            error\n          );\n        },\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n  subscribeToUserStatus(): Observable<User> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\"\n      );\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n\n    const sub$ = this.apollo\n      .subscribe<{ userStatusChanged: User }>({\n        query: USER_STATUS_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement au statut utilisateur:\",\n            result\n          )\n        ),\n        map((result) => {\n          const user = result.data?.userStatusChanged;\n          if (!user) {\n            this.logger.error('No status payload received');\n            throw new Error('No status payload received');\n          }\n          return this.normalizeUser(user);\n        }),\n        catchError((error) => {\n          this.logger.error('Status subscription error:', error as Error);\n          return throwError(() => new Error('Status subscription failed'));\n        }),\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(\n    conversationId: string\n  ): Observable<Conversation> {\n    const sub$ = this.apollo\n      .subscribe<{ conversationUpdated: Conversation }>({\n        query: CONVERSATION_UPDATED_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => {\n          const conv = result.data?.conversationUpdated;\n          if (!conv) throw new Error('No conversation payload received');\n\n          const normalizedConversation: Conversation = {\n            ...conv,\n            participants:\n              conv.participants?.map((p) => this.normalizeUser(p)) || [],\n            lastMessage: conv.lastMessage\n              ? {\n                  ...conv.lastMessage,\n                  sender: this.normalizeUser(conv.lastMessage.sender),\n                  timestamp: this.safeDate(conv.lastMessage.timestamp),\n                  readAt: conv.lastMessage.readAt\n                    ? this.safeDate(conv.lastMessage.readAt)\n                    : undefined,\n                  // Conservez toutes les autres propriétés du message\n                  id: conv.lastMessage.id,\n                  content: conv.lastMessage.content,\n                  type: conv.lastMessage.type,\n                  isRead: conv.lastMessage.isRead,\n                  // ... autres propriétés nécessaires\n                }\n              : null, // On conserve null comme dans votre version originale\n          };\n\n          return normalizedConversation as Conversation; // Assertion de type si nécessaire\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Conversation subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Conversation subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(\n    conversationId: string\n  ): Observable<TypingIndicatorEvent> {\n    const sub$ = this.apollo\n      .subscribe<TypingIndicatorEvents>({\n        query: TYPING_INDICATOR_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => result.data?.typingIndicator),\n        filter(Boolean),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Typing indicator subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Typing indicator subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  private isTokenValid(): boolean {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString(),\n        });\n        return false;\n      }\n\n      return true;\n    } catch (error) {\n      this.logger.error(\n        'Erreur lors de la vérification du token:',\n        error as Error\n      );\n      return false;\n    }\n  }\n\n  subscribeToNotificationsRead(): Observable<string[]> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications avec un token invalide ou expiré\"\n      );\n      return of([]);\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n\n    const sub$ = this.apollo\n      .subscribe<NotificationsReadEvent>({\n        query: NOTIFICATIONS_READ_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement aux notifications lues:\",\n            result\n          )\n        ),\n        map((result) => {\n          const notificationIds = result.data?.notificationsRead || [];\n          this.logger.debug(\n            'Notifications marquées comme lues:',\n            notificationIds\n          );\n          this.updateNotificationStatus(notificationIds, true);\n          return notificationIds;\n        }),\n        catchError((err) => {\n          this.logger.error(\n            'Notifications read subscription error:',\n            err as Error\n          );\n          // Retourner un tableau vide au lieu de propager l'erreur\n          return of([]);\n        }),\n        // Réessayer après un délai en cas d'erreur\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications(): Observable<Notification> {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications sans être connecté\"\n      );\n      return EMPTY;\n    }\n\n    this.logger.debug(\n      '🚀 INSTANT NOTIFICATION: Setting up real-time subscription'\n    );\n\n    const source$ = this.apollo.subscribe<NotificationReceivedEvent>({\n      query: NOTIFICATION_SUBSCRIPTION,\n    });\n\n    const processed$ = source$.pipe(\n      map((result) => {\n        const notification = result.data?.notificationReceived;\n        if (!notification) {\n          throw new Error('No notification payload received');\n        }\n\n        this.logger.debug(\n          '⚡ INSTANT: New notification received',\n          notification\n        );\n\n        const normalized = this.normalizeNotification(notification);\n\n        // Vérification rapide du cache\n        if (this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            `🔄 Notification ${normalized.id} already in cache, skipping`\n          );\n          throw new Error('Notification already exists in cache');\n        }\n\n        // TRAITEMENT INSTANTANÉ\n        this.logger.debug('📡 INSTANT: Processing notification immediately');\n\n        // Son de notification IMMÉDIAT\n        this.playNotificationSound();\n\n        // Mise à jour INSTANTANÉE du cache\n        this.updateNotificationCache(normalized);\n\n        // Émettre IMMÉDIATEMENT la notification EN PREMIER\n        this.zone.run(() => {\n          // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n          const currentNotifications = this.notifications.value;\n          const updatedNotifications = [normalized, ...currentNotifications];\n\n          this.logger.debug(\n            `⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`\n          );\n\n          this.notifications.next(updatedNotifications);\n          this.notificationCount.next(this.notificationCount.value + 1);\n        });\n\n        this.logger.debug(\n          '✅ INSTANT: Notification processed and emitted',\n          normalized\n        );\n\n        return normalized;\n      }),\n      // Gestion d'erreurs optimisée\n      catchError((err) => {\n        if (\n          err instanceof Error &&\n          err.message === 'Notification already exists in cache'\n        ) {\n          return EMPTY;\n        }\n\n        this.logger.error('❌ Notification subscription error:', err as Error);\n        return EMPTY;\n      }),\n      // Optimisation: traitement en temps réel\n      tap((notification) => {\n        this.logger.debug(\n          '⚡ INSTANT: Notification ready for UI update',\n          notification\n        );\n      })\n    );\n\n    const sub = processed$.subscribe({\n      next: (notification) => {\n        this.logger.debug(\n          '✅ INSTANT: Notification delivered to UI',\n          notification\n        );\n      },\n      error: (error) => {\n        this.logger.error(\n          '❌ CRITICAL: Notification subscription error',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n\n  private startCleanupInterval(): void {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  private cleanupExpiredNotifications(): void {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n    let expiredCount = 0;\n\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(\n        this.notificationCache.values()\n      );\n      const sortedNotifications = this.sortNotificationsByDate(\n        remainingNotifications\n      );\n\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  private sortNotificationsByDate(\n    notifications: Notification[]\n  ): Notification[] {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  private getCurrentUserId(): string {\n    return localStorage.getItem('userId') || '';\n  }\n  private normalizeMessage(message: Message): Message {\n    if (!message) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined message'\n      );\n      throw new Error('Message object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error(\n          '[MessageService] Message ID is missing',\n          undefined,\n          message\n        );\n        throw new Error('Message ID is required');\n      }\n\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender\n          ? this.normalizeUser(message.sender)\n          : undefined;\n      } catch (error) {\n        this.logger.warn(\n          '[MessageService] Error normalizing message sender, using default values',\n          error\n        );\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true,\n        };\n      }\n\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing message receiver, using default values',\n            error\n          );\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true,\n          };\n        }\n      }\n\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments =\n        message.attachments?.map((att) => ({\n          id: att.id || att._id || `attachment-${Date.now()}`,\n          url: att.url || '',\n          type: att.type || 'unknown',\n          name: att.name || 'attachment',\n          size: att.size || 0,\n          duration: att.duration || 0,\n        })) || [];\n\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null,\n      };\n\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id,\n      });\n\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing message:',\n        error instanceof Error ? error : new Error(String(error)),\n        message\n      );\n      throw new Error(\n        `Failed to normalize message: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n\n  public normalizeUser(user: any): User {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive =\n      user.isActive !== undefined && user.isActive !== null\n        ? user.isActive\n        : true;\n    const role = user.role || 'user';\n\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount,\n    };\n  }\n  private normalizeConversation(conv: Conversation): Conversation {\n    if (!conv) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined conversation'\n      );\n      throw new Error('Conversation object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error(\n          '[MessageService] Conversation ID is missing',\n          undefined,\n          conv\n        );\n        throw new Error('Conversation ID is required');\n      }\n\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing participant, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.warn(\n          '[MessageService] Conversation has no participants or invalid participants array',\n          conv\n        );\n      }\n\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length,\n        });\n\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug(\n                '[MessageService] Successfully normalized message',\n                {\n                  messageId: normalizedMessage.id,\n                  content: normalizedMessage.content?.substring(0, 20),\n                  sender: normalizedMessage.sender?.username,\n                }\n              );\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing message in conversation, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.debug(\n          '[MessageService] No messages found in conversation or invalid messages array'\n        );\n      }\n\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing last message, using null',\n            error\n          );\n        }\n      }\n\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt),\n      };\n\n      this.logger.debug(\n        '[MessageService] Conversation normalized successfully',\n        {\n          conversationId: normalizedConversation.id,\n          participantCount: normalizedParticipants.length,\n          messageCount: normalizedMessages.length,\n        }\n      );\n\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing conversation:',\n        error instanceof Error ? error : new Error(String(error)),\n        conv\n      );\n      throw new Error(\n        `Failed to normalize conversation: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n  private normalizeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  private safeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  private toSafeISOString = (\n    date: Date | string | undefined\n  ): string | undefined => {\n    if (!date) return undefined;\n    return typeof date === 'string' ? date : date.toISOString();\n  };\n  private normalizeNotification(notification: Notification): Notification {\n    this.logger.debug(\n      'MessageService',\n      'Normalizing notification',\n      notification\n    );\n\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || (notification as any)._id;\n    if (!notificationId) {\n      this.logger.error(\n        'MessageService',\n        'Notification ID is missing',\n        notification\n      );\n      throw new Error('Notification ID is required');\n    }\n\n    if (!notification.timestamp) {\n      this.logger.warn(\n        'MessageService',\n        'Notification timestamp is missing, using current time',\n        notification\n      );\n      notification.timestamp = new Date();\n    }\n\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId, // Conserver l'ID MongoDB original\n        id: notificationId, // Utiliser le même ID pour les deux propriétés\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId),\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message),\n        }),\n      };\n\n      this.logger.debug(\n        'MessageService',\n        'Normalized notification result',\n        normalized\n      );\n      return normalized;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Error in normalizeNotification',\n        error\n      );\n      throw error;\n    }\n  }\n  private normalizeSender(sender: any) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && { image: sender.image }),\n    };\n  }\n\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  private normalizeNotMessage(message: any) {\n    if (!message) return null;\n\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && { sender: this.normalizeSender(message.sender) }),\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  private updateCache(\n    notifications: Notification | Notification[],\n    skipDuplicates: boolean = true\n  ) {\n    const notificationArray = Array.isArray(notifications)\n      ? notifications\n      : [notifications];\n\n    this.logger.debug(\n      'MessageService',\n      `Updating notification cache with ${notificationArray.length} notifications`\n    );\n\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(\n      (notif) => notif && (notif.id || (notif as any)._id)\n    );\n\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn(\n        'MessageService',\n        `Found ${\n          notificationArray.length - validNotifications.length\n        } notifications without valid IDs`\n      );\n    }\n\n    let addedCount = 0;\n    let skippedCount = 0;\n\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || (notif as any)._id;\n        if (!notifId) {\n          this.logger.error(\n            'MessageService',\n            'Notification without ID:',\n            notif\n          );\n          return;\n        }\n\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            'MessageService',\n            `Notification ${normalized.id} already exists in cache, skipping`\n          );\n          skippedCount++;\n          return;\n        }\n\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n\n        this.logger.debug(\n          'MessageService',\n          `Added notification ${normalized.id} to cache`\n        );\n      } catch (error) {\n        this.logger.error(\n          'MessageService',\n          `Error processing notification ${index + 1}:`,\n          error\n        );\n      }\n    });\n\n    this.logger.debug(\n      'MessageService',\n      `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`\n    );\n\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  private refreshNotificationObservables(): void {\n    const allNotifications = Array.from(this.notificationCache.values());\n\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n\n    this.logger.debug(\n      `📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`\n    );\n\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  private updateUnreadCount(): void {\n    const count = Array.from(this.notificationCache.values()).filter(\n      (n) => !n.isRead\n    ).length;\n    this.notificationCount.next(count);\n  }\n\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  private updateNotificationCache(notification: Notification): void {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  private updateNotificationStatus(ids: string[], isRead: boolean): void {\n    ids.forEach((id) => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined,\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  private removeNotificationsFromCache(notificationIds: string[]): number {\n    let removedCount = 0;\n    notificationIds.forEach((id) => {\n      if (this.notificationCache.has(id)) {\n        this.notificationCache.delete(id);\n        removedCount++;\n      }\n    });\n\n    if (removedCount > 0) {\n      this.refreshNotificationObservables();\n    }\n\n    return removedCount;\n  }\n\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  private handleDeletionError(\n    error: any,\n    operation: string,\n    fallbackResponse: any\n  ) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StartTupingResponse>({\n        mutation: START_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.startTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error starting typing indicator',\n            error\n          );\n          return throwError(\n            () => new Error('Failed to start typing indicator')\n          );\n        })\n      );\n  }\n\n  stopTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StopTypingResponse>({\n        mutation: STOP_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.stopTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error stopping typing indicator',\n            error\n          );\n          return throwError(() => new Error('Failed to stop typing indicator'));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODES POUR LES MESSAGES\n  // ========================================\n\n  /**\n   * Récupère les messages d'une conversation avec pagination\n   * @param conversationId ID de la conversation\n   * @param page Page à récupérer (string)\n   * @param limit Nombre de messages par page (string)\n   * @returns Observable avec les messages\n   */\n  getMessages(\n    conversationId: string,\n    page: string = '1',\n    limit: string = '20'\n  ): Observable<Message[]> {\n    this.logger.info(\n      'MessageService',\n      `Getting messages for conversation ${conversationId}, page ${page}, limit ${limit}`\n    );\n\n    return this.apollo\n      .watchQuery<{ getMessages: Message[] }>({\n        query: GET_MESSAGES_QUERY,\n        variables: {\n          conversationId,\n          page: parseInt(page, 10),\n          limit: parseInt(limit, 10),\n        },\n        fetchPolicy: 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Messages response received',\n            result\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors in getMessages:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          if (!result.data?.getMessages) {\n            this.logger.warn(\n              'MessageService',\n              'No messages data received from server'\n            );\n            return [];\n          }\n\n          const messages = result.data.getMessages;\n          this.logger.debug(\n            'MessageService',\n            `Retrieved ${messages.length} messages`\n          );\n\n          // Normaliser les messages\n          return messages.map((message) => this.normalizeMessage(message));\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error fetching messages:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch messages'));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false,\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive: string | Date | undefined): string {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate =\n      lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours =\n      Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown date';\n\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        });\n      }\n\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        })}`;\n      }\n\n      const day = date\n        .toLocaleDateString('fr-FR', { weekday: 'short' })\n        .toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages: any[], index: number): boolean {\n    if (index === 0) return true;\n\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  private getDateFromTimestamp(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (\n        timestamp instanceof Date ? timestamp : new Date(timestamp)\n      ).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType?: string): string {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword'))\n      return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed'))\n      return 'fa-file-archive';\n    return 'fa-file';\n  }\n\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType?: string): string {\n    if (!mimeType) return 'File';\n\n    const typeMap: Record<string, string> = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':\n        'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':\n        'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation':\n        'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive',\n    };\n\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message: any): boolean {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message: any): boolean {\n    if (!message) return false;\n\n    // Vérifier le type du message\n    if (\n      message.type === MessageType.VOICE_MESSAGE ||\n      message.type === MessageType.VOICE_MESSAGE_LOWER\n    ) {\n      return true;\n    }\n\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          (message.metadata?.isVoiceMessage &&\n            (type === 'AUDIO' || type === 'audio'))\n        );\n      });\n    }\n\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const voiceAttachment = message.attachments.find((att: any) => {\n      const type = att.type?.toString();\n      return (\n        type === 'VOICE_MESSAGE' ||\n        type === 'voice_message' ||\n        type === 'AUDIO' ||\n        type === 'audio'\n      );\n    });\n\n    return voiceAttachment?.url || '';\n  }\n\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message: any): number {\n    if (!message) return 0;\n\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          type === 'AUDIO' ||\n          type === 'audio'\n        );\n      });\n\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n\n    return 0;\n  }\n\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index: number): number {\n    const pattern = [\n      8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18,\n    ];\n    return pattern[index % pattern.length];\n  }\n\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds: number): string {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message: any): MessageType {\n    if (!message) return MessageType.TEXT;\n\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (\n            attachmentTypeStr === 'file' ||\n            attachmentTypeStr === 'FILE'\n          ) {\n            return MessageType.FILE;\n          } else if (\n            attachmentTypeStr === 'audio' ||\n            attachmentTypeStr === 'AUDIO'\n          ) {\n            return MessageType.AUDIO;\n          } else if (\n            attachmentTypeStr === 'video' ||\n            attachmentTypeStr === 'VIDEO'\n          ) {\n            return MessageType.VIDEO;\n          }\n        }\n\n        return MessageType.FILE;\n      }\n\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis(): string[] {\n    return [\n      '😀',\n      '😃',\n      '😄',\n      '😁',\n      '😆',\n      '😅',\n      '😂',\n      '🤣',\n      '😊',\n      '😇',\n      '🙂',\n      '🙃',\n      '😉',\n      '😌',\n      '😍',\n      '🥰',\n      '😘',\n      '😗',\n      '😙',\n      '😚',\n      '😋',\n      '😛',\n      '😝',\n      '😜',\n      '🤪',\n      '🤨',\n      '🧐',\n      '🤓',\n      '😎',\n      '🤩',\n      '😏',\n      '😒',\n      '😞',\n      '😔',\n      '😟',\n      '😕',\n      '🙁',\n      '☹️',\n      '😣',\n      '😖',\n      '😫',\n      '😩',\n      '🥺',\n      '😢',\n      '😭',\n      '😤',\n      '😠',\n      '😡',\n      '🤬',\n      '🤯',\n      '😳',\n      '🥵',\n      '🥶',\n      '😱',\n      '😨',\n      '😰',\n      '😥',\n      '😓',\n      '🤗',\n      '🤔',\n      '👍',\n      '👎',\n      '👏',\n      '🙌',\n      '👐',\n      '🤲',\n      '🤝',\n      '🙏',\n      '✌️',\n      '🤞',\n      '❤️',\n      '🧡',\n      '💛',\n      '💚',\n      '💙',\n      '💜',\n      '🖤',\n      '💔',\n      '💯',\n      '💢',\n    ];\n  }\n\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message: any, currentUserId: string | null): string {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n\n    try {\n      const isCurrentUser =\n        message.sender?.id === currentUserId ||\n        message.sender?._id === currentUserId ||\n        message.senderId === currentUserId;\n\n      const baseClass = isCurrentUser\n        ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm'\n        : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n\n      const messageType = this.getMessageType(message);\n\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (\n            attachmentTypeStr === 'FILE' ||\n            attachmentTypeStr === 'file'\n          ) {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n\n      if (\n        messageType === MessageType.IMAGE ||\n        messageType === MessageType.IMAGE_LOWER\n      ) {\n        return `p-1 max-w-xs`;\n      } else if (\n        messageType === MessageType.FILE ||\n        messageType === MessageType.FILE_LOWER\n      ) {\n        return `${baseClass} p-3`;\n      }\n\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n\n  // destroy\n  cleanupSubscriptions(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n}\n"], "mappings": "AAEA,SACEA,eAAe,EACfC,UAAU,EACVC,EAAE,EAEFC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,SAAS,QAGJ,gBAAgB;AACvB,SAASC,IAAI,QAAQ,MAAM;AAC3B,SACEC,WAAW,EAEXC,QAAQ,EACRC,UAAU,QAML,yBAAyB;AAChC,SACEC,uBAAuB,EACvBC,uBAAuB,EACvBC,yBAAyB,EACzBC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,iCAAiC,EACjCC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,yBAAyB,EACzBC,qBAAqB,EACrBC,oBAAoB,EACpBC,6BAA6B,EAC7BC,sBAAsB,EACtBC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,EACpBC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EAGrBC,oBAAoB,EACpBC,eAAe,EACfC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,+BAA+B,EAC/BC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,sCAAsC,EACtCC,iCAAiC;AACjC;AACAC,kBAAkB,EAClBC,kBAAkB,EAClBC,gBAAgB,EAChBC,sBAAsB,EACtBC,yBAAyB,EACzBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,0BAA0B,EAE1BC,wBAAwB,QACnB,4BAA4B;;;;AA0CnC,OAAM,MAAOC,cAAc;EA6DzBC,YACUC,MAAc,EACdC,MAAqB,EACrBC,IAAY;IAFZ,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IA/Dd;IACQ,KAAAC,kBAAkB,GAAG,IAAItE,eAAe,CAAgB,IAAI,CAAC;IAC7D,KAAAuE,aAAa,GAAG,IAAIvE,eAAe,CAAiB,EAAE,CAAC;IACvD,KAAAwE,iBAAiB,GAAG,IAAIC,GAAG,EAAwB;IAEnD,KAAAC,iBAAiB,GAAG,IAAI1E,eAAe,CAAS,CAAC,CAAC;IAClD,KAAA2E,WAAW,GAAG,IAAIF,GAAG,EAAgB;IACrC,KAAAG,aAAa,GAAmB,EAAE;IACzB,KAAAC,cAAc,GAAG,MAAM;IAChC,KAAAC,aAAa,GAAG,CAAC;IAEzB;IACQ,KAAAC,UAAU,GAAG,IAAI/E,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAgF,YAAY,GAAG,IAAIhF,eAAe,CAAsB,IAAI,CAAC;IAC7D,KAAAiF,WAAW,GAAG,IAAIjF,eAAe,CAAoB,IAAI,CAAC;IAC1D,KAAAkF,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,cAAc,GAA6B,IAAI;IAEvD;IACO,KAAAC,WAAW,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACP,YAAY,CAACM,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACP,WAAW,CAACK,YAAY,EAAE;IAC9C,KAAAG,YAAY,GAAG,IAAIzF,eAAe,CAAqB,IAAI,CAAC;IAC5D,KAAA0F,aAAa,GAAG,IAAI1F,eAAe,CAAqB,IAAI,CAAC;IAEpE;IACiB,KAAA2F,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IACO,KAAAC,UAAU,GAAW,EAAE;IAE/B;IACO,KAAAC,qBAAqB,GAMxB;MACFC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE;KAClB;IAED;IACO,KAAAC,mBAAmB,GAAG,IAAI,CAAC/B,kBAAkB,CAACgB,YAAY,EAAE;IAC5D,KAAAgB,cAAc,GAAG,IAAI,CAAC/B,aAAa,CAACe,YAAY,EAAE;IAClD,KAAAiB,kBAAkB,GAAG,IAAI,CAAC7B,iBAAiB,CAACY,YAAY,EAAE;IAEjE;IACQ,KAAAkB,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAu9BrB;IACA;IACA;IACA;IACQ,KAAAC,sBAAsB,GAAG;MAC/BT,WAAW,EAAE,CAAC;MACdU,KAAK,EAAE,EAAE;MACTC,oBAAoB,EAAE;KACvB;IA6gFO,KAAAC,eAAe,GACrBC,IAA+B,IACT;MACtB,IAAI,CAACA,IAAI,EAAE,OAAOC,SAAS;MAC3B,OAAO,OAAOD,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACE,WAAW,EAAE;IAC7D,CAAC;IA1+GC,IAAI,CAACC,iCAAiC,EAAE;IACxC,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;;EAIQH,iCAAiCA,CAAA;IACvC,IAAI;MACF,MAAMI,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAChE,IAAIF,kBAAkB,EAAE;QACtB,MAAM/C,aAAa,GAAGkD,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAmB;QAEtE,IAAI,CAAC9C,iBAAiB,CAACmD,KAAK,EAAE;QAE9BpD,aAAa,CAACqD,OAAO,CAAEC,YAAY,IAAI;UACrC,IAAIA,YAAY,IAAIA,YAAY,CAACC,EAAE,EAAE;YACnC,IAAI,CAACtD,iBAAiB,CAACuD,GAAG,CAACF,YAAY,CAACC,EAAE,EAAED,YAAY,CAAC;;QAE7D,CAAC,CAAC;QAEF,IAAI,CAACtD,aAAa,CAACyD,IAAI,CAACC,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAAC,CAAC;QACpE,IAAI,CAACC,iBAAiB,EAAE;;KAE3B,CAAC,OAAOC,KAAK,EAAE;MACd;IAAA;EAEJ;EACQjB,iBAAiBA,CAAA;IACvB,IAAI,CAAC9C,IAAI,CAACgE,iBAAiB,CAAC,MAAK;MAC/B,IAAI,CAACC,2BAA2B,EAAE,CAACC,SAAS,EAAE;MAC9C,IAAI,CAACC,4BAA4B,EAAE,CAACD,SAAS,EAAE;MAC/C,IAAI,CAACE,wBAAwB,EAAE,CAACF,SAAS,EAAE;IAC7C,CAAC,CAAC;IACF,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQD,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACtE,MAAM,CACfoE,SAAS,CAAiC;MACzCI,KAAK,EAAE5E;KACR,CAAC,CACD6E,IAAI,CACHtI,GAAG,CAAC,CAAC;MAAEuI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAE7D,YAAY,EAAE;QACvB,OAAO,IAAI;;MAGb;MACA,IAAI,CAAC8D,kBAAkB,CAACD,IAAI,CAAC7D,YAAY,CAAC;MAC1C,OAAO6D,IAAI,CAAC7D,YAAY;IAC1B,CAAC,CAAC,EACFzE,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC/D,OAAOlI,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQ4I,kBAAkBA,CAACC,IAAkB;IAC3C,IAAI,CAAC/D,YAAY,CAACgD,IAAI,CAACe,IAAI,CAAC;IAC5B,IAAI,CAACC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;EACA;EACA;EAEA;;;EAGQ3B,aAAaA,CAAA;IACnB,IAAI,CAAC4B,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;IACpE,IAAI,CAACA,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;EAClE;EAEA;;;;;EAKQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAAC9C,MAAM,CAAC0C,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAAC3C,SAAS,CAACyC,IAAI,CAAC,GAAG,KAAK;MAE5BE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAAC9C,SAAS,CAACyC,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;KACH,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;;EAKAY,IAAIA,CAACE,IAAY,EAAEM,IAAA,GAAgB,KAAK;IACtC,IAAI,IAAI,CAAC9C,KAAK,EAAE;MACd;;IAGF,IAAI;MACF,MAAM+C,KAAK,GAAG,IAAI,CAACjD,MAAM,CAAC0C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGFA,KAAK,CAACD,IAAI,GAAGA,IAAI;MAEjB,IAAI,CAAC,IAAI,CAAC/C,SAAS,CAACyC,IAAI,CAAC,EAAE;QACzBO,KAAK,CAACC,WAAW,GAAG,CAAC;QACrBD,KAAK,CAACT,IAAI,EAAE,CAACW,KAAK,CAAEvB,KAAK,IAAI;UAC3B;QAAA,CACD,CAAC;QACF,IAAI,CAAC3B,SAAS,CAACyC,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;EAIAwB,IAAIA,CAACV,IAAY;IACf,IAAI;MACF,MAAMO,KAAK,GAAG,IAAI,CAACjD,MAAM,CAAC0C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGF,IAAI,IAAI,CAAChD,SAAS,CAACyC,IAAI,CAAC,EAAE;QACxBO,KAAK,CAACI,KAAK,EAAE;QACbJ,KAAK,CAACC,WAAW,GAAG,CAAC;QACrB,IAAI,CAACjD,SAAS,CAACyC,IAAI,CAAC,GAAG,KAAK;;KAE/B,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;EAGA0B,aAAaA,CAAA;IACXC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxD,MAAM,CAAC,CAACoB,OAAO,CAAEsB,IAAI,IAAI;MACxC,IAAI,CAACU,IAAI,CAACV,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;;EAIAe,QAAQA,CAACvD,KAAc;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAElB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACoD,aAAa,EAAE;;EAExB;EAEA;;;;EAIAI,OAAOA,CAAA;IACL,OAAO,IAAI,CAACxD,KAAK;EACnB;EAEA;;;EAGAyD,qBAAqBA,CAAA;IACnBC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAE1E,IAAI,IAAI,CAAC3D,KAAK,EAAE;MACd0D,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF;IACA,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,MAAMC,UAAU,GAAGJ,YAAY,CAACK,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAGN,YAAY,CAACO,UAAU,EAAE;MAE1C;MACAH,UAAU,CAACI,IAAI,GAAG,MAAM;MACxBJ,UAAU,CAACK,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEV,YAAY,CAACZ,WAAW,CAAC,CAAC,CAAC;MAEpE;MACAkB,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEV,YAAY,CAACZ,WAAW,CAAC;MACzDkB,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHZ,YAAY,CAACZ,WAAW,GAAG,IAAI,CAChC;MACDkB,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,CAAC,EAAEZ,YAAY,CAACZ,WAAW,GAAG,GAAG,CAAC;MAExE;MACAgB,UAAU,CAACS,OAAO,CAACP,QAAQ,CAAC;MAC5BA,QAAQ,CAACO,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;MAE1C;MACAV,UAAU,CAACW,KAAK,CAACf,YAAY,CAACZ,WAAW,CAAC;MAC1CgB,UAAU,CAACd,IAAI,CAACU,YAAY,CAACZ,WAAW,GAAG,GAAG,CAAC;MAE/CU,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;KACtE,CAAC,OAAOjC,KAAK,EAAE;MACdgC,OAAO,CAAChC,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;MAED;MACA,IAAI;QACF,MAAMgB,KAAK,GAAG,IAAIC,KAAK,CAAC,gCAAgC,CAAC;QACzDD,KAAK,CAACkC,MAAM,GAAG,GAAG,CAAC,CAAC;QACpBlC,KAAK,CAACJ,IAAI,EAAE,CAACW,KAAK,CAAE4B,GAAG,IAAI;UACzBnB,OAAO,CAAChC,KAAK,CACX,2DAA2D,EAC3DmD,GAAG,CACJ;QACH,CAAC,CAAC;OACH,CAAC,OAAOC,UAAU,EAAE;QACnBpB,OAAO,CAAChC,KAAK,CACX,8DAA8D,EAC9DoD,UAAU,CACX;;;EAGP;EACA;EACA;EACA;EAEA;;;;;;;;EAQAC,gBAAgBA,CACdC,UAAkB,EAClBC,SAAe,EACfC,cAAuB,EACvBC,QAAiB;IAEjB,IAAI,CAACzH,MAAM,CAAC0H,KAAK,CACf,kDAAkDJ,UAAU,eAAeG,QAAQ,GAAG,CACvF;IAED;IACA,IAAI,CAACF,SAAS,IAAIA,SAAS,CAACI,IAAI,KAAK,CAAC,EAAE;MACtC,IAAI,CAAC3H,MAAM,CAACgE,KAAK,CAAC,qCAAqC,CAAC;MACxD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,oBAAoB,CAAC,CAAC;;IAG1D;IACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACV,SAAS,CAAC,EAAE,iBAAiBM,SAAS,OAAO,EAAE;MACzEnB,IAAI,EAAE,YAAY;MAClBwB,YAAY,EAAEL;KACf,CAAC;IAEF;IACA,IAAI,CAACG,SAAS,IAAIA,SAAS,CAACL,IAAI,KAAK,CAAC,EAAE;MACtC,IAAI,CAAC3H,MAAM,CAACgE,KAAK,CAAC,8CAA8C,CAAC;MACjE,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CACf,wCAAwCM,SAAS,CAAClD,IAAI,WAAWkD,SAAS,CAACL,IAAI,QAAQ,CACxF;IAED;IACA,MAAMQ,QAAQ,GAAG;MACfV,QAAQ,EAAEA,QAAQ,IAAI,CAAC;MACvBW,cAAc,EAAE,IAAI;MACpBP,SAAS,EAAEA;KACZ;IAED;IACA;IACA,OAAO,IAAI,CAACQ,WAAW,CACrBf,UAAU,EACV,GAAG;IAAE;IACLU,SAAS,EACTxL,WAAW,CAAC8L,aAAa,EACzBd,cAAc,EACd5E,SAAS,EACTuF,QAAQ,CACT;EACH;EAEA;;;;;EAKAI,SAASA,CAACC,QAAgB;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAM3D,KAAK,GAAG,IAAIC,KAAK,CAACuD,QAAQ,CAAC;MAEjCxD,KAAK,CAAC4D,OAAO,GAAG,MAAK;QACnBF,OAAO,EAAE;MACX,CAAC;MAED1D,KAAK,CAAC6D,OAAO,GAAI7E,KAAK,IAAI;QACxB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjE2E,MAAM,CAAC3E,KAAK,CAAC;MACf,CAAC;MAEDgB,KAAK,CAACJ,IAAI,EAAE,CAACW,KAAK,CAAEvB,KAAK,IAAI;QAC3B,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjE2E,MAAM,CAAC3E,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;EAIA8E,gBAAgBA,CAAA;IACd,IAAI,CAAC9I,MAAM,CAAC0H,KAAK,CAAC,yCAAyC,CAAC;IAE5D,OAAO,IAAI,CAAC3H,MAAM,CACfgJ,UAAU,CAA+B;MACxCxE,KAAK,EAAE3E,wBAAwB;MAC/BoJ,WAAW,EAAE,cAAc,CAAE;KAC9B,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMC,aAAa,GAAGD,MAAM,CAACzE,IAAI,EAAEqE,gBAAgB,IAAI,EAAE;MACzD,IAAI,CAAC9I,MAAM,CAAC0H,KAAK,CACf,8BAA8ByB,aAAa,CAACC,MAAM,iBAAiB,CACpE;MACD,OAAOD,aAAa;IACtB,CAAC,CAAC,EACFhN,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,iDAAiD,EACjDA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EACA;EACAyB,WAAWA,CACTC,QAAgB,EAChBhC,UAAkB,EAClBE,cAAsB,EACtB+B,IAAA,GAAe,CAAC,EAChB/G,KAAA,GAAgB,EAAE;IAElB,OAAO,IAAI,CAACzC,MAAM,CACfgJ,UAAU,CAA6B;MACtCxE,KAAK,EAAE9F,kBAAkB;MACzB+K,SAAS,EAAE;QAAEF,QAAQ;QAAEhC,UAAU;QAAEE,cAAc;QAAEhF,KAAK;QAAE+G;MAAI,CAAE;MAChEP,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMO,QAAQ,GAAGP,MAAM,CAACzE,IAAI,EAAE4E,WAAW,IAAI,EAAE;MAC/C,OAAOI,QAAQ,CAACvN,GAAG,CAAEwN,GAAG,IAAK,IAAI,CAACC,gBAAgB,CAACD,GAAG,CAAC,CAAC;IAC1D,CAAC,CAAC,EACFvN,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACpD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EACAgC,WAAWA,CAACC,SAAiB,EAAEC,UAAkB;IAC/C,OAAO,IAAI,CAAC/J,MAAM,CACfgK,MAAM,CAA2B;MAChCC,QAAQ,EAAEzL,qBAAqB;MAC/BiL,SAAS,EAAE;QAAEK,SAAS;QAAEC;MAAU;KACnC,CAAC,CACDtF,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEmF,WAAW,EAAE;QAC7B,MAAM,IAAIhC,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAO,IAAI,CAAC+B,gBAAgB,CAACT,MAAM,CAACzE,IAAI,CAACmF,WAAW,CAAC;IACvD,CAAC,CAAC,EACFzN,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAClD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEAqC,aAAaA,CAACJ,SAAiB;IAC7B,OAAO,IAAI,CAAC9J,MAAM,CACfgK,MAAM,CAA6B;MAClCC,QAAQ,EAAExL,uBAAuB;MACjCgL,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDrF,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEwF,aAAa,EAAE;QAC/B,MAAM,IAAIrC,KAAK,CAAC,0BAA0B,CAAC;;MAE7C,OAAO,IAAI,CAAC+B,gBAAgB,CAACT,MAAM,CAACzE,IAAI,CAACwF,aAAa,CAAC;IACzD,CAAC,CAAC,EACF9N,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACnD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EAEAS,WAAWA,CACTf,UAAkB,EAClB4C,OAAe,EACfC,IAAW,EACXC,WAAA,GAA2B5N,WAAW,CAAC6N,IAAI,EAC3C7C,cAAuB,EACvB8C,OAAgB,EAChBnC,QAAc;IAEd,IAAI,CAACnI,MAAM,CAACuK,IAAI,CACd,wCAAwCjD,UAAU,cAAc,CAAC,CAAC6C,IAAI,EAAE,CACzE;IACD,IAAI,CAACnK,MAAM,CAAC0H,KAAK,CACf,sCAAsCwC,OAAO,EAAEM,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAC7DN,OAAO,EAAEd,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG,EACjC,GAAG,CACJ;IAED;IACA,MAAMqB,KAAK,GAAGtH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACpD,MAAM,CAAC0H,KAAK,CACf,uEAAuE,CAAC,CAAC+C,KAAK,EAAE,CACjF;IAED;IACA,IAAIC,gBAAgB,GAAGN,WAAW;IAElC;IACA,IAAID,IAAI,EAAE;MACR;MACA,IAAIC,WAAW,KAAK5N,WAAW,CAAC8L,aAAa,EAAE;QAC7CoC,gBAAgB,GAAGlO,WAAW,CAAC8L,aAAa;QAC5C,IAAI,CAACtI,MAAM,CAAC0H,KAAK,CAAC,oDAAoD,CAAC;;MAEzE;MAAA,KACK,IAAI0C,WAAW,KAAK5N,WAAW,CAAC6N,IAAI,EAAE;QACzC,IAAIF,IAAI,CAACzD,IAAI,CAACiE,UAAU,CAAC,QAAQ,CAAC,EAAE;UAClCD,gBAAgB,GAAGlO,WAAW,CAACoO,KAAK;SACrC,MAAM,IAAIT,IAAI,CAACzD,IAAI,CAACiE,UAAU,CAAC,QAAQ,CAAC,EAAE;UACzCD,gBAAgB,GAAGlO,WAAW,CAACqO,KAAK;SACrC,MAAM,IAAIV,IAAI,CAACzD,IAAI,CAACiE,UAAU,CAAC,QAAQ,CAAC,EAAE;UACzC;UACA,IAAIxC,QAAQ,IAAIA,QAAQ,CAACC,cAAc,EAAE;YACvCsC,gBAAgB,GAAGlO,WAAW,CAAC8L,aAAa;WAC7C,MAAM;YACLoC,gBAAgB,GAAGlO,WAAW,CAACsO,KAAK;;SAEvC,MAAM;UACLJ,gBAAgB,GAAGlO,WAAW,CAACuO,IAAI;;;;IAKzC,IAAI,CAAC/K,MAAM,CAAC0H,KAAK,CACf,6CAA6CgD,gBAAgB,EAAE,CAChE;IAED;IACA;IACA,MAAMlB,SAAS,GAAQ;MACrBlC,UAAU;MACV4C,OAAO;MACPxD,IAAI,EAAEgE,gBAAgB,CAAE;KACzB;IAED;IACA;IACA,IAAIlB,SAAS,CAAC9C,IAAI,EAAE;MAClBf,MAAM,CAACqF,cAAc,CAACxB,SAAS,EAAE,MAAM,EAAE;QACvCyB,KAAK,EAAEP,gBAAgB;QACvBQ,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE;OACX,CAAC;;IAGJ;IACA,IAAIhD,QAAQ,EAAE;MACZqB,SAAS,CAACrB,QAAQ,GAAGA,QAAQ;MAC7B,IAAI,CAACnI,MAAM,CAAC0H,KAAK,CAAC,qCAAqC,EAAES,QAAQ,CAAC;;IAGpE,IAAIgC,IAAI,EAAE;MACRX,SAAS,CAACW,IAAI,GAAGA,IAAI;MACrB,IAAI,CAACnK,MAAM,CAAC0H,KAAK,CACf,mCAAmCyC,IAAI,CAACrF,IAAI,WAAWqF,IAAI,CAACxC,IAAI,WAAWwC,IAAI,CAACzD,IAAI,kBAAkBgE,gBAAgB,EAAE,CACzH;;IAEH,IAAIlD,cAAc,EAAE;MAClBgC,SAAS,CAAChC,cAAc,GAAGA,cAAc;MACzC,IAAI,CAACxH,MAAM,CAAC0H,KAAK,CACf,iDAAiDF,cAAc,EAAE,CAClE;;IAEH,IAAI8C,OAAO,EAAE;MACXd,SAAS,CAACc,OAAO,GAAGA,OAAO;MAC3B,IAAI,CAACtK,MAAM,CAAC0H,KAAK,CAAC,yCAAyC4C,OAAO,EAAE,CAAC;;IAGvE,MAAMc,OAAO,GAAGjB,IAAI,GAAG;MAAEkB,YAAY,EAAE,IAAI;MAAElB;IAAI,CAAE,GAAGvH,SAAS;IAE/D,IAAI,CAAC5C,MAAM,CAAC0H,KAAK,CACf,2DAA2D,EAC3D8B,SAAS,CACV;IAED,OAAO,IAAI,CAACzJ,MAAM,CACfgK,MAAM,CAAsB;MAC3BC,QAAQ,EAAEjN,qBAAqB;MAC/ByM,SAAS;MACT4B;KACD,CAAC,CACD5G,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAAClJ,MAAM,CAAC0H,KAAK,CACf,4DAA4D,CAC7D;MAED,IAAI,CAACwB,MAAM,CAACzE,IAAI,EAAE4D,WAAW,EAAE;QAC7B,IAAI,CAACrI,MAAM,CAACgE,KAAK,CACf,6DAA6D,CAC9D;QACD,MAAM,IAAI4D,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI;QACF,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CACf,uDAAuD,EACvDwB,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CACxB;QACD,MAAMiD,iBAAiB,GAAG,IAAI,CAAC3B,gBAAgB,CAC7CT,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CACxB;QAED,IAAI,CAACrI,MAAM,CAACuK,IAAI,CACd,0DAA0De,iBAAiB,CAAC5H,EAAE,EAAE,CACjF;QAED;QACA,IAAI,CAACzD,IAAI,CAACsL,GAAG,CAAC,MAAK;UACjB;UACA,IAAI,CAACvL,MAAM,CAAC0H,KAAK,CAAC,qCAAqC,CAAC;QAC1D,CAAC,CAAC;QAEF,OAAO4D,iBAAiB;OACzB,CAAC,OAAOE,kBAAkB,EAAE;QAC3B,IAAI,CAACxL,MAAM,CAACgE,KAAK,CACf,+CAA+C,EAC/CwH,kBAAkB,CACnB;QAED;QACA,MAAMC,cAAc,GAAY;UAC9B/H,EAAE,EAAEwF,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CAAC3E,EAAE,IAAI,OAAO,GAAGoE,IAAI,CAACC,GAAG,EAAE;UACtDmC,OAAO,EAAEhB,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CAAC6B,OAAO,IAAI,EAAE;UAC9CxD,IAAI,EAAEwC,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CAAC3B,IAAI,IAAIlK,WAAW,CAAC6N,IAAI;UACtDxC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrB4D,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE;YACNjI,EAAE,EAAE,IAAI,CAACkI,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEb;QAED,IAAI,CAAC7L,MAAM,CAACuK,IAAI,CACd,+CAA+CkB,cAAc,CAAC/H,EAAE,EAAE,CACnE;QACD,OAAO+H,cAAc;;IAEzB,CAAC,CAAC,EACFtP,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MACnE,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEAkE,iBAAiBA,CAACjC,SAAiB;IACjC,OAAO,IAAI,CAAC9J,MAAM,CACfgK,MAAM,CAAqB;MAC1BC,QAAQ,EAAEhN,qBAAqB;MAC/BwM,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDrF,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEqH,iBAAiB,EACjC,MAAM,IAAIlE,KAAK,CAAC,gCAAgC,CAAC;MACnD,OAAO;QACL,GAAGsB,MAAM,CAACzE,IAAI,CAACqH,iBAAiB;QAChCC,MAAM,EAAE,IAAIjE,IAAI;OACjB;IACH,CAAC,CAAC,EACF3L,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEAoE,cAAcA,CAACnC,SAAiB,EAAEoC,KAAa;IAC7C,OAAO,IAAI,CAAClM,MAAM,CACfgK,MAAM,CAAyB;MAC9BC,QAAQ,EAAElM,yBAAyB;MACnC0L,SAAS,EAAE;QAAEK,SAAS;QAAEoC;MAAK;KAC9B,CAAC,CACDzH,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEuH,cAAc,EAC9B,MAAM,IAAIpE,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAOsB,MAAM,CAACzE,IAAI,CAACuH,cAAc;IACnC,CAAC,CAAC,EACF7P,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEAsE,cAAcA,CACZrC,SAAiB,EACjBsC,eAAyB;IAEzB,OAAO,IAAI,CAACpM,MAAM,CACfgK,MAAM,CAAyB;MAC9BC,QAAQ,EAAEjM,wBAAwB;MAClCyL,SAAS,EAAE;QAAEK,SAAS;QAAEsC;MAAe;KACxC,CAAC,CACD3H,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEyH,cAAc,EAC9B,MAAM,IAAItE,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAOsB,MAAM,CAACzE,IAAI,CAACyH,cAAc,CAAChQ,GAAG,CAAEwN,GAAG,KAAM;QAC9C,GAAGA,GAAG;QACN7B,SAAS,EAAE6B,GAAG,CAAC7B,SAAS,GACpB,IAAI,CAACuE,aAAa,CAAC1C,GAAG,CAAC7B,SAAS,CAAC,GACjC,IAAIC,IAAI;OACb,CAAC,CAAC;IACL,CAAC,CAAC,EACF3L,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEAyE,UAAUA,CAACxC,SAAiB,EAAErC,cAAsB;IAClD,OAAO,IAAI,CAACzH,MAAM,CACfgK,MAAM,CAAqB;MAC1BC,QAAQ,EAAEhM,oBAAoB;MAC9BwL,SAAS,EAAE;QAAEK,SAAS;QAAErC;MAAc;KACvC,CAAC,CACDhD,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAE4H,UAAU,EAC1B,MAAM,IAAIzE,KAAK,CAAC,uBAAuB,CAAC;MAC1C,OAAO;QACL,GAAGsB,MAAM,CAACzE,IAAI,CAAC4H,UAAU;QACzBC,QAAQ,EAAE,IAAIxE,IAAI;OACnB;IACH,CAAC,CAAC,EACF3L,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEA2E,cAAcA,CACZhI,KAAa,EACbiD,cAAuB,EACvBgF,OAAA,GAAyB,EAAE;IAE3B,OAAO,IAAI,CAACzM,MAAM,CACfgJ,UAAU,CAAyB;MAClCxE,KAAK,EAAEjH,qBAAqB;MAC5BkM,SAAS,EAAE;QACTjF,KAAK;QACLiD,cAAc;QACd,GAAGgF,OAAO;QACVC,QAAQ,EAAE,IAAI,CAAC/J,eAAe,CAAC8J,OAAO,CAACC,QAAQ,CAAC;QAChDC,MAAM,EAAE,IAAI,CAAChK,eAAe,CAAC8J,OAAO,CAACE,MAAM;OAC5C;MACD1D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CACAgN,MAAM,IACLA,MAAM,CAACzE,IAAI,EAAE8H,cAAc,EAAErQ,GAAG,CAAEwN,GAAG,KAAM;MACzC,GAAGA,GAAG;MACN7B,SAAS,EAAE,IAAI,CAAC8E,QAAQ,CAACjD,GAAG,CAAC7B,SAAS,CAAC;MACvC8D,MAAM,EAAE,IAAI,CAACiB,aAAa,CAAClD,GAAG,CAACiC,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDxP,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEAiF,iBAAiBA,CAACC,MAAc;IAC9B,OAAO,IAAI,CAAC/M,MAAM,CACfgJ,UAAU,CAA4B;MACrCxE,KAAK,EAAEhH,yBAAyB;MAChCiM,SAAS,EAAE;QAAEsD;MAAM,CAAE;MACrB9D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CACAgN,MAAM,IACLA,MAAM,CAACzE,IAAI,EAAEoI,iBAAiB,EAAE3Q,GAAG,CAAEwN,GAAG,KAAM;MAC5C,GAAGA,GAAG;MACN7B,SAAS,EAAE,IAAI,CAAC8E,QAAQ,CAACjD,GAAG,CAAC7B,SAAS,CAAC;MACvC8D,MAAM,EAAE,IAAI,CAACiB,aAAa,CAAClD,GAAG,CAACiC,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDxP,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEAmF,qBAAqBA,CAACvF,cAAsB;IAC1C,IAAI,CAACtH,kBAAkB,CAAC0D,IAAI,CAAC4D,cAAc,CAAC;EAC9C;EAEAwF,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACjN,MAAM,CACfgJ,UAAU,CAA2B;MACpCxE,KAAK,EAAE5H,uBAAuB;MAC9BqM,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAM+D,aAAa,GAAG/D,MAAM,CAACzE,IAAI,EAAEuI,gBAAgB,IAAI,EAAE;MACzD,OAAOC,aAAa,CAAC/Q,GAAG,CAAEgR,IAAI,IAAK,IAAI,CAACC,qBAAqB,CAACD,IAAI,CAAC,CAAC;IACtE,CAAC,CAAC,EACF/Q,UAAU,CAAE6H,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEAwF,eAAeA,CACb5F,cAAsB,EACtBhF,KAAc,EACd+G,IAAa;IAEb,IAAI,CAACvJ,MAAM,CAACuK,IAAI,CACd,0CAA0C/C,cAAc,YAAYhF,KAAK,WAAW+G,IAAI,EAAE,CAC3F;IAED,MAAMC,SAAS,GAAQ;MAAEhC;IAAc,CAAE;IAEzC;IACA,IAAIhF,KAAK,KAAKI,SAAS,EAAE;MACvB4G,SAAS,CAAChH,KAAK,GAAGA,KAAK;KACxB,MAAM;MACLgH,SAAS,CAAChH,KAAK,GAAG,EAAE,CAAC,CAAC;;IAGxB;IACA,IAAI+G,IAAI,KAAK3G,SAAS,EAAE;MACtB;MACA,MAAMyK,MAAM,GAAG,CAAC9D,IAAI,GAAG,CAAC,IAAIC,SAAS,CAAChH,KAAK;MAC3CgH,SAAS,CAAC6D,MAAM,GAAGA,MAAM;MACzB,IAAI,CAACrN,MAAM,CAAC0H,KAAK,CACf,uCAAuC2F,MAAM,eAAe9D,IAAI,eAAeC,SAAS,CAAChH,KAAK,EAAE,CACjG;KACF,MAAM;MACLgH,SAAS,CAAC6D,MAAM,GAAG,CAAC,CAAC,CAAC;;;IAGxB,IAAI,CAACrN,MAAM,CAAC0H,KAAK,CACf,uDAAuD8B,SAAS,CAAChH,KAAK,YAAYgH,SAAS,CAAC6D,MAAM,EAAE,CACrG;IAED,OAAO,IAAI,CAACtN,MAAM,CACfgJ,UAAU,CAA0B;MACnCxE,KAAK,EAAEzH,sBAAsB;MAC7B0M,SAAS,EAAEA,SAAS;MACpBR,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAAClJ,MAAM,CAAC0H,KAAK,CACf,kDAAkD,EAClDwB,MAAM,CACP;MAED,MAAMgE,IAAI,GAAGhE,MAAM,CAACzE,IAAI,EAAE2I,eAAe;MACzC,IAAI,CAACF,IAAI,EAAE;QACT,IAAI,CAAClN,MAAM,CAACgE,KAAK,CACf,4CAA4CwD,cAAc,EAAE,CAC7D;QACD,MAAM,IAAII,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CACf,8CAA8CF,cAAc,EAAE,CAC/D;MACD,MAAM8F,sBAAsB,GAAG,IAAI,CAACH,qBAAqB,CAACD,IAAI,CAAC;MAE/D,IAAI,CAAClN,MAAM,CAACuK,IAAI,CACd,sDAAsD/C,cAAc,mBAClE8F,sBAAsB,CAACC,YAAY,EAAEnE,MAAM,IAAI,CACjD,eAAekE,sBAAsB,CAAC7D,QAAQ,EAAEL,MAAM,IAAI,CAAC,EAAE,CAC9D;MACD,OAAOkE,sBAAsB;IAC/B,CAAC,CAAC,EACFnR,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,+CAA+C,EAC/CA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EAEA4F,kBAAkBA,CAACV,MAAc;IAC/B,IAAI,CAAC9M,MAAM,CAACuK,IAAI,CACd,qDAAqDuC,MAAM,EAAE,CAC9D;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACgE,KAAK,CACf,kEAAkE,CACnE;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,8CAA8C,CAAC,CAChE;;IAGH,OAAO,IAAI,CAAC7H,MAAM,CACfgK,MAAM,CAAuC;MAC5CC,QAAQ,EAAEnL,4BAA4B;MACtC2K,SAAS,EAAE;QAAEsD;MAAM;KACpB,CAAC,CACDtI,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAAClJ,MAAM,CAAC0H,KAAK,CACf,kDAAkD,EAClDwB,MAAM,CACP;MAED,MAAMuE,YAAY,GAAGvE,MAAM,CAACzE,IAAI,EAAE+I,kBAAkB;MACpD,IAAI,CAACC,YAAY,EAAE;QACjB,IAAI,CAACzN,MAAM,CAACgE,KAAK,CACf,6DAA6D8I,MAAM,EAAE,CACtE;QACD,MAAM,IAAIlF,KAAK,CAAC,+BAA+B,CAAC;;MAGlD,IAAI;QACF,MAAM0F,sBAAsB,GAC1B,IAAI,CAACH,qBAAqB,CAACM,YAAY,CAAC;QAC1C,IAAI,CAACzN,MAAM,CAACuK,IAAI,CACd,uDAAuD+C,sBAAsB,CAAC5J,EAAE,EAAE,CACnF;QACD,OAAO4J,sBAAsB;OAC9B,CAAC,OAAOtJ,KAAK,EAAE;QACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,0DAA0D,EAC1DA,KAAK,CACN;QACD,MAAM,IAAI4D,KAAK,CAAC,uCAAuC,CAAC;;IAE5D,CAAC,CAAC,EACFzL,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,0DAA0D8I,MAAM,GAAG,EACnE9I,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,kCAAkC5D,KAAK,CAAC0J,OAAO,EAAE,CAAC,CACnE;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,uBAAuBA,CAACb,MAAc;IACpC,IAAI,CAAC9M,MAAM,CAACuK,IAAI,CACd,gEAAgEuC,MAAM,EAAE,CACzE;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACgE,KAAK,CACf,sEAAsE,CACvE;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,kDAAkD,CAAC,CACpE;;IAGH;IACA,OAAO,IAAI,CAACoF,gBAAgB,EAAE,CAACxI,IAAI,CACjCtI,GAAG,CAAE+Q,aAAa,IAAI;MACpB;MACA,MAAMW,aAAa,GAAG,IAAI,CAAChC,gBAAgB,EAAE;MAE7C;MACA,MAAMiC,oBAAoB,GAAGZ,aAAa,CAACa,IAAI,CAAEZ,IAAI,IAAI;QACvD,IAAIA,IAAI,CAACa,OAAO,EAAE,OAAO,KAAK;QAE9B;QACA,MAAMC,cAAc,GAClBd,IAAI,CAACK,YAAY,EAAErR,GAAG,CAAE+R,CAAC,IAAKA,CAAC,CAACvK,EAAE,IAAIuK,CAAC,CAACC,GAAG,CAAC,IAAI,EAAE;QACpD,OACEF,cAAc,CAACG,QAAQ,CAACrB,MAAM,CAAC,IAC/BkB,cAAc,CAACG,QAAQ,CAACP,aAAa,CAAC;MAE1C,CAAC,CAAC;MAEF,IAAIC,oBAAoB,EAAE;QACxB,IAAI,CAAC7N,MAAM,CAACuK,IAAI,CACd,iDAAiDsD,oBAAoB,CAACnK,EAAE,EAAE,CAC3E;QACD,OAAOmK,oBAAoB;;MAG7B;MACA,MAAM,IAAIjG,KAAK,CAAC,gCAAgC,CAAC;IACnD,CAAC,CAAC,EACFzL,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACuK,IAAI,CACd,sEAAsEvG,KAAK,CAAC0J,OAAO,EAAE,CACtF;MACD,OAAO,IAAI,CAACF,kBAAkB,CAACV,MAAM,CAAC;IACxC,CAAC,CAAC,CACH;EACH;EAYAsB,gBAAgBA,CACdC,OAAO,GAAG,KAAK,EACf9E,IAAI,GAAG,CAAC,EACR/G,KAAK,GAAG,EAAE;IAEV,IAAI,CAACxC,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,oCAAoC8D,OAAO,WAAW9E,IAAI,YAAY/G,KAAK,EAAE,CAC9E;IACD,IAAI,CAACxC,MAAM,CAAC0H,KAAK,CAAC,gBAAgB,EAAE,aAAa,EAAE;MACjDnD,KAAK,EAAE3H;KACR,CAAC;IAEF;IACA;IACA,IAAIyR,OAAO,EAAE;MACX,IAAI,CAACrO,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,qCAAqC,CACtC;MACD,IAAI,CAACnF,sBAAsB,CAACT,WAAW,GAAG,CAAC;MAC3C,IAAI,CAACS,sBAAsB,CAACE,oBAAoB,GAAG,IAAI;;IAGzD;IACA,IAAI,CAACF,sBAAsB,CAACT,WAAW,GAAGyH,IAAI;IAC9C,IAAI,CAAChH,sBAAsB,CAACC,KAAK,GAAGA,KAAK;IAEzC;IACA,MAAM8L,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D,IAAI,CAACvO,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,SAAS4G,sBAAsB,CAAC3G,IAAI,2CAA2C,CAChF;IAED,OAAO,IAAI,CAAC5H,MAAM,CACfgJ,UAAU,CAA+B;MACxCxE,KAAK,EAAE3H,uBAAuB;MAC9B4M,SAAS,EAAE;QACTD,IAAI,EAAEA,IAAI;QACV/G,KAAK,EAAEA;OACR;MACDwG,WAAW,EAAEqF,OAAO,GAAG,cAAc,GAAG;KACzC,CAAC,CACDpF,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAAClJ,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,iCAAiC,CAClC;MAED,IAAIwB,MAAM,CAACsF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBkF,MAAM,CAACsF,MAAM,CACd;QACD,MAAM,IAAI5G,KAAK,CAACsB,MAAM,CAACsF,MAAM,CAACtS,GAAG,CAAEuS,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,MAAMvO,aAAa,GAAG+I,MAAM,CAACzE,IAAI,EAAEkK,oBAAoB,IAAI,EAAE;MAC7D,IAAI,CAAC3O,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,YAAYvH,aAAa,CAACiJ,MAAM,uCAAuCG,IAAI,EAAE,CAC9E;MAED;MACA,IAAI,CAAChH,sBAAsB,CAACE,oBAAoB,GAC9CtC,aAAa,CAACiJ,MAAM,IAAI5G,KAAK;MAE/B,IAAIrC,aAAa,CAACiJ,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACpJ,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,uCAAuC,CACxC;QACD,IAAI,CAAChI,sBAAsB,CAACE,oBAAoB,GAAG,KAAK;;MAG1D;MACA,MAAMmM,qBAAqB,GAAGzO,aAAa,CAAC9D,MAAM,CAC/CwS,KAAK,IAAK,CAACP,sBAAsB,CAACQ,GAAG,CAACD,KAAK,CAACnL,EAAE,CAAC,CACjD;MAED,IAAI,CAAC1D,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,gBACEvH,aAAa,CAACiJ,MAAM,GAAGwF,qBAAqB,CAACxF,MAC/C,wBAAwB,CACzB;MAED;MACAwF,qBAAqB,CAACpL,OAAO,CAAC,CAACqL,KAAK,EAAEE,KAAK,KAAI;QAC7C/I,OAAO,CAACC,GAAG,CAAC,gBAAgB8I,KAAK,GAAG,CAAC,UAAUxF,IAAI,IAAI,EAAE;UACvD7F,EAAE,EAAEmL,KAAK,CAACnL,EAAE,IAAKmL,KAAa,CAACX,GAAG;UAClCxH,IAAI,EAAEmI,KAAK,CAACnI,IAAI;UAChBwD,OAAO,EAAE2E,KAAK,CAAC3E,OAAO;UACtBwB,MAAM,EAAEmD,KAAK,CAACnD;SACf,CAAC;MACJ,CAAC,CAAC;MAEF;MACA;MACA,IAAI,CAACsD,WAAW,CAACJ,qBAAqB,CAAC;MAEvC;MACA,MAAMK,mBAAmB,GAAGpL,KAAK,CAACtH,IAAI,CACpC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAChC;MAED;MACA,MAAMoL,mBAAmB,GACvB,IAAI,CAACC,uBAAuB,CAACF,mBAAmB,CAAC;MAEnDjJ,OAAO,CAACC,GAAG,CACT,cAAciJ,mBAAmB,CAAC9F,MAAM,kDAAkD,CAC3F;MAED;MACA,IAAI,CAACjJ,aAAa,CAACyD,IAAI,CAACsL,mBAAmB,CAAC;MAE5C;MACA,IAAI,CAACnL,iBAAiB,EAAE;MAExB;MACA,IAAI,CAACqL,+BAA+B,EAAE;MAEtC,OAAOH,mBAAmB;IAC5B,CAAC,CAAC,EACF9S,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MAED,IAAIA,KAAK,CAACqL,aAAa,EAAE;QACvB,IAAI,CAACrP,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACqL,aAAa,CACpB;;MAGH,IAAIrL,KAAK,CAACsL,YAAY,EAAE;QACtB,IAAI,CAACtP,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACsL,YAAY,CACnB;;MAGH,OAAOvT,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKQ2G,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMgB,UAAU,GAAG,IAAIC,GAAG,EAAU;MACpC,MAAMtM,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAEhE;MACA,IAAI,CAACF,kBAAkB,EAAE;QACvB,OAAOqM,UAAU;;MAGnB;MACA,MAAME,oBAAoB,GAAG,IAAID,GAAG,CAClCnM,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAC,CAAChH,GAAG,CAAEwT,CAAe,IAAKA,CAAC,CAAChM,EAAE,CAAC,CAC9D;MAED;MACA,MAAMiM,mBAAmB,GACvB,IAAI,CAAC5P,MAAM,CAAC6P,MAAM,CAACC,SAAS,CAA+B;QACzDtL,KAAK,EAAE3H;OACR,CAAC,EAAE+R,oBAAoB,IAAI,EAAE;MAEhC;MACAgB,mBAAmB,CAACnM,OAAO,CAAEC,YAAY,IAAI;QAC3C,IAAI,CAACgM,oBAAoB,CAACX,GAAG,CAACrL,YAAY,CAACC,EAAE,CAAC,EAAE;UAC9C6L,UAAU,CAACO,GAAG,CAACrM,YAAY,CAACC,EAAE,CAAC;;MAEnC,CAAC,CAAC;MAEF,OAAO6L,UAAU;KAClB,CAAC,OAAOvL,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,qEAAqE,EACrEA,KAAK,CACN;MACD,OAAO,IAAIwL,GAAG,EAAU;;EAE5B;EAEA;EACA/M,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACF,sBAAsB,CAACE,oBAAoB;EACzD;EAEA;EACAsN,qBAAqBA,CAAA;IACnB,MAAMC,QAAQ,GAAG,IAAI,CAACzN,sBAAsB,CAACT,WAAW,GAAG,CAAC;IAC5D,OAAO,IAAI,CAACsM,gBAAgB,CAC1B,KAAK,EACL4B,QAAQ,EACR,IAAI,CAACzN,sBAAsB,CAACC,KAAK,CAClC;EACH;EACAyN,mBAAmBA,CAACvM,EAAU;IAC5B,OAAO,IAAI,CAACxB,cAAc,CAACsC,IAAI,CAC7BtI,GAAG,CAAEiE,aAAa,IAAKA,aAAa,CAAC2N,IAAI,CAAE4B,CAAC,IAAKA,CAAC,CAAChM,EAAE,KAAKA,EAAE,CAAC,CAAC,EAC9DvH,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACvD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACH;EACAsI,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC/P,aAAa,CAAC8K,KAAK,EAAE7B,MAAM,IAAI,CAAC;EAC9C;EACA+G,0BAA0BA,CAACC,cAAsB;IAC/C,OAAO,IAAI,CAACrQ,MAAM,CACfwE,KAAK,CAAkC;MACtCA,KAAK,EAAE7F,8BAA8B;MACrC8K,SAAS,EAAE;QAAE9F,EAAE,EAAE0M;MAAc,CAAE;MACjCpH,WAAW,EAAE;KACd,CAAC,CACDxE,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAE0L,0BAA0B,IAAI,EAAE,CAAC,EAC9DhU,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EACAyI,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACnO,cAAc,CAACsC,IAAI,CAC7BtI,GAAG,CAAEiE,aAAa,IAAKA,aAAa,CAAC9D,MAAM,CAAEqT,CAAC,IAAK,CAACA,CAAC,CAAChE,MAAM,CAAC,CAAC,CAC/D;EACH;EAEA;;;;;EAKA4E,kBAAkBA,CAChBF,cAAsB;IAEtB,IAAI,CAACpQ,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,kCAAkC0I,cAAc,EAAE,CACnD;IAED,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACpQ,MAAM,CAACuQ,IAAI,CAAC,gBAAgB,EAAE,6BAA6B,CAAC;MACjE,OAAOxU,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,MAAM4I,YAAY,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAACL,cAAc,CAAC,CAAC;IAExE;IACA,OAAO,IAAI,CAACrQ,MAAM,CACfgK,MAAM,CAAgE;MACrEC,QAAQ,EAAElL,4BAA4B;MACtC0K,SAAS,EAAE;QAAE4G;MAAc;KAC5B,CAAC,CACD5L,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMwH,QAAQ,GAAGxH,MAAM,CAACzE,IAAI,EAAE6L,kBAAkB;MAChD,IAAI,CAACI,QAAQ,EAAE;QACb,MAAM,IAAI9I,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BgJ,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFvU,UAAU,CAAE6H,KAAK,IACf,IAAI,CAAC2M,mBAAmB,CAAC3M,KAAK,EAAE,mCAAmC,EAAE;MACnE4M,OAAO,EAAE,IAAI;MACblD,OAAO,EAAE;KACV,CAAC,CACH,CACF;EACL;EAEA;;;;EAIQ0B,+BAA+BA,CAAA;IACrC,IAAI;MACF,MAAMjP,aAAa,GAAG0D,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAAC;MACjEX,YAAY,CAAC0N,OAAO,CAAC,eAAe,EAAExN,IAAI,CAACyN,SAAS,CAAC3Q,aAAa,CAAC,CAAC;MACpE,IAAI,CAACH,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,uCAAuC,CACxC;KACF,CAAC,OAAO1D,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iDAAiD,EACjDA,KAAK,CACN;;EAEL;EAEA;;;;EAIA+M,sBAAsBA,CAAA;IAKpB,IAAI,CAAC/Q,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,yCAAyC,CAC1C;IAED;IACA,MAAMsJ,KAAK,GAAG,IAAI,CAAC5Q,iBAAiB,CAACuH,IAAI;IACzC,MAAMsJ,kBAAkB,GAAGpN,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAACwF,IAAI,EAAE,CAAC;IACpE,IAAI,CAAC6K,4BAA4B,CAACQ,kBAAkB,CAAC;IAErD;IACA,OAAO,IAAI,CAAClR,MAAM,CACfgK,MAAM,CAMJ;MACDC,QAAQ,EAAEhL;KACX,CAAC,CACDwF,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMwH,QAAQ,GAAGxH,MAAM,CAACzE,IAAI,EAAEsM,sBAAsB;MACpD,IAAI,CAACL,QAAQ,EAAE;QACb,MAAM,IAAI9I,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,yDAAyD,EACzDgJ,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFvU,UAAU,CAAE6H,KAAK,IACf,IAAI,CAAC2M,mBAAmB,CACtB3M,KAAK,EACL,4CAA4C,EAC5C;MACE4M,OAAO,EAAE,IAAI;MACbI,KAAK;MACLtD,OAAO,EAAE,GAAGsD,KAAK;KAClB,CACF,CACF,CACF;EACL;EAEA;;;;;EAKAE,2BAA2BA,CACzBC,eAAyB;IAEzB,IAAI,CAACnR,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,kBAAkByJ,eAAe,CAAC/H,MAAM,gBAAgB,CACzD;IAED,IAAI,CAAC+H,eAAe,IAAIA,eAAe,CAAC/H,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAACpJ,MAAM,CAACuQ,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOxU,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,iCAAiC,CAAC,CAAC;;IAGvE;IACA,MAAMoJ,KAAK,GAAG,IAAI,CAACP,4BAA4B,CAACU,eAAe,CAAC;IAEhE;IACA,OAAO,IAAI,CAACpR,MAAM,CACfgK,MAAM,CAMJ;MACDC,QAAQ,EAAEjL,sCAAsC;MAChDyK,SAAS,EAAE;QAAE2H;MAAe;KAC7B,CAAC,CACD3M,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMwH,QAAQ,GAAGxH,MAAM,CAACzE,IAAI,EAAEyM,2BAA2B;MACzD,IAAI,CAACR,QAAQ,EAAE;QACb,MAAM,IAAI9I,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCgJ,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFvU,UAAU,CAAE6H,KAAK,IACf,IAAI,CAAC2M,mBAAmB,CACtB3M,KAAK,EACL,0CAA0C,EAC1C;MACE4M,OAAO,EAAEI,KAAK,GAAG,CAAC;MAClBA,KAAK;MACLtD,OAAO,EAAE,GAAGsD,KAAK;KAClB,CACF,CACF,CACF;EACL;EACAI,wBAAwBA,CAAA;IAGtB,OAAO,IAAI,CAAClP,cAAc,CAACsC,IAAI,CAC7BtI,GAAG,CAAEiE,aAAa,IAAI;MACpB,MAAMkR,MAAM,GAAG,IAAIhR,GAAG,EAAoC;MAC1DF,aAAa,CAACqD,OAAO,CAAEqL,KAAK,IAAI;QAC9B,IAAI,CAACwC,MAAM,CAACvC,GAAG,CAACD,KAAK,CAACnI,IAAI,CAAC,EAAE;UAC3B2K,MAAM,CAAC1N,GAAG,CAACkL,KAAK,CAACnI,IAAI,EAAE,EAAE,CAAC;;QAE5B2K,MAAM,CAACC,GAAG,CAACzC,KAAK,CAACnI,IAAI,CAAC,EAAE6K,IAAI,CAAC1C,KAAK,CAAC;MACrC,CAAC,CAAC;MACF,OAAOwC,MAAM;IACf,CAAC,CAAC,CACH;EACH;EACAG,UAAUA,CAACL,eAAyB;IAKlC,IAAI,CAACnR,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,kCAAkCyJ,eAAe,EAAEzC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAC1E;IAED,IAAI,CAACyC,eAAe,IAAIA,eAAe,CAAC/H,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAACpJ,MAAM,CAACuQ,IAAI,CAAC,gBAAgB,EAAE,8BAA8B,CAAC;MAClE,OAAOzU,EAAE,CAAC;QACR8U,OAAO,EAAE,KAAK;QACda,SAAS,EAAE,CAAC;QACZC,cAAc,EAAE,IAAI,CAACpR,iBAAiB,CAAC2K;OACxC,CAAC;;IAGJ;IACA,MAAM0G,QAAQ,GAAGR,eAAe,CAAC9U,MAAM,CACpCqH,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACkO,IAAI,EAAE,KAAK,EAAE,CACzD;IAED,IAAID,QAAQ,CAACvI,MAAM,KAAK+H,eAAe,CAAC/H,MAAM,EAAE;MAC9C,IAAI,CAACpJ,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,EAAE;QACvE6N,QAAQ,EAAEV,eAAe;QACzBW,KAAK,EAAEH;OACR,CAAC;MACF,OAAO5V,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,mCAAmC,CAAC,CAAC;;IAGzE,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,gDAAgD,EAChDiK,QAAQ,CACT;IAED;IACA,IAAI,CAACI,wBAAwB,CAACJ,QAAQ,EAAE,IAAI,CAAC;IAE7C;IACA,MAAMK,kBAAkB,GAAG;MACzBC,uBAAuB,EAAE;QACvBrB,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAACvI,MAAM;QAC1BsI,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAAC7R,iBAAiB,CAAC2K,KAAK,GAAG0G,QAAQ,CAACvI,MAAM;;KAGnD;IAED;IACApD,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE;MACtEkL,eAAe,EAAEQ;KAClB,CAAC;IACF3L,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEtH,+BAA+B,CAAC;IAE/D,OAAO,IAAI,CAACoB,MAAM,CACfgK,MAAM,CAAkC;MACvCC,QAAQ,EAAErL,+BAA+B;MACzC6K,SAAS,EAAE;QAAE2H,eAAe,EAAEQ;MAAQ,CAAE;MACxCK,kBAAkB,EAAEA,kBAAkB;MACtCI,WAAW,EAAE,KAAK;MAClBpJ,WAAW,EAAE,UAAU,CAAE;KAC1B,CAAC,CACDxE,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAAClJ,MAAM,CAAC0H,KAAK,CAAC,gBAAgB,EAAE,iBAAiB,EAAEwB,MAAM,CAAC;MAC9DlD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEiD,MAAM,CAAC;MAEvC;MACA,IAAIA,MAAM,CAACsF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBkF,MAAM,CAACsF,MAAM,CACd;QACDxI,OAAO,CAAChC,KAAK,CAAC,iBAAiB,EAAEkF,MAAM,CAACsF,MAAM,CAAC;;MAGjD;MACA,MAAMkC,QAAQ,GACZxH,MAAM,CAACzE,IAAI,EAAEwN,uBAAuB,IACpCD,kBAAkB,CAACC,uBAAuB;MAE5C,OAAOvB,QAAQ;IACjB,CAAC,CAAC,EACFvU,UAAU,CAAE6H,KAAY,IAAI;MAC1B,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACDgC,OAAO,CAAChC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C;MACA;MACA,OAAOlI,EAAE,CAAC;QACR8U,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAACvI,MAAM;QAC1BsI,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAAC7R,iBAAiB,CAAC2K,KAAK,GAAG0G,QAAQ,CAACvI,MAAM;OAEjD,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EACA;EACA;EACA;EAEA;;;;;;;;EAQAiJ,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClB/K,cAAuB,EACvBgL,OAAqB;IAErB,OAAO,IAAI,CAACC,iBAAiB,CAACF,QAAQ,CAAC,CAAC/N,IAAI,CAC1ClI,SAAS,CAAEoW,MAAM,IAAI;MACnB,IAAI,CAAC5R,WAAW,GAAG4R,MAAM;MACzB,IAAI,CAACrR,YAAY,CAACuC,IAAI,CAAC8O,MAAM,CAAC;MAE9B;MACA,IAAI,CAAC1R,cAAc,GAAG,IAAI2R,iBAAiB,CAAC,IAAI,CAACpR,SAAS,CAAC;MAE3D;MACAmR,MAAM,CAACE,SAAS,EAAE,CAACpP,OAAO,CAAEqP,KAAK,IAAI;QACnC,IAAI,CAAC7R,cAAe,CAAC8R,QAAQ,CAACD,KAAK,EAAEH,MAAM,CAAC;MAC9C,CAAC,CAAC;MAEF;MACA,IAAI,CAAC1R,cAAc,CAAC+R,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,EAAE;UACnB,IAAI,CAACC,cAAc,CACjB,IAAI,CAACC,cAAc,EAAE,EACrB,eAAe,EACf9P,IAAI,CAACyN,SAAS,CAACkC,KAAK,CAACC,SAAS,CAAC,CAChC;;MAEL,CAAC;MAED;MACA,IAAI,CAACjS,cAAc,CAACoS,OAAO,GAAIJ,KAAK,IAAI;QACtC,IAAI,CAAC,IAAI,CAACjS,YAAY,EAAE;UACtB,IAAI,CAACA,YAAY,GAAG,IAAIsS,WAAW,EAAE;UACrC,IAAI,CAAC/R,aAAa,CAACsC,IAAI,CAAC,IAAI,CAAC7C,YAAY,CAAC;;QAE5CiS,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,EAAE,CAACpP,OAAO,CAAEqP,KAAK,IAAI;UAC7C,IAAI,CAAC9R,YAAa,CAAC+R,QAAQ,CAACD,KAAK,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC;MAED;MACA,OAAOtW,IAAI,CAAC,IAAI,CAACyE,cAAc,CAACuS,WAAW,EAAE,CAAC,CAAC/O,IAAI,CACjDlI,SAAS,CAAEkX,KAAK,IAAI;QAClB,OAAOjX,IAAI,CAAC,IAAI,CAACyE,cAAe,CAACyS,mBAAmB,CAACD,KAAK,CAAC,CAAC,CAAChP,IAAI,CAC/DtI,GAAG,CAAC,MAAMsX,KAAK,CAAC,CACjB;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACFlX,SAAS,CAAEkX,KAAK,IAAI;MAClB;MACA,MAAME,MAAM,GAAG,IAAI,CAACP,cAAc,EAAE;MAEpC;MACA,OAAO,IAAI,CAACpT,MAAM,CACfgK,MAAM,CAAyB;QAC9BC,QAAQ,EAAE5K,sBAAsB;QAChCoK,SAAS,EAAE;UACT8I,WAAW;UACXC,QAAQ;UACRmB,MAAM;UACNF,KAAK,EAAEnQ,IAAI,CAACyN,SAAS,CAAC0C,KAAK,CAAC;UAC5BhM,cAAc;UACdgL;;OAEH,CAAC,CACDhO,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;QACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAE4N,YAAY;QACtC,IAAI,CAAC1N,IAAI,EAAE;UACT,MAAM,IAAIiD,KAAK,CAAC,yBAAyB,CAAC;;QAG5C;QACA,IAAI,CAACjH,UAAU,CAACiD,IAAI,CAACe,IAAI,CAAC;QAE1B;QACA,MAAMgP,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAC3CjP,IAAI,CAACjB,EAAE,CACR,CAACS,SAAS,EAAE;QACb,IAAI,CAAC3D,aAAa,CAAC+Q,IAAI,CAACoC,SAAS,CAAC;QAElC,OAAOhP,IAAI;MACb,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,IAAI,CAAC6P,WAAW,EAAE;MAClB,OAAO9X,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/D,CAAC,CAAC,CACH;EACH;EAEA;;;;;EAKAkM,UAAUA,CAAClT,YAA0B;IACnC,IAAI,CAAC4E,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAACiN,iBAAiB,CAAC7R,YAAY,CAAC8F,IAAI,CAAC,CAAClC,IAAI,CACnDlI,SAAS,CAAEoW,MAAM,IAAI;MACnB,IAAI,CAAC5R,WAAW,GAAG4R,MAAM;MACzB,IAAI,CAACrR,YAAY,CAACuC,IAAI,CAAC8O,MAAM,CAAC;MAE9B;MACA,IAAI,CAAC1R,cAAc,GAAG,IAAI2R,iBAAiB,CAAC,IAAI,CAACpR,SAAS,CAAC;MAE3D;MACAmR,MAAM,CAACE,SAAS,EAAE,CAACpP,OAAO,CAAEqP,KAAK,IAAI;QACnC,IAAI,CAAC7R,cAAe,CAAC8R,QAAQ,CAACD,KAAK,EAAEH,MAAM,CAAC;MAC9C,CAAC,CAAC;MAEF;MACA,IAAI,CAAC1R,cAAc,CAAC+R,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,EAAE;UACnB,IAAI,CAACC,cAAc,CACjBtS,YAAY,CAAC8C,EAAE,EACf,eAAe,EACfL,IAAI,CAACyN,SAAS,CAACkC,KAAK,CAACC,SAAS,CAAC,CAChC;;MAEL,CAAC;MAED;MACA,IAAI,CAACjS,cAAc,CAACoS,OAAO,GAAIJ,KAAK,IAAI;QACtC,IAAI,CAAC,IAAI,CAACjS,YAAY,EAAE;UACtB,IAAI,CAACA,YAAY,GAAG,IAAIsS,WAAW,EAAE;UACrC,IAAI,CAAC/R,aAAa,CAACsC,IAAI,CAAC,IAAI,CAAC7C,YAAY,CAAC;;QAE5CiS,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,EAAE,CAACpP,OAAO,CAAEqP,KAAK,IAAI;UAC7C,IAAI,CAAC9R,YAAa,CAAC+R,QAAQ,CAACD,KAAK,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC;MAED;MACA,MAAMW,KAAK,GAAGnQ,IAAI,CAACC,KAAK,CAAC1C,YAAY,CAAC4S,KAAK,CAAC;MAC5C,OAAOjX,IAAI,CACT,IAAI,CAACyE,cAAc,CAAC+S,oBAAoB,CACtC,IAAIC,qBAAqB,CAACR,KAAK,CAAC,CACjC,CACF,CAAChP,IAAI,CACJlI,SAAS,CAAC,MAAMC,IAAI,CAAC,IAAI,CAACyE,cAAe,CAACiT,YAAY,EAAE,CAAC,CAAC,EAC1D3X,SAAS,CAAE4X,MAAM,IAAI;QACnB,OAAO3X,IAAI,CAAC,IAAI,CAACyE,cAAe,CAACyS,mBAAmB,CAACS,MAAM,CAAC,CAAC,CAAC1P,IAAI,CAChEtI,GAAG,CAAC,MAAMgY,MAAM,CAAC,CAClB;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACF5X,SAAS,CAAE4X,MAAM,IAAI;MACnB;MACA,OAAO,IAAI,CAACnU,MAAM,CACfgK,MAAM,CAAuB;QAC5BC,QAAQ,EAAE1K,oBAAoB;QAC9BkK,SAAS,EAAE;UACTkK,MAAM,EAAE9S,YAAY,CAAC8C,EAAE;UACvBwQ,MAAM,EAAE7Q,IAAI,CAACyN,SAAS,CAACoD,MAAM;;OAEhC,CAAC,CACD1P,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;QACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAEqP,UAAU;QACpC,IAAI,CAACnP,IAAI,EAAE;UACT,MAAM,IAAIiD,KAAK,CAAC,uBAAuB,CAAC;;QAG1C;QACA,IAAI,CAAChD,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,IAAI,CAACjE,UAAU,CAACiD,IAAI,CAAC;UACnB,GAAGe,IAAI;UACPwP,MAAM,EAAEvT,YAAY,CAACuT,MAAM;UAC3BzN,IAAI,EAAE9F,YAAY,CAAC8F,IAAI;UACvBc,cAAc,EAAE5G,YAAY,CAAC4G;SAC9B,CAAC;QAEF;QACA,MAAMmM,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAC3ChT,YAAY,CAAC8C,EAAE,CAChB,CAACS,SAAS,EAAE;QACb,IAAI,CAAC3D,aAAa,CAAC+Q,IAAI,CAACoC,SAAS,CAAC;QAElC;QACA,IAAI,CAAC/S,YAAY,CAACgD,IAAI,CAAC,IAAI,CAAC;QAE5B,OAAOe,IAAI;MACb,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,IAAI,CAAC6P,WAAW,EAAE;MAClB,OAAO9X,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACH;EAEA;;;;;;EAMAwM,UAAUA,CAACV,MAAc,EAAEW,MAAe;IACxC,IAAI,CAAC7O,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAACzF,MAAM,CACfgK,MAAM,CAAuB;MAC5BC,QAAQ,EAAEzK,oBAAoB;MAC9BiK,SAAS,EAAE;QACTkK,MAAM;QACNW;;KAEH,CAAC,CACD7P,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAE2P,UAAU;MACpC,IAAI,CAACzP,IAAI,EAAE;QACT,MAAM,IAAIiD,KAAK,CAAC,uBAAuB,CAAC;;MAG1C;MACA,IAAI,CAAChH,YAAY,CAACgD,IAAI,CAAC,IAAI,CAAC;MAE5B,OAAOe,IAAI;IACb,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEA;;;;;;EAMA0M,OAAOA,CAACZ,MAAc,EAAEa,QAAuB;IAC7C,IAAI,CAAC/O,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAAC7E,MAAM,CACfgK,MAAM,CAAoB;MACzBC,QAAQ,EAAExK,iBAAiB;MAC3BgK,SAAS,EAAE;QACTkK,MAAM;QACNa;;KAEH,CAAC,CACD/P,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAE6P,OAAO;MACjC,IAAI,CAAC3P,IAAI,EAAE;QACT,MAAM,IAAIiD,KAAK,CAAC,oBAAoB,CAAC;;MAGvC;MACA,IAAI,CAACiM,WAAW,EAAE;MAElB;MACA,IAAI,CAAClT,UAAU,CAACiD,IAAI,CAAC,IAAI,CAAC;MAE1B,OAAOe,IAAI;IACb,CAAC,CAAC,EACFxI,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAAC6P,WAAW,EAAE;MAClB,OAAO9X,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAC1D,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOA4M,WAAWA,CACTd,MAAc,EACde,KAAe,EACfzP,KAAe;IAEf,IAAI,IAAI,CAAClE,WAAW,EAAE;MACpB;MACA,IAAI2T,KAAK,KAAK7R,SAAS,EAAE;QACvB,IAAI,CAAC9B,WAAW,CAAC4T,cAAc,EAAE,CAAClR,OAAO,CAAEqP,KAAK,IAAI;UAClDA,KAAK,CAAC8B,OAAO,GAAGF,KAAK;QACvB,CAAC,CAAC;;MAGJ,IAAIzP,KAAK,KAAKpC,SAAS,EAAE;QACvB,IAAI,CAAC9B,WAAW,CAAC8T,cAAc,EAAE,CAACpR,OAAO,CAAEqP,KAAK,IAAI;UAClDA,KAAK,CAAC8B,OAAO,GAAG3P,KAAK;QACvB,CAAC,CAAC;;;IAIN,OAAO,IAAI,CAACjF,MAAM,CACfgK,MAAM,CAAmC;MACxCC,QAAQ,EAAEvK,0BAA0B;MACpC+J,SAAS,EAAE;QACTkK,MAAM;QACNe,KAAK;QACLzP;;KAEH,CAAC,CACDR,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAM0H,OAAO,GAAG1H,MAAM,CAACzE,IAAI,EAAEoQ,eAAe;MAC5C,IAAI,CAACjE,OAAO,EAAE;QACZ,MAAM,IAAIhJ,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAOgJ,OAAO;IAChB,CAAC,CAAC,EACFzU,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAgM,sBAAsBA,CAACF,MAAc;IACnC,OAAO,IAAI,CAAC3T,MAAM,CACfoE,SAAS,CAA6B;MACrCI,KAAK,EAAE7E,wBAAwB;MAC/B8J,SAAS,EAAE;QAAEkK;MAAM;KACpB,CAAC,CACDlP,IAAI,CACHtI,GAAG,CAAC,CAAC;MAAEuI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAEqQ,UAAU,EAAE;QACrB,MAAM,IAAIlN,KAAK,CAAC,yBAAyB,CAAC;;MAE5C,OAAOnD,IAAI,CAACqQ,UAAU;IACxB,CAAC,CAAC,EACF1Y,GAAG,CAAE2Y,MAAM,IAAI;MACb,IAAI,CAAClU,WAAW,CAAC+C,IAAI,CAACmR,MAAM,CAAC;MAC7B,IAAI,CAACC,gBAAgB,CAACD,MAAM,CAAC;IAC/B,CAAC,CAAC,EACF5Y,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC7D,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOAsL,cAAcA,CACZQ,MAAc,EACduB,UAAkB,EAClBC,UAAkB;IAElB,OAAO,IAAI,CAACnV,MAAM,CACfgK,MAAM,CAAkC;MACvCC,QAAQ,EAAE3K,yBAAyB;MACnCmK,SAAS,EAAE;QACTkK,MAAM;QACNuB,UAAU;QACVC;;KAEH,CAAC,CACD1Q,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAM0H,OAAO,GAAG1H,MAAM,CAACzE,IAAI,EAAEyO,cAAc;MAC3C,IAAI,CAACtC,OAAO,EAAE;QACZ,MAAM,IAAIhJ,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAOgJ,OAAO;IAChB,CAAC,CAAC,EACFzU,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACrD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;;;;EAUAuN,cAAcA,CACZ3S,KAAA,GAAgB,EAAE,EAClB6K,MAAA,GAAiB,CAAC,EAClB+H,MAAiB,EACjB1O,IAAe,EACf2O,SAAyB,EACzBC,OAAuB;IAEvB,OAAO,IAAI,CAACvV,MAAM,CACfgJ,UAAU,CAA0B;MACnCxE,KAAK,EAAEtF,kBAAkB;MACzBuK,SAAS,EAAE;QACThH,KAAK;QACL6K,MAAM;QACN+H,MAAM;QACN1O,IAAI;QACJ2O,SAAS;QACTC;OACD;MACDtM,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMqM,OAAO,GAAGrM,MAAM,CAACzE,IAAI,EAAE+Q,WAAW,IAAI,EAAE;MAC9C,IAAI,CAACxV,MAAM,CAAC0H,KAAK,CAAC,aAAa6N,OAAO,CAACnM,MAAM,qBAAqB,CAAC;MACnE,OAAOmM,OAAO;IAChB,CAAC,CAAC,EACFpZ,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKA6N,cAAcA,CAAC/B,MAAc;IAC3B,OAAO,IAAI,CAAC3T,MAAM,CACfgJ,UAAU,CAAwB;MACjCxE,KAAK,EAAErF,kBAAkB;MACzBsK,SAAS,EAAE;QAAEkK;MAAM,CAAE;MACrB1K,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMwM,OAAO,GAAGxM,MAAM,CAACzE,IAAI,EAAEkR,WAAW;MACxC,IAAI,CAACD,OAAO,EAAE;QACZ,MAAM,IAAI9N,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CAAC,+BAA+BgM,MAAM,EAAE,CAAC;MAC1D,OAAOgC,OAAO;IAChB,CAAC,CAAC,EACFvZ,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIAgO,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC7V,MAAM,CACfgJ,UAAU,CAAqB;MAC9BxE,KAAK,EAAEpF,gBAAgB;MACvB6J,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAM2M,KAAK,GAAG3M,MAAM,CAACzE,IAAI,EAAEqR,SAAS;MACpC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAIjO,KAAK,CAAC,sBAAsB,CAAC;;MAEzC,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CAAC,uBAAuB,EAAEmO,KAAK,CAAC;MACjD,OAAOA,KAAK;IACd,CAAC,CAAC,EACF1Z,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACtD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIQoN,gBAAgBA,CAACD,MAAkB;IACzC,QAAQA,MAAM,CAACrO,IAAI;MACjB,KAAK,eAAe;QAClB,IAAI,CAACqP,kBAAkB,CAAChB,MAAM,CAAC;QAC/B;MACF,KAAK,QAAQ;QACX,IAAI,CAACiB,YAAY,CAACjB,MAAM,CAAC;QACzB;MACF,KAAK,UAAU;QACb,IAAI,CAACkB,aAAa,CAAClB,MAAM,CAAC;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACmB,gBAAgB,CAACnB,MAAM,CAAC;QAC7B;MACF;QACE,IAAI,CAAC/U,MAAM,CAAC0H,KAAK,CAAC,0BAA0BqN,MAAM,CAACrO,IAAI,EAAE,EAAEqO,MAAM,CAAC;;EAExE;EAEA;;;;EAIQgB,kBAAkBA,CAAChB,MAAkB;IAC3C,IAAI,CAAC,IAAI,CAAC/T,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACgE,KAAK,CAAC,gDAAgD,CAAC;MACnE;;IAGF,IAAI;MACF,MAAMiP,SAAS,GAAG5P,IAAI,CAACC,KAAK,CAACyR,MAAM,CAACtQ,IAAI,CAAC;MACzC,IAAI,CAACzD,cAAc,CAChBmV,eAAe,CAAC,IAAIC,eAAe,CAACnD,SAAS,CAAC,CAAC,CAC/C1N,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MACjE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,6BAA6B,EAAEA,KAAc,CAAC;;EAEpE;EAEA;;;;EAIQgS,YAAYA,CAACjB,MAAkB;IACrC,IAAI,CAAC,IAAI,CAAC/T,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACgE,KAAK,CAAC,yCAAyC,CAAC;MAC5D;;IAGF,IAAI;MACF,MAAMkQ,MAAM,GAAG7Q,IAAI,CAACC,KAAK,CAACyR,MAAM,CAACtQ,IAAI,CAAC;MACtC,IAAI,CAACzD,cAAc,CAChB+S,oBAAoB,CAAC,IAAIC,qBAAqB,CAACE,MAAM,CAAC,CAAC,CACvD3O,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,kCAAkC,EAAEA,KAAc,CAAC;MACvE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,sBAAsB,EAAEA,KAAc,CAAC;;EAE7D;EAEA;;;;EAIQiS,aAAaA,CAAClB,MAAkB;IACtC,IAAI,CAACvP,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACqO,WAAW,EAAE;IAElB;IACA,MAAMwC,WAAW,GAAG,IAAI,CAAC1V,UAAU,CAACsK,KAAK;IACzC,IAAIoL,WAAW,IAAIA,WAAW,CAAC3S,EAAE,KAAKqR,MAAM,CAACrB,MAAM,EAAE;MACnD,IAAI,CAAC/S,UAAU,CAACiD,IAAI,CAAC;QACnB,GAAGyS,WAAW;QACdjB,MAAM,EAAE1Y,UAAU,CAAC4Z,KAAK;QACxBC,OAAO,EAAE,IAAIzO,IAAI,EAAE,CAACjF,WAAW;OAChC,CAAC;;EAEN;EAEA;;;;EAIQqT,gBAAgBA,CAACnB,MAAkB;IACzC,IAAI,CAACvP,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACqO,WAAW,EAAE;IAElB;IACA,MAAMwC,WAAW,GAAG,IAAI,CAAC1V,UAAU,CAACsK,KAAK;IACzC,IAAIoL,WAAW,IAAIA,WAAW,CAAC3S,EAAE,KAAKqR,MAAM,CAACrB,MAAM,EAAE;MACnD,IAAI,CAAC/S,UAAU,CAACiD,IAAI,CAAC;QACnB,GAAGyS,WAAW;QACdjB,MAAM,EAAE1Y,UAAU,CAAC8Z,QAAQ;QAC3BD,OAAO,EAAE,IAAIzO,IAAI,EAAE,CAACjF,WAAW;OAChC,CAAC;;EAEN;EAEA;;;EAGQgR,WAAWA,CAAA;IACjB,IAAI,IAAI,CAAC/S,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC8R,SAAS,EAAE,CAACpP,OAAO,CAAEqP,KAAK,IAAKA,KAAK,CAACrN,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC1E,WAAW,GAAG,IAAI;MACvB,IAAI,CAACO,YAAY,CAACuC,IAAI,CAAC,IAAI,CAAC;;IAG9B,IAAI,IAAI,CAAC5C,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACyV,KAAK,EAAE;MAC3B,IAAI,CAACzV,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACO,aAAa,CAACsC,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;;;EAKQ6O,iBAAiBA,CAACF,QAAkB;IAC1C,MAAMmE,WAAW,GAA2B;MAC1C1R,KAAK,EAAE,IAAI;MACXyP,KAAK,EACHlC,QAAQ,KAAK9V,QAAQ,CAACqO,KAAK,GACvB;QACE6L,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QACtBC,MAAM,EAAE;UAAED,KAAK,EAAE;QAAG;OACrB,GACD;KACP;IAED,OAAO,IAAI/a,UAAU,CAAeib,QAAQ,IAAI;MAC9CC,SAAS,CAACC,YAAY,CACnBC,YAAY,CAACP,WAAW,CAAC,CACzBQ,IAAI,CAAExE,MAAM,IAAI;QACfoE,QAAQ,CAAClT,IAAI,CAAC8O,MAAM,CAAC;QACrBoE,QAAQ,CAACK,QAAQ,EAAE;MACrB,CAAC,CAAC,CACD5R,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACzD8S,QAAQ,CAAC9S,KAAK,CAAC,IAAI4D,KAAK,CAAC,gCAAgC,CAAC,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA;;;;EAIQuL,cAAcA,CAAA;IACpB,OAAOrL,IAAI,CAACC,GAAG,EAAE,CAACqP,QAAQ,EAAE,GAAGlF,IAAI,CAACmF,MAAM,EAAE,CAACD,QAAQ,CAAC,EAAE,CAAC,CAAC5M,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEA;EACA;EACA;EACA;EACA8M,WAAWA,CACTC,YAAY,GAAG,KAAK,EACpBC,MAAe,EACfjO,IAAA,GAAe,CAAC,EAChB/G,KAAA,GAAgB,EAAE,EAClBiV,MAAA,GAAiB,UAAU,EAC3BC,SAAA,GAAoB,KAAK,EACzBC,QAAkB;IAElB,IAAI,CAAC3X,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,2CAA2CgN,YAAY,YACrDC,MAAM,IAAI,SACZ,UAAUjO,IAAI,WAAW/G,KAAK,YAAYiV,MAAM,eAAeC,SAAS,cAAcC,QAAQ,EAAE,CACjG;IAED,MAAM5P,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE;IACtB,MAAM6P,UAAU,GACd,CAACL,YAAY,IACb,IAAI,CAAC7V,UAAU,CAAC0H,MAAM,GAAG,CAAC,IAC1BrB,GAAG,GAAG,IAAI,CAACrH,aAAa,IAAI,IAAI,CAACD,cAAc,IAC/C,CAAC+W,MAAM,IACPjO,IAAI,KAAK,CAAC,IACV/G,KAAK,IAAI,IAAI,CAACd,UAAU,CAAC0H,MAAM;IAEjC;IACA,IAAIwO,UAAU,EAAE;MACd,IAAI,CAAC5X,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,uBAAuB,IAAI,CAAChG,UAAU,CAAC0H,MAAM,SAAS,CACvD;MACD,OAAOtN,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC4F,UAAU,CAAC,CAAC;;IAGjC,IAAI,CAAC1B,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,2DACE6P,YAAY,GAAG,cAAc,GAAG,aAClC,EAAE,CACH;IAED,OAAO,IAAI,CAACxX,MAAM,CACfgJ,UAAU,CAAM;MACfxE,KAAK,EAAEnH,kBAAkB;MACzBoM,SAAS,EAAE;QACTgO,MAAM;QACNjO,IAAI;QACJ/G,KAAK;QACLiV,MAAM;QACNC,SAAS;QACTC,QAAQ,EAAEA,QAAQ,KAAK/U,SAAS,GAAG+U,QAAQ,GAAG;OAC/C;MACD3O,WAAW,EAAEuO,YAAY,GAAG,cAAc,GAAG;KAC9C,CAAC,CACDtO,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAAClJ,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,yBAAyB,EACzBwB,MAAM,CACP;MAED,IAAIA,MAAM,CAACsF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCkF,MAAM,CAACsF,MAAM,CACd;QACD,MAAM,IAAI5G,KAAK,CAACsB,MAAM,CAACsF,MAAM,CAACtS,GAAG,CAAEuS,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,IAAI,CAACxF,MAAM,CAACzE,IAAI,EAAE6S,WAAW,EAAE;QAC7B,IAAI,CAACtX,MAAM,CAACuQ,IAAI,CACd,gBAAgB,EAChB,oCAAoC,CACrC;QACD,OAAO,EAAE;;MAGX,MAAMsH,iBAAiB,GAAG3O,MAAM,CAACzE,IAAI,CAAC6S,WAAW;MAEjD;MACA,IAAI,CAACtX,MAAM,CAAC0H,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;QAC1D9F,UAAU,EAAEiW,iBAAiB,CAACjW,UAAU;QACxCC,UAAU,EAAEgW,iBAAiB,CAAChW,UAAU;QACxCC,WAAW,EAAE+V,iBAAiB,CAAC/V,WAAW;QAC1CC,WAAW,EAAE8V,iBAAiB,CAAC9V,WAAW;QAC1CC,eAAe,EAAE6V,iBAAiB,CAAC7V;OACpC,CAAC;MAEF;MACA,MAAM8V,KAAK,GAAW,EAAE;MACxB,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,CAACC,KAAK,EAAE;QAC1C,IAAI;UACF,IAAIC,IAAI,EAAE;YACRD,KAAK,CAACvG,IAAI,CAAC,IAAI,CAAC3E,aAAa,CAACmL,IAAI,CAAC,CAAC;;SAEvC,CAAC,OAAO/T,KAAK,EAAE;UACd,IAAI,CAAChE,MAAM,CAACuQ,IAAI,CACd,gBAAgB,EAChB,mCAAmC,EACnCvM,KAAK,CACN;;;MAIL,IAAI,CAAChE,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,YAAYuN,KAAK,CAAC1O,MAAM,4BAA4ByO,iBAAiB,CAAC/V,WAAW,OAAO+V,iBAAiB,CAAChW,UAAU,GAAG,CACxH;MAED;MACA,IAAI,CAAC2V,MAAM,IAAIjO,IAAI,KAAK,CAAC,IAAI,CAACoO,QAAQ,EAAE;QACtC,IAAI,CAACjW,UAAU,GAAG,CAAC,GAAGoW,KAAK,CAAC;QAC5B,IAAI,CAACpX,aAAa,GAAGoH,IAAI,CAACC,GAAG,EAAE;QAC/B,IAAI,CAAC/H,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,2BAA2BoQ,KAAK,CAAC1O,MAAM,QAAQ,CAChD;;MAGH;MACA,IAAI,CAACzH,qBAAqB,GAAG;QAC3BC,UAAU,EAAEiW,iBAAiB,CAACjW,UAAU;QACxCC,UAAU,EAAEgW,iBAAiB,CAAChW,UAAU;QACxCC,WAAW,EAAE+V,iBAAiB,CAAC/V,WAAW;QAC1CC,WAAW,EAAE8V,iBAAiB,CAAC9V,WAAW;QAC1CC,eAAe,EAAE6V,iBAAiB,CAAC7V;OACpC;MAED,OAAO8V,KAAK;IACd,CAAC,CAAC,EACF3b,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAEnE,IAAIA,KAAK,CAACqL,aAAa,EAAE;QACvB,IAAI,CAACrP,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACqL,aAAa,CACpB;;MAGH,IAAIrL,KAAK,CAACsL,YAAY,EAAE;QACtB,IAAI,CAACtP,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACsL,YAAY,CACnB;;MAGH;MACA,IACE,IAAI,CAAC5N,UAAU,CAAC0H,MAAM,GAAG,CAAC,IAC1BG,IAAI,KAAK,CAAC,IACV,CAACiO,MAAM,IACP,CAACG,QAAQ,EACT;QACA,IAAI,CAAC3X,MAAM,CAACuQ,IAAI,CACd,gBAAgB,EAChB,aAAa,IAAI,CAAC7O,UAAU,CAAC0H,MAAM,kCAAkC,CACtE;QACD,OAAOtN,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC4F,UAAU,CAAC,CAAC;;MAGjC,OAAO3F,UAAU,CACf,MACE,IAAI6L,KAAK,CACP,0BAA0B5D,KAAK,CAAC0J,OAAO,IAAI,eAAe,EAAE,CAC7D,CACJ;IACH,CAAC,CAAC,CACH;EACL;EACAsK,UAAUA,CAAClL,MAAc;IACvB,OAAO,IAAI,CAAC/M,MAAM,CACfgJ,UAAU,CAAqB;MAC9BxE,KAAK,EAAEpH,cAAc;MACrBqM,SAAS,EAAE;QAAE9F,EAAE,EAAEoJ;MAAM,CAAE;MACzB9D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAK,IAAI,CAAC0D,aAAa,CAAC1D,MAAM,CAACzE,IAAI,EAAEuT,UAAU,CAAC,CAAC,EAC5D7b,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC5D,CAAC,CAAC,CACH;EACL;EACAqQ,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClY,MAAM,CACfgJ,UAAU,CAAyB;MAClCxE,KAAK,EAAE1G,sBAAsB;MAC7BmL,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAK,IAAI,CAAC0D,aAAa,CAAC1D,MAAM,CAACzE,IAAI,EAAEwT,cAAc,CAAC,CAAC,EAChE9b,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EACAsQ,aAAaA,CAACpL,MAAc;IAC1B,OAAO,IAAI,CAAC/M,MAAM,CACfgK,MAAM,CAAwB;MAC7BC,QAAQ,EAAExM,wBAAwB;MAClCgM,SAAS,EAAE;QAAEsD;MAAM;KACpB,CAAC,CACDtI,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEyT,aAAa,EAC7B,MAAM,IAAItQ,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAO,IAAI,CAACgF,aAAa,CAAC1D,MAAM,CAACzE,IAAI,CAACyT,aAAa,CAAC;IACtD,CAAC,CAAC,EACF/b,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EACAuQ,cAAcA,CAACrL,MAAc;IAC3B,OAAO,IAAI,CAAC/M,MAAM,CACfgK,MAAM,CAAyB;MAC9BC,QAAQ,EAAEvM,yBAAyB;MACnC+L,SAAS,EAAE;QAAEsD;MAAM;KACpB,CAAC,CACDtI,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAE0T,cAAc,EAC9B,MAAM,IAAIvQ,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAO,IAAI,CAACgF,aAAa,CAAC1D,MAAM,CAACzE,IAAI,CAAC0T,cAAc,CAAC;IACvD,CAAC,CAAC,EACFhc,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGAwQ,WAAWA,CACTtT,IAAY,EACZkJ,cAAwB,EACxBqK,KAAY,EACZC,WAAoB;IAEpB,IAAI,CAACtY,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,mBAAmB5C,IAAI,SAASkJ,cAAc,CAAC5E,MAAM,eAAe,CACrE;IAED,IAAI,CAACtE,IAAI,IAAI,CAACkJ,cAAc,IAAIA,cAAc,CAAC5E,MAAM,KAAK,CAAC,EAAE;MAC3D,OAAOrN,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,sCAAsC,CAAC,CACxD;;IAGH,OAAO,IAAI,CAAC7H,MAAM,CACfgK,MAAM,CAAC;MACNC,QAAQ,EAAE/L,qBAAqB;MAC/BuL,SAAS,EAAE;QAAE1E,IAAI;QAAEkJ,cAAc;QAAEqK,KAAK;QAAEC;MAAW;KACtD,CAAC,CACD9T,IAAI,CACHtI,GAAG,CAAEgN,MAAW,IAAI;MAClB,MAAMqP,KAAK,GAAGrP,MAAM,CAACzE,IAAI,EAAE2T,WAAW;MACtC,IAAI,CAACG,KAAK,EAAE;QACV,MAAM,IAAI3Q,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,IAAI,CAAC5H,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,+BAA+BgO,KAAK,CAAC7U,EAAE,EAAE,CAC1C;MACD,OAAO6U,KAAK;IACd,CAAC,CAAC,EACFpc,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA4Q,WAAWA,CAACC,OAAe,EAAEC,KAAU;IACrC,IAAI,CAAC1Y,MAAM,CAAC0H,KAAK,CAAC,gBAAgB,EAAE,mBAAmB+Q,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO1c,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAC7H,MAAM,CACfgK,MAAM,CAAC;MACNC,QAAQ,EAAE9L,qBAAqB;MAC/BsL,SAAS,EAAE;QAAE9F,EAAE,EAAE+U,OAAO;QAAEC;MAAK;KAChC,CAAC,CACDlU,IAAI,CACHtI,GAAG,CAAEgN,MAAW,IAAI;MAClB,MAAMqP,KAAK,GAAGrP,MAAM,CAACzE,IAAI,EAAE+T,WAAW;MACtC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAI3Q,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC5H,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,+BAA+BgO,KAAK,CAAC7U,EAAE,EAAE,CAC1C;MACD,OAAO6U,KAAK;IACd,CAAC,CAAC,EACFpc,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA+Q,WAAWA,CACTF,OAAe;IAEf,IAAI,CAACzY,MAAM,CAAC0H,KAAK,CAAC,gBAAgB,EAAE,mBAAmB+Q,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO1c,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAC7H,MAAM,CACfgK,MAAM,CAAC;MACNC,QAAQ,EAAE7L,qBAAqB;MAC/BqL,SAAS,EAAE;QAAE9F,EAAE,EAAE+U;MAAO;KACzB,CAAC,CACDjU,IAAI,CACHtI,GAAG,CAAEgN,MAAW,IAAI;MAClB,MAAMwH,QAAQ,GAAGxH,MAAM,CAACzE,IAAI,EAAEkU,WAAW;MACzC,IAAI,CAACjI,QAAQ,EAAE;QACb,MAAM,IAAI9I,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC5H,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,+BAA+BkO,OAAO,EAAE,CACzC;MACD,OAAO/H,QAAQ;IACjB,CAAC,CAAC,EACFvU,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAgR,UAAUA,CACRH,OAAe;IAEf,IAAI,CAACzY,MAAM,CAAC0H,KAAK,CAAC,gBAAgB,EAAE,kBAAkB+Q,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO1c,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAC7H,MAAM,CACfgK,MAAM,CAAC;MACNC,QAAQ,EAAE5L,oBAAoB;MAC9BoL,SAAS,EAAE;QAAEiP;MAAO;KACrB,CAAC,CACDjU,IAAI,CACHtI,GAAG,CAAEgN,MAAW,IAAI;MAClB,MAAMwH,QAAQ,GAAGxH,MAAM,CAACzE,IAAI,EAAEmU,UAAU;MACxC,IAAI,CAAClI,QAAQ,EAAE;QACb,MAAM,IAAI9I,KAAK,CAAC,8BAA8B,CAAC;;MAEjD,IAAI,CAAC5H,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,4BAA4BkO,OAAO,EAAE,CACtC;MACD,OAAO/H,QAAQ;IACjB,CAAC,CAAC,EACFvU,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAiR,QAAQA,CAACJ,OAAe;IACtB,IAAI,CAACzY,MAAM,CAAC0H,KAAK,CAAC,gBAAgB,EAAE,kBAAkB+Q,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO1c,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAC7H,MAAM,CACfwE,KAAK,CAAC;MACLA,KAAK,EAAElG,eAAe;MACtBmL,SAAS,EAAE;QAAE9F,EAAE,EAAE+U;MAAO,CAAE;MAC1BzP,WAAW,EAAE;KACd,CAAC,CACDxE,IAAI,CACHtI,GAAG,CAAEgN,MAAW,IAAI;MAClB,MAAMqP,KAAK,GAAGrP,MAAM,CAACzE,IAAI,EAAEoU,QAAQ;MACnC,IAAI,CAACN,KAAK,EAAE;QACV,MAAM,IAAI3Q,KAAK,CAAC,mBAAmB,CAAC;;MAEtC,IAAI,CAAC5H,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,iCAAiCkO,OAAO,EAAE,CAC3C;MACD,OAAOF,KAAK;IACd,CAAC,CAAC,EACFpc,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,oCAAoC,CAAC,CACtD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAkR,aAAaA,CAAChM,MAAc;IAC1B,IAAI,CAAC9M,MAAM,CAAC0H,KAAK,CAAC,gBAAgB,EAAE,4BAA4BoF,MAAM,EAAE,CAAC;IAEzE,IAAI,CAACA,MAAM,EAAE;MACX,OAAO/Q,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,4BAA4B,CAAC,CAAC;;IAGlE,OAAO,IAAI,CAAC7H,MAAM,CACfwE,KAAK,CAAC;MACLA,KAAK,EAAEjG,qBAAqB;MAC5BkL,SAAS,EAAE;QAAEsD;MAAM,CAAE;MACrB9D,WAAW,EAAE;KACd,CAAC,CACDxE,IAAI,CACHtI,GAAG,CAAEgN,MAAW,IAAI;MAClB,MAAMmI,MAAM,GAAGnI,MAAM,CAACzE,IAAI,EAAEqU,aAAa,IAAI,EAAE;MAC/C,IAAI,CAAC9Y,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,aAAa8G,MAAM,CAACjI,MAAM,qBAAqB0D,MAAM,EAAE,CACxD;MACD,OAAOuE,MAAM;IACf,CAAC,CAAC,EACFlV,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EACAmR,sBAAsBA,CAACvR,cAAsB;IAC3C;IACA,IAAI,CAAC,IAAI,CAACwR,YAAY,EAAE,EAAE;MACxB,IAAI,CAAChZ,MAAM,CAACuQ,IAAI,CACd,sEAAsE,CACvE;MACD,OAAOzU,EAAE,CAAC,IAA0B,CAAC;;IAGvC,IAAI,CAACkE,MAAM,CAAC0H,KAAK,CACf,2EAA2EF,cAAc,EAAE,CAC5F;IAED,MAAMyR,IAAI,GAAG,IAAI,CAAClZ,MAAM,CACrBoE,SAAS,CAA2B;MACnCI,KAAK,EAAEtH,yBAAyB;MAChCuM,SAAS,EAAE;QAAEhC;MAAc;KAC5B,CAAC,CACDhD,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMQ,GAAG,GAAGR,MAAM,CAACzE,IAAI,EAAEyU,WAAW;MACpC,IAAI,CAACxP,GAAG,EAAE;QACR,IAAI,CAAC1J,MAAM,CAACuQ,IAAI,CAAC,gCAAgC,CAAC;QAClD,MAAM,IAAI3I,KAAK,CAAC,6BAA6B,CAAC;;MAGhD,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CACf,+CAA+C,EAC/CgC,GAAG,CACJ;MAED;MACA,IAAI,CAACA,GAAG,CAAChG,EAAE,IAAI,CAACgG,GAAG,CAACwE,GAAG,EAAE;QACvB,IAAI,CAAClO,MAAM,CAACuQ,IAAI,CACd,oDAAoD,CACrD;QACD7G,GAAG,CAAChG,EAAE,GAAG,QAAQoE,IAAI,CAACC,GAAG,EAAE,EAAE;;MAG/B,IAAI;QACF;QACA,MAAMuD,iBAAiB,GAAG,IAAI,CAAC3B,gBAAgB,CAACD,GAAG,CAAC;QAEpD,IAAI,CAAC1J,MAAM,CAAC0H,KAAK,CACf,4CAA4C,EAC5C4D,iBAAiB,CAClB;QAED;QACA,IACEA,iBAAiB,CAAC5E,IAAI,KAAKlK,WAAW,CAACsO,KAAK,IAC5CQ,iBAAiB,CAAC5E,IAAI,KAAKlK,WAAW,CAAC8L,aAAa,IACnDgD,iBAAiB,CAAC6N,WAAW,IAC5B7N,iBAAiB,CAAC6N,WAAW,CAACC,IAAI,CAC/BC,GAAG,IAAKA,GAAG,CAAC3S,IAAI,KAAK,OAAO,IAAI2S,GAAG,CAAC3S,IAAI,KAAK,OAAO,CACrD,EACJ;UACA,IAAI,CAAC1G,MAAM,CAAC0H,KAAK,CACf,iDAAiD,CAClD;;QAGH;QACA,IAAI,CAACzH,IAAI,CAACsL,GAAG,CAAC,MAAK;UACjB,IAAI,CAACvL,MAAM,CAAC0H,KAAK,CACf,kDAAkD,CACnD;UACD,IAAI,CAAC4R,gCAAgC,CACnC9R,cAAc,EACd8D,iBAAiB,CAClB;QACH,CAAC,CAAC;QAEF,OAAOA,iBAAiB;OACzB,CAAC,OAAOnE,GAAG,EAAE;QACZ,IAAI,CAACnH,MAAM,CAACgE,KAAK,CAAC,8BAA8B,EAAEmD,GAAG,CAAC;QAEtD;QACA,MAAMsE,cAAc,GAAY;UAC9B/H,EAAE,EAAEgG,GAAG,CAAChG,EAAE,IAAIgG,GAAG,CAACwE,GAAG,IAAI,QAAQpG,IAAI,CAACC,GAAG,EAAE,EAAE;UAC7CmC,OAAO,EAAER,GAAG,CAACQ,OAAO,IAAI,EAAE;UAC1BxD,IAAI,EAAEgD,GAAG,CAAChD,IAAI,IAAIlK,WAAW,CAAC6N,IAAI;UAClCxC,SAAS,EAAE,IAAI,CAAC8E,QAAQ,CAACjD,GAAG,CAAC7B,SAAS,CAAC;UACvC6D,MAAM,EAAE,KAAK;UACbC,MAAM,EAAEjC,GAAG,CAACiC,MAAM,GACd,IAAI,CAACiB,aAAa,CAAClD,GAAG,CAACiC,MAAM,CAAC,GAC9B;YACEjI,EAAE,EAAE,IAAI,CAACkI,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEjB;QAED,IAAI,CAAC7L,MAAM,CAAC0H,KAAK,CACf,sCAAsC,EACtC+D,cAAc,CACf;QACD,OAAOA,cAAc;;IAEzB,CAAC,CAAC,EACFtP,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD;MACA,OAAO/H,KAAK;IACd,CAAC,CAAC;IACF;IACAI,MAAM,CAAEqR,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC;IAC9B;IACA1R,KAAK,CAAC,CAAC,CAAC,CACT;IAEH,MAAMud,GAAG,GAAGN,IAAI,CAAC9U,SAAS,CAAC;MACzBP,IAAI,EAAG8J,OAAO,IAAI;QAChB;QACA,IAAI,CAAC1N,MAAM,CAAC0H,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEgG,OAAO,CAAC;QAErE;QACA,IAAI,CAAC4L,gCAAgC,CAAC9R,cAAc,EAAEkG,OAAO,CAAC;MAChE,CAAC;MACD1J,KAAK,EAAGmD,GAAG,IAAI;QACb,IAAI,CAACnH,MAAM,CAACgE,KAAK,CAAC,gCAAgC,EAAEmD,GAAG,CAAC;MAC1D;KACD,CAAC;IAEF,IAAI,CAAC3G,aAAa,CAAC+Q,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EAEA;;;;;EAKQK,gCAAgCA,CACtC9R,cAAsB,EACtBkG,OAAgB;IAEhB,IAAI,CAAC1N,MAAM,CAAC0H,KAAK,CACf,oCAAoCF,cAAc,qBAAqBkG,OAAO,CAAChK,EAAE,EAAE,CACpF;IAED;IACA,IAAI,CAACzD,IAAI,CAACsL,GAAG,CAAC,MAAK;MACjB;MACA,IAAI,CAACrL,kBAAkB,CAAC0D,IAAI,CAAC4D,cAAc,CAAC;MAE5C,IAAI,CAACxH,MAAM,CAAC0H,KAAK,CAAC,oDAAoD,CAAC;IACzE,CAAC,CAAC;IAEF;IACA8R,UAAU,CAAC,MAAK;MACd,IAAI,CAACpM,eAAe,CAAC5F,cAAc,CAAC,CAACrD,SAAS,CAAC;QAC7CP,IAAI,EAAG6J,YAAY,IAAI;UACrB,IAAI,CAACzN,MAAM,CAAC0H,KAAK,CACf,8BAA8BF,cAAc,mBAC1CiG,YAAY,EAAEhE,QAAQ,EAAEL,MAAM,IAAI,CACpC,WAAW,CACZ;QACH,CAAC;QACDpF,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gDAAgDwD,cAAc,GAAG,EACjExD,KAAK,CACN;QACH;OACD,CAAC;IACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACT;;EACAM,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAC,IAAI,CAAC0U,YAAY,EAAE,EAAE;MACxB,IAAI,CAAChZ,MAAM,CAACuQ,IAAI,CACd,+EAA+E,CAChF;MACD,OAAOxU,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CAAC,iDAAiD,CAAC;IAEpE,MAAMuR,IAAI,GAAG,IAAI,CAAClZ,MAAM,CACrBoE,SAAS,CAA8B;MACtCI,KAAK,EAAErH;KACR,CAAC,CACDsH,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IACT,IAAI,CAAClJ,MAAM,CAAC0H,KAAK,CACf,uDAAuD,EACvDwB,MAAM,CACP,CACF,EACDhN,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAM6O,IAAI,GAAG7O,MAAM,CAACzE,IAAI,EAAEgV,iBAAiB;MAC3C,IAAI,CAAC1B,IAAI,EAAE;QACT,IAAI,CAAC/X,MAAM,CAACgE,KAAK,CAAC,4BAA4B,CAAC;QAC/C,MAAM,IAAI4D,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAO,IAAI,CAACgF,aAAa,CAACmL,IAAI,CAAC;IACjC,CAAC,CAAC,EACF5b,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MAC/D,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,EACF5L,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMud,GAAG,GAAGN,IAAI,CAAC9U,SAAS,EAAE;IAC5B,IAAI,CAAC3D,aAAa,CAAC+Q,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACAS,8BAA8BA,CAC5BlS,cAAsB;IAEtB,MAAMyR,IAAI,GAAG,IAAI,CAAClZ,MAAM,CACrBoE,SAAS,CAAwC;MAChDI,KAAK,EAAElH,iCAAiC;MACxCmM,SAAS,EAAE;QAAEhC;MAAc;KAC5B,CAAC,CACDhD,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMgE,IAAI,GAAGhE,MAAM,CAACzE,IAAI,EAAEkV,mBAAmB;MAC7C,IAAI,CAACzM,IAAI,EAAE,MAAM,IAAItF,KAAK,CAAC,kCAAkC,CAAC;MAE9D,MAAM0F,sBAAsB,GAAiB;QAC3C,GAAGJ,IAAI;QACPK,YAAY,EACVL,IAAI,CAACK,YAAY,EAAErR,GAAG,CAAE+R,CAAC,IAAK,IAAI,CAACrB,aAAa,CAACqB,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5D2L,WAAW,EAAE1M,IAAI,CAAC0M,WAAW,GACzB;UACE,GAAG1M,IAAI,CAAC0M,WAAW;UACnBjO,MAAM,EAAE,IAAI,CAACiB,aAAa,CAACM,IAAI,CAAC0M,WAAW,CAACjO,MAAM,CAAC;UACnD9D,SAAS,EAAE,IAAI,CAAC8E,QAAQ,CAACO,IAAI,CAAC0M,WAAW,CAAC/R,SAAS,CAAC;UACpDkE,MAAM,EAAEmB,IAAI,CAAC0M,WAAW,CAAC7N,MAAM,GAC3B,IAAI,CAACY,QAAQ,CAACO,IAAI,CAAC0M,WAAW,CAAC7N,MAAM,CAAC,GACtCnJ,SAAS;UACb;UACAc,EAAE,EAAEwJ,IAAI,CAAC0M,WAAW,CAAClW,EAAE;UACvBwG,OAAO,EAAEgD,IAAI,CAAC0M,WAAW,CAAC1P,OAAO;UACjCxD,IAAI,EAAEwG,IAAI,CAAC0M,WAAW,CAAClT,IAAI;UAC3BgF,MAAM,EAAEwB,IAAI,CAAC0M,WAAW,CAAClO;UACzB;SACD,GACD,IAAI,CAAE;OACX;;MAED,OAAO4B,sBAAsC,CAAC,CAAC;IACjD,CAAC,CAAC,EACFnR,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,kCAAkC,EAClCA,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;IAEH,MAAM2R,GAAG,GAAGN,IAAI,CAAC9U,SAAS,EAAE;IAC5B,IAAI,CAAC3D,aAAa,CAAC+Q,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACAY,0BAA0BA,CACxBrS,cAAsB;IAEtB,MAAMyR,IAAI,GAAG,IAAI,CAAClZ,MAAM,CACrBoE,SAAS,CAAwB;MAChCI,KAAK,EAAE3G,6BAA6B;MACpC4L,SAAS,EAAE;QAAEhC;MAAc;KAC5B,CAAC,CACDhD,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAEqV,eAAe,CAAC,EAC7Czd,MAAM,CAAC0d,OAAO,CAAC,EACf5d,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;IAEH,MAAM2R,GAAG,GAAGN,IAAI,CAAC9U,SAAS,EAAE;IAC5B,IAAI,CAAC3D,aAAa,CAAC+Q,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACQD,YAAYA,CAAA;IAClB,MAAMvO,KAAK,GAAGtH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACqH,KAAK,EAAE;MACV,IAAI,CAACzK,MAAM,CAACuQ,IAAI,CAAC,oBAAoB,CAAC;MACtC,OAAO,KAAK;;IAGd,IAAI;MACF;MACA,MAAMyJ,KAAK,GAAGvP,KAAK,CAACwP,KAAK,CAAC,GAAG,CAAC;MAC9B,IAAID,KAAK,CAAC5Q,MAAM,KAAK,CAAC,EAAE;QACtB,IAAI,CAACpJ,MAAM,CAACuQ,IAAI,CAAC,0BAA0B,CAAC;QAC5C,OAAO,KAAK;;MAGd;MACA,MAAM2J,OAAO,GAAG7W,IAAI,CAACC,KAAK,CAAC6W,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAE1C;MACA,IAAI,CAACE,OAAO,CAACE,GAAG,EAAE;QAChB,IAAI,CAACpa,MAAM,CAACuQ,IAAI,CAAC,8BAA8B,CAAC;QAChD,OAAO,KAAK;;MAGd,MAAM8J,cAAc,GAAG,IAAIvS,IAAI,CAACoS,OAAO,CAACE,GAAG,GAAG,IAAI,CAAC;MACnD,MAAMrS,GAAG,GAAG,IAAID,IAAI,EAAE;MAEtB,IAAIuS,cAAc,GAAGtS,GAAG,EAAE;QACxB,IAAI,CAAC/H,MAAM,CAACuQ,IAAI,CAAC,cAAc,EAAE;UAC/B+J,UAAU,EAAED,cAAc,CAACxX,WAAW,EAAE;UACxCkF,GAAG,EAAEA,GAAG,CAAClF,WAAW;SACrB,CAAC;QACF,OAAO,KAAK;;MAGd,OAAO,IAAI;KACZ,CAAC,OAAOmB,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,0CAA0C,EAC1CA,KAAc,CACf;MACD,OAAO,KAAK;;EAEhB;EAEAI,4BAA4BA,CAAA;IAC1B;IACA,IAAI,CAAC,IAAI,CAAC4U,YAAY,EAAE,EAAE;MACxB,IAAI,CAAChZ,MAAM,CAACuQ,IAAI,CACd,2EAA2E,CAC5E;MACD,OAAOzU,EAAE,CAAC,EAAE,CAAC;;IAGf,IAAI,CAACkE,MAAM,CAAC0H,KAAK,CAAC,kDAAkD,CAAC;IAErE,MAAMuR,IAAI,GAAG,IAAI,CAAClZ,MAAM,CACrBoE,SAAS,CAAyB;MACjCI,KAAK,EAAE3F;KACR,CAAC,CACD4F,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IACT,IAAI,CAAClJ,MAAM,CAAC0H,KAAK,CACf,wDAAwD,EACxDwB,MAAM,CACP,CACF,EACDhN,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMiI,eAAe,GAAGjI,MAAM,CAACzE,IAAI,EAAE8V,iBAAiB,IAAI,EAAE;MAC5D,IAAI,CAACva,MAAM,CAAC0H,KAAK,CACf,oCAAoC,EACpCyJ,eAAe,CAChB;MACD,IAAI,CAACY,wBAAwB,CAACZ,eAAe,EAAE,IAAI,CAAC;MACpD,OAAOA,eAAe;IACxB,CAAC,CAAC,EACFhV,UAAU,CAAEgL,GAAG,IAAI;MACjB,IAAI,CAACnH,MAAM,CAACgE,KAAK,CACf,wCAAwC,EACxCmD,GAAY,CACb;MACD;MACA,OAAOrL,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;IACF;IACAE,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMud,GAAG,GAAGN,IAAI,CAAC9U,SAAS,EAAE;IAC5B,IAAI,CAAC3D,aAAa,CAAC+Q,IAAI,CAACgI,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACA/U,2BAA2BA,CAAA;IACzB;IACA,MAAMuG,KAAK,GAAGtH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACqH,KAAK,EAAE;MACV,IAAI,CAACzK,MAAM,CAACuQ,IAAI,CACd,6DAA6D,CAC9D;MACD,OAAOtU,KAAK;;IAGd,IAAI,CAAC+D,MAAM,CAAC0H,KAAK,CACf,4DAA4D,CAC7D;IAED,MAAM8S,OAAO,GAAG,IAAI,CAACza,MAAM,CAACoE,SAAS,CAA4B;MAC/DI,KAAK,EAAE1H;KACR,CAAC;IAEF,MAAM4d,UAAU,GAAGD,OAAO,CAAChW,IAAI,CAC7BtI,GAAG,CAAEgN,MAAM,IAAI;MACb,MAAMzF,YAAY,GAAGyF,MAAM,CAACzE,IAAI,EAAEiW,oBAAoB;MACtD,IAAI,CAACjX,YAAY,EAAE;QACjB,MAAM,IAAImE,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CACf,sCAAsC,EACtCjE,YAAY,CACb;MAED,MAAMkX,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACnX,YAAY,CAAC;MAE3D;MACA,IAAI,IAAI,CAACrD,iBAAiB,CAAC0O,GAAG,CAAC6L,UAAU,CAACjX,EAAE,CAAC,EAAE;QAC7C,IAAI,CAAC1D,MAAM,CAAC0H,KAAK,CACf,mBAAmBiT,UAAU,CAACjX,EAAE,6BAA6B,CAC9D;QACD,MAAM,IAAIkE,KAAK,CAAC,sCAAsC,CAAC;;MAGzD;MACA,IAAI,CAAC5H,MAAM,CAAC0H,KAAK,CAAC,iDAAiD,CAAC;MAEpE;MACA,IAAI,CAAC3B,qBAAqB,EAAE;MAE5B;MACA,IAAI,CAAC8U,uBAAuB,CAACF,UAAU,CAAC;MAExC;MACA,IAAI,CAAC1a,IAAI,CAACsL,GAAG,CAAC,MAAK;QACjB;QACA,MAAMuP,oBAAoB,GAAG,IAAI,CAAC3a,aAAa,CAAC8K,KAAK;QACrD,MAAM8P,oBAAoB,GAAG,CAACJ,UAAU,EAAE,GAAGG,oBAAoB,CAAC;QAElE,IAAI,CAAC9a,MAAM,CAAC0H,KAAK,CACf,wDAAwDqT,oBAAoB,CAAC3R,MAAM,SAAS,CAC7F;QAED,IAAI,CAACjJ,aAAa,CAACyD,IAAI,CAACmX,oBAAoB,CAAC;QAC7C,IAAI,CAACza,iBAAiB,CAACsD,IAAI,CAAC,IAAI,CAACtD,iBAAiB,CAAC2K,KAAK,GAAG,CAAC,CAAC;MAC/D,CAAC,CAAC;MAEF,IAAI,CAACjL,MAAM,CAAC0H,KAAK,CACf,+CAA+C,EAC/CiT,UAAU,CACX;MAED,OAAOA,UAAU;IACnB,CAAC,CAAC;IACF;IACAxe,UAAU,CAAEgL,GAAG,IAAI;MACjB,IACEA,GAAG,YAAYS,KAAK,IACpBT,GAAG,CAACuG,OAAO,KAAK,sCAAsC,EACtD;QACA,OAAOzR,KAAK;;MAGd,IAAI,CAAC+D,MAAM,CAACgE,KAAK,CAAC,oCAAoC,EAAEmD,GAAY,CAAC;MACrE,OAAOlL,KAAK;IACd,CAAC,CAAC;IACF;IACAG,GAAG,CAAEqH,YAAY,IAAI;MACnB,IAAI,CAACzD,MAAM,CAAC0H,KAAK,CACf,6CAA6C,EAC7CjE,YAAY,CACb;IACH,CAAC,CAAC,CACH;IAED,MAAM8V,GAAG,GAAGkB,UAAU,CAACtW,SAAS,CAAC;MAC/BP,IAAI,EAAGH,YAAY,IAAI;QACrB,IAAI,CAACzD,MAAM,CAAC0H,KAAK,CACf,yCAAyC,EACzCjE,YAAY,CACb;MACH,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAACxD,aAAa,CAAC+Q,IAAI,CAACgI,GAAG,CAAC;IAC5B,IAAI,CAACvZ,MAAM,CAAC0H,KAAK,CAAC,mDAAmD,CAAC;IACtE,OAAO+S,UAAU;EACnB;EACA;EACA;EACA;EAEQzX,oBAAoBA,CAAA;IAC1B,IAAI,CAACgY,eAAe,GAAGC,WAAW,CAAC,MAAK;MACtC,IAAI,CAACC,2BAA2B,EAAE;IACpC,CAAC,EAAE,OAAO,CAAC;EACb;EACQA,2BAA2BA,CAAA;IACjC,MAAMnT,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMqT,aAAa,GAAG,IAAIrT,IAAI,CAACC,GAAG,CAACqT,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAExE,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAI,CAACjb,iBAAiB,CAACoD,OAAO,CAAC,CAACC,YAAY,EAAEC,EAAE,KAAI;MAClD,MAAM4X,gBAAgB,GAAG,IAAIxT,IAAI,CAACrE,YAAY,CAACoE,SAAS,CAAC;MACzD,IAAIyT,gBAAgB,GAAGH,aAAa,EAAE;QACpC,IAAI,CAAC/a,iBAAiB,CAACmb,MAAM,CAAC7X,EAAE,CAAC;QACjC2X,YAAY,EAAE;;IAElB,CAAC,CAAC;IAEF,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAACrb,MAAM,CAAC0H,KAAK,CAAC,cAAc2T,YAAY,wBAAwB,CAAC;MAErE;MACA,MAAMG,sBAAsB,GAAG3X,KAAK,CAACtH,IAAI,CACvC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAChC;MACD,MAAMoL,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CACtDqM,sBAAsB,CACvB;MAED,IAAI,CAACrb,aAAa,CAACyD,IAAI,CAACsL,mBAAmB,CAAC;MAC5C,IAAI,CAACnL,iBAAiB,EAAE;;EAE5B;EACA;;;;;EAKQoL,uBAAuBA,CAC7BhP,aAA6B;IAE7B,OAAOA,aAAa,CAACsb,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjC;MACA,MAAMC,KAAK,GAAG,IAAI9T,IAAI,CAAC4T,CAAC,CAAC7T,SAAS,IAAI,CAAC,CAAC;MACxC,MAAMgU,KAAK,GAAG,IAAI/T,IAAI,CAAC6T,CAAC,CAAC9T,SAAS,IAAI,CAAC,CAAC;MACxC,OAAOgU,KAAK,CAACT,OAAO,EAAE,GAAGQ,KAAK,CAACR,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ;;EAEQxP,gBAAgBA,CAAA;IACtB,OAAOzI,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC7C;EACQuG,gBAAgBA,CAAC+D,OAAgB;IACvC,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAAC1N,MAAM,CAACgE,KAAK,CACf,6DAA6D,CAC9D;MACD,MAAM,IAAI4D,KAAK,CAAC,4BAA4B,CAAC;;IAG/C,IAAI;MACF;MACA,IAAI,CAAC8F,OAAO,CAAChK,EAAE,IAAI,CAACgK,OAAO,CAACQ,GAAG,EAAE;QAC/B,IAAI,CAAClO,MAAM,CAACgE,KAAK,CACf,wCAAwC,EACxCpB,SAAS,EACT8K,OAAO,CACR;QACD,MAAM,IAAI9F,KAAK,CAAC,wBAAwB,CAAC;;MAG3C;MACA,IAAIkU,gBAAgB;MACpB,IAAI;QACFA,gBAAgB,GAAGpO,OAAO,CAAC/B,MAAM,GAC7B,IAAI,CAACiB,aAAa,CAACc,OAAO,CAAC/B,MAAM,CAAC,GAClC/I,SAAS;OACd,CAAC,OAAOoB,KAAK,EAAE;QACd,IAAI,CAAChE,MAAM,CAACuQ,IAAI,CACd,yEAAyE,EACzEvM,KAAK,CACN;QACD8X,gBAAgB,GAAG;UACjB5N,GAAG,EAAER,OAAO,CAACpE,QAAQ,IAAI,SAAS;UAClC5F,EAAE,EAAEgK,OAAO,CAACpE,QAAQ,IAAI,SAAS;UACjCuC,QAAQ,EAAE,cAAc;UACxBkQ,KAAK,EAAE,qBAAqB;UAC5BC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE;SACX;;MAGH;MACA,IAAIC,kBAAkB;MACtB,IAAIxO,OAAO,CAACyO,QAAQ,EAAE;QACpB,IAAI;UACFD,kBAAkB,GAAG,IAAI,CAACtP,aAAa,CAACc,OAAO,CAACyO,QAAQ,CAAC;SAC1D,CAAC,OAAOnY,KAAK,EAAE;UACd,IAAI,CAAChE,MAAM,CAACuQ,IAAI,CACd,2EAA2E,EAC3EvM,KAAK,CACN;UACDkY,kBAAkB,GAAG;YACnBhO,GAAG,EAAER,OAAO,CAACpG,UAAU,IAAI,SAAS;YACpC5D,EAAE,EAAEgK,OAAO,CAACpG,UAAU,IAAI,SAAS;YACnCuE,QAAQ,EAAE,cAAc;YACxBkQ,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;WACX;;;MAIL;MACA,MAAMG,qBAAqB,GACzB1O,OAAO,CAACyL,WAAW,EAAEjd,GAAG,CAAEmd,GAAG,KAAM;QACjC3V,EAAE,EAAE2V,GAAG,CAAC3V,EAAE,IAAI2V,GAAG,CAACnL,GAAG,IAAI,cAAcpG,IAAI,CAACC,GAAG,EAAE,EAAE;QACnDsU,GAAG,EAAEhD,GAAG,CAACgD,GAAG,IAAI,EAAE;QAClB3V,IAAI,EAAE2S,GAAG,CAAC3S,IAAI,IAAI,SAAS;QAC3B5B,IAAI,EAAEuU,GAAG,CAACvU,IAAI,IAAI,YAAY;QAC9B6C,IAAI,EAAE0R,GAAG,CAAC1R,IAAI,IAAI,CAAC;QACnBF,QAAQ,EAAE4R,GAAG,CAAC5R,QAAQ,IAAI;OAC3B,CAAC,CAAC,IAAI,EAAE;MAEX;MACA,MAAM6D,iBAAiB,GAAG;QACxB,GAAGoC,OAAO;QACVQ,GAAG,EAAER,OAAO,CAAChK,EAAE,IAAIgK,OAAO,CAACQ,GAAG;QAC9BxK,EAAE,EAAEgK,OAAO,CAAChK,EAAE,IAAIgK,OAAO,CAACQ,GAAG;QAC7BhE,OAAO,EAAEwD,OAAO,CAACxD,OAAO,IAAI,EAAE;QAC9ByB,MAAM,EAAEmQ,gBAAgB;QACxBjU,SAAS,EAAE,IAAI,CAACuE,aAAa,CAACsB,OAAO,CAAC7F,SAAS,CAAC;QAChDkE,MAAM,EAAE2B,OAAO,CAAC3B,MAAM,GAAG,IAAI,CAACK,aAAa,CAACsB,OAAO,CAAC3B,MAAM,CAAC,GAAGnJ,SAAS;QACvEuW,WAAW,EAAEiD,qBAAqB;QAClCjU,QAAQ,EAAEuF,OAAO,CAACvF,QAAQ,IAAI;OAC/B;MAED;MACA,IAAI+T,kBAAkB,EAAE;QACtB5Q,iBAAiB,CAAC6Q,QAAQ,GAAGD,kBAAkB;;MAGjD,IAAI,CAAClc,MAAM,CAAC0H,KAAK,CAAC,kDAAkD,EAAE;QACpEmC,SAAS,EAAEyB,iBAAiB,CAAC5H,EAAE;QAC/B4F,QAAQ,EAAEgC,iBAAiB,CAACK,MAAM,EAAEjI;OACrC,CAAC;MAEF,OAAO4H,iBAAiB;KACzB,CAAC,OAAOtH,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,GAAG,IAAI4D,KAAK,CAAC0U,MAAM,CAACtY,KAAK,CAAC,CAAC,EACzD0J,OAAO,CACR;MACD,MAAM,IAAI9F,KAAK,CACb,gCACE5D,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,CAAC0J,OAAO,GAAG4O,MAAM,CAACtY,KAAK,CACvD,EAAE,CACH;;EAEL;EAEO4I,aAAaA,CAACmL,IAAS;IAC5B,IAAI,CAACA,IAAI,EAAE;MACT,MAAM,IAAInQ,KAAK,CAAC,yBAAyB,CAAC;;IAG5C;IACA,MAAMkF,MAAM,GAAGiL,IAAI,CAACrU,EAAE,IAAIqU,IAAI,CAAC7J,GAAG;IAClC,IAAI,CAACpB,MAAM,EAAE;MACX,MAAM,IAAIlF,KAAK,CAAC,qBAAqB,CAAC;;IAGxC;IACA,MAAMiE,QAAQ,GAAGkM,IAAI,CAAClM,QAAQ,IAAI,cAAc;IAChD,MAAMkQ,KAAK,GAAGhE,IAAI,CAACgE,KAAK,IAAI,QAAQjP,MAAM,cAAc;IACxD,MAAMmP,QAAQ,GACZlE,IAAI,CAACkE,QAAQ,KAAKrZ,SAAS,IAAImV,IAAI,CAACkE,QAAQ,KAAK,IAAI,GACjDlE,IAAI,CAACkE,QAAQ,GACb,IAAI;IACV,MAAMD,IAAI,GAAGjE,IAAI,CAACiE,IAAI,IAAI,MAAM;IAEhC;IACA,OAAO;MACL9N,GAAG,EAAEpB,MAAM;MACXpJ,EAAE,EAAEoJ,MAAM;MACVjB,QAAQ,EAAEA,QAAQ;MAClBkQ,KAAK,EAAEA,KAAK;MACZC,IAAI,EAAEA,IAAI;MACVC,QAAQ,EAAEA,QAAQ;MAClB;MACAM,KAAK,EAAExE,IAAI,CAACwE,KAAK,IAAI,IAAI;MACzBC,GAAG,EAAEzE,IAAI,CAACyE,GAAG;MACb7E,QAAQ,EAAEI,IAAI,CAACJ,QAAQ,IAAI,KAAK;MAChC8E,UAAU,EAAE1E,IAAI,CAAC0E,UAAU,GAAG,IAAI3U,IAAI,CAACiQ,IAAI,CAAC0E,UAAU,CAAC,GAAG7Z,SAAS;MACnE8Z,SAAS,EAAE3E,IAAI,CAAC2E,SAAS,GAAG,IAAI5U,IAAI,CAACiQ,IAAI,CAAC2E,SAAS,CAAC,GAAG9Z,SAAS;MAChE+Z,SAAS,EAAE5E,IAAI,CAAC4E,SAAS,GAAG,IAAI7U,IAAI,CAACiQ,IAAI,CAAC4E,SAAS,CAAC,GAAG/Z,SAAS;MAChEga,cAAc,EAAE7E,IAAI,CAAC6E,cAAc;MACnCC,cAAc,EAAE9E,IAAI,CAAC8E,cAAc;MACnCC,SAAS,EAAE/E,IAAI,CAAC+E;KACjB;EACH;EACQ3P,qBAAqBA,CAACD,IAAkB;IAC9C,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAAClN,MAAM,CAACgE,KAAK,CACf,kEAAkE,CACnE;MACD,MAAM,IAAI4D,KAAK,CAAC,iCAAiC,CAAC;;IAGpD,IAAI;MACF;MACA,IAAI,CAACsF,IAAI,CAACxJ,EAAE,IAAI,CAACwJ,IAAI,CAACgB,GAAG,EAAE;QACzB,IAAI,CAAClO,MAAM,CAACgE,KAAK,CACf,6CAA6C,EAC7CpB,SAAS,EACTsK,IAAI,CACL;QACD,MAAM,IAAItF,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,MAAMmV,sBAAsB,GAAG,EAAE;MACjC,IAAI7P,IAAI,CAACK,YAAY,IAAI1J,KAAK,CAACmZ,OAAO,CAAC9P,IAAI,CAACK,YAAY,CAAC,EAAE;QACzD,KAAK,MAAM0P,WAAW,IAAI/P,IAAI,CAACK,YAAY,EAAE;UAC3C,IAAI;YACF,IAAI0P,WAAW,EAAE;cACfF,sBAAsB,CAACxL,IAAI,CAAC,IAAI,CAAC3E,aAAa,CAACqQ,WAAW,CAAC,CAAC;;WAE/D,CAAC,OAAOjZ,KAAK,EAAE;YACd,IAAI,CAAChE,MAAM,CAACuQ,IAAI,CACd,0DAA0D,EAC1DvM,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAAChE,MAAM,CAACuQ,IAAI,CACd,iFAAiF,EACjFrD,IAAI,CACL;;MAGH;MACA,MAAMgQ,kBAAkB,GAAG,EAAE;MAC7B,IAAIhQ,IAAI,CAACzD,QAAQ,IAAI5F,KAAK,CAACmZ,OAAO,CAAC9P,IAAI,CAACzD,QAAQ,CAAC,EAAE;QACjD,IAAI,CAACzJ,MAAM,CAAC0H,KAAK,CAAC,mDAAmD,EAAE;UACrEsJ,KAAK,EAAE9D,IAAI,CAACzD,QAAQ,CAACL;SACtB,CAAC;QAEF,KAAK,MAAMsE,OAAO,IAAIR,IAAI,CAACzD,QAAQ,EAAE;UACnC,IAAI;YACF,IAAIiE,OAAO,EAAE;cACX,MAAMpC,iBAAiB,GAAG,IAAI,CAAC3B,gBAAgB,CAAC+D,OAAO,CAAC;cACxD,IAAI,CAAC1N,MAAM,CAAC0H,KAAK,CACf,kDAAkD,EAClD;gBACEmC,SAAS,EAAEyB,iBAAiB,CAAC5H,EAAE;gBAC/BwG,OAAO,EAAEoB,iBAAiB,CAACpB,OAAO,EAAEM,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBACpDmB,MAAM,EAAEL,iBAAiB,CAACK,MAAM,EAAEE;eACnC,CACF;cACDqR,kBAAkB,CAAC3L,IAAI,CAACjG,iBAAiB,CAAC;;WAE7C,CAAC,OAAOtH,KAAK,EAAE;YACd,IAAI,CAAChE,MAAM,CAACuQ,IAAI,CACd,sEAAsE,EACtEvM,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAAChE,MAAM,CAAC0H,KAAK,CACf,8EAA8E,CAC/E;;MAGH;MACA,IAAIyV,qBAAqB,GAAG,IAAI;MAChC,IAAIjQ,IAAI,CAAC0M,WAAW,EAAE;QACpB,IAAI;UACFuD,qBAAqB,GAAG,IAAI,CAACxT,gBAAgB,CAACuD,IAAI,CAAC0M,WAAW,CAAC;SAChE,CAAC,OAAO5V,KAAK,EAAE;UACd,IAAI,CAAChE,MAAM,CAACuQ,IAAI,CACd,6DAA6D,EAC7DvM,KAAK,CACN;;;MAIL;MACA,MAAMsJ,sBAAsB,GAAG;QAC7B,GAAGJ,IAAI;QACPgB,GAAG,EAAEhB,IAAI,CAACxJ,EAAE,IAAIwJ,IAAI,CAACgB,GAAG;QACxBxK,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE,IAAIwJ,IAAI,CAACgB,GAAG;QACvBX,YAAY,EAAEwP,sBAAsB;QACpCtT,QAAQ,EAAEyT,kBAAkB;QAC5BtD,WAAW,EAAEuD,qBAAqB;QAClCC,WAAW,EAAElQ,IAAI,CAACkQ,WAAW,IAAI,CAAC;QAClCrP,OAAO,EAAE,CAAC,CAACb,IAAI,CAACa,OAAO;QACvB2O,SAAS,EAAE,IAAI,CAACtQ,aAAa,CAACc,IAAI,CAACwP,SAAS,CAAC;QAC7CC,SAAS,EAAE,IAAI,CAACvQ,aAAa,CAACc,IAAI,CAACyP,SAAS;OAC7C;MAED,IAAI,CAAC3c,MAAM,CAAC0H,KAAK,CACf,uDAAuD,EACvD;QACEF,cAAc,EAAE8F,sBAAsB,CAAC5J,EAAE;QACzC2Z,gBAAgB,EAAEN,sBAAsB,CAAC3T,MAAM;QAC/CkU,YAAY,EAAEJ,kBAAkB,CAAC9T;OAClC,CACF;MAED,OAAOkE,sBAAsB;KAC9B,CAAC,OAAOtJ,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,kDAAkD,EAClDA,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,GAAG,IAAI4D,KAAK,CAAC0U,MAAM,CAACtY,KAAK,CAAC,CAAC,EACzDkJ,IAAI,CACL;MACD,MAAM,IAAItF,KAAK,CACb,qCACE5D,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,CAAC0J,OAAO,GAAG4O,MAAM,CAACtY,KAAK,CACvD,EAAE,CACH;;EAEL;EACQoI,aAAaA,CAACzJ,IAA+B;IACnD,IAAI,CAACA,IAAI,EAAE,OAAO,IAAImF,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOnF,IAAI,KAAK,QAAQ,GAAG,IAAImF,IAAI,CAACnF,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOqB,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACuQ,IAAI,CAAC,yBAAyB5N,IAAI,EAAE,EAAEqB,KAAK,CAAC;MACxD,OAAO,IAAI8D,IAAI,EAAE;;EAErB;EAEA;EACQ6E,QAAQA,CAAChK,IAA+B;IAC9C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAImF,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOnF,IAAI,KAAK,QAAQ,GAAG,IAAImF,IAAI,CAACnF,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOqB,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACuQ,IAAI,CAAC,+BAA+B5N,IAAI,EAAE,EAAEqB,KAAK,CAAC;MAC9D,OAAO,IAAI8D,IAAI,EAAE;;EAErB;EAOQ8S,qBAAqBA,CAACnX,YAA0B;IACtD,IAAI,CAACzD,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1BjE,YAAY,CACb;IAED,IAAI,CAACA,YAAY,EAAE;MACjB,IAAI,CAACzD,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;MACxE,MAAM,IAAI4D,KAAK,CAAC,0BAA0B,CAAC;;IAG7C;IACA,MAAMwI,cAAc,GAAG3M,YAAY,CAACC,EAAE,IAAKD,YAAoB,CAACyK,GAAG;IACnE,IAAI,CAACkC,cAAc,EAAE;MACnB,IAAI,CAACpQ,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BP,YAAY,CACb;MACD,MAAM,IAAImE,KAAK,CAAC,6BAA6B,CAAC;;IAGhD,IAAI,CAACnE,YAAY,CAACoE,SAAS,EAAE;MAC3B,IAAI,CAAC7H,MAAM,CAACuQ,IAAI,CACd,gBAAgB,EAChB,uDAAuD,EACvD9M,YAAY,CACb;MACDA,YAAY,CAACoE,SAAS,GAAG,IAAIC,IAAI,EAAE;;IAGrC,IAAI;MACF,MAAM6S,UAAU,GAAG;QACjB,GAAGlX,YAAY;QACfyK,GAAG,EAAEkC,cAAc;QACnB1M,EAAE,EAAE0M,cAAc;QAClBvI,SAAS,EAAE,IAAIC,IAAI,CAACrE,YAAY,CAACoE,SAAS,CAAC;QAC3C,IAAIpE,YAAY,CAAC6F,QAAQ,IAAI;UAC3BA,QAAQ,EAAE,IAAI,CAACiU,eAAe,CAAC9Z,YAAY,CAAC6F,QAAQ;SACrD,CAAC;QACF,IAAI7F,YAAY,CAACiK,OAAO,IAAI;UAC1BA,OAAO,EAAE,IAAI,CAAC8P,mBAAmB,CAAC/Z,YAAY,CAACiK,OAAO;SACvD;OACF;MAED,IAAI,CAAC1N,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCiT,UAAU,CACX;MACD,OAAOA,UAAU;KAClB,CAAC,OAAO3W,KAAK,EAAE;MACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCA,KAAK,CACN;MACD,MAAMA,KAAK;;EAEf;EACQuZ,eAAeA,CAAC5R,MAAW;IACjC,OAAO;MACLjI,EAAE,EAAEiI,MAAM,CAACjI,EAAE;MACbmI,QAAQ,EAAEF,MAAM,CAACE,QAAQ;MACzB,IAAIF,MAAM,CAAC4Q,KAAK,IAAI;QAAEA,KAAK,EAAE5Q,MAAM,CAAC4Q;MAAK,CAAE;KAC5C;EACH;EAEA;;;;;EAKQiB,mBAAmBA,CAAC9P,OAAY;IACtC,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,OAAO;MACLhK,EAAE,EAAEgK,OAAO,CAAChK,EAAE,IAAIgK,OAAO,CAACQ,GAAG;MAC7BhE,OAAO,EAAEwD,OAAO,CAACxD,OAAO,IAAI,EAAE;MAC9BxD,IAAI,EAAEgH,OAAO,CAAChH,IAAI,IAAI,MAAM;MAC5BmB,SAAS,EAAE,IAAI,CAAC8E,QAAQ,CAACe,OAAO,CAAC7F,SAAS,CAAC;MAC3CsR,WAAW,EAAEzL,OAAO,CAACyL,WAAW,IAAI,EAAE;MACtC,IAAIzL,OAAO,CAAC/B,MAAM,IAAI;QAAEA,MAAM,EAAE,IAAI,CAAC4R,eAAe,CAAC7P,OAAO,CAAC/B,MAAM;MAAC,CAAE;KACvE;EACH;EACA;;;;;EAKQqD,WAAWA,CACjB7O,aAA4C,EAC5Csd,cAAA,GAA0B,IAAI;IAE9B,MAAMC,iBAAiB,GAAG7Z,KAAK,CAACmZ,OAAO,CAAC7c,aAAa,CAAC,GAClDA,aAAa,GACb,CAACA,aAAa,CAAC;IAEnB,IAAI,CAACH,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,oCAAoCgW,iBAAiB,CAACtU,MAAM,gBAAgB,CAC7E;IAED,IAAIsU,iBAAiB,CAACtU,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACpJ,MAAM,CAACuQ,IAAI,CAAC,gBAAgB,EAAE,qCAAqC,CAAC;MACzE;;IAGF;IACA,MAAMoN,kBAAkB,GAAGD,iBAAiB,CAACrhB,MAAM,CAChDwS,KAAK,IAAKA,KAAK,KAAKA,KAAK,CAACnL,EAAE,IAAKmL,KAAa,CAACX,GAAG,CAAC,CACrD;IAED,IAAIyP,kBAAkB,CAACvU,MAAM,KAAKsU,iBAAiB,CAACtU,MAAM,EAAE;MAC1D,IAAI,CAACpJ,MAAM,CAACuQ,IAAI,CACd,gBAAgB,EAChB,SACEmN,iBAAiB,CAACtU,MAAM,GAAGuU,kBAAkB,CAACvU,MAChD,kCAAkC,CACnC;;IAGH,IAAIwU,UAAU,GAAG,CAAC;IAClB,IAAIC,YAAY,GAAG,CAAC;IAEpB;IACAF,kBAAkB,CAACna,OAAO,CAAC,CAACqL,KAAK,EAAEE,KAAK,KAAI;MAC1C,IAAI;QACF;QACA,MAAM+O,OAAO,GAAGjP,KAAK,CAACnL,EAAE,IAAKmL,KAAa,CAACX,GAAG;QAC9C,IAAI,CAAC4P,OAAO,EAAE;UACZ,IAAI,CAAC9d,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1B6K,KAAK,CACN;UACD;;QAGF;QACA,MAAM8L,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAAC/L,KAAK,CAAC;QAEpD;QACA,IAAI4O,cAAc,IAAI,IAAI,CAACrd,iBAAiB,CAAC0O,GAAG,CAAC6L,UAAU,CAACjX,EAAE,CAAC,EAAE;UAC/D,IAAI,CAAC1D,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,gBAAgBiT,UAAU,CAACjX,EAAE,oCAAoC,CAClE;UACDma,YAAY,EAAE;UACd;;QAGF;QACA,IAAI,CAACzd,iBAAiB,CAACuD,GAAG,CAACgX,UAAU,CAACjX,EAAE,EAAEiX,UAAU,CAAC;QACrDiD,UAAU,EAAE;QAEZ,IAAI,CAAC5d,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,sBAAsBiT,UAAU,CAACjX,EAAE,WAAW,CAC/C;OACF,CAAC,OAAOM,KAAK,EAAE;QACd,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iCAAiC+K,KAAK,GAAG,CAAC,GAAG,EAC7C/K,KAAK,CACN;;IAEL,CAAC,CAAC;IAEF,IAAI,CAAChE,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,0BAA0BkW,UAAU,WAAWC,YAAY,oBAAoB,IAAI,CAACzd,iBAAiB,CAACuH,IAAI,EAAE,CAC7G;IAED;IACA,IAAI,CAACoW,8BAA8B,EAAE;EACvC;EACA;;;;EAIQA,8BAA8BA,CAAA;IACpC,MAAMC,gBAAgB,GAAGna,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAAC;IAEpE;IACA,MAAMoL,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CAAC6O,gBAAgB,CAAC;IAE1E,IAAI,CAAChe,MAAM,CAAC0H,KAAK,CACf,cAAcwH,mBAAmB,CAAC9F,MAAM,2DAA2D,CACpG;IAED,IAAI,CAACjJ,aAAa,CAACyD,IAAI,CAACsL,mBAAmB,CAAC;IAC5C,IAAI,CAACnL,iBAAiB,EAAE;IACxB,IAAI,CAACqL,+BAA+B,EAAE;EACxC;EAEA;;;EAGQrL,iBAAiBA,CAAA;IACvB,MAAMiN,KAAK,GAAGnN,KAAK,CAACtH,IAAI,CAAC,IAAI,CAAC6D,iBAAiB,CAAC0D,MAAM,EAAE,CAAC,CAACzH,MAAM,CAC7DqT,CAAC,IAAK,CAACA,CAAC,CAAChE,MAAM,CACjB,CAACtC,MAAM;IACR,IAAI,CAAC9I,iBAAiB,CAACsD,IAAI,CAACoN,KAAK,CAAC;EACpC;EAEA;;;;EAIQ6J,uBAAuBA,CAACpX,YAA0B;IACxD,IAAI,CAACuL,WAAW,CAACvL,YAAY,EAAE,IAAI,CAAC;EACtC;EACA;;;;;EAKQsO,wBAAwBA,CAACkM,GAAa,EAAEvS,MAAe;IAC7DuS,GAAG,CAACza,OAAO,CAAEE,EAAE,IAAI;MACjB,MAAMmL,KAAK,GAAG,IAAI,CAACzO,iBAAiB,CAACkR,GAAG,CAAC5N,EAAE,CAAC;MAC5C,IAAImL,KAAK,EAAE;QACT,IAAI,CAACzO,iBAAiB,CAACuD,GAAG,CAACD,EAAE,EAAE;UAC7B,GAAGmL,KAAK;UACRnD,MAAM;UACNK,MAAM,EAAEL,MAAM,GAAG,IAAI5D,IAAI,EAAE,CAACjF,WAAW,EAAE,GAAGD;SAC7C,CAAC;;IAEN,CAAC,CAAC;IACF,IAAI,CAACmb,8BAA8B,EAAE;EACvC;EAEA;;;;;EAKQtN,4BAA4BA,CAACU,eAAyB;IAC5D,IAAIX,YAAY,GAAG,CAAC;IACpBW,eAAe,CAAC3N,OAAO,CAAEE,EAAE,IAAI;MAC7B,IAAI,IAAI,CAACtD,iBAAiB,CAAC0O,GAAG,CAACpL,EAAE,CAAC,EAAE;QAClC,IAAI,CAACtD,iBAAiB,CAACmb,MAAM,CAAC7X,EAAE,CAAC;QACjC8M,YAAY,EAAE;;IAElB,CAAC,CAAC;IAEF,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAACuN,8BAA8B,EAAE;;IAGvC,OAAOvN,YAAY;EACrB;EAEA;;;;;;EAMQG,mBAAmBA,CACzB3M,KAAU,EACVka,SAAiB,EACjBC,gBAAqB;IAErB,IAAI,CAACne,MAAM,CAACgE,KAAK,CAAC,gBAAgB,EAAE,kBAAkBka,SAAS,GAAG,EAAEla,KAAK,CAAC;IAC1E,OAAOlI,EAAE,CAACqiB,gBAAgB,CAAC;EAC7B;EACA;EACAC,WAAWA,CAAC5W,cAAsB;IAChC,MAAMsF,MAAM,GAAG,IAAI,CAAClB,gBAAgB,EAAE;IACtC,IAAI,CAACkB,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACuQ,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOzU,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAACiE,MAAM,CACfgK,MAAM,CAAsB;MAC3BC,QAAQ,EAAEtM,qBAAqB;MAC/B8L,SAAS,EAAE;QACTkP,KAAK,EAAE;UACLlR,cAAc;UACdsF;;;KAGL,CAAC,CACDtI,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAE2Z,WAAW,IAAI,KAAK,CAAC,EAClDjiB,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAOjI,UAAU,CACf,MAAM,IAAI6L,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEAyW,UAAUA,CAAC7W,cAAsB;IAC/B,MAAMsF,MAAM,GAAG,IAAI,CAAClB,gBAAgB,EAAE;IACtC,IAAI,CAACkB,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACuQ,IAAI,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;MACpE,OAAOzU,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAACiE,MAAM,CACfgK,MAAM,CAAqB;MAC1BC,QAAQ,EAAErM,oBAAoB;MAC9B6L,SAAS,EAAE;QACTkP,KAAK,EAAE;UACLlR,cAAc;UACdsF;;;KAGL,CAAC,CACDtI,IAAI,CACHtI,GAAG,CAAEgN,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAE4Z,UAAU,IAAI,KAAK,CAAC,EACjDliB,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;;;;;EAOAyB,WAAWA,CACT7B,cAAsB,EACtB+B,IAAA,GAAe,GAAG,EAClB/G,KAAA,GAAgB,IAAI;IAEpB,IAAI,CAACxC,MAAM,CAACuK,IAAI,CACd,gBAAgB,EAChB,qCAAqC/C,cAAc,UAAU+B,IAAI,WAAW/G,KAAK,EAAE,CACpF;IAED,OAAO,IAAI,CAACzC,MAAM,CACfgJ,UAAU,CAA6B;MACtCxE,KAAK,EAAE9F,kBAAkB;MACzB+K,SAAS,EAAE;QACThC,cAAc;QACd+B,IAAI,EAAE+U,QAAQ,CAAC/U,IAAI,EAAE,EAAE,CAAC;QACxB/G,KAAK,EAAE8b,QAAQ,CAAC9b,KAAK,EAAE,EAAE;OAC1B;MACDwG,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBtI,GAAG,CAAEgN,MAAM,IAAI;MACb,IAAI,CAAClJ,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BwB,MAAM,CACP;MAED,IAAIA,MAAM,CAACsF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCkF,MAAM,CAACsF,MAAM,CACd;QACD,MAAM,IAAI5G,KAAK,CAACsB,MAAM,CAACsF,MAAM,CAACtS,GAAG,CAAEuS,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,IAAI,CAACxF,MAAM,CAACzE,IAAI,EAAE4E,WAAW,EAAE;QAC7B,IAAI,CAACrJ,MAAM,CAACuQ,IAAI,CACd,gBAAgB,EAChB,uCAAuC,CACxC;QACD,OAAO,EAAE;;MAGX,MAAM9G,QAAQ,GAAGP,MAAM,CAACzE,IAAI,CAAC4E,WAAW;MACxC,IAAI,CAACrJ,MAAM,CAAC0H,KAAK,CACf,gBAAgB,EAChB,aAAa+B,QAAQ,CAACL,MAAM,WAAW,CACxC;MAED;MACA,OAAOK,QAAQ,CAACvN,GAAG,CAAEwR,OAAO,IAAK,IAAI,CAAC/D,gBAAgB,CAAC+D,OAAO,CAAC,CAAC;IAClE,CAAC,CAAC,EACFvR,UAAU,CAAE6H,KAAK,IAAI;MACnB,IAAI,CAAChE,MAAM,CAACgE,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1BA,KAAK,CACN;MACD,OAAOjI,UAAU,CAAC,MAAM,IAAI6L,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGA2W,iBAAiBA,CAAC1W,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,MAAMlF,IAAI,GAAGkF,SAAS,YAAYC,IAAI,GAAGD,SAAS,GAAG,IAAIC,IAAI,CAACD,SAAS,CAAC;MACxE,OAAOlF,IAAI,CAAC6b,kBAAkB,CAAC,EAAE,EAAE;QACjCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;OACT,CAAC;KACH,CAAC,OAAO3a,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGA4a,gBAAgBA,CAACnC,UAAqC;IACpD,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,MAAMoC,cAAc,GAClBpC,UAAU,YAAY3U,IAAI,GAAG2U,UAAU,GAAG,IAAI3U,IAAI,CAAC2U,UAAU,CAAC;IAChE,MAAM1U,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMgX,SAAS,GACb5M,IAAI,CAAC6M,GAAG,CAAChX,GAAG,CAACqT,OAAO,EAAE,GAAGyD,cAAc,CAACzD,OAAO,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAI0D,SAAS,GAAG,EAAE,EAAE;MAClB,OAAO,UAAUD,cAAc,CAACL,kBAAkB,CAAC,EAAE,EAAE;QACrDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;;IAEN,OAAO,UAAUG,cAAc,CAACG,kBAAkB,EAAE,EAAE;EACxD;EAEA;;;EAGAC,iBAAiBA,CAACpX,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IAErC,IAAI;MACF,MAAMlF,IAAI,GAAGkF,SAAS,YAAYC,IAAI,GAAGD,SAAS,GAAG,IAAIC,IAAI,CAACD,SAAS,CAAC;MACxE,MAAMqX,KAAK,GAAG,IAAIpX,IAAI,EAAE;MAExB,IAAInF,IAAI,CAACwc,YAAY,EAAE,KAAKD,KAAK,CAACC,YAAY,EAAE,EAAE;QAChD,OAAOxc,IAAI,CAAC6b,kBAAkB,CAAC,EAAE,EAAE;UACjCC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC;;MAGJ,MAAMU,SAAS,GAAG,IAAItX,IAAI,CAACoX,KAAK,CAAC;MACjCE,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;MAE1C,IAAI3c,IAAI,CAACwc,YAAY,EAAE,KAAKC,SAAS,CAACD,YAAY,EAAE,EAAE;QACpD,OAAO,SAASxc,IAAI,CAAC6b,kBAAkB,CAAC,EAAE,EAAE;UAC1CC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC,EAAE;;MAGN,MAAMa,GAAG,GAAG5c,IAAI,CACbqc,kBAAkB,CAAC,OAAO,EAAE;QAAEQ,OAAO,EAAE;MAAO,CAAE,CAAC,CACjDC,WAAW,EAAE;MAChB,OAAO,GAAGF,GAAG,MAAM5c,IAAI,CAAC6b,kBAAkB,CAAC,EAAE,EAAE;QAC7CC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;KACL,CAAC,OAAO1a,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGA0b,oBAAoBA,CAACjW,QAAe,EAAEsF,KAAa;IACjD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,IAAI;MACF,MAAM4Q,UAAU,GAAGlW,QAAQ,CAACsF,KAAK,CAAC;MAClC,MAAM6Q,OAAO,GAAGnW,QAAQ,CAACsF,KAAK,GAAG,CAAC,CAAC;MAEnC,IAAI,CAAC4Q,UAAU,EAAE9X,SAAS,IAAI,CAAC+X,OAAO,EAAE/X,SAAS,EAAE,OAAO,IAAI;MAE9D,MAAMgY,WAAW,GAAG,IAAI,CAACC,oBAAoB,CAACH,UAAU,CAAC9X,SAAS,CAAC;MACnE,MAAMkY,QAAQ,GAAG,IAAI,CAACD,oBAAoB,CAACF,OAAO,CAAC/X,SAAS,CAAC;MAE7D,OAAOgY,WAAW,KAAKE,QAAQ;KAChC,CAAC,OAAO/b,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEQ8b,oBAAoBA,CAACjY,SAAoC;IAC/D,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,OAAO,CACLA,SAAS,YAAYC,IAAI,GAAGD,SAAS,GAAG,IAAIC,IAAI,CAACD,SAAS,CAAC,EAC3DsX,YAAY,EAAE;KACjB,CAAC,OAAOnb,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGAgc,WAAWA,CAACC,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAC/B,IAAIA,QAAQ,CAACtV,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,UAAU;IACpD,IAAIsV,QAAQ,CAAC9R,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,aAAa;IAClD,IAAI8R,QAAQ,CAAC9R,QAAQ,CAAC,MAAM,CAAC,IAAI8R,QAAQ,CAAC9R,QAAQ,CAAC,QAAQ,CAAC,EAC1D,OAAO,cAAc;IACvB,IAAI8R,QAAQ,CAAC9R,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAI8R,QAAQ,CAAC9R,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,oBAAoB;IAChE,IAAI8R,QAAQ,CAAC9R,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAI8R,QAAQ,CAAC9R,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAI8R,QAAQ,CAAC9R,QAAQ,CAAC,KAAK,CAAC,IAAI8R,QAAQ,CAAC9R,QAAQ,CAAC,YAAY,CAAC,EAC7D,OAAO,iBAAiB;IAC1B,OAAO,SAAS;EAClB;EAEA;;;EAGA+R,WAAWA,CAACD,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAME,OAAO,GAA2B;MACtC,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,KAAK;MACxB,oBAAoB,EAAE,UAAU;MAChC,yEAAyE,EACvE,UAAU;MACZ,0BAA0B,EAAE,OAAO;MACnC,mEAAmE,EACjE,OAAO;MACT,+BAA+B,EAAE,YAAY;MAC7C,2EAA2E,EACzE,YAAY;MACd,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,aAAa;MAChC,8BAA8B,EAAE;KACjC;IAED,KAAK,MAAM,CAACC,GAAG,EAAEnV,KAAK,CAAC,IAAItF,MAAM,CAAC0a,OAAO,CAACF,OAAO,CAAC,EAAE;MAClD,IAAIF,QAAQ,CAAC9R,QAAQ,CAACiS,GAAG,CAAC,EAAE,OAAOnV,KAAK;;IAE1C,OAAO,MAAM;EACf;EAEA;;;EAGAqV,QAAQA,CAAC5S,OAAY;IACnB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACyL,WAAW,IAAIzL,OAAO,CAACyL,WAAW,CAAC/P,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,KAAK;;IAGd,MAAMmX,UAAU,GAAG7S,OAAO,CAACyL,WAAW,CAAC,CAAC,CAAC;IACzC,IAAI,CAACoH,UAAU,IAAI,CAACA,UAAU,CAAC7Z,IAAI,EAAE;MACnC,OAAO,KAAK;;IAGd,MAAMA,IAAI,GAAG6Z,UAAU,CAAC7Z,IAAI,CAAC0Q,QAAQ,EAAE;IACvC,OAAO1Q,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO;EAC7C;EAEA;;;EAGA0B,cAAcA,CAACsF,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B;IACA,IACEA,OAAO,CAAChH,IAAI,KAAKlK,WAAW,CAAC8L,aAAa,IAC1CoF,OAAO,CAAChH,IAAI,KAAKlK,WAAW,CAACgkB,mBAAmB,EAChD;MACA,OAAO,IAAI;;IAGb;IACA,IAAI9S,OAAO,CAACyL,WAAW,IAAIzL,OAAO,CAACyL,WAAW,CAAC/P,MAAM,GAAG,CAAC,EAAE;MACzD,OAAOsE,OAAO,CAACyL,WAAW,CAACC,IAAI,CAAEC,GAAQ,IAAI;QAC3C,MAAM3S,IAAI,GAAG2S,GAAG,CAAC3S,IAAI,EAAE0Q,QAAQ,EAAE;QACjC,OACE1Q,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACvBgH,OAAO,CAACvF,QAAQ,EAAEC,cAAc,KAC9B1B,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO,CAAE;MAE7C,CAAC,CAAC;;IAGJ;IACA,OAAO,CAAC,CAACgH,OAAO,CAACvF,QAAQ,EAAEC,cAAc;EAC3C;EAEA;;;EAGAqY,kBAAkBA,CAAC/S,OAAY;IAC7B,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACyL,WAAW,IAAIzL,OAAO,CAACyL,WAAW,CAAC/P,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAMsX,eAAe,GAAGhT,OAAO,CAACyL,WAAW,CAACrL,IAAI,CAAEuL,GAAQ,IAAI;MAC5D,MAAM3S,IAAI,GAAG2S,GAAG,CAAC3S,IAAI,EAAE0Q,QAAQ,EAAE;MACjC,OACE1Q,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;IAEpB,CAAC,CAAC;IAEF,OAAOga,eAAe,EAAErE,GAAG,IAAI,EAAE;EACnC;EAEA;;;EAGAsE,uBAAuBA,CAACjT,OAAY;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IAEtB;IACA,IAAIA,OAAO,CAACvF,QAAQ,EAAEV,QAAQ,EAAE;MAC9B,OAAOiG,OAAO,CAACvF,QAAQ,CAACV,QAAQ;;IAGlC;IACA,IAAIiG,OAAO,CAACyL,WAAW,IAAIzL,OAAO,CAACyL,WAAW,CAAC/P,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMsX,eAAe,GAAGhT,OAAO,CAACyL,WAAW,CAACrL,IAAI,CAAEuL,GAAQ,IAAI;QAC5D,MAAM3S,IAAI,GAAG2S,GAAG,CAAC3S,IAAI,EAAE0Q,QAAQ,EAAE;QACjC,OACE1Q,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;MAEpB,CAAC,CAAC;MAEF,IAAIga,eAAe,IAAIA,eAAe,CAACjZ,QAAQ,EAAE;QAC/C,OAAOiZ,eAAe,CAACjZ,QAAQ;;;IAInC,OAAO,CAAC;EACV;EAEA;;;EAGAmZ,iBAAiBA,CAAC7R,KAAa;IAC7B,MAAM8R,OAAO,GAAG,CACd,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACxE;IACD,OAAOA,OAAO,CAAC9R,KAAK,GAAG8R,OAAO,CAACzX,MAAM,CAAC;EACxC;EAEA;;;EAGA0X,mBAAmBA,CAACC,OAAe;IACjC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;MAC7B,OAAO,MAAM;;IAGf,MAAMC,OAAO,GAAG9O,IAAI,CAAC+O,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAGhP,IAAI,CAAC+O,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAAC9J,QAAQ,EAAE,CAAC+J,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA;;;EAGAC,WAAWA,CAAC1T,OAAY;IACtB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACyL,WAAW,IAAIzL,OAAO,CAACyL,WAAW,CAAC/P,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAMmX,UAAU,GAAG7S,OAAO,CAACyL,WAAW,CAAC,CAAC,CAAC;IACzC,OAAOoH,UAAU,EAAElE,GAAG,IAAI,EAAE;EAC9B;EAEA;;;EAGAgF,cAAcA,CAAC3T,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAOlR,WAAW,CAAC6N,IAAI;IAErC,IAAI;MACF,IAAIqD,OAAO,CAAChH,IAAI,EAAE;QAChB,MAAM4a,OAAO,GAAG5T,OAAO,CAAChH,IAAI,CAAC0Q,QAAQ,EAAE;QACvC,IAAIkK,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UAC5C,OAAO9kB,WAAW,CAAC6N,IAAI;SACxB,MAAM,IAAIiX,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAO9kB,WAAW,CAACoO,KAAK;SACzB,MAAM,IAAI0W,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UACnD,OAAO9kB,WAAW,CAACuO,IAAI;SACxB,MAAM,IAAIuW,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAO9kB,WAAW,CAACsO,KAAK;SACzB,MAAM,IAAIwW,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAO9kB,WAAW,CAACqO,KAAK;SACzB,MAAM,IAAIyW,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;UACvD,OAAO9kB,WAAW,CAAC+kB,MAAM;;;MAI7B,IAAI7T,OAAO,CAACyL,WAAW,EAAE/P,MAAM,EAAE;QAC/B,MAAMmX,UAAU,GAAG7S,OAAO,CAACyL,WAAW,CAAC,CAAC,CAAC;QACzC,IAAIoH,UAAU,IAAIA,UAAU,CAAC7Z,IAAI,EAAE;UACjC,MAAM8a,iBAAiB,GAAGjB,UAAU,CAAC7Z,IAAI,CAAC0Q,QAAQ,EAAE;UAEpD,IAAIoK,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAOhlB,WAAW,CAACoO,KAAK;WACzB,MAAM,IACL4W,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAOhlB,WAAW,CAACuO,IAAI;WACxB,MAAM,IACLyW,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAOhlB,WAAW,CAACsO,KAAK;WACzB,MAAM,IACL0W,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAOhlB,WAAW,CAACqO,KAAK;;;QAI5B,OAAOrO,WAAW,CAACuO,IAAI;;MAGzB,OAAOvO,WAAW,CAAC6N,IAAI;KACxB,CAAC,OAAOrG,KAAK,EAAE;MACd,OAAOxH,WAAW,CAAC6N,IAAI;;EAE3B;EAEA;;;EAGAoX,eAAeA,CAAA;IACb,OAAO,CACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;EACH;EAEA;;;EAGAC,mBAAmBA,CAAChU,OAAY,EAAEE,aAA4B;IAC5D,IAAI,CAACF,OAAO,EAAE;MACZ,OAAO,kCAAkC;;IAG3C,IAAI;MACF,MAAMiU,aAAa,GACjBjU,OAAO,CAAC/B,MAAM,EAAEjI,EAAE,KAAKkK,aAAa,IACpCF,OAAO,CAAC/B,MAAM,EAAEuC,GAAG,KAAKN,aAAa,IACrCF,OAAO,CAACpE,QAAQ,KAAKsE,aAAa;MAEpC,MAAMgU,SAAS,GAAGD,aAAa,GAC3B,kDAAkD,GAClD,qDAAqD;MAEzD,MAAMvX,WAAW,GAAG,IAAI,CAACiX,cAAc,CAAC3T,OAAO,CAAC;MAEhD,IAAIA,OAAO,CAACyL,WAAW,IAAIzL,OAAO,CAACyL,WAAW,CAAC/P,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMmX,UAAU,GAAG7S,OAAO,CAACyL,WAAW,CAAC,CAAC,CAAC;QACzC,IAAIoH,UAAU,IAAIA,UAAU,CAAC7Z,IAAI,EAAE;UACjC,MAAM8a,iBAAiB,GAAGjB,UAAU,CAAC7Z,IAAI,CAAC0Q,QAAQ,EAAE;UACpD,IAAIoK,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAO,cAAc;WACtB,MAAM,IACLA,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO,GAAGI,SAAS,MAAM;;;;MAK/B,IACExX,WAAW,KAAK5N,WAAW,CAACoO,KAAK,IACjCR,WAAW,KAAK5N,WAAW,CAACqlB,WAAW,EACvC;QACA,OAAO,cAAc;OACtB,MAAM,IACLzX,WAAW,KAAK5N,WAAW,CAACuO,IAAI,IAChCX,WAAW,KAAK5N,WAAW,CAACslB,UAAU,EACtC;QACA,OAAO,GAAGF,SAAS,MAAM;;MAG3B,OAAO,GAAGA,SAAS,wDAAwD;KAC5E,CAAC,OAAO5d,KAAK,EAAE;MACd,OAAO,gEAAgE;;EAE3E;EAEA;EACA+d,oBAAoBA,CAAA;IAClB,IAAI,CAACvhB,aAAa,CAACgD,OAAO,CAAE+V,GAAG,IAAKA,GAAG,CAACyI,WAAW,EAAE,CAAC;IACtD,IAAI,CAACxhB,aAAa,GAAG,EAAE;IACvB,IAAI,IAAI,CAACwa,eAAe,EAAE;MACxBiH,aAAa,CAAC,IAAI,CAACjH,eAAe,CAAC;;IAErC,IAAI,CAAC5a,iBAAiB,CAACmD,KAAK,EAAE;IAC9B,IAAI,CAACvD,MAAM,CAAC0H,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEAwa,WAAWA,CAAA;IACT,IAAI,CAACH,oBAAoB,EAAE;EAC7B;;;uBA/7IWliB,cAAc,EAAAsiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAd5iB,cAAc;MAAA6iB,OAAA,EAAd7iB,cAAc,CAAA8iB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}