{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class MessagesSidebarComponent {\n  static {\n    this.ɵfac = function MessagesSidebarComponent_Factory(t) {\n      return new (t || MessagesSidebarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessagesSidebarComponent,\n      selectors: [[\"app-messages-sidebar\"]],\n      decls: 2,\n      vars: 0,\n      template: function MessagesSidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"messages-sidebar works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\t\\\"use strict\\\";\\n\\n \\t\\n\\n \\t\\n\\n })()[_ngcontent-%COMP%]\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MessagesSidebarComponent", "selectors", "decls", "vars", "template", "MessagesSidebarComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages-sidebar\\messages-sidebar.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages-sidebar\\messages-sidebar.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-messages-sidebar',\n  templateUrl: './messages-sidebar.component.html',\n  styleUrls: ['./messages-sidebar.component.css']\n})\nexport class MessagesSidebarComponent {\n\n}\n", "<p>messages-sidebar works!</p>\n"], "mappings": ";AAOA,OAAM,MAAOA,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPrCE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,8BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}