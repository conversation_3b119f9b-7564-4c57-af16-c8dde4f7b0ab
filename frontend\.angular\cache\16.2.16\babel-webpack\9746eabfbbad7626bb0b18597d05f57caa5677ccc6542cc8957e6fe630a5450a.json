{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../services/message.service\";\nimport * as i4 from \"../../../../services/toast.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 44);\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46);\n    i0.ɵɵelement(4, \"div\", 47)(5, \"div\", 48)(6, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.isOnline) ? \"En ligne\" : ctx_r2.formatLastActive(ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"div\", 51);\n    i0.ɵɵelementStart(2, \"span\", 52);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵelement(2, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 56);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r5.otherParticipant == null ? null : ctx_r5.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"span\", 73);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.formatDateSeparator(message_r16.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"img\", 75);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const message_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.openUserProfile(message_r16.sender == null ? null : message_r16.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r16.sender == null ? null : message_r16.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r16.sender == null ? null : message_r16.sender.username);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r20.getUserColor(message_r16.sender == null ? null : message_r16.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r16.sender == null ? null : message_r16.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"div\", 78);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r21.formatMessageContent(message_r16.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 84);\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r33.formatMessageContent(message_r16.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 79);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_7_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const message_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.openImageViewer(message_r16));\n    });\n    i0.ɵɵelement(2, \"img\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵelement(4, \"i\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, MessageChatComponent_div_27_ng_container_1_div_7_div_5_Template, 1, 1, \"div\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r22.getImageUrl(message_r16), i0.ɵɵsanitizeUrl)(\"alt\", message_r16.content || \"Image\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", message_r16.content);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const message_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.downloadFile(message_r16));\n    });\n    i0.ɵɵelementStart(1, \"div\", 86);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8)(4, \"div\", 87);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 73);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 88);\n    i0.ɵɵelement(9, \"i\", 89);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r23.getFileIcon(message_r16));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getFileName(message_r16), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getFileSize(message_r16), \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 95);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 96);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 97);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 98);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_div_12_i_1_Template, 1, 0, \"i\", 91);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_27_ng_container_1_div_12_i_2_Template, 1, 0, \"i\", 92);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_27_ng_container_1_div_12_i_3_Template, 1, 0, \"i\", 93);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_27_ng_container_1_div_12_i_4_Template, 1, 0, \"i\", 94);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const reaction_r49 = restoredCtx.$implicit;\n      const message_r16 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.toggleReaction(message_r16.id, reaction_r49.emoji));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"1\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reaction_r49 = ctx.$implicit;\n    const ctx_r48 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r48.hasUserReacted(reaction_r49, ctx_r48.currentUserId || \"\"))(\"text-green-600\", ctx_r48.hasUserReacted(reaction_r49, ctx_r48.currentUserId || \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r49.emoji);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template, 5, 5, \"button\", 100);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", message_r16.reactions);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_div_1_Template, 4, 1, \"div\", 61);\n    i0.ɵɵelementStart(2, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const message_r16 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.onMessageClick(message_r16, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_27_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const message_r16 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.onMessageContextMenu(message_r16, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_27_ng_container_1_div_3_Template, 2, 2, \"div\", 63);\n    i0.ɵɵelementStart(4, \"div\", 64);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_27_ng_container_1_div_5_Template, 2, 3, \"div\", 65);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_27_ng_container_1_div_6_Template, 2, 1, \"div\", 66);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_27_ng_container_1_div_7_Template, 6, 3, \"div\", 26);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_27_ng_container_1_div_8_Template, 10, 4, \"div\", 67);\n    i0.ɵɵelementStart(9, \"div\", 68)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, MessageChatComponent_div_27_ng_container_1_div_12_Template, 5, 4, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, MessageChatComponent_div_27_ng_container_1_div_13_Template, 2, 1, \"div\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = ctx.$implicit;\n    const i_r17 = ctx.index;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.shouldShowDateSeparator(i_r17));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"justify-end\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId)(\"justify-start\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId);\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r16.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId && ctx_r14.shouldShowAvatar(i_r17));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"bg-green-500\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId)(\"text-white\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId)(\"bg-white\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId)(\"text-gray-900\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId)(\"dark:bg-gray-700\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId)(\"dark:text-white\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isGroupConversation() && (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId && ctx_r14.shouldShowSenderName(i_r17));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.getMessageType(message_r16) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.hasImage(message_r16));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.hasFile(message_r16));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r14.formatMessageTime(message_r16.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.reactions && message_r16.reactions.length > 0);\n  }\n}\nfunction MessageChatComponent_div_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"img\", 103);\n    i0.ɵɵelementStart(2, \"div\", 104)(3, \"div\", 46);\n    i0.ɵɵelement(4, \"div\", 105)(5, \"div\", 106)(6, \"div\", 107);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r15.otherParticipant == null ? null : ctx_r15.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r15.otherParticipant == null ? null : ctx_r15.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_Template, 14, 26, \"ng-container\", 59);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_27_div_2_Template, 7, 2, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.messages)(\"ngForTrackBy\", ctx_r6.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isTyping);\n  }\n}\nfunction MessageChatComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_button_40_Template_button_mousedown_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.startVoiceRecording());\n    })(\"mouseup\", function MessageChatComponent_button_40_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.stopVoiceRecording());\n    })(\"mouseleave\", function MessageChatComponent_button_40_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.cancelVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"bg-red-500\", ctx_r8.isRecordingVoice)(\"hover:bg-red-600\", ctx_r8.isRecordingVoice)(\"animate-pulse\", ctx_r8.isRecordingVoice);\n  }\n}\nfunction MessageChatComponent_button_41_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 113);\n  }\n}\nfunction MessageChatComponent_button_41_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 114);\n  }\n}\nfunction MessageChatComponent_button_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_41_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.sendMessage());\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_button_41_i_1_Template, 1, 0, \"i\", 111);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_41_i_2_Template, 1, 0, \"i\", 112);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r9.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isSendingMessage);\n  }\n}\nfunction MessageChatComponent_div_42_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_42_button_3_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const category_r67 = restoredCtx.$implicit;\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.selectEmojiCategory(category_r67));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r67 = ctx.$implicit;\n    const ctx_r65 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r65.selectedEmojiCategory === category_r67)(\"text-green-600\", ctx_r65.selectedEmojiCategory === category_r67)(\"hover:bg-gray-100\", ctx_r65.selectedEmojiCategory !== category_r67)(\"dark:hover:bg-gray-700\", ctx_r65.selectedEmojiCategory !== category_r67);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r67.icon, \" \");\n  }\n}\nfunction MessageChatComponent_div_42_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_42_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r72);\n      const emoji_r70 = restoredCtx.$implicit;\n      const ctx_r71 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r71.insertEmoji(emoji_r70));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r70 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r70.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r70.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"div\", 116)(2, \"div\", 117);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_42_button_3_Template, 2, 9, \"button\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 119);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_42_button_5_Template, 2, 2, \"button\", 120);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.emojiCategories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.getEmojisForCategory(ctx_r10.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 116)(2, \"div\", 124)(3, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 126);\n    i0.ɵɵelement(5, \"i\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 128);\n    i0.ɵɵtext(7, \"Photos\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.triggerFileInput(\"video\"));\n    });\n    i0.ɵɵelementStart(9, \"div\", 129);\n    i0.ɵɵelement(10, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 128);\n    i0.ɵɵtext(12, \"Vid\\u00E9os\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 131);\n    i0.ɵɵelement(15, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 128);\n    i0.ɵɵtext(17, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.openCamera());\n    });\n    i0.ɵɵelementStart(19, \"div\", 133);\n    i0.ɵɵelement(20, \"i\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 128);\n    i0.ɵɵtext(22, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 135);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class MessageChatComponent {\n  constructor(fb, route, MessageService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.MessageService = MessageService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.searchMode = false;\n    this.isSendingMessage = false;\n    // === ENREGISTREMENT VOCAL ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    // === APPELS ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.isUserTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  ngOnInit() {\n    this.initializeComponent();\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n  }\n  loadCurrentUser() {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId\n      });\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: conversation => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  setOtherParticipant() {\n    if (!this.conversation?.participants || this.conversation.participants.length === 0) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find(p => {\n        const participantId = p.id || p._id;\n        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log('First participant is current user, using second participant');\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline\n      });\n      // Log très visible pour debug\n      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);\n      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    // Les messages sont déjà chargés avec la conversation\n    this.messages = this.conversation.messages || [];\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n  setupSubscriptions() {\n    // Abonnements GraphQL pour les mises à jour en temps réel\n    // À implémenter selon vos besoins\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.conversation.participants?.find(p => p.id !== this.currentUserId)?.id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        this.messages.push(message);\n        this.messageForm.reset();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n      }\n    });\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  // === MÉTHODES POUR LE TEMPLATE ===\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const date = new Date(lastActive);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n    const diffHours = Math.floor(diffMins / 60);\n    if (diffHours < 24) return `Il y a ${diffHours}h`;\n    const diffDays = Math.floor(diffHours / 24);\n    return `Il y a ${diffDays}j`;\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  formatMessageContent(content) {\n    if (!content) return '';\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(urlRegex, '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>');\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  shouldShowAvatar(index) {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    if (!nextMessage) return true;\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n  shouldShowSenderName(index) {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!previousMessage) return true;\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n  getMessageType(message) {\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    return 'text';\n  }\n  hasImage(message) {\n    return message.attachments?.some(att => att.type?.startsWith('image/')) || false;\n  }\n  hasFile(message) {\n    return message.attachments?.some(att => !att.type?.startsWith('image/')) || false;\n  }\n  getImageUrl(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    return imageAttachment?.url || '';\n  }\n  getFileName(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    return fileAttachment?.name || 'Fichier';\n  }\n  getFileSize(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.size) return '';\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n  getFileIcon(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (!fileAttachment?.type) return 'fas fa-file';\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n  getUserColor(userId) {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message, event) {\n    console.log('Message clicked:', message);\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    console.log('Message context menu:', message);\n  }\n  onInputChange(event) {\n    // Gérer les changements dans le champ de saisie\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInputFocus() {\n    // Gérer le focus sur le champ de saisie\n  }\n  onInputBlur() {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n  onScroll(event) {\n    // Gérer le scroll pour charger plus de messages\n  }\n  openUserProfile(userId) {\n    console.log('Opening user profile for:', userId);\n  }\n  openImageViewer(message) {\n    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));\n    if (imageAttachment?.url) {\n      window.open(imageAttachment.url, '_blank');\n    }\n  }\n  downloadFile(message) {\n    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));\n    if (fileAttachment?.url) {\n      window.open(fileAttachment.url, '_blank');\n    }\n  }\n  toggleReaction(messageId, emoji) {\n    console.log('Toggle reaction:', messageId, emoji);\n  }\n  hasUserReacted(reaction, userId) {\n    return reaction.users?.includes(userId) || false;\n  }\n  toggleSearch() {\n    this.searchMode = !this.searchMode;\n    if (!this.searchMode) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n  toggleMainMenu() {\n    console.log('Toggle main menu');\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    this.showAttachmentMenu = false;\n  }\n  triggerFileInput(type) {\n    // Déclencher la sélection de fichier\n    const input = document.createElement('input');\n    input.type = 'file';\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    }\n    input.onchange = event => {\n      // Gérer la sélection de fichier\n      console.log('File selected:', event.target.files);\n    };\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n  openCamera() {\n    console.log('Opening camera...');\n    this.showAttachmentMenu = false;\n  }\n  // === MÉTHODES POUR LES ÉMOJIS ===\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + (emoji.emoji || emoji);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n    setTimeout(() => {\n      const textarea = document.querySelector('textarea[formControlName=\"content\"]');\n      if (textarea) {\n        textarea.focus();\n      }\n    }, 100);\n  }\n  // === MÉTHODES MANQUANTES ===\n  goBackToConversations() {\n    window.history.back();\n  }\n  startVideoCall() {\n    console.log('Starting video call...');\n    // Implémenter l'appel vidéo\n  }\n\n  startVoiceCall() {\n    console.log('Starting voice call...');\n    // Implémenter l'appel audio\n  }\n\n  trackByMessageId(index, message) {\n    return message.id || index;\n  }\n  isGroupConversation() {\n    return this.conversation?.participants?.length > 2 || false;\n  }\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          }\n        });\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: 'audio/webm;codecs=opus'\n        });\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          _this.cdr.detectChanges();\n        }, 1000);\n        _this.mediaRecorder.ondataavailable = event => {\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.start(100);\n        _this.toastService.showSuccess('Enregistrement vocal démarré');\n      } catch (error) {\n        console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n        _this.toastService.showError(\"Impossible d'accéder au microphone\");\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this2.audioChunks.length === 0) {\n          _this2.toastService.showError('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: 'audio/webm;codecs=opus'\n        });\n        if (_this2.voiceRecordingDuration < 1) {\n          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n          type: 'audio/webm;codecs=opus'\n        });\n        yield _this2.sendVoiceMessage(audioFile);\n        _this2.toastService.showSuccess('Message vocal envoyé');\n      } catch (error) {\n        console.error(\"Erreur lors du traitement de l'audio:\", error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n      } finally {\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n    for (let file of files) {\n      console.log('File selected:', file.name);\n      // Implémenter l'upload de fichier\n    }\n  }\n\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 47,\n      vars: 29,\n      consts: [[1, \"flex\", \"flex-col\", \"h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-green-50\", \"dark:from-gray-900\", \"dark:to-gray-800\"], [1, \"flex\", \"items-center\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-gray-800\", \"border-b\", \"border-gray-200\", \"dark:border-gray-700\", \"shadow-sm\"], [1, \"p-2\", \"mr-3\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"text-gray-600\", \"dark:text-gray-300\"], [1, \"flex\", \"items-center\", \"flex-1\", \"min-w-0\"], [1, \"relative\", \"mr-3\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-green-500\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [\"class\", \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\", 4, \"ngIf\"], [1, \"flex-1\", \"min-w-0\"], [1, \"font-semibold\", \"text-gray-900\", \"dark:text-white\", \"truncate\"], [1, \"text-sm\", \"text-gray-500\", \"dark:text-gray-400\"], [\"class\", \"flex items-center gap-1 text-green-600\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"items-center\", \"gap-2\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Appel vocal\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Rechercher\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Menu\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"flex-1\", \"overflow-y-auto\", \"p-4\", \"space-y-4\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"flex flex-col items-center justify-center py-8\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [1, \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"p-4\"], [1, \"flex\", \"items-end\", \"gap-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"gap-2\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Joindre un fichier\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [1, \"flex-1\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", \"rows\", \"1\", \"maxlength\", \"4096\", \"autocomplete\", \"off\", \"spellcheck\", \"true\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-gray-50\", \"dark:bg-gray-700\", \"border\", \"border-gray-200\", \"dark:border-gray-600\", \"rounded-2xl\", \"resize-none\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-green-500\", \"focus:border-transparent\", \"text-gray-900\", \"dark:text-white\", \"placeholder-gray-500\", \"dark:placeholder-gray-400\", 3, \"disabled\", \"input\", \"keydown\", \"focus\", \"blur\"], [\"messageTextarea\", \"\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\", \"title\", \"Maintenir pour enregistrer\", 3, \"bg-red-500\", \"hover:bg-red-600\", \"animate-pulse\", \"mousedown\", \"mouseup\", \"mouseleave\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\", \"title\", \"Envoyer\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 1, \"hidden\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-25 z-40\", 3, \"click\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"right-0\", \"w-3\", \"h-3\", \"bg-green-500\", \"border-2\", \"border-white\", \"dark:border-gray-800\", \"rounded-full\", \"animate-pulse\"], [1, \"flex\", \"items-center\", \"gap-1\", \"text-green-600\"], [1, \"flex\", \"gap-1\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-green-500\", \"mb-4\"], [1, \"text-gray-500\", \"dark:text-gray-400\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"text-6xl\", \"text-gray-300\", \"dark:text-gray-600\", \"mb-4\"], [1, \"fas\", \"fa-comments\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-700\", \"dark:text-gray-300\", \"mb-2\"], [1, \"text-gray-500\", \"dark:text-gray-400\", \"text-center\"], [1, \"space-y-2\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"flex items-start gap-2\", 4, \"ngIf\"], [\"class\", \"flex justify-center my-4\", 4, \"ngIf\"], [1, \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"class\", \"mr-2 flex-shrink-0\", 4, \"ngIf\"], [1, \"max-w-xs\", \"lg:max-w-md\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\", \"relative\", \"group\"], [\"class\", \"text-xs font-semibold mb-1 opacity-75\", 3, \"color\", 4, \"ngIf\"], [\"class\", \"break-words\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-end\", \"gap-1\", \"mt-1\", \"text-xs\", \"opacity-75\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex gap-1 mt-2\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-4\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-3\", \"py-1\", \"rounded-full\", \"shadow-sm\"], [1, \"text-xs\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"mr-2\", \"flex-shrink-0\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [1, \"text-xs\", \"font-semibold\", \"mb-1\", \"opacity-75\"], [1, \"break-words\"], [3, \"innerHTML\"], [1, \"relative\", \"cursor-pointer\", \"rounded-lg\", \"overflow-hidden\", 3, \"click\"], [1, \"max-w-full\", \"h-auto\", \"rounded-lg\", \"hover:opacity-90\", \"transition-opacity\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"hover:bg-opacity-10\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-expand\", \"text-white\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [\"class\", \"text-sm\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"text-sm\", 3, \"innerHTML\"], [1, \"flex\", \"items-center\", \"gap-3\", \"p-2\", \"bg-gray-50\", \"dark:bg-gray-600\", \"rounded-lg\", \"cursor-pointer\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"text-2xl\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"font-medium\", \"text-sm\", \"truncate\"], [1, \"p-1\", \"rounded-full\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-400\", \"transition-colors\"], [1, \"fas\", \"fa-download\", \"text-sm\"], [1, \"flex\", \"items-center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double text-blue-400\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", \"text-blue-400\"], [1, \"flex\", \"gap-1\", \"mt-2\"], [\"class\", \"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\", 3, \"bg-green-100\", \"text-green-600\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"gap-1\", \"px-2\", \"py-1\", \"bg-gray-100\", \"dark:bg-gray-600\", \"rounded-full\", \"text-xs\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", 3, \"mousedown\", \"mouseup\", \"mouseleave\"], [1, \"fas\", \"fa-microphone\"], [\"type\", \"button\", \"title\", \"Envoyer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"right-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"p-4\"], [1, \"flex\", \"gap-2\", \"mb-4\", \"overflow-x-auto\"], [\"class\", \"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\", 3, \"bg-green-100\", \"text-green-600\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-8\", \"gap-2\", \"max-h-48\", \"overflow-y-auto\"], [\"class\", \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-3\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex-shrink-0\", 3, \"click\"], [1, \"p-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-xl\", 3, \"title\", \"click\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"grid\", \"grid-cols-2\", \"gap-3\"], [1, \"flex\", \"flex-col\", \"items-center\", \"gap-2\", \"p-4\", \"rounded-xl\", \"hover:bg-gray-50\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"w-12\", \"h-12\", \"bg-blue-100\", \"dark:bg-blue-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-image\", \"text-blue-600\", \"dark:text-blue-400\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"dark:text-gray-300\"], [1, \"w-12\", \"h-12\", \"bg-purple-100\", \"dark:bg-purple-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-video\", \"text-purple-600\", \"dark:text-purple-400\"], [1, \"w-12\", \"h-12\", \"bg-orange-100\", \"dark:bg-orange-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-file\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"w-12\", \"h-12\", \"bg-green-100\", \"dark:bg-green-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-camera\", \"text-green-600\", \"dark:text-green-400\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-25\", \"z-40\", 3, \"click\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"img\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_6_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MessageChatComponent_div_7_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"h3\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 7, 0, \"div\", 11);\n          i0.ɵɵtemplate(13, MessageChatComponent_span_13_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_15_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_17_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(18, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"main\", 22, 23);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_23_listener($event) {\n            return ctx.onScroll($event);\n          });\n          i0.ɵɵtemplate(25, MessageChatComponent_div_25_Template, 4, 0, \"div\", 24);\n          i0.ɵɵtemplate(26, MessageChatComponent_div_26_Template, 7, 1, \"div\", 25);\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 3, 3, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"footer\", 27)(29, \"form\", 28);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_29_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(30, \"div\", 29)(31, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_31_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(32, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_33_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(34, \"i\", 33);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 34)(36, \"textarea\", 35, 36);\n          i0.ɵɵlistener(\"input\", function MessageChatComponent_Template_textarea_input_36_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"keydown\", function MessageChatComponent_Template_textarea_keydown_36_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_36_listener() {\n            return ctx.onInputFocus();\n          })(\"blur\", function MessageChatComponent_Template_textarea_blur_36_listener() {\n            return ctx.onInputBlur();\n          });\n          i0.ɵɵtext(38, \"        \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 29);\n          i0.ɵɵtemplate(40, MessageChatComponent_button_40_Template, 2, 6, \"button\", 37);\n          i0.ɵɵtemplate(41, MessageChatComponent_button_41_Template, 3, 3, \"button\", 38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(42, MessageChatComponent_div_42_Template, 6, 2, \"div\", 39);\n          i0.ɵɵtemplate(43, MessageChatComponent_div_43_Template, 23, 0, \"div\", 40);\n          i0.ɵɵelementStart(44, \"input\", 41, 42);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_44_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, MessageChatComponent_div_46_Template, 1, 0, \"div\", 43);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_17_0;\n          let tmp_18_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.searchMode)(\"text-green-600\", ctx.searchMode);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.messages.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showEmojiPicker)(\"text-green-600\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showAttachmentMenu)(\"text-green-600\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.otherParticipant || ctx.isRecordingVoice || ctx.isSendingMessage);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_17_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_17_0.value == null ? null : tmp_17_0.value.trim()));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_18_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_18_0.value == null ? null : tmp_18_0.value.trim());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ctx_r5", "username", "ctx_r18", "formatDateSeparator", "message_r16", "timestamp", "ɵɵlistener", "MessageChatComponent_div_27_ng_container_1_div_3_Template_img_click_1_listener", "ɵɵrestoreView", "_r29", "ɵɵnextContext", "$implicit", "ctx_r27", "ɵɵresetView", "openUserProfile", "sender", "id", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r20", "getUserColor", "ctx_r21", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r33", "MessageChatComponent_div_27_ng_container_1_div_7_Template_div_click_1_listener", "_r37", "ctx_r35", "openImageViewer", "ɵɵtemplate", "MessageChatComponent_div_27_ng_container_1_div_7_div_5_Template", "ctx_r22", "getImageUrl", "MessageChatComponent_div_27_ng_container_1_div_8_Template_div_click_0_listener", "_r41", "ctx_r39", "downloadFile", "ɵɵclassMap", "ctx_r23", "getFileIcon", "getFileName", "getFileSize", "MessageChatComponent_div_27_ng_container_1_div_12_i_1_Template", "MessageChatComponent_div_27_ng_container_1_div_12_i_2_Template", "MessageChatComponent_div_27_ng_container_1_div_12_i_3_Template", "MessageChatComponent_div_27_ng_container_1_div_12_i_4_Template", "status", "MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template_button_click_0_listener", "restoredCtx", "_r52", "reaction_r49", "ctx_r50", "toggleReaction", "emoji", "ɵɵclassProp", "ctx_r48", "hasUserReacted", "currentUserId", "ɵɵtextInterpolate", "MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template", "reactions", "ɵɵelementContainerStart", "MessageChatComponent_div_27_ng_container_1_div_1_Template", "MessageChatComponent_div_27_ng_container_1_Template_div_click_2_listener", "$event", "_r55", "ctx_r54", "onMessageClick", "MessageChatComponent_div_27_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r56", "onMessageContextMenu", "MessageChatComponent_div_27_ng_container_1_div_3_Template", "MessageChatComponent_div_27_ng_container_1_div_5_Template", "MessageChatComponent_div_27_ng_container_1_div_6_Template", "MessageChatComponent_div_27_ng_container_1_div_7_Template", "MessageChatComponent_div_27_ng_container_1_div_8_Template", "MessageChatComponent_div_27_ng_container_1_div_12_Template", "MessageChatComponent_div_27_ng_container_1_div_13_Template", "ɵɵelementContainerEnd", "ctx_r14", "shouldShowDateSeparator", "i_r17", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "hasFile", "formatMessageTime", "length", "ctx_r15", "MessageChatComponent_div_27_ng_container_1_Template", "MessageChatComponent_div_27_div_2_Template", "ctx_r6", "messages", "trackByMessageId", "isTyping", "MessageChatComponent_button_40_Template_button_mousedown_0_listener", "_r58", "ctx_r57", "startVoiceRecording", "MessageChatComponent_button_40_Template_button_mouseup_0_listener", "ctx_r59", "stopVoiceRecording", "MessageChatComponent_button_40_Template_button_mouseleave_0_listener", "ctx_r60", "cancelVoiceRecording", "ctx_r8", "isRecordingVoice", "MessageChatComponent_button_41_Template_button_click_0_listener", "_r64", "ctx_r63", "sendMessage", "MessageChatComponent_button_41_i_1_Template", "MessageChatComponent_button_41_i_2_Template", "ctx_r9", "isSendingMessage", "MessageChatComponent_div_42_button_3_Template_button_click_0_listener", "_r69", "category_r67", "ctx_r68", "selectEmojiCategory", "ctx_r65", "selectedEmojiCategory", "icon", "MessageChatComponent_div_42_button_5_Template_button_click_0_listener", "_r72", "emoji_r70", "ctx_r71", "insert<PERSON><PERSON><PERSON>", "name", "MessageChatComponent_div_42_button_3_Template", "MessageChatComponent_div_42_button_5_Template", "ctx_r10", "emojiCategories", "getEmojisForCategory", "MessageChatComponent_div_43_Template_button_click_3_listener", "_r74", "ctx_r73", "triggerFileInput", "MessageChatComponent_div_43_Template_button_click_8_listener", "ctx_r75", "MessageChatComponent_div_43_Template_button_click_13_listener", "ctx_r76", "MessageChatComponent_div_43_Template_button_click_18_listener", "ctx_r77", "openCamera", "MessageChatComponent_div_46_Template_div_click_0_listener", "_r79", "ctx_r78", "closeAllMenus", "MessageChatComponent", "constructor", "fb", "route", "MessageService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "voiceRecordingDuration", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "isInCall", "callType", "callDuration", "callTimer", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "initializeComponent", "loadCurrentUser", "loadConversation", "setupSubscriptions", "userString", "localStorage", "getItem", "console", "log", "error", "user", "JSON", "parse", "userId", "_id", "extracted", "conversationId", "snapshot", "paramMap", "get", "showError", "getConversation", "subscribe", "next", "participants", "participantsCount", "isGroup", "messagesCount", "setOtherParticipant", "loadMessages", "warn", "find", "p", "participantId", "String", "firstParticipantId", "scrollToBottom", "valid", "value", "trim", "receiverId", "undefined", "message", "push", "reset", "setTimeout", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "date", "Date", "now", "diffMs", "getTime", "diffMins", "Math", "floor", "diffHours", "diffDays", "toLocaleTimeString", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "urlRegex", "replace", "index", "currentMessage", "previousMessage", "currentDate", "previousDate", "nextMessage", "attachments", "attachment", "type", "startsWith", "some", "att", "imageAttachment", "url", "fileAttachment", "size", "bytes", "round", "includes", "colors", "charCodeAt", "event", "preventDefault", "onInputChange", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "window", "open", "messageId", "reaction", "users", "toggleSearch", "toggleMainMenu", "toggleAttachmentMenu", "toggleEmojiPicker", "input", "document", "createElement", "accept", "onchange", "target", "files", "click", "category", "currentC<PERSON>nt", "newContent", "patchValue", "textarea", "querySelector", "focus", "goBackToConversations", "history", "back", "startVideoCall", "startVoiceCall", "_this", "_asyncToGenerator", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "MediaRecorder", "mimeType", "setInterval", "detectChanges", "ondataavailable", "data", "onstop", "processRecordedAudio", "start", "showSuccess", "state", "stop", "getTracks", "for<PERSON>ach", "track", "clearInterval", "_this2", "audioBlob", "Blob", "audioFile", "File", "sendVoiceMessage", "_this3", "Error", "Promise", "resolve", "reject", "formatRecordingDuration", "duration", "minutes", "seconds", "toString", "padStart", "onFileSelected", "file", "getFileAcceptTypes", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "i3", "i4", "ToastService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_Template_img_click_6_listener", "MessageChatComponent_div_7_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_span_13_Template", "MessageChatComponent_Template_button_click_15_listener", "MessageChatComponent_Template_button_click_17_listener", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_Template_main_scroll_23_listener", "MessageChatComponent_div_25_Template", "MessageChatComponent_div_26_Template", "MessageChatComponent_div_27_Template", "MessageChatComponent_Template_form_ngSubmit_29_listener", "MessageChatComponent_Template_button_click_31_listener", "MessageChatComponent_Template_button_click_33_listener", "MessageChatComponent_Template_textarea_input_36_listener", "MessageChatComponent_Template_textarea_keydown_36_listener", "MessageChatComponent_Template_textarea_focus_36_listener", "MessageChatComponent_Template_textarea_blur_36_listener", "MessageChatComponent_button_40_Template", "MessageChatComponent_button_41_Template", "MessageChatComponent_div_42_Template", "MessageChatComponent_div_43_Template", "MessageChatComponent_Template_input_change_44_listener", "MessageChatComponent_div_46_Template", "tmp_17_0", "tmp_18_0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Component,\n  On<PERSON>ni<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>Child,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { ToastService } from '../../../../services/toast.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent implements OnInit, OnDestroy {\n  // === RÉFÉRENCES DOM ===\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === DONNÉES PRINCIPALES ===\n  conversation: any = null;\n  messages: any[] = [];\n  currentUserId: string | null = null;\n  currentUsername = 'You';\n  messageForm: FormGroup;\n  otherParticipant: any = null;\n\n  // === ÉTATS DE L'INTERFACE ===\n  isLoading = false;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  showEmojiPicker = false;\n  showAttachmentMenu = false;\n  showSearch = false;\n  searchQuery = '';\n  searchResults: any[] = [];\n  searchMode = false;\n  isSendingMessage = false;\n\n  // === ENREGISTREMENT VOCAL ===\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n\n  // === APPELS ===\n  isInCall = false;\n  callType: 'VIDEO' | 'AUDIO' | null = null;\n  callDuration = 0;\n  private callTimer: any = null;\n\n  // === ÉMOJIS ===\n  emojiCategories: any[] = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n      ],\n    },\n  ];\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  // === PAGINATION ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n\n  // === AUTRES ÉTATS ===\n  isTyping = false;\n  isUserTyping = false;\n  private typingTimeout: any = null;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private MessageService: MessageService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  ngOnInit(): void {\n    this.initializeComponent();\n  }\n\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      const userString = localStorage.getItem('user');\n      console.log('🔍 Raw user from localStorage:', userString);\n\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        console.error('❌ No user data in localStorage');\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n        return;\n      }\n\n      const user = JSON.parse(userString);\n      console.log('🔍 Parsed user object:', user);\n\n      // Essayer différentes propriétés pour l'ID utilisateur\n      const userId = user._id || user.id || user.userId;\n      console.log('🔍 Trying to extract user ID:', {\n        _id: user._id,\n        id: user.id,\n        userId: user.userId,\n        extracted: userId,\n      });\n\n      if (userId) {\n        this.currentUserId = userId;\n        this.currentUsername = user.username || user.name || 'You';\n        console.log('✅ Current user loaded successfully:', {\n          id: this.currentUserId,\n          username: this.currentUsername,\n        });\n      } else {\n        console.error('❌ No valid user ID found in user object:', user);\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error('❌ Error parsing user from localStorage:', error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  private loadConversation(): void {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    console.log('Loading conversation with ID:', conversationId);\n\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId).subscribe({\n      next: (conversation) => {\n        console.log('🔍 Conversation loaded successfully:', conversation);\n        console.log('🔍 Conversation structure:', {\n          id: conversation?.id,\n          participants: conversation?.participants,\n          participantsCount: conversation?.participants?.length,\n          isGroup: conversation?.isGroup,\n          messages: conversation?.messages,\n          messagesCount: conversation?.messages?.length,\n        });\n        this.conversation = conversation;\n        this.setOtherParticipant();\n        this.loadMessages();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors du chargement de la conversation'\n        );\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private setOtherParticipant(): void {\n    if (\n      !this.conversation?.participants ||\n      this.conversation.participants.length === 0\n    ) {\n      console.warn('No participants found in conversation');\n      this.otherParticipant = null;\n      return;\n    }\n\n    console.log('Setting other participant...');\n    console.log('Current user ID:', this.currentUserId);\n    console.log('All participants:', this.conversation.participants);\n\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\n\n    if (this.conversation.isGroup) {\n      // Pour les groupes, on pourrait afficher le nom du groupe\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        return String(participantId) !== String(this.currentUserId);\n      });\n    } else {\n      // Pour les conversations 1-à-1, on prend l'autre participant\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\n        const participantId = p.id || p._id;\n        console.log(\n          'Comparing participant ID:',\n          participantId,\n          'with current user ID:',\n          this.currentUserId\n        );\n        return String(participantId) !== String(this.currentUserId);\n      });\n    }\n\n    // Fallback si aucun autre participant n'est trouvé\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\n      console.log('Fallback: using first participant');\n      this.otherParticipant = this.conversation.participants[0];\n\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\n      if (this.conversation.participants.length > 1) {\n        const firstParticipantId =\n          this.otherParticipant.id || this.otherParticipant._id;\n        if (String(firstParticipantId) === String(this.currentUserId)) {\n          console.log(\n            'First participant is current user, using second participant'\n          );\n          this.otherParticipant = this.conversation.participants[1];\n        }\n      }\n    }\n\n    // Vérification finale et logs\n    if (this.otherParticipant) {\n      console.log('✅ Other participant set successfully:', {\n        id: this.otherParticipant.id || this.otherParticipant._id,\n        username: this.otherParticipant.username,\n        image: this.otherParticipant.image,\n        isOnline: this.otherParticipant.isOnline,\n      });\n\n      // Log très visible pour debug\n      console.log(\n        '🎯 FINAL RESULT: otherParticipant =',\n        this.otherParticipant.username\n      );\n      console.log(\n        '🎯 Should display in sidebar:',\n        this.otherParticipant.username\n      );\n    } else {\n      console.error('❌ No other participant found! This should not happen.');\n      console.log('Conversation participants:', this.conversation.participants);\n      console.log('Current user ID:', this.currentUserId);\n\n      // Log très visible pour debug\n      console.log('🚨 ERROR: No otherParticipant found!');\n    }\n  }\n\n  private loadMessages(): void {\n    if (!this.conversation?.id) return;\n\n    // Les messages sont déjà chargés avec la conversation\n    this.messages = this.conversation.messages || [];\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\n    this.isLoading = false;\n    this.scrollToBottom();\n  }\n\n  private setupSubscriptions(): void {\n    // Abonnements GraphQL pour les mises à jour en temps réel\n    // À implémenter selon vos besoins\n  }\n\n  // === ENVOI DE MESSAGES ===\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n\n    const receiverId = this.conversation.participants?.find(\n      (p: any) => p.id !== this.currentUserId\n    )?.id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    this.MessageService.sendMessage(\n      receiverId,\n      content,\n      undefined,\n      'TEXT' as any,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        this.messages.push(message);\n        this.messageForm.reset();\n        this.scrollToBottom();\n      },\n      error: (error: any) => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n      },\n    });\n  }\n\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  // === MÉTHODES POUR LE TEMPLATE ===\n  formatLastActive(lastActive: string | Date | null): string {\n    if (!lastActive) return 'Hors ligne';\n\n    const date = new Date(lastActive);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n\n    if (diffMins < 1) return \"À l'instant\";\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\n\n    const diffHours = Math.floor(diffMins / 60);\n    if (diffHours < 24) return `Il y a ${diffHours}h`;\n\n    const diffDays = Math.floor(diffHours / 24);\n    return `Il y a ${diffDays}j`;\n  }\n\n  formatMessageTime(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  formatDateSeparator(timestamp: string | Date): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return \"Aujourd'hui\";\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Hier';\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n\n  formatMessageContent(content: string): string {\n    if (!content) return '';\n\n    // Remplacer les URLs par des liens\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    return content.replace(\n      urlRegex,\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\n    );\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n\n    if (!nextMessage) return true;\n\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!previousMessage) return true;\n\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\n  }\n\n  getMessageType(message: any): string {\n    if (message.attachments && message.attachments.length > 0) {\n      const attachment = message.attachments[0];\n      if (attachment.type?.startsWith('image/')) return 'image';\n      if (attachment.type?.startsWith('video/')) return 'video';\n      if (attachment.type?.startsWith('audio/')) return 'audio';\n      return 'file';\n    }\n    return 'text';\n  }\n\n  hasImage(message: any): boolean {\n    return (\n      message.attachments?.some((att: any) => att.type?.startsWith('image/')) ||\n      false\n    );\n  }\n\n  hasFile(message: any): boolean {\n    return (\n      message.attachments?.some(\n        (att: any) => !att.type?.startsWith('image/')\n      ) || false\n    );\n  }\n\n  getImageUrl(message: any): string {\n    const imageAttachment = message.attachments?.find((att: any) =>\n      att.type?.startsWith('image/')\n    );\n    return imageAttachment?.url || '';\n  }\n\n  getFileName(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    return fileAttachment?.name || 'Fichier';\n  }\n\n  getFileSize(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.size) return '';\n\n    const bytes = fileAttachment.size;\n    if (bytes < 1024) return bytes + ' B';\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\n    return Math.round(bytes / 1048576) + ' MB';\n  }\n\n  getFileIcon(message: any): string {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (!fileAttachment?.type) return 'fas fa-file';\n\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\n    return 'fas fa-file';\n  }\n\n  getUserColor(userId: string): string {\n    // Générer une couleur basée sur l'ID utilisateur\n    const colors = [\n      '#FF6B6B',\n      '#4ECDC4',\n      '#45B7D1',\n      '#96CEB4',\n      '#FFEAA7',\n      '#DDA0DD',\n      '#98D8C8',\n    ];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n\n  // === MÉTHODES D'INTERACTION ===\n  onMessageClick(message: any, event: any): void {\n    console.log('Message clicked:', message);\n  }\n\n  onMessageContextMenu(message: any, event: any): void {\n    event.preventDefault();\n    console.log('Message context menu:', message);\n  }\n\n  onInputChange(event: any): void {\n    // Gérer les changements dans le champ de saisie\n  }\n\n  onInputKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  onInputFocus(): void {\n    // Gérer le focus sur le champ de saisie\n  }\n\n  onInputBlur(): void {\n    // Gérer la perte de focus sur le champ de saisie\n  }\n\n  onScroll(event: any): void {\n    // Gérer le scroll pour charger plus de messages\n  }\n\n  openUserProfile(userId: string): void {\n    console.log('Opening user profile for:', userId);\n  }\n\n  openImageViewer(message: any): void {\n    const imageAttachment = message.attachments?.find((att: any) =>\n      att.type?.startsWith('image/')\n    );\n    if (imageAttachment?.url) {\n      window.open(imageAttachment.url, '_blank');\n    }\n  }\n\n  downloadFile(message: any): void {\n    const fileAttachment = message.attachments?.find(\n      (att: any) => !att.type?.startsWith('image/')\n    );\n    if (fileAttachment?.url) {\n      window.open(fileAttachment.url, '_blank');\n    }\n  }\n\n  toggleReaction(messageId: string, emoji: string): void {\n    console.log('Toggle reaction:', messageId, emoji);\n  }\n\n  hasUserReacted(reaction: any, userId: string): boolean {\n    return reaction.users?.includes(userId) || false;\n  }\n\n  toggleSearch(): void {\n    this.searchMode = !this.searchMode;\n    if (!this.searchMode) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n\n  toggleMainMenu(): void {\n    console.log('Toggle main menu');\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    this.showAttachmentMenu = false;\n  }\n\n  triggerFileInput(type?: string): void {\n    // Déclencher la sélection de fichier\n    const input = document.createElement('input');\n    input.type = 'file';\n\n    if (type === 'image') {\n      input.accept = 'image/*';\n    } else if (type === 'video') {\n      input.accept = 'video/*';\n    } else if (type === 'document') {\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\n    }\n\n    input.onchange = (event: any) => {\n      // Gérer la sélection de fichier\n      console.log('File selected:', event.target.files);\n    };\n\n    input.click();\n    this.showAttachmentMenu = false;\n  }\n\n  openCamera(): void {\n    console.log('Opening camera...');\n    this.showAttachmentMenu = false;\n  }\n\n  // === MÉTHODES POUR LES ÉMOJIS ===\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + (emoji.emoji || emoji);\n    this.messageForm.patchValue({ content: newContent });\n\n    this.showEmojiPicker = false;\n\n    setTimeout(() => {\n      const textarea = document.querySelector(\n        'textarea[formControlName=\"content\"]'\n      ) as HTMLTextAreaElement;\n      if (textarea) {\n        textarea.focus();\n      }\n    }, 100);\n  }\n\n  // === MÉTHODES MANQUANTES ===\n  goBackToConversations(): void {\n    window.history.back();\n  }\n\n  startVideoCall(): void {\n    console.log('Starting video call...');\n    // Implémenter l'appel vidéo\n  }\n\n  startVoiceCall(): void {\n    console.log('Starting voice call...');\n    // Implémenter l'appel audio\n  }\n\n  trackByMessageId(index: number, message: any): any {\n    return message.id || index;\n  }\n\n  isGroupConversation(): boolean {\n    return this.conversation?.participants?.length > 2 || false;\n  }\n\n  async startVoiceRecording(): Promise<void> {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n        },\n      });\n\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus',\n      });\n\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        this.cdr.detectChanges();\n      }, 1000);\n\n      this.mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      this.mediaRecorder.onstop = () => {\n        this.processRecordedAudio();\n      };\n\n      this.mediaRecorder.start(100);\n      this.toastService.showSuccess('Enregistrement vocal démarré');\n    } catch (error) {\n      console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n      this.toastService.showError(\"Impossible d'accéder au microphone\");\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    try {\n      if (this.audioChunks.length === 0) {\n        this.toastService.showError('Aucun audio enregistré');\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      const audioBlob = new Blob(this.audioChunks, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      if (this.voiceRecordingDuration < 1) {\n        this.toastService.showError(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      await this.sendVoiceMessage(audioFile);\n      this.toastService.showSuccess('Message vocal envoyé');\n    } catch (error) {\n      console.error(\"Erreur lors du traitement de l'audio:\", error);\n      this.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n    } finally {\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        '',\n        audioFile,\n        'AUDIO',\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  onFileSelected(event: any): void {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n\n    for (let file of files) {\n      console.log('File selected:', file.name);\n      // Implémenter l'upload de fichier\n    }\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n  }\n}\n", "<!-- Chat WhatsApp Moderne avec Tailwind CSS -->\n<div\n  class=\"flex flex-col h-screen bg-gradient-to-br from-gray-50 to-green-50 dark:from-gray-900 dark:to-gray-800\"\n>\n  <!-- En-tête -->\n  <header\n    class=\"flex items-center px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm\"\n  >\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      class=\"p-2 mr-3 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n    >\n      <i class=\"fas fa-arrow-left text-gray-600 dark:text-gray-300\"></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div class=\"flex items-center flex-1 min-w-0\">\n      <div class=\"relative mr-3\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-10 h-10 rounded-full object-cover border-2 border-green-500 cursor-pointer hover:scale-105 transition-transform\"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n        />\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\"\n        ></div>\n      </div>\n\n      <div class=\"flex-1 min-w-0\">\n        <h3 class=\"font-semibold text-gray-900 dark:text-white truncate\">\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n          <div\n            *ngIf=\"isUserTyping\"\n            class=\"flex items-center gap-1 text-green-600\"\n          >\n            <span>En train d'écrire</span>\n            <div class=\"flex gap-1\">\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.1s\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.2s\"\n              ></div>\n            </div>\n          </div>\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div class=\"flex items-center gap-2\">\n      <button\n        (click)=\"startVideoCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vidéo\"\n      >\n        <i class=\"fas fa-video\"></i>\n      </button>\n      <button\n        (click)=\"startVoiceCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vocal\"\n      >\n        <i class=\"fas fa-phone\"></i>\n      </button>\n      <button\n        (click)=\"toggleSearch()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        [class.bg-green-100]=\"searchMode\"\n        [class.text-green-600]=\"searchMode\"\n        title=\"Rechercher\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n      <button\n        (click)=\"toggleMainMenu()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Menu\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n  </header>\n\n  <!-- Zone de messages -->\n  <main\n    class=\"flex-1 overflow-y-auto p-4 space-y-4\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n  >\n    <!-- Chargement -->\n    <div\n      *ngIf=\"isLoading\"\n      class=\"flex flex-col items-center justify-center py-8\"\n    >\n      <div\n        class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-4\"\n      ></div>\n      <span class=\"text-gray-500 dark:text-gray-400\"\n        >Chargement des messages...</span\n      >\n    </div>\n\n    <!-- État vide -->\n    <div\n      *ngIf=\"!isLoading && messages.length === 0\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"text-6xl text-gray-300 dark:text-gray-600 mb-4\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3 class=\"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n        Aucun message\n      </h3>\n      <p class=\"text-gray-500 dark:text-gray-400 text-center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages -->\n    <div *ngIf=\"!isLoading && messages.length > 0\" class=\"space-y-2\">\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Séparateur de date -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          class=\"flex justify-center my-4\"\n        >\n          <div\n            class=\"bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-sm\"\n          >\n            <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message -->\n        <div\n          class=\"flex\"\n          [class.justify-end]=\"message.sender?.id === currentUserId\"\n          [class.justify-start]=\"message.sender?.id !== currentUserId\"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar pour les autres -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            class=\"mr-2 flex-shrink-0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              class=\"w-8 h-8 rounded-full object-cover cursor-pointer hover:scale-105 transition-transform\"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n            />\n          </div>\n\n          <!-- Bulle de message -->\n          <div\n            class=\"max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm relative group\"\n            [class.bg-green-500]=\"message.sender?.id === currentUserId\"\n            [class.text-white]=\"message.sender?.id === currentUserId\"\n            [class.bg-white]=\"message.sender?.id !== currentUserId\"\n            [class.text-gray-900]=\"message.sender?.id !== currentUserId\"\n            [class.dark:bg-gray-700]=\"message.sender?.id !== currentUserId\"\n            [class.dark:text-white]=\"message.sender?.id !== currentUserId\"\n          >\n            <!-- Nom expéditeur (groupes) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              class=\"text-xs font-semibold mb-1 opacity-75\"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Contenu texte -->\n            <div *ngIf=\"getMessageType(message) === 'text'\" class=\"break-words\">\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image -->\n            <div *ngIf=\"hasImage(message)\" class=\"space-y-2\">\n              <div\n                class=\"relative cursor-pointer rounded-lg overflow-hidden\"\n                (click)=\"openImageViewer(message)\"\n              >\n                <img\n                  [src]=\"getImageUrl(message)\"\n                  [alt]=\"message.content || 'Image'\"\n                  class=\"max-w-full h-auto rounded-lg hover:opacity-90 transition-opacity\"\n                />\n                <div\n                  class=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all flex items-center justify-center\"\n                >\n                  <i\n                    class=\"fas fa-expand text-white opacity-0 group-hover:opacity-100 transition-opacity\"\n                  ></i>\n                </div>\n              </div>\n              <div\n                *ngIf=\"message.content\"\n                class=\"text-sm\"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Fichier -->\n            <div\n              *ngIf=\"hasFile(message)\"\n              class=\"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\"\n              (click)=\"downloadFile(message)\"\n            >\n              <div class=\"text-2xl text-gray-500 dark:text-gray-400\">\n                <i [class]=\"getFileIcon(message)\"></i>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <div class=\"font-medium text-sm truncate\">\n                  {{ getFileName(message) }}\n                </div>\n                <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ getFileSize(message) }}\n                </div>\n              </div>\n              <button\n                class=\"p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-400 transition-colors\"\n              >\n                <i class=\"fas fa-download text-sm\"></i>\n              </button>\n            </div>\n\n            <!-- Métadonnées -->\n            <div\n              class=\"flex items-center justify-end gap-1 mt-1 text-xs opacity-75\"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                class=\"flex items-center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double text-blue-400\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n\n            <!-- Réactions -->\n            <div\n              *ngIf=\"message.reactions && message.reactions.length > 0\"\n              class=\"flex gap-1 mt-2\"\n            >\n              <button\n                *ngFor=\"let reaction of message.reactions\"\n                (click)=\"toggleReaction(message.id!, reaction.emoji)\"\n                class=\"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\"\n                [class.bg-green-100]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n                [class.text-green-600]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n              >\n                <span>{{ reaction.emoji }}</span>\n                <span>1</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Indicateur de frappe -->\n      <div *ngIf=\"isTyping\" class=\"flex items-start gap-2\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-8 h-8 rounded-full object-cover\"\n        />\n        <div class=\"bg-white dark:bg-gray-700 px-4 py-2 rounded-2xl shadow-sm\">\n          <div class=\"flex gap-1\">\n            <div class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.1s\"\n            ></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.2s\"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- Zone d'input -->\n  <footer\n    class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"flex items-end gap-3\"\n    >\n      <!-- Actions gauche -->\n      <div class=\"flex gap-2\">\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showEmojiPicker\"\n          [class.text-green-600]=\"showEmojiPicker\"\n          title=\"Émojis\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showAttachmentMenu\"\n          [class.text-green-600]=\"showAttachmentMenu\"\n          title=\"Joindre un fichier\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n      </div>\n\n      <!-- Champ de saisie -->\n      <div class=\"flex-1\">\n        <textarea\n          formControlName=\"content\"\n          #messageTextarea\n          placeholder=\"Tapez votre message...\"\n          class=\"w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400\"\n          [disabled]=\"!otherParticipant || isRecordingVoice || isSendingMessage\"\n          (input)=\"onInputChange($event)\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (focus)=\"onInputFocus()\"\n          (blur)=\"onInputBlur()\"\n          rows=\"1\"\n          maxlength=\"4096\"\n          autocomplete=\"off\"\n          spellcheck=\"true\"\n        >\n        </textarea>\n      </div>\n\n      <!-- Actions droite -->\n      <div class=\"flex gap-2\">\n        <!-- Enregistrement vocal -->\n        <button\n          *ngIf=\"!messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (mousedown)=\"startVoiceRecording()\"\n          (mouseup)=\"stopVoiceRecording()\"\n          (mouseleave)=\"cancelVoiceRecording()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\"\n          [class.bg-red-500]=\"isRecordingVoice\"\n          [class.hover:bg-red-600]=\"isRecordingVoice\"\n          [class.animate-pulse]=\"isRecordingVoice\"\n          title=\"Maintenir pour enregistrer\"\n        >\n          <i class=\"fas fa-microphone\"></i>\n        </button>\n\n        <!-- Bouton d'envoi -->\n        <button\n          *ngIf=\"messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (click)=\"sendMessage()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\"\n          [disabled]=\"isSendingMessage\"\n          title=\"Envoyer\"\n        >\n          <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n          <i class=\"fas fa-spinner fa-spin\" *ngIf=\"isSendingMessage\"></i>\n        </button>\n      </div>\n    </form>\n  </footer>\n\n  <!-- Sélecteur d'émojis -->\n  <div\n    *ngIf=\"showEmojiPicker\"\n    class=\"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"flex gap-2 mb-4 overflow-x-auto\">\n        <button\n          *ngFor=\"let category of emojiCategories\"\n          (click)=\"selectEmojiCategory(category)\"\n          class=\"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\"\n          [class.bg-green-100]=\"selectedEmojiCategory === category\"\n          [class.text-green-600]=\"selectedEmojiCategory === category\"\n          [class.hover:bg-gray-100]=\"selectedEmojiCategory !== category\"\n          [class.dark:hover:bg-gray-700]=\"selectedEmojiCategory !== category\"\n        >\n          {{ category.icon }}\n        </button>\n      </div>\n      <div class=\"grid grid-cols-8 gap-2 max-h-48 overflow-y-auto\">\n        <button\n          *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n          (click)=\"insertEmoji(emoji)\"\n          class=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\"\n          [title]=\"emoji.name\"\n        >\n          {{ emoji.emoji }}\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Menu des pièces jointes -->\n  <div\n    *ngIf=\"showAttachmentMenu\"\n    class=\"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"grid grid-cols-2 gap-3\">\n        <button\n          (click)=\"triggerFileInput('image')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-image text-blue-600 dark:text-blue-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Photos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('video')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-video text-purple-600 dark:text-purple-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Vidéos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('document')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-file text-orange-600 dark:text-orange-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Documents</span\n          >\n        </button>\n        <button\n          (click)=\"openCamera()\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-camera text-green-600 dark:text-green-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Caméra</span\n          >\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input caché pour fichiers -->\n  <input\n    #fileInput\n    type=\"file\"\n    class=\"hidden\"\n    (change)=\"onFileSelected($event)\"\n    [accept]=\"getFileAcceptTypes()\"\n    multiple\n  />\n\n  <!-- Overlay pour fermer les menus -->\n  <div\n    *ngIf=\"showEmojiPicker || showAttachmentMenu\"\n    class=\"fixed inset-0 bg-black bg-opacity-25 z-40\"\n    (click)=\"closeAllMenus()\"\n  ></div>\n</div>\n"], "mappings": ";AAQA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;;;;;;;;;;;ICe3BC,EAAA,CAAAC,SAAA,cAGO;;;;;IAQLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAwB;IACtBF,EAAA,CAAAC,SAAA,cAEO;IASTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAERJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;IA+CNX,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,eACG;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAC5B;;;;;IAIHJ,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAAwE;IACtEF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAAwD;IACtDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAM,MAAA,CAAAJ,gBAAA,kBAAAI,MAAA,CAAAJ,gBAAA,CAAAK,QAAA,MACF;;;;;IAaEb,EAAA,CAAAE,cAAA,cAGC;IAKKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAQ,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAcFjB,EAAA,CAAAE,cAAA,cAGC;IAOGF,EAAA,CAAAkB,UAAA,mBAAAC,+EAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,IAAA;MAAA,MAAAL,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAD,OAAA,CAAAE,eAAA,CAAAV,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,CAAoC;IAAA,EAAC;IANhD5B,EAAA,CAAAI,YAAA,EAOE;;;;IANAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAG,KAAA,yCAAA9B,EAAA,CAAA+B,aAAA,CAEC,QAAAf,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAd,QAAA;;;;;IAkBHb,EAAA,CAAAE,cAAA,cAQC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAgC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAlB,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,EAAiD;IAEjD5B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAU,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAd,QAAA,MACF;;;;;IAGAb,EAAA,CAAAE,cAAA,cAAoE;IAClEF,EAAA,CAAAC,SAAA,cAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA6B,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAApB,WAAA,CAAAqB,OAAA,GAAArC,EAAA,CAAAsC,cAAA,CAAmD;;;;;IAsBxDtC,EAAA,CAAAC,SAAA,cAIO;;;;;IADLD,EAAA,CAAA6B,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAApB,WAAA,CAAAqB,OAAA,GAAArC,EAAA,CAAAsC,cAAA,CAAmD;;;;;;IArBvDtC,EAAA,CAAAE,cAAA,cAAiD;IAG7CF,EAAA,CAAAkB,UAAA,mBAAAsB,+EAAA;MAAAxC,EAAA,CAAAoB,aAAA,CAAAqB,IAAA;MAAA,MAAAzB,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAmB,OAAA,GAAA1C,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAiB,OAAA,CAAAC,eAAA,CAAA3B,WAAA,CAAwB;IAAA,EAAC;IAElChB,EAAA,CAAAC,SAAA,cAIE;IACFD,EAAA,CAAAE,cAAA,cAEC;IACCF,EAAA,CAAAC,SAAA,YAEK;IACPD,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAA4C,UAAA,IAAAC,+DAAA,kBAIO;IACT7C,EAAA,CAAAI,YAAA,EAAM;;;;;IAjBAJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA6B,UAAA,QAAAiB,OAAA,CAAAC,WAAA,CAAA/B,WAAA,GAAAhB,EAAA,CAAA+B,aAAA,CAA4B,QAAAf,WAAA,CAAAqB,OAAA;IAa7BrC,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAAqB,OAAA,CAAqB;;;;;;IAO1BrC,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAkB,UAAA,mBAAA8B,+EAAA;MAAAhD,EAAA,CAAAoB,aAAA,CAAA6B,IAAA;MAAA,MAAAjC,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAA2B,OAAA,GAAAlD,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAyB,OAAA,CAAAC,YAAA,CAAAnC,WAAA,CAAqB;IAAA,EAAC;IAE/BhB,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAAC,SAAA,QAAsC;IACxCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAA4B;IAExBF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAAsD;IACpDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAE,cAAA,iBAEC;IACCF,EAAA,CAAAC,SAAA,YAAuC;IACzCD,EAAA,CAAAI,YAAA,EAAS;;;;;IAdJJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAoD,UAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAtC,WAAA,EAA8B;IAI/BhB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+C,OAAA,CAAAE,WAAA,CAAAvC,WAAA,OACF;IAEEhB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+C,OAAA,CAAAG,WAAA,CAAAxC,WAAA,OACF;;;;;IAkBAhB,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,YAIK;;;;;IAvBPD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAA4C,UAAA,IAAAa,8DAAA,gBAIK;IACLzD,EAAA,CAAA4C,UAAA,IAAAc,8DAAA,gBAIK;IACL1D,EAAA,CAAA4C,UAAA,IAAAe,8DAAA,gBAIK;IACL3D,EAAA,CAAA4C,UAAA,IAAAgB,8DAAA,gBAIK;IACP5D,EAAA,CAAAI,YAAA,EAAM;;;;IAlBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,eAAkC;IAKlC7D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,YAA+B;IAK/B7D,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,iBAAoC;IAKpC7D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,YAA+B;;;;;;IAWpC7D,EAAA,CAAAE,cAAA,kBAUC;IARCF,EAAA,CAAAkB,UAAA,mBAAA4C,4FAAA;MAAA,MAAAC,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAA4C,IAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAAxC,SAAA;MAAA,MAAAP,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,IAAAC,SAAA;MAAA,MAAA2C,OAAA,GAAAlE,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAyC,OAAA,CAAAC,cAAA,CAAAnD,WAAA,CAAAY,EAAA,EAAAqC,YAAA,CAAAG,KAAA,CAA2C;IAAA,EAAC;IASrDpE,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,QAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IARdJ,EAAA,CAAAqE,WAAA,iBAAAC,OAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAK,OAAA,CAAAE,aAAA,QAEC,mBAAAF,OAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAK,OAAA,CAAAE,aAAA;IAKKxE,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAyE,iBAAA,CAAAR,YAAA,CAAAG,KAAA,CAAoB;;;;;IAf9BpE,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAA4C,UAAA,IAAA8B,mEAAA,sBAaS;IACX1E,EAAA,CAAAI,YAAA,EAAM;;;;IAbmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA6B,UAAA,YAAAb,WAAA,CAAA2D,SAAA,CAAoB;;;;;;IAjKnD3E,EAAA,CAAA4E,uBAAA,GAMC;IAEC5E,EAAA,CAAA4C,UAAA,IAAAiC,yDAAA,kBAWM;IAGN7E,EAAA,CAAAE,cAAA,cAOC;IAFCF,EAAA,CAAAkB,UAAA,mBAAA4D,yEAAAC,MAAA;MAAA,MAAAhB,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAA4D,IAAA;MAAA,MAAAhE,WAAA,GAAA+C,WAAA,CAAAxC,SAAA;MAAA,MAAA0D,OAAA,GAAAjF,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAwD,OAAA,CAAAC,cAAA,CAAAlE,WAAA,EAAA+D,MAAA,CAA+B;IAAA,EAAC,yBAAAI,+EAAAJ,MAAA;MAAA,MAAAhB,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAA4D,IAAA;MAAA,MAAAhE,WAAA,GAAA+C,WAAA,CAAAxC,SAAA;MAAA,MAAA6D,OAAA,GAAApF,EAAA,CAAAsB,aAAA;MAAA,OAC1BtB,EAAA,CAAAyB,WAAA,CAAA2D,OAAA,CAAAC,oBAAA,CAAArE,WAAA,EAAA+D,MAAA,CAAqC;IAAA,EADX;IAIzC/E,EAAA,CAAA4C,UAAA,IAAA0C,yDAAA,kBAYM;IAGNtF,EAAA,CAAAE,cAAA,cAQC;IAECF,EAAA,CAAA4C,UAAA,IAAA2C,yDAAA,kBAUM;IAGNvF,EAAA,CAAA4C,UAAA,IAAA4C,yDAAA,kBAEM;IAGNxF,EAAA,CAAA4C,UAAA,IAAA6C,yDAAA,kBAuBM;IAGNzF,EAAA,CAAA4C,UAAA,IAAA8C,yDAAA,mBAqBM;IAGN1F,EAAA,CAAAE,cAAA,cAEC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAA4C,UAAA,KAAA+C,0DAAA,kBAwBM;IACR3F,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAA4C,UAAA,KAAAgD,0DAAA,kBAkBM;IACR5F,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAA6F,qBAAA,EAAe;;;;;;IAxKV7F,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAejChG,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAqE,WAAA,iBAAArD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,CAA0D,mBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA;IAE1DxE,EAAA,CAAA6B,UAAA,oBAAAb,WAAA,CAAAY,EAAA,CAA8B;IAM3B5B,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAA6B,UAAA,UAAAb,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,IAAAsB,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAgBlEhG,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAqE,WAAA,kBAAArD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,CAA2D,gBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,eAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,oBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,uBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,sBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA;IASxDxE,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAI,mBAAA,OAAAlF,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,IAAAsB,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAOkBhG,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAM,cAAA,CAAApF,WAAA,aAAwC;IAKxChB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAO,QAAA,CAAArF,WAAA,EAAuB;IA2B1BhB,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAQ,OAAA,CAAAtF,WAAA,EAAsB;IA0BjBhB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAyE,iBAAA,CAAAqB,OAAA,CAAAS,iBAAA,CAAAvF,WAAA,CAAAC,SAAA,EAA0C;IAE7CjB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA6B,UAAA,UAAAb,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,CAA0C;IA4B5CxE,EAAA,CAAAK,SAAA,GAAuD;IAAvDL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA2D,SAAA,IAAA3D,WAAA,CAAA2D,SAAA,CAAA6B,MAAA,KAAuD;;;;;IAuBhExG,EAAA,CAAAE,cAAA,eAAqD;IACnDF,EAAA,CAAAC,SAAA,eAIE;IACFD,EAAA,CAAAE,cAAA,eAAuE;IAEnEF,EAAA,CAAAC,SAAA,eAAmE;IASrED,EAAA,CAAAI,YAAA,EAAM;;;;IAfNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAA6B,UAAA,SAAA4E,OAAA,CAAAjG,gBAAA,kBAAAiG,OAAA,CAAAjG,gBAAA,CAAAsB,KAAA,yCAAA9B,EAAA,CAAA+B,aAAA,CAAqE,QAAA0E,OAAA,CAAAjG,gBAAA,kBAAAiG,OAAA,CAAAjG,gBAAA,CAAAK,QAAA;;;;;IAvL3Eb,EAAA,CAAAE,cAAA,cAAiE;IAC/DF,EAAA,CAAA4C,UAAA,IAAA8D,mDAAA,6BAiLe;IAGf1G,EAAA,CAAA4C,UAAA,IAAA+D,0CAAA,kBAmBM;IACR3G,EAAA,CAAAI,YAAA,EAAM;;;;IAtMuBJ,EAAA,CAAAK,SAAA,GACZ;IADYL,EAAA,CAAA6B,UAAA,YAAA+E,MAAA,CAAAC,QAAA,CACZ,iBAAAD,MAAA,CAAAE,gBAAA;IAiLT9G,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA6B,UAAA,SAAA+E,MAAA,CAAAG,QAAA,CAAc;;;;;;IA+ElB/G,EAAA,CAAAE,cAAA,kBAWC;IARCF,EAAA,CAAAkB,UAAA,uBAAA8F,oEAAA;MAAAhH,EAAA,CAAAoB,aAAA,CAAA6F,IAAA;MAAA,MAAAC,OAAA,GAAAlH,EAAA,CAAAsB,aAAA;MAAA,OAAatB,EAAA,CAAAyB,WAAA,CAAAyF,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC,qBAAAC,kEAAA;MAAApH,EAAA,CAAAoB,aAAA,CAAA6F,IAAA;MAAA,MAAAI,OAAA,GAAArH,EAAA,CAAAsB,aAAA;MAAA,OACxBtB,EAAA,CAAAyB,WAAA,CAAA4F,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EADI,wBAAAC,qEAAA;MAAAvH,EAAA,CAAAoB,aAAA,CAAA6F,IAAA;MAAA,MAAAO,OAAA,GAAAxH,EAAA,CAAAsB,aAAA;MAAA,OAErBtB,EAAA,CAAAyB,WAAA,CAAA+F,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAFD;IASnCzH,EAAA,CAAAC,SAAA,aAAiC;IACnCD,EAAA,CAAAI,YAAA,EAAS;;;;IANPJ,EAAA,CAAAqE,WAAA,eAAAqD,MAAA,CAAAC,gBAAA,CAAqC,qBAAAD,MAAA,CAAAC,gBAAA,mBAAAD,MAAA,CAAAC,gBAAA;;;;;IAiBrC3H,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,aAA+D;;;;;;IATjED,EAAA,CAAAE,cAAA,kBAOC;IAJCF,EAAA,CAAAkB,UAAA,mBAAA0G,gEAAA;MAAA5H,EAAA,CAAAoB,aAAA,CAAAyG,IAAA;MAAA,MAAAC,OAAA,GAAA9H,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAqG,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvB/H,EAAA,CAAA4C,UAAA,IAAAoF,2CAAA,iBAA4D;IAC5DhI,EAAA,CAAA4C,UAAA,IAAAqF,2CAAA,iBAA+D;IACjEjI,EAAA,CAAAI,YAAA,EAAS;;;;IALPJ,EAAA,CAAA6B,UAAA,aAAAqG,MAAA,CAAAC,gBAAA,CAA6B;IAGEnI,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,UAAAqG,MAAA,CAAAC,gBAAA,CAAuB;IACnBnI,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,SAAAqG,MAAA,CAAAC,gBAAA,CAAsB;;;;;;IAa3DnI,EAAA,CAAAE,cAAA,kBAQC;IANCF,EAAA,CAAAkB,UAAA,mBAAAkH,sEAAA;MAAA,MAAArE,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAAiH,IAAA;MAAA,MAAAC,YAAA,GAAAvE,WAAA,CAAAxC,SAAA;MAAA,MAAAgH,OAAA,GAAAvI,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAA8G,OAAA,CAAAC,mBAAA,CAAAF,YAAA,CAA6B;IAAA,EAAC;IAOvCtI,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IANPJ,EAAA,CAAAqE,WAAA,iBAAAoE,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,CAAyD,mBAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,uBAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,4BAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA;IAKzDtI,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAgI,YAAA,CAAAK,IAAA,MACF;;;;;;IAGA3I,EAAA,CAAAE,cAAA,kBAKC;IAHCF,EAAA,CAAAkB,UAAA,mBAAA0H,sEAAA;MAAA,MAAA7E,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAAyH,IAAA;MAAA,MAAAC,SAAA,GAAA/E,WAAA,CAAAxC,SAAA;MAAA,MAAAwH,OAAA,GAAA/I,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAsH,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAI5B9I,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAA6B,UAAA,UAAAiH,SAAA,CAAAG,IAAA,CAAoB;IAEpBjJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwI,SAAA,CAAA1E,KAAA,MACF;;;;;IA1BNpE,EAAA,CAAAE,cAAA,eAGC;IAGKF,EAAA,CAAA4C,UAAA,IAAAsG,6CAAA,sBAUS;IACXlJ,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA6D;IAC3DF,EAAA,CAAA4C,UAAA,IAAAuG,6CAAA,sBAOS;IACXnJ,EAAA,CAAAI,YAAA,EAAM;;;;IApBmBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA6B,UAAA,YAAAuH,OAAA,CAAAC,eAAA,CAAkB;IAarBrJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAA6B,UAAA,YAAAuH,OAAA,CAAAE,oBAAA,CAAAF,OAAA,CAAAV,qBAAA,EAA8C;;;;;;IAYxE1I,EAAA,CAAAE,cAAA,eAGC;IAIOF,EAAA,CAAAkB,UAAA,mBAAAqI,6DAAA;MAAAvJ,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAC,OAAA,GAAAzJ,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAgI,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC1J,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,aAA6D;IAC/DD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,kBAGC;IAFCF,EAAA,CAAAkB,UAAA,mBAAAyI,6DAAA;MAAA3J,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAI,OAAA,GAAA5J,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAmI,OAAA,CAAAF,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC1J,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,cAAiE;IACnED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAkB,UAAA,mBAAA2I,8DAAA;MAAA7J,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAM,OAAA,GAAA9J,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAqI,OAAA,CAAAJ,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAGtC1J,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAkB,UAAA,mBAAA6I,8DAAA;MAAA/J,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAQ,OAAA,GAAAhK,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAuI,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAGtBjK,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAiBTJ,EAAA,CAAAE,cAAA,eAIC;IADCF,EAAA,CAAAkB,UAAA,mBAAAgJ,0DAAA;MAAAlK,EAAA,CAAAoB,aAAA,CAAA+I,IAAA;MAAA,MAAAC,OAAA,GAAApK,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAA2I,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1BrK,EAAA,CAAAI,YAAA,EAAM;;;ADxgBT,OAAM,MAAOkK,oBAAoB;EAuG/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,cAA8B,EAC9BC,YAA0B,EAC1BC,GAAsB;IAJtB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IAtGb;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAhE,QAAQ,GAAU,EAAE;IACpB,KAAArC,aAAa,GAAkB,IAAI;IACnC,KAAAsG,eAAe,GAAG,KAAK;IAEvB,KAAAtK,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAAuK,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAApD,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAR,gBAAgB,GAAG,KAAK;IACxB,KAAA6D,sBAAsB,GAAG,CAAC;IAC1B,KAAAC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAElC;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAA3C,eAAe,GAAU,CACvB;MACEzH,EAAE,EAAE,SAAS;MACbqH,IAAI,EAAE,SAAS;MACfN,IAAI,EAAE,IAAI;MACVsD,MAAM,EAAE,CACN;QAAE7H,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACErH,EAAE,EAAE,QAAQ;MACZqH,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVsD,MAAM,EAAE,CACN;QAAE7H,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACErH,EAAE,EAAE,QAAQ;MACZqH,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVsD,MAAM,EAAE,CACN;QAAE7H,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAP,qBAAqB,GAAG,IAAI,CAACW,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAA6C,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAApF,QAAQ,GAAG,KAAK;IAChB,KAAAqF,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAIvM,YAAY,EAAE;IASxC,IAAI,CAACwM,WAAW,GAAG,IAAI,CAAC/B,EAAE,CAACgC,KAAK,CAAC;MAC/BnK,OAAO,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAAC2M,QAAQ,EAAE3M,UAAU,CAAC4M,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQF,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEJ,UAAU,CAAC;MAEzD,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtEG,OAAO,CAACE,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAAC7I,aAAa,GAAG,IAAI;QACzB,IAAI,CAACsG,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAMwC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACR,UAAU,CAAC;MACnCG,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEE,IAAI,CAAC;MAE3C;MACA,MAAMG,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAAC1L,EAAE,IAAI0L,IAAI,CAACG,MAAM;MACjDN,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CM,GAAG,EAAEJ,IAAI,CAACI,GAAG;QACb9L,EAAE,EAAE0L,IAAI,CAAC1L,EAAE;QACX6L,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBE,SAAS,EAAEF;OACZ,CAAC;MAEF,IAAIA,MAAM,EAAE;QACV,IAAI,CAACjJ,aAAa,GAAGiJ,MAAM;QAC3B,IAAI,CAAC3C,eAAe,GAAGwC,IAAI,CAACzM,QAAQ,IAAIyM,IAAI,CAACrE,IAAI,IAAI,KAAK;QAC1DkE,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjDxL,EAAE,EAAE,IAAI,CAAC4C,aAAa;UACtB3D,QAAQ,EAAE,IAAI,CAACiK;SAChB,CAAC;OACH,MAAM;QACLqC,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEC,IAAI,CAAC;QAC/D,IAAI,CAAC9I,aAAa,GAAG,IAAI;QACzB,IAAI,CAACsG,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAOuC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAAC7I,aAAa,GAAG,IAAI;MACzB,IAAI,CAACsG,eAAe,GAAG,KAAK;;EAEhC;EAEQgC,gBAAgBA,CAAA;IACtB,MAAMc,cAAc,GAAG,IAAI,CAACnD,KAAK,CAACoD,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAC7DZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEQ,cAAc,CAAC;IAE5D,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACjD,YAAY,CAACqD,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAACjD,SAAS,GAAG,IAAI;IACrB,IAAI,CAACL,cAAc,CAACuD,eAAe,CAACL,cAAc,CAAC,CAACM,SAAS,CAAC;MAC5DC,IAAI,EAAGtD,YAAY,IAAI;QACrBsC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEvC,YAAY,CAAC;QACjEsC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACxCxL,EAAE,EAAEiJ,YAAY,EAAEjJ,EAAE;UACpBwM,YAAY,EAAEvD,YAAY,EAAEuD,YAAY;UACxCC,iBAAiB,EAAExD,YAAY,EAAEuD,YAAY,EAAE5H,MAAM;UACrD8H,OAAO,EAAEzD,YAAY,EAAEyD,OAAO;UAC9BzH,QAAQ,EAAEgE,YAAY,EAAEhE,QAAQ;UAChC0H,aAAa,EAAE1D,YAAY,EAAEhE,QAAQ,EAAEL;SACxC,CAAC;QACF,IAAI,CAACqE,YAAY,GAAGA,YAAY;QAChC,IAAI,CAAC2D,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;MACrB,CAAC;MACDpB,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAC1C,YAAY,CAACqD,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAACjD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQyD,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAAC3D,YAAY,EAAEuD,YAAY,IAChC,IAAI,CAACvD,YAAY,CAACuD,YAAY,CAAC5H,MAAM,KAAK,CAAC,EAC3C;MACA2G,OAAO,CAACuB,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAAClO,gBAAgB,GAAG,IAAI;MAC5B;;IAGF2M,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC5I,aAAa,CAAC;IACnD2I,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACvC,YAAY,CAACuD,YAAY,CAAC;IAEhE;IACA;IAEA,IAAI,IAAI,CAACvD,YAAY,CAACyD,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAAC9N,gBAAgB,GAAG,IAAI,CAACqK,YAAY,CAACuD,YAAY,CAACO,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAChN,EAAE,IAAIgN,CAAC,CAAClB,GAAG;QACnC,OAAOoB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACtK,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAChE,gBAAgB,GAAG,IAAI,CAACqK,YAAY,CAACuD,YAAY,CAACO,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAChN,EAAE,IAAIgN,CAAC,CAAClB,GAAG;QACnCP,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3ByB,aAAa,EACb,uBAAuB,EACvB,IAAI,CAACrK,aAAa,CACnB;QACD,OAAOsK,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAACtK,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAAChE,gBAAgB,IAAI,IAAI,CAACqK,YAAY,CAACuD,YAAY,CAAC5H,MAAM,GAAG,CAAC,EAAE;MACvE2G,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAC5M,gBAAgB,GAAG,IAAI,CAACqK,YAAY,CAACuD,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAACvD,YAAY,CAACuD,YAAY,CAAC5H,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMuI,kBAAkB,GACtB,IAAI,CAACvO,gBAAgB,CAACoB,EAAE,IAAI,IAAI,CAACpB,gBAAgB,CAACkN,GAAG;QACvD,IAAIoB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAACtK,aAAa,CAAC,EAAE;UAC7D2I,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAAC5M,gBAAgB,GAAG,IAAI,CAACqK,YAAY,CAACuD,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAAC5N,gBAAgB,EAAE;MACzB2M,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACnDxL,EAAE,EAAE,IAAI,CAACpB,gBAAgB,CAACoB,EAAE,IAAI,IAAI,CAACpB,gBAAgB,CAACkN,GAAG;QACzD7M,QAAQ,EAAE,IAAI,CAACL,gBAAgB,CAACK,QAAQ;QACxCiB,KAAK,EAAE,IAAI,CAACtB,gBAAgB,CAACsB,KAAK;QAClCrB,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC;OACjC,CAAC;MAEF;MACA0M,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC5M,gBAAgB,CAACK,QAAQ,CAC/B;MACDsM,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAAC5M,gBAAgB,CAACK,QAAQ,CAC/B;KACF,MAAM;MACLsM,OAAO,CAACE,KAAK,CAAC,uDAAuD,CAAC;MACtEF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvC,YAAY,CAACuD,YAAY,CAAC;MACzEjB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC5I,aAAa,CAAC;MAEnD;MACA2I,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;EAEvD;EAEQqB,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAAC5D,YAAY,EAAEjJ,EAAE,EAAE;IAE5B;IACA,IAAI,CAACiF,QAAQ,GAAG,IAAI,CAACgE,YAAY,CAAChE,QAAQ,IAAI,EAAE;IAChD,IAAI,CAACoE,eAAe,GAAG,IAAI,CAACpE,QAAQ,CAACL,MAAM,KAAK,IAAI,CAAC0F,oBAAoB;IACzE,IAAI,CAACnB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACiE,cAAc,EAAE;EACvB;EAEQjC,kBAAkBA,CAAA;IACxB;IACA;EAAA;EAGF;EACAhF,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACwE,WAAW,CAAC0C,KAAK,IAAI,CAAC,IAAI,CAACpE,YAAY,EAAEjJ,EAAE,EAAE;IAEvD,MAAMS,OAAO,GAAG,IAAI,CAACkK,WAAW,CAACwB,GAAG,CAAC,SAAS,CAAC,EAAEmB,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAAC9M,OAAO,EAAE;IAEd,MAAM+M,UAAU,GAAG,IAAI,CAACvE,YAAY,CAACuD,YAAY,EAAEO,IAAI,CACpDC,CAAM,IAAKA,CAAC,CAAChN,EAAE,KAAK,IAAI,CAAC4C,aAAa,CACxC,EAAE5C,EAAE;IAEL,IAAI,CAACwN,UAAU,EAAE;MACf,IAAI,CAACzE,YAAY,CAACqD,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF,IAAI,CAACtD,cAAc,CAAC3C,WAAW,CAC7BqH,UAAU,EACV/M,OAAO,EACPgN,SAAS,EACT,MAAa,EACb,IAAI,CAACxE,YAAY,CAACjJ,EAAE,CACrB,CAACsM,SAAS,CAAC;MACVC,IAAI,EAAGmB,OAAY,IAAI;QACrB,IAAI,CAACzI,QAAQ,CAAC0I,IAAI,CAACD,OAAO,CAAC;QAC3B,IAAI,CAAC/C,WAAW,CAACiD,KAAK,EAAE;QACxB,IAAI,CAACR,cAAc,EAAE;MACvB,CAAC;MACD3B,KAAK,EAAGA,KAAU,IAAI;QACpBF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAAC1C,YAAY,CAACqD,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEAgB,cAAcA,CAAA;IACZS,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACApP,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAMoP,IAAI,GAAG,IAAIC,IAAI,CAACrP,UAAU,CAAC;IACjC,MAAMsP,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,MAAM,GAAGD,GAAG,CAACE,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE;IAC7C,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAG,KAAK,CAAC;IAE3C,IAAIE,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAElD,MAAMG,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC;IAC3C,IAAIG,SAAS,GAAG,EAAE,EAAE,OAAO,UAAUA,SAAS,GAAG;IAEjD,MAAMC,QAAQ,GAAGH,IAAI,CAACC,KAAK,CAACC,SAAS,GAAG,EAAE,CAAC;IAC3C,OAAO,UAAUC,QAAQ,GAAG;EAC9B;EAEAjK,iBAAiBA,CAACtF,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAM8O,IAAI,GAAG,IAAIC,IAAI,CAAC/O,SAAS,CAAC;IAChC,OAAO8O,IAAI,CAACU,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA5P,mBAAmBA,CAACE,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAM8O,IAAI,GAAG,IAAIC,IAAI,CAAC/O,SAAS,CAAC;IAChC,MAAM2P,KAAK,GAAG,IAAIZ,IAAI,EAAE;IACxB,MAAMa,SAAS,GAAG,IAAIb,IAAI,CAACY,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIhB,IAAI,CAACiB,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIjB,IAAI,CAACiB,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOjB,IAAI,CAACkB,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEA7O,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAM6O,QAAQ,GAAG,sBAAsB;IACvC,OAAO7O,OAAO,CAAC8O,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEAnL,uBAAuBA,CAACqL,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAMC,cAAc,GAAG,IAAI,CAACxK,QAAQ,CAACuK,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAACzK,QAAQ,CAACuK,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACC,cAAc,EAAEpQ,SAAS,IAAI,CAACqQ,eAAe,EAAErQ,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAMsQ,WAAW,GAAG,IAAIvB,IAAI,CAACqB,cAAc,CAACpQ,SAAS,CAAC,CAAC+P,YAAY,EAAE;IACrE,MAAMQ,YAAY,GAAG,IAAIxB,IAAI,CAACsB,eAAe,CAACrQ,SAAS,CAAC,CAAC+P,YAAY,EAAE;IAEvE,OAAOO,WAAW,KAAKC,YAAY;EACrC;EAEAvL,gBAAgBA,CAACmL,KAAa;IAC5B,MAAMC,cAAc,GAAG,IAAI,CAACxK,QAAQ,CAACuK,KAAK,CAAC;IAC3C,MAAMK,WAAW,GAAG,IAAI,CAAC5K,QAAQ,CAACuK,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACK,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOJ,cAAc,CAAC1P,MAAM,EAAEC,EAAE,KAAK6P,WAAW,CAAC9P,MAAM,EAAEC,EAAE;EAC7D;EAEAuE,oBAAoBA,CAACiL,KAAa;IAChC,MAAMC,cAAc,GAAG,IAAI,CAACxK,QAAQ,CAACuK,KAAK,CAAC;IAC3C,MAAME,eAAe,GAAG,IAAI,CAACzK,QAAQ,CAACuK,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACE,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAAC1P,MAAM,EAAEC,EAAE,KAAK0P,eAAe,CAAC3P,MAAM,EAAEC,EAAE;EACjE;EAEAwE,cAAcA,CAACkJ,OAAY;IACzB,IAAIA,OAAO,CAACoC,WAAW,IAAIpC,OAAO,CAACoC,WAAW,CAAClL,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMmL,UAAU,GAAGrC,OAAO,CAACoC,WAAW,CAAC,CAAC,CAAC;MACzC,IAAIC,UAAU,CAACC,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIF,UAAU,CAACC,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIF,UAAU,CAACC,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAEf,OAAO,MAAM;EACf;EAEAxL,QAAQA,CAACiJ,OAAY;IACnB,OACEA,OAAO,CAACoC,WAAW,EAAEI,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACH,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,CAAC,IACvE,KAAK;EAET;EAEAvL,OAAOA,CAACgJ,OAAY;IAClB,OACEA,OAAO,CAACoC,WAAW,EAAEI,IAAI,CACtBC,GAAQ,IAAK,CAACA,GAAG,CAACH,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,CAC9C,IAAI,KAAK;EAEd;EAEA9O,WAAWA,CAACuM,OAAY;IACtB,MAAM0C,eAAe,GAAG1C,OAAO,CAACoC,WAAW,EAAE/C,IAAI,CAAEoD,GAAQ,IACzDA,GAAG,CAACH,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,OAAOG,eAAe,EAAEC,GAAG,IAAI,EAAE;EACnC;EAEA1O,WAAWA,CAAC+L,OAAY;IACtB,MAAM4C,cAAc,GAAG5C,OAAO,CAACoC,WAAW,EAAE/C,IAAI,CAC7CoD,GAAQ,IAAK,CAACA,GAAG,CAACH,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOK,cAAc,EAAEjJ,IAAI,IAAI,SAAS;EAC1C;EAEAzF,WAAWA,CAAC8L,OAAY;IACtB,MAAM4C,cAAc,GAAG5C,OAAO,CAACoC,WAAW,EAAE/C,IAAI,CAC7CoD,GAAQ,IAAK,CAACA,GAAG,CAACH,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACK,cAAc,EAAEC,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAMC,KAAK,GAAGF,cAAc,CAACC,IAAI;IACjC,IAAIC,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAO/B,IAAI,CAACgC,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAO/B,IAAI,CAACgC,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEA9O,WAAWA,CAACgM,OAAY;IACtB,MAAM4C,cAAc,GAAG5C,OAAO,CAACoC,WAAW,EAAE/C,IAAI,CAC7CoD,GAAQ,IAAK,CAACA,GAAG,CAACH,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACK,cAAc,EAAEN,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAIM,cAAc,CAACN,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAIK,cAAc,CAACN,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAIK,cAAc,CAACN,IAAI,CAACU,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAIJ,cAAc,CAACN,IAAI,CAACU,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAIJ,cAAc,CAACN,IAAI,CAACU,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEApQ,YAAYA,CAACuL,MAAc;IACzB;IACA,MAAM8E,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAMnB,KAAK,GAAG3D,MAAM,CAAC+E,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAC/L,MAAM;IAClD,OAAO+L,MAAM,CAACnB,KAAK,CAAC;EACtB;EAEA;EACAlM,cAAcA,CAACoK,OAAY,EAAEmD,KAAU;IACrCtF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkC,OAAO,CAAC;EAC1C;EAEAjK,oBAAoBA,CAACiK,OAAY,EAAEmD,KAAU;IAC3CA,KAAK,CAACC,cAAc,EAAE;IACtBvF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEkC,OAAO,CAAC;EAC/C;EAEAqD,aAAaA,CAACF,KAAU;IACtB;EAAA;EAGFG,cAAcA,CAACH,KAAoB;IACjC,IAAIA,KAAK,CAACI,GAAG,KAAK,OAAO,IAAI,CAACJ,KAAK,CAACK,QAAQ,EAAE;MAC5CL,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAAC3K,WAAW,EAAE;;EAEtB;EAEAgL,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAACR,KAAU;IACjB;EAAA;EAGF/Q,eAAeA,CAAC+L,MAAc;IAC5BN,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEK,MAAM,CAAC;EAClD;EAEA9K,eAAeA,CAAC2M,OAAY;IAC1B,MAAM0C,eAAe,GAAG1C,OAAO,CAACoC,WAAW,EAAE/C,IAAI,CAAEoD,GAAQ,IACzDA,GAAG,CAACH,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAIG,eAAe,EAAEC,GAAG,EAAE;MACxBiB,MAAM,CAACC,IAAI,CAACnB,eAAe,CAACC,GAAG,EAAE,QAAQ,CAAC;;EAE9C;EAEA9O,YAAYA,CAACmM,OAAY;IACvB,MAAM4C,cAAc,GAAG5C,OAAO,CAACoC,WAAW,EAAE/C,IAAI,CAC7CoD,GAAQ,IAAK,CAACA,GAAG,CAACH,IAAI,EAAEC,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAIK,cAAc,EAAED,GAAG,EAAE;MACvBiB,MAAM,CAACC,IAAI,CAACjB,cAAc,CAACD,GAAG,EAAE,QAAQ,CAAC;;EAE7C;EAEA9N,cAAcA,CAACiP,SAAiB,EAAEhP,KAAa;IAC7C+I,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgG,SAAS,EAAEhP,KAAK,CAAC;EACnD;EAEAG,cAAcA,CAAC8O,QAAa,EAAE5F,MAAc;IAC1C,OAAO4F,QAAQ,CAACC,KAAK,EAAEhB,QAAQ,CAAC7E,MAAM,CAAC,IAAI,KAAK;EAClD;EAEA8F,YAAYA,CAAA;IACV,IAAI,CAAChI,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB,IAAI,CAACF,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,EAAE;;EAE3B;EAEAkI,cAAcA,CAAA;IACZrG,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC;EAEAqG,oBAAoBA,CAAA;IAClB,IAAI,CAACtI,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,CAACD,eAAe,GAAG,KAAK;EAC9B;EAEAwI,iBAAiBA,CAAA;IACf,IAAI,CAACxI,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,CAACC,kBAAkB,GAAG,KAAK;EACjC;EAEAzB,gBAAgBA,CAACkI,IAAa;IAC5B;IACA,MAAM+B,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAAC/B,IAAI,GAAG,MAAM;IAEnB,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB+B,KAAK,CAACG,MAAM,GAAG,SAAS;KACzB,MAAM,IAAIlC,IAAI,KAAK,OAAO,EAAE;MAC3B+B,KAAK,CAACG,MAAM,GAAG,SAAS;KACzB,MAAM,IAAIlC,IAAI,KAAK,UAAU,EAAE;MAC9B+B,KAAK,CAACG,MAAM,GAAG,iCAAiC;;IAGlDH,KAAK,CAACI,QAAQ,GAAItB,KAAU,IAAI;MAC9B;MACAtF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqF,KAAK,CAACuB,MAAM,CAACC,KAAK,CAAC;IACnD,CAAC;IAEDN,KAAK,CAACO,KAAK,EAAE;IACb,IAAI,CAAC/I,kBAAkB,GAAG,KAAK;EACjC;EAEAlB,UAAUA,CAAA;IACRkD,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI,CAACjC,kBAAkB,GAAG,KAAK;EACjC;EAEA;EACA7B,oBAAoBA,CAAC6K,QAAa;IAChC,OAAOA,QAAQ,EAAElI,MAAM,IAAI,EAAE;EAC/B;EAEAzD,mBAAmBA,CAAC2L,QAAa;IAC/B,IAAI,CAACzL,qBAAqB,GAAGyL,QAAQ;EACvC;EAEAnL,WAAWA,CAAC5E,KAAU;IACpB,MAAMgQ,cAAc,GAAG,IAAI,CAAC7H,WAAW,CAACwB,GAAG,CAAC,SAAS,CAAC,EAAEmB,KAAK,IAAI,EAAE;IACnE,MAAMmF,UAAU,GAAGD,cAAc,IAAIhQ,KAAK,CAACA,KAAK,IAAIA,KAAK,CAAC;IAC1D,IAAI,CAACmI,WAAW,CAAC+H,UAAU,CAAC;MAAEjS,OAAO,EAAEgS;IAAU,CAAE,CAAC;IAEpD,IAAI,CAACnJ,eAAe,GAAG,KAAK;IAE5BuE,UAAU,CAAC,MAAK;MACd,MAAM8E,QAAQ,GAAGX,QAAQ,CAACY,aAAa,CACrC,qCAAqC,CACf;MACxB,IAAID,QAAQ,EAAE;QACZA,QAAQ,CAACE,KAAK,EAAE;;IAEpB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAC,qBAAqBA,CAAA;IACnBxB,MAAM,CAACyB,OAAO,CAACC,IAAI,EAAE;EACvB;EAEAC,cAAcA,CAAA;IACZ1H,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC;EACF;;EAEA0H,cAAcA,CAAA;IACZ3H,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC;EACF;;EAEAtG,gBAAgBA,CAACsK,KAAa,EAAE9B,OAAY;IAC1C,OAAOA,OAAO,CAAC1N,EAAE,IAAIwP,KAAK;EAC5B;EAEAlL,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC2E,YAAY,EAAEuD,YAAY,EAAE5H,MAAM,GAAG,CAAC,IAAI,KAAK;EAC7D;EAEMW,mBAAmBA,CAAA;IAAA,IAAA4N,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDC,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE;;SAEpB,CAAC;QAEFT,KAAI,CAACrJ,aAAa,GAAG,IAAI+J,aAAa,CAACR,MAAM,EAAE;UAC7CS,QAAQ,EAAE;SACX,CAAC;QAEFX,KAAI,CAACpJ,WAAW,GAAG,EAAE;QACrBoJ,KAAI,CAACpN,gBAAgB,GAAG,IAAI;QAC5BoN,KAAI,CAACvJ,sBAAsB,GAAG,CAAC;QAC/BuJ,KAAI,CAACtJ,mBAAmB,GAAG,WAAW;QAEtCsJ,KAAI,CAACnJ,cAAc,GAAG+J,WAAW,CAAC,MAAK;UACrCZ,KAAI,CAACvJ,sBAAsB,EAAE;UAC7BuJ,KAAI,CAACnK,GAAG,CAACgL,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAERb,KAAI,CAACrJ,aAAa,CAACmK,eAAe,GAAIpD,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAACqD,IAAI,CAAC3D,IAAI,GAAG,CAAC,EAAE;YACvB4C,KAAI,CAACpJ,WAAW,CAAC4D,IAAI,CAACkD,KAAK,CAACqD,IAAI,CAAC;;QAErC,CAAC;QAEDf,KAAI,CAACrJ,aAAa,CAACqK,MAAM,GAAG,MAAK;UAC/BhB,KAAI,CAACiB,oBAAoB,EAAE;QAC7B,CAAC;QAEDjB,KAAI,CAACrJ,aAAa,CAACuK,KAAK,CAAC,GAAG,CAAC;QAC7BlB,KAAI,CAACpK,YAAY,CAACuL,WAAW,CAAC,8BAA8B,CAAC;OAC9D,CAAC,OAAO7I,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE0H,KAAI,CAACpK,YAAY,CAACqD,SAAS,CAAC,oCAAoC,CAAC;QACjE+G,KAAI,CAACtN,oBAAoB,EAAE;;IAC5B;EACH;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACoE,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyK,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACzK,aAAa,CAAC0K,IAAI,EAAE;MACzB,IAAI,CAAC1K,aAAa,CAACuJ,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAACxK,cAAc,EAAE;MACvB4K,aAAa,CAAC,IAAI,CAAC5K,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACjE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC8D,mBAAmB,GAAG,YAAY;EACzC;EAEAhE,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACiE,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACyK,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACzK,aAAa,CAAC0K,IAAI,EAAE;;MAE3B,IAAI,CAAC1K,aAAa,CAACuJ,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;MACtE,IAAI,CAAC1K,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB4K,aAAa,CAAC,IAAI,CAAC5K,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACjE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC6D,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEcqK,oBAAoBA,CAAA;IAAA,IAAAS,MAAA;IAAA,OAAAzB,iBAAA;MAChC,IAAI;QACF,IAAIyB,MAAI,CAAC9K,WAAW,CAACnF,MAAM,KAAK,CAAC,EAAE;UACjCiQ,MAAI,CAAC9L,YAAY,CAACqD,SAAS,CAAC,wBAAwB,CAAC;UACrDyI,MAAI,CAAChP,oBAAoB,EAAE;UAC3B;;QAGF,MAAMiP,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAAC9K,WAAW,EAAE;UAC3CiG,IAAI,EAAE;SACP,CAAC;QAEF,IAAI6E,MAAI,CAACjL,sBAAsB,GAAG,CAAC,EAAE;UACnCiL,MAAI,CAAC9L,YAAY,CAACqD,SAAS,CACzB,+CAA+C,CAChD;UACDyI,MAAI,CAAChP,oBAAoB,EAAE;UAC3B;;QAGF,MAAMmP,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACH,SAAS,CAAC,EAAE,SAAS1G,IAAI,CAACC,GAAG,EAAE,OAAO,EAAE;UAClE2B,IAAI,EAAE;SACP,CAAC;QAEF,MAAM6E,MAAI,CAACK,gBAAgB,CAACF,SAAS,CAAC;QACtCH,MAAI,CAAC9L,YAAY,CAACuL,WAAW,CAAC,sBAAsB,CAAC;OACtD,CAAC,OAAO7I,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7DoJ,MAAI,CAAC9L,YAAY,CAACqD,SAAS,CAAC,yCAAyC,CAAC;OACvE,SAAS;QACRyI,MAAI,CAAChL,mBAAmB,GAAG,MAAM;QACjCgL,MAAI,CAACjL,sBAAsB,GAAG,CAAC;QAC/BiL,MAAI,CAAC9K,WAAW,GAAG,EAAE;;IACtB;EACH;EAEcmL,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAA/B,iBAAA;MAC5C,MAAM5F,UAAU,GAAG2H,MAAI,CAACvW,gBAAgB,EAAEoB,EAAE,IAAImV,MAAI,CAACvW,gBAAgB,EAAEkN,GAAG;MAE1E,IAAI,CAAC0B,UAAU,EAAE;QACf,MAAM,IAAI4H,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCJ,MAAI,CAACrM,cAAc,CAAC3C,WAAW,CAC7BqH,UAAU,EACV,EAAE,EACFwH,SAAS,EACT,OAAO,EACPG,MAAI,CAAClM,YAAY,CAACjJ,EAAE,CACrB,CAACsM,SAAS,CAAC;UACVC,IAAI,EAAGmB,OAAY,IAAI;YACrByH,MAAI,CAAClQ,QAAQ,CAAC0I,IAAI,CAACD,OAAO,CAAC;YAC3ByH,MAAI,CAAC/H,cAAc,EAAE;YACrBkI,OAAO,EAAE;UACX,CAAC;UACD7J,KAAK,EAAGA,KAAU,IAAI;YACpBF,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChE8J,MAAM,CAAC9J,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA+J,uBAAuBA,CAACC,QAAgB;IACtC,MAAMC,OAAO,GAAGjH,IAAI,CAACC,KAAK,CAAC+G,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAME,OAAO,GAAGF,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGC,OAAO,IAAIC,OAAO,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEAC,cAAcA,CAACjF,KAAU;IACvB,MAAMwB,KAAK,GAAGxB,KAAK,CAACuB,MAAM,CAACC,KAAK;IAChC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACzN,MAAM,KAAK,CAAC,EAAE;IAElC,KAAK,IAAImR,IAAI,IAAI1D,KAAK,EAAE;MACtB9G,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuK,IAAI,CAAC1O,IAAI,CAAC;MACxC;;EAEJ;;EAEA2O,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAvN,aAAaA,CAAA;IACX,IAAI,CAACa,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,UAAU,GAAG,KAAK;EACzB;EAEAyM,WAAWA,CAAA;IACT,IAAI,CAACvL,aAAa,CAACwL,WAAW,EAAE;EAClC;;;uBA/0BWxN,oBAAoB,EAAAtK,EAAA,CAAA+X,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjY,EAAA,CAAA+X,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnY,EAAA,CAAA+X,iBAAA,CAAAK,EAAA,CAAA1N,cAAA,GAAA1K,EAAA,CAAA+X,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAtY,EAAA,CAAA+X,iBAAA,CAAA/X,EAAA,CAAAuY,iBAAA;IAAA;EAAA;;;YAApBjO,oBAAoB;MAAAkO,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCjBjC3Y,EAAA,CAAAE,cAAA,aAEC;UAOKF,EAAA,CAAAkB,UAAA,mBAAA2X,sDAAA;YAAA,OAASD,GAAA,CAAAlE,qBAAA,EAAuB;UAAA,EAAC;UAGjC1U,EAAA,CAAAC,SAAA,WAAkE;UACpED,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAA8C;UAMxCF,EAAA,CAAAkB,UAAA,mBAAA4X,mDAAA;YAAA,OAASF,GAAA,CAAAlX,eAAA,CAAAkX,GAAA,CAAApY,gBAAA,kBAAAoY,GAAA,CAAApY,gBAAA,CAAAoB,EAAA,CAAsC;UAAA,EAAC;UAJlD5B,EAAA,CAAAI,YAAA,EAKE;UACFJ,EAAA,CAAA4C,UAAA,IAAAmW,mCAAA,iBAGO;UACT/Y,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,aAA4B;UAExBF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAAsD;UACpDF,EAAA,CAAA4C,UAAA,KAAAoW,oCAAA,kBAkBM;UACNhZ,EAAA,CAAA4C,UAAA,KAAAqW,qCAAA,mBAMO;UACTjZ,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAAqC;UAEjCF,EAAA,CAAAkB,UAAA,mBAAAgY,uDAAA;YAAA,OAASN,GAAA,CAAA/D,cAAA,EAAgB;UAAA,EAAC;UAI1B7U,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAkB,UAAA,mBAAAiY,uDAAA;YAAA,OAASP,GAAA,CAAA9D,cAAA,EAAgB;UAAA,EAAC;UAI1B9U,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAMC;UALCF,EAAA,CAAAkB,UAAA,mBAAAkY,uDAAA;YAAA,OAASR,GAAA,CAAArF,YAAA,EAAc;UAAA,EAAC;UAMxBvT,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAkB,UAAA,mBAAAmY,uDAAA;YAAA,OAAST,GAAA,CAAApF,cAAA,EAAgB;UAAA,EAAC;UAI1BxT,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAE,cAAA,oBAIC;UADCF,EAAA,CAAAkB,UAAA,oBAAAoY,sDAAAvU,MAAA;YAAA,OAAU6T,GAAA,CAAA3F,QAAA,CAAAlO,MAAA,CAAgB;UAAA,EAAC;UAG3B/E,EAAA,CAAA4C,UAAA,KAAA2W,oCAAA,kBAUM;UAGNvZ,EAAA,CAAA4C,UAAA,KAAA4W,oCAAA,kBAaM;UAGNxZ,EAAA,CAAA4C,UAAA,KAAA6W,oCAAA,kBAyMM;UACRzZ,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAkB,UAAA,sBAAAwY,wDAAA;YAAA,OAAYd,GAAA,CAAA7Q,WAAA,EAAa;UAAA,EAAC;UAI1B/H,EAAA,CAAAE,cAAA,eAAwB;UAGpBF,EAAA,CAAAkB,UAAA,mBAAAyY,uDAAA;YAAA,OAASf,GAAA,CAAAlF,iBAAA,EAAmB;UAAA,EAAC;UAM7B1T,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAOC;UALCF,EAAA,CAAAkB,UAAA,mBAAA0Y,uDAAA;YAAA,OAAShB,GAAA,CAAAnF,oBAAA,EAAsB;UAAA,EAAC;UAMhCzT,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAoB;UAOhBF,EAAA,CAAAkB,UAAA,mBAAA2Y,yDAAA9U,MAAA;YAAA,OAAS6T,GAAA,CAAAjG,aAAA,CAAA5N,MAAA,CAAqB;UAAA,EAAC,qBAAA+U,2DAAA/U,MAAA;YAAA,OACpB6T,GAAA,CAAAhG,cAAA,CAAA7N,MAAA,CAAsB;UAAA,EADF,mBAAAgV,yDAAA;YAAA,OAEtBnB,GAAA,CAAA7F,YAAA,EAAc;UAAA,EAFQ,kBAAAiH,wDAAA;YAAA,OAGvBpB,GAAA,CAAA5F,WAAA,EAAa;UAAA,EAHU;UASjChT,EAAA,CAAAG,MAAA;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAIbJ,EAAA,CAAAE,cAAA,eAAwB;UAEtBF,EAAA,CAAA4C,UAAA,KAAAqX,uCAAA,qBAaS;UAGTja,EAAA,CAAA4C,UAAA,KAAAsX,uCAAA,qBAUS;UACXla,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAA4C,UAAA,KAAAuX,oCAAA,kBA6BM;UAGNna,EAAA,CAAA4C,UAAA,KAAAwX,oCAAA,mBA4DM;UAGNpa,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAkB,UAAA,oBAAAmZ,uDAAAtV,MAAA;YAAA,OAAU6T,GAAA,CAAAlB,cAAA,CAAA3S,MAAA,CAAsB;UAAA,EAAC;UAJnC/E,EAAA,CAAAI,YAAA,EAOE;UAGFJ,EAAA,CAAA4C,UAAA,KAAA0X,oCAAA,kBAIO;UACTta,EAAA,CAAAI,YAAA,EAAM;;;;;UAvgBIJ,EAAA,CAAAK,SAAA,GAAqE;UAArEL,EAAA,CAAA6B,UAAA,SAAA+W,GAAA,CAAApY,gBAAA,kBAAAoY,GAAA,CAAApY,gBAAA,CAAAsB,KAAA,yCAAA9B,EAAA,CAAA+B,aAAA,CAAqE,QAAA6W,GAAA,CAAApY,gBAAA,kBAAAoY,GAAA,CAAApY,gBAAA,CAAAK,QAAA;UAMpEb,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAA6B,UAAA,SAAA+W,GAAA,CAAApY,gBAAA,kBAAAoY,GAAA,CAAApY,gBAAA,CAAAC,QAAA,CAAgC;UAOjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAAsY,GAAA,CAAApY,gBAAA,kBAAAoY,GAAA,CAAApY,gBAAA,CAAAK,QAAA,wBACF;UAGKb,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAA6B,UAAA,SAAA+W,GAAA,CAAAxM,YAAA,CAAkB;UAkBdpM,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAA6B,UAAA,UAAA+W,GAAA,CAAAxM,YAAA,CAAmB;UA8B5BpM,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAAqE,WAAA,iBAAAuU,GAAA,CAAArN,UAAA,CAAiC,mBAAAqN,GAAA,CAAArN,UAAA;UAwBlCvL,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAA6B,UAAA,SAAA+W,GAAA,CAAA7N,SAAA,CAAe;UAaf/K,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAA6B,UAAA,UAAA+W,GAAA,CAAA7N,SAAA,IAAA6N,GAAA,CAAA/R,QAAA,CAAAL,MAAA,OAAyC;UAetCxG,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAA6B,UAAA,UAAA+W,GAAA,CAAA7N,SAAA,IAAA6N,GAAA,CAAA/R,QAAA,CAAAL,MAAA,KAAuC;UAiN3CxG,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAA6B,UAAA,cAAA+W,GAAA,CAAArM,WAAA,CAAyB;UAUrBvM,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAqE,WAAA,iBAAAuU,GAAA,CAAA1N,eAAA,CAAsC,mBAAA0N,GAAA,CAAA1N,eAAA;UAUtClL,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAqE,WAAA,iBAAAuU,GAAA,CAAAzN,kBAAA,CAAyC,mBAAAyN,GAAA,CAAAzN,kBAAA;UAezCnL,EAAA,CAAAK,SAAA,GAAsE;UAAtEL,EAAA,CAAA6B,UAAA,cAAA+W,GAAA,CAAApY,gBAAA,IAAAoY,GAAA,CAAAjR,gBAAA,IAAAiR,GAAA,CAAAzQ,gBAAA,CAAsE;UAiBrEnI,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAA6B,UAAA,YAAA0Y,QAAA,GAAA3B,GAAA,CAAArM,WAAA,CAAAwB,GAAA,8BAAAwM,QAAA,CAAArL,KAAA,kBAAAqL,QAAA,CAAArL,KAAA,CAAAC,IAAA,IAAgD;UAgBhDnP,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAA6B,UAAA,UAAA2Y,QAAA,GAAA5B,GAAA,CAAArM,WAAA,CAAAwB,GAAA,8BAAAyM,QAAA,CAAAtL,KAAA,kBAAAsL,QAAA,CAAAtL,KAAA,CAAAC,IAAA,GAA+C;UAgBrDnP,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAA6B,UAAA,SAAA+W,GAAA,CAAA1N,eAAA,CAAqB;UAgCrBlL,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAA6B,UAAA,SAAA+W,GAAA,CAAAzN,kBAAA,CAAwB;UAmEzBnL,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAA6B,UAAA,WAAA+W,GAAA,CAAAhB,kBAAA,GAA+B;UAM9B5X,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAA6B,UAAA,SAAA+W,GAAA,CAAA1N,eAAA,IAAA0N,GAAA,CAAAzN,kBAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}