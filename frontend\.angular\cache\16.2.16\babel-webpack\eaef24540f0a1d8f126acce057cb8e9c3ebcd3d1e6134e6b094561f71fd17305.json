{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { MessageType, CallType } from 'src/app/models/message.model';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/services/user-status.service\";\nimport * as i6 from \"src/app/services/toast.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.otherParticipant.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.otherParticipant.isOnline ? \"En ligne\" : ctx_r0.formatLastActive(ctx_r0.otherParticipant.lastActive), \" \");\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"animate-pulse\": a0\n  };\n};\nfunction MessageChatComponent_ng_container_7_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassMap(\"absolute -top-2 -right-2 min-w-[20px] h-5 px-1.5 text-white text-xs rounded-md flex items-center justify-center font-bold shadow-lg border border-white/20 \" + action_r13.badge.class);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c2, action_r13.badge.animate));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", action_r13.badge.count > 99 ? \"99+\" : action_r13.badge.count, \" \");\n  }\n}\nconst _c3 = function () {\n  return {};\n};\nfunction MessageChatComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_7_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const action_r13 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(action_r13.onClick());\n    });\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵtemplate(3, MessageChatComponent_ng_container_7_span_3_Template, 2, 6, \"span\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const action_r13 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"whatsapp-action-button \" + action_r13.class);\n    i0.ɵɵproperty(\"ngClass\", action_r13.activeClass && action_r13.isActive ? action_r13.activeClass : i0.ɵɵpureFunction0(7, _c3))(\"title\", action_r13.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(action_r13.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", action_r13.badge && action_r13.badge.count > 0);\n  }\n}\nfunction MessageChatComponent_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 39);\n  }\n}\nconst _c4 = function (a0) {\n  return {\n    \"bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20\": a0\n  };\n};\nfunction MessageChatComponent_div_12_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_12_button_8_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const status_r19 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      ctx_r20.updateUserStatus(status_r19.key);\n      return i0.ɵɵresetView(ctx_r20.toggleStatusSelector());\n    });\n    i0.ɵɵelementStart(1, \"div\", 48);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementStart(3, \"div\")(4, \"div\", 50);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const status_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r18.isUpdatingStatus)(\"ngClass\", i0.ɵɵpureFunction1(6, _c4, ctx_r18.currentUserStatus === status_r19.key));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(status_r19.icon + \" \" + status_r19.color + \" mr-3 text-xs\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(status_r19.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", status_r19.description, \" \");\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"div\", 42)(3, \"span\");\n    i0.ɵɵtext(4, \"Statut actuel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 44);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_12_button_8_Template, 8, 8, \"button\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 46)(10, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_12_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      ctx_r22.toggleUserStatusPanel();\n      return i0.ɵɵresetView(ctx_r22.toggleStatusSelector());\n    });\n    i0.ɵɵelementStart(11, \"div\", 48);\n    i0.ɵɵelement(12, \"i\", 49);\n    i0.ɵɵelementStart(13, \"div\")(14, \"div\", 50);\n    i0.ɵɵtext(15, \"Voir tous les utilisateurs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 51);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getStatusColor(ctx_r3.currentUserStatus));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getStatusText(ctx_r3.currentUserStatus), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getStatusOptions());\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getOnlineUsersCount(), \" en ligne \");\n  }\n}\nfunction MessageChatComponent_div_16_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 56);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_16_a_4_Template_a_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const theme_r25 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.changeTheme(theme_r25.key));\n    });\n    i0.ɵɵelementStart(1, \"div\", 48);\n    i0.ɵɵelement(2, \"div\", 10);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const theme_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"hover:bg-\" + theme_r25.hoverColor + \"/10 dark:hover:bg-\" + theme_r25.hoverColor + \"/10\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", \"w-4 h-4 rounded-full bg-\" + theme_r25.color + \" mr-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(theme_r25.label);\n  }\n}\nfunction MessageChatComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵtext(2, \" Choisir un th\\u00E8me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_16_a_4_Template, 5, 3, \"a\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getThemeOptions());\n  }\n}\nfunction MessageChatComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 54);\n    i0.ɵɵtext(2, \" Options de conversation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44)(4, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_20_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.clearConversation());\n    });\n    i0.ɵɵelementStart(5, \"div\", 48);\n    i0.ɵɵelement(6, \"i\", 58);\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \"Vider la conversation\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_20_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.exportConversation());\n    });\n    i0.ɵɵelementStart(10, \"div\", 48);\n    i0.ɵɵelement(11, \"i\", 59);\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13, \"Exporter la conversation\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_20_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.toggleConversationInfo());\n    });\n    i0.ɵɵelementStart(15, \"div\", 48);\n    i0.ɵɵelement(16, \"i\", 60);\n    i0.ɵɵelementStart(17, \"div\");\n    i0.ɵɵtext(18, \"Informations\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_20_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.toggleConversationSettings());\n    });\n    i0.ɵɵelementStart(20, \"div\", 48);\n    i0.ɵɵelement(21, \"i\", 61);\n    i0.ɵɵelementStart(22, \"div\");\n    i0.ɵɵtext(23, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵtext(2, \" Chargement... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"i\", 65);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun message pour le moment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 66);\n    i0.ɵɵtext(5, \"Commencez la conversation !\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c5 = function (a0, a1) {\n  return {\n    \"current-user\": a0,\n    \"other-user\": a1\n  };\n};\nfunction MessageChatComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68)(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r33 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c5, (message_r33.sender == null ? null : message_r33.sender.id) === ctx_r9.currentUserId, (message_r33.sender == null ? null : message_r33.sender.id) !== ctx_r9.currentUserId));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r33.content);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.formatMessageTime(message_r33.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune notification\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"p\", 73);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"small\", 74);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notification_r34 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r34.content);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.formatMessageTime(notification_r34.timestamp));\n  }\n}\nfunction MessageChatComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"input\", 77);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_39_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.searchQuery = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_39_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.toggleSearchBar());\n    });\n    i0.ɵɵelement(4, \"i\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r12.searchQuery);\n  }\n}\nconst _c6 = function (a0) {\n  return {\n    \"text-[#4f5fad] dark:text-[#6d78c9]\": a0\n  };\n};\nconst _c7 = function (a0) {\n  return {\n    open: a0\n  };\n};\nexport class MessageChatComponent {\n  // Getter pour compatibilité\n  get availableReactions() {\n    return this.c.reactions;\n  }\n  // Emojis du service\n  get commonEmojis() {\n    return this.MessageService.getCommonEmojis();\n  }\n  // Configuration des boutons d'action de l'en-tête\n  getHeaderActions() {\n    return [{\n      class: 'btn-audio-call',\n      icon: 'fas fa-phone-alt',\n      title: 'Appel audio',\n      onClick: () => this.initiateCall('AUDIO'),\n      isActive: false\n    }, {\n      class: 'btn-video-call',\n      icon: 'fas fa-video',\n      title: 'Appel vidéo',\n      onClick: () => this.initiateCall('VIDEO'),\n      isActive: false\n    }, {\n      class: 'btn-search',\n      icon: 'fas fa-search',\n      title: 'Rechercher',\n      onClick: () => this.toggleSearchBar(),\n      isActive: this.showSearchBar,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-pinned relative',\n      icon: 'fas fa-thumbtack',\n      title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n      onClick: () => this.togglePinnedMessages(),\n      isActive: this.showPinnedMessages,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.getPinnedMessagesCount() > 0 ? {\n        count: this.getPinnedMessagesCount(),\n        class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n        animate: false\n      } : null\n    }, {\n      class: 'btn-notifications relative',\n      icon: 'fas fa-bell',\n      title: 'Notifications',\n      onClick: () => this.toggleNotificationPanel(),\n      isActive: this.showNotificationPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.unreadNotificationCount > 0 ? {\n        count: this.unreadNotificationCount,\n        class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n        animate: true\n      } : null\n    }, {\n      class: 'btn-history relative',\n      icon: 'fas fa-history',\n      title: 'Historique des appels',\n      onClick: () => this.toggleCallHistoryPanel(),\n      isActive: this.showCallHistoryPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-stats relative',\n      icon: 'fas fa-chart-bar',\n      title: \"Statistiques d'appels\",\n      onClick: () => this.toggleCallStatsPanel(),\n      isActive: this.showCallStatsPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-voice-messages relative',\n      icon: 'fas fa-microphone',\n      title: 'Messages vocaux',\n      onClick: () => this.toggleVoiceMessagesPanel(),\n      isActive: this.showVoiceMessagesPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.voiceMessages.length > 0 ? {\n        count: this.voiceMessages.length,\n        class: 'bg-[#4f5fad]',\n        animate: false\n      } : null\n    }];\n  }\n  constructor(MessageService, route, authService, fb, statusService, router, toastService, cdr) {\n    this.MessageService = MessageService;\n    this.route = route;\n    this.authService = authService;\n    this.fb = fb;\n    this.statusService = statusService;\n    this.router = router;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    this.messages = [];\n    this.conversation = null;\n    this.loading = true;\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    this.selectedFile = null;\n    this.previewUrl = null;\n    this.isUploading = false;\n    this.isTyping = false;\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.MAX_MESSAGES_PER_SIDE = 5;\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.MAX_TOTAL_MESSAGES = 100;\n    this.currentPage = 1;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.subscriptions = new Subscription();\n    // Interface\n    this.selectedTheme = 'theme-default';\n    // États\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n    this.showEmojiPicker = false;\n    this.showSearchBar = false;\n    this.showPinnedMessages = false;\n    this.showStatusSelector = false;\n    this.showNotificationPanel = false;\n    this.showNotificationSettings = false;\n    this.showUserStatusPanel = false;\n    this.showCallHistoryPanel = false;\n    this.showCallStatsPanel = false;\n    this.showVoiceMessagesPanel = false;\n    // Appels\n    this.incomingCall = null;\n    this.activeCall = null;\n    this.showCallModal = false;\n    this.showActiveCallModal = false;\n    this.isCallMuted = false;\n    this.isVideoEnabled = true;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // Notifications et messages\n    this.notifications = [];\n    this.unreadNotificationCount = 0;\n    this.selectedNotifications = new Set();\n    this.showDeleteConfirmModal = false;\n    this.editingMessageId = null;\n    this.editingContent = '';\n    this.replyingToMessage = null;\n    // Recherche\n    this.searchQuery = '';\n    this.isSearching = false;\n    this.searchMode = false;\n    this.searchResults = [];\n    // Panneaux\n    this.pinnedMessages = [];\n    this.showReactionPicker = {};\n    this.showDeleteConfirm = {};\n    this.showPinConfirm = {};\n    this.isPinning = {};\n    this.showMessageOptions = {};\n    // Variables de transfert\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n    this.availableConversations = [];\n    this.isForwarding = false;\n    this.isLoadingConversations = false;\n    // Constantes optimisées\n    this.c = {\n      reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n      delays: {\n        away: 300000,\n        typing: 3000,\n        scroll: 100,\n        anim: 300\n      },\n      error: 'Erreur. Veuillez réessayer.',\n      trackById: (i, item) => item?.id || i.toString(),\n      notifications: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      status: {\n        online: {\n          text: 'En ligne',\n          color: 'text-green-500',\n          icon: 'fas fa-circle',\n          label: 'En ligne',\n          description: 'Disponible pour discuter'\n        },\n        offline: {\n          text: 'Hors ligne',\n          color: 'text-gray-500',\n          icon: 'far fa-circle',\n          label: 'Hors ligne',\n          description: 'Invisible pour tous'\n        },\n        away: {\n          text: 'Absent',\n          color: 'text-yellow-500',\n          icon: 'fas fa-clock',\n          label: 'Absent',\n          description: 'Absent temporairement'\n        },\n        busy: {\n          text: 'Occupé',\n          color: 'text-red-500',\n          icon: 'fas fa-minus-circle',\n          label: 'Occupé',\n          description: 'Ne pas déranger'\n        }\n      },\n      themes: [{\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad',\n        hoverColor: '#4f5fad'\n      }, {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d',\n        hoverColor: '#ff6b9d'\n      }, {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6',\n        hoverColor: '#3d85c6'\n      }, {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f',\n        hoverColor: '#6aa84f'\n      }],\n      calls: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      },\n      // Alias pour compatibilité\n      notificationConfig: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      callStatusColors: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      }\n    };\n    // Variables de notification\n    this.notificationFilter = 'all';\n    this.isLoadingNotifications = false;\n    this.isMarkingAsRead = false;\n    this.isDeletingNotifications = false;\n    this.hasMoreNotifications = false;\n    this.notificationSounds = true;\n    this.notificationPreview = true;\n    this.autoMarkAsRead = true;\n    // Variables d'appel\n    this.isCallMinimized = false;\n    this.callQuality = 'connecting';\n    this.showCallControls = false;\n    // Variables de statut utilisateur\n    this.onlineUsers = new Map();\n    this.currentUserStatus = 'online';\n    this.lastActivityTime = new Date();\n    this.autoAwayTimeout = null;\n    this.isUpdatingStatus = false;\n    this.callHistory = [];\n    this.voiceMessages = [];\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.statusFilterType = 'all';\n    // Services de fichiers optimisés\n    this.f = {\n      getIcon: t => this.MessageService.getFileIcon(t),\n      getType: t => this.MessageService.getFileType(t)\n    };\n    this.getFileIcon = this.f.getIcon;\n    this.getFileType = this.f.getType;\n    this.isCurrentlyTyping = false;\n    this.TYPING_DELAY = 500;\n    this.TYPING_TIMEOUT = 3000;\n    // Méthodes de basculement principales consolidées\n    this.mainToggleMethods = {\n      themeSelector: () => this.togglePanel('theme'),\n      mainMenu: () => this.togglePanel('menu'),\n      emojiPicker: () => this.togglePanel('emoji')\n    };\n    this.toggleThemeSelector = this.mainToggleMethods.themeSelector;\n    this.toggleMainMenu = this.mainToggleMethods.mainMenu;\n    this.toggleEmojiPicker = this.mainToggleMethods.emojiPicker;\n    // Méthodes toggle consolidées\n    this.toggleMethods = {\n      pinnedMessages: () => this.showPinnedMessages = !this.showPinnedMessages,\n      searchBar: () => {\n        this.togglePanel('search');\n        if (!this.showSearchBar) this.clearSearch();\n      },\n      statusSelector: () => this.togglePanel('status'),\n      notificationSettings: () => this.showNotificationSettings = !this.showNotificationSettings,\n      userStatusPanel: () => this.showUserStatusPanel = !this.showUserStatusPanel,\n      callMinimize: () => this.isCallMinimized = !this.isCallMinimized,\n      callHistoryPanel: () => this.showCallHistoryPanel = !this.showCallHistoryPanel,\n      callStatsPanel: () => this.showCallStatsPanel = !this.showCallStatsPanel,\n      voiceMessagesPanel: () => this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel\n    };\n    this.togglePinnedMessages = this.toggleMethods.pinnedMessages;\n    this.toggleSearchBar = this.toggleMethods.searchBar;\n    this.toggleStatusSelector = this.toggleMethods.statusSelector;\n    this.toggleNotificationSettings = this.toggleMethods.notificationSettings;\n    this.toggleUserStatusPanel = this.toggleMethods.userStatusPanel;\n    this.toggleCallMinimize = this.toggleMethods.callMinimize;\n    this.toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;\n    this.toggleCallStatsPanel = this.toggleMethods.callStatsPanel;\n    this.toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;\n    this.conversationMethods = {\n      showInfo: () => this.showDevelopmentFeature('Informations de conversation'),\n      showSettings: () => this.showDevelopmentFeature('Paramètres de conversation'),\n      clear: () => {\n        if (!this.conversation?.id || this.messages.length === 0) {\n          this.toastService.showWarning('Aucune conversation à vider');\n          return;\n        }\n        if (confirm('Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.')) {\n          this.messages = [];\n          this.showMainMenu = false;\n          this.toastService.showSuccess('Conversation vidée avec succès');\n        }\n      },\n      export: () => {\n        if (!this.conversation?.id || this.messages.length === 0) {\n          this.toastService.showWarning('Aucune conversation à exporter');\n          return;\n        }\n        const conversationName = this.conversation.isGroup ? this.conversation.groupName || 'Groupe sans nom' : this.otherParticipant?.username || 'Conversation privée';\n        const exportData = {\n          conversation: {\n            id: this.conversation.id,\n            name: conversationName,\n            isGroup: this.conversation.isGroup,\n            participants: this.conversation.participants,\n            createdAt: this.conversation.createdAt\n          },\n          messages: this.messages.map(msg => ({\n            id: msg.id,\n            content: msg.content,\n            sender: msg.sender,\n            timestamp: msg.timestamp,\n            type: msg.type\n          })),\n          exportedAt: new Date().toISOString(),\n          exportedBy: this.currentUserId\n        };\n        const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n          type: 'application/json'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        const safeFileName = conversationName.replace(/[^a-z0-9]/gi, '_').toLowerCase();\n        const dateStr = new Date().toISOString().split('T')[0];\n        link.download = `conversation-${safeFileName}-${dateStr}.json`;\n        link.click();\n        window.URL.revokeObjectURL(url);\n        this.showMainMenu = false;\n        this.toastService.showSuccess('Conversation exportée avec succès');\n      }\n    };\n    // Template methods\n    this.toggleConversationInfo = this.conversationMethods.showInfo;\n    this.toggleConversationSettings = this.conversationMethods.showSettings;\n    this.clearConversation = this.conversationMethods.clear;\n    this.exportConversation = this.conversationMethods.export;\n    // Service - méthodes optimisées\n    this.s = {\n      formatTime: t => this.MessageService.formatMessageTime(t),\n      formatActive: t => this.MessageService.formatLastActive(t),\n      formatDate: t => this.MessageService.formatMessageDate(t),\n      showDateHeader: i => this.MessageService.shouldShowDateHeader(this.messages, i),\n      getType: m => this.MessageService.getMessageType(m),\n      hasImage: m => this.MessageService.hasImage(m),\n      isVoice: m => this.MessageService.isVoiceMessage(m),\n      getVoiceUrl: m => this.MessageService.getVoiceMessageUrl(m),\n      getVoiceDuration: m => this.MessageService.getVoiceMessageDuration(m),\n      getVoiceHeight: i => this.MessageService.getVoiceBarHeight(i),\n      formatVoice: s => this.MessageService.formatVoiceDuration(s),\n      getImageUrl: m => this.MessageService.getImageUrl(m),\n      getTypeClass: m => this.MessageService.getMessageTypeClass(m, this.currentUserId)\n    };\n    // Méthodes exposées optimisées\n    this.formatMessageTime = this.s.formatTime;\n    this.formatLastActive = this.s.formatActive;\n    this.formatMessageDate = this.s.formatDate;\n    this.shouldShowDateHeader = this.s.showDateHeader;\n    this.getMessageType = this.s.getType;\n    this.hasImage = this.s.hasImage;\n    this.isVoiceMessage = this.s.isVoice;\n    this.getVoiceMessageUrl = this.s.getVoiceUrl;\n    this.getVoiceMessageDuration = this.s.getVoiceDuration;\n    this.getVoiceBarHeight = this.s.getVoiceHeight;\n    this.formatVoiceDuration = this.s.formatVoice;\n    this.getImageUrl = this.s.getImageUrl;\n    this.getMessageTypeClass = this.s.getTypeClass;\n    // Indicateurs de chargement consolidés\n    this.loadingIndicatorMethods = {\n      show: () => {\n        if (!document.getElementById('message-loading-indicator')) {\n          const indicator = document.createElement('div');\n          indicator.id = 'message-loading-indicator';\n          indicator.className = 'text-center py-2 text-gray-500 text-sm';\n          indicator.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n          this.messagesContainer?.nativeElement?.prepend(indicator);\n        }\n      },\n      hide: () => {\n        const indicator = document.getElementById('message-loading-indicator');\n        indicator?.parentNode?.removeChild(indicator);\n      }\n    };\n    this.showLoadingIndicator = this.loadingIndicatorMethods.show;\n    this.hideLoadingIndicator = this.loadingIndicatorMethods.hide;\n    // Méthodes d'enregistrement vocal consolidées\n    this.voiceRecordingMethods = {\n      toggle: () => {\n        this.isRecordingVoice = !this.isRecordingVoice;\n        if (!this.isRecordingVoice) this.voiceRecordingDuration = 0;\n      },\n      complete: audioBlob => {\n        if (!this.conversation?.id && !this.otherParticipant?.id) {\n          this.toastService.showError('No conversation or recipient selected');\n          this.isRecordingVoice = false;\n          return;\n        }\n        const receiverId = this.otherParticipant?.id || '';\n        this.MessageService.sendVoiceMessage(receiverId, audioBlob, this.conversation?.id, this.voiceRecordingDuration).subscribe({\n          next: message => {\n            this.isRecordingVoice = false;\n            this.voiceRecordingDuration = 0;\n            this.scrollToBottom(true);\n          },\n          error: error => {\n            this.toastService.showError('Failed to send voice message');\n            this.isRecordingVoice = false;\n          }\n        });\n      },\n      cancel: () => {\n        this.isRecordingVoice = false;\n        this.voiceRecordingDuration = 0;\n      }\n    };\n    this.toggleVoiceRecording = this.voiceRecordingMethods.toggle;\n    this.onVoiceRecordingComplete = this.voiceRecordingMethods.complete;\n    this.onVoiceRecordingCancelled = this.voiceRecordingMethods.cancel;\n    // Méthodes d'appel consolidées\n    this.callMethods = {\n      initiate: type => {\n        if (!this.otherParticipant?.id) return;\n        this.MessageService.initiateCall(this.otherParticipant.id, type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO, this.conversation?.id).subscribe({\n          next: call => {},\n          error: () => this.toastService.showError(this.c.error)\n        });\n      },\n      accept: () => {\n        if (!this.incomingCall) return;\n        this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n          next: call => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n            this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n            this.callQuality = 'connecting';\n            this.toastService.showSuccess('Appel connecté');\n          },\n          error: () => {\n            this.toastService.showError(this.c.error);\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        });\n      },\n      reject: () => {\n        if (!this.incomingCall) return;\n        this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n          next: call => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          },\n          error: error => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        });\n      },\n      end: () => {\n        const sub = this.MessageService.activeCall$.subscribe(call => {\n          if (call) {\n            this.MessageService.endCall(call.id).subscribe({\n              next: call => {},\n              error: error => {}\n            });\n          }\n        });\n        sub.unsubscribe();\n      }\n    };\n    this.initiateCall = this.callMethods.initiate;\n    this.acceptCall = this.callMethods.accept;\n    this.rejectCall = this.callMethods.reject;\n    this.endCall = this.callMethods.end;\n    // Méthodes de contrôle d'appel consolidées\n    this.callControlMethods = {\n      toggleMute: () => {\n        this.isCallMuted = !this.isCallMuted;\n        this.callControlMethods.updateMedia();\n        this.toastService.showInfo(this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé');\n      },\n      toggleVideo: () => {\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.callControlMethods.updateMedia();\n        this.toastService.showInfo(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      updateMedia: () => {\n        this.MessageService.toggleMedia(this.activeCall?.id, !this.isCallMuted, this.isVideoEnabled).subscribe({\n          next: () => {},\n          error: error => console.error('Erreur lors de la mise à jour des médias:', error)\n        });\n      }\n    };\n    this.toggleCallMute = this.callControlMethods.toggleMute;\n    this.toggleCallVideo = this.callControlMethods.toggleVideo;\n    this.updateCallMedia = this.callControlMethods.updateMedia;\n    // Méthodes de timer consolidées\n    this.timerMethods = {\n      startCallTimer: () => {\n        this.callDuration = 0;\n        this.callTimer = setInterval(() => {\n          this.callDuration++;\n          if (this.callDuration === 3 && this.callQuality === 'connecting') {\n            this.callQuality = 'excellent';\n          }\n        }, 1000);\n      },\n      stopCallTimer: () => {\n        if (this.callTimer) {\n          clearInterval(this.callTimer);\n          this.callTimer = null;\n        }\n      },\n      resetCallState: () => this.callDuration = 0\n    };\n    this.startCallTimerMethod = this.timerMethods.startCallTimer;\n    this.stopCallTimerMethod = this.timerMethods.stopCallTimer;\n    this.resetCallStateMethod = this.timerMethods.resetCallState;\n    // Notifications\n    // Méthode de basculement de notification consolidée\n    this.notificationToggleMethod = {\n      togglePanel: () => {\n        this.togglePanel('notification');\n        if (this.showNotificationPanel) {\n          this.loadNotifications();\n        }\n      }\n    };\n    this.toggleNotificationPanel = this.notificationToggleMethod.togglePanel;\n    // Méthodes de notification consolidées\n    this.notificationMethods = {\n      loadMore: () => this.loadNotifications(),\n      updateCount: () => this.unreadNotificationCount = this.notifications.filter(n => !n.isRead).length,\n      getFiltered: () => this.notifications,\n      toggleSelection: notificationId => {\n        if (this.selectedNotifications.has(notificationId)) {\n          this.selectedNotifications.delete(notificationId);\n        } else {\n          this.selectedNotifications.add(notificationId);\n        }\n      },\n      toggleSelectAll: () => {\n        const filteredNotifications = this.notificationMethods.getFiltered();\n        const allSelected = filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n        if (allSelected) {\n          filteredNotifications.forEach(n => this.selectedNotifications.delete(n.id));\n        } else {\n          filteredNotifications.forEach(n => this.selectedNotifications.add(n.id));\n        }\n      },\n      areAllSelected: () => {\n        const filteredNotifications = this.notificationMethods.getFiltered();\n        return filteredNotifications.length > 0 && filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n      }\n    };\n    this.loadMoreNotifications = this.notificationMethods.loadMore;\n    this.updateNotificationCount = this.notificationMethods.updateCount;\n    this.getFilteredNotifications = this.notificationMethods.getFiltered;\n    this.toggleNotificationSelection = this.notificationMethods.toggleSelection;\n    this.toggleSelectAllNotifications = this.notificationMethods.toggleSelectAll;\n    this.areAllNotificationsSelected = this.notificationMethods.areAllSelected;\n    // Méthodes de marquage consolidées\n    this.markingMethods = {\n      markSelected: () => {\n        const selectedIds = Array.from(this.selectedNotifications);\n        if (selectedIds.length === 0) {\n          this.toastService.showWarning('Aucune notification sélectionnée');\n          return;\n        }\n        this.markingMethods.markAsRead(selectedIds, () => {\n          this.selectedNotifications.clear();\n          this.toastService.showSuccess(`${selectedIds.length} notification(s) marquée(s) comme lue(s)`);\n        });\n      },\n      markAll: () => {\n        const unreadNotifications = this.notifications.filter(n => !n.isRead);\n        if (unreadNotifications.length === 0) {\n          this.toastService.showInfo('Aucune notification non lue');\n          return;\n        }\n        const unreadIds = unreadNotifications.map(n => n.id);\n        this.markingMethods.markAsRead(unreadIds, () => {\n          this.toastService.showSuccess('Toutes les notifications ont été marquées comme lues');\n        });\n      },\n      markAsRead: (ids, onSuccess) => {\n        const markSub = this.MessageService.markAsRead(ids).subscribe({\n          next: result => {\n            this.notifications = this.notifications.map(n => ids.includes(n.id) ? {\n              ...n,\n              isRead: true,\n              readAt: new Date()\n            } : n);\n            this.updateNotificationCount();\n            onSuccess();\n          },\n          error: error => {\n            this.toastService.showError('Erreur lors du marquage des notifications');\n          }\n        });\n        this.subscriptions.add(markSub);\n      }\n    };\n    this.markSelectedAsRead = this.markingMethods.markSelected;\n    this.markAllAsRead = this.markingMethods.markAll;\n    // Méthodes de suppression de notifications consolidées\n    this.notificationDeleteMethods = {\n      showDeleteSelectedConfirmation: () => {\n        if (this.selectedNotifications.size === 0) {\n          this.toastService.showWarning('Aucune notification sélectionnée');\n          return;\n        }\n        this.showDeleteConfirmModal = true;\n      },\n      deleteSelected: () => {\n        const selectedIds = Array.from(this.selectedNotifications);\n        if (selectedIds.length === 0) return;\n        this.isDeletingNotifications = true;\n        this.showDeleteConfirmModal = false;\n        const deleteSub = this.MessageService.deleteMultipleNotifications(selectedIds).subscribe({\n          next: result => {\n            this.notifications = this.notifications.filter(n => !selectedIds.includes(n.id));\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(`${result.count} notification(s) supprimée(s)`);\n          },\n          error: error => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError('Erreur lors de la suppression des notifications');\n          }\n        });\n        this.subscriptions.add(deleteSub);\n      },\n      deleteOne: notificationId => {\n        const deleteSub = this.MessageService.deleteNotification(notificationId).subscribe({\n          next: result => {\n            this.notifications = this.notifications.filter(n => n.id !== notificationId);\n            this.selectedNotifications.delete(notificationId);\n            this.updateNotificationCount();\n            this.toastService.showSuccess('Notification supprimée');\n          },\n          error: error => {\n            this.toastService.showError('Erreur lors de la suppression de la notification');\n          }\n        });\n        this.subscriptions.add(deleteSub);\n      },\n      deleteAll: () => {\n        if (this.notifications.length === 0) return;\n        if (!confirm('Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.')) {\n          return;\n        }\n        this.isDeletingNotifications = true;\n        const deleteAllSub = this.MessageService.deleteAllNotifications().subscribe({\n          next: result => {\n            this.notifications = [];\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(`${result.count} notifications supprimées avec succès`);\n          },\n          error: error => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError('Erreur lors de la suppression de toutes les notifications');\n          }\n        });\n        this.subscriptions.add(deleteAllSub);\n      },\n      cancel: () => this.showDeleteConfirmModal = false\n    };\n    this.showDeleteSelectedConfirmation = this.notificationDeleteMethods.showDeleteSelectedConfirmation;\n    this.deleteSelectedNotifications = this.notificationDeleteMethods.deleteSelected;\n    this.deleteNotification = this.notificationDeleteMethods.deleteOne;\n    this.deleteAllNotifications = this.notificationDeleteMethods.deleteAll;\n    this.cancelDeleteNotifications = this.notificationDeleteMethods.cancel;\n    // Méthodes utilitaires de notification consolidées\n    this.notificationUtilMethods = {\n      formatDate: date => this.MessageService.formatLastActive(date),\n      getIcon: type => this.c.notifications[type]?.icon || 'fas fa-bell',\n      getColor: type => this.c.notifications[type]?.color || 'text-cyan-500',\n      trackById: (index, notification) => this.c.trackById(0, notification)\n    };\n    this.formatNotificationDate = this.notificationUtilMethods.formatDate;\n    this.getNotificationIcon = this.notificationUtilMethods.getIcon;\n    this.getNotificationColor = this.notificationUtilMethods.getColor;\n    this.trackByNotificationId = this.notificationUtilMethods.trackById;\n    // Méthodes de gestion des panneaux consolidées\n    this.panelMethods = {\n      getActivePanels: () => {\n        const panels = [];\n        if (this.showUserStatusPanel) panels.push({\n          key: 'userStatus',\n          title: 'Utilisateurs',\n          icon: 'fas fa-users',\n          closeAction: () => this.showUserStatusPanel = false\n        });\n        if (this.showCallHistoryPanel) panels.push({\n          key: 'callHistory',\n          title: 'Historique des appels',\n          icon: 'fas fa-history',\n          closeAction: () => this.showCallHistoryPanel = false\n        });\n        if (this.showCallStatsPanel) panels.push({\n          key: 'callStats',\n          title: \"Statistiques d'appels\",\n          icon: 'fas fa-chart-bar',\n          closeAction: () => this.showCallStatsPanel = false\n        });\n        if (this.showVoiceMessagesPanel) panels.push({\n          key: 'voiceMessages',\n          title: 'Messages vocaux',\n          icon: 'fas fa-microphone',\n          closeAction: () => this.showVoiceMessagesPanel = false\n        });\n        return panels;\n      },\n      getStatusOptions: () => Object.entries(this.c.status).map(([key, config]) => ({\n        key,\n        ...config\n      })),\n      getThemeOptions: () => this.c.themes\n    };\n    this.getActivePanels = this.panelMethods.getActivePanels;\n    this.getStatusOptions = this.panelMethods.getStatusOptions;\n    this.getThemeOptions = this.panelMethods.getThemeOptions;\n    // Méthodes de statut simplifiées\n    this.statusMethods = {\n      getText: status => this.c.status[status]?.text || 'Inconnu',\n      getColor: status => this.c.status[status]?.color || 'text-gray-400',\n      getIcon: status => this.c.status[status]?.icon || 'fas fa-question-circle'\n    };\n    this.getStatusText = this.statusMethods.getText;\n    this.getStatusColor = this.statusMethods.getColor;\n    this.getStatusIcon = this.statusMethods.getIcon;\n    // Méthodes utilitaires consolidées\n    this.utilityMethods = {\n      formatLastSeen: lastActive => lastActive ? this.MessageService.formatLastActive(lastActive) : 'Jamais vu',\n      getOnlineUsersCount: () => Array.from(this.onlineUsers.values()).filter(user => user.isOnline).length,\n      getFilteredUsers: () => Array.from(this.onlineUsers.values()),\n      setStatusFilter: filter => this.statusFilterType = filter\n    };\n    this.formatLastSeen = this.utilityMethods.formatLastSeen;\n    this.getOnlineUsersCount = this.utilityMethods.getOnlineUsersCount;\n    this.setStatusFilter = this.utilityMethods.setStatusFilter;\n    this.getFilteredUsers = this.utilityMethods.getFilteredUsers;\n    // Méthodes de réponse et transfert consolidées\n    this.replyForwardMethods = {\n      startReply: message => this.replyingToMessage = message,\n      cancelReply: () => this.replyingToMessage = null,\n      openForwardModal: message => {\n        this.forwardingMessage = message;\n        this.showForwardModal = true;\n      },\n      closeForwardModal: () => {\n        this.showForwardModal = false;\n        this.forwardingMessage = null;\n        this.selectedConversations = [];\n      }\n    };\n    this.startReplyToMessage = this.replyForwardMethods.startReply;\n    this.cancelReply = this.replyForwardMethods.cancelReply;\n    this.openForwardModal = this.replyForwardMethods.openForwardModal;\n    this.closeForwardModal = this.replyForwardMethods.closeForwardModal;\n    // Messages - méthodes consolidées\n    this.messageMethods = {\n      getPinIcon: message => this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack',\n      getPinDisplayText: message => this.isMessagePinned(message) ? 'Désépingler' : 'Épingler',\n      canEditMessage: message => message.sender?.id === this.currentUserId,\n      isMessagePinned: message => message.isPinned || false\n    };\n    this.getPinIcon = this.messageMethods.getPinIcon;\n    this.getPinDisplayText = this.messageMethods.getPinDisplayText;\n    this.canEditMessage = this.messageMethods.canEditMessage;\n    this.isMessagePinned = this.messageMethods.isMessagePinned;\n    // Méthodes d'édition consolidées\n    this.editMethods = {\n      startEditMessage: message => {\n        this.editingMessageId = message.id;\n        this.editingContent = message.content;\n      },\n      cancelEditMessage: () => {\n        this.editingMessageId = null;\n        this.editingContent = '';\n      },\n      saveEditMessage: messageId => this.editMethods.cancelEditMessage(),\n      onEditKeyPress: (event, messageId) => {\n        if (event.key === 'Enter' && !event.shiftKey) {\n          event.preventDefault();\n          this.editMethods.saveEditMessage(messageId);\n        } else if (event.key === 'Escape') {\n          this.editMethods.cancelEditMessage();\n        }\n      }\n    };\n    this.startEditMessage = this.editMethods.startEditMessage;\n    this.cancelEditMessage = this.editMethods.cancelEditMessage;\n    this.saveEditMessage = this.editMethods.saveEditMessage;\n    this.onEditKeyPress = this.editMethods.onEditKeyPress;\n    // Utilitaires d'appel consolidées\n    this.callUtilities = {\n      getCallStatusColor: status => this.c.callStatusColors[status] || 'text-gray-500',\n      getCallTypeIcon: type => type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',\n      formatCallDuration: duration => {\n        if (!duration) return '00:00';\n        const minutes = Math.floor(duration / 60);\n        const seconds = duration % 60;\n        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      },\n      formatCallDate: timestamp => this.MessageService.formatMessageDate(timestamp)\n    };\n    this.getCallStatusColor = this.callUtilities.getCallStatusColor;\n    this.getCallTypeIcon = this.callUtilities.getCallTypeIcon;\n    this.formatCallDuration = this.callUtilities.formatCallDuration;\n    this.formatCallDate = this.callUtilities.formatCallDate;\n    // Méthodes d'événements consolidées\n    this.eventMethods = {\n      onDocumentClick: event => {\n        const target = event.target;\n        const closeConfigs = [{\n          selectors: ['.theme-selector-menu', '.btn-theme'],\n          property: 'showThemeSelector'\n        }, {\n          selectors: ['.emoji-picker', '.btn-emoji'],\n          property: 'showEmojiPicker'\n        }];\n        closeConfigs.forEach(config => {\n          const isClickOutside = !config.selectors.some(selector => target.closest(selector));\n          if (isClickOutside) {\n            this[config.property] = false;\n          }\n        });\n      },\n      confirmDeleteMessage: messageId => {\n        this.showDeleteConfirm[messageId] = false;\n      }\n    };\n    this.onDocumentClick = this.eventMethods.onDocumentClick;\n    this.confirmDeleteMessage = this.eventMethods.confirmDeleteMessage;\n    // Méthodes de réaction consolidées\n    this.reactionMethods = {\n      getUniqueReactions: message => message.reactions || [],\n      onReactionClick: (messageId, emoji) => {\n        // Implémentation des réactions\n      },\n      hasUserReacted: (message, emoji) => false\n    };\n    this.getUniqueReactions = this.reactionMethods.getUniqueReactions;\n    this.onReactionClick = this.reactionMethods.onReactionClick;\n    this.hasUserReacted = this.reactionMethods.hasUserReacted;\n    // Méthodes de conversation consolidées\n    this.conversationSelectionMethods = {\n      areAllSelected: () => this.selectedConversations.length === this.availableConversations.length,\n      selectAll: () => this.selectedConversations = this.availableConversations.map(c => c.id),\n      deselectAll: () => this.selectedConversations = [],\n      toggle: conversationId => {\n        const index = this.selectedConversations.indexOf(conversationId);\n        index > -1 ? this.selectedConversations.splice(index, 1) : this.selectedConversations.push(conversationId);\n      },\n      isSelected: conversationId => this.selectedConversations.includes(conversationId),\n      getDisplayImage: conversation => conversation.image || 'assets/images/default-avatar.png',\n      getDisplayName: conversation => conversation.name || 'Conversation',\n      forwardMessage: () => {\n        this.isForwarding = true;\n        setTimeout(() => {\n          this.isForwarding = false;\n          this.closeForwardModal();\n        }, 1000);\n      }\n    };\n    this.areAllConversationsSelected = this.conversationSelectionMethods.areAllSelected;\n    this.selectAllConversations = this.conversationSelectionMethods.selectAll;\n    this.deselectAllConversations = this.conversationSelectionMethods.deselectAll;\n    this.toggleConversationSelection = this.conversationSelectionMethods.toggle;\n    this.isConversationSelected = this.conversationSelectionMethods.isSelected;\n    this.getConversationDisplayImage = this.conversationSelectionMethods.getDisplayImage;\n    this.getConversationDisplayName = this.conversationSelectionMethods.getDisplayName;\n    this.forwardMessage = this.conversationSelectionMethods.forwardMessage;\n    // Méthodes de notification simplifiées consolidées\n    this.simpleNotificationMethods = {\n      onCallMouseMove: () => this.showCallControls = true,\n      saveNotificationSettings: () => {},\n      setNotificationFilter: filter => this.notificationFilter = filter\n    };\n    this.onCallMouseMove = this.simpleNotificationMethods.onCallMouseMove;\n    this.saveNotificationSettings = this.simpleNotificationMethods.saveNotificationSettings;\n    this.setNotificationFilter = this.simpleNotificationMethods.setNotificationFilter;\n    // Méthodes de recherche consolidées\n    this.searchMethods = {\n      onInput: event => {\n        this.searchQuery = event.target.value;\n        this.searchQuery.length >= 2 ? this.searchMethods.perform() : this.searchMethods.clear();\n      },\n      onKeyPress: event => {\n        if (event.key === 'Enter') {\n          this.searchMethods.perform();\n        } else if (event.key === 'Escape') {\n          this.searchMethods.clear();\n        }\n      },\n      perform: () => {\n        this.isSearching = true;\n        this.searchMode = true;\n        setTimeout(() => {\n          this.searchResults = this.messages.filter(m => m.content?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n          this.isSearching = false;\n        }, 500);\n      },\n      clear: () => {\n        this.searchQuery = '';\n        this.searchResults = [];\n        this.isSearching = false;\n        this.searchMode = false;\n      }\n    };\n    this.onSearchInput = this.searchMethods.onInput;\n    this.onSearchKeyPress = this.searchMethods.onKeyPress;\n    this.performSearch = this.searchMethods.perform;\n    this.clearSearch = this.searchMethods.clear;\n    // Méthodes utilitaires finales consolidées\n    this.finalUtilityMethods = {\n      navigateToMessage: messageId => {\n        // Navigation vers un message spécifique\n      },\n      scrollToPinnedMessage: messageId => {\n        // Défilement vers un message épinglé\n      },\n      getPinnedMessagesCount: () => this.pinnedMessages.length\n    };\n    this.navigateToMessage = this.finalUtilityMethods.navigateToMessage;\n    this.scrollToPinnedMessage = this.finalUtilityMethods.scrollToPinnedMessage;\n    this.getPinnedMessagesCount = this.finalUtilityMethods.getPinnedMessagesCount;\n    // Méthodes de recherche et réaction consolidées\n    this.searchAndReactionMethods = {\n      highlightSearchTerms: (content, query) => {\n        if (!query) return content;\n        const regex = new RegExp(`(${query})`, 'gi');\n        return content.replace(regex, '<mark>$1</mark>');\n      },\n      toggleReactionPicker: messageId => this.showReactionPicker[messageId] = !this.showReactionPicker[messageId],\n      reactToMessage: (messageId, emoji) => this.showReactionPicker[messageId] = false,\n      toggleMessageOptions: messageId => this.showMessageOptions[messageId] = !this.showMessageOptions[messageId]\n    };\n    this.highlightSearchTerms = this.searchAndReactionMethods.highlightSearchTerms;\n    this.toggleReactionPicker = this.searchAndReactionMethods.toggleReactionPicker;\n    this.reactToMessage = this.searchAndReactionMethods.reactToMessage;\n    this.toggleMessageOptions = this.searchAndReactionMethods.toggleMessageOptions;\n    // Confirmations consolidées\n    this.confirmationMethods = {\n      showPinConfirmation: messageId => this.showPinConfirm[messageId] = true,\n      cancelPinConfirmation: messageId => this.showPinConfirm[messageId] = false,\n      showDeleteConfirmation: messageId => this.showDeleteConfirm[messageId] = true,\n      cancelDeleteMessage: messageId => this.showDeleteConfirm[messageId] = false\n    };\n    this.showPinConfirmation = this.confirmationMethods.showPinConfirmation;\n    this.cancelPinConfirmation = this.confirmationMethods.cancelPinConfirmation;\n    this.showDeleteConfirmation = this.confirmationMethods.showDeleteConfirmation;\n    this.cancelDeleteMessage = this.confirmationMethods.cancelDeleteMessage;\n    // Méthodes de nettoyage optimisées\n    this.cleanup = {\n      clearTimeouts: () => [this.typingTimeout, this.autoAwayTimeout, this.callTimer].forEach(t => t && clearTimeout(t)),\n      setUserOffline: () => this.currentUserId && this.MessageService.setUserOffline(this.currentUserId).subscribe(),\n      stopTypingIndicator: () => {\n        if (this.isCurrentlyTyping && this.conversation?.id) {\n          this.isCurrentlyTyping = false;\n          clearTimeout(this.typingTimer);\n          this.MessageService.stopTyping(this.conversation.id).subscribe({\n            next: () => {},\n            error: error => {}\n          });\n        }\n      }\n    };\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]]\n    });\n  }\n  ngOnInit() {\n    this.currentUserId = this.authService.getCurrentUserId();\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n    }\n    this.subscribeToNotifications();\n    this.subscribeToUserStatus();\n    this.initializeUserStatus();\n    this.startActivityTracking();\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n    const routeSub = this.route.params.pipe(filter(params => params['id']), distinctUntilChanged(), switchMap(params => {\n      this.loading = true;\n      this.messages = [];\n      this.currentPage = 1;\n      this.hasMoreMessages = true;\n      return this.MessageService.getConversation(params['id'], this.MAX_MESSAGES_TO_LOAD, this.currentPage);\n    })).subscribe({\n      next: conversation => {\n        this.handleConversationLoaded(conversation);\n      },\n      error: error => {\n        this.handleError('Failed to load conversation', error);\n      }\n    });\n    this.subscriptions.add(routeSub);\n  }\n  // Gestion centralisée des erreurs\n  handleError(message, error, resetLoading = true) {\n    console.error('MessageChat', message, error);\n    if (resetLoading) {\n      this.loading = false;\n      this.isUploading = false;\n      this.isLoadingMore = false;\n    }\n    this.error = error;\n    this.toastService.showError(message);\n  }\n  // Gestion centralisée des succès\n  handleSuccess(message, callback) {\n    if (message) {\n      this.toastService.showSuccess(message);\n    }\n    if (callback) {\n      callback();\n    }\n  }\n  handleConversationLoaded(conversation) {\n    this.conversation = conversation;\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n      this.messages = [];\n    } else {\n      const conversationMessages = [...(conversation?.messages || [])];\n      conversationMessages.sort((a, b) => {\n        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n        return timeA - timeB;\n      });\n      this.messages = conversationMessages;\n    }\n    this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n    this.markMessagesAsRead();\n    if (this.conversation?.id) {\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub = this.MessageService.subscribeToConversationUpdates(conversationId).subscribe({\n      next: updatedConversation => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages ? [...updatedConversation.messages] : [];\n        this.scrollToBottom();\n      },\n      error: error => {\n        this.toastService.showError('Connection to conversation updates lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToNewMessages(conversationId) {\n    const sub = this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n      next: newMessage => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n            const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          setTimeout(() => this.scrollToBottom(), 100);\n          if (newMessage.sender?.id !== this.currentUserId && newMessage.sender?._id !== this.currentUserId) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: error => {\n        this.toastService.showError('Connection to new messages lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToTypingIndicators(conversationId) {\n    const sub = this.MessageService.subscribeToTypingIndicator(conversationId).subscribe({\n      next: event => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  markMessagesAsRead() {\n    const unreadMessages = this.messages.filter(msg => !msg.isRead && (msg.receiver?.id === this.currentUserId || msg.receiver?._id === this.currentUserId));\n    unreadMessages.forEach(msg => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: error => {}\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (!file) return;\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError('Invalid file type. Only images, PDFs and Word docs are allowed');\n      return;\n    }\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n  removeAttachment() {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n  // Frappe\n  onTyping() {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n    const conversationId = this.conversation.id;\n    clearTimeout(this.typingTimer);\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {},\n        error: error => {}\n      });\n    }\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {},\n          error: error => {}\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n  // Panneaux\n  togglePanel(panelName, closeOthers = true) {\n    const panels = {\n      theme: 'showThemeSelector',\n      menu: 'showMainMenu',\n      emoji: 'showEmojiPicker',\n      notification: 'showNotificationPanel',\n      search: 'showSearchBar',\n      status: 'showStatusSelector'\n    };\n    const currentPanel = panels[panelName];\n    if (currentPanel) {\n      this[currentPanel] = !this[currentPanel];\n      if (closeOthers && this[currentPanel]) {\n        Object.values(panels).forEach(panel => {\n          if (panel !== currentPanel) {\n            this[panel] = false;\n          }\n        });\n      }\n    }\n  }\n  changeTheme(theme) {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    localStorage.setItem('chat-theme', theme);\n  }\n  // Conversation - méthode utilitaire\n  showDevelopmentFeature(featureName) {\n    this.showMainMenu = false;\n    this.toastService.showInfo(`${featureName} en cours de développement`);\n  }\n  sendMessage() {\n    if (this.messageForm.invalid && !this.selectedFile || !this.currentUserId || !this.otherParticipant?.id) {\n      return;\n    }\n    this.cleanup.stopTypingIndicator();\n    const content = this.messageForm.get('content')?.value;\n    const tempMessage = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient'\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true\n    };\n    if (this.selectedFile) {\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n        if (this.previewUrl) {\n          tempMessage.attachments = [{\n            id: 'temp-attachment',\n            url: this.previewUrl ? this.previewUrl.toString() : '',\n            type: MessageType.IMAGE,\n            name: this.selectedFile.name,\n            size: this.selectedFile.size\n          }];\n        }\n      }\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n    this.messages = [...this.messages, tempMessage];\n    const fileToSend = this.selectedFile;\n    this.messageForm.reset();\n    this.removeAttachment();\n    setTimeout(() => this.scrollToBottom(true), 50);\n    this.isUploading = true;\n    const sendSub = this.MessageService.sendMessage(this.otherParticipant.id, content, fileToSend || undefined, MessageType.TEXT, this.conversation?.id).subscribe({\n      next: message => {\n        this.updateMessageState(tempMessage.id, message);\n        this.isUploading = false;\n      },\n      error: error => {\n        this.updateMessageState(tempMessage.id, null, true);\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      }\n    });\n    this.subscriptions.add(sendSub);\n  }\n  // Méthode consolidée pour mettre à jour l'état des messages\n  updateMessageState(tempId, newMessage, isError = false) {\n    this.messages = this.messages.map(msg => {\n      if (msg.id === tempId) {\n        if (newMessage) {\n          return newMessage;\n        } else if (isError) {\n          return {\n            ...msg,\n            isPending: false,\n            isError: true\n          };\n        }\n      }\n      return msg;\n    });\n  }\n  // Défilement\n  onScroll(event) {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n    if (scrollTop < 50 && !this.isLoadingMore && this.conversation?.id && this.hasMoreMessages) {\n      this.showLoadingIndicator();\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n      this.isLoadingMore = true;\n      this.loadMoreMessages();\n      // Maintenir la position de défilement\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(firstVisibleMessage.id);\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({\n                block: 'center'\n              });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n  // Méthode pour trouver le premier message visible dans la vue\n  getFirstVisibleMessage() {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length) return null;\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find(m => m.id === messageId) || null;\n      }\n    }\n    return null;\n  }\n  // Méthode pour trouver un élément de message par ID\n  findMessageElement(messageId) {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(`[data-message-id=\"${messageId}\"]`);\n  }\n  // Charger plus de messages\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages) return;\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n    // Augmenter la page\n    this.currentPage++;\n    // Charger plus de messages\n    this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        if (conversation && conversation.messages && conversation.messages.length > 0) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n          // Créer un Set des IDs existants\n          const existingIds = new Set(oldMessages.map(msg => msg.id));\n          // Filtrer et trier les nouveaux messages\n          const newMessages = conversation.messages.filter(msg => !existingIds.has(msg.id)).sort((a, b) => {\n            const timeA = new Date(a.timestamp).getTime();\n            const timeB = new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages = newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: error => {\n        console.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      }\n    });\n  }\n  // Méthode utilitaire pour comparer les timestamps\n  isSameTimestamp(timestamp1, timestamp2) {\n    if (!timestamp1 || !timestamp2) return false;\n    try {\n      const time1 = timestamp1 instanceof Date ? timestamp1.getTime() : new Date(timestamp1).getTime();\n      const time2 = timestamp2 instanceof Date ? timestamp2.getTime() : new Date(timestamp2).getTime();\n      return Math.abs(time1 - time2) < 1000;\n    } catch (error) {\n      return false;\n    }\n  }\n  scrollToBottom(force = false) {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 150;\n        if (force || isScrolledToBottom) {\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth'\n          });\n        }\n      });\n    } catch (err) {\n      console.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl) {\n    window.open(imageUrl, '_blank');\n  }\n  /**\n   * Détecte les changements après chaque vérification de la vue - Optimisé\n   */\n  ngAfterViewChecked() {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n    // Détection des changements optimisée - Suppression de la vérification inutile\n    // La détection automatique d'Angular gère déjà les messages vocaux\n  }\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations() {\n    this.router.navigate(['/messages/conversations']);\n  }\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji) {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector('.whatsapp-input-field');\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  subscribeToNotifications() {\n    const notificationSub = this.MessageService.subscribeToNewNotifications().subscribe({\n      next: notification => {\n        this.notifications.unshift(notification);\n        this.updateNotificationCount();\n        this.MessageService.play('notification');\n        if (notification.type === 'NEW_MESSAGE' && notification.conversationId === this.conversation?.id) {\n          if (notification.id) {\n            this.MessageService.markAsRead([notification.id]).subscribe();\n          }\n        }\n      },\n      error: error => {}\n    });\n    this.subscriptions.add(notificationSub);\n    const notificationsListSub = this.MessageService.notifications$.subscribe({\n      next: notifications => {\n        this.notifications = notifications;\n        this.updateNotificationCount();\n      },\n      error: error => {}\n    });\n    this.subscriptions.add(notificationsListSub);\n    const notificationCountSub = this.MessageService.notificationCount$.subscribe({\n      next: count => {\n        this.unreadNotificationCount = count;\n      }\n    });\n    this.subscriptions.add(notificationCountSub);\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: call => {\n        if (call) {\n          this.incomingCall = call;\n          this.showCallModal = true;\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      }\n    });\n    this.subscriptions.add(callSub);\n    const activeCallSub = this.MessageService.activeCall$.subscribe({\n      next: call => {\n        this.activeCall = call;\n        if (call) {\n          this.showActiveCallModal = true;\n          this.startCallTimerMethod();\n        } else {\n          this.showActiveCallModal = false;\n          this.stopCallTimerMethod();\n          this.resetCallStateMethod();\n        }\n      }\n    });\n    this.subscriptions.add(activeCallSub);\n    // S'abonner aux flux vidéo locaux\n    const localStreamSub = this.MessageService.localStream$.subscribe({\n      next: stream => {\n        if (stream && this.localVideoElement) {\n          this.localVideoElement.srcObject = stream;\n        }\n      }\n    });\n    this.subscriptions.add(localStreamSub);\n    // S'abonner aux flux vidéo distants\n    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n      next: stream => {\n        if (stream && this.remoteVideoElement) {\n          this.remoteVideoElement.srcObject = stream;\n        }\n      }\n    });\n    this.subscriptions.add(remoteStreamSub);\n  }\n  /**\n   * Charge les notifications\n   */\n  loadNotifications(refresh = false) {\n    const loadSub = this.MessageService.getNotifications(refresh, 1, 20).subscribe({\n      next: notifications => {\n        if (refresh) {\n          this.notifications = notifications;\n        } else {\n          this.notifications = [...this.notifications, ...notifications];\n        }\n        this.updateNotificationCount();\n      },\n      error: error => {\n        this.toastService.showError('Erreur lors du chargement des notifications');\n      }\n    });\n    this.subscriptions.add(loadSub);\n  }\n  /**\n   * S'abonne au statut utilisateur en temps réel\n   */\n  subscribeToUserStatus() {\n    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n      next: user => {\n        this.handleUserStatusUpdate(user);\n      },\n      error: error => {\n        // Error handled silently\n      }\n    });\n    this.subscriptions.add(statusSub);\n  }\n  /**\n   * Gère la mise à jour du statut d'un utilisateur\n   */\n  handleUserStatusUpdate(user) {\n    if (!user.id) return;\n    if (user.isOnline) {\n      this.onlineUsers.set(user.id, user);\n    } else {\n      this.onlineUsers.delete(user.id);\n    }\n    if (this.otherParticipant && this.otherParticipant.id === user.id) {\n      this.otherParticipant = {\n        ...this.otherParticipant,\n        ...user\n      };\n    }\n  }\n  /**\n   * Initialise le statut de l'utilisateur actuel\n   */\n  initializeUserStatus() {\n    if (!this.currentUserId) return;\n    const setOnlineSub = this.MessageService.setUserOnline(this.currentUserId).subscribe({\n      next: user => {\n        this.currentUserStatus = 'online';\n        this.lastActivityTime = new Date();\n      },\n      error: error => {\n        console.error('MessageChat', \"Erreur lors de l'initialisation du statut\", error);\n      }\n    });\n    this.subscriptions.add(setOnlineSub);\n  }\n  /**\n   * Démarre le suivi d'activité automatique\n   */\n  startActivityTracking() {\n    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];\n    events.forEach(event => {\n      document.addEventListener(event, this.onUserActivity.bind(this), true);\n    });\n  }\n  /**\n   * Gère l'activité de l'utilisateur\n   */\n  onUserActivity() {\n    this.lastActivityTime = new Date();\n    // Réinitialiser le timer\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n    // Remettre en ligne si absent\n    if (this.currentUserStatus === 'away' || this.currentUserStatus === 'offline') {\n      this.updateUserStatus('online');\n    }\n    // Programmer la mise en absence\n    this.autoAwayTimeout = setTimeout(() => {\n      if (this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      }\n    }, this.c.delays.away);\n  }\n  /**\n   * Met à jour le statut de l'utilisateur\n   */\n  updateUserStatus(status) {\n    if (!this.currentUserId) return;\n    const previousStatus = this.currentUserStatus;\n    let updateObservable;\n    if (status === 'online') {\n      updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n    } else {\n      updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n    }\n    const updateSub = updateObservable.subscribe({\n      next: user => {\n        this.currentUserStatus = status;\n        if (status !== previousStatus) {\n          const statusText = this.getStatusText(status);\n          this.toastService.showInfo(`Statut : ${statusText}`);\n        }\n      },\n      error: error => {\n        console.error('MessageChat', 'Erreur lors de la mise à jour du statut', error);\n      }\n    });\n    this.subscriptions.add(updateSub);\n  }\n  /**\n   * Charge la liste des utilisateurs en ligne\n   */\n  loadOnlineUsers() {\n    const usersSub = this.MessageService.getAllUsers(false, undefined, 1, 50, 'username', 'asc', true).subscribe({\n      next: users => {\n        users.forEach(user => {\n          if (user.isOnline && user.id) {\n            this.onlineUsers.set(user.id, user);\n          }\n        });\n      },\n      error: error => {\n        console.error('MessageChat', 'Erreur lors du chargement des utilisateurs en ligne', error);\n      }\n    });\n    this.subscriptions.add(usersSub);\n  }\n  togglePinMessage(message) {\n    this.isPinning[message.id] = true;\n    setTimeout(() => {\n      this.isPinning[message.id] = false;\n      this.showPinConfirm[message.id] = false;\n    }, 1000);\n  }\n  ngOnDestroy() {\n    this.cleanup.clearTimeouts();\n    this.cleanup.setUserOffline();\n    this.cleanup.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.UserStatusService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 40,\n      vars: 25,\n      consts: [[1, \"chat-container\"], [1, \"chat-header\"], [1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [\"alt\", \"User avatar\", 1, \"user-avatar\", 3, \"src\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"whatsapp-actions\"], [4, \"ngFor\", \"ngForOf\"], [1, \"relative\"], [\"title\", \"Statut utilisateur\", 1, \"whatsapp-action-button\", \"relative\", 3, \"ngClass\", \"click\"], [3, \"ngClass\"], [\"class\", \"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\", 4, \"ngIf\"], [\"class\", \"absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\", 4, \"ngIf\"], [1, \"whatsapp-action-button\", \"btn-theme\", 3, \"click\"], [1, \"fas\", \"fa-palette\"], [\"class\", \"theme-selector-menu absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\", 4, \"ngIf\"], [\"title\", \"Menu principal\", 1, \"whatsapp-action-button\", \"btn-menu\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"messages-area\"], [\"messagesContainer\", \"\"], [\"style\", \"text-align: center; padding: 2rem; color: #00f7ff\", 4, \"ngIf\"], [\"style\", \"text-align: center; padding: 2rem; color: #888\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"input-area\"], [1, \"input-form\", 3, \"formGroup\", \"ngSubmit\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", 1, \"message-input\", 3, \"disabled\"], [\"type\", \"submit\", 1, \"send-btn\", 3, \"disabled\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"side-panel\", 3, \"ngClass\"], [1, \"panel-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"style\", \"padding: 1rem; text-align: center; color: #888\", 4, \"ngIf\"], [\"style\", \"padding: 0.5rem; border-bottom: 1px solid rgba(0, 247, 255, 0.1)\", 4, \"ngFor\", \"ngForOf\"], [\"style\", \"\\n      position: absolute;\\n      top: 60px;\\n      left: 0;\\n      right: 0;\\n      background: rgba(0, 0, 0, 0.9);\\n      padding: 1rem;\\n      z-index: 100;\\n    \", 4, \"ngIf\"], [1, \"user-info\"], [1, \"user-status\"], [3, \"ngClass\", \"title\", \"click\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [1, \"absolute\", \"-top-1\", \"-right-1\", \"w-3\", \"h-3\", \"bg-blue-500\", \"rounded-full\", \"animate-pulse\"], [1, \"absolute\", \"right-0\", \"mt-2\", \"w-56\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"shadow-lg\", \"z-50\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"p-3\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"bg-gradient-to-r\", \"from-[#4f5fad]/10\", \"to-[#6d78c9]/10\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"font-medium\", 3, \"ngClass\"], [1, \"p-1\"], [\"class\", \"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\", 3, \"disabled\", \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"p-1\"], [1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"text-xs\"], [1, \"font-medium\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", 3, \"disabled\", \"ngClass\", \"click\"], [1, \"theme-selector-menu\", \"absolute\", \"right-0\", \"mt-2\", \"w-48\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"shadow-lg\", \"z-50\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"p-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [\"href\", \"javascript:void(0)\", \"class\", \"block w-full text-left px-3 py-2 text-sm rounded-md transition-colors\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"transition-colors\", 3, \"ngClass\", \"click\"], [1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-red-500/10\", \"dark:hover:bg-red-500/10\", \"transition-colors\", \"text-red-600\", \"dark:text-red-400\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"mr-3\", \"text-xs\"], [1, \"fas\", \"fa-download\", \"mr-3\", \"text-xs\"], [1, \"fas\", \"fa-info-circle\", \"mr-3\", \"text-xs\"], [1, \"fas\", \"fa-cog\", \"mr-3\", \"text-xs\"], [2, \"text-align\", \"center\", \"padding\", \"2rem\", \"color\", \"#00f7ff\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [2, \"text-align\", \"center\", \"padding\", \"2rem\", \"color\", \"#888\"], [1, \"fas\", \"fa-comments\", 2, \"font-size\", \"3rem\", \"margin-bottom\", \"1rem\", \"opacity\", \"0.5\"], [2, \"font-size\", \"0.9rem\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-bubble\"], [2, \"font-size\", \"0.7rem\", \"opacity\", \"0.7\", \"margin-top\", \"0.5rem\"], [2, \"padding\", \"1rem\", \"text-align\", \"center\", \"color\", \"#888\"], [1, \"fas\", \"fa-bell-slash\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"0.5rem\", \"opacity\", \"0.5\"], [2, \"padding\", \"0.5rem\", \"border-bottom\", \"1px solid rgba(0, 247, 255, 0.1)\"], [2, \"margin\", \"0\", \"font-size\", \"0.9rem\"], [2, \"opacity\", \"0.7\"], [2, \"position\", \"absolute\", \"top\", \"60px\", \"left\", \"0\", \"right\", \"0\", \"background\", \"rgba(0, 0, 0, 0.9)\", \"padding\", \"1rem\", \"z-index\", \"100\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"0.5rem\"], [\"placeholder\", \"Rechercher dans la conversation...\", 2, \"flex\", \"1\", \"padding\", \"0.5rem\", \"background\", \"rgba(0, 0, 0, 0.5)\", \"border\", \"1px solid #00f7ff\", \"border-radius\", \"5px\", \"color\", \"#e0e0e0\", 3, \"ngModel\", \"ngModelChange\"], [2, \"background\", \"transparent\", \"border\", \"1px solid #00f7ff\", \"color\", \"#00f7ff\", \"padding\", \"0.5rem\", \"border-radius\", \"5px\", 3, \"click\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵtemplate(5, MessageChatComponent_div_5_Template, 5, 2, \"div\", 5);\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵtemplate(7, MessageChatComponent_ng_container_7_Template, 4, 8, \"ng-container\", 7);\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_9_listener() {\n            return ctx.toggleStatusSelector();\n          });\n          i0.ɵɵelement(10, \"i\", 10);\n          i0.ɵɵtemplate(11, MessageChatComponent_span_11_Template, 1, 0, \"span\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 18, 4, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_14_listener() {\n            return ctx.toggleThemeSelector();\n          });\n          i0.ɵɵelement(15, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, MessageChatComponent_div_16_Template, 5, 1, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_18_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(19, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, MessageChatComponent_div_20_Template, 24, 0, \"div\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 18, 19);\n          i0.ɵɵtemplate(23, MessageChatComponent_div_23_Template, 3, 0, \"div\", 20);\n          i0.ɵɵtemplate(24, MessageChatComponent_div_24_Template, 6, 0, \"div\", 21);\n          i0.ɵɵtemplate(25, MessageChatComponent_div_25_Template, 6, 6, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 23)(27, \"form\", 24);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_27_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelement(28, \"input\", 25);\n          i0.ɵɵelementStart(29, \"button\", 26);\n          i0.ɵɵelement(30, \"i\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 28)(32, \"div\", 29)(33, \"h3\");\n          i0.ɵɵtext(34, \"Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_35_listener() {\n            return ctx.toggleNotificationPanel();\n          });\n          i0.ɵɵelement(36, \"i\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(37, MessageChatComponent_div_37_Template, 4, 0, \"div\", 32);\n          i0.ɵɵtemplate(38, MessageChatComponent_div_38_Template, 5, 2, \"div\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(39, MessageChatComponent_div_39_Template, 5, 1, \"div\", 34);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getHeaderActions());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c6, ctx.showStatusSelector));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.getStatusIcon(ctx.currentUserStatus));\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusColor(ctx.currentUserStatus));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUpdatingStatus);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showStatusSelector);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showThemeSelector);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.otherParticipant);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.messageForm.invalid || !ctx.otherParticipant);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c7, ctx.showNotificationPanel));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.notifications.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showSearchBar);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.NgModel, i4.FormGroupDirective, i4.FormControlName],\n      styles: [\"\\n\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);\\n  color: #e0e0e0;\\n}\\n\\n\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 1rem;\\n  background: rgba(0, 247, 255, 0.1);\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  margin-right: 1rem;\\n  border: 2px solid #00f7ff;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #00f7ff;\\n  font-size: 1.1rem;\\n}\\n\\n.user-status[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #888;\\n}\\n\\n\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 1px solid #00f7ff;\\n  color: #00f7ff;\\n  padding: 0.5rem;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: #00f7ff;\\n  color: #000;\\n  box-shadow: 0 0 10px #00f7ff;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  background: linear-gradient(135deg, #ff6b69, #ff4757);\\n  color: white;\\n  font-size: 0.7rem;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.messages-area[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  background: rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.message[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.message.current-user[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  max-width: 70%;\\n  padding: 0.8rem 1rem;\\n  border-radius: 18px;\\n  position: relative;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.message.current-user[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff, #0066ff);\\n  color: #fff;\\n  border-radius: 18px 18px 4px 18px;\\n}\\n\\n.message.other-user[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: #e0e0e0;\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  border-radius: 18px 18px 18px 4px;\\n}\\n\\n\\n\\n.input-area[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background: rgba(0, 247, 255, 0.05);\\n  border-top: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.input-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.8rem 1rem;\\n  background: rgba(0, 0, 0, 0.5);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  border-radius: 25px;\\n  color: #e0e0e0;\\n  outline: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  border-color: #00f7ff;\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.3);\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff, #0066ff);\\n  border: none;\\n  color: #fff;\\n  padding: 0.8rem;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.send-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n.side-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: -300px;\\n  width: 300px;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.9);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-left: 1px solid rgba(0, 247, 255, 0.3);\\n  transition: right 0.3s ease;\\n  z-index: 1000;\\n  padding: 1rem;\\n}\\n\\n.side-panel.open[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: none;\\n  color: #00f7ff;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .chat-header[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n\\n  .message-bubble[_ngcontent-%COMP%] {\\n    max-width: 85%;\\n  }\\n\\n  .side-panel[_ngcontent-%COMP%] {\\n    width: 100%;\\n    right: -100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "MessageType", "CallType", "switchMap", "distinctUntilChanged", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "otherParticipant", "username", "ɵɵtextInterpolate1", "isOnline", "formatLastActive", "lastActive", "ɵɵclassMap", "action_r13", "badge", "class", "ɵɵproperty", "ɵɵpureFunction1", "_c2", "animate", "count", "ɵɵelementContainerStart", "ɵɵlistener", "MessageChatComponent_ng_container_7_Template_button_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r17", "$implicit", "ɵɵresetView", "onClick", "ɵɵelement", "ɵɵtemplate", "MessageChatComponent_ng_container_7_span_3_Template", "ɵɵelementContainerEnd", "activeClass", "isActive", "ɵɵpureFunction0", "_c3", "title", "icon", "MessageChatComponent_div_12_button_8_Template_button_click_0_listener", "_r21", "status_r19", "ctx_r20", "ɵɵnextContext", "updateUserStatus", "key", "toggleStatusSelector", "ctx_r18", "isUpdatingStatus", "_c4", "currentUserStatus", "color", "label", "description", "MessageChatComponent_div_12_button_8_Template", "MessageChatComponent_div_12_Template_button_click_10_listener", "_r23", "ctx_r22", "toggleUserStatusPanel", "ctx_r3", "getStatusColor", "getStatusText", "getStatusOptions", "getOnlineUsersCount", "MessageChatComponent_div_16_a_4_Template_a_click_0_listener", "_r27", "theme_r25", "ctx_r26", "changeTheme", "hoverColor", "MessageChatComponent_div_16_a_4_Template", "ctx_r4", "getThemeOptions", "MessageChatComponent_div_20_Template_button_click_4_listener", "_r29", "ctx_r28", "clearConversation", "MessageChatComponent_div_20_Template_button_click_9_listener", "ctx_r30", "exportConversation", "MessageChatComponent_div_20_Template_button_click_14_listener", "ctx_r31", "toggleConversationInfo", "MessageChatComponent_div_20_Template_button_click_19_listener", "ctx_r32", "toggleConversationSettings", "ɵɵpureFunction2", "_c5", "message_r33", "sender", "id", "ctx_r9", "currentUserId", "content", "formatMessageTime", "timestamp", "notification_r34", "ctx_r11", "MessageChatComponent_div_39_Template_input_ngModelChange_2_listener", "$event", "_r36", "ctx_r35", "searchQuery", "MessageChatComponent_div_39_Template_button_click_3_listener", "ctx_r37", "toggleSearchBar", "ctx_r12", "MessageChatComponent", "availableReactions", "c", "reactions", "commonEmojis", "MessageService", "getCommonEmojis", "getHeaderActions", "initiateCall", "showSearchBar", "getPinnedMessagesCount", "togglePinnedMessages", "showPinnedMessages", "toggleNotificationPanel", "showNotificationPanel", "unreadNotificationCount", "toggleCallHistoryPanel", "showCallHistoryPanel", "toggleCallStatsPanel", "showCallStatsPanel", "toggleVoiceMessagesPanel", "showVoiceMessagesPanel", "voiceMessages", "length", "constructor", "route", "authService", "fb", "statusService", "router", "toastService", "cdr", "messages", "conversation", "loading", "currentUsername", "selectedFile", "previewUrl", "isUploading", "isTyping", "isRecordingVoice", "voiceRecordingDuration", "MAX_MESSAGES_PER_SIDE", "MAX_MESSAGES_TO_LOAD", "MAX_TOTAL_MESSAGES", "currentPage", "isLoadingMore", "hasMoreMessages", "subscriptions", "selectedTheme", "showThemeSelector", "showMainMenu", "showEmojiPicker", "showStatusSelector", "showNotificationSettings", "showUserStatusPanel", "incomingCall", "activeCall", "showCallModal", "showActiveCallModal", "isCallMuted", "isVideoEnabled", "callDuration", "callTimer", "notifications", "selectedNotifications", "Set", "showDeleteConfirmModal", "editingMessageId", "<PERSON><PERSON><PERSON><PERSON>", "replyingToMessage", "isSearching", "searchMode", "searchResults", "pinnedMessages", "showReactionPicker", "showDeleteConfirm", "showPinConfirm", "isPinning", "showMessageOptions", "showForwardModal", "forwardingMessage", "selectedConversations", "availableConversations", "isForwarding", "isLoadingConversations", "delays", "away", "typing", "scroll", "anim", "error", "trackById", "i", "item", "toString", "NEW_MESSAGE", "CALL_MISSED", "SYSTEM", "status", "online", "text", "offline", "busy", "themes", "calls", "COMPLETED", "MISSED", "REJECTED", "notificationConfig", "callStatusColors", "notificationFilter", "isLoadingNotifications", "isMarkingAsRead", "isDeletingNotifications", "hasMoreNotifications", "notificationSounds", "notificationPreview", "autoMarkAsRead", "isCallMinimized", "callQuality", "showCallControls", "onlineUsers", "Map", "lastActivityTime", "Date", "autoAwayTimeout", "callHistory", "localVideoElement", "remoteVideoElement", "statusFilterType", "f", "getIcon", "t", "getFileIcon", "getType", "getFileType", "isCurrentlyTyping", "TYPING_DELAY", "TYPING_TIMEOUT", "mainToggleMethods", "themeSelector", "togglePanel", "mainMenu", "emojiPicker", "toggleThemeSelector", "toggleMainMenu", "toggleEmojiPicker", "toggleMethods", "searchBar", "clearSearch", "statusSelector", "notificationSettings", "userStatusPanel", "callMinimize", "callHistoryPanel", "callStatsPanel", "voiceMessagesPanel", "toggleNotificationSettings", "toggleCallMinimize", "conversationMethods", "showInfo", "showDevelopmentFeature", "showSettings", "clear", "showWarning", "confirm", "showSuccess", "export", "conversation<PERSON>ame", "isGroup", "groupName", "exportData", "name", "participants", "createdAt", "map", "msg", "type", "exportedAt", "toISOString", "exportedBy", "blob", "Blob", "JSON", "stringify", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "safeFileName", "replace", "toLowerCase", "dateStr", "split", "download", "click", "revokeObjectURL", "s", "formatTime", "formatActive", "formatDate", "formatMessageDate", "showDateHeader", "shouldShowDateHeader", "m", "getMessageType", "hasImage", "isVoice", "isVoiceMessage", "getVoiceUrl", "getVoiceMessageUrl", "getVoiceDuration", "getVoiceMessageDuration", "getVoiceHeight", "getVoiceBarHeight", "formatVoice", "formatVoiceDuration", "getImageUrl", "getTypeClass", "getMessageTypeClass", "loadingIndicatorMethods", "show", "getElementById", "indicator", "className", "innerHTML", "messagesContainer", "nativeElement", "prepend", "hide", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showLoadingIndicator", "hideLoadingIndicator", "voiceRecordingMethods", "toggle", "complete", "audioBlob", "showError", "receiverId", "sendVoiceMessage", "subscribe", "next", "message", "scrollToBottom", "cancel", "toggleVoiceRecording", "onVoiceRecordingComplete", "onVoiceRecordingCancelled", "callMethods", "initiate", "AUDIO", "VIDEO", "call", "accept", "acceptCall", "reject", "rejectCall", "end", "sub", "activeCall$", "endCall", "unsubscribe", "callControlMethods", "toggleMute", "updateMedia", "toggleVideo", "toggleMedia", "console", "toggleCallMute", "toggleCallVideo", "updateCallMedia", "timerMethods", "startCallTimer", "setInterval", "stopCallTimer", "clearInterval", "resetCallState", "startCallTimerMethod", "stopCallTimerMethod", "resetCallStateMethod", "notificationToggleMethod", "loadNotifications", "notificationMethods", "loadMore", "updateCount", "n", "isRead", "getFiltered", "toggleSelection", "notificationId", "has", "delete", "add", "toggleSelectAll", "filteredNotifications", "allSelected", "every", "for<PERSON>ach", "areAllSelected", "loadMoreNotifications", "updateNotificationCount", "getFilteredNotifications", "toggleNotificationSelection", "toggleSelectAllNotifications", "areAllNotificationsSelected", "markingMethods", "markSelected", "selectedIds", "Array", "from", "mark<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "unreadNotifications", "unreadIds", "ids", "onSuccess", "mark<PERSON>ub", "result", "includes", "readAt", "markSelectedAsRead", "markAllAsRead", "notificationDeleteMethods", "showDeleteSelectedConfirmation", "size", "deleteSelected", "deleteSub", "deleteMultipleNotifications", "deleteOne", "deleteNotification", "deleteAll", "deleteAllSub", "deleteAllNotifications", "deleteSelectedNotifications", "cancelDeleteNotifications", "notificationUtilMethods", "date", "getColor", "index", "notification", "formatNotificationDate", "getNotificationIcon", "getNotificationColor", "trackByNotificationId", "panelMethods", "getActivePanels", "panels", "push", "closeAction", "Object", "entries", "config", "statusMethods", "getText", "getStatusIcon", "utilityMethods", "formatLastSeen", "values", "user", "getFilteredUsers", "setStatus<PERSON>ilter", "replyForwardMethods", "startReply", "cancelReply", "openForwardModal", "closeForwardModal", "startReplyToMessage", "messageMethods", "getPinIcon", "isMessagePinned", "getPinDisplayText", "canEditMessage", "isPinned", "editMethods", "startEditMessage", "cancelEditMessage", "saveEditMessage", "messageId", "onEditKeyPress", "event", "shift<PERSON>ey", "preventDefault", "callUtilities", "getCallStatusColor", "getCallTypeIcon", "formatCallDuration", "duration", "minutes", "Math", "floor", "seconds", "padStart", "formatCallDate", "eventMethods", "onDocumentClick", "target", "closeConfigs", "selectors", "property", "isClickOutside", "some", "selector", "closest", "confirmDeleteMessage", "reactionMethods", "getUniqueReactions", "onReactionClick", "emoji", "hasUserReacted", "conversationSelectionMethods", "selectAll", "deselectAll", "conversationId", "indexOf", "splice", "isSelected", "getDisplayImage", "image", "getDisplayName", "forwardMessage", "setTimeout", "areAllConversationsSelected", "selectAllConversations", "deselectAllConversations", "toggleConversationSelection", "isConversationSelected", "getConversationDisplayImage", "getConversationDisplayName", "simpleNotificationMethods", "onCallMouseMove", "saveNotificationSettings", "setNotificationFilter", "searchMethods", "onInput", "value", "perform", "onKeyPress", "onSearchInput", "onSearchKeyPress", "performSearch", "finalUtilityMethods", "navigateToMessage", "scrollToPinnedMessage", "searchAndReactionMethods", "highlightSearchTerms", "query", "regex", "RegExp", "toggleReactionPicker", "reactToMessage", "toggleMessageOptions", "confirmationMethods", "showPinConfirmation", "cancelPinConfirmation", "showDeleteConfirmation", "cancelDeleteMessage", "cleanup", "clearTimeouts", "typingTimeout", "clearTimeout", "setUserOffline", "stopTypingIndicator", "typingTimer", "stopTyping", "messageForm", "group", "max<PERSON><PERSON><PERSON>", "ngOnInit", "getCurrentUserId", "savedTheme", "localStorage", "getItem", "subscribeToNotifications", "subscribeToUserStatus", "initializeUserStatus", "startActivityTracking", "addEventListener", "bind", "routeSub", "params", "pipe", "getConversation", "handleConversationLoaded", "handleError", "resetLoading", "handleSuccess", "callback", "find", "p", "_id", "conversationMessages", "sort", "a", "b", "timeA", "getTime", "timeB", "markMessagesAsRead", "subscribeToConversationUpdates", "subscribeToNewMessages", "subscribeToTypingIndicators", "updatedConversation", "newMessage", "markMessageAsRead", "subscribeToTypingIndicator", "userId", "unreadMessages", "receiver", "onFileSelected", "file", "files", "validTypes", "reader", "FileReader", "onload", "readAsDataURL", "removeAttachment", "fileInput", "onTyping", "startTyping", "panelName", "closeOthers", "theme", "menu", "search", "currentPanel", "panel", "setItem", "featureName", "sendMessage", "invalid", "get", "tempMessage", "isPending", "fileType", "startsWith", "attachments", "IMAGE", "FILE", "fileToSend", "reset", "sendSub", "undefined", "TEXT", "updateMessageState", "tempId", "isError", "onScroll", "container", "scrollTop", "oldScrollHeight", "scrollHeight", "firstVisibleMessage", "getFirstVisibleMessage", "loadMoreMessages", "requestAnimationFrame", "preserveScrollPosition", "messageElement", "findMessageElement", "scrollIntoView", "block", "newScrollHeight", "scrollDiff", "messageElements", "querySelectorAll", "element", "rect", "getBoundingClientRect", "top", "bottom", "clientHeight", "getAttribute", "querySelector", "oldMessages", "existingIds", "newMessages", "slice", "isSameTimestamp", "timestamp1", "timestamp2", "time1", "time2", "abs", "force", "isScrolledToBottom", "scrollTo", "behavior", "err", "openImageFullscreen", "imageUrl", "open", "ngAfterViewChecked", "goBackToConversations", "navigate", "insert<PERSON><PERSON><PERSON>", "control", "currentValue", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputElement", "focus", "notificationSub", "subscribeToNewNotifications", "unshift", "play", "notificationsListSub", "notifications$", "notificationCountSub", "notificationCount$", "callSub", "incomingCall$", "activeCallSub", "localStreamSub", "localStream$", "stream", "srcObject", "remoteStreamSub", "remoteStream$", "refresh", "loadSub", "getNotifications", "statusSub", "handleUserStatusUpdate", "set", "setOnlineSub", "setUserOnline", "events", "onUserActivity", "previousStatus", "updateObservable", "updateSub", "statusText", "loadOnlineUsers", "usersSub", "getAllUsers", "users", "togglePinMessage", "ngOnDestroy", "removeEventListener", "ɵɵdirectiveInject", "i1", "i2", "ActivatedRoute", "i3", "AuthuserService", "i4", "FormBuilder", "i5", "UserStatusService", "Router", "i6", "ToastService", "ChangeDetectorRef", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_div_5_Template", "MessageChatComponent_ng_container_7_Template", "MessageChatComponent_Template_button_click_9_listener", "MessageChatComponent_span_11_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_Template_button_click_14_listener", "MessageChatComponent_div_16_Template", "MessageChatComponent_Template_button_click_18_listener", "MessageChatComponent_div_20_Template", "MessageChatComponent_div_23_Template", "MessageChatComponent_div_24_Template", "MessageChatComponent_div_25_Template", "MessageChatComponent_Template_form_ngSubmit_27_listener", "MessageChatComponent_Template_button_click_35_listener", "MessageChatComponent_div_37_Template", "MessageChatComponent_div_38_Template", "MessageChatComponent_div_39_Template", "ɵɵsanitizeUrl", "_c6", "_c7"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ElementRef,\n  AfterViewChecked,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { User } from '@app/models/user.model';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport {\n  Message,\n  Conversation,\n  MessageType,\n  CallType,\n} from 'src/app/models/message.model';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { MessageService } from '@app/services/message.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: 'message-chat.component.html',\n  styleUrls: ['./message-chat.component.css'],\n})\nexport class MessageChatComponent\n  implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, AfterViewChecked\n{\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  messages: Message[] = [];\n  messageForm: FormGroup;\n  conversation: Conversation | null = null;\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  currentUsername: string = 'You';\n  otherParticipant: User | null = null;\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n  isUploading = false;\n  isTyping = false;\n  typingTimeout: any;\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n\n  private readonly MAX_MESSAGES_PER_SIDE = 5;\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private readonly MAX_TOTAL_MESSAGES = 100;\n  private currentPage = 1;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  private subscriptions = new Subscription();\n\n  // Interface\n  selectedTheme: string = 'theme-default';\n\n  // États\n  showThemeSelector = false;\n  showMainMenu = false;\n  showEmojiPicker = false;\n  showSearchBar = false;\n  showPinnedMessages = false;\n  showStatusSelector = false;\n  showNotificationPanel = false;\n  showNotificationSettings = false;\n  showUserStatusPanel = false;\n  showCallHistoryPanel = false;\n  showCallStatsPanel = false;\n  showVoiceMessagesPanel = false;\n\n  // Appels\n  incomingCall: any = null;\n  activeCall: any = null;\n  showCallModal = false;\n  showActiveCallModal = false;\n  isCallMuted = false;\n  isVideoEnabled = true;\n  callDuration = 0;\n  callTimer: any = null;\n\n  // Notifications et messages\n  notifications: any[] = [];\n  unreadNotificationCount: number = 0;\n  selectedNotifications: Set<string> = new Set();\n  showDeleteConfirmModal: boolean = false;\n  editingMessageId: string | null = null;\n  editingContent = '';\n  replyingToMessage: any = null;\n\n  // Recherche\n  searchQuery = '';\n  isSearching = false;\n  searchMode = false;\n  searchResults: any[] = [];\n\n  // Panneaux\n  pinnedMessages: any[] = [];\n  showReactionPicker: { [key: string]: boolean } = {};\n  showDeleteConfirm: { [key: string]: boolean } = {};\n  showPinConfirm: { [key: string]: boolean } = {};\n  isPinning: { [key: string]: boolean } = {};\n  showMessageOptions: { [key: string]: boolean } = {};\n\n  // Variables de transfert\n  showForwardModal = false;\n  forwardingMessage: any = null;\n  selectedConversations: string[] = [];\n  availableConversations: any[] = [];\n  isForwarding = false;\n  isLoadingConversations = false;\n\n  // Constantes optimisées\n  readonly c = {\n    reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n    delays: { away: 300000, typing: 3000, scroll: 100, anim: 300 },\n    error: 'Erreur. Veuillez réessayer.',\n    trackById: (i: number, item: any): string => item?.id || i.toString(),\n    notifications: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    status: {\n      online: {\n        text: 'En ligne',\n        color: 'text-green-500',\n        icon: 'fas fa-circle',\n        label: 'En ligne',\n        description: 'Disponible pour discuter',\n      },\n      offline: {\n        text: 'Hors ligne',\n        color: 'text-gray-500',\n        icon: 'far fa-circle',\n        label: 'Hors ligne',\n        description: 'Invisible pour tous',\n      },\n      away: {\n        text: 'Absent',\n        color: 'text-yellow-500',\n        icon: 'fas fa-clock',\n        label: 'Absent',\n        description: 'Absent temporairement',\n      },\n      busy: {\n        text: 'Occupé',\n        color: 'text-red-500',\n        icon: 'fas fa-minus-circle',\n        label: 'Occupé',\n        description: 'Ne pas déranger',\n      },\n    },\n    themes: [\n      {\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad',\n        hoverColor: '#4f5fad',\n      },\n      {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d',\n        hoverColor: '#ff6b9d',\n      },\n      {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6',\n        hoverColor: '#3d85c6',\n      },\n      {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f',\n        hoverColor: '#6aa84f',\n      },\n    ],\n    calls: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n    // Alias pour compatibilité\n    notificationConfig: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    callStatusColors: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n  };\n\n  // Getter pour compatibilité\n  get availableReactions(): string[] {\n    return this.c.reactions;\n  }\n\n  // Variables de notification\n  notificationFilter = 'all';\n  isLoadingNotifications = false;\n  isMarkingAsRead = false;\n  isDeletingNotifications = false;\n  hasMoreNotifications = false;\n  notificationSounds = true;\n  notificationPreview = true;\n  autoMarkAsRead = true;\n\n  // Variables d'appel\n  isCallMinimized = false;\n  callQuality = 'connecting';\n  showCallControls = false;\n\n  // Variables de statut utilisateur\n  onlineUsers: Map<string, User> = new Map();\n  currentUserStatus: string = 'online';\n  lastActivityTime: Date = new Date();\n  autoAwayTimeout: any = null;\n  isUpdatingStatus = false;\n  callHistory: any[] = [];\n  voiceMessages: any[] = [];\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n  statusFilterType = 'all';\n\n  // Emojis du service\n  get commonEmojis(): string[] {\n    return this.MessageService.getCommonEmojis();\n  }\n\n  // Configuration des boutons d'action de l'en-tête\n  getHeaderActions() {\n    return [\n      {\n        class: 'btn-audio-call',\n        icon: 'fas fa-phone-alt',\n        title: 'Appel audio',\n        onClick: () => this.initiateCall('AUDIO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-video-call',\n        icon: 'fas fa-video',\n        title: 'Appel vidéo',\n        onClick: () => this.initiateCall('VIDEO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-search',\n        icon: 'fas fa-search',\n        title: 'Rechercher',\n        onClick: () => this.toggleSearchBar(),\n        isActive: this.showSearchBar,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-pinned relative',\n        icon: 'fas fa-thumbtack',\n        title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n        onClick: () => this.togglePinnedMessages(),\n        isActive: this.showPinnedMessages,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.getPinnedMessagesCount() > 0\n            ? {\n                count: this.getPinnedMessagesCount(),\n                class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n                animate: false,\n              }\n            : null,\n      },\n      {\n        class: 'btn-notifications relative',\n        icon: 'fas fa-bell',\n        title: 'Notifications',\n        onClick: () => this.toggleNotificationPanel(),\n        isActive: this.showNotificationPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.unreadNotificationCount > 0\n            ? {\n                count: this.unreadNotificationCount,\n                class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n                animate: true,\n              }\n            : null,\n      },\n      {\n        class: 'btn-history relative',\n        icon: 'fas fa-history',\n        title: 'Historique des appels',\n        onClick: () => this.toggleCallHistoryPanel(),\n        isActive: this.showCallHistoryPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-stats relative',\n        icon: 'fas fa-chart-bar',\n        title: \"Statistiques d'appels\",\n        onClick: () => this.toggleCallStatsPanel(),\n        isActive: this.showCallStatsPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-voice-messages relative',\n        icon: 'fas fa-microphone',\n        title: 'Messages vocaux',\n        onClick: () => this.toggleVoiceMessagesPanel(),\n        isActive: this.showVoiceMessagesPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.voiceMessages.length > 0\n            ? {\n                count: this.voiceMessages.length,\n                class: 'bg-[#4f5fad]',\n                animate: false,\n              }\n            : null,\n      },\n    ];\n  }\n\n  constructor(\n    private MessageService: MessageService,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private fb: FormBuilder,\n    public statusService: UserStatusService,\n    public router: Router,\n    private toastService: ToastService,\n\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]],\n    });\n  }\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n    }\n\n    this.subscribeToNotifications();\n    this.subscribeToUserStatus();\n    this.initializeUserStatus();\n    this.startActivityTracking();\n\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n\n    const routeSub = this.route.params\n      .pipe(\n        filter((params) => params['id']),\n        distinctUntilChanged(),\n        switchMap((params) => {\n          this.loading = true;\n          this.messages = [];\n          this.currentPage = 1;\n          this.hasMoreMessages = true;\n\n          return this.MessageService.getConversation(\n            params['id'],\n            this.MAX_MESSAGES_TO_LOAD,\n            this.currentPage\n          );\n        })\n      )\n      .subscribe({\n        next: (conversation) => {\n          this.handleConversationLoaded(conversation);\n        },\n        error: (error) => {\n          this.handleError('Failed to load conversation', error);\n        },\n      });\n    this.subscriptions.add(routeSub);\n  }\n\n  // Gestion centralisée des erreurs\n  private handleError(\n    message: string,\n    error: any,\n    resetLoading: boolean = true\n  ): void {\n    console.error('MessageChat', message, error);\n    if (resetLoading) {\n      this.loading = false;\n      this.isUploading = false;\n      this.isLoadingMore = false;\n    }\n    this.error = error;\n    this.toastService.showError(message);\n  }\n\n  // Gestion centralisée des succès\n  private handleSuccess(message?: string, callback?: () => void): void {\n    if (message) {\n      this.toastService.showSuccess(message);\n    }\n    if (callback) {\n      callback();\n    }\n  }\n\n  // Services de fichiers optimisés\n  readonly f = {\n    getIcon: (t?: string) => this.MessageService.getFileIcon(t),\n    getType: (t?: string) => this.MessageService.getFileType(t),\n  };\n\n  getFileIcon = this.f.getIcon;\n  getFileType = this.f.getType;\n\n  private handleConversationLoaded(conversation: Conversation): void {\n    this.conversation = conversation;\n\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.otherParticipant =\n        conversation?.participants?.find(\n          (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n        ) || null;\n      this.messages = [];\n    } else {\n      const conversationMessages = [...(conversation?.messages || [])];\n\n      conversationMessages.sort((a, b) => {\n        const timeA =\n          a.timestamp instanceof Date\n            ? a.timestamp.getTime()\n            : new Date(a.timestamp as string).getTime();\n        const timeB =\n          b.timestamp instanceof Date\n            ? b.timestamp.getTime()\n            : new Date(b.timestamp as string).getTime();\n        return timeA - timeB;\n      });\n\n      this.messages = conversationMessages;\n    }\n\n    this.otherParticipant =\n      conversation?.participants?.find(\n        (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n      ) || null;\n\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n\n    this.markMessagesAsRead();\n\n    if (this.conversation?.id) {\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n  }\n\n  private subscribeToConversationUpdates(conversationId: string): void {\n    const sub = this.MessageService.subscribeToConversationUpdates(\n      conversationId\n    ).subscribe({\n      next: (updatedConversation) => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages\n          ? [...updatedConversation.messages]\n          : [];\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to conversation updates lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToNewMessages(conversationId: string): void {\n    const sub = this.MessageService.subscribeToNewMessages(\n      conversationId\n    ).subscribe({\n      next: (newMessage) => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA =\n              a.timestamp instanceof Date\n                ? a.timestamp.getTime()\n                : new Date(a.timestamp as string).getTime();\n            const timeB =\n              b.timestamp instanceof Date\n                ? b.timestamp.getTime()\n                : new Date(b.timestamp as string).getTime();\n            return timeA - timeB;\n          });\n\n          setTimeout(() => this.scrollToBottom(), 100);\n\n          if (\n            newMessage.sender?.id !== this.currentUserId &&\n            newMessage.sender?._id !== this.currentUserId\n          ) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to new messages lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToTypingIndicators(conversationId: string): void {\n    const sub = this.MessageService.subscribeToTypingIndicator(\n      conversationId\n    ).subscribe({\n      next: (event) => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private markMessagesAsRead(): void {\n    const unreadMessages = this.messages.filter(\n      (msg) =>\n        !msg.isRead &&\n        (msg.receiver?.id === this.currentUserId ||\n          msg.receiver?._id === this.currentUserId)\n    );\n\n    unreadMessages.forEach((msg) => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: (error) => {},\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n\n    const validTypes = [\n      'image/jpeg',\n      'image/png',\n      'image/gif',\n      'application/pdf',\n      'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    ];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError(\n        'Invalid file type. Only images, PDFs and Word docs are allowed'\n      );\n      return;\n    }\n\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n\n  removeAttachment(): void {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n\n  private typingTimer: any;\n  private isCurrentlyTyping = false;\n  private readonly TYPING_DELAY = 500;\n  private readonly TYPING_TIMEOUT = 3000;\n\n  // Frappe\n  onTyping(): void {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n\n    const conversationId = this.conversation.id;\n    clearTimeout(this.typingTimer);\n\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {},\n        error: (error) => {},\n      });\n    }\n\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {},\n          error: (error) => {},\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n\n  // Panneaux\n  private togglePanel(panelName: string, closeOthers: boolean = true): void {\n    const panels = {\n      theme: 'showThemeSelector',\n      menu: 'showMainMenu',\n      emoji: 'showEmojiPicker',\n      notification: 'showNotificationPanel',\n      search: 'showSearchBar',\n      status: 'showStatusSelector',\n    };\n\n    const currentPanel = panels[panelName as keyof typeof panels];\n    if (currentPanel) {\n      (this as any)[currentPanel] = !(this as any)[currentPanel];\n\n      if (closeOthers && (this as any)[currentPanel]) {\n        Object.values(panels).forEach((panel) => {\n          if (panel !== currentPanel) {\n            (this as any)[panel] = false;\n          }\n        });\n      }\n    }\n  }\n\n  // Méthodes de basculement principales consolidées\n  readonly mainToggleMethods = {\n    themeSelector: () => this.togglePanel('theme'),\n    mainMenu: () => this.togglePanel('menu'),\n    emojiPicker: () => this.togglePanel('emoji'),\n  };\n\n  toggleThemeSelector = this.mainToggleMethods.themeSelector;\n  toggleMainMenu = this.mainToggleMethods.mainMenu;\n  toggleEmojiPicker = this.mainToggleMethods.emojiPicker;\n\n  changeTheme(theme: string): void {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    localStorage.setItem('chat-theme', theme);\n  }\n\n  // Méthodes toggle consolidées\n  private readonly toggleMethods = {\n    pinnedMessages: () => (this.showPinnedMessages = !this.showPinnedMessages),\n    searchBar: () => {\n      this.togglePanel('search');\n      if (!this.showSearchBar) this.clearSearch();\n    },\n    statusSelector: () => this.togglePanel('status'),\n    notificationSettings: () =>\n      (this.showNotificationSettings = !this.showNotificationSettings),\n    userStatusPanel: () =>\n      (this.showUserStatusPanel = !this.showUserStatusPanel),\n    callMinimize: () => (this.isCallMinimized = !this.isCallMinimized),\n    callHistoryPanel: () =>\n      (this.showCallHistoryPanel = !this.showCallHistoryPanel),\n    callStatsPanel: () => (this.showCallStatsPanel = !this.showCallStatsPanel),\n    voiceMessagesPanel: () =>\n      (this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel),\n  };\n\n  togglePinnedMessages = this.toggleMethods.pinnedMessages;\n  toggleSearchBar = this.toggleMethods.searchBar;\n  toggleStatusSelector = this.toggleMethods.statusSelector;\n  toggleNotificationSettings = this.toggleMethods.notificationSettings;\n  toggleUserStatusPanel = this.toggleMethods.userStatusPanel;\n  toggleCallMinimize = this.toggleMethods.callMinimize;\n  toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;\n  toggleCallStatsPanel = this.toggleMethods.callStatsPanel;\n  toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;\n\n  // Conversation - méthode utilitaire\n  private showDevelopmentFeature(featureName: string): void {\n    this.showMainMenu = false;\n    this.toastService.showInfo(`${featureName} en cours de développement`);\n  }\n\n  readonly conversationMethods = {\n    showInfo: () => this.showDevelopmentFeature('Informations de conversation'),\n    showSettings: () =>\n      this.showDevelopmentFeature('Paramètres de conversation'),\n    clear: (): void => {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à vider');\n        return;\n      }\n\n      if (\n        confirm(\n          'Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.'\n        )\n      ) {\n        this.messages = [];\n        this.showMainMenu = false;\n        this.toastService.showSuccess('Conversation vidée avec succès');\n      }\n    },\n    export: (): void => {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à exporter');\n        return;\n      }\n\n      const conversationName = this.conversation.isGroup\n        ? this.conversation.groupName || 'Groupe sans nom'\n        : this.otherParticipant?.username || 'Conversation privée';\n\n      const exportData = {\n        conversation: {\n          id: this.conversation.id,\n          name: conversationName,\n          isGroup: this.conversation.isGroup,\n          participants: this.conversation.participants,\n          createdAt: this.conversation.createdAt,\n        },\n        messages: this.messages.map((msg) => ({\n          id: msg.id,\n          content: msg.content,\n          sender: msg.sender,\n          timestamp: msg.timestamp,\n          type: msg.type,\n        })),\n        exportedAt: new Date().toISOString(),\n        exportedBy: this.currentUserId,\n      };\n\n      const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n        type: 'application/json',\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n\n      const safeFileName = conversationName\n        .replace(/[^a-z0-9]/gi, '_')\n        .toLowerCase();\n      const dateStr = new Date().toISOString().split('T')[0];\n      link.download = `conversation-${safeFileName}-${dateStr}.json`;\n\n      link.click();\n      window.URL.revokeObjectURL(url);\n\n      this.showMainMenu = false;\n      this.toastService.showSuccess('Conversation exportée avec succès');\n    },\n  };\n\n  // Template methods\n  toggleConversationInfo = this.conversationMethods.showInfo;\n  toggleConversationSettings = this.conversationMethods.showSettings;\n  clearConversation = this.conversationMethods.clear;\n  exportConversation = this.conversationMethods.export;\n\n  sendMessage(): void {\n    if (\n      (this.messageForm.invalid && !this.selectedFile) ||\n      !this.currentUserId ||\n      !this.otherParticipant?.id\n    ) {\n      return;\n    }\n\n    this.cleanup.stopTypingIndicator();\n\n    const content = this.messageForm.get('content')?.value;\n\n    const tempMessage: Message = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername,\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient',\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true,\n    };\n\n    if (this.selectedFile) {\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n\n        if (this.previewUrl) {\n          tempMessage.attachments = [\n            {\n              id: 'temp-attachment',\n              url: this.previewUrl ? this.previewUrl.toString() : '',\n              type: MessageType.IMAGE,\n              name: this.selectedFile.name,\n              size: this.selectedFile.size,\n            },\n          ];\n        }\n      }\n\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n\n    this.messages = [...this.messages, tempMessage];\n\n    const fileToSend = this.selectedFile;\n    this.messageForm.reset();\n    this.removeAttachment();\n\n    setTimeout(() => this.scrollToBottom(true), 50);\n\n    this.isUploading = true;\n\n    const sendSub = this.MessageService.sendMessage(\n      this.otherParticipant.id,\n      content,\n      fileToSend || undefined,\n      MessageType.TEXT,\n      this.conversation?.id\n    ).subscribe({\n      next: (message) => {\n        this.updateMessageState(tempMessage.id!, message);\n        this.isUploading = false;\n      },\n      error: (error) => {\n        this.updateMessageState(tempMessage.id!, null, true);\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      },\n    });\n\n    this.subscriptions.add(sendSub);\n  }\n\n  // Méthode consolidée pour mettre à jour l'état des messages\n  private updateMessageState(\n    tempId: string,\n    newMessage?: Message | null,\n    isError: boolean = false\n  ): void {\n    this.messages = this.messages.map((msg) => {\n      if (msg.id === tempId) {\n        if (newMessage) {\n          return newMessage;\n        } else if (isError) {\n          return {\n            ...msg,\n            isPending: false,\n            isError: true,\n          };\n        }\n      }\n      return msg;\n    });\n  }\n\n  // Service - méthodes optimisées\n  readonly s = {\n    formatTime: (t: string | Date | undefined) =>\n      this.MessageService.formatMessageTime(t),\n    formatActive: (t: string | Date | undefined) =>\n      this.MessageService.formatLastActive(t),\n    formatDate: (t: string | Date | undefined) =>\n      this.MessageService.formatMessageDate(t),\n    showDateHeader: (i: number) =>\n      this.MessageService.shouldShowDateHeader(this.messages, i),\n    getType: (m: Message | null | undefined) =>\n      this.MessageService.getMessageType(m),\n    hasImage: (m: Message | null | undefined) =>\n      this.MessageService.hasImage(m),\n    isVoice: (m: Message | null | undefined) =>\n      this.MessageService.isVoiceMessage(m),\n    getVoiceUrl: (m: Message | null | undefined) =>\n      this.MessageService.getVoiceMessageUrl(m),\n    getVoiceDuration: (m: Message | null | undefined) =>\n      this.MessageService.getVoiceMessageDuration(m),\n    getVoiceHeight: (i: number) => this.MessageService.getVoiceBarHeight(i),\n    formatVoice: (s: number) => this.MessageService.formatVoiceDuration(s),\n    getImageUrl: (m: Message | null | undefined) =>\n      this.MessageService.getImageUrl(m),\n    getTypeClass: (m: Message | null | undefined) =>\n      this.MessageService.getMessageTypeClass(m, this.currentUserId),\n  };\n\n  // Méthodes exposées optimisées\n  formatMessageTime = this.s.formatTime;\n  formatLastActive = this.s.formatActive;\n  formatMessageDate = this.s.formatDate;\n  shouldShowDateHeader = this.s.showDateHeader;\n  getMessageType = this.s.getType;\n  hasImage = this.s.hasImage;\n  isVoiceMessage = this.s.isVoice;\n  getVoiceMessageUrl = this.s.getVoiceUrl;\n  getVoiceMessageDuration = this.s.getVoiceDuration;\n  getVoiceBarHeight = this.s.getVoiceHeight;\n  formatVoiceDuration = this.s.formatVoice;\n  getImageUrl = this.s.getImageUrl;\n  getMessageTypeClass = this.s.getTypeClass;\n\n  // Défilement\n  onScroll(event: any): void {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n\n    if (\n      scrollTop < 50 &&\n      !this.isLoadingMore &&\n      this.conversation?.id &&\n      this.hasMoreMessages\n    ) {\n      this.showLoadingIndicator();\n\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n\n      this.isLoadingMore = true;\n\n      this.loadMoreMessages();\n\n      // Maintenir la position de défilement\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(\n              firstVisibleMessage.id\n            );\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({ block: 'center' });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n\n  // Méthode pour trouver le premier message visible dans la vue\n  private getFirstVisibleMessage(): Message | null {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length)\n      return null;\n\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find((m) => m.id === messageId) || null;\n      }\n    }\n\n    return null;\n  }\n\n  // Méthode pour trouver un élément de message par ID\n  private findMessageElement(\n    messageId: string | undefined\n  ): HTMLElement | null {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(\n      `[data-message-id=\"${messageId}\"]`\n    );\n  }\n\n  // Indicateurs de chargement consolidés\n  readonly loadingIndicatorMethods = {\n    show: () => {\n      if (!document.getElementById('message-loading-indicator')) {\n        const indicator = document.createElement('div');\n        indicator.id = 'message-loading-indicator';\n        indicator.className = 'text-center py-2 text-gray-500 text-sm';\n        indicator.innerHTML =\n          '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n        this.messagesContainer?.nativeElement?.prepend(indicator);\n      }\n    },\n    hide: () => {\n      const indicator = document.getElementById('message-loading-indicator');\n      indicator?.parentNode?.removeChild(indicator);\n    },\n  };\n\n  private showLoadingIndicator = this.loadingIndicatorMethods.show;\n  private hideLoadingIndicator = this.loadingIndicatorMethods.hide;\n\n  // Charger plus de messages\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages)\n      return;\n\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n\n    // Augmenter la page\n    this.currentPage++;\n\n    // Charger plus de messages\n    this.MessageService.getConversation(\n      this.conversation.id,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        if (\n          conversation &&\n          conversation.messages &&\n          conversation.messages.length > 0\n        ) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n\n          // Créer un Set des IDs existants\n          const existingIds = new Set(oldMessages.map((msg) => msg.id));\n\n          // Filtrer et trier les nouveaux messages\n          const newMessages = conversation.messages\n            .filter((msg) => !existingIds.has(msg.id))\n            .sort((a, b) => {\n              const timeA = new Date(a.timestamp as string).getTime();\n              const timeB = new Date(b.timestamp as string).getTime();\n              return timeA - timeB;\n            });\n\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages =\n              newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: (error) => {\n        console.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      },\n    });\n  }\n\n  // Méthode utilitaire pour comparer les timestamps\n  private isSameTimestamp(\n    timestamp1: string | Date | undefined,\n    timestamp2: string | Date | undefined\n  ): boolean {\n    if (!timestamp1 || !timestamp2) return false;\n\n    try {\n      const time1 =\n        timestamp1 instanceof Date\n          ? timestamp1.getTime()\n          : new Date(timestamp1 as string).getTime();\n      const time2 =\n        timestamp2 instanceof Date\n          ? timestamp2.getTime()\n          : new Date(timestamp2 as string).getTime();\n      return Math.abs(time1 - time2) < 1000;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  scrollToBottom(force: boolean = false): void {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom =\n          container.scrollHeight - container.clientHeight <=\n          container.scrollTop + 150;\n\n        if (force || isScrolledToBottom) {\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth',\n          });\n        }\n      });\n    } catch (err) {\n      console.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n\n  // Méthodes d'enregistrement vocal consolidées\n  readonly voiceRecordingMethods = {\n    toggle: () => {\n      this.isRecordingVoice = !this.isRecordingVoice;\n      if (!this.isRecordingVoice) this.voiceRecordingDuration = 0;\n    },\n    complete: (audioBlob: Blob) => {\n      if (!this.conversation?.id && !this.otherParticipant?.id) {\n        this.toastService.showError('No conversation or recipient selected');\n        this.isRecordingVoice = false;\n        return;\n      }\n      const receiverId = this.otherParticipant?.id || '';\n      this.MessageService.sendVoiceMessage(\n        receiverId,\n        audioBlob,\n        this.conversation?.id,\n        this.voiceRecordingDuration\n      ).subscribe({\n        next: (message) => {\n          this.isRecordingVoice = false;\n          this.voiceRecordingDuration = 0;\n          this.scrollToBottom(true);\n        },\n        error: (error) => {\n          this.toastService.showError('Failed to send voice message');\n          this.isRecordingVoice = false;\n        },\n      });\n    },\n    cancel: () => {\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n    },\n  };\n\n  toggleVoiceRecording = this.voiceRecordingMethods.toggle;\n  onVoiceRecordingComplete = this.voiceRecordingMethods.complete;\n  onVoiceRecordingCancelled = this.voiceRecordingMethods.cancel;\n\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl: string): void {\n    window.open(imageUrl, '_blank');\n  }\n\n  /**\n   * Détecte les changements après chaque vérification de la vue - Optimisé\n   */\n  ngAfterViewChecked(): void {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n\n    // Détection des changements optimisée - Suppression de la vérification inutile\n    // La détection automatique d'Angular gère déjà les messages vocaux\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji: string): void {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector(\n          '.whatsapp-input-field'\n        ) as HTMLInputElement;\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  private subscribeToNotifications(): void {\n    const notificationSub =\n      this.MessageService.subscribeToNewNotifications().subscribe({\n        next: (notification) => {\n          this.notifications.unshift(notification);\n          this.updateNotificationCount();\n\n          this.MessageService.play('notification');\n\n          if (\n            notification.type === 'NEW_MESSAGE' &&\n            notification.conversationId === this.conversation?.id\n          ) {\n            if (notification.id) {\n              this.MessageService.markAsRead([notification.id]).subscribe();\n            }\n          }\n        },\n        error: (error) => {},\n      });\n    this.subscriptions.add(notificationSub);\n\n    const notificationsListSub = this.MessageService.notifications$.subscribe({\n      next: (notifications) => {\n        this.notifications = notifications;\n        this.updateNotificationCount();\n      },\n      error: (error) => {},\n    });\n    this.subscriptions.add(notificationsListSub);\n\n    const notificationCountSub =\n      this.MessageService.notificationCount$.subscribe({\n        next: (count) => {\n          this.unreadNotificationCount = count;\n        },\n      });\n    this.subscriptions.add(notificationCountSub);\n\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: (call) => {\n        if (call) {\n          this.incomingCall = call;\n          this.showCallModal = true;\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      },\n    });\n    this.subscriptions.add(callSub);\n\n    const activeCallSub = this.MessageService.activeCall$.subscribe({\n      next: (call) => {\n        this.activeCall = call;\n        if (call) {\n          this.showActiveCallModal = true;\n          this.startCallTimerMethod();\n        } else {\n          this.showActiveCallModal = false;\n          this.stopCallTimerMethod();\n          this.resetCallStateMethod();\n        }\n      },\n    });\n    this.subscriptions.add(activeCallSub);\n\n    // S'abonner aux flux vidéo locaux\n    const localStreamSub = this.MessageService.localStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.localVideoElement) {\n          this.localVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(localStreamSub);\n\n    // S'abonner aux flux vidéo distants\n    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.remoteVideoElement) {\n          this.remoteVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(remoteStreamSub);\n  }\n\n  // Méthodes d'appel consolidées\n  readonly callMethods = {\n    initiate: (type: 'AUDIO' | 'VIDEO') => {\n      if (!this.otherParticipant?.id) return;\n      this.MessageService.initiateCall(\n        this.otherParticipant.id,\n        type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO,\n        this.conversation?.id\n      ).subscribe({\n        next: (call) => {},\n        error: () => this.toastService.showError(this.c.error),\n      });\n    },\n    accept: () => {\n      if (!this.incomingCall) return;\n      this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n        next: (call) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n          this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n          this.callQuality = 'connecting';\n          this.toastService.showSuccess('Appel connecté');\n        },\n        error: () => {\n          this.toastService.showError(this.c.error);\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n      });\n    },\n    reject: () => {\n      if (!this.incomingCall) return;\n      this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n        next: (call) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n        error: (error) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n      });\n    },\n    end: () => {\n      const sub = this.MessageService.activeCall$.subscribe((call) => {\n        if (call) {\n          this.MessageService.endCall(call.id).subscribe({\n            next: (call) => {},\n            error: (error) => {},\n          });\n        }\n      });\n      sub.unsubscribe();\n    },\n  };\n\n  initiateCall = this.callMethods.initiate;\n  acceptCall = this.callMethods.accept;\n  rejectCall = this.callMethods.reject;\n  endCall = this.callMethods.end;\n\n  // Méthodes de contrôle d'appel consolidées\n  readonly callControlMethods = {\n    toggleMute: () => {\n      this.isCallMuted = !this.isCallMuted;\n      this.callControlMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé'\n      );\n    },\n    toggleVideo: () => {\n      this.isVideoEnabled = !this.isVideoEnabled;\n      this.callControlMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n      );\n    },\n    updateMedia: () => {\n      this.MessageService.toggleMedia(\n        this.activeCall?.id,\n        !this.isCallMuted,\n        this.isVideoEnabled\n      ).subscribe({\n        next: () => {},\n        error: (error) =>\n          console.error('Erreur lors de la mise à jour des médias:', error),\n      });\n    },\n  };\n\n  toggleCallMute = this.callControlMethods.toggleMute;\n  toggleCallVideo = this.callControlMethods.toggleVideo;\n  private updateCallMedia = this.callControlMethods.updateMedia;\n\n  // Méthodes de timer consolidées\n  readonly timerMethods = {\n    startCallTimer: () => {\n      this.callDuration = 0;\n      this.callTimer = setInterval(() => {\n        this.callDuration++;\n        if (this.callDuration === 3 && this.callQuality === 'connecting') {\n          this.callQuality = 'excellent';\n        }\n      }, 1000);\n    },\n    stopCallTimer: () => {\n      if (this.callTimer) {\n        clearInterval(this.callTimer);\n        this.callTimer = null;\n      }\n    },\n    resetCallState: () => (this.callDuration = 0),\n  };\n\n  private startCallTimerMethod = this.timerMethods.startCallTimer;\n  private stopCallTimerMethod = this.timerMethods.stopCallTimer;\n  private resetCallStateMethod = this.timerMethods.resetCallState;\n\n  // Notifications\n\n  // Méthode de basculement de notification consolidée\n  readonly notificationToggleMethod = {\n    togglePanel: () => {\n      this.togglePanel('notification');\n      if (this.showNotificationPanel) {\n        this.loadNotifications();\n      }\n    },\n  };\n\n  toggleNotificationPanel = this.notificationToggleMethod.togglePanel;\n\n  /**\n   * Charge les notifications\n   */\n  loadNotifications(refresh: boolean = false): void {\n    const loadSub = this.MessageService.getNotifications(\n      refresh,\n      1,\n      20\n    ).subscribe({\n      next: (notifications) => {\n        if (refresh) {\n          this.notifications = notifications;\n        } else {\n          this.notifications = [...this.notifications, ...notifications];\n        }\n\n        this.updateNotificationCount();\n      },\n      error: (error) => {\n        this.toastService.showError(\n          'Erreur lors du chargement des notifications'\n        );\n      },\n    });\n\n    this.subscriptions.add(loadSub);\n  }\n\n  // Méthodes de notification consolidées\n  readonly notificationMethods = {\n    loadMore: () => this.loadNotifications(),\n    updateCount: () =>\n      (this.unreadNotificationCount = this.notifications.filter(\n        (n) => !n.isRead\n      ).length),\n    getFiltered: () => this.notifications,\n    toggleSelection: (notificationId: string) => {\n      if (this.selectedNotifications.has(notificationId)) {\n        this.selectedNotifications.delete(notificationId);\n      } else {\n        this.selectedNotifications.add(notificationId);\n      }\n    },\n    toggleSelectAll: () => {\n      const filteredNotifications = this.notificationMethods.getFiltered();\n      const allSelected = filteredNotifications.every((n) =>\n        this.selectedNotifications.has(n.id)\n      );\n      if (allSelected) {\n        filteredNotifications.forEach((n) =>\n          this.selectedNotifications.delete(n.id)\n        );\n      } else {\n        filteredNotifications.forEach((n) =>\n          this.selectedNotifications.add(n.id)\n        );\n      }\n    },\n    areAllSelected: () => {\n      const filteredNotifications = this.notificationMethods.getFiltered();\n      return (\n        filteredNotifications.length > 0 &&\n        filteredNotifications.every((n) => this.selectedNotifications.has(n.id))\n      );\n    },\n  };\n\n  loadMoreNotifications = this.notificationMethods.loadMore;\n  private updateNotificationCount = this.notificationMethods.updateCount;\n  getFilteredNotifications = this.notificationMethods.getFiltered;\n  toggleNotificationSelection = this.notificationMethods.toggleSelection;\n  toggleSelectAllNotifications = this.notificationMethods.toggleSelectAll;\n  areAllNotificationsSelected = this.notificationMethods.areAllSelected;\n\n  // Méthodes de marquage consolidées\n  readonly markingMethods = {\n    markSelected: () => {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      this.markingMethods.markAsRead(selectedIds, () => {\n        this.selectedNotifications.clear();\n        this.toastService.showSuccess(\n          `${selectedIds.length} notification(s) marquée(s) comme lue(s)`\n        );\n      });\n    },\n    markAll: () => {\n      const unreadNotifications = this.notifications.filter((n) => !n.isRead);\n      if (unreadNotifications.length === 0) {\n        this.toastService.showInfo('Aucune notification non lue');\n        return;\n      }\n      const unreadIds = unreadNotifications.map((n) => n.id);\n      this.markingMethods.markAsRead(unreadIds, () => {\n        this.toastService.showSuccess(\n          'Toutes les notifications ont été marquées comme lues'\n        );\n      });\n    },\n    markAsRead: (ids: string[], onSuccess: () => void) => {\n      const markSub = this.MessageService.markAsRead(ids).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.map((n) =>\n            ids.includes(n.id) ? { ...n, isRead: true, readAt: new Date() } : n\n          );\n          this.updateNotificationCount();\n          onSuccess();\n        },\n        error: (error) => {\n          this.toastService.showError(\n            'Erreur lors du marquage des notifications'\n          );\n        },\n      });\n      this.subscriptions.add(markSub);\n    },\n  };\n\n  markSelectedAsRead = this.markingMethods.markSelected;\n  markAllAsRead = this.markingMethods.markAll;\n\n  // Méthodes de suppression de notifications consolidées\n  readonly notificationDeleteMethods = {\n    showDeleteSelectedConfirmation: () => {\n      if (this.selectedNotifications.size === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      this.showDeleteConfirmModal = true;\n    },\n    deleteSelected: () => {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) return;\n      this.isDeletingNotifications = true;\n      this.showDeleteConfirmModal = false;\n      const deleteSub = this.MessageService.deleteMultipleNotifications(\n        selectedIds\n      ).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.filter(\n            (n) => !selectedIds.includes(n.id)\n          );\n          this.selectedNotifications.clear();\n          this.updateNotificationCount();\n          this.isDeletingNotifications = false;\n          this.toastService.showSuccess(\n            `${result.count} notification(s) supprimée(s)`\n          );\n        },\n        error: (error) => {\n          this.isDeletingNotifications = false;\n          this.toastService.showError(\n            'Erreur lors de la suppression des notifications'\n          );\n        },\n      });\n      this.subscriptions.add(deleteSub);\n    },\n    deleteOne: (notificationId: string) => {\n      const deleteSub = this.MessageService.deleteNotification(\n        notificationId\n      ).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.filter(\n            (n) => n.id !== notificationId\n          );\n          this.selectedNotifications.delete(notificationId);\n          this.updateNotificationCount();\n          this.toastService.showSuccess('Notification supprimée');\n        },\n        error: (error) => {\n          this.toastService.showError(\n            'Erreur lors de la suppression de la notification'\n          );\n        },\n      });\n      this.subscriptions.add(deleteSub);\n    },\n    deleteAll: () => {\n      if (this.notifications.length === 0) return;\n      if (\n        !confirm(\n          'Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.'\n        )\n      ) {\n        return;\n      }\n      this.isDeletingNotifications = true;\n      const deleteAllSub =\n        this.MessageService.deleteAllNotifications().subscribe({\n          next: (result) => {\n            this.notifications = [];\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(\n              `${result.count} notifications supprimées avec succès`\n            );\n          },\n          error: (error) => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError(\n              'Erreur lors de la suppression de toutes les notifications'\n            );\n          },\n        });\n      this.subscriptions.add(deleteAllSub);\n    },\n    cancel: () => (this.showDeleteConfirmModal = false),\n  };\n\n  showDeleteSelectedConfirmation =\n    this.notificationDeleteMethods.showDeleteSelectedConfirmation;\n  deleteSelectedNotifications = this.notificationDeleteMethods.deleteSelected;\n  deleteNotification = this.notificationDeleteMethods.deleteOne;\n  deleteAllNotifications = this.notificationDeleteMethods.deleteAll;\n  cancelDeleteNotifications = this.notificationDeleteMethods.cancel;\n\n  // Méthodes utilitaires de notification consolidées\n  readonly notificationUtilMethods = {\n    formatDate: (date: string | Date | undefined) =>\n      this.MessageService.formatLastActive(date),\n    getIcon: (type: string) =>\n      this.c.notifications[type as keyof typeof this.c.notifications]?.icon ||\n      'fas fa-bell',\n    getColor: (type: string) =>\n      this.c.notifications[type as keyof typeof this.c.notifications]?.color ||\n      'text-cyan-500',\n    trackById: (index: number, notification: any) =>\n      this.c.trackById(0, notification),\n  };\n\n  formatNotificationDate = this.notificationUtilMethods.formatDate;\n  getNotificationIcon = this.notificationUtilMethods.getIcon;\n  getNotificationColor = this.notificationUtilMethods.getColor;\n  trackByNotificationId = this.notificationUtilMethods.trackById;\n\n  /**\n   * S'abonne au statut utilisateur en temps réel\n   */\n  private subscribeToUserStatus(): void {\n    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n      next: (user: User) => {\n        this.handleUserStatusUpdate(user);\n      },\n      error: (error) => {\n        // Error handled silently\n      },\n    });\n\n    this.subscriptions.add(statusSub);\n  }\n\n  /**\n   * Gère la mise à jour du statut d'un utilisateur\n   */\n  private handleUserStatusUpdate(user: User): void {\n    if (!user.id) return;\n\n    if (user.isOnline) {\n      this.onlineUsers.set(user.id, user);\n    } else {\n      this.onlineUsers.delete(user.id);\n    }\n\n    if (this.otherParticipant && this.otherParticipant.id === user.id) {\n      this.otherParticipant = { ...this.otherParticipant, ...user };\n    }\n  }\n\n  /**\n   * Initialise le statut de l'utilisateur actuel\n   */\n  private initializeUserStatus(): void {\n    if (!this.currentUserId) return;\n\n    const setOnlineSub = this.MessageService.setUserOnline(\n      this.currentUserId\n    ).subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = 'online';\n        this.lastActivityTime = new Date();\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          \"Erreur lors de l'initialisation du statut\",\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(setOnlineSub);\n  }\n\n  /**\n   * Démarre le suivi d'activité automatique\n   */\n  private startActivityTracking(): void {\n    const events = [\n      'mousedown',\n      'mousemove',\n      'keypress',\n      'scroll',\n      'touchstart',\n      'click',\n    ];\n\n    events.forEach((event) => {\n      document.addEventListener(event, this.onUserActivity.bind(this), true);\n    });\n  }\n\n  /**\n   * Gère l'activité de l'utilisateur\n   */\n  private onUserActivity(): void {\n    this.lastActivityTime = new Date();\n\n    // Réinitialiser le timer\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n\n    // Remettre en ligne si absent\n    if (\n      this.currentUserStatus === 'away' ||\n      this.currentUserStatus === 'offline'\n    ) {\n      this.updateUserStatus('online');\n    }\n\n    // Programmer la mise en absence\n    this.autoAwayTimeout = setTimeout(() => {\n      if (this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      }\n    }, this.c.delays.away);\n  }\n\n  /**\n   * Met à jour le statut de l'utilisateur\n   */\n  updateUserStatus(status: string): void {\n    if (!this.currentUserId) return;\n\n    const previousStatus = this.currentUserStatus;\n\n    let updateObservable;\n    if (status === 'online') {\n      updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n    } else {\n      updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n    }\n\n    const updateSub = updateObservable.subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = status;\n\n        if (status !== previousStatus) {\n          const statusText = this.getStatusText(status);\n          this.toastService.showInfo(`Statut : ${statusText}`);\n        }\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          'Erreur lors de la mise à jour du statut',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(updateSub);\n  }\n\n  /**\n   * Charge la liste des utilisateurs en ligne\n   */\n  loadOnlineUsers(): void {\n    const usersSub = this.MessageService.getAllUsers(\n      false,\n      undefined,\n      1,\n      50,\n      'username',\n      'asc',\n      true\n    ).subscribe({\n      next: (users: User[]) => {\n        users.forEach((user) => {\n          if (user.isOnline && user.id) {\n            this.onlineUsers.set(user.id, user);\n          }\n        });\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          'Erreur lors du chargement des utilisateurs en ligne',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(usersSub);\n  }\n\n  // Méthodes de gestion des panneaux consolidées\n  readonly panelMethods = {\n    getActivePanels: () => {\n      const panels = [];\n      if (this.showUserStatusPanel)\n        panels.push({\n          key: 'userStatus',\n          title: 'Utilisateurs',\n          icon: 'fas fa-users',\n          closeAction: () => (this.showUserStatusPanel = false),\n        });\n      if (this.showCallHistoryPanel)\n        panels.push({\n          key: 'callHistory',\n          title: 'Historique des appels',\n          icon: 'fas fa-history',\n          closeAction: () => (this.showCallHistoryPanel = false),\n        });\n      if (this.showCallStatsPanel)\n        panels.push({\n          key: 'callStats',\n          title: \"Statistiques d'appels\",\n          icon: 'fas fa-chart-bar',\n          closeAction: () => (this.showCallStatsPanel = false),\n        });\n      if (this.showVoiceMessagesPanel)\n        panels.push({\n          key: 'voiceMessages',\n          title: 'Messages vocaux',\n          icon: 'fas fa-microphone',\n          closeAction: () => (this.showVoiceMessagesPanel = false),\n        });\n      return panels;\n    },\n    getStatusOptions: () =>\n      Object.entries(this.c.status).map(([key, config]) => ({\n        key,\n        ...config,\n      })),\n    getThemeOptions: () => this.c.themes,\n  };\n\n  getActivePanels = this.panelMethods.getActivePanels;\n  getStatusOptions = this.panelMethods.getStatusOptions;\n  getThemeOptions = this.panelMethods.getThemeOptions;\n\n  // Méthodes de statut simplifiées\n  readonly statusMethods = {\n    getText: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.text || 'Inconnu',\n    getColor: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.color ||\n      'text-gray-400',\n    getIcon: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.icon ||\n      'fas fa-question-circle',\n  };\n\n  getStatusText = this.statusMethods.getText;\n  getStatusColor = this.statusMethods.getColor;\n  getStatusIcon = this.statusMethods.getIcon;\n\n  // Méthodes utilitaires consolidées\n  readonly utilityMethods = {\n    formatLastSeen: (lastActive: Date | null) =>\n      lastActive\n        ? this.MessageService.formatLastActive(lastActive)\n        : 'Jamais vu',\n    getOnlineUsersCount: () =>\n      Array.from(this.onlineUsers.values()).filter((user) => user.isOnline)\n        .length,\n    getFilteredUsers: () => Array.from(this.onlineUsers.values()),\n    setStatusFilter: (filter: string) => (this.statusFilterType = filter),\n  };\n\n  formatLastSeen = this.utilityMethods.formatLastSeen;\n  getOnlineUsersCount = this.utilityMethods.getOnlineUsersCount;\n  setStatusFilter = this.utilityMethods.setStatusFilter;\n  getFilteredUsers = this.utilityMethods.getFilteredUsers;\n\n  // Méthodes de réponse et transfert consolidées\n  readonly replyForwardMethods = {\n    startReply: (message: any) => (this.replyingToMessage = message),\n    cancelReply: () => (this.replyingToMessage = null),\n    openForwardModal: (message: any) => {\n      this.forwardingMessage = message;\n      this.showForwardModal = true;\n    },\n    closeForwardModal: () => {\n      this.showForwardModal = false;\n      this.forwardingMessage = null;\n      this.selectedConversations = [];\n    },\n  };\n\n  startReplyToMessage = this.replyForwardMethods.startReply;\n  cancelReply = this.replyForwardMethods.cancelReply;\n  openForwardModal = this.replyForwardMethods.openForwardModal;\n  closeForwardModal = this.replyForwardMethods.closeForwardModal;\n\n  // Messages - méthodes consolidées\n  readonly messageMethods = {\n    getPinIcon: (message: any) =>\n      this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack',\n    getPinDisplayText: (message: any) =>\n      this.isMessagePinned(message) ? 'Désépingler' : 'Épingler',\n    canEditMessage: (message: any) => message.sender?.id === this.currentUserId,\n    isMessagePinned: (message: any) => message.isPinned || false,\n  };\n\n  getPinIcon = this.messageMethods.getPinIcon;\n  getPinDisplayText = this.messageMethods.getPinDisplayText;\n  canEditMessage = this.messageMethods.canEditMessage;\n  isMessagePinned = this.messageMethods.isMessagePinned;\n\n  // Méthodes d'édition consolidées\n  readonly editMethods = {\n    startEditMessage: (message: any) => {\n      this.editingMessageId = message.id;\n      this.editingContent = message.content;\n    },\n    cancelEditMessage: () => {\n      this.editingMessageId = null;\n      this.editingContent = '';\n    },\n    saveEditMessage: (messageId: string) =>\n      this.editMethods.cancelEditMessage(),\n    onEditKeyPress: (event: KeyboardEvent, messageId: string) => {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.editMethods.saveEditMessage(messageId);\n      } else if (event.key === 'Escape') {\n        this.editMethods.cancelEditMessage();\n      }\n    },\n  };\n\n  startEditMessage = this.editMethods.startEditMessage;\n  cancelEditMessage = this.editMethods.cancelEditMessage;\n  saveEditMessage = this.editMethods.saveEditMessage;\n  onEditKeyPress = this.editMethods.onEditKeyPress;\n\n  togglePinMessage(message: any): void {\n    this.isPinning[message.id] = true;\n    setTimeout(() => {\n      this.isPinning[message.id] = false;\n      this.showPinConfirm[message.id] = false;\n    }, 1000);\n  }\n\n  // Utilitaires d'appel consolidées\n  readonly callUtilities = {\n    getCallStatusColor: (status: string) =>\n      this.c.callStatusColors[status as keyof typeof this.c.callStatusColors] ||\n      'text-gray-500',\n    getCallTypeIcon: (type: string) =>\n      type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',\n    formatCallDuration: (duration: number) => {\n      if (!duration) return '00:00';\n      const minutes = Math.floor(duration / 60);\n      const seconds = duration % 60;\n      return `${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    },\n    formatCallDate: (timestamp: string | Date | undefined) =>\n      this.MessageService.formatMessageDate(timestamp),\n  };\n\n  getCallStatusColor = this.callUtilities.getCallStatusColor;\n  getCallTypeIcon = this.callUtilities.getCallTypeIcon;\n  formatCallDuration = this.callUtilities.formatCallDuration;\n  formatCallDate = this.callUtilities.formatCallDate;\n\n  // Méthodes d'événements consolidées\n  readonly eventMethods = {\n    onDocumentClick: (event: Event) => {\n      const target = event.target as HTMLElement;\n      const closeConfigs = [\n        {\n          selectors: ['.theme-selector-menu', '.btn-theme'],\n          property: 'showThemeSelector',\n        },\n        {\n          selectors: ['.emoji-picker', '.btn-emoji'],\n          property: 'showEmojiPicker',\n        },\n      ];\n\n      closeConfigs.forEach((config) => {\n        const isClickOutside = !config.selectors.some((selector) =>\n          target.closest(selector)\n        );\n        if (isClickOutside) {\n          (this as any)[config.property] = false;\n        }\n      });\n    },\n    confirmDeleteMessage: (messageId: string) => {\n      this.showDeleteConfirm[messageId] = false;\n    },\n  };\n\n  onDocumentClick = this.eventMethods.onDocumentClick;\n  confirmDeleteMessage = this.eventMethods.confirmDeleteMessage;\n\n  // Méthodes de réaction consolidées\n  readonly reactionMethods = {\n    getUniqueReactions: (message: any) => message.reactions || [],\n    onReactionClick: (messageId: string, emoji: string) => {\n      // Implémentation des réactions\n    },\n    hasUserReacted: (message: any, emoji: string) => false,\n  };\n\n  getUniqueReactions = this.reactionMethods.getUniqueReactions;\n  onReactionClick = this.reactionMethods.onReactionClick;\n  hasUserReacted = this.reactionMethods.hasUserReacted;\n\n  // Méthodes de conversation consolidées\n  readonly conversationSelectionMethods = {\n    areAllSelected: () =>\n      this.selectedConversations.length === this.availableConversations.length,\n    selectAll: () =>\n      (this.selectedConversations = this.availableConversations.map(\n        (c) => c.id\n      )),\n    deselectAll: () => (this.selectedConversations = []),\n    toggle: (conversationId: string) => {\n      const index = this.selectedConversations.indexOf(conversationId);\n      index > -1\n        ? this.selectedConversations.splice(index, 1)\n        : this.selectedConversations.push(conversationId);\n    },\n    isSelected: (conversationId: string) =>\n      this.selectedConversations.includes(conversationId),\n    getDisplayImage: (conversation: any) =>\n      conversation.image || 'assets/images/default-avatar.png',\n    getDisplayName: (conversation: any) => conversation.name || 'Conversation',\n    forwardMessage: () => {\n      this.isForwarding = true;\n      setTimeout(() => {\n        this.isForwarding = false;\n        this.closeForwardModal();\n      }, 1000);\n    },\n  };\n\n  areAllConversationsSelected =\n    this.conversationSelectionMethods.areAllSelected;\n  selectAllConversations = this.conversationSelectionMethods.selectAll;\n  deselectAllConversations = this.conversationSelectionMethods.deselectAll;\n  toggleConversationSelection = this.conversationSelectionMethods.toggle;\n  isConversationSelected = this.conversationSelectionMethods.isSelected;\n  getConversationDisplayImage =\n    this.conversationSelectionMethods.getDisplayImage;\n  getConversationDisplayName = this.conversationSelectionMethods.getDisplayName;\n  forwardMessage = this.conversationSelectionMethods.forwardMessage;\n\n  // Méthodes de notification simplifiées consolidées\n  readonly simpleNotificationMethods = {\n    onCallMouseMove: () => (this.showCallControls = true),\n    saveNotificationSettings: () => {},\n    setNotificationFilter: (filter: string) =>\n      (this.notificationFilter = filter),\n  };\n\n  onCallMouseMove = this.simpleNotificationMethods.onCallMouseMove;\n  saveNotificationSettings =\n    this.simpleNotificationMethods.saveNotificationSettings;\n  setNotificationFilter = this.simpleNotificationMethods.setNotificationFilter;\n\n  // Méthodes de recherche consolidées\n  readonly searchMethods = {\n    onInput: (event: any) => {\n      this.searchQuery = event.target.value;\n      this.searchQuery.length >= 2\n        ? this.searchMethods.perform()\n        : this.searchMethods.clear();\n    },\n    onKeyPress: (event: KeyboardEvent) => {\n      if (event.key === 'Enter') {\n        this.searchMethods.perform();\n      } else if (event.key === 'Escape') {\n        this.searchMethods.clear();\n      }\n    },\n    perform: () => {\n      this.isSearching = true;\n      this.searchMode = true;\n      setTimeout(() => {\n        this.searchResults = this.messages.filter((m) =>\n          m.content?.toLowerCase().includes(this.searchQuery.toLowerCase())\n        );\n        this.isSearching = false;\n      }, 500);\n    },\n    clear: () => {\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.isSearching = false;\n      this.searchMode = false;\n    },\n  };\n\n  onSearchInput = this.searchMethods.onInput;\n  onSearchKeyPress = this.searchMethods.onKeyPress;\n  performSearch = this.searchMethods.perform;\n  clearSearch = this.searchMethods.clear;\n\n  // Méthodes utilitaires finales consolidées\n  readonly finalUtilityMethods = {\n    navigateToMessage: (messageId: string) => {\n      // Navigation vers un message spécifique\n    },\n    scrollToPinnedMessage: (messageId: string) => {\n      // Défilement vers un message épinglé\n    },\n    getPinnedMessagesCount: () => this.pinnedMessages.length,\n  };\n\n  navigateToMessage = this.finalUtilityMethods.navigateToMessage;\n  scrollToPinnedMessage = this.finalUtilityMethods.scrollToPinnedMessage;\n  getPinnedMessagesCount = this.finalUtilityMethods.getPinnedMessagesCount;\n\n  // Méthodes de recherche et réaction consolidées\n  readonly searchAndReactionMethods = {\n    highlightSearchTerms: (content: string, query: string) => {\n      if (!query) return content;\n      const regex = new RegExp(`(${query})`, 'gi');\n      return content.replace(regex, '<mark>$1</mark>');\n    },\n    toggleReactionPicker: (messageId: string) =>\n      (this.showReactionPicker[messageId] =\n        !this.showReactionPicker[messageId]),\n    reactToMessage: (messageId: string, emoji: string) =>\n      (this.showReactionPicker[messageId] = false),\n    toggleMessageOptions: (messageId: string) =>\n      (this.showMessageOptions[messageId] =\n        !this.showMessageOptions[messageId]),\n  };\n\n  highlightSearchTerms = this.searchAndReactionMethods.highlightSearchTerms;\n  toggleReactionPicker = this.searchAndReactionMethods.toggleReactionPicker;\n  reactToMessage = this.searchAndReactionMethods.reactToMessage;\n  toggleMessageOptions = this.searchAndReactionMethods.toggleMessageOptions;\n\n  // Confirmations consolidées\n  readonly confirmationMethods = {\n    showPinConfirmation: (messageId: string) =>\n      (this.showPinConfirm[messageId] = true),\n    cancelPinConfirmation: (messageId: string) =>\n      (this.showPinConfirm[messageId] = false),\n    showDeleteConfirmation: (messageId: string) =>\n      (this.showDeleteConfirm[messageId] = true),\n    cancelDeleteMessage: (messageId: string) =>\n      (this.showDeleteConfirm[messageId] = false),\n  };\n\n  showPinConfirmation = this.confirmationMethods.showPinConfirmation;\n  cancelPinConfirmation = this.confirmationMethods.cancelPinConfirmation;\n  showDeleteConfirmation = this.confirmationMethods.showDeleteConfirmation;\n  cancelDeleteMessage = this.confirmationMethods.cancelDeleteMessage;\n\n  // Méthodes de nettoyage optimisées\n  readonly cleanup = {\n    clearTimeouts: () =>\n      [this.typingTimeout, this.autoAwayTimeout, this.callTimer].forEach(\n        (t) => t && clearTimeout(t)\n      ),\n    setUserOffline: () =>\n      this.currentUserId &&\n      this.MessageService.setUserOffline(this.currentUserId).subscribe(),\n    stopTypingIndicator: () => {\n      if (this.isCurrentlyTyping && this.conversation?.id) {\n        this.isCurrentlyTyping = false;\n        clearTimeout(this.typingTimer);\n        this.MessageService.stopTyping(this.conversation.id).subscribe({\n          next: () => {},\n          error: (error) => {},\n        });\n      }\n    },\n  };\n\n  ngOnDestroy(): void {\n    this.cleanup.clearTimeouts();\n    this.cleanup.setUserOffline();\n    this.cleanup.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n}\n", "<div class=\"chat-container\">\n  <!-- En-tête -->\n  <div class=\"chat-header\">\n    <button (click)=\"goBackToConversations()\" class=\"action-btn\">\n      <i class=\"fas fa-arrow-left\"></i>\n    </button>\n\n    <img\n      [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n      alt=\"User avatar\"\n      class=\"user-avatar\"\n    />\n\n    <div class=\"user-info\" *ngIf=\"otherParticipant\">\n      <h3>{{ otherParticipant.username }}</h3>\n      <div class=\"user-status\">\n        {{\n          otherParticipant.isOnline\n            ? \"En ligne\"\n            : formatLastActive(otherParticipant.lastActive)\n        }}\n      </div>\n    </div>\n\n    <div class=\"whatsapp-actions\">\n      <!-- Boutons d'action consolidés -->\n      <ng-container *ngFor=\"let action of getHeaderActions()\">\n        <button\n          [class]=\"'whatsapp-action-button ' + action.class\"\n          [ngClass]=\"\n            action.activeClass && action.isActive ? action.activeClass : {}\n          \"\n          [title]=\"action.title\"\n          (click)=\"action.onClick()\"\n        >\n          <i [class]=\"action.icon\"></i>\n          <!-- Badge universel -->\n          <span\n            *ngIf=\"action.badge && action.badge.count > 0\"\n            [class]=\"\n              'absolute -top-2 -right-2 min-w-[20px] h-5 px-1.5 text-white text-xs rounded-md flex items-center justify-center font-bold shadow-lg border border-white/20 ' +\n              action.badge.class\n            \"\n            [ngClass]=\"{ 'animate-pulse': action.badge.animate }\"\n          >\n            {{ action.badge.count > 99 ? \"99+\" : action.badge.count }}\n          </span>\n        </button>\n      </ng-container>\n\n      <!-- Bouton du statut utilisateur -->\n      <div class=\"relative\">\n        <button\n          (click)=\"toggleStatusSelector()\"\n          class=\"whatsapp-action-button relative\"\n          [ngClass]=\"{\n            'text-[#4f5fad] dark:text-[#6d78c9]': showStatusSelector\n          }\"\n          title=\"Statut utilisateur\"\n        >\n          <i\n            [class]=\"getStatusIcon(currentUserStatus)\"\n            [ngClass]=\"getStatusColor(currentUserStatus)\"\n          ></i>\n          <!-- Indicateur de mise à jour -->\n          <span\n            *ngIf=\"isUpdatingStatus\"\n            class=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"\n          ></span>\n        </button>\n\n        <!-- Menu déroulant du statut -->\n        <div\n          *ngIf=\"showStatusSelector\"\n          class=\"absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-3 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] bg-gradient-to-r from-[#4f5fad]/10 to-[#6d78c9]/10\"\n          >\n            <div class=\"flex items-center justify-between\">\n              <span>Statut actuel</span>\n              <span\n                class=\"font-medium\"\n                [ngClass]=\"getStatusColor(currentUserStatus)\"\n              >\n                {{ getStatusText(currentUserStatus) }}\n              </span>\n            </div>\n          </div>\n\n          <div class=\"p-1\">\n            <!-- Boutons de statut consolidés avec *ngFor -->\n            <button\n              *ngFor=\"let status of getStatusOptions()\"\n              (click)=\"updateUserStatus(status.key); toggleStatusSelector()\"\n              [disabled]=\"isUpdatingStatus\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n              [ngClass]=\"{\n                'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20':\n                  currentUserStatus === status.key\n              }\"\n            >\n              <div class=\"flex items-center\">\n                <i\n                  [class]=\"status.icon + ' ' + status.color + ' mr-3 text-xs'\"\n                ></i>\n                <div>\n                  <div class=\"font-medium\">{{ status.label }}</div>\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n                    {{ status.description }}\n                  </div>\n                </div>\n              </div>\n            </button>\n          </div>\n\n          <div class=\"border-t border-[#edf1f4]/50 dark:border-[#2a2a2a] p-1\">\n            <button\n              (click)=\"toggleUserStatusPanel(); toggleStatusSelector()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i\n                  class=\"fas fa-users text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xs\"\n                ></i>\n                <div>\n                  <div class=\"font-medium\">Voir tous les utilisateurs</div>\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n                    {{ getOnlineUsersCount() }} en ligne\n                  </div>\n                </div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Sélecteur de thème -->\n      <div class=\"relative\">\n        <button\n          (click)=\"toggleThemeSelector()\"\n          class=\"whatsapp-action-button btn-theme\"\n        >\n          <i class=\"fas fa-palette\"></i>\n        </button>\n\n        <!-- Menu déroulant des thèmes -->\n        <div\n          *ngIf=\"showThemeSelector\"\n          class=\"theme-selector-menu absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            Choisir un thème\n          </div>\n          <div class=\"p-1\">\n            <!-- Boutons de thème consolidés avec *ngFor -->\n            <a\n              *ngFor=\"let theme of getThemeOptions()\"\n              href=\"javascript:void(0)\"\n              (click)=\"changeTheme(theme.key)\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md transition-colors\"\n              [ngClass]=\"\n                'hover:bg-' +\n                theme.hoverColor +\n                '/10 dark:hover:bg-' +\n                theme.hoverColor +\n                '/10'\n              \"\n            >\n              <div class=\"flex items-center\">\n                <div\n                  [ngClass]=\"'w-4 h-4 rounded-full bg-' + theme.color + ' mr-2'\"\n                ></div>\n                <div>{{ theme.label }}</div>\n              </div>\n            </a>\n          </div>\n        </div>\n      </div>\n\n      <!-- Bouton menu principal -->\n      <div class=\"relative\">\n        <button\n          (click)=\"toggleMainMenu()\"\n          class=\"whatsapp-action-button btn-menu\"\n          title=\"Menu principal\"\n        >\n          <i class=\"fas fa-ellipsis-v\"></i>\n        </button>\n\n        <!-- Menu déroulant principal -->\n        <div\n          *ngIf=\"showMainMenu\"\n          class=\"absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            Options de conversation\n          </div>\n          <div class=\"p-1\">\n            <button\n              (click)=\"clearConversation()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-red-500/10 dark:hover:bg-red-500/10 transition-colors text-red-600 dark:text-red-400\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-trash mr-3 text-xs\"></i>\n                <div>Vider la conversation</div>\n              </div>\n            </button>\n            <button\n              (click)=\"exportConversation()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-download mr-3 text-xs\"></i>\n                <div>Exporter la conversation</div>\n              </div>\n            </button>\n            <button\n              (click)=\"toggleConversationInfo()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-info-circle mr-3 text-xs\"></i>\n                <div>Informations</div>\n              </div>\n            </button>\n            <button\n              (click)=\"toggleConversationSettings()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-cog mr-3 text-xs\"></i>\n                <div>Paramètres</div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Zone des messages -->\n  <div class=\"messages-area\" #messagesContainer>\n    <div\n      *ngIf=\"loading\"\n      style=\"text-align: center; padding: 2rem; color: #00f7ff\"\n    >\n      <i class=\"fas fa-spinner fa-spin\"></i> Chargement...\n    </div>\n\n    <div\n      *ngIf=\"!loading && messages.length === 0\"\n      style=\"text-align: center; padding: 2rem; color: #888\"\n    >\n      <i\n        class=\"fas fa-comments\"\n        style=\"font-size: 3rem; margin-bottom: 1rem; opacity: 0.5\"\n      ></i>\n      <p>Aucun message pour le moment</p>\n      <p style=\"font-size: 0.9rem\">Commencez la conversation !</p>\n    </div>\n\n    <div\n      *ngFor=\"let message of messages\"\n      class=\"message\"\n      [ngClass]=\"{\n        'current-user': message.sender?.id === currentUserId,\n        'other-user': message.sender?.id !== currentUserId\n      }\"\n    >\n      <div class=\"message-bubble\">\n        <div>{{ message.content }}</div>\n        <div style=\"font-size: 0.7rem; opacity: 0.7; margin-top: 0.5rem\">\n          {{ formatMessageTime(message.timestamp) }}\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Zone de saisie -->\n  <div class=\"input-area\">\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"input-form\"\n    >\n      <input\n        formControlName=\"content\"\n        placeholder=\"Tapez votre message...\"\n        class=\"message-input\"\n        [disabled]=\"!otherParticipant\"\n      />\n      <button\n        type=\"submit\"\n        class=\"send-btn\"\n        [disabled]=\"messageForm.invalid || !otherParticipant\"\n      >\n        <i class=\"fas fa-paper-plane\"></i>\n      </button>\n    </form>\n  </div>\n\n  <!-- Panneau de notifications -->\n  <div class=\"side-panel\" [ngClass]=\"{ open: showNotificationPanel }\">\n    <div class=\"panel-header\">\n      <h3>Notifications</h3>\n      <button class=\"close-btn\" (click)=\"toggleNotificationPanel()\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n    <div\n      *ngIf=\"notifications.length === 0\"\n      style=\"padding: 1rem; text-align: center; color: #888\"\n    >\n      <i\n        class=\"fas fa-bell-slash\"\n        style=\"font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.5\"\n      ></i>\n      <p>Aucune notification</p>\n    </div>\n    <div\n      *ngFor=\"let notification of notifications\"\n      style=\"padding: 0.5rem; border-bottom: 1px solid rgba(0, 247, 255, 0.1)\"\n    >\n      <p style=\"margin: 0; font-size: 0.9rem\">{{ notification.content }}</p>\n      <small style=\"opacity: 0.7\">{{\n        formatMessageTime(notification.timestamp)\n      }}</small>\n    </div>\n  </div>\n\n  <!-- Barre de recherche -->\n  <div\n    *ngIf=\"showSearchBar\"\n    style=\"\n      position: absolute;\n      top: 60px;\n      left: 0;\n      right: 0;\n      background: rgba(0, 0, 0, 0.9);\n      padding: 1rem;\n      z-index: 100;\n    \"\n  >\n    <div style=\"display: flex; align-items: center; gap: 0.5rem\">\n      <input\n        [(ngModel)]=\"searchQuery\"\n        placeholder=\"Rechercher dans la conversation...\"\n        style=\"\n          flex: 1;\n          padding: 0.5rem;\n          background: rgba(0, 0, 0, 0.5);\n          border: 1px solid #00f7ff;\n          border-radius: 5px;\n          color: #e0e0e0;\n        \"\n      />\n      <button\n        (click)=\"toggleSearchBar()\"\n        style=\"\n          background: transparent;\n          border: 1px solid #00f7ff;\n          color: #00f7ff;\n          padding: 0.5rem;\n          border-radius: 5px;\n        \"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAWA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAGEC,WAAW,EACXC,QAAQ,QACH,8BAA8B;AAErC,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;ICTpEC,EAAA,CAAAC,cAAA,cAAgD;IAC1CD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAPFH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAAC,QAAA,CAA+B;IAEjCR,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAS,kBAAA,MAAAH,MAAA,CAAAC,gBAAA,CAAAG,QAAA,gBAAAJ,MAAA,CAAAK,gBAAA,CAAAL,MAAA,CAAAC,gBAAA,CAAAK,UAAA,OAKF;;;;;;;;;;IAgBIZ,EAAA,CAAAC,cAAA,eAOC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAPLH,EAAA,CAAAa,UAAA,iKAAAC,UAAA,CAAAC,KAAA,CAAAC,KAAA,CAGC;IACDhB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAL,UAAA,CAAAC,KAAA,CAAAK,OAAA,EAAqD;IAErDpB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAK,UAAA,CAAAC,KAAA,CAAAM,KAAA,gBAAAP,UAAA,CAAAC,KAAA,CAAAM,KAAA,MACF;;;;;;;;;IApBJrB,EAAA,CAAAsB,uBAAA,GAAwD;IACtDtB,EAAA,CAAAC,cAAA,iBAOC;IADCD,EAAA,CAAAuB,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAC,IAAA;MAAA,MAAAb,UAAA,GAAAW,WAAA,CAAAG,SAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAf,UAAA,CAAAgB,OAAA,EAAgB;IAAA,EAAC;IAE1B9B,EAAA,CAAA+B,SAAA,QAA6B;IAE7B/B,EAAA,CAAAgC,UAAA,IAAAC,mDAAA,mBASO;IACTjC,EAAA,CAAAG,YAAA,EAAS;IACXH,EAAA,CAAAkC,qBAAA,EAAe;;;;IApBXlC,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAa,UAAA,6BAAAC,UAAA,CAAAE,KAAA,CAAkD;IAClDhB,EAAA,CAAAiB,UAAA,YAAAH,UAAA,CAAAqB,WAAA,IAAArB,UAAA,CAAAsB,QAAA,GAAAtB,UAAA,CAAAqB,WAAA,GAAAnC,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAEC,UAAAxB,UAAA,CAAAyB,KAAA;IAIEvC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAa,UAAA,CAAAC,UAAA,CAAA0B,IAAA,CAAqB;IAGrBxC,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAiB,UAAA,SAAAH,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,CAAAM,KAAA,KAA4C;;;;;IA2B/CrB,EAAA,CAAA+B,SAAA,eAGQ;;;;;;;;;;;IAwBN/B,EAAA,CAAAC,cAAA,iBASC;IAPCD,EAAA,CAAAuB,UAAA,mBAAAkB,sEAAA;MAAA,MAAAhB,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAgB,IAAA;MAAA,MAAAC,UAAA,GAAAlB,WAAA,CAAAG,SAAA;MAAA,MAAAgB,OAAA,GAAA5C,EAAA,CAAA6C,aAAA;MAASD,OAAA,CAAAE,gBAAA,CAAAH,UAAA,CAAAI,GAAA,CAA4B;MAAA,OAAE/C,EAAA,CAAA6B,WAAA,CAAAe,OAAA,CAAAI,oBAAA,EAAsB;IAAA,EAAC;IAQ9DhD,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA+B,SAAA,QAEK;IACL/B,EAAA,CAAAC,cAAA,UAAK;IACsBD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAfVH,EAAA,CAAAiB,UAAA,aAAAgC,OAAA,CAAAC,gBAAA,CAA6B,YAAAlD,EAAA,CAAAkB,eAAA,IAAAiC,GAAA,EAAAF,OAAA,CAAAG,iBAAA,KAAAT,UAAA,CAAAI,GAAA;IASzB/C,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAa,UAAA,CAAA8B,UAAA,CAAAH,IAAA,SAAAG,UAAA,CAAAU,KAAA,mBAA4D;IAGnCrD,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAsC,UAAA,CAAAW,KAAA,CAAkB;IAEzCtD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAkC,UAAA,CAAAY,WAAA,MACF;;;;;;IAtCVvD,EAAA,CAAAC,cAAA,cAGC;IAKWD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1BH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIXH,EAAA,CAAAC,cAAA,cAAiB;IAEfD,EAAA,CAAAgC,UAAA,IAAAwB,6CAAA,qBAqBS;IACXxD,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAoE;IAEhED,EAAA,CAAAuB,UAAA,mBAAAkC,8DAAA;MAAAzD,EAAA,CAAA0B,aAAA,CAAAgC,IAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAA6C,aAAA;MAASc,OAAA,CAAAC,qBAAA,EAAuB;MAAA,OAAE5D,EAAA,CAAA6B,WAAA,CAAA8B,OAAA,CAAAX,oBAAA,EAAsB;IAAA,EAAC;IAGzDhD,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA+B,SAAA,aAEK;IACL/B,EAAA,CAAAC,cAAA,WAAK;IACsBD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA9CRH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAiB,UAAA,YAAA4C,MAAA,CAAAC,cAAA,CAAAD,MAAA,CAAAT,iBAAA,EAA6C;IAE7CpD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAoD,MAAA,CAAAE,aAAA,CAAAF,MAAA,CAAAT,iBAAA,OACF;IAOmBpD,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAiB,UAAA,YAAA4C,MAAA,CAAAG,gBAAA,GAAqB;IAmClChE,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAoD,MAAA,CAAAI,mBAAA,iBACF;;;;;;IA6BNjE,EAAA,CAAAC,cAAA,YAYC;IATCD,EAAA,CAAAuB,UAAA,mBAAA2C,4DAAA;MAAA,MAAAzC,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAyC,IAAA;MAAA,MAAAC,SAAA,GAAA3C,WAAA,CAAAG,SAAA;MAAA,MAAAyC,OAAA,GAAArE,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAwC,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAArB,GAAA,CAAsB;IAAA,EAAC;IAUhC/C,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA+B,SAAA,cAEO;IACP/B,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAZ9BH,EAAA,CAAAiB,UAAA,0BAAAmD,SAAA,CAAAG,UAAA,0BAAAH,SAAA,CAAAG,UAAA,SAMC;IAIGvE,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAiB,UAAA,yCAAAmD,SAAA,CAAAf,KAAA,WAA8D;IAE3DrD,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA+D,SAAA,CAAAd,KAAA,CAAiB;;;;;IA5B9BtD,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAiB;IAEfD,EAAA,CAAAgC,UAAA,IAAAwC,wCAAA,gBAmBI;IACNxE,EAAA,CAAAG,YAAA,EAAM;;;;IAnBgBH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAiB,UAAA,YAAAwD,MAAA,CAAAC,eAAA,GAAoB;;;;;;IAkC5C1E,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAiB;IAEbD,EAAA,CAAAuB,UAAA,mBAAAoD,6DAAA;MAAA3E,EAAA,CAAA0B,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAA7E,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAgD,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAG7B9E,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA+B,SAAA,YAAyC;IACzC/B,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGpCH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAwD,6DAAA;MAAA/E,EAAA,CAAA0B,aAAA,CAAAkD,IAAA;MAAA,MAAAI,OAAA,GAAAhF,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAmD,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BjF,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA+B,SAAA,aAA4C;IAC5C/B,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGvCH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA2D,8DAAA;MAAAlF,EAAA,CAAA0B,aAAA,CAAAkD,IAAA;MAAA,MAAAO,OAAA,GAAAnF,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAsD,OAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAGlCpF,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA+B,SAAA,aAA+C;IAC/C/B,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG3BH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA8D,8DAAA;MAAArF,EAAA,CAAA0B,aAAA,CAAAkD,IAAA;MAAA,MAAAU,OAAA,GAAAtF,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAyD,OAAA,CAAAC,0BAAA,EAA4B;IAAA,EAAC;IAGtCvF,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA+B,SAAA,aAAuC;IACvC/B,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,uBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWjCH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA+B,SAAA,YAAsC;IAAC/B,EAAA,CAAAE,MAAA,sBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA+B,SAAA,YAGK;IACL/B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACnCH,EAAA,CAAAC,cAAA,YAA6B;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;IAG9DH,EAAA,CAAAC,cAAA,cAOC;IAEQD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChCH,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IATRH,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAwF,eAAA,IAAAC,GAAA,GAAAC,WAAA,CAAAC,MAAA,kBAAAD,WAAA,CAAAC,MAAA,CAAAC,EAAA,MAAAC,MAAA,CAAAC,aAAA,GAAAJ,WAAA,CAAAC,MAAA,kBAAAD,WAAA,CAAAC,MAAA,CAAAC,EAAA,MAAAC,MAAA,CAAAC,aAAA,EAGE;IAGK9F,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAqF,WAAA,CAAAK,OAAA,CAAqB;IAExB/F,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAoF,MAAA,CAAAG,iBAAA,CAAAN,WAAA,CAAAO,SAAA,OACF;;;;;IAoCJjG,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA+B,SAAA,YAGK;IACL/B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAE5BH,EAAA,CAAAC,cAAA,cAGC;IACyCD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,GAE1B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAH8BH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAA6F,gBAAA,CAAAH,OAAA,CAA0B;IACtC/F,EAAA,CAAAI,SAAA,GAE1B;IAF0BJ,EAAA,CAAAK,iBAAA,CAAA8F,OAAA,CAAAH,iBAAA,CAAAE,gBAAA,CAAAD,SAAA,EAE1B;;;;;;IAKNjG,EAAA,CAAAC,cAAA,cAWC;IAGKD,EAAA,CAAAuB,UAAA,2BAAA6E,oEAAAC,MAAA;MAAArG,EAAA,CAAA0B,aAAA,CAAA4E,IAAA;MAAA,MAAAC,OAAA,GAAAvG,EAAA,CAAA6C,aAAA;MAAA,OAAA7C,EAAA,CAAA6B,WAAA,CAAA0E,OAAA,CAAAC,WAAA,GAAAH,MAAA;IAAA,EAAyB;IAD3BrG,EAAA,CAAAG,YAAA,EAWE;IACFH,EAAA,CAAAC,cAAA,iBASC;IARCD,EAAA,CAAAuB,UAAA,mBAAAkF,6DAAA;MAAAzG,EAAA,CAAA0B,aAAA,CAAA4E,IAAA;MAAA,MAAAI,OAAA,GAAA1G,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA6E,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAS3B3G,EAAA,CAAA+B,SAAA,YAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;;;;IAtBPH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAiB,UAAA,YAAA2F,OAAA,CAAAJ,WAAA,CAAyB;;;;;;;;;;;;;ADhUjC,OAAM,MAAOK,oBAAoB;EA8K/B;EACA,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACC,CAAC,CAACC,SAAS;EACzB;EA6BA;EACA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACC,cAAc,CAACC,eAAe,EAAE;EAC9C;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAO,CACL;MACEpG,KAAK,EAAE,gBAAgB;MACvBwB,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE,aAAa;MACpBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACuF,YAAY,CAAC,OAAO,CAAC;MACzCjF,QAAQ,EAAE;KACX,EACD;MACEpB,KAAK,EAAE,gBAAgB;MACvBwB,IAAI,EAAE,cAAc;MACpBD,KAAK,EAAE,aAAa;MACpBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACuF,YAAY,CAAC,OAAO,CAAC;MACzCjF,QAAQ,EAAE;KACX,EACD;MACEpB,KAAK,EAAE,YAAY;MACnBwB,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE,YAAY;MACnBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC6E,eAAe,EAAE;MACrCvE,QAAQ,EAAE,IAAI,CAACkF,aAAa;MAC5BnF,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACEnB,KAAK,EAAE,qBAAqB;MAC5BwB,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE,sBAAsB,IAAI,CAACgF,sBAAsB,EAAE,GAAG;MAC7DzF,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC0F,oBAAoB,EAAE;MAC1CpF,QAAQ,EAAE,IAAI,CAACqF,kBAAkB;MACjCtF,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DpB,KAAK,EACH,IAAI,CAACwG,sBAAsB,EAAE,GAAG,CAAC,GAC7B;QACElG,KAAK,EAAE,IAAI,CAACkG,sBAAsB,EAAE;QACpCvG,KAAK,EAAE,gCAAgC;QACvCI,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEJ,KAAK,EAAE,4BAA4B;MACnCwB,IAAI,EAAE,aAAa;MACnBD,KAAK,EAAE,eAAe;MACtBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC4F,uBAAuB,EAAE;MAC7CtF,QAAQ,EAAE,IAAI,CAACuF,qBAAqB;MACpCxF,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DpB,KAAK,EACH,IAAI,CAAC6G,uBAAuB,GAAG,CAAC,GAC5B;QACEvG,KAAK,EAAE,IAAI,CAACuG,uBAAuB;QACnC5G,KAAK,EAAE,8CAA8C;QACrDI,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEJ,KAAK,EAAE,sBAAsB;MAC7BwB,IAAI,EAAE,gBAAgB;MACtBD,KAAK,EAAE,uBAAuB;MAC9BT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC+F,sBAAsB,EAAE;MAC5CzF,QAAQ,EAAE,IAAI,CAAC0F,oBAAoB;MACnC3F,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACEnB,KAAK,EAAE,oBAAoB;MAC3BwB,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE,uBAAuB;MAC9BT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACiG,oBAAoB,EAAE;MAC1C3F,QAAQ,EAAE,IAAI,CAAC4F,kBAAkB;MACjC7F,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACEnB,KAAK,EAAE,6BAA6B;MACpCwB,IAAI,EAAE,mBAAmB;MACzBD,KAAK,EAAE,iBAAiB;MACxBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACmG,wBAAwB,EAAE;MAC9C7F,QAAQ,EAAE,IAAI,CAAC8F,sBAAsB;MACrC/F,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DpB,KAAK,EACH,IAAI,CAACoH,aAAa,CAACC,MAAM,GAAG,CAAC,GACzB;QACE/G,KAAK,EAAE,IAAI,CAAC8G,aAAa,CAACC,MAAM;QAChCpH,KAAK,EAAE,cAAc;QACrBI,OAAO,EAAE;OACV,GACD;KACP,CACF;EACH;EAEAiH,YACUnB,cAA8B,EAC/BoB,KAAqB,EACpBC,WAA4B,EAC5BC,EAAe,EAChBC,aAAgC,EAChCC,MAAc,EACbC,YAA0B,EAE1BC,GAAsB;IARtB,KAAA1B,cAAc,GAAdA,cAAc;IACf,KAAAoB,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,YAAY,GAAZA,YAAY;IAEZ,KAAAC,GAAG,GAAHA,GAAG;IAjTb,KAAAC,QAAQ,GAAc,EAAE;IAExB,KAAAC,YAAY,GAAwB,IAAI;IACxC,KAAAC,OAAO,GAAG,IAAI;IAEd,KAAAjD,aAAa,GAAkB,IAAI;IACnC,KAAAkD,eAAe,GAAW,KAAK;IAC/B,KAAAzI,gBAAgB,GAAgB,IAAI;IACpC,KAAA0I,YAAY,GAAgB,IAAI;IAChC,KAAAC,UAAU,GAAgC,IAAI;IAC9C,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAET,KAAAC,qBAAqB,GAAG,CAAC;IACzB,KAAAC,oBAAoB,GAAG,EAAE;IACzB,KAAAC,kBAAkB,GAAG,GAAG;IACjC,KAAAC,WAAW,GAAG,CAAC;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACd,KAAAC,aAAa,GAAG,IAAInK,YAAY,EAAE;IAE1C;IACA,KAAAoK,aAAa,GAAW,eAAe;IAEvC;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAA3C,aAAa,GAAG,KAAK;IACrB,KAAAG,kBAAkB,GAAG,KAAK;IAC1B,KAAAyC,kBAAkB,GAAG,KAAK;IAC1B,KAAAvC,qBAAqB,GAAG,KAAK;IAC7B,KAAAwC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAtC,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,kBAAkB,GAAG,KAAK;IAC1B,KAAAE,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAmC,YAAY,GAAQ,IAAI;IACxB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,SAAS,GAAQ,IAAI;IAErB;IACA,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAjD,uBAAuB,GAAW,CAAC;IACnC,KAAAkD,qBAAqB,GAAgB,IAAIC,GAAG,EAAE;IAC9C,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,iBAAiB,GAAQ,IAAI;IAE7B;IACA,KAAA3E,WAAW,GAAG,EAAE;IAChB,KAAA4E,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,aAAa,GAAU,EAAE;IAEzB;IACA,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,kBAAkB,GAA+B,EAAE;IACnD,KAAAC,iBAAiB,GAA+B,EAAE;IAClD,KAAAC,cAAc,GAA+B,EAAE;IAC/C,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,qBAAqB,GAAa,EAAE;IACpC,KAAAC,sBAAsB,GAAU,EAAE;IAClC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,sBAAsB,GAAG,KAAK;IAE9B;IACS,KAAAnF,CAAC,GAAG;MACXC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC/CmF,MAAM,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAG,CAAE;MAC9DC,KAAK,EAAE,6BAA6B;MACpCC,SAAS,EAAEA,CAACC,CAAS,EAAEC,IAAS,KAAaA,IAAI,EAAE/G,EAAE,IAAI8G,CAAC,CAACE,QAAQ,EAAE;MACrE/B,aAAa,EAAE;QACbgC,WAAW,EAAE;UAAErK,IAAI,EAAE,gBAAgB;UAAEa,KAAK,EAAE;QAAe,CAAE;QAC/DyJ,WAAW,EAAE;UAAEtK,IAAI,EAAE,oBAAoB;UAAEa,KAAK,EAAE;QAAc,CAAE;QAClE0J,MAAM,EAAE;UAAEvK,IAAI,EAAE,YAAY;UAAEa,KAAK,EAAE;QAAe;OACrD;MACD2J,MAAM,EAAE;QACNC,MAAM,EAAE;UACNC,IAAI,EAAE,UAAU;UAChB7J,KAAK,EAAE,gBAAgB;UACvBb,IAAI,EAAE,eAAe;UACrBc,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAE;SACd;QACD4J,OAAO,EAAE;UACPD,IAAI,EAAE,YAAY;UAClB7J,KAAK,EAAE,eAAe;UACtBb,IAAI,EAAE,eAAe;UACrBc,KAAK,EAAE,YAAY;UACnBC,WAAW,EAAE;SACd;QACD6I,IAAI,EAAE;UACJc,IAAI,EAAE,QAAQ;UACd7J,KAAK,EAAE,iBAAiB;UACxBb,IAAI,EAAE,cAAc;UACpBc,KAAK,EAAE,QAAQ;UACfC,WAAW,EAAE;SACd;QACD6J,IAAI,EAAE;UACJF,IAAI,EAAE,QAAQ;UACd7J,KAAK,EAAE,cAAc;UACrBb,IAAI,EAAE,qBAAqB;UAC3Bc,KAAK,EAAE,QAAQ;UACfC,WAAW,EAAE;;OAEhB;MACD8J,MAAM,EAAE,CACN;QACEtK,GAAG,EAAE,eAAe;QACpBO,KAAK,EAAE,QAAQ;QACfD,KAAK,EAAE,SAAS;QAChBkB,UAAU,EAAE;OACb,EACD;QACExB,GAAG,EAAE,gBAAgB;QACrBO,KAAK,EAAE,MAAM;QACbD,KAAK,EAAE,SAAS;QAChBkB,UAAU,EAAE;OACb,EACD;QACExB,GAAG,EAAE,iBAAiB;QACtBO,KAAK,EAAE,MAAM;QACbD,KAAK,EAAE,SAAS;QAChBkB,UAAU,EAAE;OACb,EACD;QACExB,GAAG,EAAE,eAAe;QACpBO,KAAK,EAAE,MAAM;QACbD,KAAK,EAAE,SAAS;QAChBkB,UAAU,EAAE;OACb,CACF;MACD+I,KAAK,EAAE;QACLC,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;OACX;MACD;MACAC,kBAAkB,EAAE;QAClBb,WAAW,EAAE;UAAErK,IAAI,EAAE,gBAAgB;UAAEa,KAAK,EAAE;QAAe,CAAE;QAC/DyJ,WAAW,EAAE;UAAEtK,IAAI,EAAE,oBAAoB;UAAEa,KAAK,EAAE;QAAc,CAAE;QAClE0J,MAAM,EAAE;UAAEvK,IAAI,EAAE,YAAY;UAAEa,KAAK,EAAE;QAAe;OACrD;MACDsK,gBAAgB,EAAE;QAChBJ,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;;KAEb;IAOD;IACA,KAAAG,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,uBAAuB,GAAG,KAAK;IAC/B,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,cAAc,GAAG,IAAI;IAErB;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,WAAW,GAAG,YAAY;IAC1B,KAAAC,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAC,WAAW,GAAsB,IAAIC,GAAG,EAAE;IAC1C,KAAApL,iBAAiB,GAAW,QAAQ;IACpC,KAAAqL,gBAAgB,GAAS,IAAIC,IAAI,EAAE;IACnC,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAzL,gBAAgB,GAAG,KAAK;IACxB,KAAA0L,WAAW,GAAU,EAAE;IACvB,KAAAzG,aAAa,GAAU,EAAE;IACzB,KAAA0G,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,gBAAgB,GAAG,KAAK;IAuLxB;IACS,KAAAC,CAAC,GAAG;MACXC,OAAO,EAAGC,CAAU,IAAK,IAAI,CAAChI,cAAc,CAACiI,WAAW,CAACD,CAAC,CAAC;MAC3DE,OAAO,EAAGF,CAAU,IAAK,IAAI,CAAChI,cAAc,CAACmI,WAAW,CAACH,CAAC;KAC3D;IAED,KAAAC,WAAW,GAAG,IAAI,CAACH,CAAC,CAACC,OAAO;IAC5B,KAAAI,WAAW,GAAG,IAAI,CAACL,CAAC,CAACI,OAAO;IAmLpB,KAAAE,iBAAiB,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,GAAG;IAClB,KAAAC,cAAc,GAAG,IAAI;IAuDtC;IACS,KAAAC,iBAAiB,GAAG;MAC3BC,aAAa,EAAEA,CAAA,KAAM,IAAI,CAACC,WAAW,CAAC,OAAO,CAAC;MAC9CC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACD,WAAW,CAAC,MAAM,CAAC;MACxCE,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACF,WAAW,CAAC,OAAO;KAC5C;IAED,KAAAG,mBAAmB,GAAG,IAAI,CAACL,iBAAiB,CAACC,aAAa;IAC1D,KAAAK,cAAc,GAAG,IAAI,CAACN,iBAAiB,CAACG,QAAQ;IAChD,KAAAI,iBAAiB,GAAG,IAAI,CAACP,iBAAiB,CAACI,WAAW;IAQtD;IACiB,KAAAI,aAAa,GAAG;MAC/B1E,cAAc,EAAEA,CAAA,KAAO,IAAI,CAAC9D,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAmB;MAC1EyI,SAAS,EAAEA,CAAA,KAAK;QACd,IAAI,CAACP,WAAW,CAAC,QAAQ,CAAC;QAC1B,IAAI,CAAC,IAAI,CAACrI,aAAa,EAAE,IAAI,CAAC6I,WAAW,EAAE;MAC7C,CAAC;MACDC,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACT,WAAW,CAAC,QAAQ,CAAC;MAChDU,oBAAoB,EAAEA,CAAA,KACnB,IAAI,CAAClG,wBAAwB,GAAG,CAAC,IAAI,CAACA,wBAAyB;MAClEmG,eAAe,EAAEA,CAAA,KACd,IAAI,CAAClG,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAoB;MACxDmG,YAAY,EAAEA,CAAA,KAAO,IAAI,CAACnC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAgB;MAClEoC,gBAAgB,EAAEA,CAAA,KACf,IAAI,CAAC1I,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAqB;MAC1D2I,cAAc,EAAEA,CAAA,KAAO,IAAI,CAACzI,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAmB;MAC1E0I,kBAAkB,EAAEA,CAAA,KACjB,IAAI,CAACxI,sBAAsB,GAAG,CAAC,IAAI,CAACA;KACxC;IAED,KAAAV,oBAAoB,GAAG,IAAI,CAACyI,aAAa,CAAC1E,cAAc;IACxD,KAAA5E,eAAe,GAAG,IAAI,CAACsJ,aAAa,CAACC,SAAS;IAC9C,KAAAlN,oBAAoB,GAAG,IAAI,CAACiN,aAAa,CAACG,cAAc;IACxD,KAAAO,0BAA0B,GAAG,IAAI,CAACV,aAAa,CAACI,oBAAoB;IACpE,KAAAzM,qBAAqB,GAAG,IAAI,CAACqM,aAAa,CAACK,eAAe;IAC1D,KAAAM,kBAAkB,GAAG,IAAI,CAACX,aAAa,CAACM,YAAY;IACpD,KAAA1I,sBAAsB,GAAG,IAAI,CAACoI,aAAa,CAACO,gBAAgB;IAC5D,KAAAzI,oBAAoB,GAAG,IAAI,CAACkI,aAAa,CAACQ,cAAc;IACxD,KAAAxI,wBAAwB,GAAG,IAAI,CAACgI,aAAa,CAACS,kBAAkB;IAQvD,KAAAG,mBAAmB,GAAG;MAC7BC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACC,sBAAsB,CAAC,8BAA8B,CAAC;MAC3EC,YAAY,EAAEA,CAAA,KACZ,IAAI,CAACD,sBAAsB,CAAC,4BAA4B,CAAC;MAC3DE,KAAK,EAAEA,CAAA,KAAW;QAChB,IAAI,CAAC,IAAI,CAACnI,YAAY,EAAElD,EAAE,IAAI,IAAI,CAACiD,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;UACxD,IAAI,CAACO,YAAY,CAACuI,WAAW,CAAC,6BAA6B,CAAC;UAC5D;;QAGF,IACEC,OAAO,CACL,oHAAoH,CACrH,EACD;UACA,IAAI,CAACtI,QAAQ,GAAG,EAAE;UAClB,IAAI,CAACmB,YAAY,GAAG,KAAK;UACzB,IAAI,CAACrB,YAAY,CAACyI,WAAW,CAAC,gCAAgC,CAAC;;MAEnE,CAAC;MACDC,MAAM,EAAEA,CAAA,KAAW;QACjB,IAAI,CAAC,IAAI,CAACvI,YAAY,EAAElD,EAAE,IAAI,IAAI,CAACiD,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;UACxD,IAAI,CAACO,YAAY,CAACuI,WAAW,CAAC,gCAAgC,CAAC;UAC/D;;QAGF,MAAMI,gBAAgB,GAAG,IAAI,CAACxI,YAAY,CAACyI,OAAO,GAC9C,IAAI,CAACzI,YAAY,CAAC0I,SAAS,IAAI,iBAAiB,GAChD,IAAI,CAACjR,gBAAgB,EAAEC,QAAQ,IAAI,qBAAqB;QAE5D,MAAMiR,UAAU,GAAG;UACjB3I,YAAY,EAAE;YACZlD,EAAE,EAAE,IAAI,CAACkD,YAAY,CAAClD,EAAE;YACxB8L,IAAI,EAAEJ,gBAAgB;YACtBC,OAAO,EAAE,IAAI,CAACzI,YAAY,CAACyI,OAAO;YAClCI,YAAY,EAAE,IAAI,CAAC7I,YAAY,CAAC6I,YAAY;YAC5CC,SAAS,EAAE,IAAI,CAAC9I,YAAY,CAAC8I;WAC9B;UACD/I,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACgJ,GAAG,CAAEC,GAAG,KAAM;YACpClM,EAAE,EAAEkM,GAAG,CAAClM,EAAE;YACVG,OAAO,EAAE+L,GAAG,CAAC/L,OAAO;YACpBJ,MAAM,EAAEmM,GAAG,CAACnM,MAAM;YAClBM,SAAS,EAAE6L,GAAG,CAAC7L,SAAS;YACxB8L,IAAI,EAAED,GAAG,CAACC;WACX,CAAC,CAAC;UACHC,UAAU,EAAE,IAAItD,IAAI,EAAE,CAACuD,WAAW,EAAE;UACpCC,UAAU,EAAE,IAAI,CAACpM;SAClB;QAED,MAAMqM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAACb,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;UAC3DM,IAAI,EAAE;SACP,CAAC;QACF,MAAMQ,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;QAC5C,MAAMQ,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;QAEf,MAAMQ,YAAY,GAAGzB,gBAAgB,CAClC0B,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAC3BC,WAAW,EAAE;QAChB,MAAMC,OAAO,GAAG,IAAIxE,IAAI,EAAE,CAACuD,WAAW,EAAE,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtDR,IAAI,CAACS,QAAQ,GAAG,gBAAgBL,YAAY,IAAIG,OAAO,OAAO;QAE9DP,IAAI,CAACU,KAAK,EAAE;QACZb,MAAM,CAACC,GAAG,CAACa,eAAe,CAACf,GAAG,CAAC;QAE/B,IAAI,CAACvI,YAAY,GAAG,KAAK;QACzB,IAAI,CAACrB,YAAY,CAACyI,WAAW,CAAC,mCAAmC,CAAC;MACpE;KACD;IAED;IACA,KAAAhM,sBAAsB,GAAG,IAAI,CAACyL,mBAAmB,CAACC,QAAQ;IAC1D,KAAAvL,0BAA0B,GAAG,IAAI,CAACsL,mBAAmB,CAACG,YAAY;IAClE,KAAAlM,iBAAiB,GAAG,IAAI,CAAC+L,mBAAmB,CAACI,KAAK;IAClD,KAAAhM,kBAAkB,GAAG,IAAI,CAAC4L,mBAAmB,CAACQ,MAAM;IA6GpD;IACS,KAAAkC,CAAC,GAAG;MACXC,UAAU,EAAGtE,CAA4B,IACvC,IAAI,CAAChI,cAAc,CAAClB,iBAAiB,CAACkJ,CAAC,CAAC;MAC1CuE,YAAY,EAAGvE,CAA4B,IACzC,IAAI,CAAChI,cAAc,CAACvG,gBAAgB,CAACuO,CAAC,CAAC;MACzCwE,UAAU,EAAGxE,CAA4B,IACvC,IAAI,CAAChI,cAAc,CAACyM,iBAAiB,CAACzE,CAAC,CAAC;MAC1C0E,cAAc,EAAGlH,CAAS,IACxB,IAAI,CAACxF,cAAc,CAAC2M,oBAAoB,CAAC,IAAI,CAAChL,QAAQ,EAAE6D,CAAC,CAAC;MAC5D0C,OAAO,EAAG0E,CAA6B,IACrC,IAAI,CAAC5M,cAAc,CAAC6M,cAAc,CAACD,CAAC,CAAC;MACvCE,QAAQ,EAAGF,CAA6B,IACtC,IAAI,CAAC5M,cAAc,CAAC8M,QAAQ,CAACF,CAAC,CAAC;MACjCG,OAAO,EAAGH,CAA6B,IACrC,IAAI,CAAC5M,cAAc,CAACgN,cAAc,CAACJ,CAAC,CAAC;MACvCK,WAAW,EAAGL,CAA6B,IACzC,IAAI,CAAC5M,cAAc,CAACkN,kBAAkB,CAACN,CAAC,CAAC;MAC3CO,gBAAgB,EAAGP,CAA6B,IAC9C,IAAI,CAAC5M,cAAc,CAACoN,uBAAuB,CAACR,CAAC,CAAC;MAChDS,cAAc,EAAG7H,CAAS,IAAK,IAAI,CAACxF,cAAc,CAACsN,iBAAiB,CAAC9H,CAAC,CAAC;MACvE+H,WAAW,EAAGlB,CAAS,IAAK,IAAI,CAACrM,cAAc,CAACwN,mBAAmB,CAACnB,CAAC,CAAC;MACtEoB,WAAW,EAAGb,CAA6B,IACzC,IAAI,CAAC5M,cAAc,CAACyN,WAAW,CAACb,CAAC,CAAC;MACpCc,YAAY,EAAGd,CAA6B,IAC1C,IAAI,CAAC5M,cAAc,CAAC2N,mBAAmB,CAACf,CAAC,EAAE,IAAI,CAAChO,aAAa;KAChE;IAED;IACA,KAAAE,iBAAiB,GAAG,IAAI,CAACuN,CAAC,CAACC,UAAU;IACrC,KAAA7S,gBAAgB,GAAG,IAAI,CAAC4S,CAAC,CAACE,YAAY;IACtC,KAAAE,iBAAiB,GAAG,IAAI,CAACJ,CAAC,CAACG,UAAU;IACrC,KAAAG,oBAAoB,GAAG,IAAI,CAACN,CAAC,CAACK,cAAc;IAC5C,KAAAG,cAAc,GAAG,IAAI,CAACR,CAAC,CAACnE,OAAO;IAC/B,KAAA4E,QAAQ,GAAG,IAAI,CAACT,CAAC,CAACS,QAAQ;IAC1B,KAAAE,cAAc,GAAG,IAAI,CAACX,CAAC,CAACU,OAAO;IAC/B,KAAAG,kBAAkB,GAAG,IAAI,CAACb,CAAC,CAACY,WAAW;IACvC,KAAAG,uBAAuB,GAAG,IAAI,CAACf,CAAC,CAACc,gBAAgB;IACjD,KAAAG,iBAAiB,GAAG,IAAI,CAACjB,CAAC,CAACgB,cAAc;IACzC,KAAAG,mBAAmB,GAAG,IAAI,CAACnB,CAAC,CAACkB,WAAW;IACxC,KAAAE,WAAW,GAAG,IAAI,CAACpB,CAAC,CAACoB,WAAW;IAChC,KAAAE,mBAAmB,GAAG,IAAI,CAACtB,CAAC,CAACqB,YAAY;IAkFzC;IACS,KAAAE,uBAAuB,GAAG;MACjCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACnC,QAAQ,CAACoC,cAAc,CAAC,2BAA2B,CAAC,EAAE;UACzD,MAAMC,SAAS,GAAGrC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC/CoC,SAAS,CAACrP,EAAE,GAAG,2BAA2B;UAC1CqP,SAAS,CAACC,SAAS,GAAG,wCAAwC;UAC9DD,SAAS,CAACE,SAAS,GACjB,uEAAuE;UACzE,IAAI,CAACC,iBAAiB,EAAEC,aAAa,EAAEC,OAAO,CAACL,SAAS,CAAC;;MAE7D,CAAC;MACDM,IAAI,EAAEA,CAAA,KAAK;QACT,MAAMN,SAAS,GAAGrC,QAAQ,CAACoC,cAAc,CAAC,2BAA2B,CAAC;QACtEC,SAAS,EAAEO,UAAU,EAAEC,WAAW,CAACR,SAAS,CAAC;MAC/C;KACD;IAEO,KAAAS,oBAAoB,GAAG,IAAI,CAACZ,uBAAuB,CAACC,IAAI;IACxD,KAAAY,oBAAoB,GAAG,IAAI,CAACb,uBAAuB,CAACS,IAAI;IAuHhE;IACS,KAAAK,qBAAqB,GAAG;MAC/BC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACxM,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;QAC9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE,IAAI,CAACC,sBAAsB,GAAG,CAAC;MAC7D,CAAC;MACDwM,QAAQ,EAAGC,SAAe,IAAI;QAC5B,IAAI,CAAC,IAAI,CAACjN,YAAY,EAAElD,EAAE,IAAI,CAAC,IAAI,CAACrF,gBAAgB,EAAEqF,EAAE,EAAE;UACxD,IAAI,CAAC+C,YAAY,CAACqN,SAAS,CAAC,uCAAuC,CAAC;UACpE,IAAI,CAAC3M,gBAAgB,GAAG,KAAK;UAC7B;;QAEF,MAAM4M,UAAU,GAAG,IAAI,CAAC1V,gBAAgB,EAAEqF,EAAE,IAAI,EAAE;QAClD,IAAI,CAACsB,cAAc,CAACgP,gBAAgB,CAClCD,UAAU,EACVF,SAAS,EACT,IAAI,CAACjN,YAAY,EAAElD,EAAE,EACrB,IAAI,CAAC0D,sBAAsB,CAC5B,CAAC6M,SAAS,CAAC;UACVC,IAAI,EAAGC,OAAO,IAAI;YAChB,IAAI,CAAChN,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;YAC/B,IAAI,CAACgN,cAAc,CAAC,IAAI,CAAC;UAC3B,CAAC;UACD9J,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC7D,YAAY,CAACqN,SAAS,CAAC,8BAA8B,CAAC;YAC3D,IAAI,CAAC3M,gBAAgB,GAAG,KAAK;UAC/B;SACD,CAAC;MACJ,CAAC;MACDkN,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAClN,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;MACjC;KACD;IAED,KAAAkN,oBAAoB,GAAG,IAAI,CAACZ,qBAAqB,CAACC,MAAM;IACxD,KAAAY,wBAAwB,GAAG,IAAI,CAACb,qBAAqB,CAACE,QAAQ;IAC9D,KAAAY,yBAAyB,GAAG,IAAI,CAACd,qBAAqB,CAACW,MAAM;IA8I7D;IACS,KAAAI,WAAW,GAAG;MACrBC,QAAQ,EAAG7E,IAAuB,IAAI;QACpC,IAAI,CAAC,IAAI,CAACxR,gBAAgB,EAAEqF,EAAE,EAAE;QAChC,IAAI,CAACsB,cAAc,CAACG,YAAY,CAC9B,IAAI,CAAC9G,gBAAgB,CAACqF,EAAE,EACxBmM,IAAI,KAAK,OAAO,GAAGnS,QAAQ,CAACiX,KAAK,GAAGjX,QAAQ,CAACkX,KAAK,EAClD,IAAI,CAAChO,YAAY,EAAElD,EAAE,CACtB,CAACuQ,SAAS,CAAC;UACVC,IAAI,EAAGW,IAAI,IAAI,CAAE,CAAC;UAClBvK,KAAK,EAAEA,CAAA,KAAM,IAAI,CAAC7D,YAAY,CAACqN,SAAS,CAAC,IAAI,CAACjP,CAAC,CAACyF,KAAK;SACtD,CAAC;MACJ,CAAC;MACDwK,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC,IAAI,CAAC3M,YAAY,EAAE;QACxB,IAAI,CAACnD,cAAc,CAAC+P,UAAU,CAAC,IAAI,CAAC5M,YAAY,CAACzE,EAAE,CAAC,CAACuQ,SAAS,CAAC;UAC7DC,IAAI,EAAGW,IAAI,IAAI;YACb,IAAI,CAACxM,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;YACxB,IAAI,CAACK,cAAc,GAAG,IAAI,CAACL,YAAY,EAAE0H,IAAI,KAAK,OAAO;YACzD,IAAI,CAAC1D,WAAW,GAAG,YAAY;YAC/B,IAAI,CAAC1F,YAAY,CAACyI,WAAW,CAAC,gBAAgB,CAAC;UACjD,CAAC;UACD5E,KAAK,EAAEA,CAAA,KAAK;YACV,IAAI,CAAC7D,YAAY,CAACqN,SAAS,CAAC,IAAI,CAACjP,CAAC,CAACyF,KAAK,CAAC;YACzC,IAAI,CAACjC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;UAC1B;SACD,CAAC;MACJ,CAAC;MACD6M,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC,IAAI,CAAC7M,YAAY,EAAE;QACxB,IAAI,CAACnD,cAAc,CAACiQ,UAAU,CAAC,IAAI,CAAC9M,YAAY,CAACzE,EAAE,CAAC,CAACuQ,SAAS,CAAC;UAC7DC,IAAI,EAAGW,IAAI,IAAI;YACb,IAAI,CAACxM,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;UAC1B,CAAC;UACDmC,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACjC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;UAC1B;SACD,CAAC;MACJ,CAAC;MACD+M,GAAG,EAAEA,CAAA,KAAK;QACR,MAAMC,GAAG,GAAG,IAAI,CAACnQ,cAAc,CAACoQ,WAAW,CAACnB,SAAS,CAAEY,IAAI,IAAI;UAC7D,IAAIA,IAAI,EAAE;YACR,IAAI,CAAC7P,cAAc,CAACqQ,OAAO,CAACR,IAAI,CAACnR,EAAE,CAAC,CAACuQ,SAAS,CAAC;cAC7CC,IAAI,EAAGW,IAAI,IAAI,CAAE,CAAC;cAClBvK,KAAK,EAAGA,KAAK,IAAI,CAAE;aACpB,CAAC;;QAEN,CAAC,CAAC;QACF6K,GAAG,CAACG,WAAW,EAAE;MACnB;KACD;IAED,KAAAnQ,YAAY,GAAG,IAAI,CAACsP,WAAW,CAACC,QAAQ;IACxC,KAAAK,UAAU,GAAG,IAAI,CAACN,WAAW,CAACK,MAAM;IACpC,KAAAG,UAAU,GAAG,IAAI,CAACR,WAAW,CAACO,MAAM;IACpC,KAAAK,OAAO,GAAG,IAAI,CAACZ,WAAW,CAACS,GAAG;IAE9B;IACS,KAAAK,kBAAkB,GAAG;MAC5BC,UAAU,EAAEA,CAAA,KAAK;QACf,IAAI,CAACjN,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;QACpC,IAAI,CAACgN,kBAAkB,CAACE,WAAW,EAAE;QACrC,IAAI,CAAChP,YAAY,CAACmI,QAAQ,CACxB,IAAI,CAACrG,WAAW,GAAG,sBAAsB,GAAG,mBAAmB,CAChE;MACH,CAAC;MACDmN,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAAClN,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAAC+M,kBAAkB,CAACE,WAAW,EAAE;QACrC,IAAI,CAAChP,YAAY,CAACmI,QAAQ,CACxB,IAAI,CAACpG,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDiN,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAACzQ,cAAc,CAAC2Q,WAAW,CAC7B,IAAI,CAACvN,UAAU,EAAE1E,EAAE,EACnB,CAAC,IAAI,CAAC6E,WAAW,EACjB,IAAI,CAACC,cAAc,CACpB,CAACyL,SAAS,CAAC;UACVC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;UACd5J,KAAK,EAAGA,KAAK,IACXsL,OAAO,CAACtL,KAAK,CAAC,2CAA2C,EAAEA,KAAK;SACnE,CAAC;MACJ;KACD;IAED,KAAAuL,cAAc,GAAG,IAAI,CAACN,kBAAkB,CAACC,UAAU;IACnD,KAAAM,eAAe,GAAG,IAAI,CAACP,kBAAkB,CAACG,WAAW;IAC7C,KAAAK,eAAe,GAAG,IAAI,CAACR,kBAAkB,CAACE,WAAW;IAE7D;IACS,KAAAO,YAAY,GAAG;MACtBC,cAAc,EAAEA,CAAA,KAAK;QACnB,IAAI,CAACxN,YAAY,GAAG,CAAC;QACrB,IAAI,CAACC,SAAS,GAAGwN,WAAW,CAAC,MAAK;UAChC,IAAI,CAACzN,YAAY,EAAE;UACnB,IAAI,IAAI,CAACA,YAAY,KAAK,CAAC,IAAI,IAAI,CAAC0D,WAAW,KAAK,YAAY,EAAE;YAChE,IAAI,CAACA,WAAW,GAAG,WAAW;;QAElC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDgK,aAAa,EAAEA,CAAA,KAAK;QAClB,IAAI,IAAI,CAACzN,SAAS,EAAE;UAClB0N,aAAa,CAAC,IAAI,CAAC1N,SAAS,CAAC;UAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;MAEzB,CAAC;MACD2N,cAAc,EAAEA,CAAA,KAAO,IAAI,CAAC5N,YAAY,GAAG;KAC5C;IAEO,KAAA6N,oBAAoB,GAAG,IAAI,CAACN,YAAY,CAACC,cAAc;IACvD,KAAAM,mBAAmB,GAAG,IAAI,CAACP,YAAY,CAACG,aAAa;IACrD,KAAAK,oBAAoB,GAAG,IAAI,CAACR,YAAY,CAACK,cAAc;IAE/D;IAEA;IACS,KAAAI,wBAAwB,GAAG;MAClChJ,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAACA,WAAW,CAAC,cAAc,CAAC;QAChC,IAAI,IAAI,CAAChI,qBAAqB,EAAE;UAC9B,IAAI,CAACiR,iBAAiB,EAAE;;MAE5B;KACD;IAED,KAAAlR,uBAAuB,GAAG,IAAI,CAACiR,wBAAwB,CAAChJ,WAAW;IA8BnE;IACS,KAAAkJ,mBAAmB,GAAG;MAC7BC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACF,iBAAiB,EAAE;MACxCG,WAAW,EAAEA,CAAA,KACV,IAAI,CAACnR,uBAAuB,GAAG,IAAI,CAACiD,aAAa,CAAC9K,MAAM,CACtDiZ,CAAC,IAAK,CAACA,CAAC,CAACC,MAAM,CACjB,CAAC7Q,MAAO;MACX8Q,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACrO,aAAa;MACrCsO,eAAe,EAAGC,cAAsB,IAAI;QAC1C,IAAI,IAAI,CAACtO,qBAAqB,CAACuO,GAAG,CAACD,cAAc,CAAC,EAAE;UAClD,IAAI,CAACtO,qBAAqB,CAACwO,MAAM,CAACF,cAAc,CAAC;SAClD,MAAM;UACL,IAAI,CAACtO,qBAAqB,CAACyO,GAAG,CAACH,cAAc,CAAC;;MAElD,CAAC;MACDI,eAAe,EAAEA,CAAA,KAAK;QACpB,MAAMC,qBAAqB,GAAG,IAAI,CAACZ,mBAAmB,CAACK,WAAW,EAAE;QACpE,MAAMQ,WAAW,GAAGD,qBAAqB,CAACE,KAAK,CAAEX,CAAC,IAChD,IAAI,CAAClO,qBAAqB,CAACuO,GAAG,CAACL,CAAC,CAACpT,EAAE,CAAC,CACrC;QACD,IAAI8T,WAAW,EAAE;UACfD,qBAAqB,CAACG,OAAO,CAAEZ,CAAC,IAC9B,IAAI,CAAClO,qBAAqB,CAACwO,MAAM,CAACN,CAAC,CAACpT,EAAE,CAAC,CACxC;SACF,MAAM;UACL6T,qBAAqB,CAACG,OAAO,CAAEZ,CAAC,IAC9B,IAAI,CAAClO,qBAAqB,CAACyO,GAAG,CAACP,CAAC,CAACpT,EAAE,CAAC,CACrC;;MAEL,CAAC;MACDiU,cAAc,EAAEA,CAAA,KAAK;QACnB,MAAMJ,qBAAqB,GAAG,IAAI,CAACZ,mBAAmB,CAACK,WAAW,EAAE;QACpE,OACEO,qBAAqB,CAACrR,MAAM,GAAG,CAAC,IAChCqR,qBAAqB,CAACE,KAAK,CAAEX,CAAC,IAAK,IAAI,CAAClO,qBAAqB,CAACuO,GAAG,CAACL,CAAC,CAACpT,EAAE,CAAC,CAAC;MAE5E;KACD;IAED,KAAAkU,qBAAqB,GAAG,IAAI,CAACjB,mBAAmB,CAACC,QAAQ;IACjD,KAAAiB,uBAAuB,GAAG,IAAI,CAAClB,mBAAmB,CAACE,WAAW;IACtE,KAAAiB,wBAAwB,GAAG,IAAI,CAACnB,mBAAmB,CAACK,WAAW;IAC/D,KAAAe,2BAA2B,GAAG,IAAI,CAACpB,mBAAmB,CAACM,eAAe;IACtE,KAAAe,4BAA4B,GAAG,IAAI,CAACrB,mBAAmB,CAACW,eAAe;IACvE,KAAAW,2BAA2B,GAAG,IAAI,CAACtB,mBAAmB,CAACgB,cAAc;IAErE;IACS,KAAAO,cAAc,GAAG;MACxBC,YAAY,EAAEA,CAAA,KAAK;QACjB,MAAMC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1P,qBAAqB,CAAC;QAC1D,IAAIwP,WAAW,CAAClS,MAAM,KAAK,CAAC,EAAE;UAC5B,IAAI,CAACO,YAAY,CAACuI,WAAW,CAAC,kCAAkC,CAAC;UACjE;;QAEF,IAAI,CAACkJ,cAAc,CAACK,UAAU,CAACH,WAAW,EAAE,MAAK;UAC/C,IAAI,CAACxP,qBAAqB,CAACmG,KAAK,EAAE;UAClC,IAAI,CAACtI,YAAY,CAACyI,WAAW,CAC3B,GAAGkJ,WAAW,CAAClS,MAAM,0CAA0C,CAChE;QACH,CAAC,CAAC;MACJ,CAAC;MACDsS,OAAO,EAAEA,CAAA,KAAK;QACZ,MAAMC,mBAAmB,GAAG,IAAI,CAAC9P,aAAa,CAAC9K,MAAM,CAAEiZ,CAAC,IAAK,CAACA,CAAC,CAACC,MAAM,CAAC;QACvE,IAAI0B,mBAAmB,CAACvS,MAAM,KAAK,CAAC,EAAE;UACpC,IAAI,CAACO,YAAY,CAACmI,QAAQ,CAAC,6BAA6B,CAAC;UACzD;;QAEF,MAAM8J,SAAS,GAAGD,mBAAmB,CAAC9I,GAAG,CAAEmH,CAAC,IAAKA,CAAC,CAACpT,EAAE,CAAC;QACtD,IAAI,CAACwU,cAAc,CAACK,UAAU,CAACG,SAAS,EAAE,MAAK;UAC7C,IAAI,CAACjS,YAAY,CAACyI,WAAW,CAC3B,sDAAsD,CACvD;QACH,CAAC,CAAC;MACJ,CAAC;MACDqJ,UAAU,EAAEA,CAACI,GAAa,EAAEC,SAAqB,KAAI;QACnD,MAAMC,OAAO,GAAG,IAAI,CAAC7T,cAAc,CAACuT,UAAU,CAACI,GAAG,CAAC,CAAC1E,SAAS,CAAC;UAC5DC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAACnQ,aAAa,GAAG,IAAI,CAACA,aAAa,CAACgH,GAAG,CAAEmH,CAAC,IAC5C6B,GAAG,CAACI,QAAQ,CAACjC,CAAC,CAACpT,EAAE,CAAC,GAAG;cAAE,GAAGoT,CAAC;cAAEC,MAAM,EAAE,IAAI;cAAEiC,MAAM,EAAE,IAAIxM,IAAI;YAAE,CAAE,GAAGsK,CAAC,CACpE;YACD,IAAI,CAACe,uBAAuB,EAAE;YAC9Be,SAAS,EAAE;UACb,CAAC;UACDtO,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC7D,YAAY,CAACqN,SAAS,CACzB,2CAA2C,CAC5C;UACH;SACD,CAAC;QACF,IAAI,CAACnM,aAAa,CAAC0P,GAAG,CAACwB,OAAO,CAAC;MACjC;KACD;IAED,KAAAI,kBAAkB,GAAG,IAAI,CAACf,cAAc,CAACC,YAAY;IACrD,KAAAe,aAAa,GAAG,IAAI,CAAChB,cAAc,CAACM,OAAO;IAE3C;IACS,KAAAW,yBAAyB,GAAG;MACnCC,8BAA8B,EAAEA,CAAA,KAAK;QACnC,IAAI,IAAI,CAACxQ,qBAAqB,CAACyQ,IAAI,KAAK,CAAC,EAAE;UACzC,IAAI,CAAC5S,YAAY,CAACuI,WAAW,CAAC,kCAAkC,CAAC;UACjE;;QAEF,IAAI,CAAClG,sBAAsB,GAAG,IAAI;MACpC,CAAC;MACDwQ,cAAc,EAAEA,CAAA,KAAK;QACnB,MAAMlB,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1P,qBAAqB,CAAC;QAC1D,IAAIwP,WAAW,CAAClS,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC2F,uBAAuB,GAAG,IAAI;QACnC,IAAI,CAAC/C,sBAAsB,GAAG,KAAK;QACnC,MAAMyQ,SAAS,GAAG,IAAI,CAACvU,cAAc,CAACwU,2BAA2B,CAC/DpB,WAAW,CACZ,CAACnE,SAAS,CAAC;UACVC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAACnQ,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC9K,MAAM,CAC3CiZ,CAAC,IAAK,CAACsB,WAAW,CAACW,QAAQ,CAACjC,CAAC,CAACpT,EAAE,CAAC,CACnC;YACD,IAAI,CAACkF,qBAAqB,CAACmG,KAAK,EAAE;YAClC,IAAI,CAAC8I,uBAAuB,EAAE;YAC9B,IAAI,CAAChM,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACpF,YAAY,CAACyI,WAAW,CAC3B,GAAG4J,MAAM,CAAC3Z,KAAK,+BAA+B,CAC/C;UACH,CAAC;UACDmL,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACuB,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACpF,YAAY,CAACqN,SAAS,CACzB,iDAAiD,CAClD;UACH;SACD,CAAC;QACF,IAAI,CAACnM,aAAa,CAAC0P,GAAG,CAACkC,SAAS,CAAC;MACnC,CAAC;MACDE,SAAS,EAAGvC,cAAsB,IAAI;QACpC,MAAMqC,SAAS,GAAG,IAAI,CAACvU,cAAc,CAAC0U,kBAAkB,CACtDxC,cAAc,CACf,CAACjD,SAAS,CAAC;UACVC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAACnQ,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC9K,MAAM,CAC3CiZ,CAAC,IAAKA,CAAC,CAACpT,EAAE,KAAKwT,cAAc,CAC/B;YACD,IAAI,CAACtO,qBAAqB,CAACwO,MAAM,CAACF,cAAc,CAAC;YACjD,IAAI,CAACW,uBAAuB,EAAE;YAC9B,IAAI,CAACpR,YAAY,CAACyI,WAAW,CAAC,wBAAwB,CAAC;UACzD,CAAC;UACD5E,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC7D,YAAY,CAACqN,SAAS,CACzB,kDAAkD,CACnD;UACH;SACD,CAAC;QACF,IAAI,CAACnM,aAAa,CAAC0P,GAAG,CAACkC,SAAS,CAAC;MACnC,CAAC;MACDI,SAAS,EAAEA,CAAA,KAAK;QACd,IAAI,IAAI,CAAChR,aAAa,CAACzC,MAAM,KAAK,CAAC,EAAE;QACrC,IACE,CAAC+I,OAAO,CACN,8FAA8F,CAC/F,EACD;UACA;;QAEF,IAAI,CAACpD,uBAAuB,GAAG,IAAI;QACnC,MAAM+N,YAAY,GAChB,IAAI,CAAC5U,cAAc,CAAC6U,sBAAsB,EAAE,CAAC5F,SAAS,CAAC;UACrDC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAACnQ,aAAa,GAAG,EAAE;YACvB,IAAI,CAACC,qBAAqB,CAACmG,KAAK,EAAE;YAClC,IAAI,CAAC8I,uBAAuB,EAAE;YAC9B,IAAI,CAAChM,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACpF,YAAY,CAACyI,WAAW,CAC3B,GAAG4J,MAAM,CAAC3Z,KAAK,uCAAuC,CACvD;UACH,CAAC;UACDmL,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACuB,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACpF,YAAY,CAACqN,SAAS,CACzB,2DAA2D,CAC5D;UACH;SACD,CAAC;QACJ,IAAI,CAACnM,aAAa,CAAC0P,GAAG,CAACuC,YAAY,CAAC;MACtC,CAAC;MACDvF,MAAM,EAAEA,CAAA,KAAO,IAAI,CAACvL,sBAAsB,GAAG;KAC9C;IAED,KAAAsQ,8BAA8B,GAC5B,IAAI,CAACD,yBAAyB,CAACC,8BAA8B;IAC/D,KAAAU,2BAA2B,GAAG,IAAI,CAACX,yBAAyB,CAACG,cAAc;IAC3E,KAAAI,kBAAkB,GAAG,IAAI,CAACP,yBAAyB,CAACM,SAAS;IAC7D,KAAAI,sBAAsB,GAAG,IAAI,CAACV,yBAAyB,CAACQ,SAAS;IACjE,KAAAI,yBAAyB,GAAG,IAAI,CAACZ,yBAAyB,CAAC9E,MAAM;IAEjE;IACS,KAAA2F,uBAAuB,GAAG;MACjCxI,UAAU,EAAGyI,IAA+B,IAC1C,IAAI,CAACjV,cAAc,CAACvG,gBAAgB,CAACwb,IAAI,CAAC;MAC5ClN,OAAO,EAAG8C,IAAY,IACpB,IAAI,CAAChL,CAAC,CAAC8D,aAAa,CAACkH,IAAyC,CAAC,EAAEvP,IAAI,IACrE,aAAa;MACf4Z,QAAQ,EAAGrK,IAAY,IACrB,IAAI,CAAChL,CAAC,CAAC8D,aAAa,CAACkH,IAAyC,CAAC,EAAE1O,KAAK,IACtE,eAAe;MACjBoJ,SAAS,EAAEA,CAAC4P,KAAa,EAAEC,YAAiB,KAC1C,IAAI,CAACvV,CAAC,CAAC0F,SAAS,CAAC,CAAC,EAAE6P,YAAY;KACnC;IAED,KAAAC,sBAAsB,GAAG,IAAI,CAACL,uBAAuB,CAACxI,UAAU;IAChE,KAAA8I,mBAAmB,GAAG,IAAI,CAACN,uBAAuB,CAACjN,OAAO;IAC1D,KAAAwN,oBAAoB,GAAG,IAAI,CAACP,uBAAuB,CAACE,QAAQ;IAC5D,KAAAM,qBAAqB,GAAG,IAAI,CAACR,uBAAuB,CAACzP,SAAS;IA6K9D;IACS,KAAAkQ,YAAY,GAAG;MACtBC,eAAe,EAAEA,CAAA,KAAK;QACpB,MAAMC,MAAM,GAAG,EAAE;QACjB,IAAI,IAAI,CAACzS,mBAAmB,EAC1ByS,MAAM,CAACC,IAAI,CAAC;UACV/Z,GAAG,EAAE,YAAY;UACjBR,KAAK,EAAE,cAAc;UACrBC,IAAI,EAAE,cAAc;UACpBua,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC3S,mBAAmB,GAAG;SAChD,CAAC;QACJ,IAAI,IAAI,CAACtC,oBAAoB,EAC3B+U,MAAM,CAACC,IAAI,CAAC;UACV/Z,GAAG,EAAE,aAAa;UAClBR,KAAK,EAAE,uBAAuB;UAC9BC,IAAI,EAAE,gBAAgB;UACtBua,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACjV,oBAAoB,GAAG;SACjD,CAAC;QACJ,IAAI,IAAI,CAACE,kBAAkB,EACzB6U,MAAM,CAACC,IAAI,CAAC;UACV/Z,GAAG,EAAE,WAAW;UAChBR,KAAK,EAAE,uBAAuB;UAC9BC,IAAI,EAAE,kBAAkB;UACxBua,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC/U,kBAAkB,GAAG;SAC/C,CAAC;QACJ,IAAI,IAAI,CAACE,sBAAsB,EAC7B2U,MAAM,CAACC,IAAI,CAAC;UACV/Z,GAAG,EAAE,eAAe;UACpBR,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE,mBAAmB;UACzBua,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC7U,sBAAsB,GAAG;SACnD,CAAC;QACJ,OAAO2U,MAAM;MACf,CAAC;MACD7Y,gBAAgB,EAAEA,CAAA,KAChBgZ,MAAM,CAACC,OAAO,CAAC,IAAI,CAAClW,CAAC,CAACiG,MAAM,CAAC,CAAC6E,GAAG,CAAC,CAAC,CAAC9O,GAAG,EAAEma,MAAM,CAAC,MAAM;QACpDna,GAAG;QACH,GAAGma;OACJ,CAAC,CAAC;MACLxY,eAAe,EAAEA,CAAA,KAAM,IAAI,CAACqC,CAAC,CAACsG;KAC/B;IAED,KAAAuP,eAAe,GAAG,IAAI,CAACD,YAAY,CAACC,eAAe;IACnD,KAAA5Y,gBAAgB,GAAG,IAAI,CAAC2Y,YAAY,CAAC3Y,gBAAgB;IACrD,KAAAU,eAAe,GAAG,IAAI,CAACiY,YAAY,CAACjY,eAAe;IAEnD;IACS,KAAAyY,aAAa,GAAG;MACvBC,OAAO,EAAGpQ,MAAc,IACtB,IAAI,CAACjG,CAAC,CAACiG,MAAM,CAACA,MAAoC,CAAC,EAAEE,IAAI,IAAI,SAAS;MACxEkP,QAAQ,EAAGpP,MAAc,IACvB,IAAI,CAACjG,CAAC,CAACiG,MAAM,CAACA,MAAoC,CAAC,EAAE3J,KAAK,IAC1D,eAAe;MACjB4L,OAAO,EAAGjC,MAAc,IACtB,IAAI,CAACjG,CAAC,CAACiG,MAAM,CAACA,MAAoC,CAAC,EAAExK,IAAI,IACzD;KACH;IAED,KAAAuB,aAAa,GAAG,IAAI,CAACoZ,aAAa,CAACC,OAAO;IAC1C,KAAAtZ,cAAc,GAAG,IAAI,CAACqZ,aAAa,CAACf,QAAQ;IAC5C,KAAAiB,aAAa,GAAG,IAAI,CAACF,aAAa,CAAClO,OAAO;IAE1C;IACS,KAAAqO,cAAc,GAAG;MACxBC,cAAc,EAAG3c,UAAuB,IACtCA,UAAU,GACN,IAAI,CAACsG,cAAc,CAACvG,gBAAgB,CAACC,UAAU,CAAC,GAChD,WAAW;MACjBqD,mBAAmB,EAAEA,CAAA,KACnBsW,KAAK,CAACC,IAAI,CAAC,IAAI,CAACjM,WAAW,CAACiP,MAAM,EAAE,CAAC,CAACzd,MAAM,CAAE0d,IAAI,IAAKA,IAAI,CAAC/c,QAAQ,CAAC,CAClE0H,MAAM;MACXsV,gBAAgB,EAAEA,CAAA,KAAMnD,KAAK,CAACC,IAAI,CAAC,IAAI,CAACjM,WAAW,CAACiP,MAAM,EAAE,CAAC;MAC7DG,eAAe,EAAG5d,MAAc,IAAM,IAAI,CAACgP,gBAAgB,GAAGhP;KAC/D;IAED,KAAAwd,cAAc,GAAG,IAAI,CAACD,cAAc,CAACC,cAAc;IACnD,KAAAtZ,mBAAmB,GAAG,IAAI,CAACqZ,cAAc,CAACrZ,mBAAmB;IAC7D,KAAA0Z,eAAe,GAAG,IAAI,CAACL,cAAc,CAACK,eAAe;IACrD,KAAAD,gBAAgB,GAAG,IAAI,CAACJ,cAAc,CAACI,gBAAgB;IAEvD;IACS,KAAAE,mBAAmB,GAAG;MAC7BC,UAAU,EAAGxH,OAAY,IAAM,IAAI,CAAClL,iBAAiB,GAAGkL,OAAQ;MAChEyH,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC3S,iBAAiB,GAAG,IAAK;MAClD4S,gBAAgB,EAAG1H,OAAY,IAAI;QACjC,IAAI,CAACvK,iBAAiB,GAAGuK,OAAO;QAChC,IAAI,CAACxK,gBAAgB,GAAG,IAAI;MAC9B,CAAC;MACDmS,iBAAiB,EAAEA,CAAA,KAAK;QACtB,IAAI,CAACnS,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,qBAAqB,GAAG,EAAE;MACjC;KACD;IAED,KAAAkS,mBAAmB,GAAG,IAAI,CAACL,mBAAmB,CAACC,UAAU;IACzD,KAAAC,WAAW,GAAG,IAAI,CAACF,mBAAmB,CAACE,WAAW;IAClD,KAAAC,gBAAgB,GAAG,IAAI,CAACH,mBAAmB,CAACG,gBAAgB;IAC5D,KAAAC,iBAAiB,GAAG,IAAI,CAACJ,mBAAmB,CAACI,iBAAiB;IAE9D;IACS,KAAAE,cAAc,GAAG;MACxBC,UAAU,EAAG9H,OAAY,IACvB,IAAI,CAAC+H,eAAe,CAAC/H,OAAO,CAAC,GAAG,kBAAkB,GAAG,kBAAkB;MACzEgI,iBAAiB,EAAGhI,OAAY,IAC9B,IAAI,CAAC+H,eAAe,CAAC/H,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;MAC5DiI,cAAc,EAAGjI,OAAY,IAAKA,OAAO,CAAC1Q,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACE,aAAa;MAC3EsY,eAAe,EAAG/H,OAAY,IAAKA,OAAO,CAACkI,QAAQ,IAAI;KACxD;IAED,KAAAJ,UAAU,GAAG,IAAI,CAACD,cAAc,CAACC,UAAU;IAC3C,KAAAE,iBAAiB,GAAG,IAAI,CAACH,cAAc,CAACG,iBAAiB;IACzD,KAAAC,cAAc,GAAG,IAAI,CAACJ,cAAc,CAACI,cAAc;IACnD,KAAAF,eAAe,GAAG,IAAI,CAACF,cAAc,CAACE,eAAe;IAErD;IACS,KAAAI,WAAW,GAAG;MACrBC,gBAAgB,EAAGpI,OAAY,IAAI;QACjC,IAAI,CAACpL,gBAAgB,GAAGoL,OAAO,CAACzQ,EAAE;QAClC,IAAI,CAACsF,cAAc,GAAGmL,OAAO,CAACtQ,OAAO;MACvC,CAAC;MACD2Y,iBAAiB,EAAEA,CAAA,KAAK;QACtB,IAAI,CAACzT,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACC,cAAc,GAAG,EAAE;MAC1B,CAAC;MACDyT,eAAe,EAAGC,SAAiB,IACjC,IAAI,CAACJ,WAAW,CAACE,iBAAiB,EAAE;MACtCG,cAAc,EAAEA,CAACC,KAAoB,EAAEF,SAAiB,KAAI;QAC1D,IAAIE,KAAK,CAAC/b,GAAG,KAAK,OAAO,IAAI,CAAC+b,KAAK,CAACC,QAAQ,EAAE;UAC5CD,KAAK,CAACE,cAAc,EAAE;UACtB,IAAI,CAACR,WAAW,CAACG,eAAe,CAACC,SAAS,CAAC;SAC5C,MAAM,IAAIE,KAAK,CAAC/b,GAAG,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACyb,WAAW,CAACE,iBAAiB,EAAE;;MAExC;KACD;IAED,KAAAD,gBAAgB,GAAG,IAAI,CAACD,WAAW,CAACC,gBAAgB;IACpD,KAAAC,iBAAiB,GAAG,IAAI,CAACF,WAAW,CAACE,iBAAiB;IACtD,KAAAC,eAAe,GAAG,IAAI,CAACH,WAAW,CAACG,eAAe;IAClD,KAAAE,cAAc,GAAG,IAAI,CAACL,WAAW,CAACK,cAAc;IAUhD;IACS,KAAAI,aAAa,GAAG;MACvBC,kBAAkB,EAAGlS,MAAc,IACjC,IAAI,CAACjG,CAAC,CAAC4G,gBAAgB,CAACX,MAA8C,CAAC,IACvE,eAAe;MACjBmS,eAAe,EAAGpN,IAAY,IAC5BA,IAAI,KAAK,OAAO,GAAG,cAAc,GAAG,cAAc;MACpDqN,kBAAkB,EAAGC,QAAgB,IAAI;QACvC,IAAI,CAACA,QAAQ,EAAE,OAAO,OAAO;QAC7B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAG,EAAE,CAAC;QACzC,MAAMI,OAAO,GAAGJ,QAAQ,GAAG,EAAE;QAC7B,OAAO,GAAGC,OAAO,CAAC1S,QAAQ,EAAE,CAAC8S,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CACrD7S,QAAQ,EAAE,CACV8S,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACvB,CAAC;MACDC,cAAc,EAAG1Z,SAAoC,IACnD,IAAI,CAACiB,cAAc,CAACyM,iBAAiB,CAAC1N,SAAS;KAClD;IAED,KAAAiZ,kBAAkB,GAAG,IAAI,CAACD,aAAa,CAACC,kBAAkB;IAC1D,KAAAC,eAAe,GAAG,IAAI,CAACF,aAAa,CAACE,eAAe;IACpD,KAAAC,kBAAkB,GAAG,IAAI,CAACH,aAAa,CAACG,kBAAkB;IAC1D,KAAAO,cAAc,GAAG,IAAI,CAACV,aAAa,CAACU,cAAc;IAElD;IACS,KAAAC,YAAY,GAAG;MACtBC,eAAe,EAAGf,KAAY,IAAI;QAChC,MAAMgB,MAAM,GAAGhB,KAAK,CAACgB,MAAqB;QAC1C,MAAMC,YAAY,GAAG,CACnB;UACEC,SAAS,EAAE,CAAC,sBAAsB,EAAE,YAAY,CAAC;UACjDC,QAAQ,EAAE;SACX,EACD;UACED,SAAS,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC;UAC1CC,QAAQ,EAAE;SACX,CACF;QAEDF,YAAY,CAACnG,OAAO,CAAEsD,MAAM,IAAI;UAC9B,MAAMgD,cAAc,GAAG,CAAChD,MAAM,CAAC8C,SAAS,CAACG,IAAI,CAAEC,QAAQ,IACrDN,MAAM,CAACO,OAAO,CAACD,QAAQ,CAAC,CACzB;UACD,IAAIF,cAAc,EAAE;YACjB,IAAY,CAAChD,MAAM,CAAC+C,QAAQ,CAAC,GAAG,KAAK;;QAE1C,CAAC,CAAC;MACJ,CAAC;MACDK,oBAAoB,EAAG1B,SAAiB,IAAI;QAC1C,IAAI,CAACnT,iBAAiB,CAACmT,SAAS,CAAC,GAAG,KAAK;MAC3C;KACD;IAED,KAAAiB,eAAe,GAAG,IAAI,CAACD,YAAY,CAACC,eAAe;IACnD,KAAAS,oBAAoB,GAAG,IAAI,CAACV,YAAY,CAACU,oBAAoB;IAE7D;IACS,KAAAC,eAAe,GAAG;MACzBC,kBAAkB,EAAGnK,OAAY,IAAKA,OAAO,CAACrP,SAAS,IAAI,EAAE;MAC7DyZ,eAAe,EAAEA,CAAC7B,SAAiB,EAAE8B,KAAa,KAAI;QACpD;MAAA,CACD;MACDC,cAAc,EAAEA,CAACtK,OAAY,EAAEqK,KAAa,KAAK;KAClD;IAED,KAAAF,kBAAkB,GAAG,IAAI,CAACD,eAAe,CAACC,kBAAkB;IAC5D,KAAAC,eAAe,GAAG,IAAI,CAACF,eAAe,CAACE,eAAe;IACtD,KAAAE,cAAc,GAAG,IAAI,CAACJ,eAAe,CAACI,cAAc;IAEpD;IACS,KAAAC,4BAA4B,GAAG;MACtC/G,cAAc,EAAEA,CAAA,KACd,IAAI,CAAC9N,qBAAqB,CAAC3D,MAAM,KAAK,IAAI,CAAC4D,sBAAsB,CAAC5D,MAAM;MAC1EyY,SAAS,EAAEA,CAAA,KACR,IAAI,CAAC9U,qBAAqB,GAAG,IAAI,CAACC,sBAAsB,CAAC6F,GAAG,CAC1D9K,CAAC,IAAKA,CAAC,CAACnB,EAAE,CACX;MACJkb,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC/U,qBAAqB,GAAG,EAAG;MACpD8J,MAAM,EAAGkL,cAAsB,IAAI;QACjC,MAAM1E,KAAK,GAAG,IAAI,CAACtQ,qBAAqB,CAACiV,OAAO,CAACD,cAAc,CAAC;QAChE1E,KAAK,GAAG,CAAC,CAAC,GACN,IAAI,CAACtQ,qBAAqB,CAACkV,MAAM,CAAC5E,KAAK,EAAE,CAAC,CAAC,GAC3C,IAAI,CAACtQ,qBAAqB,CAAC+Q,IAAI,CAACiE,cAAc,CAAC;MACrD,CAAC;MACDG,UAAU,EAAGH,cAAsB,IACjC,IAAI,CAAChV,qBAAqB,CAACkP,QAAQ,CAAC8F,cAAc,CAAC;MACrDI,eAAe,EAAGrY,YAAiB,IACjCA,YAAY,CAACsY,KAAK,IAAI,kCAAkC;MAC1DC,cAAc,EAAGvY,YAAiB,IAAKA,YAAY,CAAC4I,IAAI,IAAI,cAAc;MAC1E4P,cAAc,EAAEA,CAAA,KAAK;QACnB,IAAI,CAACrV,YAAY,GAAG,IAAI;QACxBsV,UAAU,CAAC,MAAK;UACd,IAAI,CAACtV,YAAY,GAAG,KAAK;UACzB,IAAI,CAAC+R,iBAAiB,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV;KACD;IAED,KAAAwD,2BAA2B,GACzB,IAAI,CAACZ,4BAA4B,CAAC/G,cAAc;IAClD,KAAA4H,sBAAsB,GAAG,IAAI,CAACb,4BAA4B,CAACC,SAAS;IACpE,KAAAa,wBAAwB,GAAG,IAAI,CAACd,4BAA4B,CAACE,WAAW;IACxE,KAAAa,2BAA2B,GAAG,IAAI,CAACf,4BAA4B,CAAC/K,MAAM;IACtE,KAAA+L,sBAAsB,GAAG,IAAI,CAAChB,4BAA4B,CAACM,UAAU;IACrE,KAAAW,2BAA2B,GACzB,IAAI,CAACjB,4BAA4B,CAACO,eAAe;IACnD,KAAAW,0BAA0B,GAAG,IAAI,CAAClB,4BAA4B,CAACS,cAAc;IAC7E,KAAAC,cAAc,GAAG,IAAI,CAACV,4BAA4B,CAACU,cAAc;IAEjE;IACS,KAAAS,yBAAyB,GAAG;MACnCC,eAAe,EAAEA,CAAA,KAAO,IAAI,CAAC1T,gBAAgB,GAAG,IAAK;MACrD2T,wBAAwB,EAAEA,CAAA,KAAK,CAAE,CAAC;MAClCC,qBAAqB,EAAGniB,MAAc,IACnC,IAAI,CAAC6N,kBAAkB,GAAG7N;KAC9B;IAED,KAAAiiB,eAAe,GAAG,IAAI,CAACD,yBAAyB,CAACC,eAAe;IAChE,KAAAC,wBAAwB,GACtB,IAAI,CAACF,yBAAyB,CAACE,wBAAwB;IACzD,KAAAC,qBAAqB,GAAG,IAAI,CAACH,yBAAyB,CAACG,qBAAqB;IAE5E;IACS,KAAAC,aAAa,GAAG;MACvBC,OAAO,EAAGtD,KAAU,IAAI;QACtB,IAAI,CAACtY,WAAW,GAAGsY,KAAK,CAACgB,MAAM,CAACuC,KAAK;QACrC,IAAI,CAAC7b,WAAW,CAAC4B,MAAM,IAAI,CAAC,GACxB,IAAI,CAAC+Z,aAAa,CAACG,OAAO,EAAE,GAC5B,IAAI,CAACH,aAAa,CAAClR,KAAK,EAAE;MAChC,CAAC;MACDsR,UAAU,EAAGzD,KAAoB,IAAI;QACnC,IAAIA,KAAK,CAAC/b,GAAG,KAAK,OAAO,EAAE;UACzB,IAAI,CAACof,aAAa,CAACG,OAAO,EAAE;SAC7B,MAAM,IAAIxD,KAAK,CAAC/b,GAAG,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACof,aAAa,CAAClR,KAAK,EAAE;;MAE9B,CAAC;MACDqR,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAClX,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,UAAU,GAAG,IAAI;QACtBkW,UAAU,CAAC,MAAK;UACd,IAAI,CAACjW,aAAa,GAAG,IAAI,CAACzC,QAAQ,CAAC9I,MAAM,CAAE+T,CAAC,IAC1CA,CAAC,CAAC/N,OAAO,EAAEkN,WAAW,EAAE,CAACgI,QAAQ,CAAC,IAAI,CAACzU,WAAW,CAACyM,WAAW,EAAE,CAAC,CAClE;UACD,IAAI,CAAC7H,WAAW,GAAG,KAAK;QAC1B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD6F,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACzK,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC8E,aAAa,GAAG,EAAE;QACvB,IAAI,CAACF,WAAW,GAAG,KAAK;QACxB,IAAI,CAACC,UAAU,GAAG,KAAK;MACzB;KACD;IAED,KAAAmX,aAAa,GAAG,IAAI,CAACL,aAAa,CAACC,OAAO;IAC1C,KAAAK,gBAAgB,GAAG,IAAI,CAACN,aAAa,CAACI,UAAU;IAChD,KAAAG,aAAa,GAAG,IAAI,CAACP,aAAa,CAACG,OAAO;IAC1C,KAAAnS,WAAW,GAAG,IAAI,CAACgS,aAAa,CAAClR,KAAK;IAEtC;IACS,KAAA0R,mBAAmB,GAAG;MAC7BC,iBAAiB,EAAGhE,SAAiB,IAAI;QACvC;MAAA,CACD;MACDiE,qBAAqB,EAAGjE,SAAiB,IAAI;QAC3C;MAAA,CACD;MACDrX,sBAAsB,EAAEA,CAAA,KAAM,IAAI,CAACgE,cAAc,CAACnD;KACnD;IAED,KAAAwa,iBAAiB,GAAG,IAAI,CAACD,mBAAmB,CAACC,iBAAiB;IAC9D,KAAAC,qBAAqB,GAAG,IAAI,CAACF,mBAAmB,CAACE,qBAAqB;IACtE,KAAAtb,sBAAsB,GAAG,IAAI,CAACob,mBAAmB,CAACpb,sBAAsB;IAExE;IACS,KAAAub,wBAAwB,GAAG;MAClCC,oBAAoB,EAAEA,CAAChd,OAAe,EAAEid,KAAa,KAAI;QACvD,IAAI,CAACA,KAAK,EAAE,OAAOjd,OAAO;QAC1B,MAAMkd,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIF,KAAK,GAAG,EAAE,IAAI,CAAC;QAC5C,OAAOjd,OAAO,CAACiN,OAAO,CAACiQ,KAAK,EAAE,iBAAiB,CAAC;MAClD,CAAC;MACDE,oBAAoB,EAAGvE,SAAiB,IACrC,IAAI,CAACpT,kBAAkB,CAACoT,SAAS,CAAC,GACjC,CAAC,IAAI,CAACpT,kBAAkB,CAACoT,SAAS,CAAE;MACxCwE,cAAc,EAAEA,CAACxE,SAAiB,EAAE8B,KAAa,KAC9C,IAAI,CAAClV,kBAAkB,CAACoT,SAAS,CAAC,GAAG,KAAM;MAC9CyE,oBAAoB,EAAGzE,SAAiB,IACrC,IAAI,CAAChT,kBAAkB,CAACgT,SAAS,CAAC,GACjC,CAAC,IAAI,CAAChT,kBAAkB,CAACgT,SAAS;KACvC;IAED,KAAAmE,oBAAoB,GAAG,IAAI,CAACD,wBAAwB,CAACC,oBAAoB;IACzE,KAAAI,oBAAoB,GAAG,IAAI,CAACL,wBAAwB,CAACK,oBAAoB;IACzE,KAAAC,cAAc,GAAG,IAAI,CAACN,wBAAwB,CAACM,cAAc;IAC7D,KAAAC,oBAAoB,GAAG,IAAI,CAACP,wBAAwB,CAACO,oBAAoB;IAEzE;IACS,KAAAC,mBAAmB,GAAG;MAC7BC,mBAAmB,EAAG3E,SAAiB,IACpC,IAAI,CAAClT,cAAc,CAACkT,SAAS,CAAC,GAAG,IAAK;MACzC4E,qBAAqB,EAAG5E,SAAiB,IACtC,IAAI,CAAClT,cAAc,CAACkT,SAAS,CAAC,GAAG,KAAM;MAC1C6E,sBAAsB,EAAG7E,SAAiB,IACvC,IAAI,CAACnT,iBAAiB,CAACmT,SAAS,CAAC,GAAG,IAAK;MAC5C8E,mBAAmB,EAAG9E,SAAiB,IACpC,IAAI,CAACnT,iBAAiB,CAACmT,SAAS,CAAC,GAAG;KACxC;IAED,KAAA2E,mBAAmB,GAAG,IAAI,CAACD,mBAAmB,CAACC,mBAAmB;IAClE,KAAAC,qBAAqB,GAAG,IAAI,CAACF,mBAAmB,CAACE,qBAAqB;IACtE,KAAAC,sBAAsB,GAAG,IAAI,CAACH,mBAAmB,CAACG,sBAAsB;IACxE,KAAAC,mBAAmB,GAAG,IAAI,CAACJ,mBAAmB,CAACI,mBAAmB;IAElE;IACS,KAAAC,OAAO,GAAG;MACjBC,aAAa,EAAEA,CAAA,KACb,CAAC,IAAI,CAACC,aAAa,EAAE,IAAI,CAAClV,eAAe,EAAE,IAAI,CAAC/D,SAAS,CAAC,CAACgP,OAAO,CAC/D1K,CAAC,IAAKA,CAAC,IAAI4U,YAAY,CAAC5U,CAAC,CAAC,CAC5B;MACH6U,cAAc,EAAEA,CAAA,KACd,IAAI,CAACje,aAAa,IAClB,IAAI,CAACoB,cAAc,CAAC6c,cAAc,CAAC,IAAI,CAACje,aAAa,CAAC,CAACqQ,SAAS,EAAE;MACpE6N,mBAAmB,EAAEA,CAAA,KAAK;QACxB,IAAI,IAAI,CAAC1U,iBAAiB,IAAI,IAAI,CAACxG,YAAY,EAAElD,EAAE,EAAE;UACnD,IAAI,CAAC0J,iBAAiB,GAAG,KAAK;UAC9BwU,YAAY,CAAC,IAAI,CAACG,WAAW,CAAC;UAC9B,IAAI,CAAC/c,cAAc,CAACgd,UAAU,CAAC,IAAI,CAACpb,YAAY,CAAClD,EAAE,CAAC,CAACuQ,SAAS,CAAC;YAC7DC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;YACd5J,KAAK,EAAGA,KAAK,IAAI,CAAE;WACpB,CAAC;;MAEN;KACD;IA/3DC,IAAI,CAAC2X,WAAW,GAAG,IAAI,CAAC3b,EAAE,CAAC4b,KAAK,CAAC;MAC/Bre,OAAO,EAAE,CAAC,EAAE,EAAE,CAACtG,UAAU,CAAC4kB,SAAS,CAAC,IAAI,CAAC,CAAC;KAC3C,CAAC;EACJ;EACAC,QAAQA,CAAA;IACN,IAAI,CAACxe,aAAa,GAAG,IAAI,CAACyC,WAAW,CAACgc,gBAAgB,EAAE;IAExD,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,IAAIF,UAAU,EAAE;MACd,IAAI,CAAC1a,aAAa,GAAG0a,UAAU;;IAGjC,IAAI,CAACG,wBAAwB,EAAE;IAC/B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,qBAAqB,EAAE;IAE5BlS,QAAQ,CAACmS,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAClF,eAAe,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;IAEnE,MAAMC,QAAQ,GAAG,IAAI,CAAC3c,KAAK,CAAC4c,MAAM,CAC/BC,IAAI,CACHplB,MAAM,CAAEmlB,MAAM,IAAKA,MAAM,CAAC,IAAI,CAAC,CAAC,EAChCplB,oBAAoB,EAAE,EACtBD,SAAS,CAAEqlB,MAAM,IAAI;MACnB,IAAI,CAACnc,OAAO,GAAG,IAAI;MACnB,IAAI,CAACF,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACa,WAAW,GAAG,CAAC;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI;MAE3B,OAAO,IAAI,CAAC1C,cAAc,CAACke,eAAe,CACxCF,MAAM,CAAC,IAAI,CAAC,EACZ,IAAI,CAAC1b,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB;IACH,CAAC,CAAC,CACH,CACAyM,SAAS,CAAC;MACTC,IAAI,EAAGtN,YAAY,IAAI;QACrB,IAAI,CAACuc,wBAAwB,CAACvc,YAAY,CAAC;MAC7C,CAAC;MACD0D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC8Y,WAAW,CAAC,6BAA6B,EAAE9Y,KAAK,CAAC;MACxD;KACD,CAAC;IACJ,IAAI,CAAC3C,aAAa,CAAC0P,GAAG,CAAC0L,QAAQ,CAAC;EAClC;EAEA;EACQK,WAAWA,CACjBjP,OAAe,EACf7J,KAAU,EACV+Y,YAAA,GAAwB,IAAI;IAE5BzN,OAAO,CAACtL,KAAK,CAAC,aAAa,EAAE6J,OAAO,EAAE7J,KAAK,CAAC;IAC5C,IAAI+Y,YAAY,EAAE;MAChB,IAAI,CAACxc,OAAO,GAAG,KAAK;MACpB,IAAI,CAACI,WAAW,GAAG,KAAK;MACxB,IAAI,CAACQ,aAAa,GAAG,KAAK;;IAE5B,IAAI,CAAC6C,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC7D,YAAY,CAACqN,SAAS,CAACK,OAAO,CAAC;EACtC;EAEA;EACQmP,aAAaA,CAACnP,OAAgB,EAAEoP,QAAqB;IAC3D,IAAIpP,OAAO,EAAE;MACX,IAAI,CAAC1N,YAAY,CAACyI,WAAW,CAACiF,OAAO,CAAC;;IAExC,IAAIoP,QAAQ,EAAE;MACZA,QAAQ,EAAE;;EAEd;EAWQJ,wBAAwBA,CAACvc,YAA0B;IACzD,IAAI,CAACA,YAAY,GAAGA,YAAY;IAEhC,IAAI,CAACA,YAAY,EAAED,QAAQ,IAAIC,YAAY,CAACD,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;MACjE,IAAI,CAAC7H,gBAAgB,GACnBuI,YAAY,EAAE6I,YAAY,EAAE+T,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAAC/f,EAAE,KAAK,IAAI,CAACE,aAAa,IAAI6f,CAAC,CAACC,GAAG,KAAK,IAAI,CAAC9f,aAAa,CACnE,IAAI,IAAI;MACX,IAAI,CAAC+C,QAAQ,GAAG,EAAE;KACnB,MAAM;MACL,MAAMgd,oBAAoB,GAAG,CAAC,IAAI/c,YAAY,EAAED,QAAQ,IAAI,EAAE,CAAC,CAAC;MAEhEgd,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACjC,MAAMC,KAAK,GACTF,CAAC,CAAC9f,SAAS,YAAYyI,IAAI,GACvBqX,CAAC,CAAC9f,SAAS,CAACigB,OAAO,EAAE,GACrB,IAAIxX,IAAI,CAACqX,CAAC,CAAC9f,SAAmB,CAAC,CAACigB,OAAO,EAAE;QAC/C,MAAMC,KAAK,GACTH,CAAC,CAAC/f,SAAS,YAAYyI,IAAI,GACvBsX,CAAC,CAAC/f,SAAS,CAACigB,OAAO,EAAE,GACrB,IAAIxX,IAAI,CAACsX,CAAC,CAAC/f,SAAmB,CAAC,CAACigB,OAAO,EAAE;QAC/C,OAAOD,KAAK,GAAGE,KAAK;MACtB,CAAC,CAAC;MAEF,IAAI,CAACtd,QAAQ,GAAGgd,oBAAoB;;IAGtC,IAAI,CAACtlB,gBAAgB,GACnBuI,YAAY,EAAE6I,YAAY,EAAE+T,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAAC/f,EAAE,KAAK,IAAI,CAACE,aAAa,IAAI6f,CAAC,CAACC,GAAG,KAAK,IAAI,CAAC9f,aAAa,CACnE,IAAI,IAAI;IAEX,IAAI,CAACiD,OAAO,GAAG,KAAK;IACpBwY,UAAU,CAAC,MAAM,IAAI,CAACjL,cAAc,EAAE,EAAE,GAAG,CAAC;IAE5C,IAAI,CAAC8P,kBAAkB,EAAE;IAEzB,IAAI,IAAI,CAACtd,YAAY,EAAElD,EAAE,EAAE;MACzB,IAAI,CAACygB,8BAA8B,CAAC,IAAI,CAACvd,YAAY,CAAClD,EAAE,CAAC;MACzD,IAAI,CAAC0gB,sBAAsB,CAAC,IAAI,CAACxd,YAAY,CAAClD,EAAE,CAAC;MACjD,IAAI,CAAC2gB,2BAA2B,CAAC,IAAI,CAACzd,YAAY,CAAClD,EAAE,CAAC;;EAE1D;EAEQygB,8BAA8BA,CAACtF,cAAsB;IAC3D,MAAM1J,GAAG,GAAG,IAAI,CAACnQ,cAAc,CAACmf,8BAA8B,CAC5DtF,cAAc,CACf,CAAC5K,SAAS,CAAC;MACVC,IAAI,EAAGoQ,mBAAmB,IAAI;QAC5B,IAAI,CAAC1d,YAAY,GAAG0d,mBAAmB;QACvC,IAAI,CAAC3d,QAAQ,GAAG2d,mBAAmB,CAAC3d,QAAQ,GACxC,CAAC,GAAG2d,mBAAmB,CAAC3d,QAAQ,CAAC,GACjC,EAAE;QACN,IAAI,CAACyN,cAAc,EAAE;MACvB,CAAC;MACD9J,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7D,YAAY,CAACqN,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;IACF,IAAI,CAACnM,aAAa,CAAC0P,GAAG,CAAClC,GAAG,CAAC;EAC7B;EAEQiP,sBAAsBA,CAACvF,cAAsB;IACnD,MAAM1J,GAAG,GAAG,IAAI,CAACnQ,cAAc,CAACof,sBAAsB,CACpDvF,cAAc,CACf,CAAC5K,SAAS,CAAC;MACVC,IAAI,EAAGqQ,UAAU,IAAI;QACnB,IAAIA,UAAU,EAAE1F,cAAc,KAAK,IAAI,CAACjY,YAAY,EAAElD,EAAE,EAAE;UACxD,IAAI,CAACiD,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE4d,UAAU,CAAC,CAACX,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YAC3D,MAAMC,KAAK,GACTF,CAAC,CAAC9f,SAAS,YAAYyI,IAAI,GACvBqX,CAAC,CAAC9f,SAAS,CAACigB,OAAO,EAAE,GACrB,IAAIxX,IAAI,CAACqX,CAAC,CAAC9f,SAAmB,CAAC,CAACigB,OAAO,EAAE;YAC/C,MAAMC,KAAK,GACTH,CAAC,CAAC/f,SAAS,YAAYyI,IAAI,GACvBsX,CAAC,CAAC/f,SAAS,CAACigB,OAAO,EAAE,GACrB,IAAIxX,IAAI,CAACsX,CAAC,CAAC/f,SAAmB,CAAC,CAACigB,OAAO,EAAE;YAC/C,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEF5E,UAAU,CAAC,MAAM,IAAI,CAACjL,cAAc,EAAE,EAAE,GAAG,CAAC;UAE5C,IACEmQ,UAAU,CAAC9gB,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACE,aAAa,IAC5C2gB,UAAU,CAAC9gB,MAAM,EAAEigB,GAAG,KAAK,IAAI,CAAC9f,aAAa,EAC7C;YACA,IAAI2gB,UAAU,CAAC7gB,EAAE,EAAE;cACjB,IAAI,CAACsB,cAAc,CAACwf,iBAAiB,CAACD,UAAU,CAAC7gB,EAAE,CAAC,CAACuQ,SAAS,EAAE;;;;MAIxE,CAAC;MACD3J,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7D,YAAY,CAACqN,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;IACF,IAAI,CAACnM,aAAa,CAAC0P,GAAG,CAAClC,GAAG,CAAC;EAC7B;EAEQkP,2BAA2BA,CAACxF,cAAsB;IACxD,MAAM1J,GAAG,GAAG,IAAI,CAACnQ,cAAc,CAACyf,0BAA0B,CACxD5F,cAAc,CACf,CAAC5K,SAAS,CAAC;MACVC,IAAI,EAAG0I,KAAK,IAAI;QACd,IAAIA,KAAK,CAAC8H,MAAM,KAAK,IAAI,CAAC9gB,aAAa,EAAE;UACvC,IAAI,CAACsD,QAAQ,GAAG0V,KAAK,CAAC1V,QAAQ;UAC9B,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB0a,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;YAChC,IAAI,CAACA,aAAa,GAAGtC,UAAU,CAAC,MAAK;cACnC,IAAI,CAACnY,QAAQ,GAAG,KAAK;YACvB,CAAC,EAAE,IAAI,CAAC;;;MAGd;KACD,CAAC;IACF,IAAI,CAACS,aAAa,CAAC0P,GAAG,CAAClC,GAAG,CAAC;EAC7B;EAEQ+O,kBAAkBA,CAAA;IACxB,MAAMS,cAAc,GAAG,IAAI,CAAChe,QAAQ,CAAC9I,MAAM,CACxC+R,GAAG,IACF,CAACA,GAAG,CAACmH,MAAM,KACVnH,GAAG,CAACgV,QAAQ,EAAElhB,EAAE,KAAK,IAAI,CAACE,aAAa,IACtCgM,GAAG,CAACgV,QAAQ,EAAElB,GAAG,KAAK,IAAI,CAAC9f,aAAa,CAAC,CAC9C;IAED+gB,cAAc,CAACjN,OAAO,CAAE9H,GAAG,IAAI;MAC7B,IAAIA,GAAG,CAAClM,EAAE,EAAE;QACV,MAAMyR,GAAG,GAAG,IAAI,CAACnQ,cAAc,CAACwf,iBAAiB,CAAC5U,GAAG,CAAClM,EAAE,CAAC,CAACuQ,SAAS,CAAC;UAClE3J,KAAK,EAAGA,KAAK,IAAI,CAAE;SACpB,CAAC;QACF,IAAI,CAAC3C,aAAa,CAAC0P,GAAG,CAAClC,GAAG,CAAC;;IAE/B,CAAC,CAAC;EACJ;EAEA0P,cAAcA,CAACjI,KAAU;IACvB,MAAMkI,IAAI,GAAGlI,KAAK,CAACgB,MAAM,CAACmH,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACD,IAAI,EAAE;IAEX,IAAIA,IAAI,CAACzL,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/B,IAAI,CAAC5S,YAAY,CAACqN,SAAS,CAAC,mCAAmC,CAAC;MAChE;;IAGF,MAAMkR,UAAU,GAAG,CACjB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,oBAAoB,EACpB,yEAAyE,CAC1E;IACD,IAAI,CAACA,UAAU,CAACjM,QAAQ,CAAC+L,IAAI,CAACjV,IAAI,CAAC,EAAE;MACnC,IAAI,CAACpJ,YAAY,CAACqN,SAAS,CACzB,gEAAgE,CACjE;MACD;;IAGF,IAAI,CAAC/M,YAAY,GAAG+d,IAAI;IACxB,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,IAAI,CAACne,UAAU,GAAGie,MAAM,CAACnM,MAAM;IACjC,CAAC;IACDmM,MAAM,CAACG,aAAa,CAACN,IAAI,CAAC;EAC5B;EAEAO,gBAAgBA,CAAA;IACd,IAAI,CAACte,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAACse,SAAS,EAAEnS,aAAa,EAAE;MACjC,IAAI,CAACmS,SAAS,CAACnS,aAAa,CAACgN,KAAK,GAAG,EAAE;;EAE3C;EAOA;EACAoF,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC3e,YAAY,EAAElD,EAAE,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;MACjD;;IAGF,MAAMib,cAAc,GAAG,IAAI,CAACjY,YAAY,CAAClD,EAAE;IAC3Cke,YAAY,CAAC,IAAI,CAACG,WAAW,CAAC;IAE9B,IAAI,CAAC,IAAI,CAAC3U,iBAAiB,EAAE;MAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACpI,cAAc,CAACwgB,WAAW,CAAC3G,cAAc,CAAC,CAAC5K,SAAS,CAAC;QACxDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;QACd5J,KAAK,EAAGA,KAAK,IAAI,CAAE;OACpB,CAAC;;IAGJ,IAAI,CAACyX,WAAW,GAAG1C,UAAU,CAAC,MAAK;MACjC,IAAI,IAAI,CAACjS,iBAAiB,EAAE;QAC1B,IAAI,CAACA,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACpI,cAAc,CAACgd,UAAU,CAACnD,cAAc,CAAC,CAAC5K,SAAS,CAAC;UACvDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;UACd5J,KAAK,EAAGA,KAAK,IAAI,CAAE;SACpB,CAAC;;IAEN,CAAC,EAAE,IAAI,CAACgD,cAAc,CAAC;EACzB;EAEA;EACQG,WAAWA,CAACgY,SAAiB,EAAEC,WAAA,GAAuB,IAAI;IAChE,MAAM/K,MAAM,GAAG;MACbgL,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,cAAc;MACpBpH,KAAK,EAAE,iBAAiB;MACxBpE,YAAY,EAAE,uBAAuB;MACrCyL,MAAM,EAAE,eAAe;MACvB/a,MAAM,EAAE;KACT;IAED,MAAMgb,YAAY,GAAGnL,MAAM,CAAC8K,SAAgC,CAAC;IAC7D,IAAIK,YAAY,EAAE;MACf,IAAY,CAACA,YAAY,CAAC,GAAG,CAAE,IAAY,CAACA,YAAY,CAAC;MAE1D,IAAIJ,WAAW,IAAK,IAAY,CAACI,YAAY,CAAC,EAAE;QAC9ChL,MAAM,CAACQ,MAAM,CAACX,MAAM,CAAC,CAACjD,OAAO,CAAEqO,KAAK,IAAI;UACtC,IAAIA,KAAK,KAAKD,YAAY,EAAE;YACzB,IAAY,CAACC,KAAK,CAAC,GAAG,KAAK;;QAEhC,CAAC,CAAC;;;EAGR;EAaA3jB,WAAWA,CAACujB,KAAa;IACvB,IAAI,CAAC/d,aAAa,GAAG+d,KAAK;IAC1B,IAAI,CAAC9d,iBAAiB,GAAG,KAAK;IAC9B0a,YAAY,CAACyD,OAAO,CAAC,YAAY,EAAEL,KAAK,CAAC;EAC3C;EAgCA;EACQ9W,sBAAsBA,CAACoX,WAAmB;IAChD,IAAI,CAACne,YAAY,GAAG,KAAK;IACzB,IAAI,CAACrB,YAAY,CAACmI,QAAQ,CAAC,GAAGqX,WAAW,4BAA4B,CAAC;EACxE;EA8EAC,WAAWA,CAAA;IACT,IACG,IAAI,CAACjE,WAAW,CAACkE,OAAO,IAAI,CAAC,IAAI,CAACpf,YAAY,IAC/C,CAAC,IAAI,CAACnD,aAAa,IACnB,CAAC,IAAI,CAACvF,gBAAgB,EAAEqF,EAAE,EAC1B;MACA;;IAGF,IAAI,CAAC+d,OAAO,CAACK,mBAAmB,EAAE;IAElC,MAAMje,OAAO,GAAG,IAAI,CAACoe,WAAW,CAACmE,GAAG,CAAC,SAAS,CAAC,EAAEjG,KAAK;IAEtD,MAAMkG,WAAW,GAAY;MAC3B3iB,EAAE,EAAE,OAAO,GAAG,IAAI8I,IAAI,EAAE,CAACwX,OAAO,EAAE;MAClCngB,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtBJ,MAAM,EAAE;QACNC,EAAE,EAAE,IAAI,CAACE,aAAa,IAAI,EAAE;QAC5BtF,QAAQ,EAAE,IAAI,CAACwI;OAChB;MACD8d,QAAQ,EAAE;QACRlhB,EAAE,EAAE,IAAI,CAACrF,gBAAgB,CAACqF,EAAE;QAC5BpF,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC,QAAQ,IAAI;OAC7C;MACDyF,SAAS,EAAE,IAAIyI,IAAI,EAAE;MACrBuK,MAAM,EAAE,KAAK;MACbuP,SAAS,EAAE;KACZ;IAED,IAAI,IAAI,CAACvf,YAAY,EAAE;MACrB,IAAIwf,QAAQ,GAAG,MAAM;MACrB,IAAI,IAAI,CAACxf,YAAY,CAAC8I,IAAI,CAAC2W,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC/CD,QAAQ,GAAG,OAAO;QAElB,IAAI,IAAI,CAACvf,UAAU,EAAE;UACnBqf,WAAW,CAACI,WAAW,GAAG,CACxB;YACE/iB,EAAE,EAAE,iBAAiB;YACrB2M,GAAG,EAAE,IAAI,CAACrJ,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC0D,QAAQ,EAAE,GAAG,EAAE;YACtDmF,IAAI,EAAEpS,WAAW,CAACipB,KAAK;YACvBlX,IAAI,EAAE,IAAI,CAACzI,YAAY,CAACyI,IAAI;YAC5B6J,IAAI,EAAE,IAAI,CAACtS,YAAY,CAACsS;WACzB,CACF;;;MAIL,IAAIkN,QAAQ,KAAK,OAAO,EAAE;QACxBF,WAAW,CAACxW,IAAI,GAAGpS,WAAW,CAACipB,KAAK;OACrC,MAAM,IAAIH,QAAQ,KAAK,MAAM,EAAE;QAC9BF,WAAW,CAACxW,IAAI,GAAGpS,WAAW,CAACkpB,IAAI;;;IAIvC,IAAI,CAAChgB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE0f,WAAW,CAAC;IAE/C,MAAMO,UAAU,GAAG,IAAI,CAAC7f,YAAY;IACpC,IAAI,CAACkb,WAAW,CAAC4E,KAAK,EAAE;IACxB,IAAI,CAACxB,gBAAgB,EAAE;IAEvBhG,UAAU,CAAC,MAAM,IAAI,CAACjL,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAE/C,IAAI,CAACnN,WAAW,GAAG,IAAI;IAEvB,MAAM6f,OAAO,GAAG,IAAI,CAAC9hB,cAAc,CAACkhB,WAAW,CAC7C,IAAI,CAAC7nB,gBAAgB,CAACqF,EAAE,EACxBG,OAAO,EACP+iB,UAAU,IAAIG,SAAS,EACvBtpB,WAAW,CAACupB,IAAI,EAChB,IAAI,CAACpgB,YAAY,EAAElD,EAAE,CACtB,CAACuQ,SAAS,CAAC;MACVC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAAC8S,kBAAkB,CAACZ,WAAW,CAAC3iB,EAAG,EAAEyQ,OAAO,CAAC;QACjD,IAAI,CAAClN,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDqD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC2c,kBAAkB,CAACZ,WAAW,CAAC3iB,EAAG,EAAE,IAAI,EAAE,IAAI,CAAC;QACpD,IAAI,CAACuD,WAAW,GAAG,KAAK;QACxB,IAAI,CAACR,YAAY,CAACqN,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;IAEF,IAAI,CAACnM,aAAa,CAAC0P,GAAG,CAACyP,OAAO,CAAC;EACjC;EAEA;EACQG,kBAAkBA,CACxBC,MAAc,EACd3C,UAA2B,EAC3B4C,OAAA,GAAmB,KAAK;IAExB,IAAI,CAACxgB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACgJ,GAAG,CAAEC,GAAG,IAAI;MACxC,IAAIA,GAAG,CAAClM,EAAE,KAAKwjB,MAAM,EAAE;QACrB,IAAI3C,UAAU,EAAE;UACd,OAAOA,UAAU;SAClB,MAAM,IAAI4C,OAAO,EAAE;UAClB,OAAO;YACL,GAAGvX,GAAG;YACN0W,SAAS,EAAE,KAAK;YAChBa,OAAO,EAAE;WACV;;;MAGL,OAAOvX,GAAG;IACZ,CAAC,CAAC;EACJ;EA6CA;EACAwX,QAAQA,CAACxK,KAAU;IACjB,MAAMyK,SAAS,GAAGzK,KAAK,CAACgB,MAAM;IAC9B,MAAM0J,SAAS,GAAGD,SAAS,CAACC,SAAS;IAErC,IACEA,SAAS,GAAG,EAAE,IACd,CAAC,IAAI,CAAC7f,aAAa,IACnB,IAAI,CAACb,YAAY,EAAElD,EAAE,IACrB,IAAI,CAACgE,eAAe,EACpB;MACA,IAAI,CAAC8L,oBAAoB,EAAE;MAE3B,MAAM+T,eAAe,GAAGF,SAAS,CAACG,YAAY;MAC9C,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,EAAE;MAEzD,IAAI,CAACjgB,aAAa,GAAG,IAAI;MAEzB,IAAI,CAACkgB,gBAAgB,EAAE;MAEvB;MACAC,qBAAqB,CAAC,MAAK;QACzB,MAAMC,sBAAsB,GAAGA,CAAA,KAAK;UAClC,IAAIJ,mBAAmB,EAAE;YACvB,MAAMK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAC5CN,mBAAmB,CAAC/jB,EAAE,CACvB;YACD,IAAIokB,cAAc,EAAE;cAClB;cACAA,cAAc,CAACE,cAAc,CAAC;gBAAEC,KAAK,EAAE;cAAQ,CAAE,CAAC;aACnD,MAAM;cACL;cACA,MAAMC,eAAe,GAAGb,SAAS,CAACG,YAAY;cAC9C,MAAMW,UAAU,GAAGD,eAAe,GAAGX,eAAe;cACpDF,SAAS,CAACC,SAAS,GAAGA,SAAS,GAAGa,UAAU;;;UAIhD;UACA,IAAI,CAAC1U,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACA4L,UAAU,CAACwI,sBAAsB,EAAE,GAAG,CAAC;MACzC,CAAC,CAAC;;EAEN;EAEA;EACQH,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAACxU,iBAAiB,EAAEC,aAAa,IAAI,CAAC,IAAI,CAACxM,QAAQ,CAACT,MAAM,EACjE,OAAO,IAAI;IAEb,MAAMmhB,SAAS,GAAG,IAAI,CAACnU,iBAAiB,CAACC,aAAa;IACtD,MAAMiV,eAAe,GAAGf,SAAS,CAACgB,gBAAgB,CAAC,eAAe,CAAC;IAEnE,KAAK,IAAI7d,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4d,eAAe,CAACliB,MAAM,EAAEsE,CAAC,EAAE,EAAE;MAC/C,MAAM8d,OAAO,GAAGF,eAAe,CAAC5d,CAAC,CAAC;MAClC,MAAM+d,IAAI,GAAGD,OAAO,CAACE,qBAAqB,EAAE;MAE5C;MACA,IAAID,IAAI,CAACE,GAAG,IAAI,CAAC,IAAIF,IAAI,CAACG,MAAM,IAAIrB,SAAS,CAACsB,YAAY,EAAE;QAC1D,MAAMjM,SAAS,GAAG4L,OAAO,CAACM,YAAY,CAAC,iBAAiB,CAAC;QACzD,OAAO,IAAI,CAACjiB,QAAQ,CAAC6c,IAAI,CAAE5R,CAAC,IAAKA,CAAC,CAAClO,EAAE,KAAKgZ,SAAS,CAAC,IAAI,IAAI;;;IAIhE,OAAO,IAAI;EACb;EAEA;EACQqL,kBAAkBA,CACxBrL,SAA6B;IAE7B,IAAI,CAAC,IAAI,CAACxJ,iBAAiB,EAAEC,aAAa,IAAI,CAACuJ,SAAS,EAAE,OAAO,IAAI;IACrE,OAAO,IAAI,CAACxJ,iBAAiB,CAACC,aAAa,CAAC0V,aAAa,CACvD,qBAAqBnM,SAAS,IAAI,CACnC;EACH;EAuBA;EACAiL,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAClgB,aAAa,IAAI,CAAC,IAAI,CAACb,YAAY,EAAElD,EAAE,IAAI,CAAC,IAAI,CAACgE,eAAe,EACvE;IAEF;IACA,IAAI,CAACD,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAACD,WAAW,EAAE;IAElB;IACA,IAAI,CAACxC,cAAc,CAACke,eAAe,CACjC,IAAI,CAACtc,YAAY,CAAClD,EAAE,EACpB,IAAI,CAAC4D,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB,CAACyM,SAAS,CAAC;MACVC,IAAI,EAAGtN,YAAY,IAAI;QACrB,IACEA,YAAY,IACZA,YAAY,CAACD,QAAQ,IACrBC,YAAY,CAACD,QAAQ,CAACT,MAAM,GAAG,CAAC,EAChC;UACA;UACA,MAAM4iB,WAAW,GAAG,CAAC,GAAG,IAAI,CAACniB,QAAQ,CAAC;UAEtC;UACA,MAAMoiB,WAAW,GAAG,IAAIlgB,GAAG,CAACigB,WAAW,CAACnZ,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAClM,EAAE,CAAC,CAAC;UAE7D;UACA,MAAMslB,WAAW,GAAGpiB,YAAY,CAACD,QAAQ,CACtC9I,MAAM,CAAE+R,GAAG,IAAK,CAACmZ,WAAW,CAAC5R,GAAG,CAACvH,GAAG,CAAClM,EAAE,CAAC,CAAC,CACzCkgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACb,MAAMC,KAAK,GAAG,IAAIvX,IAAI,CAACqX,CAAC,CAAC9f,SAAmB,CAAC,CAACigB,OAAO,EAAE;YACvD,MAAMC,KAAK,GAAG,IAAIzX,IAAI,CAACsX,CAAC,CAAC/f,SAAmB,CAAC,CAACigB,OAAO,EAAE;YACvD,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEJ,IAAI+E,WAAW,CAAC9iB,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAI,CAACS,QAAQ,GAAG,CAAC,GAAGqiB,WAAW,EAAE,GAAGF,WAAW,CAAC;YAEhD;YACA,IAAI,IAAI,CAACniB,QAAQ,CAACT,MAAM,GAAG,IAAI,CAACqB,kBAAkB,EAAE;cAClD,IAAI,CAACZ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACsiB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC1hB,kBAAkB,CAAC;;YAGjE;YACA,IAAI,CAACG,eAAe,GAClBshB,WAAW,CAAC9iB,MAAM,IAAI,IAAI,CAACoB,oBAAoB;WAClD,MAAM;YACL;YACA,IAAI,CAACI,eAAe,GAAG,KAAK;;SAE/B,MAAM;UACL,IAAI,CAACA,eAAe,GAAG,KAAK;;QAG9B;QACA;QACA2X,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5X,aAAa,GAAG,KAAK;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD6C,KAAK,EAAGA,KAAK,IAAI;QACfsL,OAAO,CAACtL,KAAK,CAAC,aAAa,EAAE,8BAA8B,EAAEA,KAAK,CAAC;QACnE,IAAI,CAAC7C,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACgM,oBAAoB,EAAE;QAC3B,IAAI,CAAChN,YAAY,CAACqN,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACQoV,eAAeA,CACrBC,UAAqC,EACrCC,UAAqC;IAErC,IAAI,CAACD,UAAU,IAAI,CAACC,UAAU,EAAE,OAAO,KAAK;IAE5C,IAAI;MACF,MAAMC,KAAK,GACTF,UAAU,YAAY3c,IAAI,GACtB2c,UAAU,CAACnF,OAAO,EAAE,GACpB,IAAIxX,IAAI,CAAC2c,UAAoB,CAAC,CAACnF,OAAO,EAAE;MAC9C,MAAMsF,KAAK,GACTF,UAAU,YAAY5c,IAAI,GACtB4c,UAAU,CAACpF,OAAO,EAAE,GACpB,IAAIxX,IAAI,CAAC4c,UAAoB,CAAC,CAACpF,OAAO,EAAE;MAC9C,OAAO3G,IAAI,CAACkM,GAAG,CAACF,KAAK,GAAGC,KAAK,CAAC,GAAG,IAAI;KACtC,CAAC,OAAOhf,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEA8J,cAAcA,CAACoV,KAAA,GAAiB,KAAK;IACnC,IAAI;MACF,IAAI,CAAC,IAAI,CAACtW,iBAAiB,EAAEC,aAAa,EAAE;MAE5CyU,qBAAqB,CAAC,MAAK;QACzB,MAAMP,SAAS,GAAG,IAAI,CAACnU,iBAAiB,CAACC,aAAa;QACtD,MAAMsW,kBAAkB,GACtBpC,SAAS,CAACG,YAAY,GAAGH,SAAS,CAACsB,YAAY,IAC/CtB,SAAS,CAACC,SAAS,GAAG,GAAG;QAE3B,IAAIkC,KAAK,IAAIC,kBAAkB,EAAE;UAC/BpC,SAAS,CAACqC,QAAQ,CAAC;YACjBjB,GAAG,EAAEpB,SAAS,CAACG,YAAY;YAC3BmC,QAAQ,EAAE;WACX,CAAC;;MAEN,CAAC,CAAC;KACH,CAAC,OAAOC,GAAG,EAAE;MACZhU,OAAO,CAACtL,KAAK,CAAC,aAAa,EAAE,4BAA4B,EAAEsf,GAAG,CAAC;;EAEnE;EA0CA;;;;EAIAC,mBAAmBA,CAACC,QAAgB;IAClCxZ,MAAM,CAACyZ,IAAI,CAACD,QAAQ,EAAE,QAAQ,CAAC;EACjC;EAEA;;;EAGAE,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC5V,cAAc,EAAE;IAErB;IACA;EACF;EAEA;;;EAGA6V,qBAAqBA,CAAA;IACnB,IAAI,CAACzjB,MAAM,CAAC0jB,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEA;;;;EAIAC,WAAWA,CAAC3L,KAAa;IACvB,MAAM4L,OAAO,GAAG,IAAI,CAACnI,WAAW,CAACmE,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAIgE,OAAO,EAAE;MACX,MAAMC,YAAY,GAAGD,OAAO,CAACjK,KAAK,IAAI,EAAE;MACxCiK,OAAO,CAACE,QAAQ,CAACD,YAAY,GAAG7L,KAAK,CAAC;MACtC4L,OAAO,CAACG,WAAW,EAAE;MACrB;MACAlL,UAAU,CAAC,MAAK;QACd,MAAMmL,YAAY,GAAG9Z,QAAQ,CAACmY,aAAa,CACzC,uBAAuB,CACJ;QACrB,IAAI2B,YAAY,EAAE;UAChBA,YAAY,CAACC,KAAK,EAAE;;MAExB,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQhI,wBAAwBA,CAAA;IAC9B,MAAMiI,eAAe,GACnB,IAAI,CAAC1lB,cAAc,CAAC2lB,2BAA2B,EAAE,CAAC1W,SAAS,CAAC;MAC1DC,IAAI,EAAGkG,YAAY,IAAI;QACrB,IAAI,CAACzR,aAAa,CAACiiB,OAAO,CAACxQ,YAAY,CAAC;QACxC,IAAI,CAACvC,uBAAuB,EAAE;QAE9B,IAAI,CAAC7S,cAAc,CAAC6lB,IAAI,CAAC,cAAc,CAAC;QAExC,IACEzQ,YAAY,CAACvK,IAAI,KAAK,aAAa,IACnCuK,YAAY,CAACyE,cAAc,KAAK,IAAI,CAACjY,YAAY,EAAElD,EAAE,EACrD;UACA,IAAI0W,YAAY,CAAC1W,EAAE,EAAE;YACnB,IAAI,CAACsB,cAAc,CAACuT,UAAU,CAAC,CAAC6B,YAAY,CAAC1W,EAAE,CAAC,CAAC,CAACuQ,SAAS,EAAE;;;MAGnE,CAAC;MACD3J,KAAK,EAAGA,KAAK,IAAI,CAAE;KACpB,CAAC;IACJ,IAAI,CAAC3C,aAAa,CAAC0P,GAAG,CAACqT,eAAe,CAAC;IAEvC,MAAMI,oBAAoB,GAAG,IAAI,CAAC9lB,cAAc,CAAC+lB,cAAc,CAAC9W,SAAS,CAAC;MACxEC,IAAI,EAAGvL,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACkP,uBAAuB,EAAE;MAChC,CAAC;MACDvN,KAAK,EAAGA,KAAK,IAAI,CAAE;KACpB,CAAC;IACF,IAAI,CAAC3C,aAAa,CAAC0P,GAAG,CAACyT,oBAAoB,CAAC;IAE5C,MAAME,oBAAoB,GACxB,IAAI,CAAChmB,cAAc,CAACimB,kBAAkB,CAAChX,SAAS,CAAC;MAC/CC,IAAI,EAAG/U,KAAK,IAAI;QACd,IAAI,CAACuG,uBAAuB,GAAGvG,KAAK;MACtC;KACD,CAAC;IACJ,IAAI,CAACwI,aAAa,CAAC0P,GAAG,CAAC2T,oBAAoB,CAAC;IAE5C,MAAME,OAAO,GAAG,IAAI,CAAClmB,cAAc,CAACmmB,aAAa,CAAClX,SAAS,CAAC;MAC1DC,IAAI,EAAGW,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC1M,YAAY,GAAG0M,IAAI;UACxB,IAAI,CAACxM,aAAa,GAAG,IAAI;UACzB,IAAI,CAACrD,cAAc,CAAC6lB,IAAI,CAAC,UAAU,CAAC;SACrC,MAAM;UACL,IAAI,CAACxiB,aAAa,GAAG,KAAK;UAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;;MAE5B;KACD,CAAC;IACF,IAAI,CAACR,aAAa,CAAC0P,GAAG,CAAC6T,OAAO,CAAC;IAE/B,MAAME,aAAa,GAAG,IAAI,CAACpmB,cAAc,CAACoQ,WAAW,CAACnB,SAAS,CAAC;MAC9DC,IAAI,EAAGW,IAAI,IAAI;QACb,IAAI,CAACzM,UAAU,GAAGyM,IAAI;QACtB,IAAIA,IAAI,EAAE;UACR,IAAI,CAACvM,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACgO,oBAAoB,EAAE;SAC5B,MAAM;UACL,IAAI,CAAChO,mBAAmB,GAAG,KAAK;UAChC,IAAI,CAACiO,mBAAmB,EAAE;UAC1B,IAAI,CAACC,oBAAoB,EAAE;;MAE/B;KACD,CAAC;IACF,IAAI,CAAC7O,aAAa,CAAC0P,GAAG,CAAC+T,aAAa,CAAC;IAErC;IACA,MAAMC,cAAc,GAAG,IAAI,CAACrmB,cAAc,CAACsmB,YAAY,CAACrX,SAAS,CAAC;MAChEC,IAAI,EAAGqX,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAAC5e,iBAAiB,EAAE;UACpC,IAAI,CAACA,iBAAiB,CAAC6e,SAAS,GAAGD,MAAM;;MAE7C;KACD,CAAC;IACF,IAAI,CAAC5jB,aAAa,CAAC0P,GAAG,CAACgU,cAAc,CAAC;IAEtC;IACA,MAAMI,eAAe,GAAG,IAAI,CAACzmB,cAAc,CAAC0mB,aAAa,CAACzX,SAAS,CAAC;MAClEC,IAAI,EAAGqX,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAAC3e,kBAAkB,EAAE;UACrC,IAAI,CAACA,kBAAkB,CAAC4e,SAAS,GAAGD,MAAM;;MAE9C;KACD,CAAC;IACF,IAAI,CAAC5jB,aAAa,CAAC0P,GAAG,CAACoU,eAAe,CAAC;EACzC;EAsIA;;;EAGA/U,iBAAiBA,CAACiV,OAAA,GAAmB,KAAK;IACxC,MAAMC,OAAO,GAAG,IAAI,CAAC5mB,cAAc,CAAC6mB,gBAAgB,CAClDF,OAAO,EACP,CAAC,EACD,EAAE,CACH,CAAC1X,SAAS,CAAC;MACVC,IAAI,EAAGvL,aAAa,IAAI;QACtB,IAAIgjB,OAAO,EAAE;UACX,IAAI,CAAChjB,aAAa,GAAGA,aAAa;SACnC,MAAM;UACL,IAAI,CAACA,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAGA,aAAa,CAAC;;QAGhE,IAAI,CAACkP,uBAAuB,EAAE;MAChC,CAAC;MACDvN,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7D,YAAY,CAACqN,SAAS,CACzB,6CAA6C,CAC9C;MACH;KACD,CAAC;IAEF,IAAI,CAACnM,aAAa,CAAC0P,GAAG,CAACuU,OAAO,CAAC;EACjC;EAsNA;;;EAGQlJ,qBAAqBA,CAAA;IAC3B,MAAMoJ,SAAS,GAAG,IAAI,CAAC9mB,cAAc,CAAC0d,qBAAqB,EAAE,CAACzO,SAAS,CAAC;MACtEC,IAAI,EAAGqH,IAAU,IAAI;QACnB,IAAI,CAACwQ,sBAAsB,CAACxQ,IAAI,CAAC;MACnC,CAAC;MACDjR,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;IAEF,IAAI,CAAC3C,aAAa,CAAC0P,GAAG,CAACyU,SAAS,CAAC;EACnC;EAEA;;;EAGQC,sBAAsBA,CAACxQ,IAAU;IACvC,IAAI,CAACA,IAAI,CAAC7X,EAAE,EAAE;IAEd,IAAI6X,IAAI,CAAC/c,QAAQ,EAAE;MACjB,IAAI,CAAC6N,WAAW,CAAC2f,GAAG,CAACzQ,IAAI,CAAC7X,EAAE,EAAE6X,IAAI,CAAC;KACpC,MAAM;MACL,IAAI,CAAClP,WAAW,CAAC+K,MAAM,CAACmE,IAAI,CAAC7X,EAAE,CAAC;;IAGlC,IAAI,IAAI,CAACrF,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACqF,EAAE,KAAK6X,IAAI,CAAC7X,EAAE,EAAE;MACjE,IAAI,CAACrF,gBAAgB,GAAG;QAAE,GAAG,IAAI,CAACA,gBAAgB;QAAE,GAAGkd;MAAI,CAAE;;EAEjE;EAEA;;;EAGQoH,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAAC/e,aAAa,EAAE;IAEzB,MAAMqoB,YAAY,GAAG,IAAI,CAACjnB,cAAc,CAACknB,aAAa,CACpD,IAAI,CAACtoB,aAAa,CACnB,CAACqQ,SAAS,CAAC;MACVC,IAAI,EAAGqH,IAAU,IAAI;QACnB,IAAI,CAACra,iBAAiB,GAAG,QAAQ;QACjC,IAAI,CAACqL,gBAAgB,GAAG,IAAIC,IAAI,EAAE;MACpC,CAAC;MACDlC,KAAK,EAAGA,KAAK,IAAI;QACfsL,OAAO,CAACtL,KAAK,CACX,aAAa,EACb,2CAA2C,EAC3CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3C,aAAa,CAAC0P,GAAG,CAAC4U,YAAY,CAAC;EACtC;EAEA;;;EAGQrJ,qBAAqBA,CAAA;IAC3B,MAAMuJ,MAAM,GAAG,CACb,WAAW,EACX,WAAW,EACX,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,OAAO,CACR;IAEDA,MAAM,CAACzU,OAAO,CAAEkF,KAAK,IAAI;MACvBlM,QAAQ,CAACmS,gBAAgB,CAACjG,KAAK,EAAE,IAAI,CAACwP,cAAc,CAACtJ,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IACxE,CAAC,CAAC;EACJ;EAEA;;;EAGQsJ,cAAcA,CAAA;IACpB,IAAI,CAAC7f,gBAAgB,GAAG,IAAIC,IAAI,EAAE;IAElC;IACA,IAAI,IAAI,CAACC,eAAe,EAAE;MACxBmV,YAAY,CAAC,IAAI,CAACnV,eAAe,CAAC;;IAGpC;IACA,IACE,IAAI,CAACvL,iBAAiB,KAAK,MAAM,IACjC,IAAI,CAACA,iBAAiB,KAAK,SAAS,EACpC;MACA,IAAI,CAACN,gBAAgB,CAAC,QAAQ,CAAC;;IAGjC;IACA,IAAI,CAAC6L,eAAe,GAAG4S,UAAU,CAAC,MAAK;MACrC,IAAI,IAAI,CAACne,iBAAiB,KAAK,QAAQ,EAAE;QACvC,IAAI,CAACN,gBAAgB,CAAC,MAAM,CAAC;;IAEjC,CAAC,EAAE,IAAI,CAACiE,CAAC,CAACoF,MAAM,CAACC,IAAI,CAAC;EACxB;EAEA;;;EAGAtJ,gBAAgBA,CAACkK,MAAc;IAC7B,IAAI,CAAC,IAAI,CAAClH,aAAa,EAAE;IAEzB,MAAMyoB,cAAc,GAAG,IAAI,CAACnrB,iBAAiB;IAE7C,IAAIorB,gBAAgB;IACpB,IAAIxhB,MAAM,KAAK,QAAQ,EAAE;MACvBwhB,gBAAgB,GAAG,IAAI,CAACtnB,cAAc,CAACknB,aAAa,CAAC,IAAI,CAACtoB,aAAa,CAAC;KACzE,MAAM;MACL0oB,gBAAgB,GAAG,IAAI,CAACtnB,cAAc,CAAC6c,cAAc,CAAC,IAAI,CAACje,aAAa,CAAC;;IAG3E,MAAM2oB,SAAS,GAAGD,gBAAgB,CAACrY,SAAS,CAAC;MAC3CC,IAAI,EAAGqH,IAAU,IAAI;QACnB,IAAI,CAACra,iBAAiB,GAAG4J,MAAM;QAE/B,IAAIA,MAAM,KAAKuhB,cAAc,EAAE;UAC7B,MAAMG,UAAU,GAAG,IAAI,CAAC3qB,aAAa,CAACiJ,MAAM,CAAC;UAC7C,IAAI,CAACrE,YAAY,CAACmI,QAAQ,CAAC,YAAY4d,UAAU,EAAE,CAAC;;MAExD,CAAC;MACDliB,KAAK,EAAGA,KAAK,IAAI;QACfsL,OAAO,CAACtL,KAAK,CACX,aAAa,EACb,yCAAyC,EACzCA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3C,aAAa,CAAC0P,GAAG,CAACkV,SAAS,CAAC;EACnC;EAEA;;;EAGAE,eAAeA,CAAA;IACb,MAAMC,QAAQ,GAAG,IAAI,CAAC1nB,cAAc,CAAC2nB,WAAW,CAC9C,KAAK,EACL5F,SAAS,EACT,CAAC,EACD,EAAE,EACF,UAAU,EACV,KAAK,EACL,IAAI,CACL,CAAC9S,SAAS,CAAC;MACVC,IAAI,EAAG0Y,KAAa,IAAI;QACtBA,KAAK,CAAClV,OAAO,CAAE6D,IAAI,IAAI;UACrB,IAAIA,IAAI,CAAC/c,QAAQ,IAAI+c,IAAI,CAAC7X,EAAE,EAAE;YAC5B,IAAI,CAAC2I,WAAW,CAAC2f,GAAG,CAACzQ,IAAI,CAAC7X,EAAE,EAAE6X,IAAI,CAAC;;QAEvC,CAAC,CAAC;MACJ,CAAC;MACDjR,KAAK,EAAGA,KAAK,IAAI;QACfsL,OAAO,CAACtL,KAAK,CACX,aAAa,EACb,qDAAqD,EACrDA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3C,aAAa,CAAC0P,GAAG,CAACqV,QAAQ,CAAC;EAClC;EAgJAG,gBAAgBA,CAAC1Y,OAAY;IAC3B,IAAI,CAAC1K,SAAS,CAAC0K,OAAO,CAACzQ,EAAE,CAAC,GAAG,IAAI;IACjC2b,UAAU,CAAC,MAAK;MACd,IAAI,CAAC5V,SAAS,CAAC0K,OAAO,CAACzQ,EAAE,CAAC,GAAG,KAAK;MAClC,IAAI,CAAC8F,cAAc,CAAC2K,OAAO,CAACzQ,EAAE,CAAC,GAAG,KAAK;IACzC,CAAC,EAAE,IAAI,CAAC;EACV;EA6OAopB,WAAWA,CAAA;IACT,IAAI,CAACrL,OAAO,CAACC,aAAa,EAAE;IAC5B,IAAI,CAACD,OAAO,CAACI,cAAc,EAAE;IAC7B,IAAI,CAACJ,OAAO,CAACK,mBAAmB,EAAE;IAClC,IAAI,CAACna,aAAa,CAAC2N,WAAW,EAAE;IAChC5E,QAAQ,CAACqc,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACpP,eAAe,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;EACxE;;;uBAjsEWne,oBAAoB,EAAA7G,EAAA,CAAAkvB,iBAAA,CAAAC,EAAA,CAAAjoB,cAAA,GAAAlH,EAAA,CAAAkvB,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAArvB,EAAA,CAAAkvB,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAvvB,EAAA,CAAAkvB,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAzvB,EAAA,CAAAkvB,iBAAA,CAAAQ,EAAA,CAAAC,iBAAA,GAAA3vB,EAAA,CAAAkvB,iBAAA,CAAAE,EAAA,CAAAQ,MAAA,GAAA5vB,EAAA,CAAAkvB,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAA9vB,EAAA,CAAAkvB,iBAAA,CAAAlvB,EAAA,CAAA+vB,iBAAA;IAAA;EAAA;;;YAApBlpB,oBAAoB;MAAAmZ,SAAA;MAAAgQ,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UC9BjClwB,EAAA,CAAAC,cAAA,aAA4B;UAGhBD,EAAA,CAAAuB,UAAA,mBAAA6uB,sDAAA;YAAA,OAASD,GAAA,CAAAhE,qBAAA,EAAuB;UAAA,EAAC;UACvCnsB,EAAA,CAAA+B,SAAA,WAAiC;UACnC/B,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAA+B,SAAA,aAIE;UAEF/B,EAAA,CAAAgC,UAAA,IAAAquB,mCAAA,iBASM;UAENrwB,EAAA,CAAAC,cAAA,aAA8B;UAE5BD,EAAA,CAAAgC,UAAA,IAAAsuB,4CAAA,0BAsBe;UAGftwB,EAAA,CAAAC,cAAA,aAAsB;UAElBD,EAAA,CAAAuB,UAAA,mBAAAgvB,sDAAA;YAAA,OAASJ,GAAA,CAAAntB,oBAAA,EAAsB;UAAA,EAAC;UAOhChD,EAAA,CAAA+B,SAAA,aAGK;UAEL/B,EAAA,CAAAgC,UAAA,KAAAwuB,qCAAA,mBAGQ;UACVxwB,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAgC,UAAA,KAAAyuB,oCAAA,mBA8DM;UACRzwB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAsB;UAElBD,EAAA,CAAAuB,UAAA,mBAAAmvB,uDAAA;YAAA,OAASP,GAAA,CAAArgB,mBAAA,EAAqB;UAAA,EAAC;UAG/B9P,EAAA,CAAA+B,SAAA,aAA8B;UAChC/B,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAgC,UAAA,KAAA2uB,oCAAA,kBAgCM;UACR3wB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAsB;UAElBD,EAAA,CAAAuB,UAAA,mBAAAqvB,uDAAA;YAAA,OAAST,GAAA,CAAApgB,cAAA,EAAgB;UAAA,EAAC;UAI1B/P,EAAA,CAAA+B,SAAA,aAAiC;UACnC/B,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAgC,UAAA,KAAA6uB,oCAAA,mBA+CM;UACR7wB,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,mBAA8C;UAC5CD,EAAA,CAAAgC,UAAA,KAAA8uB,oCAAA,kBAKM;UAEN9wB,EAAA,CAAAgC,UAAA,KAAA+uB,oCAAA,kBAUM;UAEN/wB,EAAA,CAAAgC,UAAA,KAAAgvB,oCAAA,kBAcM;UACRhxB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAwB;UAGpBD,EAAA,CAAAuB,UAAA,sBAAA0vB,wDAAA;YAAA,OAAYd,GAAA,CAAA/H,WAAA,EAAa;UAAA,EAAC;UAG1BpoB,EAAA,CAAA+B,SAAA,iBAKE;UACF/B,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAA+B,SAAA,aAAkC;UACpC/B,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAAoE;UAE5DD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,kBAA8D;UAApCD,EAAA,CAAAuB,UAAA,mBAAA2vB,uDAAA;YAAA,OAASf,GAAA,CAAAzoB,uBAAA,EAAyB;UAAA,EAAC;UAC3D1H,EAAA,CAAA+B,SAAA,aAA4B;UAC9B/B,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAgC,UAAA,KAAAmvB,oCAAA,kBASM;UACNnxB,EAAA,CAAAgC,UAAA,KAAAovB,oCAAA,kBAQM;UACRpxB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAgC,UAAA,KAAAqvB,oCAAA,kBAsCM;UACRrxB,EAAA,CAAAG,YAAA,EAAM;;;UA/WAH,EAAA,CAAAI,SAAA,GAAqE;UAArEJ,EAAA,CAAAiB,UAAA,SAAAkvB,GAAA,CAAA5vB,gBAAA,kBAAA4vB,GAAA,CAAA5vB,gBAAA,CAAA6gB,KAAA,yCAAAphB,EAAA,CAAAsxB,aAAA,CAAqE;UAK/CtxB,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,SAAAkvB,GAAA,CAAA5vB,gBAAA,CAAsB;UAaXP,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAiB,UAAA,YAAAkvB,GAAA,CAAA/oB,gBAAA,GAAqB;UA6BlDpH,EAAA,CAAAI,SAAA,GAEE;UAFFJ,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAAqwB,GAAA,EAAApB,GAAA,CAAAjmB,kBAAA,EAEE;UAIAlK,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAa,UAAA,CAAAsvB,GAAA,CAAA9S,aAAA,CAAA8S,GAAA,CAAA/sB,iBAAA,EAA0C;UAC1CpD,EAAA,CAAAiB,UAAA,YAAAkvB,GAAA,CAAArsB,cAAA,CAAAqsB,GAAA,CAAA/sB,iBAAA,EAA6C;UAI5CpD,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,SAAAkvB,GAAA,CAAAjtB,gBAAA,CAAsB;UAOxBlD,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAiB,UAAA,SAAAkvB,GAAA,CAAAjmB,kBAAA,CAAwB;UA2ExBlK,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAiB,UAAA,SAAAkvB,GAAA,CAAApmB,iBAAA,CAAuB;UA8CvB/J,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAiB,UAAA,SAAAkvB,GAAA,CAAAnmB,YAAA,CAAkB;UAsDtBhK,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAiB,UAAA,SAAAkvB,GAAA,CAAApnB,OAAA,CAAa;UAOb/I,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAiB,UAAA,UAAAkvB,GAAA,CAAApnB,OAAA,IAAAonB,GAAA,CAAAtnB,QAAA,CAAAT,MAAA,OAAuC;UAYpBpI,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAiB,UAAA,YAAAkvB,GAAA,CAAAtnB,QAAA,CAAW;UAmB/B7I,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAiB,UAAA,cAAAkvB,GAAA,CAAAhM,WAAA,CAAyB;UAQvBnkB,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAiB,UAAA,cAAAkvB,GAAA,CAAA5vB,gBAAA,CAA8B;UAK9BP,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAiB,UAAA,aAAAkvB,GAAA,CAAAhM,WAAA,CAAAkE,OAAA,KAAA8H,GAAA,CAAA5vB,gBAAA,CAAqD;UAQnCP,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAAswB,GAAA,EAAArB,GAAA,CAAAxoB,qBAAA,EAA2C;UAQ9D3H,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAiB,UAAA,SAAAkvB,GAAA,CAAAtlB,aAAA,CAAAzC,MAAA,OAAgC;UAURpI,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAiB,UAAA,YAAAkvB,GAAA,CAAAtlB,aAAA,CAAgB;UAY1C7K,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAiB,UAAA,SAAAkvB,GAAA,CAAA7oB,aAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}