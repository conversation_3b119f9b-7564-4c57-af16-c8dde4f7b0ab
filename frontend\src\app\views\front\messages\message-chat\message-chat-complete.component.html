<div class="chat-container">
  <!-- En-tête -->
  <div class="chat-header">
    <button (click)="goBackToConversations()" class="action-btn">
      <i class="fas fa-arrow-left"></i>
    </button>

    <img
      [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
      alt="User avatar"
      class="user-avatar"
    />

    <div class="user-info" *ngIf="otherParticipant">
      <h3>{{ otherParticipant.username }}</h3>
      <div class="user-status">
        {{
          otherParticipant.isOnline
            ? "En ligne"
            : formatLastActive(otherParticipant.lastActive)
        }}
      </div>
    </div>

    <div class="action-buttons">
      <!-- Boutons d'action consolidés -->
      <ng-container *ngFor="let action of getHeaderActions()">
        <button
          class="action-btn"
          [ngClass]="
            action.activeClass && action.isActive ? action.activeClass : {}
          "
          [title]="action.title"
          (click)="action.onClick()"
        >
          <i [class]="action.icon"></i>
          <!-- Badge universel -->
          <span
            *ngIf="action.badge && action.badge.count > 0"
            class="badge"
            [ngClass]="{ 'animate-pulse': action.badge.animate }"
          >
            {{ action.badge.count > 99 ? "99+" : action.badge.count }}
          </span>
        </button>
      </ng-container>
    </div>
  </div>

  <!-- Zone des messages -->
  <div class="messages-area" #messagesContainer (scroll)="onScroll($event)">
    <!-- Indicateur de chargement -->
    <div *ngIf="loading" class="loading-indicator">
      <i class="fas fa-spinner fa-spin"></i> Chargement...
    </div>

    <!-- Message d'état vide -->
    <div *ngIf="!loading && messages.length === 0" class="empty-state">
      <i class="fas fa-comments"></i>
      <p>Aucun message pour le moment</p>
      <p>Commencez la conversation !</p>
    </div>

    <!-- Bouton charger plus -->
    <div *ngIf="hasMoreMessages && !loading" class="load-more-container">
      <button
        (click)="loadMoreMessages()"
        class="load-more-btn"
        [disabled]="isLoadingMore"
      >
        <i class="fas fa-chevron-up" *ngIf="!isLoadingMore"></i>
        <i class="fas fa-spinner fa-spin" *ngIf="isLoadingMore"></i>
        {{ isLoadingMore ? "Chargement..." : "Charger plus" }}
      </button>
    </div>

    <!-- Messages -->
    <ng-container
      *ngFor="let message of messages; let i = index; trackBy: c.trackById"
    >
      <!-- En-tête de date -->
      <div *ngIf="shouldShowDateHeader(i)" class="date-header">
        {{ formatMessageDate(message.timestamp) }}
      </div>

      <!-- Message -->
      <div
        class="message"
        [ngClass]="{
          'current-user': message.sender?.id === currentUserId,
          'other-user': message.sender?.id !== currentUserId,
          editing: editingMessageId === message.id
        }"
        [id]="'message-' + message.id"
      >
        <!-- Avatar pour les autres utilisateurs -->
        <div
          *ngIf="message.sender?.id !== currentUserId"
          class="message-avatar"
        >
          <img
            [src]="message.sender?.image || 'assets/images/default-avatar.png'"
            [alt]="message.sender?.username"
          />
        </div>

        <!-- Contenu du message -->
        <div class="message-content">
          <!-- Message de réponse -->
          <div *ngIf="message.replyTo" class="reply-message">
            <div class="reply-content">
              <span class="reply-author">{{
                message.replyTo.sender?.username
              }}</span>
              <p>{{ message.replyTo.content }}</p>
            </div>
          </div>

          <!-- Bulle de message -->
          <div class="message-bubble" [ngClass]="getMessageTypeClass(message)">
            <!-- Mode édition -->
            <div *ngIf="editingMessageId === message.id" class="edit-mode">
              <input
                [(ngModel)]="editingContent"
                class="edit-input"
                (keyup.enter)="saveEditedMessage(message.id)"
                (keyup.escape)="cancelEdit()"
              />
              <div class="edit-actions">
                <button
                  (click)="saveEditedMessage(message.id)"
                  class="save-btn"
                >
                  <i class="fas fa-check"></i>
                </button>
                <button (click)="cancelEdit()" class="cancel-btn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>

            <!-- Contenu normal -->
            <div *ngIf="editingMessageId !== message.id">
              <!-- Message texte -->
              <div
                *ngIf="getMessageType(message) === 'text'"
                class="text-content"
              >
                <p
                  [innerHTML]="
                    searchAndReactionMethods.highlightSearchTerms(
                      message.content,
                      searchQuery
                    )
                  "
                ></p>
              </div>

              <!-- Message image -->
              <div *ngIf="hasImage(message)" class="image-content">
                <img
                  [src]="getImageUrl(message)"
                  [alt]="message.content"
                  (click)="openImageFullscreen(getImageUrl(message))"
                  class="message-image"
                />
                <p *ngIf="message.content" class="image-caption">
                  {{ message.content }}
                </p>
              </div>

              <!-- Message vocal -->
              <div *ngIf="isVoiceMessage(message)" class="voice-content">
                <div class="voice-player">
                  <button
                    (click)="toggleVoicePlayback(message.id)"
                    class="voice-play-btn"
                    [ngClass]="{ playing: playingVoiceId === message.id }"
                  >
                    <i
                      class="fas fa-play"
                      *ngIf="playingVoiceId !== message.id"
                    ></i>
                    <i
                      class="fas fa-pause"
                      *ngIf="playingVoiceId === message.id"
                    ></i>
                  </button>

                  <div class="voice-waveform">
                    <div
                      *ngFor="
                        let bar of getVoiceWaveform(message);
                        let j = index
                      "
                      class="voice-bar"
                      [style.height.px]="getVoiceBarHeight(message, j)"
                      [ngClass]="{
                        active: j <= voicePlaybackProgress[message.id]
                      }"
                    ></div>
                  </div>

                  <span class="voice-duration">{{
                    formatVoiceDuration(getVoiceMessageDuration(message))
                  }}</span>
                </div>
              </div>

              <!-- Métadonnées du message -->
              <div class="message-meta">
                <span class="message-time">{{
                  formatMessageTime(message.timestamp)
                }}</span>
                <span
                  *ngIf="message.sender?.id === currentUserId && message.status"
                  class="message-status"
                >
                  <i class="fas fa-check" *ngIf="message.status === 'sent'"></i>
                  <i
                    class="fas fa-check-double"
                    *ngIf="message.status === 'delivered'"
                  ></i>
                  <i
                    class="fas fa-check-double text-blue-500"
                    *ngIf="message.status === 'read'"
                  ></i>
                </span>
                <span *ngIf="message.edited" class="edited-indicator"
                  >modifié</span
                >
              </div>
            </div>

            <!-- Réactions -->
            <div
              *ngIf="message.reactions && message.reactions.length > 0"
              class="message-reactions"
            >
              <span
                *ngFor="let reaction of message.reactions"
                class="reaction"
                (click)="toggleReaction(message.id, reaction.emoji)"
                [ngClass]="{
                  'own-reaction': reaction.users.includes(currentUserId)
                }"
              >
                {{ reaction.emoji }} {{ reaction.count }}
              </span>
            </div>
          </div>

          <!-- Options du message -->
          <div
            class="message-options"
            [ngClass]="{ show: showMessageOptions[message.id] }"
          >
            <button
              (click)="replyToMessage(message)"
              class="option-btn"
              title="Répondre"
            >
              <i class="fas fa-reply"></i>
            </button>
            <button
              (click)="
                searchAndReactionMethods.toggleReactionPicker(message.id)
              "
              class="option-btn"
              title="Réagir"
            >
              <i class="fas fa-smile"></i>
            </button>
            <button
              *ngIf="message.sender?.id === currentUserId"
              (click)="startEditMessage(message)"
              class="option-btn"
              title="Modifier"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button
              (click)="copyMessage(message.content)"
              class="option-btn"
              title="Copier"
            >
              <i class="fas fa-copy"></i>
            </button>
            <button
              *ngIf="message.sender?.id === currentUserId"
              (click)="deleteMessage(message.id)"
              class="option-btn delete"
              title="Supprimer"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>

          <!-- Sélecteur de réactions -->
          <div *ngIf="showReactionPicker[message.id]" class="reaction-picker">
            <span
              *ngFor="let emoji of c.reactions"
              class="reaction-option"
              (click)="addReaction(message.id, emoji)"
            >
              {{ emoji }}
            </span>
          </div>
        </div>

        <!-- Bouton d'options -->
        <button
          class="message-menu-btn"
          (click)="searchAndReactionMethods.toggleMessageOptions(message.id)"
        >
          <i class="fas fa-ellipsis-v"></i>
        </button>
      </div>
    </ng-container>

    <!-- Indicateur de frappe -->
    <div *ngIf="isTyping" class="typing-indicator">
      <div class="typing-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <span class="typing-text"
        >{{ otherParticipant?.username }} est en train d'écrire...</span
      >
    </div>
  </div>

  <!-- Message de réponse -->
  <div *ngIf="replyingToMessage" class="reply-preview">
    <div class="reply-preview-content">
      <div class="reply-preview-header">
        <span>Répondre à {{ replyingToMessage.sender?.username }}</span>
        <button (click)="cancelReply()" class="cancel-reply-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <p class="reply-preview-text">{{ replyingToMessage.content }}</p>
    </div>
  </div>

  <!-- Zone de saisie -->
  <div class="input-area">
    <!-- Prévisualisation de fichier -->
    <div *ngIf="selectedFile" class="file-preview">
      <div class="file-preview-content">
        <img
          *ngIf="previewUrl"
          [src]="previewUrl"
          alt="Preview"
          class="file-preview-image"
        />
        <div class="file-info">
          <span class="file-name">{{ selectedFile.name }}</span>
          <span class="file-size">{{ formatFileSize(selectedFile.size) }}</span>
        </div>
        <button (click)="removeSelectedFile()" class="remove-file-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Enregistrement vocal -->
    <div *ngIf="isRecordingVoice" class="voice-recording">
      <div class="voice-recording-content">
        <div class="voice-recording-indicator">
          <i class="fas fa-microphone recording-pulse"></i>
          <span class="recording-duration">{{
            formatRecordingDuration(voiceRecordingDuration)
          }}</span>
        </div>
        <div class="voice-recording-actions">
          <button
            (click)="onVoiceRecordingCancelled()"
            class="cancel-recording-btn"
          >
            <i class="fas fa-times"></i>
          </button>
          <button
            (click)="onVoiceRecordingComplete()"
            class="send-recording-btn"
          >
            <i class="fas fa-paper-plane"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Formulaire de saisie -->
    <form
      [formGroup]="messageForm"
      (ngSubmit)="sendMessage()"
      class="input-form"
    >
      <!-- Boutons d'action gauche -->
      <div class="input-actions-left">
        <button
          type="button"
          (click)="triggerFileInput()"
          class="input-action-btn"
          title="Joindre un fichier"
        >
          <i class="fas fa-paperclip"></i>
        </button>
        <button
          type="button"
          (click)="toggleEmojiPicker()"
          class="input-action-btn"
          title="Émojis"
        >
          <i class="fas fa-smile"></i>
        </button>
      </div>

      <!-- Champ de saisie -->
      <input
        formControlName="content"
        placeholder="Tapez votre message..."
        class="message-input"
        [disabled]="!otherParticipant || isRecordingVoice"
        (input)="onTyping()"
        (keydown.enter)="sendMessage()"
      />

      <!-- Boutons d'action droite -->
      <div class="input-actions-right">
        <button
          *ngIf="messageForm.get('content')?.value?.trim() || selectedFile"
          type="submit"
          class="send-btn"
          [disabled]="messageForm.invalid || !otherParticipant || isUploading"
        >
          <i class="fas fa-paper-plane" *ngIf="!isUploading"></i>
          <i class="fas fa-spinner fa-spin" *ngIf="isUploading"></i>
        </button>
        <button
          *ngIf="!messageForm.get('content')?.value?.trim() && !selectedFile"
          type="button"
          (click)="toggleVoiceRecording()"
          class="voice-btn"
          [ngClass]="{ recording: isRecordingVoice }"
        >
          <i class="fas fa-microphone"></i>
        </button>
      </div>
    </form>

    <!-- Sélecteur d'émojis -->
    <div *ngIf="showEmojiPicker" class="emoji-picker">
      <div class="emoji-categories">
        <button
          *ngFor="let category of emojiCategories"
          (click)="selectEmojiCategory(category.name)"
          class="emoji-category-btn"
          [ngClass]="{ active: selectedEmojiCategory === category.name }"
        >
          {{ category.icon }}
        </button>
      </div>
      <div class="emoji-grid">
        <span
          *ngFor="let emoji of getEmojisForCategory(selectedEmojiCategory)"
          class="emoji-option"
          (click)="insertEmoji(emoji)"
        >
          {{ emoji }}
        </span>
      </div>
    </div>
  </div>

  <!-- Input de fichier caché -->
  <input
    #fileInput
    type="file"
    accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
    (change)="onFileSelected($event)"
    style="display: none"
  />

  <!-- Panneaux latéraux -->

  <!-- Panneau de notifications -->
  <div class="side-panel" [ngClass]="{ open: showNotificationPanel }">
    <div class="panel-header">
      <h3>Notifications</h3>
      <div class="panel-actions">
        <button
          *ngIf="notifications.length > 0"
          (click)="markAllNotificationsAsRead()"
          class="mark-all-read-btn"
        >
          <i class="fas fa-check-double"></i>
        </button>
        <button (click)="toggleNotificationPanel()" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <div class="notification-filters">
      <button
        *ngFor="let filter of notificationFilters"
        (click)="setNotificationFilter(filter.value)"
        class="filter-btn"
        [ngClass]="{ active: notificationFilter === filter.value }"
      >
        {{ filter.label }}
      </button>
    </div>

    <div class="notification-list">
      <div
        *ngIf="filteredNotifications.length === 0"
        class="empty-notifications"
      >
        <i class="fas fa-bell-slash"></i>
        <p>Aucune notification</p>
      </div>

      <div
        *ngFor="let notification of filteredNotifications"
        class="notification-item"
        [ngClass]="{ unread: !notification.read }"
        (click)="markNotificationAsRead(notification.id)"
      >
        <div class="notification-icon">
          <i [class]="getNotificationIcon(notification.type)"></i>
        </div>
        <div class="notification-content">
          <p class="notification-text">{{ notification.content }}</p>
          <small class="notification-time">{{
            formatMessageTime(notification.timestamp)
          }}</small>
        </div>
        <button
          (click)="deleteNotification(notification.id)"
          class="delete-notification-btn"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Panneau des messages épinglés -->
  <div class="side-panel" [ngClass]="{ open: showPinnedMessagesPanel }">
    <div class="panel-header">
      <h3>Messages épinglés</h3>
      <button (click)="togglePinnedMessagesPanel()" class="close-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="pinned-messages-list">
      <div *ngIf="pinnedMessages.length === 0" class="empty-pinned">
        <i class="fas fa-thumbtack"></i>
        <p>Aucun message épinglé</p>
      </div>

      <div
        *ngFor="let message of pinnedMessages"
        class="pinned-message-item"
        (click)="scrollToMessage(message.id)"
      >
        <div class="pinned-message-content">
          <p>{{ message.content }}</p>
          <small>{{ formatMessageTime(message.timestamp) }}</small>
        </div>
        <button (click)="unpinMessage(message.id)" class="unpin-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Panneau d'historique d'appels -->
  <div class="side-panel" [ngClass]="{ open: showCallHistoryPanel }">
    <div class="panel-header">
      <h3>Historique d'appels</h3>
      <button (click)="toggleCallHistoryPanel()" class="close-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="call-history-list">
      <div *ngIf="callHistory.length === 0" class="empty-calls">
        <i class="fas fa-phone-slash"></i>
        <p>Aucun appel</p>
      </div>

      <div
        *ngFor="let call of callHistory"
        class="call-history-item"
        [ngClass]="call.status"
      >
        <div class="call-icon">
          <i class="fas fa-phone" *ngIf="call.type === 'AUDIO'"></i>
          <i class="fas fa-video" *ngIf="call.type === 'VIDEO'"></i>
        </div>
        <div class="call-info">
          <p class="call-type">
            {{ call.type === "AUDIO" ? "Appel audio" : "Appel vidéo" }}
          </p>
          <small class="call-time">{{
            formatMessageTime(call.timestamp)
          }}</small>
          <small class="call-duration" *ngIf="call.duration">{{
            formatCallDuration(call.duration)
          }}</small>
        </div>
        <div class="call-status">
          <i class="fas fa-phone-slash" *ngIf="call.status === 'missed'"></i>
          <i class="fas fa-arrow-up" *ngIf="call.status === 'outgoing'"></i>
          <i class="fas fa-arrow-down" *ngIf="call.status === 'incoming'"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Barre de recherche -->
  <div *ngIf="showSearchBar" class="search-overlay">
    <div class="search-container">
      <div class="search-input-container">
        <input
          [(ngModel)]="searchQuery"
          placeholder="Rechercher dans la conversation..."
          class="search-input"
          (input)="performSearch()"
          #searchInput
        />
        <button
          (click)="clearSearch()"
          class="clear-search-btn"
          *ngIf="searchQuery"
        >
          <i class="fas fa-times"></i>
        </button>
        <button (click)="toggleSearchBar()" class="close-search-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div *ngIf="searchResults.length > 0" class="search-results">
        <div class="search-results-header">
          <span>{{ searchResults.length }} résultat(s) trouvé(s)</span>
        </div>
        <div
          *ngFor="let result of searchResults"
          class="search-result-item"
          (click)="scrollToMessage(result.id)"
        >
          <p
            [innerHTML]="
              searchAndReactionMethods.highlightSearchTerms(
                result.content,
                searchQuery
              )
            "
          ></p>
          <small>{{ formatMessageTime(result.timestamp) }}</small>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modales d'appel -->

<!-- Appel entrant -->
<div *ngIf="incomingCall" class="call-modal incoming-call">
  <div class="call-modal-content">
    <div class="caller-info">
      <img
        [src]="incomingCall.caller?.image || 'assets/images/default-avatar.png'"
        alt="Caller"
        class="caller-avatar"
      />
      <h3>{{ incomingCall.caller?.username }}</h3>
      <p>
        {{
          incomingCall.type === "AUDIO"
            ? "Appel audio entrant"
            : "Appel vidéo entrant"
        }}
      </p>
    </div>

    <div class="call-actions">
      <button (click)="declineCall()" class="decline-call-btn">
        <i class="fas fa-phone-slash"></i>
      </button>
      <button (click)="acceptCall()" class="accept-call-btn">
        <i class="fas fa-phone"></i>
      </button>
    </div>
  </div>
</div>

<!-- Appel actif -->
<div
  *ngIf="activeCall"
  class="call-modal active-call"
  [ngClass]="{ minimized: isCallMinimized }"
>
  <div class="call-modal-content">
    <!-- En-tête d'appel -->
    <div class="call-header">
      <div class="call-participant-info">
        <img
          [src]="
            activeCall.participant?.image || 'assets/images/default-avatar.png'
          "
          alt="Participant"
          class="participant-avatar"
        />
        <div class="call-details">
          <h4>{{ activeCall.participant?.username }}</h4>
          <span class="call-duration">{{
            formatCallDuration(callDuration)
          }}</span>
          <span class="call-status">{{ getCallStatusText() }}</span>
        </div>
      </div>

      <div class="call-header-actions">
        <button (click)="toggleCallMinimize()" class="minimize-call-btn">
          <i class="fas fa-minus" *ngIf="!isCallMinimized"></i>
          <i class="fas fa-expand" *ngIf="isCallMinimized"></i>
        </button>
      </div>
    </div>

    <!-- Zone vidéo -->
    <div *ngIf="activeCall.type === 'VIDEO'" class="video-container">
      <video #remoteVideo class="remote-video" autoplay></video>
      <video #localVideo class="local-video" autoplay muted></video>
    </div>

    <!-- Contrôles d'appel -->
    <div
      class="call-controls"
      [ngClass]="{ show: showCallControls || !isCallMinimized }"
    >
      <button
        (click)="toggleMute()"
        class="call-control-btn"
        [ngClass]="{ active: isCallMuted }"
      >
        <i class="fas fa-microphone" *ngIf="!isCallMuted"></i>
        <i class="fas fa-microphone-slash" *ngIf="isCallMuted"></i>
      </button>

      <button
        *ngIf="activeCall.type === 'VIDEO'"
        (click)="toggleVideo()"
        class="call-control-btn"
        [ngClass]="{ active: !isVideoEnabled }"
      >
        <i class="fas fa-video" *ngIf="isVideoEnabled"></i>
        <i class="fas fa-video-slash" *ngIf="!isVideoEnabled"></i>
      </button>

      <button (click)="endCall()" class="end-call-btn">
        <i class="fas fa-phone-slash"></i>
      </button>
    </div>
  </div>
</div>

<!-- Modal de confirmation de suppression -->
<div *ngIf="showDeleteConfirmModal" class="modal-overlay">
  <div class="modal-content">
    <div class="modal-header">
      <h3>Confirmer la suppression</h3>
    </div>
    <div class="modal-body">
      <p>
        Êtes-vous sûr de vouloir supprimer ce message ? Cette action est
        irréversible.
      </p>
    </div>
    <div class="modal-actions">
      <button (click)="cancelDelete()" class="cancel-btn">Annuler</button>
      <button (click)="confirmDelete()" class="confirm-btn">Supprimer</button>
    </div>
  </div>
</div>

<!-- Toast de notification -->
<div *ngIf="showToast" class="toast" [ngClass]="toastType">
  <div class="toast-content">
    <i [class]="getToastIcon()"></i>
    <span>{{ toastMessage }}</span>
  </div>
</div>
