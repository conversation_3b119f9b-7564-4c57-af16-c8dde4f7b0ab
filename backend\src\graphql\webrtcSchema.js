const { gql } = require('apollo-server-express');

const webrtcTypeDefs = gql`
  # ✅ Types pour les appels WebRTC
  type CallState {
    id: ID!
    status: CallStatus!
    type: CallType!
    startTime: String!
  }

  type CallResult {
    id: ID!
    callerId: ID!
    recipientId: ID!
    type: CallType!
    status: CallStatus!
    caller: User
  }

  type CallResponse {
    id: ID!
    status: CallStatus!
    duration: Int
  }

  type WebRTCResponse {
    success: Boolean!
    message: String
  }

  # ✅ Types pour les événements WebRTC
  type IncomingCall {
    id: ID!
    caller: User!
    type: CallType!
    status: CallStatus!
  }

  type CallEvent {
    id: ID!
    status: CallStatus!
    duration: Int
  }

  type WebRTCOffer {
    callId: ID!
    offer: JSON!
  }

  type WebRTCAnswer {
    callId: ID!
    answer: JSON!
  }

  type WebRTCIceCandidate {
    callId: ID!
    candidate: JSON!
  }

  # ✅ Enums pour les appels
  enum CallStatus {
    ringing
    accepted
    connected
    rejected
    ended
    missed
    failed
  }

  enum CallType {
    audio
    video
    video_only
  }

  # ✅ Inputs pour WebRTC
  input WebRTCOfferInput {
    type: String!
    sdp: String!
  }

  input WebRTCAnswerInput {
    type: String!
    sdp: String!
  }

  input ICECandidateInput {
    candidate: String!
    sdpMLineIndex: Int
    sdpMid: String
  }

  # ✅ Queries
  extend type Query {
    # Obtenir l'état d'un appel
    getCallState(callId: ID!): CallState
  }

  # ✅ Mutations
  extend type Mutation {
    # Gestion des appels
    initiateCall(recipientId: ID!, type: CallType = video): CallResult!
    acceptCall(callId: ID!): CallResponse!
    rejectCall(callId: ID!): CallResponse!
    endCall(callId: ID!): CallResponse!

    # Signalisation WebRTC
    sendWebRTCOffer(callId: ID!, offer: WebRTCOfferInput!): WebRTCResponse!
    sendWebRTCAnswer(callId: ID!, answer: WebRTCAnswerInput!): WebRTCResponse!
    sendICECandidate(callId: ID!, candidate: ICECandidateInput!): WebRTCResponse!
  }

  # ✅ Subscriptions
  extend type Subscription {
    # Événements d'appel
    incomingCall: IncomingCall
    callAccepted: CallEvent
    callRejected: CallEvent
    callEnded: CallEvent
    callConnected: CallEvent

    # Signalisation WebRTC
    webrtcOffer: WebRTCOffer
    webrtcAnswer: WebRTCAnswer
    webrtcIceCandidate: WebRTCIceCandidate
  }

  # ✅ Type JSON pour les données WebRTC
  scalar JSON
`;

module.exports = webrtcTypeDefs;
