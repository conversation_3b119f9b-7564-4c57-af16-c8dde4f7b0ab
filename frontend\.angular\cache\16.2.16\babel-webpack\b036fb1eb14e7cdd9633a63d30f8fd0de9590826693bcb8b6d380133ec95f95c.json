{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/logger.service\";\nexport class MessageLayoutComponent {\n  constructor(MessageService, route, logger) {\n    this.MessageService = MessageService;\n    this.route = route;\n    this.logger = logger;\n    this.subscriptions = [];\n    this.context = 'messages';\n  }\n  ngOnInit() {\n    // Détermine le contexte (messages ou notifications)\n    this.context = this.route.snapshot.data['context'] || 'messages';\n    if (this.context === 'messages') {\n      // S'abonner aux changements de conversation active\n      this.subscriptions.push(this.MessageService.activeConversation$.subscribe(conversationId => {\n        // Ne s'abonner aux messages que si une conversation est sélectionnée\n        if (conversationId) {\n          this.conversationId = conversationId;\n          // Désabonner de l'ancienne souscription si elle existe\n          this.subscriptions.forEach(sub => sub.unsubscribe());\n          this.subscriptions = [];\n          // S'abonner aux nouveaux messages pour cette conversation\n          this.subscriptions.push(this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n            next: message => {\n              // Gestion des nouveaux messages\n            },\n            error: err => this.logger.error('MessageLayout', 'Error in message subscription', err)\n          }));\n        }\n      }));\n    }\n    // Ajoutez ici la logique spécifique aux notifications si nécessaire\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  static {\n    this.ɵfac = function MessageLayoutComponent_Factory(t) {\n      return new (t || MessageLayoutComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageLayoutComponent,\n      selectors: [[\"app-message-layout\"]],\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"layout-container\"], [1, \"main-content\"]],\n      template: function MessageLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i2.RouterOutlet],\n      styles: [\"\\n\\n.layout-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  width: 100%;\\n  overflow: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm1lc3NhZ2UtbGF5b3V0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsaUNBQWlDO0FBQ2pDO0VBQ0UsYUFBYTtFQUNiLGFBQWE7RUFDYixXQUFXO0VBQ1gsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsT0FBTztFQUNQLFdBQVc7RUFDWCxZQUFZO0VBQ1osZ0JBQWdCO0FBQ2xCIiwiZmlsZSI6Im1lc3NhZ2UtbGF5b3V0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBMYXlvdXQgb3B0aW1pc8OpIHNhbnMgc2lkZWJhciAqL1xyXG4ubGF5b3V0LWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBoZWlnaHQ6IDEwMHZoO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbn1cclxuXHJcbi5tYWluLWNvbnRlbnQge1xyXG4gIGZsZXg6IDE7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvbWVzc2FnZS1sYXlvdXQvbWVzc2FnZS1sYXlvdXQuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxpQ0FBaUM7QUFDakM7RUFDRSxhQUFhO0VBQ2IsYUFBYTtFQUNiLFdBQVc7RUFDWCxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxPQUFPO0VBQ1AsV0FBVztFQUNYLFlBQVk7RUFDWixnQkFBZ0I7QUFDbEI7O0FBRUEsNHRCQUE0dEIiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBMYXlvdXQgb3B0aW1pc8ODwqkgc2FucyBzaWRlYmFyICovXHJcbi5sYXlvdXQtY29udGFpbmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGhlaWdodDogMTAwdmg7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG5cclxuLm1haW4tY29udGVudCB7XHJcbiAgZmxleDogMTtcclxuICB3aWR0aDogMTAwJTtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MessageLayoutComponent", "constructor", "MessageService", "route", "logger", "subscriptions", "context", "ngOnInit", "snapshot", "data", "push", "activeConversation$", "subscribe", "conversationId", "for<PERSON>ach", "sub", "unsubscribe", "subscribeToNewMessages", "next", "message", "error", "err", "ngOnDestroy", "i0", "ɵɵdirectiveInject", "i1", "i2", "ActivatedRoute", "i3", "LoggerService", "selectors", "decls", "vars", "consts", "template", "MessageLayoutComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-layout\\message-layout.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-layout\\message-layout.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { ActivatedRoute } from '@angular/router';\nimport { MessageService } from '@app/services/message.service';\nimport { LoggerService } from '@app/services/logger.service';\n@Component({\n  selector: 'app-message-layout',\n  templateUrl: './message-layout.component.html',\n  styleUrls: ['./message-layout.component.css'],\n  // schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})\nexport class MessageLayoutComponent implements OnInit, OnDestroy {\n  private subscriptions: Subscription[] = [];\n  context: string = 'messages';\n  conversationId: any;\n  constructor(\n    private MessageService: MessageService,\n    private route: ActivatedRoute,\n    private logger: LoggerService\n  ) {}\n\n  ngOnInit() {\n    // Détermine le contexte (messages ou notifications)\n    this.context = this.route.snapshot.data['context'] || 'messages';\n\n    if (this.context === 'messages') {\n      // S'abonner aux changements de conversation active\n      this.subscriptions.push(\n        this.MessageService.activeConversation$.subscribe((conversationId) => {\n          // Ne s'abonner aux messages que si une conversation est sélectionnée\n          if (conversationId) {\n            this.conversationId = conversationId;\n\n            // Désabonner de l'ancienne souscription si elle existe\n            this.subscriptions.forEach((sub) => sub.unsubscribe());\n            this.subscriptions = [];\n\n            // S'abonner aux nouveaux messages pour cette conversation\n            this.subscriptions.push(\n              this.MessageService.subscribeToNewMessages(\n                conversationId\n              ).subscribe({\n                next: (message) => {\n                  // Gestion des nouveaux messages\n                },\n                error: (err) =>\n                  this.logger.error(\n                    'MessageLayout',\n                    'Error in message subscription',\n                    err\n                  ),\n              })\n            );\n          }\n        })\n      );\n    }\n    // Ajoutez ici la logique spécifique aux notifications si nécessaire\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n}\n", "<div class=\"layout-container\">\n  <div class=\"main-content\">\n    <router-outlet></router-outlet>\n  </div>\n</div>\n"], "mappings": ";;;;AAWA,OAAM,MAAOA,sBAAsB;EAIjCC,YACUC,cAA8B,EAC9BC,KAAqB,EACrBC,MAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IANR,KAAAC,aAAa,GAAmB,EAAE;IAC1C,KAAAC,OAAO,GAAW,UAAU;EAMzB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,OAAO,GAAG,IAAI,CAACH,KAAK,CAACK,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC,IAAI,UAAU;IAEhE,IAAI,IAAI,CAACH,OAAO,KAAK,UAAU,EAAE;MAC/B;MACA,IAAI,CAACD,aAAa,CAACK,IAAI,CACrB,IAAI,CAACR,cAAc,CAACS,mBAAmB,CAACC,SAAS,CAAEC,cAAc,IAAI;QACnE;QACA,IAAIA,cAAc,EAAE;UAClB,IAAI,CAACA,cAAc,GAAGA,cAAc;UAEpC;UACA,IAAI,CAACR,aAAa,CAACS,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;UACtD,IAAI,CAACX,aAAa,GAAG,EAAE;UAEvB;UACA,IAAI,CAACA,aAAa,CAACK,IAAI,CACrB,IAAI,CAACR,cAAc,CAACe,sBAAsB,CACxCJ,cAAc,CACf,CAACD,SAAS,CAAC;YACVM,IAAI,EAAGC,OAAO,IAAI;cAChB;YAAA,CACD;YACDC,KAAK,EAAGC,GAAG,IACT,IAAI,CAACjB,MAAM,CAACgB,KAAK,CACf,eAAe,EACf,+BAA+B,EAC/BC,GAAG;WAER,CAAC,CACH;;MAEL,CAAC,CAAC,CACH;;IAEH;EACF;;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjB,aAAa,CAACS,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;;;uBAnDWhB,sBAAsB,EAAAuB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAvB,cAAA,GAAAqB,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAtB7B,sBAAsB;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXnCb,EAAA,CAAAe,cAAA,aAA8B;UAE1Bf,EAAA,CAAAgB,SAAA,oBAA+B;UACjChB,EAAA,CAAAiB,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}