<!-- Interface d'appel WebRTC -->
<div 
  class="call-interface-overlay" 
  [class.visible]="isVisible"
  [class.video-call]="callType === 'VIDEO'"
  [class.audio-call]="callType === 'AUDIO'"
>
  <!-- Arrière-plan flou pour appel audio -->
  <div class="call-background" *ngIf="callType === 'AUDIO'">
    <div class="background-blur"></div>
    <div class="background-gradient"></div>
  </div>

  <!-- Conteneur principal -->
  <div class="call-container">
    
    <!-- === APPEL VIDÉO === -->
    <div class="video-container" *ngIf="callType === 'VIDEO'">
      <!-- Vidéo distante (plein écran) -->
      <video 
        #remoteVideo
        class="remote-video"
        [class.fullscreen]="isFullscreen"
        autoplay
        playsinline
        (click)="toggleFullscreen()"
      ></video>

      <!-- Vidéo locale (picture-in-picture) -->
      <video 
        #localVideo
        class="local-video"
        [class.hidden]="!isVideoEnabled"
        autoplay
        playsinline
        muted
      ></video>

      <!-- Placeholder si vidéo désactivée -->
      <div class="video-placeholder local" *ngIf="!isVideoEnabled">
        <div class="avatar-container">
          <img [src]="otherParticipant?.image || '/assets/images/default-avatar.png'" 
               [alt]="otherParticipant?.username" 
               class="user-avatar">
        </div>
        <p class="video-disabled-text">Caméra désactivée</p>
      </div>

      <!-- Placeholder si pas de vidéo distante -->
      <div class="video-placeholder remote" *ngIf="!remoteStream">
        <div class="avatar-container">
          <img [src]="otherParticipant?.image || '/assets/images/default-avatar.png'" 
               [alt]="otherParticipant?.username" 
               class="user-avatar large">
        </div>
        <p class="connecting-text">{{ callStatus }}</p>
      </div>
    </div>

    <!-- === APPEL AUDIO === -->
    <div class="audio-container" *ngIf="callType === 'AUDIO'">
      <div class="audio-content">
        <!-- Avatar de l'utilisateur -->
        <div class="avatar-container large">
          <img [src]="otherParticipant?.image || '/assets/images/default-avatar.png'" 
               [alt]="otherParticipant?.username" 
               class="user-avatar xl">
          <div class="avatar-ring" [class.pulsing]="!isConnected"></div>
        </div>

        <!-- Informations utilisateur -->
        <div class="user-info">
          <h2 class="user-name">{{ otherParticipant?.username }}</h2>
          <p class="call-status">{{ callStatus }}</p>
          <p class="call-duration" *ngIf="isConnected && callDuration > 0">
            {{ formatDuration(callDuration) }}
          </p>
        </div>
      </div>
    </div>

    <!-- === EN-TÊTE D'APPEL === -->
    <div class="call-header" *ngIf="callType === 'VIDEO'">
      <div class="call-info">
        <div class="user-details">
          <img [src]="otherParticipant?.image || '/assets/images/default-avatar.png'" 
               [alt]="otherParticipant?.username" 
               class="header-avatar">
          <div class="user-text">
            <span class="user-name">{{ otherParticipant?.username }}</span>
            <span class="call-status">{{ callStatus }}</span>
            <span class="call-duration" *ngIf="isConnected && callDuration > 0">
              {{ formatDuration(callDuration) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Bouton minimiser/fermer -->
      <div class="header-actions">
        <button class="header-btn minimize" (click)="toggleFullscreen()">
          <i class="material-icons">{{ isFullscreen ? 'fullscreen_exit' : 'fullscreen' }}</i>
        </button>
      </div>
    </div>

    <!-- === CONTRÔLES D'APPEL === -->
    <div class="call-controls">
      <div class="controls-container">
        
        <!-- Bouton Micro -->
        <button 
          class="control-btn audio"
          [class.active]="!isMuted"
          [class.muted]="isMuted"
          (click)="toggleMute()"
          title="{{ isMuted ? 'Activer le micro' : 'Couper le micro' }}"
        >
          <i class="material-icons">{{ isMuted ? 'mic_off' : 'mic' }}</i>
        </button>

        <!-- Bouton Vidéo (seulement pour appels vidéo) -->
        <button 
          *ngIf="callType === 'VIDEO'"
          class="control-btn video"
          [class.active]="isVideoEnabled"
          [class.disabled]="!isVideoEnabled"
          (click)="toggleVideo()"
          title="{{ isVideoEnabled ? 'Désactiver la caméra' : 'Activer la caméra' }}"
        >
          <i class="material-icons">{{ isVideoEnabled ? 'videocam' : 'videocam_off' }}</i>
        </button>

        <!-- Bouton Haut-parleur -->
        <button 
          class="control-btn speaker"
          [class.active]="isSpeakerOn"
          (click)="toggleSpeaker()"
          title="{{ isSpeakerOn ? 'Désactiver haut-parleur' : 'Activer haut-parleur' }}"
        >
          <i class="material-icons">{{ isSpeakerOn ? 'volume_up' : 'volume_down' }}</i>
        </button>

        <!-- Bouton Raccrocher -->
        <button 
          class="control-btn end-call"
          (click)="endCall()"
          title="Raccrocher"
        >
          <i class="material-icons">call_end</i>
        </button>

      </div>
    </div>

    <!-- === APPEL ENTRANT === -->
    <div class="incoming-call-modal" *ngIf="isIncoming && !isConnected">
      <div class="incoming-content">
        
        <!-- Avatar et info -->
        <div class="incoming-info">
          <div class="avatar-container large">
            <img [src]="otherParticipant?.image || '/assets/images/default-avatar.png'" 
                 [alt]="otherParticipant?.username" 
                 class="user-avatar xl">
            <div class="avatar-ring pulsing"></div>
          </div>
          
          <h2 class="caller-name">{{ otherParticipant?.username }}</h2>
          <p class="incoming-text">{{ getCallTypeLabel() }} entrant...</p>
        </div>

        <!-- Boutons d'action -->
        <div class="incoming-actions">
          <button 
            class="action-btn reject"
            (click)="rejectCall()"
            title="Rejeter l'appel"
          >
            <i class="material-icons">call_end</i>
          </button>

          <button 
            class="action-btn accept"
            (click)="acceptCall()"
            title="Accepter l'appel"
          >
            <i class="material-icons">{{ getCallTypeIcon() }}</i>
          </button>
        </div>

      </div>
    </div>

  </div>

  <!-- Audio éléments (cachés) -->
  <audio #remoteAudio autoplay *ngIf="callType === 'AUDIO'"></audio>
  
</div>

<!-- Overlay de chargement -->
<div class="call-loading" *ngIf="isVisible && !isConnected && !isIncoming">
  <div class="loading-content">
    <div class="loading-spinner"></div>
    <p>{{ callStatus }}</p>
  </div>
</div>
