{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { MessageType, CallType } from 'src/app/models/message.model';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/services/user-status.service\";\nimport * as i6 from \"src/app/services/toast.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.otherParticipant.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.otherParticipant.isOnline ? \"En ligne\" : ctx_r0.formatLastActive(ctx_r0.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵtext(2, \" Chargement... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun message pour le moment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 38);\n    i0.ɵɵtext(5, \"Commencez la conversation !\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"current-user\": a0,\n    \"other-user\": a1\n  };\n};\nfunction MessageChatComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c2, (message_r8.sender == null ? null : message_r8.sender.id) === ctx_r4.currentUserId, (message_r8.sender == null ? null : message_r8.sender.id) !== ctx_r4.currentUserId));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r8.content);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.formatMessageTime(message_r8.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune notification\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"p\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"small\", 46);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notification_r9 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r9.content);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.formatMessageTime(notification_r9.timestamp));\n  }\n}\nfunction MessageChatComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48)(2, \"input\", 49);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_33_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.searchQuery = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_33_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.toggleSearchBar());\n    });\n    i0.ɵɵelement(4, \"i\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r7.searchQuery);\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    open: a0\n  };\n};\nexport class MessageChatComponent {\n  // Getter pour compatibilité\n  get availableReactions() {\n    return this.c.reactions;\n  }\n  // Emojis du service\n  get commonEmojis() {\n    return this.MessageService.getCommonEmojis();\n  }\n  // Configuration des boutons d'action de l'en-tête\n  getHeaderActions() {\n    return [{\n      class: 'btn-audio-call',\n      icon: 'fas fa-phone-alt',\n      title: 'Appel audio',\n      onClick: () => this.initiateCall('AUDIO'),\n      isActive: false\n    }, {\n      class: 'btn-video-call',\n      icon: 'fas fa-video',\n      title: 'Appel vidéo',\n      onClick: () => this.initiateCall('VIDEO'),\n      isActive: false\n    }, {\n      class: 'btn-search',\n      icon: 'fas fa-search',\n      title: 'Rechercher',\n      onClick: () => this.toggleSearchBar(),\n      isActive: this.showSearchBar,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-pinned relative',\n      icon: 'fas fa-thumbtack',\n      title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n      onClick: () => this.togglePinnedMessages(),\n      isActive: this.showPinnedMessages,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.getPinnedMessagesCount() > 0 ? {\n        count: this.getPinnedMessagesCount(),\n        class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n        animate: false\n      } : null\n    }, {\n      class: 'btn-notifications relative',\n      icon: 'fas fa-bell',\n      title: 'Notifications',\n      onClick: () => this.toggleNotificationPanel(),\n      isActive: this.showNotificationPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.unreadNotificationCount > 0 ? {\n        count: this.unreadNotificationCount,\n        class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n        animate: true\n      } : null\n    }, {\n      class: 'btn-history relative',\n      icon: 'fas fa-history',\n      title: 'Historique des appels',\n      onClick: () => this.toggleCallHistoryPanel(),\n      isActive: this.showCallHistoryPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-stats relative',\n      icon: 'fas fa-chart-bar',\n      title: \"Statistiques d'appels\",\n      onClick: () => this.toggleCallStatsPanel(),\n      isActive: this.showCallStatsPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-voice-messages relative',\n      icon: 'fas fa-microphone',\n      title: 'Messages vocaux',\n      onClick: () => this.toggleVoiceMessagesPanel(),\n      isActive: this.showVoiceMessagesPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.voiceMessages.length > 0 ? {\n        count: this.voiceMessages.length,\n        class: 'bg-[#4f5fad]',\n        animate: false\n      } : null\n    }];\n  }\n  constructor(MessageService, route, authService, fb, statusService, router, toastService, cdr) {\n    this.MessageService = MessageService;\n    this.route = route;\n    this.authService = authService;\n    this.fb = fb;\n    this.statusService = statusService;\n    this.router = router;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    this.messages = [];\n    this.conversation = null;\n    this.loading = true;\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    this.selectedFile = null;\n    this.previewUrl = null;\n    this.isUploading = false;\n    this.isTyping = false;\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.MAX_MESSAGES_PER_SIDE = 5;\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.MAX_TOTAL_MESSAGES = 100;\n    this.currentPage = 1;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.subscriptions = new Subscription();\n    // Interface\n    this.selectedTheme = 'theme-default';\n    // États\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n    this.showEmojiPicker = false;\n    this.showSearchBar = false;\n    this.showPinnedMessages = false;\n    this.showStatusSelector = false;\n    this.showNotificationPanel = false;\n    this.showNotificationSettings = false;\n    this.showUserStatusPanel = false;\n    this.showCallHistoryPanel = false;\n    this.showCallStatsPanel = false;\n    this.showVoiceMessagesPanel = false;\n    // Appels\n    this.incomingCall = null;\n    this.activeCall = null;\n    this.showCallModal = false;\n    this.showActiveCallModal = false;\n    this.isCallMuted = false;\n    this.isVideoEnabled = true;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // Notifications et messages\n    this.notifications = [];\n    this.unreadNotificationCount = 0;\n    this.selectedNotifications = new Set();\n    this.showDeleteConfirmModal = false;\n    this.editingMessageId = null;\n    this.editingContent = '';\n    this.replyingToMessage = null;\n    // Recherche\n    this.searchQuery = '';\n    this.isSearching = false;\n    this.searchMode = false;\n    this.searchResults = [];\n    // Panneaux\n    this.pinnedMessages = [];\n    this.showReactionPicker = {};\n    this.showDeleteConfirm = {};\n    this.showPinConfirm = {};\n    this.isPinning = {};\n    this.showMessageOptions = {};\n    // Variables de transfert\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n    this.availableConversations = [];\n    this.isForwarding = false;\n    this.isLoadingConversations = false;\n    // Constantes optimisées\n    this.c = {\n      reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n      delays: {\n        away: 300000,\n        typing: 3000,\n        scroll: 100,\n        anim: 300\n      },\n      error: 'Erreur. Veuillez réessayer.',\n      trackById: (i, item) => item?.id || i.toString(),\n      notifications: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      status: {\n        online: {\n          text: 'En ligne',\n          color: 'text-green-500',\n          icon: 'fas fa-circle',\n          label: 'En ligne',\n          description: 'Disponible pour discuter'\n        },\n        offline: {\n          text: 'Hors ligne',\n          color: 'text-gray-500',\n          icon: 'far fa-circle',\n          label: 'Hors ligne',\n          description: 'Invisible pour tous'\n        },\n        away: {\n          text: 'Absent',\n          color: 'text-yellow-500',\n          icon: 'fas fa-clock',\n          label: 'Absent',\n          description: 'Absent temporairement'\n        },\n        busy: {\n          text: 'Occupé',\n          color: 'text-red-500',\n          icon: 'fas fa-minus-circle',\n          label: 'Occupé',\n          description: 'Ne pas déranger'\n        }\n      },\n      themes: [{\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad',\n        hoverColor: '#4f5fad'\n      }, {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d',\n        hoverColor: '#ff6b9d'\n      }, {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6',\n        hoverColor: '#3d85c6'\n      }, {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f',\n        hoverColor: '#6aa84f'\n      }],\n      calls: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      },\n      // Alias pour compatibilité\n      notificationConfig: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      callStatusColors: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      }\n    };\n    // Variables de notification\n    this.notificationFilter = 'all';\n    this.isLoadingNotifications = false;\n    this.isMarkingAsRead = false;\n    this.isDeletingNotifications = false;\n    this.hasMoreNotifications = false;\n    this.notificationSounds = true;\n    this.notificationPreview = true;\n    this.autoMarkAsRead = true;\n    // Variables d'appel\n    this.isCallMinimized = false;\n    this.callQuality = 'connecting';\n    this.showCallControls = false;\n    // Variables de statut utilisateur\n    this.onlineUsers = new Map();\n    this.currentUserStatus = 'online';\n    this.lastActivityTime = new Date();\n    this.autoAwayTimeout = null;\n    this.isUpdatingStatus = false;\n    this.callHistory = [];\n    this.voiceMessages = [];\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.statusFilterType = 'all';\n    // Services de fichiers optimisés\n    this.f = {\n      getIcon: t => this.MessageService.getFileIcon(t),\n      getType: t => this.MessageService.getFileType(t)\n    };\n    this.getFileIcon = this.f.getIcon;\n    this.getFileType = this.f.getType;\n    this.isCurrentlyTyping = false;\n    this.TYPING_DELAY = 500;\n    this.TYPING_TIMEOUT = 3000;\n    // Méthodes de basculement principales consolidées\n    this.mainToggleMethods = {\n      themeSelector: () => this.togglePanel('theme'),\n      mainMenu: () => this.togglePanel('menu'),\n      emojiPicker: () => this.togglePanel('emoji')\n    };\n    this.toggleThemeSelector = this.mainToggleMethods.themeSelector;\n    this.toggleMainMenu = this.mainToggleMethods.mainMenu;\n    this.toggleEmojiPicker = this.mainToggleMethods.emojiPicker;\n    // Méthodes toggle consolidées\n    this.toggleMethods = {\n      pinnedMessages: () => this.showPinnedMessages = !this.showPinnedMessages,\n      searchBar: () => {\n        this.togglePanel('search');\n        if (!this.showSearchBar) this.clearSearch();\n      },\n      statusSelector: () => this.togglePanel('status'),\n      notificationSettings: () => this.showNotificationSettings = !this.showNotificationSettings,\n      userStatusPanel: () => this.showUserStatusPanel = !this.showUserStatusPanel,\n      callMinimize: () => this.isCallMinimized = !this.isCallMinimized,\n      callHistoryPanel: () => this.showCallHistoryPanel = !this.showCallHistoryPanel,\n      callStatsPanel: () => this.showCallStatsPanel = !this.showCallStatsPanel,\n      voiceMessagesPanel: () => this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel\n    };\n    this.togglePinnedMessages = this.toggleMethods.pinnedMessages;\n    this.toggleSearchBar = this.toggleMethods.searchBar;\n    this.toggleStatusSelector = this.toggleMethods.statusSelector;\n    this.toggleNotificationSettings = this.toggleMethods.notificationSettings;\n    this.toggleUserStatusPanel = this.toggleMethods.userStatusPanel;\n    this.toggleCallMinimize = this.toggleMethods.callMinimize;\n    this.toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;\n    this.toggleCallStatsPanel = this.toggleMethods.callStatsPanel;\n    this.toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;\n    this.conversationMethods = {\n      showInfo: () => this.showDevelopmentFeature('Informations de conversation'),\n      showSettings: () => this.showDevelopmentFeature('Paramètres de conversation'),\n      clear: () => {\n        if (!this.conversation?.id || this.messages.length === 0) {\n          this.toastService.showWarning('Aucune conversation à vider');\n          return;\n        }\n        if (confirm('Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.')) {\n          this.messages = [];\n          this.showMainMenu = false;\n          this.toastService.showSuccess('Conversation vidée avec succès');\n        }\n      },\n      export: () => {\n        if (!this.conversation?.id || this.messages.length === 0) {\n          this.toastService.showWarning('Aucune conversation à exporter');\n          return;\n        }\n        const conversationName = this.conversation.isGroup ? this.conversation.groupName || 'Groupe sans nom' : this.otherParticipant?.username || 'Conversation privée';\n        const exportData = {\n          conversation: {\n            id: this.conversation.id,\n            name: conversationName,\n            isGroup: this.conversation.isGroup,\n            participants: this.conversation.participants,\n            createdAt: this.conversation.createdAt\n          },\n          messages: this.messages.map(msg => ({\n            id: msg.id,\n            content: msg.content,\n            sender: msg.sender,\n            timestamp: msg.timestamp,\n            type: msg.type\n          })),\n          exportedAt: new Date().toISOString(),\n          exportedBy: this.currentUserId\n        };\n        const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n          type: 'application/json'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        const safeFileName = conversationName.replace(/[^a-z0-9]/gi, '_').toLowerCase();\n        const dateStr = new Date().toISOString().split('T')[0];\n        link.download = `conversation-${safeFileName}-${dateStr}.json`;\n        link.click();\n        window.URL.revokeObjectURL(url);\n        this.showMainMenu = false;\n        this.toastService.showSuccess('Conversation exportée avec succès');\n      }\n    };\n    // Template methods\n    this.toggleConversationInfo = this.conversationMethods.showInfo;\n    this.toggleConversationSettings = this.conversationMethods.showSettings;\n    this.clearConversation = this.conversationMethods.clear;\n    this.exportConversation = this.conversationMethods.export;\n    // Service - méthodes optimisées\n    this.s = {\n      formatTime: t => this.MessageService.formatMessageTime(t),\n      formatActive: t => this.MessageService.formatLastActive(t),\n      formatDate: t => this.MessageService.formatMessageDate(t),\n      showDateHeader: i => this.MessageService.shouldShowDateHeader(this.messages, i),\n      getType: m => this.MessageService.getMessageType(m),\n      hasImage: m => this.MessageService.hasImage(m),\n      isVoice: m => this.MessageService.isVoiceMessage(m),\n      getVoiceUrl: m => this.MessageService.getVoiceMessageUrl(m),\n      getVoiceDuration: m => this.MessageService.getVoiceMessageDuration(m),\n      getVoiceHeight: i => this.MessageService.getVoiceBarHeight(i),\n      formatVoice: s => this.MessageService.formatVoiceDuration(s),\n      getImageUrl: m => this.MessageService.getImageUrl(m),\n      getTypeClass: m => this.MessageService.getMessageTypeClass(m, this.currentUserId)\n    };\n    // Méthodes exposées optimisées\n    this.formatMessageTime = this.s.formatTime;\n    this.formatLastActive = this.s.formatActive;\n    this.formatMessageDate = this.s.formatDate;\n    this.shouldShowDateHeader = this.s.showDateHeader;\n    this.getMessageType = this.s.getType;\n    this.hasImage = this.s.hasImage;\n    this.isVoiceMessage = this.s.isVoice;\n    this.getVoiceMessageUrl = this.s.getVoiceUrl;\n    this.getVoiceMessageDuration = this.s.getVoiceDuration;\n    this.getVoiceBarHeight = this.s.getVoiceHeight;\n    this.formatVoiceDuration = this.s.formatVoice;\n    this.getImageUrl = this.s.getImageUrl;\n    this.getMessageTypeClass = this.s.getTypeClass;\n    // Indicateurs de chargement consolidés\n    this.loadingIndicatorMethods = {\n      show: () => {\n        if (!document.getElementById('message-loading-indicator')) {\n          const indicator = document.createElement('div');\n          indicator.id = 'message-loading-indicator';\n          indicator.className = 'text-center py-2 text-gray-500 text-sm';\n          indicator.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n          this.messagesContainer?.nativeElement?.prepend(indicator);\n        }\n      },\n      hide: () => {\n        const indicator = document.getElementById('message-loading-indicator');\n        indicator?.parentNode?.removeChild(indicator);\n      }\n    };\n    this.showLoadingIndicator = this.loadingIndicatorMethods.show;\n    this.hideLoadingIndicator = this.loadingIndicatorMethods.hide;\n    // Méthodes d'enregistrement vocal consolidées\n    this.voiceRecordingMethods = {\n      toggle: () => {\n        this.isRecordingVoice = !this.isRecordingVoice;\n        if (!this.isRecordingVoice) this.voiceRecordingDuration = 0;\n      },\n      complete: audioBlob => {\n        if (!this.conversation?.id && !this.otherParticipant?.id) {\n          this.toastService.showError('No conversation or recipient selected');\n          this.isRecordingVoice = false;\n          return;\n        }\n        const receiverId = this.otherParticipant?.id || '';\n        this.MessageService.sendVoiceMessage(receiverId, audioBlob, this.conversation?.id, this.voiceRecordingDuration).subscribe({\n          next: message => {\n            this.isRecordingVoice = false;\n            this.voiceRecordingDuration = 0;\n            this.scrollToBottom(true);\n          },\n          error: error => {\n            this.toastService.showError('Failed to send voice message');\n            this.isRecordingVoice = false;\n          }\n        });\n      },\n      cancel: () => {\n        this.isRecordingVoice = false;\n        this.voiceRecordingDuration = 0;\n      }\n    };\n    this.toggleVoiceRecording = this.voiceRecordingMethods.toggle;\n    this.onVoiceRecordingComplete = this.voiceRecordingMethods.complete;\n    this.onVoiceRecordingCancelled = this.voiceRecordingMethods.cancel;\n    // Méthodes d'appel consolidées\n    this.callMethods = {\n      initiate: type => {\n        if (!this.otherParticipant?.id) return;\n        this.MessageService.initiateCall(this.otherParticipant.id, type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO, this.conversation?.id).subscribe({\n          next: call => {},\n          error: () => this.toastService.showError(this.c.error)\n        });\n      },\n      accept: () => {\n        if (!this.incomingCall) return;\n        this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n          next: call => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n            this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n            this.callQuality = 'connecting';\n            this.toastService.showSuccess('Appel connecté');\n          },\n          error: () => {\n            this.toastService.showError(this.c.error);\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        });\n      },\n      reject: () => {\n        if (!this.incomingCall) return;\n        this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n          next: call => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          },\n          error: error => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        });\n      },\n      end: () => {\n        const sub = this.MessageService.activeCall$.subscribe(call => {\n          if (call) {\n            this.MessageService.endCall(call.id).subscribe({\n              next: call => {},\n              error: error => {}\n            });\n          }\n        });\n        sub.unsubscribe();\n      }\n    };\n    this.initiateCall = this.callMethods.initiate;\n    this.acceptCall = this.callMethods.accept;\n    this.rejectCall = this.callMethods.reject;\n    this.endCall = this.callMethods.end;\n    // Méthodes de contrôle d'appel consolidées\n    this.callControlMethods = {\n      toggleMute: () => {\n        this.isCallMuted = !this.isCallMuted;\n        this.callControlMethods.updateMedia();\n        this.toastService.showInfo(this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé');\n      },\n      toggleVideo: () => {\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.callControlMethods.updateMedia();\n        this.toastService.showInfo(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      updateMedia: () => {\n        this.MessageService.toggleMedia(this.activeCall?.id, !this.isCallMuted, this.isVideoEnabled).subscribe({\n          next: () => {},\n          error: error => console.error('Erreur lors de la mise à jour des médias:', error)\n        });\n      }\n    };\n    this.toggleCallMute = this.callControlMethods.toggleMute;\n    this.toggleCallVideo = this.callControlMethods.toggleVideo;\n    this.updateCallMedia = this.callControlMethods.updateMedia;\n    // Méthodes de timer consolidées\n    this.timerMethods = {\n      startCallTimer: () => {\n        this.callDuration = 0;\n        this.callTimer = setInterval(() => {\n          this.callDuration++;\n          if (this.callDuration === 3 && this.callQuality === 'connecting') {\n            this.callQuality = 'excellent';\n          }\n        }, 1000);\n      },\n      stopCallTimer: () => {\n        if (this.callTimer) {\n          clearInterval(this.callTimer);\n          this.callTimer = null;\n        }\n      },\n      resetCallState: () => this.callDuration = 0\n    };\n    this.startCallTimerMethod = this.timerMethods.startCallTimer;\n    this.stopCallTimerMethod = this.timerMethods.stopCallTimer;\n    this.resetCallStateMethod = this.timerMethods.resetCallState;\n    // Notifications\n    // Méthode de basculement de notification consolidée\n    this.notificationToggleMethod = {\n      togglePanel: () => {\n        this.togglePanel('notification');\n        if (this.showNotificationPanel) {\n          this.loadNotifications();\n        }\n      }\n    };\n    this.toggleNotificationPanel = this.notificationToggleMethod.togglePanel;\n    // Méthodes de notification consolidées\n    this.notificationMethods = {\n      loadMore: () => this.loadNotifications(),\n      updateCount: () => this.unreadNotificationCount = this.notifications.filter(n => !n.isRead).length,\n      getFiltered: () => this.notifications,\n      toggleSelection: notificationId => {\n        if (this.selectedNotifications.has(notificationId)) {\n          this.selectedNotifications.delete(notificationId);\n        } else {\n          this.selectedNotifications.add(notificationId);\n        }\n      },\n      toggleSelectAll: () => {\n        const filteredNotifications = this.notificationMethods.getFiltered();\n        const allSelected = filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n        if (allSelected) {\n          filteredNotifications.forEach(n => this.selectedNotifications.delete(n.id));\n        } else {\n          filteredNotifications.forEach(n => this.selectedNotifications.add(n.id));\n        }\n      },\n      areAllSelected: () => {\n        const filteredNotifications = this.notificationMethods.getFiltered();\n        return filteredNotifications.length > 0 && filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n      }\n    };\n    this.loadMoreNotifications = this.notificationMethods.loadMore;\n    this.updateNotificationCount = this.notificationMethods.updateCount;\n    this.getFilteredNotifications = this.notificationMethods.getFiltered;\n    this.toggleNotificationSelection = this.notificationMethods.toggleSelection;\n    this.toggleSelectAllNotifications = this.notificationMethods.toggleSelectAll;\n    this.areAllNotificationsSelected = this.notificationMethods.areAllSelected;\n    // Méthodes de marquage consolidées\n    this.markingMethods = {\n      markSelected: () => {\n        const selectedIds = Array.from(this.selectedNotifications);\n        if (selectedIds.length === 0) {\n          this.toastService.showWarning('Aucune notification sélectionnée');\n          return;\n        }\n        this.markingMethods.markAsRead(selectedIds, () => {\n          this.selectedNotifications.clear();\n          this.toastService.showSuccess(`${selectedIds.length} notification(s) marquée(s) comme lue(s)`);\n        });\n      },\n      markAll: () => {\n        const unreadNotifications = this.notifications.filter(n => !n.isRead);\n        if (unreadNotifications.length === 0) {\n          this.toastService.showInfo('Aucune notification non lue');\n          return;\n        }\n        const unreadIds = unreadNotifications.map(n => n.id);\n        this.markingMethods.markAsRead(unreadIds, () => {\n          this.toastService.showSuccess('Toutes les notifications ont été marquées comme lues');\n        });\n      },\n      markAsRead: (ids, onSuccess) => {\n        const markSub = this.MessageService.markAsRead(ids).subscribe({\n          next: result => {\n            this.notifications = this.notifications.map(n => ids.includes(n.id) ? {\n              ...n,\n              isRead: true,\n              readAt: new Date()\n            } : n);\n            this.updateNotificationCount();\n            onSuccess();\n          },\n          error: error => {\n            this.toastService.showError('Erreur lors du marquage des notifications');\n          }\n        });\n        this.subscriptions.add(markSub);\n      }\n    };\n    this.markSelectedAsRead = this.markingMethods.markSelected;\n    this.markAllAsRead = this.markingMethods.markAll;\n    // Méthodes de suppression de notifications consolidées\n    this.notificationDeleteMethods = {\n      showDeleteSelectedConfirmation: () => {\n        if (this.selectedNotifications.size === 0) {\n          this.toastService.showWarning('Aucune notification sélectionnée');\n          return;\n        }\n        this.showDeleteConfirmModal = true;\n      },\n      deleteSelected: () => {\n        const selectedIds = Array.from(this.selectedNotifications);\n        if (selectedIds.length === 0) return;\n        this.isDeletingNotifications = true;\n        this.showDeleteConfirmModal = false;\n        const deleteSub = this.MessageService.deleteMultipleNotifications(selectedIds).subscribe({\n          next: result => {\n            this.notifications = this.notifications.filter(n => !selectedIds.includes(n.id));\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(`${result.count} notification(s) supprimée(s)`);\n          },\n          error: error => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError('Erreur lors de la suppression des notifications');\n          }\n        });\n        this.subscriptions.add(deleteSub);\n      },\n      deleteOne: notificationId => {\n        const deleteSub = this.MessageService.deleteNotification(notificationId).subscribe({\n          next: result => {\n            this.notifications = this.notifications.filter(n => n.id !== notificationId);\n            this.selectedNotifications.delete(notificationId);\n            this.updateNotificationCount();\n            this.toastService.showSuccess('Notification supprimée');\n          },\n          error: error => {\n            this.toastService.showError('Erreur lors de la suppression de la notification');\n          }\n        });\n        this.subscriptions.add(deleteSub);\n      },\n      deleteAll: () => {\n        if (this.notifications.length === 0) return;\n        if (!confirm('Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.')) {\n          return;\n        }\n        this.isDeletingNotifications = true;\n        const deleteAllSub = this.MessageService.deleteAllNotifications().subscribe({\n          next: result => {\n            this.notifications = [];\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(`${result.count} notifications supprimées avec succès`);\n          },\n          error: error => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError('Erreur lors de la suppression de toutes les notifications');\n          }\n        });\n        this.subscriptions.add(deleteAllSub);\n      },\n      cancel: () => this.showDeleteConfirmModal = false\n    };\n    this.showDeleteSelectedConfirmation = this.notificationDeleteMethods.showDeleteSelectedConfirmation;\n    this.deleteSelectedNotifications = this.notificationDeleteMethods.deleteSelected;\n    this.deleteNotification = this.notificationDeleteMethods.deleteOne;\n    this.deleteAllNotifications = this.notificationDeleteMethods.deleteAll;\n    this.cancelDeleteNotifications = this.notificationDeleteMethods.cancel;\n    // Méthodes utilitaires de notification consolidées\n    this.notificationUtilMethods = {\n      formatDate: date => this.MessageService.formatLastActive(date),\n      getIcon: type => this.c.notifications[type]?.icon || 'fas fa-bell',\n      getColor: type => this.c.notifications[type]?.color || 'text-cyan-500',\n      trackById: (index, notification) => this.c.trackById(0, notification)\n    };\n    this.formatNotificationDate = this.notificationUtilMethods.formatDate;\n    this.getNotificationIcon = this.notificationUtilMethods.getIcon;\n    this.getNotificationColor = this.notificationUtilMethods.getColor;\n    this.trackByNotificationId = this.notificationUtilMethods.trackById;\n    // Méthodes de gestion des panneaux consolidées\n    this.panelMethods = {\n      getActivePanels: () => {\n        const panels = [];\n        if (this.showUserStatusPanel) panels.push({\n          key: 'userStatus',\n          title: 'Utilisateurs',\n          icon: 'fas fa-users',\n          closeAction: () => this.showUserStatusPanel = false\n        });\n        if (this.showCallHistoryPanel) panels.push({\n          key: 'callHistory',\n          title: 'Historique des appels',\n          icon: 'fas fa-history',\n          closeAction: () => this.showCallHistoryPanel = false\n        });\n        if (this.showCallStatsPanel) panels.push({\n          key: 'callStats',\n          title: \"Statistiques d'appels\",\n          icon: 'fas fa-chart-bar',\n          closeAction: () => this.showCallStatsPanel = false\n        });\n        if (this.showVoiceMessagesPanel) panels.push({\n          key: 'voiceMessages',\n          title: 'Messages vocaux',\n          icon: 'fas fa-microphone',\n          closeAction: () => this.showVoiceMessagesPanel = false\n        });\n        return panels;\n      },\n      getStatusOptions: () => Object.entries(this.c.status).map(([key, config]) => ({\n        key,\n        ...config\n      })),\n      getThemeOptions: () => this.c.themes\n    };\n    this.getActivePanels = this.panelMethods.getActivePanels;\n    this.getStatusOptions = this.panelMethods.getStatusOptions;\n    this.getThemeOptions = this.panelMethods.getThemeOptions;\n    // Méthodes de statut simplifiées\n    this.statusMethods = {\n      getText: status => this.c.status[status]?.text || 'Inconnu',\n      getColor: status => this.c.status[status]?.color || 'text-gray-400',\n      getIcon: status => this.c.status[status]?.icon || 'fas fa-question-circle'\n    };\n    this.getStatusText = this.statusMethods.getText;\n    this.getStatusColor = this.statusMethods.getColor;\n    this.getStatusIcon = this.statusMethods.getIcon;\n    // Méthodes utilitaires consolidées\n    this.utilityMethods = {\n      formatLastSeen: lastActive => lastActive ? this.MessageService.formatLastActive(lastActive) : 'Jamais vu',\n      getOnlineUsersCount: () => Array.from(this.onlineUsers.values()).filter(user => user.isOnline).length,\n      getFilteredUsers: () => Array.from(this.onlineUsers.values()),\n      setStatusFilter: filter => this.statusFilterType = filter\n    };\n    this.formatLastSeen = this.utilityMethods.formatLastSeen;\n    this.getOnlineUsersCount = this.utilityMethods.getOnlineUsersCount;\n    this.setStatusFilter = this.utilityMethods.setStatusFilter;\n    this.getFilteredUsers = this.utilityMethods.getFilteredUsers;\n    // Méthodes de réponse et transfert consolidées\n    this.replyForwardMethods = {\n      startReply: message => this.replyingToMessage = message,\n      cancelReply: () => this.replyingToMessage = null,\n      openForwardModal: message => {\n        this.forwardingMessage = message;\n        this.showForwardModal = true;\n      },\n      closeForwardModal: () => {\n        this.showForwardModal = false;\n        this.forwardingMessage = null;\n        this.selectedConversations = [];\n      }\n    };\n    this.startReplyToMessage = this.replyForwardMethods.startReply;\n    this.cancelReply = this.replyForwardMethods.cancelReply;\n    this.openForwardModal = this.replyForwardMethods.openForwardModal;\n    this.closeForwardModal = this.replyForwardMethods.closeForwardModal;\n    // Messages - méthodes consolidées\n    this.messageMethods = {\n      getPinIcon: message => this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack',\n      getPinDisplayText: message => this.isMessagePinned(message) ? 'Désépingler' : 'Épingler',\n      canEditMessage: message => message.sender?.id === this.currentUserId,\n      isMessagePinned: message => message.isPinned || false\n    };\n    this.getPinIcon = this.messageMethods.getPinIcon;\n    this.getPinDisplayText = this.messageMethods.getPinDisplayText;\n    this.canEditMessage = this.messageMethods.canEditMessage;\n    this.isMessagePinned = this.messageMethods.isMessagePinned;\n    // Méthodes d'édition consolidées\n    this.editMethods = {\n      startEditMessage: message => {\n        this.editingMessageId = message.id;\n        this.editingContent = message.content;\n      },\n      cancelEditMessage: () => {\n        this.editingMessageId = null;\n        this.editingContent = '';\n      },\n      saveEditMessage: messageId => this.editMethods.cancelEditMessage(),\n      onEditKeyPress: (event, messageId) => {\n        if (event.key === 'Enter' && !event.shiftKey) {\n          event.preventDefault();\n          this.editMethods.saveEditMessage(messageId);\n        } else if (event.key === 'Escape') {\n          this.editMethods.cancelEditMessage();\n        }\n      }\n    };\n    this.startEditMessage = this.editMethods.startEditMessage;\n    this.cancelEditMessage = this.editMethods.cancelEditMessage;\n    this.saveEditMessage = this.editMethods.saveEditMessage;\n    this.onEditKeyPress = this.editMethods.onEditKeyPress;\n    // Utilitaires d'appel consolidées\n    this.callUtilities = {\n      getCallStatusColor: status => this.c.callStatusColors[status] || 'text-gray-500',\n      getCallTypeIcon: type => type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',\n      formatCallDuration: duration => {\n        if (!duration) return '00:00';\n        const minutes = Math.floor(duration / 60);\n        const seconds = duration % 60;\n        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      },\n      formatCallDate: timestamp => this.MessageService.formatMessageDate(timestamp)\n    };\n    this.getCallStatusColor = this.callUtilities.getCallStatusColor;\n    this.getCallTypeIcon = this.callUtilities.getCallTypeIcon;\n    this.formatCallDuration = this.callUtilities.formatCallDuration;\n    this.formatCallDate = this.callUtilities.formatCallDate;\n    // Méthodes d'événements consolidées\n    this.eventMethods = {\n      onDocumentClick: event => {\n        const target = event.target;\n        const closeConfigs = [{\n          selectors: ['.theme-selector-menu', '.btn-theme'],\n          property: 'showThemeSelector'\n        }, {\n          selectors: ['.emoji-picker', '.btn-emoji'],\n          property: 'showEmojiPicker'\n        }];\n        closeConfigs.forEach(config => {\n          const isClickOutside = !config.selectors.some(selector => target.closest(selector));\n          if (isClickOutside) {\n            this[config.property] = false;\n          }\n        });\n      },\n      confirmDeleteMessage: messageId => {\n        this.showDeleteConfirm[messageId] = false;\n      }\n    };\n    this.onDocumentClick = this.eventMethods.onDocumentClick;\n    this.confirmDeleteMessage = this.eventMethods.confirmDeleteMessage;\n    // Méthodes de réaction consolidées\n    this.reactionMethods = {\n      getUniqueReactions: message => message.reactions || [],\n      onReactionClick: (messageId, emoji) => {\n        // Implémentation des réactions\n      },\n      hasUserReacted: (message, emoji) => false\n    };\n    this.getUniqueReactions = this.reactionMethods.getUniqueReactions;\n    this.onReactionClick = this.reactionMethods.onReactionClick;\n    this.hasUserReacted = this.reactionMethods.hasUserReacted;\n    // Méthodes de conversation consolidées\n    this.conversationSelectionMethods = {\n      areAllSelected: () => this.selectedConversations.length === this.availableConversations.length,\n      selectAll: () => this.selectedConversations = this.availableConversations.map(c => c.id),\n      deselectAll: () => this.selectedConversations = [],\n      toggle: conversationId => {\n        const index = this.selectedConversations.indexOf(conversationId);\n        index > -1 ? this.selectedConversations.splice(index, 1) : this.selectedConversations.push(conversationId);\n      },\n      isSelected: conversationId => this.selectedConversations.includes(conversationId),\n      getDisplayImage: conversation => conversation.image || 'assets/images/default-avatar.png',\n      getDisplayName: conversation => conversation.name || 'Conversation',\n      forwardMessage: () => {\n        this.isForwarding = true;\n        setTimeout(() => {\n          this.isForwarding = false;\n          this.closeForwardModal();\n        }, 1000);\n      }\n    };\n    this.areAllConversationsSelected = this.conversationSelectionMethods.areAllSelected;\n    this.selectAllConversations = this.conversationSelectionMethods.selectAll;\n    this.deselectAllConversations = this.conversationSelectionMethods.deselectAll;\n    this.toggleConversationSelection = this.conversationSelectionMethods.toggle;\n    this.isConversationSelected = this.conversationSelectionMethods.isSelected;\n    this.getConversationDisplayImage = this.conversationSelectionMethods.getDisplayImage;\n    this.getConversationDisplayName = this.conversationSelectionMethods.getDisplayName;\n    this.forwardMessage = this.conversationSelectionMethods.forwardMessage;\n    // Méthodes de notification simplifiées consolidées\n    this.simpleNotificationMethods = {\n      onCallMouseMove: () => this.showCallControls = true,\n      saveNotificationSettings: () => {},\n      setNotificationFilter: filter => this.notificationFilter = filter\n    };\n    this.onCallMouseMove = this.simpleNotificationMethods.onCallMouseMove;\n    this.saveNotificationSettings = this.simpleNotificationMethods.saveNotificationSettings;\n    this.setNotificationFilter = this.simpleNotificationMethods.setNotificationFilter;\n    // Méthodes de recherche consolidées\n    this.searchMethods = {\n      onInput: event => {\n        this.searchQuery = event.target.value;\n        this.searchQuery.length >= 2 ? this.searchMethods.perform() : this.searchMethods.clear();\n      },\n      onKeyPress: event => {\n        if (event.key === 'Enter') {\n          this.searchMethods.perform();\n        } else if (event.key === 'Escape') {\n          this.searchMethods.clear();\n        }\n      },\n      perform: () => {\n        this.isSearching = true;\n        this.searchMode = true;\n        setTimeout(() => {\n          this.searchResults = this.messages.filter(m => m.content?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n          this.isSearching = false;\n        }, 500);\n      },\n      clear: () => {\n        this.searchQuery = '';\n        this.searchResults = [];\n        this.isSearching = false;\n        this.searchMode = false;\n      }\n    };\n    this.onSearchInput = this.searchMethods.onInput;\n    this.onSearchKeyPress = this.searchMethods.onKeyPress;\n    this.performSearch = this.searchMethods.perform;\n    this.clearSearch = this.searchMethods.clear;\n    // Méthodes utilitaires finales consolidées\n    this.finalUtilityMethods = {\n      navigateToMessage: messageId => {\n        // Navigation vers un message spécifique\n      },\n      scrollToPinnedMessage: messageId => {\n        // Défilement vers un message épinglé\n      },\n      getPinnedMessagesCount: () => this.pinnedMessages.length\n    };\n    this.navigateToMessage = this.finalUtilityMethods.navigateToMessage;\n    this.scrollToPinnedMessage = this.finalUtilityMethods.scrollToPinnedMessage;\n    this.getPinnedMessagesCount = this.finalUtilityMethods.getPinnedMessagesCount;\n    // Méthodes de recherche et réaction consolidées\n    this.searchAndReactionMethods = {\n      highlightSearchTerms: (content, query) => {\n        if (!query) return content;\n        const regex = new RegExp(`(${query})`, 'gi');\n        return content.replace(regex, '<mark>$1</mark>');\n      },\n      toggleReactionPicker: messageId => this.showReactionPicker[messageId] = !this.showReactionPicker[messageId],\n      reactToMessage: (messageId, emoji) => this.showReactionPicker[messageId] = false,\n      toggleMessageOptions: messageId => this.showMessageOptions[messageId] = !this.showMessageOptions[messageId]\n    };\n    this.highlightSearchTerms = this.searchAndReactionMethods.highlightSearchTerms;\n    this.toggleReactionPicker = this.searchAndReactionMethods.toggleReactionPicker;\n    this.reactToMessage = this.searchAndReactionMethods.reactToMessage;\n    this.toggleMessageOptions = this.searchAndReactionMethods.toggleMessageOptions;\n    // Confirmations consolidées\n    this.confirmationMethods = {\n      showPinConfirmation: messageId => this.showPinConfirm[messageId] = true,\n      cancelPinConfirmation: messageId => this.showPinConfirm[messageId] = false,\n      showDeleteConfirmation: messageId => this.showDeleteConfirm[messageId] = true,\n      cancelDeleteMessage: messageId => this.showDeleteConfirm[messageId] = false\n    };\n    this.showPinConfirmation = this.confirmationMethods.showPinConfirmation;\n    this.cancelPinConfirmation = this.confirmationMethods.cancelPinConfirmation;\n    this.showDeleteConfirmation = this.confirmationMethods.showDeleteConfirmation;\n    this.cancelDeleteMessage = this.confirmationMethods.cancelDeleteMessage;\n    // Méthodes de nettoyage optimisées\n    this.cleanup = {\n      clearTimeouts: () => [this.typingTimeout, this.autoAwayTimeout, this.callTimer].forEach(t => t && clearTimeout(t)),\n      setUserOffline: () => this.currentUserId && this.MessageService.setUserOffline(this.currentUserId).subscribe(),\n      stopTypingIndicator: () => {\n        if (this.isCurrentlyTyping && this.conversation?.id) {\n          this.isCurrentlyTyping = false;\n          clearTimeout(this.typingTimer);\n          this.MessageService.stopTyping(this.conversation.id).subscribe({\n            next: () => {},\n            error: error => {}\n          });\n        }\n      }\n    };\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]]\n    });\n  }\n  ngOnInit() {\n    this.currentUserId = this.authService.getCurrentUserId();\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n    }\n    this.subscribeToNotifications();\n    this.subscribeToUserStatus();\n    this.initializeUserStatus();\n    this.startActivityTracking();\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n    const routeSub = this.route.params.pipe(filter(params => params['id']), distinctUntilChanged(), switchMap(params => {\n      this.loading = true;\n      this.messages = [];\n      this.currentPage = 1;\n      this.hasMoreMessages = true;\n      return this.MessageService.getConversation(params['id'], this.MAX_MESSAGES_TO_LOAD, this.currentPage);\n    })).subscribe({\n      next: conversation => {\n        this.handleConversationLoaded(conversation);\n      },\n      error: error => {\n        this.handleError('Failed to load conversation', error);\n      }\n    });\n    this.subscriptions.add(routeSub);\n  }\n  // Gestion centralisée des erreurs\n  handleError(message, error, resetLoading = true) {\n    console.error('MessageChat', message, error);\n    if (resetLoading) {\n      this.loading = false;\n      this.isUploading = false;\n      this.isLoadingMore = false;\n    }\n    this.error = error;\n    this.toastService.showError(message);\n  }\n  // Gestion centralisée des succès\n  handleSuccess(message, callback) {\n    if (message) {\n      this.toastService.showSuccess(message);\n    }\n    if (callback) {\n      callback();\n    }\n  }\n  handleConversationLoaded(conversation) {\n    this.conversation = conversation;\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n      this.messages = [];\n    } else {\n      const conversationMessages = [...(conversation?.messages || [])];\n      conversationMessages.sort((a, b) => {\n        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n        return timeA - timeB;\n      });\n      this.messages = conversationMessages;\n    }\n    this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n    this.markMessagesAsRead();\n    if (this.conversation?.id) {\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub = this.MessageService.subscribeToConversationUpdates(conversationId).subscribe({\n      next: updatedConversation => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages ? [...updatedConversation.messages] : [];\n        this.scrollToBottom();\n      },\n      error: error => {\n        this.toastService.showError('Connection to conversation updates lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToNewMessages(conversationId) {\n    const sub = this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n      next: newMessage => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n            const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          setTimeout(() => this.scrollToBottom(), 100);\n          if (newMessage.sender?.id !== this.currentUserId && newMessage.sender?._id !== this.currentUserId) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: error => {\n        this.toastService.showError('Connection to new messages lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToTypingIndicators(conversationId) {\n    const sub = this.MessageService.subscribeToTypingIndicator(conversationId).subscribe({\n      next: event => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  markMessagesAsRead() {\n    const unreadMessages = this.messages.filter(msg => !msg.isRead && (msg.receiver?.id === this.currentUserId || msg.receiver?._id === this.currentUserId));\n    unreadMessages.forEach(msg => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: error => {}\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (!file) return;\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError('Invalid file type. Only images, PDFs and Word docs are allowed');\n      return;\n    }\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n  removeAttachment() {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n  // Frappe\n  onTyping() {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n    const conversationId = this.conversation.id;\n    clearTimeout(this.typingTimer);\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {},\n        error: error => {}\n      });\n    }\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {},\n          error: error => {}\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n  // Panneaux\n  togglePanel(panelName, closeOthers = true) {\n    const panels = {\n      theme: 'showThemeSelector',\n      menu: 'showMainMenu',\n      emoji: 'showEmojiPicker',\n      notification: 'showNotificationPanel',\n      search: 'showSearchBar',\n      status: 'showStatusSelector'\n    };\n    const currentPanel = panels[panelName];\n    if (currentPanel) {\n      this[currentPanel] = !this[currentPanel];\n      if (closeOthers && this[currentPanel]) {\n        Object.values(panels).forEach(panel => {\n          if (panel !== currentPanel) {\n            this[panel] = false;\n          }\n        });\n      }\n    }\n  }\n  changeTheme(theme) {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    localStorage.setItem('chat-theme', theme);\n  }\n  // Conversation - méthode utilitaire\n  showDevelopmentFeature(featureName) {\n    this.showMainMenu = false;\n    this.toastService.showInfo(`${featureName} en cours de développement`);\n  }\n  sendMessage() {\n    if (this.messageForm.invalid && !this.selectedFile || !this.currentUserId || !this.otherParticipant?.id) {\n      return;\n    }\n    this.cleanup.stopTypingIndicator();\n    const content = this.messageForm.get('content')?.value;\n    const tempMessage = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient'\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true\n    };\n    if (this.selectedFile) {\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n        if (this.previewUrl) {\n          tempMessage.attachments = [{\n            id: 'temp-attachment',\n            url: this.previewUrl ? this.previewUrl.toString() : '',\n            type: MessageType.IMAGE,\n            name: this.selectedFile.name,\n            size: this.selectedFile.size\n          }];\n        }\n      }\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n    this.messages = [...this.messages, tempMessage];\n    const fileToSend = this.selectedFile;\n    this.messageForm.reset();\n    this.removeAttachment();\n    setTimeout(() => this.scrollToBottom(true), 50);\n    this.isUploading = true;\n    const sendSub = this.MessageService.sendMessage(this.otherParticipant.id, content, fileToSend || undefined, MessageType.TEXT, this.conversation?.id).subscribe({\n      next: message => {\n        this.updateMessageState(tempMessage.id, message);\n        this.isUploading = false;\n      },\n      error: error => {\n        this.updateMessageState(tempMessage.id, null, true);\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      }\n    });\n    this.subscriptions.add(sendSub);\n  }\n  // Méthode consolidée pour mettre à jour l'état des messages\n  updateMessageState(tempId, newMessage, isError = false) {\n    this.messages = this.messages.map(msg => {\n      if (msg.id === tempId) {\n        if (newMessage) {\n          return newMessage;\n        } else if (isError) {\n          return {\n            ...msg,\n            isPending: false,\n            isError: true\n          };\n        }\n      }\n      return msg;\n    });\n  }\n  // Défilement\n  onScroll(event) {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n    if (scrollTop < 50 && !this.isLoadingMore && this.conversation?.id && this.hasMoreMessages) {\n      this.showLoadingIndicator();\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n      this.isLoadingMore = true;\n      this.loadMoreMessages();\n      // Maintenir la position de défilement\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(firstVisibleMessage.id);\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({\n                block: 'center'\n              });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n  // Méthode pour trouver le premier message visible dans la vue\n  getFirstVisibleMessage() {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length) return null;\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find(m => m.id === messageId) || null;\n      }\n    }\n    return null;\n  }\n  // Méthode pour trouver un élément de message par ID\n  findMessageElement(messageId) {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(`[data-message-id=\"${messageId}\"]`);\n  }\n  // Charger plus de messages\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages) return;\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n    // Augmenter la page\n    this.currentPage++;\n    // Charger plus de messages\n    this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        if (conversation && conversation.messages && conversation.messages.length > 0) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n          // Créer un Set des IDs existants\n          const existingIds = new Set(oldMessages.map(msg => msg.id));\n          // Filtrer et trier les nouveaux messages\n          const newMessages = conversation.messages.filter(msg => !existingIds.has(msg.id)).sort((a, b) => {\n            const timeA = new Date(a.timestamp).getTime();\n            const timeB = new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages = newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: error => {\n        console.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      }\n    });\n  }\n  // Méthode utilitaire pour comparer les timestamps\n  isSameTimestamp(timestamp1, timestamp2) {\n    if (!timestamp1 || !timestamp2) return false;\n    try {\n      const time1 = timestamp1 instanceof Date ? timestamp1.getTime() : new Date(timestamp1).getTime();\n      const time2 = timestamp2 instanceof Date ? timestamp2.getTime() : new Date(timestamp2).getTime();\n      return Math.abs(time1 - time2) < 1000;\n    } catch (error) {\n      return false;\n    }\n  }\n  scrollToBottom(force = false) {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 150;\n        if (force || isScrolledToBottom) {\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth'\n          });\n        }\n      });\n    } catch (err) {\n      console.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl) {\n    window.open(imageUrl, '_blank');\n  }\n  /**\n   * Détecte les changements après chaque vérification de la vue - Optimisé\n   */\n  ngAfterViewChecked() {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n    // Détection des changements optimisée - Suppression de la vérification inutile\n    // La détection automatique d'Angular gère déjà les messages vocaux\n  }\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations() {\n    this.router.navigate(['/messages/conversations']);\n  }\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji) {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector('.whatsapp-input-field');\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  subscribeToNotifications() {\n    const notificationSub = this.MessageService.subscribeToNewNotifications().subscribe({\n      next: notification => {\n        this.notifications.unshift(notification);\n        this.updateNotificationCount();\n        this.MessageService.play('notification');\n        if (notification.type === 'NEW_MESSAGE' && notification.conversationId === this.conversation?.id) {\n          if (notification.id) {\n            this.MessageService.markAsRead([notification.id]).subscribe();\n          }\n        }\n      },\n      error: error => {}\n    });\n    this.subscriptions.add(notificationSub);\n    const notificationsListSub = this.MessageService.notifications$.subscribe({\n      next: notifications => {\n        this.notifications = notifications;\n        this.updateNotificationCount();\n      },\n      error: error => {}\n    });\n    this.subscriptions.add(notificationsListSub);\n    const notificationCountSub = this.MessageService.notificationCount$.subscribe({\n      next: count => {\n        this.unreadNotificationCount = count;\n      }\n    });\n    this.subscriptions.add(notificationCountSub);\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: call => {\n        if (call) {\n          this.incomingCall = call;\n          this.showCallModal = true;\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      }\n    });\n    this.subscriptions.add(callSub);\n    const activeCallSub = this.MessageService.activeCall$.subscribe({\n      next: call => {\n        this.activeCall = call;\n        if (call) {\n          this.showActiveCallModal = true;\n          this.startCallTimerMethod();\n        } else {\n          this.showActiveCallModal = false;\n          this.stopCallTimerMethod();\n          this.resetCallStateMethod();\n        }\n      }\n    });\n    this.subscriptions.add(activeCallSub);\n    // S'abonner aux flux vidéo locaux\n    const localStreamSub = this.MessageService.localStream$.subscribe({\n      next: stream => {\n        if (stream && this.localVideoElement) {\n          this.localVideoElement.srcObject = stream;\n        }\n      }\n    });\n    this.subscriptions.add(localStreamSub);\n    // S'abonner aux flux vidéo distants\n    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n      next: stream => {\n        if (stream && this.remoteVideoElement) {\n          this.remoteVideoElement.srcObject = stream;\n        }\n      }\n    });\n    this.subscriptions.add(remoteStreamSub);\n  }\n  /**\n   * Charge les notifications\n   */\n  loadNotifications(refresh = false) {\n    const loadSub = this.MessageService.getNotifications(refresh, 1, 20).subscribe({\n      next: notifications => {\n        if (refresh) {\n          this.notifications = notifications;\n        } else {\n          this.notifications = [...this.notifications, ...notifications];\n        }\n        this.updateNotificationCount();\n      },\n      error: error => {\n        this.toastService.showError('Erreur lors du chargement des notifications');\n      }\n    });\n    this.subscriptions.add(loadSub);\n  }\n  /**\n   * S'abonne au statut utilisateur en temps réel\n   */\n  subscribeToUserStatus() {\n    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n      next: user => {\n        this.handleUserStatusUpdate(user);\n      },\n      error: error => {\n        // Error handled silently\n      }\n    });\n    this.subscriptions.add(statusSub);\n  }\n  /**\n   * Gère la mise à jour du statut d'un utilisateur\n   */\n  handleUserStatusUpdate(user) {\n    if (!user.id) return;\n    if (user.isOnline) {\n      this.onlineUsers.set(user.id, user);\n    } else {\n      this.onlineUsers.delete(user.id);\n    }\n    if (this.otherParticipant && this.otherParticipant.id === user.id) {\n      this.otherParticipant = {\n        ...this.otherParticipant,\n        ...user\n      };\n    }\n  }\n  /**\n   * Initialise le statut de l'utilisateur actuel\n   */\n  initializeUserStatus() {\n    if (!this.currentUserId) return;\n    const setOnlineSub = this.MessageService.setUserOnline(this.currentUserId).subscribe({\n      next: user => {\n        this.currentUserStatus = 'online';\n        this.lastActivityTime = new Date();\n      },\n      error: error => {\n        console.error('MessageChat', \"Erreur lors de l'initialisation du statut\", error);\n      }\n    });\n    this.subscriptions.add(setOnlineSub);\n  }\n  /**\n   * Démarre le suivi d'activité automatique\n   */\n  startActivityTracking() {\n    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];\n    events.forEach(event => {\n      document.addEventListener(event, this.onUserActivity.bind(this), true);\n    });\n  }\n  /**\n   * Gère l'activité de l'utilisateur\n   */\n  onUserActivity() {\n    this.lastActivityTime = new Date();\n    // Réinitialiser le timer\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n    // Remettre en ligne si absent\n    if (this.currentUserStatus === 'away' || this.currentUserStatus === 'offline') {\n      this.updateUserStatus('online');\n    }\n    // Programmer la mise en absence\n    this.autoAwayTimeout = setTimeout(() => {\n      if (this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      }\n    }, this.c.delays.away);\n  }\n  /**\n   * Met à jour le statut de l'utilisateur\n   */\n  updateUserStatus(status) {\n    if (!this.currentUserId) return;\n    const previousStatus = this.currentUserStatus;\n    let updateObservable;\n    if (status === 'online') {\n      updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n    } else {\n      updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n    }\n    const updateSub = updateObservable.subscribe({\n      next: user => {\n        this.currentUserStatus = status;\n        if (status !== previousStatus) {\n          const statusText = this.getStatusText(status);\n          this.toastService.showInfo(`Statut : ${statusText}`);\n        }\n      },\n      error: error => {\n        console.error('MessageChat', 'Erreur lors de la mise à jour du statut', error);\n      }\n    });\n    this.subscriptions.add(updateSub);\n  }\n  /**\n   * Charge la liste des utilisateurs en ligne\n   */\n  loadOnlineUsers() {\n    const usersSub = this.MessageService.getAllUsers(false, undefined, 1, 50, 'username', 'asc', true).subscribe({\n      next: users => {\n        users.forEach(user => {\n          if (user.isOnline && user.id) {\n            this.onlineUsers.set(user.id, user);\n          }\n        });\n      },\n      error: error => {\n        console.error('MessageChat', 'Erreur lors du chargement des utilisateurs en ligne', error);\n      }\n    });\n    this.subscriptions.add(usersSub);\n  }\n  togglePinMessage(message) {\n    this.isPinning[message.id] = true;\n    setTimeout(() => {\n      this.isPinning[message.id] = false;\n      this.showPinConfirm[message.id] = false;\n    }, 1000);\n  }\n  ngOnDestroy() {\n    this.cleanup.clearTimeouts();\n    this.cleanup.setUserOffline();\n    this.cleanup.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.UserStatusService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 34,\n      vars: 14,\n      consts: [[1, \"chat-container\"], [1, \"chat-header\"], [1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [\"alt\", \"User avatar\", 1, \"user-avatar\", 3, \"src\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"title\", \"Appel audio\", 1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Rechercher\", 1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Notifications\", 1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-bell\"], [1, \"messages-area\"], [\"messagesContainer\", \"\"], [\"style\", \"text-align: center; padding: 2rem; color: #00f7ff\", 4, \"ngIf\"], [\"style\", \"text-align: center; padding: 2rem; color: #888\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"input-area\"], [1, \"input-form\", 3, \"formGroup\", \"ngSubmit\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", 1, \"message-input\", 3, \"disabled\"], [\"type\", \"submit\", 1, \"send-btn\", 3, \"disabled\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"side-panel\", 3, \"ngClass\"], [1, \"panel-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"style\", \"padding: 1rem; text-align: center; color: #888\", 4, \"ngIf\"], [\"style\", \"padding: 0.5rem; border-bottom: 1px solid rgba(0, 247, 255, 0.1)\", 4, \"ngFor\", \"ngForOf\"], [\"style\", \"\\n      position: absolute;\\n      top: 60px;\\n      left: 0;\\n      right: 0;\\n      background: rgba(0, 0, 0, 0.9);\\n      padding: 1rem;\\n      z-index: 100;\\n    \", 4, \"ngIf\"], [1, \"user-info\"], [1, \"user-status\"], [2, \"text-align\", \"center\", \"padding\", \"2rem\", \"color\", \"#00f7ff\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [2, \"text-align\", \"center\", \"padding\", \"2rem\", \"color\", \"#888\"], [1, \"fas\", \"fa-comments\", 2, \"font-size\", \"3rem\", \"margin-bottom\", \"1rem\", \"opacity\", \"0.5\"], [2, \"font-size\", \"0.9rem\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-bubble\"], [2, \"font-size\", \"0.7rem\", \"opacity\", \"0.7\", \"margin-top\", \"0.5rem\"], [2, \"padding\", \"1rem\", \"text-align\", \"center\", \"color\", \"#888\"], [1, \"fas\", \"fa-bell-slash\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"0.5rem\", \"opacity\", \"0.5\"], [2, \"padding\", \"0.5rem\", \"border-bottom\", \"1px solid rgba(0, 247, 255, 0.1)\"], [2, \"margin\", \"0\", \"font-size\", \"0.9rem\"], [2, \"opacity\", \"0.7\"], [2, \"position\", \"absolute\", \"top\", \"60px\", \"left\", \"0\", \"right\", \"0\", \"background\", \"rgba(0, 0, 0, 0.9)\", \"padding\", \"1rem\", \"z-index\", \"100\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"0.5rem\"], [\"placeholder\", \"Rechercher dans la conversation...\", 2, \"flex\", \"1\", \"padding\", \"0.5rem\", \"background\", \"rgba(0, 0, 0, 0.5)\", \"border\", \"1px solid #00f7ff\", \"border-radius\", \"5px\", \"color\", \"#e0e0e0\", 3, \"ngModel\", \"ngModelChange\"], [2, \"background\", \"transparent\", \"border\", \"1px solid #00f7ff\", \"color\", \"#00f7ff\", \"padding\", \"0.5rem\", \"border-radius\", \"5px\", 3, \"click\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵtemplate(5, MessageChatComponent_div_5_Template, 5, 2, \"div\", 5);\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_7_listener() {\n            return ctx.initiateCall(\"AUDIO\");\n          });\n          i0.ɵɵelement(8, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_9_listener() {\n            return ctx.initiateCall(\"VIDEO\");\n          });\n          i0.ɵɵelement(10, \"i\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_11_listener() {\n            return ctx.toggleSearchBar();\n          });\n          i0.ɵɵelement(12, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_13_listener() {\n            return ctx.toggleNotificationPanel();\n          });\n          i0.ɵɵelement(14, \"i\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 15, 16);\n          i0.ɵɵtemplate(17, MessageChatComponent_div_17_Template, 3, 0, \"div\", 17);\n          i0.ɵɵtemplate(18, MessageChatComponent_div_18_Template, 6, 0, \"div\", 18);\n          i0.ɵɵtemplate(19, MessageChatComponent_div_19_Template, 6, 6, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 20)(21, \"form\", 21);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_21_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelement(22, \"input\", 22);\n          i0.ɵɵelementStart(23, \"button\", 23);\n          i0.ɵɵelement(24, \"i\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 25)(26, \"div\", 26)(27, \"h3\");\n          i0.ɵɵtext(28, \"Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_29_listener() {\n            return ctx.toggleNotificationPanel();\n          });\n          i0.ɵɵelement(30, \"i\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, MessageChatComponent_div_31_Template, 4, 0, \"div\", 29);\n          i0.ɵɵtemplate(32, MessageChatComponent_div_32_Template, 5, 2, \"div\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, MessageChatComponent_div_33_Template, 5, 1, \"div\", 31);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.otherParticipant);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.messageForm.invalid || !ctx.otherParticipant);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c3, ctx.showNotificationPanel));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.notifications.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showSearchBar);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.NgModel, i4.FormGroupDirective, i4.FormControlName],\n      styles: [\"\\n\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);\\n  color: #e0e0e0;\\n}\\n\\n\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 1rem;\\n  background: rgba(0, 247, 255, 0.1);\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  margin-right: 1rem;\\n  border: 2px solid #00f7ff;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #00f7ff;\\n  font-size: 1.1rem;\\n}\\n\\n.user-status[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #888;\\n}\\n\\n\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 1px solid #00f7ff;\\n  color: #00f7ff;\\n  padding: 0.5rem;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: #00f7ff;\\n  color: #000;\\n  box-shadow: 0 0 10px #00f7ff;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  background: linear-gradient(135deg, #ff6b69, #ff4757);\\n  color: white;\\n  font-size: 0.7rem;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.messages-area[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  background: rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.message[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.message.current-user[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  max-width: 70%;\\n  padding: 0.8rem 1rem;\\n  border-radius: 18px;\\n  position: relative;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.message.current-user[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff, #0066ff);\\n  color: #fff;\\n  border-radius: 18px 18px 4px 18px;\\n}\\n\\n.message.other-user[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: #e0e0e0;\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  border-radius: 18px 18px 18px 4px;\\n}\\n\\n\\n\\n.input-area[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background: rgba(0, 247, 255, 0.05);\\n  border-top: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.input-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.8rem 1rem;\\n  background: rgba(0, 0, 0, 0.5);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  border-radius: 25px;\\n  color: #e0e0e0;\\n  outline: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  border-color: #00f7ff;\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.3);\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff, #0066ff);\\n  border: none;\\n  color: #fff;\\n  padding: 0.8rem;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.send-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n.side-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: -300px;\\n  width: 300px;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.9);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-left: 1px solid rgba(0, 247, 255, 0.3);\\n  transition: right 0.3s ease;\\n  z-index: 1000;\\n  padding: 1rem;\\n}\\n\\n.side-panel.open[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: none;\\n  color: #00f7ff;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .chat-header[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n\\n  .message-bubble[_ngcontent-%COMP%] {\\n    max-width: 85%;\\n  }\\n\\n  .side-panel[_ngcontent-%COMP%] {\\n    width: 100%;\\n    right: -100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "MessageType", "CallType", "switchMap", "distinctUntilChanged", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "otherParticipant", "username", "ɵɵtextInterpolate1", "isOnline", "formatLastActive", "lastActive", "ɵɵelement", "ɵɵproperty", "ɵɵpureFunction2", "_c2", "message_r8", "sender", "id", "ctx_r4", "currentUserId", "content", "formatMessageTime", "timestamp", "notification_r9", "ctx_r6", "ɵɵlistener", "MessageChatComponent_div_33_Template_input_ngModelChange_2_listener", "$event", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "searchQuery", "MessageChatComponent_div_33_Template_button_click_3_listener", "ctx_r12", "toggleSearchBar", "ctx_r7", "MessageChatComponent", "availableReactions", "c", "reactions", "commonEmojis", "MessageService", "getCommonEmojis", "getHeaderActions", "class", "icon", "title", "onClick", "initiateCall", "isActive", "showSearchBar", "activeClass", "getPinnedMessagesCount", "togglePinnedMessages", "showPinnedMessages", "badge", "count", "animate", "toggleNotificationPanel", "showNotificationPanel", "unreadNotificationCount", "toggleCallHistoryPanel", "showCallHistoryPanel", "toggleCallStatsPanel", "showCallStatsPanel", "toggleVoiceMessagesPanel", "showVoiceMessagesPanel", "voiceMessages", "length", "constructor", "route", "authService", "fb", "statusService", "router", "toastService", "cdr", "messages", "conversation", "loading", "currentUsername", "selectedFile", "previewUrl", "isUploading", "isTyping", "isRecordingVoice", "voiceRecordingDuration", "MAX_MESSAGES_PER_SIDE", "MAX_MESSAGES_TO_LOAD", "MAX_TOTAL_MESSAGES", "currentPage", "isLoadingMore", "hasMoreMessages", "subscriptions", "selectedTheme", "showThemeSelector", "showMainMenu", "showEmojiPicker", "showStatusSelector", "showNotificationSettings", "showUserStatusPanel", "incomingCall", "activeCall", "showCallModal", "showActiveCallModal", "isCallMuted", "isVideoEnabled", "callDuration", "callTimer", "notifications", "selectedNotifications", "Set", "showDeleteConfirmModal", "editingMessageId", "<PERSON><PERSON><PERSON><PERSON>", "replyingToMessage", "isSearching", "searchMode", "searchResults", "pinnedMessages", "showReactionPicker", "showDeleteConfirm", "showPinConfirm", "isPinning", "showMessageOptions", "showForwardModal", "forwardingMessage", "selectedConversations", "availableConversations", "isForwarding", "isLoadingConversations", "delays", "away", "typing", "scroll", "anim", "error", "trackById", "i", "item", "toString", "NEW_MESSAGE", "color", "CALL_MISSED", "SYSTEM", "status", "online", "text", "label", "description", "offline", "busy", "themes", "key", "hoverColor", "calls", "COMPLETED", "MISSED", "REJECTED", "notificationConfig", "callStatusColors", "notificationFilter", "isLoadingNotifications", "isMarkingAsRead", "isDeletingNotifications", "hasMoreNotifications", "notificationSounds", "notificationPreview", "autoMarkAsRead", "isCallMinimized", "callQuality", "showCallControls", "onlineUsers", "Map", "currentUserStatus", "lastActivityTime", "Date", "autoAwayTimeout", "isUpdatingStatus", "callHistory", "localVideoElement", "remoteVideoElement", "statusFilterType", "f", "getIcon", "t", "getFileIcon", "getType", "getFileType", "isCurrentlyTyping", "TYPING_DELAY", "TYPING_TIMEOUT", "mainToggleMethods", "themeSelector", "togglePanel", "mainMenu", "emojiPicker", "toggleThemeSelector", "toggleMainMenu", "toggleEmojiPicker", "toggleMethods", "searchBar", "clearSearch", "statusSelector", "notificationSettings", "userStatusPanel", "callMinimize", "callHistoryPanel", "callStatsPanel", "voiceMessagesPanel", "toggleStatusSelector", "toggleNotificationSettings", "toggleUserStatusPanel", "toggleCallMinimize", "conversationMethods", "showInfo", "showDevelopmentFeature", "showSettings", "clear", "showWarning", "confirm", "showSuccess", "export", "conversation<PERSON>ame", "isGroup", "groupName", "exportData", "name", "participants", "createdAt", "map", "msg", "type", "exportedAt", "toISOString", "exportedBy", "blob", "Blob", "JSON", "stringify", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "safeFileName", "replace", "toLowerCase", "dateStr", "split", "download", "click", "revokeObjectURL", "toggleConversationInfo", "toggleConversationSettings", "clearConversation", "exportConversation", "s", "formatTime", "formatActive", "formatDate", "formatMessageDate", "showDateHeader", "shouldShowDateHeader", "m", "getMessageType", "hasImage", "isVoice", "isVoiceMessage", "getVoiceUrl", "getVoiceMessageUrl", "getVoiceDuration", "getVoiceMessageDuration", "getVoiceHeight", "getVoiceBarHeight", "formatVoice", "formatVoiceDuration", "getImageUrl", "getTypeClass", "getMessageTypeClass", "loadingIndicatorMethods", "show", "getElementById", "indicator", "className", "innerHTML", "messagesContainer", "nativeElement", "prepend", "hide", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showLoadingIndicator", "hideLoadingIndicator", "voiceRecordingMethods", "toggle", "complete", "audioBlob", "showError", "receiverId", "sendVoiceMessage", "subscribe", "next", "message", "scrollToBottom", "cancel", "toggleVoiceRecording", "onVoiceRecordingComplete", "onVoiceRecordingCancelled", "callMethods", "initiate", "AUDIO", "VIDEO", "call", "accept", "acceptCall", "reject", "rejectCall", "end", "sub", "activeCall$", "endCall", "unsubscribe", "callControlMethods", "toggleMute", "updateMedia", "toggleVideo", "toggleMedia", "console", "toggleCallMute", "toggleCallVideo", "updateCallMedia", "timerMethods", "startCallTimer", "setInterval", "stopCallTimer", "clearInterval", "resetCallState", "startCallTimerMethod", "stopCallTimerMethod", "resetCallStateMethod", "notificationToggleMethod", "loadNotifications", "notificationMethods", "loadMore", "updateCount", "n", "isRead", "getFiltered", "toggleSelection", "notificationId", "has", "delete", "add", "toggleSelectAll", "filteredNotifications", "allSelected", "every", "for<PERSON>ach", "areAllSelected", "loadMoreNotifications", "updateNotificationCount", "getFilteredNotifications", "toggleNotificationSelection", "toggleSelectAllNotifications", "areAllNotificationsSelected", "markingMethods", "markSelected", "selectedIds", "Array", "from", "mark<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "unreadNotifications", "unreadIds", "ids", "onSuccess", "mark<PERSON>ub", "result", "includes", "readAt", "markSelectedAsRead", "markAllAsRead", "notificationDeleteMethods", "showDeleteSelectedConfirmation", "size", "deleteSelected", "deleteSub", "deleteMultipleNotifications", "deleteOne", "deleteNotification", "deleteAll", "deleteAllSub", "deleteAllNotifications", "deleteSelectedNotifications", "cancelDeleteNotifications", "notificationUtilMethods", "date", "getColor", "index", "notification", "formatNotificationDate", "getNotificationIcon", "getNotificationColor", "trackByNotificationId", "panelMethods", "getActivePanels", "panels", "push", "closeAction", "getStatusOptions", "Object", "entries", "config", "getThemeOptions", "statusMethods", "getText", "getStatusText", "getStatusColor", "getStatusIcon", "utilityMethods", "formatLastSeen", "getOnlineUsersCount", "values", "user", "getFilteredUsers", "setStatus<PERSON>ilter", "replyForwardMethods", "startReply", "cancelReply", "openForwardModal", "closeForwardModal", "startReplyToMessage", "messageMethods", "getPinIcon", "isMessagePinned", "getPinDisplayText", "canEditMessage", "isPinned", "editMethods", "startEditMessage", "cancelEditMessage", "saveEditMessage", "messageId", "onEditKeyPress", "event", "shift<PERSON>ey", "preventDefault", "callUtilities", "getCallStatusColor", "getCallTypeIcon", "formatCallDuration", "duration", "minutes", "Math", "floor", "seconds", "padStart", "formatCallDate", "eventMethods", "onDocumentClick", "target", "closeConfigs", "selectors", "property", "isClickOutside", "some", "selector", "closest", "confirmDeleteMessage", "reactionMethods", "getUniqueReactions", "onReactionClick", "emoji", "hasUserReacted", "conversationSelectionMethods", "selectAll", "deselectAll", "conversationId", "indexOf", "splice", "isSelected", "getDisplayImage", "image", "getDisplayName", "forwardMessage", "setTimeout", "areAllConversationsSelected", "selectAllConversations", "deselectAllConversations", "toggleConversationSelection", "isConversationSelected", "getConversationDisplayImage", "getConversationDisplayName", "simpleNotificationMethods", "onCallMouseMove", "saveNotificationSettings", "setNotificationFilter", "searchMethods", "onInput", "value", "perform", "onKeyPress", "onSearchInput", "onSearchKeyPress", "performSearch", "finalUtilityMethods", "navigateToMessage", "scrollToPinnedMessage", "searchAndReactionMethods", "highlightSearchTerms", "query", "regex", "RegExp", "toggleReactionPicker", "reactToMessage", "toggleMessageOptions", "confirmationMethods", "showPinConfirmation", "cancelPinConfirmation", "showDeleteConfirmation", "cancelDeleteMessage", "cleanup", "clearTimeouts", "typingTimeout", "clearTimeout", "setUserOffline", "stopTypingIndicator", "typingTimer", "stopTyping", "messageForm", "group", "max<PERSON><PERSON><PERSON>", "ngOnInit", "getCurrentUserId", "savedTheme", "localStorage", "getItem", "subscribeToNotifications", "subscribeToUserStatus", "initializeUserStatus", "startActivityTracking", "addEventListener", "bind", "routeSub", "params", "pipe", "getConversation", "handleConversationLoaded", "handleError", "resetLoading", "handleSuccess", "callback", "find", "p", "_id", "conversationMessages", "sort", "a", "b", "timeA", "getTime", "timeB", "markMessagesAsRead", "subscribeToConversationUpdates", "subscribeToNewMessages", "subscribeToTypingIndicators", "updatedConversation", "newMessage", "markMessageAsRead", "subscribeToTypingIndicator", "userId", "unreadMessages", "receiver", "onFileSelected", "file", "files", "validTypes", "reader", "FileReader", "onload", "readAsDataURL", "removeAttachment", "fileInput", "onTyping", "startTyping", "panelName", "closeOthers", "theme", "menu", "search", "currentPanel", "panel", "changeTheme", "setItem", "featureName", "sendMessage", "invalid", "get", "tempMessage", "isPending", "fileType", "startsWith", "attachments", "IMAGE", "FILE", "fileToSend", "reset", "sendSub", "undefined", "TEXT", "updateMessageState", "tempId", "isError", "onScroll", "container", "scrollTop", "oldScrollHeight", "scrollHeight", "firstVisibleMessage", "getFirstVisibleMessage", "loadMoreMessages", "requestAnimationFrame", "preserveScrollPosition", "messageElement", "findMessageElement", "scrollIntoView", "block", "newScrollHeight", "scrollDiff", "messageElements", "querySelectorAll", "element", "rect", "getBoundingClientRect", "top", "bottom", "clientHeight", "getAttribute", "querySelector", "oldMessages", "existingIds", "newMessages", "slice", "isSameTimestamp", "timestamp1", "timestamp2", "time1", "time2", "abs", "force", "isScrolledToBottom", "scrollTo", "behavior", "err", "openImageFullscreen", "imageUrl", "open", "ngAfterViewChecked", "goBackToConversations", "navigate", "insert<PERSON><PERSON><PERSON>", "control", "currentValue", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputElement", "focus", "notificationSub", "subscribeToNewNotifications", "unshift", "play", "notificationsListSub", "notifications$", "notificationCountSub", "notificationCount$", "callSub", "incomingCall$", "activeCallSub", "localStreamSub", "localStream$", "stream", "srcObject", "remoteStreamSub", "remoteStream$", "refresh", "loadSub", "getNotifications", "statusSub", "handleUserStatusUpdate", "set", "setOnlineSub", "setUserOnline", "events", "onUserActivity", "updateUserStatus", "previousStatus", "updateObservable", "updateSub", "statusText", "loadOnlineUsers", "usersSub", "getAllUsers", "users", "togglePinMessage", "ngOnDestroy", "removeEventListener", "ɵɵdirectiveInject", "i1", "i2", "ActivatedRoute", "i3", "AuthuserService", "i4", "FormBuilder", "i5", "UserStatusService", "Router", "i6", "ToastService", "ChangeDetectorRef", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_2_listener", "ɵɵtemplate", "MessageChatComponent_div_5_Template", "MessageChatComponent_Template_button_click_7_listener", "MessageChatComponent_Template_button_click_9_listener", "MessageChatComponent_Template_button_click_11_listener", "MessageChatComponent_Template_button_click_13_listener", "MessageChatComponent_div_17_Template", "MessageChatComponent_div_18_Template", "MessageChatComponent_div_19_Template", "MessageChatComponent_Template_form_ngSubmit_21_listener", "MessageChatComponent_Template_button_click_29_listener", "MessageChatComponent_div_31_Template", "MessageChatComponent_div_32_Template", "MessageChatComponent_div_33_Template", "ɵɵsanitizeUrl", "ɵɵpureFunction1", "_c3"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ElementRef,\n  AfterViewChecked,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { User } from '@app/models/user.model';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport {\n  Message,\n  Conversation,\n  MessageType,\n  CallType,\n} from 'src/app/models/message.model';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { MessageService } from '@app/services/message.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: 'message-chat.component.html',\n  styleUrls: ['./message-chat.component.css'],\n})\nexport class MessageChatComponent\n  implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, AfterViewChecked\n{\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  messages: Message[] = [];\n  messageForm: FormGroup;\n  conversation: Conversation | null = null;\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  currentUsername: string = 'You';\n  otherParticipant: User | null = null;\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n  isUploading = false;\n  isTyping = false;\n  typingTimeout: any;\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n\n  private readonly MAX_MESSAGES_PER_SIDE = 5;\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private readonly MAX_TOTAL_MESSAGES = 100;\n  private currentPage = 1;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  private subscriptions = new Subscription();\n\n  // Interface\n  selectedTheme: string = 'theme-default';\n\n  // États\n  showThemeSelector = false;\n  showMainMenu = false;\n  showEmojiPicker = false;\n  showSearchBar = false;\n  showPinnedMessages = false;\n  showStatusSelector = false;\n  showNotificationPanel = false;\n  showNotificationSettings = false;\n  showUserStatusPanel = false;\n  showCallHistoryPanel = false;\n  showCallStatsPanel = false;\n  showVoiceMessagesPanel = false;\n\n  // Appels\n  incomingCall: any = null;\n  activeCall: any = null;\n  showCallModal = false;\n  showActiveCallModal = false;\n  isCallMuted = false;\n  isVideoEnabled = true;\n  callDuration = 0;\n  callTimer: any = null;\n\n  // Notifications et messages\n  notifications: any[] = [];\n  unreadNotificationCount: number = 0;\n  selectedNotifications: Set<string> = new Set();\n  showDeleteConfirmModal: boolean = false;\n  editingMessageId: string | null = null;\n  editingContent = '';\n  replyingToMessage: any = null;\n\n  // Recherche\n  searchQuery = '';\n  isSearching = false;\n  searchMode = false;\n  searchResults: any[] = [];\n\n  // Panneaux\n  pinnedMessages: any[] = [];\n  showReactionPicker: { [key: string]: boolean } = {};\n  showDeleteConfirm: { [key: string]: boolean } = {};\n  showPinConfirm: { [key: string]: boolean } = {};\n  isPinning: { [key: string]: boolean } = {};\n  showMessageOptions: { [key: string]: boolean } = {};\n\n  // Variables de transfert\n  showForwardModal = false;\n  forwardingMessage: any = null;\n  selectedConversations: string[] = [];\n  availableConversations: any[] = [];\n  isForwarding = false;\n  isLoadingConversations = false;\n\n  // Constantes optimisées\n  readonly c = {\n    reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n    delays: { away: 300000, typing: 3000, scroll: 100, anim: 300 },\n    error: 'Erreur. Veuillez réessayer.',\n    trackById: (i: number, item: any): string => item?.id || i.toString(),\n    notifications: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    status: {\n      online: {\n        text: 'En ligne',\n        color: 'text-green-500',\n        icon: 'fas fa-circle',\n        label: 'En ligne',\n        description: 'Disponible pour discuter',\n      },\n      offline: {\n        text: 'Hors ligne',\n        color: 'text-gray-500',\n        icon: 'far fa-circle',\n        label: 'Hors ligne',\n        description: 'Invisible pour tous',\n      },\n      away: {\n        text: 'Absent',\n        color: 'text-yellow-500',\n        icon: 'fas fa-clock',\n        label: 'Absent',\n        description: 'Absent temporairement',\n      },\n      busy: {\n        text: 'Occupé',\n        color: 'text-red-500',\n        icon: 'fas fa-minus-circle',\n        label: 'Occupé',\n        description: 'Ne pas déranger',\n      },\n    },\n    themes: [\n      {\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad',\n        hoverColor: '#4f5fad',\n      },\n      {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d',\n        hoverColor: '#ff6b9d',\n      },\n      {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6',\n        hoverColor: '#3d85c6',\n      },\n      {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f',\n        hoverColor: '#6aa84f',\n      },\n    ],\n    calls: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n    // Alias pour compatibilité\n    notificationConfig: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    callStatusColors: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n  };\n\n  // Getter pour compatibilité\n  get availableReactions(): string[] {\n    return this.c.reactions;\n  }\n\n  // Variables de notification\n  notificationFilter = 'all';\n  isLoadingNotifications = false;\n  isMarkingAsRead = false;\n  isDeletingNotifications = false;\n  hasMoreNotifications = false;\n  notificationSounds = true;\n  notificationPreview = true;\n  autoMarkAsRead = true;\n\n  // Variables d'appel\n  isCallMinimized = false;\n  callQuality = 'connecting';\n  showCallControls = false;\n\n  // Variables de statut utilisateur\n  onlineUsers: Map<string, User> = new Map();\n  currentUserStatus: string = 'online';\n  lastActivityTime: Date = new Date();\n  autoAwayTimeout: any = null;\n  isUpdatingStatus = false;\n  callHistory: any[] = [];\n  voiceMessages: any[] = [];\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n  statusFilterType = 'all';\n\n  // Emojis du service\n  get commonEmojis(): string[] {\n    return this.MessageService.getCommonEmojis();\n  }\n\n  // Configuration des boutons d'action de l'en-tête\n  getHeaderActions() {\n    return [\n      {\n        class: 'btn-audio-call',\n        icon: 'fas fa-phone-alt',\n        title: 'Appel audio',\n        onClick: () => this.initiateCall('AUDIO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-video-call',\n        icon: 'fas fa-video',\n        title: 'Appel vidéo',\n        onClick: () => this.initiateCall('VIDEO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-search',\n        icon: 'fas fa-search',\n        title: 'Rechercher',\n        onClick: () => this.toggleSearchBar(),\n        isActive: this.showSearchBar,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-pinned relative',\n        icon: 'fas fa-thumbtack',\n        title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n        onClick: () => this.togglePinnedMessages(),\n        isActive: this.showPinnedMessages,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.getPinnedMessagesCount() > 0\n            ? {\n                count: this.getPinnedMessagesCount(),\n                class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n                animate: false,\n              }\n            : null,\n      },\n      {\n        class: 'btn-notifications relative',\n        icon: 'fas fa-bell',\n        title: 'Notifications',\n        onClick: () => this.toggleNotificationPanel(),\n        isActive: this.showNotificationPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.unreadNotificationCount > 0\n            ? {\n                count: this.unreadNotificationCount,\n                class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n                animate: true,\n              }\n            : null,\n      },\n      {\n        class: 'btn-history relative',\n        icon: 'fas fa-history',\n        title: 'Historique des appels',\n        onClick: () => this.toggleCallHistoryPanel(),\n        isActive: this.showCallHistoryPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-stats relative',\n        icon: 'fas fa-chart-bar',\n        title: \"Statistiques d'appels\",\n        onClick: () => this.toggleCallStatsPanel(),\n        isActive: this.showCallStatsPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-voice-messages relative',\n        icon: 'fas fa-microphone',\n        title: 'Messages vocaux',\n        onClick: () => this.toggleVoiceMessagesPanel(),\n        isActive: this.showVoiceMessagesPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.voiceMessages.length > 0\n            ? {\n                count: this.voiceMessages.length,\n                class: 'bg-[#4f5fad]',\n                animate: false,\n              }\n            : null,\n      },\n    ];\n  }\n\n  constructor(\n    private MessageService: MessageService,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private fb: FormBuilder,\n    public statusService: UserStatusService,\n    public router: Router,\n    private toastService: ToastService,\n\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]],\n    });\n  }\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n    }\n\n    this.subscribeToNotifications();\n    this.subscribeToUserStatus();\n    this.initializeUserStatus();\n    this.startActivityTracking();\n\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n\n    const routeSub = this.route.params\n      .pipe(\n        filter((params) => params['id']),\n        distinctUntilChanged(),\n        switchMap((params) => {\n          this.loading = true;\n          this.messages = [];\n          this.currentPage = 1;\n          this.hasMoreMessages = true;\n\n          return this.MessageService.getConversation(\n            params['id'],\n            this.MAX_MESSAGES_TO_LOAD,\n            this.currentPage\n          );\n        })\n      )\n      .subscribe({\n        next: (conversation) => {\n          this.handleConversationLoaded(conversation);\n        },\n        error: (error) => {\n          this.handleError('Failed to load conversation', error);\n        },\n      });\n    this.subscriptions.add(routeSub);\n  }\n\n  // Gestion centralisée des erreurs\n  private handleError(\n    message: string,\n    error: any,\n    resetLoading: boolean = true\n  ): void {\n    console.error('MessageChat', message, error);\n    if (resetLoading) {\n      this.loading = false;\n      this.isUploading = false;\n      this.isLoadingMore = false;\n    }\n    this.error = error;\n    this.toastService.showError(message);\n  }\n\n  // Gestion centralisée des succès\n  private handleSuccess(message?: string, callback?: () => void): void {\n    if (message) {\n      this.toastService.showSuccess(message);\n    }\n    if (callback) {\n      callback();\n    }\n  }\n\n  // Services de fichiers optimisés\n  readonly f = {\n    getIcon: (t?: string) => this.MessageService.getFileIcon(t),\n    getType: (t?: string) => this.MessageService.getFileType(t),\n  };\n\n  getFileIcon = this.f.getIcon;\n  getFileType = this.f.getType;\n\n  private handleConversationLoaded(conversation: Conversation): void {\n    this.conversation = conversation;\n\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.otherParticipant =\n        conversation?.participants?.find(\n          (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n        ) || null;\n      this.messages = [];\n    } else {\n      const conversationMessages = [...(conversation?.messages || [])];\n\n      conversationMessages.sort((a, b) => {\n        const timeA =\n          a.timestamp instanceof Date\n            ? a.timestamp.getTime()\n            : new Date(a.timestamp as string).getTime();\n        const timeB =\n          b.timestamp instanceof Date\n            ? b.timestamp.getTime()\n            : new Date(b.timestamp as string).getTime();\n        return timeA - timeB;\n      });\n\n      this.messages = conversationMessages;\n    }\n\n    this.otherParticipant =\n      conversation?.participants?.find(\n        (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n      ) || null;\n\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n\n    this.markMessagesAsRead();\n\n    if (this.conversation?.id) {\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n  }\n\n  private subscribeToConversationUpdates(conversationId: string): void {\n    const sub = this.MessageService.subscribeToConversationUpdates(\n      conversationId\n    ).subscribe({\n      next: (updatedConversation) => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages\n          ? [...updatedConversation.messages]\n          : [];\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to conversation updates lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToNewMessages(conversationId: string): void {\n    const sub = this.MessageService.subscribeToNewMessages(\n      conversationId\n    ).subscribe({\n      next: (newMessage) => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA =\n              a.timestamp instanceof Date\n                ? a.timestamp.getTime()\n                : new Date(a.timestamp as string).getTime();\n            const timeB =\n              b.timestamp instanceof Date\n                ? b.timestamp.getTime()\n                : new Date(b.timestamp as string).getTime();\n            return timeA - timeB;\n          });\n\n          setTimeout(() => this.scrollToBottom(), 100);\n\n          if (\n            newMessage.sender?.id !== this.currentUserId &&\n            newMessage.sender?._id !== this.currentUserId\n          ) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to new messages lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToTypingIndicators(conversationId: string): void {\n    const sub = this.MessageService.subscribeToTypingIndicator(\n      conversationId\n    ).subscribe({\n      next: (event) => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private markMessagesAsRead(): void {\n    const unreadMessages = this.messages.filter(\n      (msg) =>\n        !msg.isRead &&\n        (msg.receiver?.id === this.currentUserId ||\n          msg.receiver?._id === this.currentUserId)\n    );\n\n    unreadMessages.forEach((msg) => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: (error) => {},\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n\n    const validTypes = [\n      'image/jpeg',\n      'image/png',\n      'image/gif',\n      'application/pdf',\n      'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    ];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError(\n        'Invalid file type. Only images, PDFs and Word docs are allowed'\n      );\n      return;\n    }\n\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n\n  removeAttachment(): void {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n\n  private typingTimer: any;\n  private isCurrentlyTyping = false;\n  private readonly TYPING_DELAY = 500;\n  private readonly TYPING_TIMEOUT = 3000;\n\n  // Frappe\n  onTyping(): void {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n\n    const conversationId = this.conversation.id;\n    clearTimeout(this.typingTimer);\n\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {},\n        error: (error) => {},\n      });\n    }\n\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {},\n          error: (error) => {},\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n\n  // Panneaux\n  private togglePanel(panelName: string, closeOthers: boolean = true): void {\n    const panels = {\n      theme: 'showThemeSelector',\n      menu: 'showMainMenu',\n      emoji: 'showEmojiPicker',\n      notification: 'showNotificationPanel',\n      search: 'showSearchBar',\n      status: 'showStatusSelector',\n    };\n\n    const currentPanel = panels[panelName as keyof typeof panels];\n    if (currentPanel) {\n      (this as any)[currentPanel] = !(this as any)[currentPanel];\n\n      if (closeOthers && (this as any)[currentPanel]) {\n        Object.values(panels).forEach((panel) => {\n          if (panel !== currentPanel) {\n            (this as any)[panel] = false;\n          }\n        });\n      }\n    }\n  }\n\n  // Méthodes de basculement principales consolidées\n  readonly mainToggleMethods = {\n    themeSelector: () => this.togglePanel('theme'),\n    mainMenu: () => this.togglePanel('menu'),\n    emojiPicker: () => this.togglePanel('emoji'),\n  };\n\n  toggleThemeSelector = this.mainToggleMethods.themeSelector;\n  toggleMainMenu = this.mainToggleMethods.mainMenu;\n  toggleEmojiPicker = this.mainToggleMethods.emojiPicker;\n\n  changeTheme(theme: string): void {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    localStorage.setItem('chat-theme', theme);\n  }\n\n  // Méthodes toggle consolidées\n  private readonly toggleMethods = {\n    pinnedMessages: () => (this.showPinnedMessages = !this.showPinnedMessages),\n    searchBar: () => {\n      this.togglePanel('search');\n      if (!this.showSearchBar) this.clearSearch();\n    },\n    statusSelector: () => this.togglePanel('status'),\n    notificationSettings: () =>\n      (this.showNotificationSettings = !this.showNotificationSettings),\n    userStatusPanel: () =>\n      (this.showUserStatusPanel = !this.showUserStatusPanel),\n    callMinimize: () => (this.isCallMinimized = !this.isCallMinimized),\n    callHistoryPanel: () =>\n      (this.showCallHistoryPanel = !this.showCallHistoryPanel),\n    callStatsPanel: () => (this.showCallStatsPanel = !this.showCallStatsPanel),\n    voiceMessagesPanel: () =>\n      (this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel),\n  };\n\n  togglePinnedMessages = this.toggleMethods.pinnedMessages;\n  toggleSearchBar = this.toggleMethods.searchBar;\n  toggleStatusSelector = this.toggleMethods.statusSelector;\n  toggleNotificationSettings = this.toggleMethods.notificationSettings;\n  toggleUserStatusPanel = this.toggleMethods.userStatusPanel;\n  toggleCallMinimize = this.toggleMethods.callMinimize;\n  toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;\n  toggleCallStatsPanel = this.toggleMethods.callStatsPanel;\n  toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;\n\n  // Conversation - méthode utilitaire\n  private showDevelopmentFeature(featureName: string): void {\n    this.showMainMenu = false;\n    this.toastService.showInfo(`${featureName} en cours de développement`);\n  }\n\n  readonly conversationMethods = {\n    showInfo: () => this.showDevelopmentFeature('Informations de conversation'),\n    showSettings: () =>\n      this.showDevelopmentFeature('Paramètres de conversation'),\n    clear: (): void => {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à vider');\n        return;\n      }\n\n      if (\n        confirm(\n          'Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.'\n        )\n      ) {\n        this.messages = [];\n        this.showMainMenu = false;\n        this.toastService.showSuccess('Conversation vidée avec succès');\n      }\n    },\n    export: (): void => {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à exporter');\n        return;\n      }\n\n      const conversationName = this.conversation.isGroup\n        ? this.conversation.groupName || 'Groupe sans nom'\n        : this.otherParticipant?.username || 'Conversation privée';\n\n      const exportData = {\n        conversation: {\n          id: this.conversation.id,\n          name: conversationName,\n          isGroup: this.conversation.isGroup,\n          participants: this.conversation.participants,\n          createdAt: this.conversation.createdAt,\n        },\n        messages: this.messages.map((msg) => ({\n          id: msg.id,\n          content: msg.content,\n          sender: msg.sender,\n          timestamp: msg.timestamp,\n          type: msg.type,\n        })),\n        exportedAt: new Date().toISOString(),\n        exportedBy: this.currentUserId,\n      };\n\n      const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n        type: 'application/json',\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n\n      const safeFileName = conversationName\n        .replace(/[^a-z0-9]/gi, '_')\n        .toLowerCase();\n      const dateStr = new Date().toISOString().split('T')[0];\n      link.download = `conversation-${safeFileName}-${dateStr}.json`;\n\n      link.click();\n      window.URL.revokeObjectURL(url);\n\n      this.showMainMenu = false;\n      this.toastService.showSuccess('Conversation exportée avec succès');\n    },\n  };\n\n  // Template methods\n  toggleConversationInfo = this.conversationMethods.showInfo;\n  toggleConversationSettings = this.conversationMethods.showSettings;\n  clearConversation = this.conversationMethods.clear;\n  exportConversation = this.conversationMethods.export;\n\n  sendMessage(): void {\n    if (\n      (this.messageForm.invalid && !this.selectedFile) ||\n      !this.currentUserId ||\n      !this.otherParticipant?.id\n    ) {\n      return;\n    }\n\n    this.cleanup.stopTypingIndicator();\n\n    const content = this.messageForm.get('content')?.value;\n\n    const tempMessage: Message = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername,\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient',\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true,\n    };\n\n    if (this.selectedFile) {\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n\n        if (this.previewUrl) {\n          tempMessage.attachments = [\n            {\n              id: 'temp-attachment',\n              url: this.previewUrl ? this.previewUrl.toString() : '',\n              type: MessageType.IMAGE,\n              name: this.selectedFile.name,\n              size: this.selectedFile.size,\n            },\n          ];\n        }\n      }\n\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n\n    this.messages = [...this.messages, tempMessage];\n\n    const fileToSend = this.selectedFile;\n    this.messageForm.reset();\n    this.removeAttachment();\n\n    setTimeout(() => this.scrollToBottom(true), 50);\n\n    this.isUploading = true;\n\n    const sendSub = this.MessageService.sendMessage(\n      this.otherParticipant.id,\n      content,\n      fileToSend || undefined,\n      MessageType.TEXT,\n      this.conversation?.id\n    ).subscribe({\n      next: (message) => {\n        this.updateMessageState(tempMessage.id!, message);\n        this.isUploading = false;\n      },\n      error: (error) => {\n        this.updateMessageState(tempMessage.id!, null, true);\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      },\n    });\n\n    this.subscriptions.add(sendSub);\n  }\n\n  // Méthode consolidée pour mettre à jour l'état des messages\n  private updateMessageState(\n    tempId: string,\n    newMessage?: Message | null,\n    isError: boolean = false\n  ): void {\n    this.messages = this.messages.map((msg) => {\n      if (msg.id === tempId) {\n        if (newMessage) {\n          return newMessage;\n        } else if (isError) {\n          return {\n            ...msg,\n            isPending: false,\n            isError: true,\n          };\n        }\n      }\n      return msg;\n    });\n  }\n\n  // Service - méthodes optimisées\n  readonly s = {\n    formatTime: (t: string | Date | undefined) =>\n      this.MessageService.formatMessageTime(t),\n    formatActive: (t: string | Date | undefined) =>\n      this.MessageService.formatLastActive(t),\n    formatDate: (t: string | Date | undefined) =>\n      this.MessageService.formatMessageDate(t),\n    showDateHeader: (i: number) =>\n      this.MessageService.shouldShowDateHeader(this.messages, i),\n    getType: (m: Message | null | undefined) =>\n      this.MessageService.getMessageType(m),\n    hasImage: (m: Message | null | undefined) =>\n      this.MessageService.hasImage(m),\n    isVoice: (m: Message | null | undefined) =>\n      this.MessageService.isVoiceMessage(m),\n    getVoiceUrl: (m: Message | null | undefined) =>\n      this.MessageService.getVoiceMessageUrl(m),\n    getVoiceDuration: (m: Message | null | undefined) =>\n      this.MessageService.getVoiceMessageDuration(m),\n    getVoiceHeight: (i: number) => this.MessageService.getVoiceBarHeight(i),\n    formatVoice: (s: number) => this.MessageService.formatVoiceDuration(s),\n    getImageUrl: (m: Message | null | undefined) =>\n      this.MessageService.getImageUrl(m),\n    getTypeClass: (m: Message | null | undefined) =>\n      this.MessageService.getMessageTypeClass(m, this.currentUserId),\n  };\n\n  // Méthodes exposées optimisées\n  formatMessageTime = this.s.formatTime;\n  formatLastActive = this.s.formatActive;\n  formatMessageDate = this.s.formatDate;\n  shouldShowDateHeader = this.s.showDateHeader;\n  getMessageType = this.s.getType;\n  hasImage = this.s.hasImage;\n  isVoiceMessage = this.s.isVoice;\n  getVoiceMessageUrl = this.s.getVoiceUrl;\n  getVoiceMessageDuration = this.s.getVoiceDuration;\n  getVoiceBarHeight = this.s.getVoiceHeight;\n  formatVoiceDuration = this.s.formatVoice;\n  getImageUrl = this.s.getImageUrl;\n  getMessageTypeClass = this.s.getTypeClass;\n\n  // Défilement\n  onScroll(event: any): void {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n\n    if (\n      scrollTop < 50 &&\n      !this.isLoadingMore &&\n      this.conversation?.id &&\n      this.hasMoreMessages\n    ) {\n      this.showLoadingIndicator();\n\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n\n      this.isLoadingMore = true;\n\n      this.loadMoreMessages();\n\n      // Maintenir la position de défilement\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(\n              firstVisibleMessage.id\n            );\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({ block: 'center' });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n\n  // Méthode pour trouver le premier message visible dans la vue\n  private getFirstVisibleMessage(): Message | null {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length)\n      return null;\n\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find((m) => m.id === messageId) || null;\n      }\n    }\n\n    return null;\n  }\n\n  // Méthode pour trouver un élément de message par ID\n  private findMessageElement(\n    messageId: string | undefined\n  ): HTMLElement | null {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(\n      `[data-message-id=\"${messageId}\"]`\n    );\n  }\n\n  // Indicateurs de chargement consolidés\n  readonly loadingIndicatorMethods = {\n    show: () => {\n      if (!document.getElementById('message-loading-indicator')) {\n        const indicator = document.createElement('div');\n        indicator.id = 'message-loading-indicator';\n        indicator.className = 'text-center py-2 text-gray-500 text-sm';\n        indicator.innerHTML =\n          '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n        this.messagesContainer?.nativeElement?.prepend(indicator);\n      }\n    },\n    hide: () => {\n      const indicator = document.getElementById('message-loading-indicator');\n      indicator?.parentNode?.removeChild(indicator);\n    },\n  };\n\n  private showLoadingIndicator = this.loadingIndicatorMethods.show;\n  private hideLoadingIndicator = this.loadingIndicatorMethods.hide;\n\n  // Charger plus de messages\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages)\n      return;\n\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n\n    // Augmenter la page\n    this.currentPage++;\n\n    // Charger plus de messages\n    this.MessageService.getConversation(\n      this.conversation.id,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        if (\n          conversation &&\n          conversation.messages &&\n          conversation.messages.length > 0\n        ) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n\n          // Créer un Set des IDs existants\n          const existingIds = new Set(oldMessages.map((msg) => msg.id));\n\n          // Filtrer et trier les nouveaux messages\n          const newMessages = conversation.messages\n            .filter((msg) => !existingIds.has(msg.id))\n            .sort((a, b) => {\n              const timeA = new Date(a.timestamp as string).getTime();\n              const timeB = new Date(b.timestamp as string).getTime();\n              return timeA - timeB;\n            });\n\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages =\n              newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: (error) => {\n        console.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      },\n    });\n  }\n\n  // Méthode utilitaire pour comparer les timestamps\n  private isSameTimestamp(\n    timestamp1: string | Date | undefined,\n    timestamp2: string | Date | undefined\n  ): boolean {\n    if (!timestamp1 || !timestamp2) return false;\n\n    try {\n      const time1 =\n        timestamp1 instanceof Date\n          ? timestamp1.getTime()\n          : new Date(timestamp1 as string).getTime();\n      const time2 =\n        timestamp2 instanceof Date\n          ? timestamp2.getTime()\n          : new Date(timestamp2 as string).getTime();\n      return Math.abs(time1 - time2) < 1000;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  scrollToBottom(force: boolean = false): void {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom =\n          container.scrollHeight - container.clientHeight <=\n          container.scrollTop + 150;\n\n        if (force || isScrolledToBottom) {\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth',\n          });\n        }\n      });\n    } catch (err) {\n      console.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n\n  // Méthodes d'enregistrement vocal consolidées\n  readonly voiceRecordingMethods = {\n    toggle: () => {\n      this.isRecordingVoice = !this.isRecordingVoice;\n      if (!this.isRecordingVoice) this.voiceRecordingDuration = 0;\n    },\n    complete: (audioBlob: Blob) => {\n      if (!this.conversation?.id && !this.otherParticipant?.id) {\n        this.toastService.showError('No conversation or recipient selected');\n        this.isRecordingVoice = false;\n        return;\n      }\n      const receiverId = this.otherParticipant?.id || '';\n      this.MessageService.sendVoiceMessage(\n        receiverId,\n        audioBlob,\n        this.conversation?.id,\n        this.voiceRecordingDuration\n      ).subscribe({\n        next: (message) => {\n          this.isRecordingVoice = false;\n          this.voiceRecordingDuration = 0;\n          this.scrollToBottom(true);\n        },\n        error: (error) => {\n          this.toastService.showError('Failed to send voice message');\n          this.isRecordingVoice = false;\n        },\n      });\n    },\n    cancel: () => {\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n    },\n  };\n\n  toggleVoiceRecording = this.voiceRecordingMethods.toggle;\n  onVoiceRecordingComplete = this.voiceRecordingMethods.complete;\n  onVoiceRecordingCancelled = this.voiceRecordingMethods.cancel;\n\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl: string): void {\n    window.open(imageUrl, '_blank');\n  }\n\n  /**\n   * Détecte les changements après chaque vérification de la vue - Optimisé\n   */\n  ngAfterViewChecked(): void {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n\n    // Détection des changements optimisée - Suppression de la vérification inutile\n    // La détection automatique d'Angular gère déjà les messages vocaux\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji: string): void {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector(\n          '.whatsapp-input-field'\n        ) as HTMLInputElement;\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  private subscribeToNotifications(): void {\n    const notificationSub =\n      this.MessageService.subscribeToNewNotifications().subscribe({\n        next: (notification) => {\n          this.notifications.unshift(notification);\n          this.updateNotificationCount();\n\n          this.MessageService.play('notification');\n\n          if (\n            notification.type === 'NEW_MESSAGE' &&\n            notification.conversationId === this.conversation?.id\n          ) {\n            if (notification.id) {\n              this.MessageService.markAsRead([notification.id]).subscribe();\n            }\n          }\n        },\n        error: (error) => {},\n      });\n    this.subscriptions.add(notificationSub);\n\n    const notificationsListSub = this.MessageService.notifications$.subscribe({\n      next: (notifications) => {\n        this.notifications = notifications;\n        this.updateNotificationCount();\n      },\n      error: (error) => {},\n    });\n    this.subscriptions.add(notificationsListSub);\n\n    const notificationCountSub =\n      this.MessageService.notificationCount$.subscribe({\n        next: (count) => {\n          this.unreadNotificationCount = count;\n        },\n      });\n    this.subscriptions.add(notificationCountSub);\n\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: (call) => {\n        if (call) {\n          this.incomingCall = call;\n          this.showCallModal = true;\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      },\n    });\n    this.subscriptions.add(callSub);\n\n    const activeCallSub = this.MessageService.activeCall$.subscribe({\n      next: (call) => {\n        this.activeCall = call;\n        if (call) {\n          this.showActiveCallModal = true;\n          this.startCallTimerMethod();\n        } else {\n          this.showActiveCallModal = false;\n          this.stopCallTimerMethod();\n          this.resetCallStateMethod();\n        }\n      },\n    });\n    this.subscriptions.add(activeCallSub);\n\n    // S'abonner aux flux vidéo locaux\n    const localStreamSub = this.MessageService.localStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.localVideoElement) {\n          this.localVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(localStreamSub);\n\n    // S'abonner aux flux vidéo distants\n    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.remoteVideoElement) {\n          this.remoteVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(remoteStreamSub);\n  }\n\n  // Méthodes d'appel consolidées\n  readonly callMethods = {\n    initiate: (type: 'AUDIO' | 'VIDEO') => {\n      if (!this.otherParticipant?.id) return;\n      this.MessageService.initiateCall(\n        this.otherParticipant.id,\n        type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO,\n        this.conversation?.id\n      ).subscribe({\n        next: (call) => {},\n        error: () => this.toastService.showError(this.c.error),\n      });\n    },\n    accept: () => {\n      if (!this.incomingCall) return;\n      this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n        next: (call) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n          this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n          this.callQuality = 'connecting';\n          this.toastService.showSuccess('Appel connecté');\n        },\n        error: () => {\n          this.toastService.showError(this.c.error);\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n      });\n    },\n    reject: () => {\n      if (!this.incomingCall) return;\n      this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n        next: (call) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n        error: (error) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n      });\n    },\n    end: () => {\n      const sub = this.MessageService.activeCall$.subscribe((call) => {\n        if (call) {\n          this.MessageService.endCall(call.id).subscribe({\n            next: (call) => {},\n            error: (error) => {},\n          });\n        }\n      });\n      sub.unsubscribe();\n    },\n  };\n\n  initiateCall = this.callMethods.initiate;\n  acceptCall = this.callMethods.accept;\n  rejectCall = this.callMethods.reject;\n  endCall = this.callMethods.end;\n\n  // Méthodes de contrôle d'appel consolidées\n  readonly callControlMethods = {\n    toggleMute: () => {\n      this.isCallMuted = !this.isCallMuted;\n      this.callControlMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé'\n      );\n    },\n    toggleVideo: () => {\n      this.isVideoEnabled = !this.isVideoEnabled;\n      this.callControlMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n      );\n    },\n    updateMedia: () => {\n      this.MessageService.toggleMedia(\n        this.activeCall?.id,\n        !this.isCallMuted,\n        this.isVideoEnabled\n      ).subscribe({\n        next: () => {},\n        error: (error) =>\n          console.error('Erreur lors de la mise à jour des médias:', error),\n      });\n    },\n  };\n\n  toggleCallMute = this.callControlMethods.toggleMute;\n  toggleCallVideo = this.callControlMethods.toggleVideo;\n  private updateCallMedia = this.callControlMethods.updateMedia;\n\n  // Méthodes de timer consolidées\n  readonly timerMethods = {\n    startCallTimer: () => {\n      this.callDuration = 0;\n      this.callTimer = setInterval(() => {\n        this.callDuration++;\n        if (this.callDuration === 3 && this.callQuality === 'connecting') {\n          this.callQuality = 'excellent';\n        }\n      }, 1000);\n    },\n    stopCallTimer: () => {\n      if (this.callTimer) {\n        clearInterval(this.callTimer);\n        this.callTimer = null;\n      }\n    },\n    resetCallState: () => (this.callDuration = 0),\n  };\n\n  private startCallTimerMethod = this.timerMethods.startCallTimer;\n  private stopCallTimerMethod = this.timerMethods.stopCallTimer;\n  private resetCallStateMethod = this.timerMethods.resetCallState;\n\n  // Notifications\n\n  // Méthode de basculement de notification consolidée\n  readonly notificationToggleMethod = {\n    togglePanel: () => {\n      this.togglePanel('notification');\n      if (this.showNotificationPanel) {\n        this.loadNotifications();\n      }\n    },\n  };\n\n  toggleNotificationPanel = this.notificationToggleMethod.togglePanel;\n\n  /**\n   * Charge les notifications\n   */\n  loadNotifications(refresh: boolean = false): void {\n    const loadSub = this.MessageService.getNotifications(\n      refresh,\n      1,\n      20\n    ).subscribe({\n      next: (notifications) => {\n        if (refresh) {\n          this.notifications = notifications;\n        } else {\n          this.notifications = [...this.notifications, ...notifications];\n        }\n\n        this.updateNotificationCount();\n      },\n      error: (error) => {\n        this.toastService.showError(\n          'Erreur lors du chargement des notifications'\n        );\n      },\n    });\n\n    this.subscriptions.add(loadSub);\n  }\n\n  // Méthodes de notification consolidées\n  readonly notificationMethods = {\n    loadMore: () => this.loadNotifications(),\n    updateCount: () =>\n      (this.unreadNotificationCount = this.notifications.filter(\n        (n) => !n.isRead\n      ).length),\n    getFiltered: () => this.notifications,\n    toggleSelection: (notificationId: string) => {\n      if (this.selectedNotifications.has(notificationId)) {\n        this.selectedNotifications.delete(notificationId);\n      } else {\n        this.selectedNotifications.add(notificationId);\n      }\n    },\n    toggleSelectAll: () => {\n      const filteredNotifications = this.notificationMethods.getFiltered();\n      const allSelected = filteredNotifications.every((n) =>\n        this.selectedNotifications.has(n.id)\n      );\n      if (allSelected) {\n        filteredNotifications.forEach((n) =>\n          this.selectedNotifications.delete(n.id)\n        );\n      } else {\n        filteredNotifications.forEach((n) =>\n          this.selectedNotifications.add(n.id)\n        );\n      }\n    },\n    areAllSelected: () => {\n      const filteredNotifications = this.notificationMethods.getFiltered();\n      return (\n        filteredNotifications.length > 0 &&\n        filteredNotifications.every((n) => this.selectedNotifications.has(n.id))\n      );\n    },\n  };\n\n  loadMoreNotifications = this.notificationMethods.loadMore;\n  private updateNotificationCount = this.notificationMethods.updateCount;\n  getFilteredNotifications = this.notificationMethods.getFiltered;\n  toggleNotificationSelection = this.notificationMethods.toggleSelection;\n  toggleSelectAllNotifications = this.notificationMethods.toggleSelectAll;\n  areAllNotificationsSelected = this.notificationMethods.areAllSelected;\n\n  // Méthodes de marquage consolidées\n  readonly markingMethods = {\n    markSelected: () => {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      this.markingMethods.markAsRead(selectedIds, () => {\n        this.selectedNotifications.clear();\n        this.toastService.showSuccess(\n          `${selectedIds.length} notification(s) marquée(s) comme lue(s)`\n        );\n      });\n    },\n    markAll: () => {\n      const unreadNotifications = this.notifications.filter((n) => !n.isRead);\n      if (unreadNotifications.length === 0) {\n        this.toastService.showInfo('Aucune notification non lue');\n        return;\n      }\n      const unreadIds = unreadNotifications.map((n) => n.id);\n      this.markingMethods.markAsRead(unreadIds, () => {\n        this.toastService.showSuccess(\n          'Toutes les notifications ont été marquées comme lues'\n        );\n      });\n    },\n    markAsRead: (ids: string[], onSuccess: () => void) => {\n      const markSub = this.MessageService.markAsRead(ids).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.map((n) =>\n            ids.includes(n.id) ? { ...n, isRead: true, readAt: new Date() } : n\n          );\n          this.updateNotificationCount();\n          onSuccess();\n        },\n        error: (error) => {\n          this.toastService.showError(\n            'Erreur lors du marquage des notifications'\n          );\n        },\n      });\n      this.subscriptions.add(markSub);\n    },\n  };\n\n  markSelectedAsRead = this.markingMethods.markSelected;\n  markAllAsRead = this.markingMethods.markAll;\n\n  // Méthodes de suppression de notifications consolidées\n  readonly notificationDeleteMethods = {\n    showDeleteSelectedConfirmation: () => {\n      if (this.selectedNotifications.size === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      this.showDeleteConfirmModal = true;\n    },\n    deleteSelected: () => {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) return;\n      this.isDeletingNotifications = true;\n      this.showDeleteConfirmModal = false;\n      const deleteSub = this.MessageService.deleteMultipleNotifications(\n        selectedIds\n      ).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.filter(\n            (n) => !selectedIds.includes(n.id)\n          );\n          this.selectedNotifications.clear();\n          this.updateNotificationCount();\n          this.isDeletingNotifications = false;\n          this.toastService.showSuccess(\n            `${result.count} notification(s) supprimée(s)`\n          );\n        },\n        error: (error) => {\n          this.isDeletingNotifications = false;\n          this.toastService.showError(\n            'Erreur lors de la suppression des notifications'\n          );\n        },\n      });\n      this.subscriptions.add(deleteSub);\n    },\n    deleteOne: (notificationId: string) => {\n      const deleteSub = this.MessageService.deleteNotification(\n        notificationId\n      ).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.filter(\n            (n) => n.id !== notificationId\n          );\n          this.selectedNotifications.delete(notificationId);\n          this.updateNotificationCount();\n          this.toastService.showSuccess('Notification supprimée');\n        },\n        error: (error) => {\n          this.toastService.showError(\n            'Erreur lors de la suppression de la notification'\n          );\n        },\n      });\n      this.subscriptions.add(deleteSub);\n    },\n    deleteAll: () => {\n      if (this.notifications.length === 0) return;\n      if (\n        !confirm(\n          'Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.'\n        )\n      ) {\n        return;\n      }\n      this.isDeletingNotifications = true;\n      const deleteAllSub =\n        this.MessageService.deleteAllNotifications().subscribe({\n          next: (result) => {\n            this.notifications = [];\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(\n              `${result.count} notifications supprimées avec succès`\n            );\n          },\n          error: (error) => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError(\n              'Erreur lors de la suppression de toutes les notifications'\n            );\n          },\n        });\n      this.subscriptions.add(deleteAllSub);\n    },\n    cancel: () => (this.showDeleteConfirmModal = false),\n  };\n\n  showDeleteSelectedConfirmation =\n    this.notificationDeleteMethods.showDeleteSelectedConfirmation;\n  deleteSelectedNotifications = this.notificationDeleteMethods.deleteSelected;\n  deleteNotification = this.notificationDeleteMethods.deleteOne;\n  deleteAllNotifications = this.notificationDeleteMethods.deleteAll;\n  cancelDeleteNotifications = this.notificationDeleteMethods.cancel;\n\n  // Méthodes utilitaires de notification consolidées\n  readonly notificationUtilMethods = {\n    formatDate: (date: string | Date | undefined) =>\n      this.MessageService.formatLastActive(date),\n    getIcon: (type: string) =>\n      this.c.notifications[type as keyof typeof this.c.notifications]?.icon ||\n      'fas fa-bell',\n    getColor: (type: string) =>\n      this.c.notifications[type as keyof typeof this.c.notifications]?.color ||\n      'text-cyan-500',\n    trackById: (index: number, notification: any) =>\n      this.c.trackById(0, notification),\n  };\n\n  formatNotificationDate = this.notificationUtilMethods.formatDate;\n  getNotificationIcon = this.notificationUtilMethods.getIcon;\n  getNotificationColor = this.notificationUtilMethods.getColor;\n  trackByNotificationId = this.notificationUtilMethods.trackById;\n\n  /**\n   * S'abonne au statut utilisateur en temps réel\n   */\n  private subscribeToUserStatus(): void {\n    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n      next: (user: User) => {\n        this.handleUserStatusUpdate(user);\n      },\n      error: (error) => {\n        // Error handled silently\n      },\n    });\n\n    this.subscriptions.add(statusSub);\n  }\n\n  /**\n   * Gère la mise à jour du statut d'un utilisateur\n   */\n  private handleUserStatusUpdate(user: User): void {\n    if (!user.id) return;\n\n    if (user.isOnline) {\n      this.onlineUsers.set(user.id, user);\n    } else {\n      this.onlineUsers.delete(user.id);\n    }\n\n    if (this.otherParticipant && this.otherParticipant.id === user.id) {\n      this.otherParticipant = { ...this.otherParticipant, ...user };\n    }\n  }\n\n  /**\n   * Initialise le statut de l'utilisateur actuel\n   */\n  private initializeUserStatus(): void {\n    if (!this.currentUserId) return;\n\n    const setOnlineSub = this.MessageService.setUserOnline(\n      this.currentUserId\n    ).subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = 'online';\n        this.lastActivityTime = new Date();\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          \"Erreur lors de l'initialisation du statut\",\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(setOnlineSub);\n  }\n\n  /**\n   * Démarre le suivi d'activité automatique\n   */\n  private startActivityTracking(): void {\n    const events = [\n      'mousedown',\n      'mousemove',\n      'keypress',\n      'scroll',\n      'touchstart',\n      'click',\n    ];\n\n    events.forEach((event) => {\n      document.addEventListener(event, this.onUserActivity.bind(this), true);\n    });\n  }\n\n  /**\n   * Gère l'activité de l'utilisateur\n   */\n  private onUserActivity(): void {\n    this.lastActivityTime = new Date();\n\n    // Réinitialiser le timer\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n\n    // Remettre en ligne si absent\n    if (\n      this.currentUserStatus === 'away' ||\n      this.currentUserStatus === 'offline'\n    ) {\n      this.updateUserStatus('online');\n    }\n\n    // Programmer la mise en absence\n    this.autoAwayTimeout = setTimeout(() => {\n      if (this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      }\n    }, this.c.delays.away);\n  }\n\n  /**\n   * Met à jour le statut de l'utilisateur\n   */\n  updateUserStatus(status: string): void {\n    if (!this.currentUserId) return;\n\n    const previousStatus = this.currentUserStatus;\n\n    let updateObservable;\n    if (status === 'online') {\n      updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n    } else {\n      updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n    }\n\n    const updateSub = updateObservable.subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = status;\n\n        if (status !== previousStatus) {\n          const statusText = this.getStatusText(status);\n          this.toastService.showInfo(`Statut : ${statusText}`);\n        }\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          'Erreur lors de la mise à jour du statut',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(updateSub);\n  }\n\n  /**\n   * Charge la liste des utilisateurs en ligne\n   */\n  loadOnlineUsers(): void {\n    const usersSub = this.MessageService.getAllUsers(\n      false,\n      undefined,\n      1,\n      50,\n      'username',\n      'asc',\n      true\n    ).subscribe({\n      next: (users: User[]) => {\n        users.forEach((user) => {\n          if (user.isOnline && user.id) {\n            this.onlineUsers.set(user.id, user);\n          }\n        });\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          'Erreur lors du chargement des utilisateurs en ligne',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(usersSub);\n  }\n\n  // Méthodes de gestion des panneaux consolidées\n  readonly panelMethods = {\n    getActivePanels: () => {\n      const panels = [];\n      if (this.showUserStatusPanel)\n        panels.push({\n          key: 'userStatus',\n          title: 'Utilisateurs',\n          icon: 'fas fa-users',\n          closeAction: () => (this.showUserStatusPanel = false),\n        });\n      if (this.showCallHistoryPanel)\n        panels.push({\n          key: 'callHistory',\n          title: 'Historique des appels',\n          icon: 'fas fa-history',\n          closeAction: () => (this.showCallHistoryPanel = false),\n        });\n      if (this.showCallStatsPanel)\n        panels.push({\n          key: 'callStats',\n          title: \"Statistiques d'appels\",\n          icon: 'fas fa-chart-bar',\n          closeAction: () => (this.showCallStatsPanel = false),\n        });\n      if (this.showVoiceMessagesPanel)\n        panels.push({\n          key: 'voiceMessages',\n          title: 'Messages vocaux',\n          icon: 'fas fa-microphone',\n          closeAction: () => (this.showVoiceMessagesPanel = false),\n        });\n      return panels;\n    },\n    getStatusOptions: () =>\n      Object.entries(this.c.status).map(([key, config]) => ({\n        key,\n        ...config,\n      })),\n    getThemeOptions: () => this.c.themes,\n  };\n\n  getActivePanels = this.panelMethods.getActivePanels;\n  getStatusOptions = this.panelMethods.getStatusOptions;\n  getThemeOptions = this.panelMethods.getThemeOptions;\n\n  // Méthodes de statut simplifiées\n  readonly statusMethods = {\n    getText: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.text || 'Inconnu',\n    getColor: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.color ||\n      'text-gray-400',\n    getIcon: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.icon ||\n      'fas fa-question-circle',\n  };\n\n  getStatusText = this.statusMethods.getText;\n  getStatusColor = this.statusMethods.getColor;\n  getStatusIcon = this.statusMethods.getIcon;\n\n  // Méthodes utilitaires consolidées\n  readonly utilityMethods = {\n    formatLastSeen: (lastActive: Date | null) =>\n      lastActive\n        ? this.MessageService.formatLastActive(lastActive)\n        : 'Jamais vu',\n    getOnlineUsersCount: () =>\n      Array.from(this.onlineUsers.values()).filter((user) => user.isOnline)\n        .length,\n    getFilteredUsers: () => Array.from(this.onlineUsers.values()),\n    setStatusFilter: (filter: string) => (this.statusFilterType = filter),\n  };\n\n  formatLastSeen = this.utilityMethods.formatLastSeen;\n  getOnlineUsersCount = this.utilityMethods.getOnlineUsersCount;\n  setStatusFilter = this.utilityMethods.setStatusFilter;\n  getFilteredUsers = this.utilityMethods.getFilteredUsers;\n\n  // Méthodes de réponse et transfert consolidées\n  readonly replyForwardMethods = {\n    startReply: (message: any) => (this.replyingToMessage = message),\n    cancelReply: () => (this.replyingToMessage = null),\n    openForwardModal: (message: any) => {\n      this.forwardingMessage = message;\n      this.showForwardModal = true;\n    },\n    closeForwardModal: () => {\n      this.showForwardModal = false;\n      this.forwardingMessage = null;\n      this.selectedConversations = [];\n    },\n  };\n\n  startReplyToMessage = this.replyForwardMethods.startReply;\n  cancelReply = this.replyForwardMethods.cancelReply;\n  openForwardModal = this.replyForwardMethods.openForwardModal;\n  closeForwardModal = this.replyForwardMethods.closeForwardModal;\n\n  // Messages - méthodes consolidées\n  readonly messageMethods = {\n    getPinIcon: (message: any) =>\n      this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack',\n    getPinDisplayText: (message: any) =>\n      this.isMessagePinned(message) ? 'Désépingler' : 'Épingler',\n    canEditMessage: (message: any) => message.sender?.id === this.currentUserId,\n    isMessagePinned: (message: any) => message.isPinned || false,\n  };\n\n  getPinIcon = this.messageMethods.getPinIcon;\n  getPinDisplayText = this.messageMethods.getPinDisplayText;\n  canEditMessage = this.messageMethods.canEditMessage;\n  isMessagePinned = this.messageMethods.isMessagePinned;\n\n  // Méthodes d'édition consolidées\n  readonly editMethods = {\n    startEditMessage: (message: any) => {\n      this.editingMessageId = message.id;\n      this.editingContent = message.content;\n    },\n    cancelEditMessage: () => {\n      this.editingMessageId = null;\n      this.editingContent = '';\n    },\n    saveEditMessage: (messageId: string) =>\n      this.editMethods.cancelEditMessage(),\n    onEditKeyPress: (event: KeyboardEvent, messageId: string) => {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.editMethods.saveEditMessage(messageId);\n      } else if (event.key === 'Escape') {\n        this.editMethods.cancelEditMessage();\n      }\n    },\n  };\n\n  startEditMessage = this.editMethods.startEditMessage;\n  cancelEditMessage = this.editMethods.cancelEditMessage;\n  saveEditMessage = this.editMethods.saveEditMessage;\n  onEditKeyPress = this.editMethods.onEditKeyPress;\n\n  togglePinMessage(message: any): void {\n    this.isPinning[message.id] = true;\n    setTimeout(() => {\n      this.isPinning[message.id] = false;\n      this.showPinConfirm[message.id] = false;\n    }, 1000);\n  }\n\n  // Utilitaires d'appel consolidées\n  readonly callUtilities = {\n    getCallStatusColor: (status: string) =>\n      this.c.callStatusColors[status as keyof typeof this.c.callStatusColors] ||\n      'text-gray-500',\n    getCallTypeIcon: (type: string) =>\n      type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',\n    formatCallDuration: (duration: number) => {\n      if (!duration) return '00:00';\n      const minutes = Math.floor(duration / 60);\n      const seconds = duration % 60;\n      return `${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    },\n    formatCallDate: (timestamp: string | Date | undefined) =>\n      this.MessageService.formatMessageDate(timestamp),\n  };\n\n  getCallStatusColor = this.callUtilities.getCallStatusColor;\n  getCallTypeIcon = this.callUtilities.getCallTypeIcon;\n  formatCallDuration = this.callUtilities.formatCallDuration;\n  formatCallDate = this.callUtilities.formatCallDate;\n\n  // Méthodes d'événements consolidées\n  readonly eventMethods = {\n    onDocumentClick: (event: Event) => {\n      const target = event.target as HTMLElement;\n      const closeConfigs = [\n        {\n          selectors: ['.theme-selector-menu', '.btn-theme'],\n          property: 'showThemeSelector',\n        },\n        {\n          selectors: ['.emoji-picker', '.btn-emoji'],\n          property: 'showEmojiPicker',\n        },\n      ];\n\n      closeConfigs.forEach((config) => {\n        const isClickOutside = !config.selectors.some((selector) =>\n          target.closest(selector)\n        );\n        if (isClickOutside) {\n          (this as any)[config.property] = false;\n        }\n      });\n    },\n    confirmDeleteMessage: (messageId: string) => {\n      this.showDeleteConfirm[messageId] = false;\n    },\n  };\n\n  onDocumentClick = this.eventMethods.onDocumentClick;\n  confirmDeleteMessage = this.eventMethods.confirmDeleteMessage;\n\n  // Méthodes de réaction consolidées\n  readonly reactionMethods = {\n    getUniqueReactions: (message: any) => message.reactions || [],\n    onReactionClick: (messageId: string, emoji: string) => {\n      // Implémentation des réactions\n    },\n    hasUserReacted: (message: any, emoji: string) => false,\n  };\n\n  getUniqueReactions = this.reactionMethods.getUniqueReactions;\n  onReactionClick = this.reactionMethods.onReactionClick;\n  hasUserReacted = this.reactionMethods.hasUserReacted;\n\n  // Méthodes de conversation consolidées\n  readonly conversationSelectionMethods = {\n    areAllSelected: () =>\n      this.selectedConversations.length === this.availableConversations.length,\n    selectAll: () =>\n      (this.selectedConversations = this.availableConversations.map(\n        (c) => c.id\n      )),\n    deselectAll: () => (this.selectedConversations = []),\n    toggle: (conversationId: string) => {\n      const index = this.selectedConversations.indexOf(conversationId);\n      index > -1\n        ? this.selectedConversations.splice(index, 1)\n        : this.selectedConversations.push(conversationId);\n    },\n    isSelected: (conversationId: string) =>\n      this.selectedConversations.includes(conversationId),\n    getDisplayImage: (conversation: any) =>\n      conversation.image || 'assets/images/default-avatar.png',\n    getDisplayName: (conversation: any) => conversation.name || 'Conversation',\n    forwardMessage: () => {\n      this.isForwarding = true;\n      setTimeout(() => {\n        this.isForwarding = false;\n        this.closeForwardModal();\n      }, 1000);\n    },\n  };\n\n  areAllConversationsSelected =\n    this.conversationSelectionMethods.areAllSelected;\n  selectAllConversations = this.conversationSelectionMethods.selectAll;\n  deselectAllConversations = this.conversationSelectionMethods.deselectAll;\n  toggleConversationSelection = this.conversationSelectionMethods.toggle;\n  isConversationSelected = this.conversationSelectionMethods.isSelected;\n  getConversationDisplayImage =\n    this.conversationSelectionMethods.getDisplayImage;\n  getConversationDisplayName = this.conversationSelectionMethods.getDisplayName;\n  forwardMessage = this.conversationSelectionMethods.forwardMessage;\n\n  // Méthodes de notification simplifiées consolidées\n  readonly simpleNotificationMethods = {\n    onCallMouseMove: () => (this.showCallControls = true),\n    saveNotificationSettings: () => {},\n    setNotificationFilter: (filter: string) =>\n      (this.notificationFilter = filter),\n  };\n\n  onCallMouseMove = this.simpleNotificationMethods.onCallMouseMove;\n  saveNotificationSettings =\n    this.simpleNotificationMethods.saveNotificationSettings;\n  setNotificationFilter = this.simpleNotificationMethods.setNotificationFilter;\n\n  // Méthodes de recherche consolidées\n  readonly searchMethods = {\n    onInput: (event: any) => {\n      this.searchQuery = event.target.value;\n      this.searchQuery.length >= 2\n        ? this.searchMethods.perform()\n        : this.searchMethods.clear();\n    },\n    onKeyPress: (event: KeyboardEvent) => {\n      if (event.key === 'Enter') {\n        this.searchMethods.perform();\n      } else if (event.key === 'Escape') {\n        this.searchMethods.clear();\n      }\n    },\n    perform: () => {\n      this.isSearching = true;\n      this.searchMode = true;\n      setTimeout(() => {\n        this.searchResults = this.messages.filter((m) =>\n          m.content?.toLowerCase().includes(this.searchQuery.toLowerCase())\n        );\n        this.isSearching = false;\n      }, 500);\n    },\n    clear: () => {\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.isSearching = false;\n      this.searchMode = false;\n    },\n  };\n\n  onSearchInput = this.searchMethods.onInput;\n  onSearchKeyPress = this.searchMethods.onKeyPress;\n  performSearch = this.searchMethods.perform;\n  clearSearch = this.searchMethods.clear;\n\n  // Méthodes utilitaires finales consolidées\n  readonly finalUtilityMethods = {\n    navigateToMessage: (messageId: string) => {\n      // Navigation vers un message spécifique\n    },\n    scrollToPinnedMessage: (messageId: string) => {\n      // Défilement vers un message épinglé\n    },\n    getPinnedMessagesCount: () => this.pinnedMessages.length,\n  };\n\n  navigateToMessage = this.finalUtilityMethods.navigateToMessage;\n  scrollToPinnedMessage = this.finalUtilityMethods.scrollToPinnedMessage;\n  getPinnedMessagesCount = this.finalUtilityMethods.getPinnedMessagesCount;\n\n  // Méthodes de recherche et réaction consolidées\n  readonly searchAndReactionMethods = {\n    highlightSearchTerms: (content: string, query: string) => {\n      if (!query) return content;\n      const regex = new RegExp(`(${query})`, 'gi');\n      return content.replace(regex, '<mark>$1</mark>');\n    },\n    toggleReactionPicker: (messageId: string) =>\n      (this.showReactionPicker[messageId] =\n        !this.showReactionPicker[messageId]),\n    reactToMessage: (messageId: string, emoji: string) =>\n      (this.showReactionPicker[messageId] = false),\n    toggleMessageOptions: (messageId: string) =>\n      (this.showMessageOptions[messageId] =\n        !this.showMessageOptions[messageId]),\n  };\n\n  highlightSearchTerms = this.searchAndReactionMethods.highlightSearchTerms;\n  toggleReactionPicker = this.searchAndReactionMethods.toggleReactionPicker;\n  reactToMessage = this.searchAndReactionMethods.reactToMessage;\n  toggleMessageOptions = this.searchAndReactionMethods.toggleMessageOptions;\n\n  // Confirmations consolidées\n  readonly confirmationMethods = {\n    showPinConfirmation: (messageId: string) =>\n      (this.showPinConfirm[messageId] = true),\n    cancelPinConfirmation: (messageId: string) =>\n      (this.showPinConfirm[messageId] = false),\n    showDeleteConfirmation: (messageId: string) =>\n      (this.showDeleteConfirm[messageId] = true),\n    cancelDeleteMessage: (messageId: string) =>\n      (this.showDeleteConfirm[messageId] = false),\n  };\n\n  showPinConfirmation = this.confirmationMethods.showPinConfirmation;\n  cancelPinConfirmation = this.confirmationMethods.cancelPinConfirmation;\n  showDeleteConfirmation = this.confirmationMethods.showDeleteConfirmation;\n  cancelDeleteMessage = this.confirmationMethods.cancelDeleteMessage;\n\n  // Méthodes de nettoyage optimisées\n  readonly cleanup = {\n    clearTimeouts: () =>\n      [this.typingTimeout, this.autoAwayTimeout, this.callTimer].forEach(\n        (t) => t && clearTimeout(t)\n      ),\n    setUserOffline: () =>\n      this.currentUserId &&\n      this.MessageService.setUserOffline(this.currentUserId).subscribe(),\n    stopTypingIndicator: () => {\n      if (this.isCurrentlyTyping && this.conversation?.id) {\n        this.isCurrentlyTyping = false;\n        clearTimeout(this.typingTimer);\n        this.MessageService.stopTyping(this.conversation.id).subscribe({\n          next: () => {},\n          error: (error) => {},\n        });\n      }\n    },\n  };\n\n  ngOnDestroy(): void {\n    this.cleanup.clearTimeouts();\n    this.cleanup.setUserOffline();\n    this.cleanup.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n}\n", "<div class=\"chat-container\">\n  <!-- En-tête -->\n  <div class=\"chat-header\">\n    <button (click)=\"goBackToConversations()\" class=\"action-btn\">\n      <i class=\"fas fa-arrow-left\"></i>\n    </button>\n\n    <img\n      [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n      alt=\"User avatar\"\n      class=\"user-avatar\"\n    />\n\n    <div class=\"user-info\" *ngIf=\"otherParticipant\">\n      <h3>{{ otherParticipant.username }}</h3>\n      <div class=\"user-status\">\n        {{\n          otherParticipant.isOnline\n            ? \"En ligne\"\n            : formatLastActive(otherParticipant.lastActive)\n        }}\n      </div>\n    </div>\n\n    <div class=\"action-buttons\">\n      <button\n        class=\"action-btn\"\n        (click)=\"initiateCall('AUDIO')\"\n        title=\"Appel audio\"\n      >\n        <i class=\"fas fa-phone\"></i>\n      </button>\n      <button\n        class=\"action-btn\"\n        (click)=\"initiateCall('VIDEO')\"\n        title=\"Appel vidéo\"\n      >\n        <i class=\"fas fa-video\"></i>\n      </button>\n      <button class=\"action-btn\" (click)=\"toggleSearchBar()\" title=\"Rechercher\">\n        <i class=\"fas fa-search\"></i>\n      </button>\n      <button\n        class=\"action-btn\"\n        (click)=\"toggleNotificationPanel()\"\n        title=\"Notifications\"\n      >\n        <i class=\"fas fa-bell\"></i>\n      </button>\n    </div>\n  </div>\n\n  <!-- Zone des messages -->\n  <div class=\"messages-area\" #messagesContainer>\n    <div\n      *ngIf=\"loading\"\n      style=\"text-align: center; padding: 2rem; color: #00f7ff\"\n    >\n      <i class=\"fas fa-spinner fa-spin\"></i> Chargement...\n    </div>\n\n    <div\n      *ngIf=\"!loading && messages.length === 0\"\n      style=\"text-align: center; padding: 2rem; color: #888\"\n    >\n      <i\n        class=\"fas fa-comments\"\n        style=\"font-size: 3rem; margin-bottom: 1rem; opacity: 0.5\"\n      ></i>\n      <p>Aucun message pour le moment</p>\n      <p style=\"font-size: 0.9rem\">Commencez la conversation !</p>\n    </div>\n\n    <div\n      *ngFor=\"let message of messages\"\n      class=\"message\"\n      [ngClass]=\"{\n        'current-user': message.sender?.id === currentUserId,\n        'other-user': message.sender?.id !== currentUserId\n      }\"\n    >\n      <div class=\"message-bubble\">\n        <div>{{ message.content }}</div>\n        <div style=\"font-size: 0.7rem; opacity: 0.7; margin-top: 0.5rem\">\n          {{ formatMessageTime(message.timestamp) }}\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Zone de saisie -->\n  <div class=\"input-area\">\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"input-form\"\n    >\n      <input\n        formControlName=\"content\"\n        placeholder=\"Tapez votre message...\"\n        class=\"message-input\"\n        [disabled]=\"!otherParticipant\"\n      />\n      <button\n        type=\"submit\"\n        class=\"send-btn\"\n        [disabled]=\"messageForm.invalid || !otherParticipant\"\n      >\n        <i class=\"fas fa-paper-plane\"></i>\n      </button>\n    </form>\n  </div>\n\n  <!-- Panneau de notifications -->\n  <div class=\"side-panel\" [ngClass]=\"{ open: showNotificationPanel }\">\n    <div class=\"panel-header\">\n      <h3>Notifications</h3>\n      <button class=\"close-btn\" (click)=\"toggleNotificationPanel()\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n    <div\n      *ngIf=\"notifications.length === 0\"\n      style=\"padding: 1rem; text-align: center; color: #888\"\n    >\n      <i\n        class=\"fas fa-bell-slash\"\n        style=\"font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.5\"\n      ></i>\n      <p>Aucune notification</p>\n    </div>\n    <div\n      *ngFor=\"let notification of notifications\"\n      style=\"padding: 0.5rem; border-bottom: 1px solid rgba(0, 247, 255, 0.1)\"\n    >\n      <p style=\"margin: 0; font-size: 0.9rem\">{{ notification.content }}</p>\n      <small style=\"opacity: 0.7\">{{\n        formatMessageTime(notification.timestamp)\n      }}</small>\n    </div>\n  </div>\n\n  <!-- Barre de recherche -->\n  <div\n    *ngIf=\"showSearchBar\"\n    style=\"\n      position: absolute;\n      top: 60px;\n      left: 0;\n      right: 0;\n      background: rgba(0, 0, 0, 0.9);\n      padding: 1rem;\n      z-index: 100;\n    \"\n  >\n    <div style=\"display: flex; align-items: center; gap: 0.5rem\">\n      <input\n        [(ngModel)]=\"searchQuery\"\n        placeholder=\"Rechercher dans la conversation...\"\n        style=\"\n          flex: 1;\n          padding: 0.5rem;\n          background: rgba(0, 0, 0, 0.5);\n          border: 1px solid #00f7ff;\n          border-radius: 5px;\n          color: #e0e0e0;\n        \"\n      />\n      <button\n        (click)=\"toggleSearchBar()\"\n        style=\"\n          background: transparent;\n          border: 1px solid #00f7ff;\n          color: #00f7ff;\n          padding: 0.5rem;\n          border-radius: 5px;\n        \"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAWA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAGEC,WAAW,EACXC,QAAQ,QACH,8BAA8B;AAErC,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;ICTpEC,EAAA,CAAAC,cAAA,cAAgD;IAC1CD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAPFH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAAC,QAAA,CAA+B;IAEjCR,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAS,kBAAA,MAAAH,MAAA,CAAAC,gBAAA,CAAAG,QAAA,gBAAAJ,MAAA,CAAAK,gBAAA,CAAAL,MAAA,CAAAC,gBAAA,CAAAK,UAAA,OAKF;;;;;IAiCFZ,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAa,SAAA,YAAsC;IAACb,EAAA,CAAAE,MAAA,sBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAa,SAAA,YAGK;IACLb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACnCH,EAAA,CAAAC,cAAA,YAA6B;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;IAG9DH,EAAA,CAAAC,cAAA,cAOC;IAEQD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChCH,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IATRH,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,GAAAC,UAAA,CAAAC,MAAA,kBAAAD,UAAA,CAAAC,MAAA,CAAAC,EAAA,MAAAC,MAAA,CAAAC,aAAA,GAAAJ,UAAA,CAAAC,MAAA,kBAAAD,UAAA,CAAAC,MAAA,CAAAC,EAAA,MAAAC,MAAA,CAAAC,aAAA,EAGE;IAGKrB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAY,UAAA,CAAAK,OAAA,CAAqB;IAExBtB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAW,MAAA,CAAAG,iBAAA,CAAAN,UAAA,CAAAO,SAAA,OACF;;;;;IAoCJxB,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAa,SAAA,YAGK;IACLb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAE5BH,EAAA,CAAAC,cAAA,cAGC;IACyCD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,GAE1B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAH8BH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAoB,eAAA,CAAAH,OAAA,CAA0B;IACtCtB,EAAA,CAAAI,SAAA,GAE1B;IAF0BJ,EAAA,CAAAK,iBAAA,CAAAqB,MAAA,CAAAH,iBAAA,CAAAE,eAAA,CAAAD,SAAA,EAE1B;;;;;;IAKNxB,EAAA,CAAAC,cAAA,cAWC;IAGKD,EAAA,CAAA2B,UAAA,2BAAAC,oEAAAC,MAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAAF,OAAA,CAAAG,WAAA,GAAAN,MAAA;IAAA,EAAyB;IAD3B7B,EAAA,CAAAG,YAAA,EAWE;IACFH,EAAA,CAAAC,cAAA,iBASC;IARCD,EAAA,CAAA2B,UAAA,mBAAAS,6DAAA;MAAApC,EAAA,CAAA8B,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAArC,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAG,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAS3BtC,EAAA,CAAAa,SAAA,YAA4B;IAC9Bb,EAAA,CAAAG,YAAA,EAAS;;;;IAtBPH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAc,UAAA,YAAAyB,MAAA,CAAAJ,WAAA,CAAyB;;;;;;;;AD/HjC,OAAM,MAAOK,oBAAoB;EA8K/B;EACA,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACC,CAAC,CAACC,SAAS;EACzB;EA6BA;EACA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACC,cAAc,CAACC,eAAe,EAAE;EAC9C;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAO,CACL;MACEC,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC;MACzCC,QAAQ,EAAE;KACX,EACD;MACEL,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC;MACzCC,QAAQ,EAAE;KACX,EACD;MACEL,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,YAAY;MACnBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACb,eAAe,EAAE;MACrCe,QAAQ,EAAE,IAAI,CAACC,aAAa;MAC5BC,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACEP,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,sBAAsB,IAAI,CAACM,sBAAsB,EAAE,GAAG;MAC7DL,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACM,oBAAoB,EAAE;MAC1CJ,QAAQ,EAAE,IAAI,CAACK,kBAAkB;MACjCH,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DI,KAAK,EACH,IAAI,CAACH,sBAAsB,EAAE,GAAG,CAAC,GAC7B;QACEI,KAAK,EAAE,IAAI,CAACJ,sBAAsB,EAAE;QACpCR,KAAK,EAAE,gCAAgC;QACvCa,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEb,KAAK,EAAE,4BAA4B;MACnCC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,eAAe;MACtBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACW,uBAAuB,EAAE;MAC7CT,QAAQ,EAAE,IAAI,CAACU,qBAAqB;MACpCR,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DI,KAAK,EACH,IAAI,CAACK,uBAAuB,GAAG,CAAC,GAC5B;QACEJ,KAAK,EAAE,IAAI,CAACI,uBAAuB;QACnChB,KAAK,EAAE,8CAA8C;QACrDa,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEb,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,uBAAuB;MAC9BC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACc,sBAAsB,EAAE;MAC5CZ,QAAQ,EAAE,IAAI,CAACa,oBAAoB;MACnCX,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACEP,KAAK,EAAE,oBAAoB;MAC3BC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,uBAAuB;MAC9BC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgB,oBAAoB,EAAE;MAC1Cd,QAAQ,EAAE,IAAI,CAACe,kBAAkB;MACjCb,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACEP,KAAK,EAAE,6BAA6B;MACpCC,IAAI,EAAE,mBAAmB;MACzBC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACkB,wBAAwB,EAAE;MAC9ChB,QAAQ,EAAE,IAAI,CAACiB,sBAAsB;MACrCf,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DI,KAAK,EACH,IAAI,CAACY,aAAa,CAACC,MAAM,GAAG,CAAC,GACzB;QACEZ,KAAK,EAAE,IAAI,CAACW,aAAa,CAACC,MAAM;QAChCxB,KAAK,EAAE,cAAc;QACrBa,OAAO,EAAE;OACV,GACD;KACP,CACF;EACH;EAEAY,YACU5B,cAA8B,EAC/B6B,KAAqB,EACpBC,WAA4B,EAC5BC,EAAe,EAChBC,aAAgC,EAChCC,MAAc,EACbC,YAA0B,EAE1BC,GAAsB;IARtB,KAAAnC,cAAc,GAAdA,cAAc;IACf,KAAA6B,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,YAAY,GAAZA,YAAY;IAEZ,KAAAC,GAAG,GAAHA,GAAG;IAjTb,KAAAC,QAAQ,GAAc,EAAE;IAExB,KAAAC,YAAY,GAAwB,IAAI;IACxC,KAAAC,OAAO,GAAG,IAAI;IAEd,KAAA9D,aAAa,GAAkB,IAAI;IACnC,KAAA+D,eAAe,GAAW,KAAK;IAC/B,KAAA7E,gBAAgB,GAAgB,IAAI;IACpC,KAAA8E,YAAY,GAAgB,IAAI;IAChC,KAAAC,UAAU,GAAgC,IAAI;IAC9C,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAET,KAAAC,qBAAqB,GAAG,CAAC;IACzB,KAAAC,oBAAoB,GAAG,EAAE;IACzB,KAAAC,kBAAkB,GAAG,GAAG;IACjC,KAAAC,WAAW,GAAG,CAAC;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACd,KAAAC,aAAa,GAAG,IAAIvG,YAAY,EAAE;IAE1C;IACA,KAAAwG,aAAa,GAAW,eAAe;IAEvC;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAA/C,aAAa,GAAG,KAAK;IACrB,KAAAI,kBAAkB,GAAG,KAAK;IAC1B,KAAA4C,kBAAkB,GAAG,KAAK;IAC1B,KAAAvC,qBAAqB,GAAG,KAAK;IAC7B,KAAAwC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAtC,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,kBAAkB,GAAG,KAAK;IAC1B,KAAAE,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAmC,YAAY,GAAQ,IAAI;IACxB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,SAAS,GAAQ,IAAI;IAErB;IACA,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAjD,uBAAuB,GAAW,CAAC;IACnC,KAAAkD,qBAAqB,GAAgB,IAAIC,GAAG,EAAE;IAC9C,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,iBAAiB,GAAQ,IAAI;IAE7B;IACA,KAAApF,WAAW,GAAG,EAAE;IAChB,KAAAqF,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,aAAa,GAAU,EAAE;IAEzB;IACA,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,kBAAkB,GAA+B,EAAE;IACnD,KAAAC,iBAAiB,GAA+B,EAAE;IAClD,KAAAC,cAAc,GAA+B,EAAE;IAC/C,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,qBAAqB,GAAa,EAAE;IACpC,KAAAC,sBAAsB,GAAU,EAAE;IAClC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,sBAAsB,GAAG,KAAK;IAE9B;IACS,KAAA5F,CAAC,GAAG;MACXC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC/C4F,MAAM,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAG,CAAE;MAC9DC,KAAK,EAAE,6BAA6B;MACpCC,SAAS,EAAEA,CAACC,CAAS,EAAEC,IAAS,KAAaA,IAAI,EAAE5H,EAAE,IAAI2H,CAAC,CAACE,QAAQ,EAAE;MACrE/B,aAAa,EAAE;QACbgC,WAAW,EAAE;UAAEhG,IAAI,EAAE,gBAAgB;UAAEiG,KAAK,EAAE;QAAe,CAAE;QAC/DC,WAAW,EAAE;UAAElG,IAAI,EAAE,oBAAoB;UAAEiG,KAAK,EAAE;QAAc,CAAE;QAClEE,MAAM,EAAE;UAAEnG,IAAI,EAAE,YAAY;UAAEiG,KAAK,EAAE;QAAe;OACrD;MACDG,MAAM,EAAE;QACNC,MAAM,EAAE;UACNC,IAAI,EAAE,UAAU;UAChBL,KAAK,EAAE,gBAAgB;UACvBjG,IAAI,EAAE,eAAe;UACrBuG,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAE;SACd;QACDC,OAAO,EAAE;UACPH,IAAI,EAAE,YAAY;UAClBL,KAAK,EAAE,eAAe;UACtBjG,IAAI,EAAE,eAAe;UACrBuG,KAAK,EAAE,YAAY;UACnBC,WAAW,EAAE;SACd;QACDjB,IAAI,EAAE;UACJe,IAAI,EAAE,QAAQ;UACdL,KAAK,EAAE,iBAAiB;UACxBjG,IAAI,EAAE,cAAc;UACpBuG,KAAK,EAAE,QAAQ;UACfC,WAAW,EAAE;SACd;QACDE,IAAI,EAAE;UACJJ,IAAI,EAAE,QAAQ;UACdL,KAAK,EAAE,cAAc;UACrBjG,IAAI,EAAE,qBAAqB;UAC3BuG,KAAK,EAAE,QAAQ;UACfC,WAAW,EAAE;;OAEhB;MACDG,MAAM,EAAE,CACN;QACEC,GAAG,EAAE,eAAe;QACpBL,KAAK,EAAE,QAAQ;QACfN,KAAK,EAAE,SAAS;QAChBY,UAAU,EAAE;OACb,EACD;QACED,GAAG,EAAE,gBAAgB;QACrBL,KAAK,EAAE,MAAM;QACbN,KAAK,EAAE,SAAS;QAChBY,UAAU,EAAE;OACb,EACD;QACED,GAAG,EAAE,iBAAiB;QACtBL,KAAK,EAAE,MAAM;QACbN,KAAK,EAAE,SAAS;QAChBY,UAAU,EAAE;OACb,EACD;QACED,GAAG,EAAE,eAAe;QACpBL,KAAK,EAAE,MAAM;QACbN,KAAK,EAAE,SAAS;QAChBY,UAAU,EAAE;OACb,CACF;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;OACX;MACD;MACAC,kBAAkB,EAAE;QAClBlB,WAAW,EAAE;UAAEhG,IAAI,EAAE,gBAAgB;UAAEiG,KAAK,EAAE;QAAe,CAAE;QAC/DC,WAAW,EAAE;UAAElG,IAAI,EAAE,oBAAoB;UAAEiG,KAAK,EAAE;QAAc,CAAE;QAClEE,MAAM,EAAE;UAAEnG,IAAI,EAAE,YAAY;UAAEiG,KAAK,EAAE;QAAe;OACrD;MACDkB,gBAAgB,EAAE;QAChBJ,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;;KAEb;IAOD;IACA,KAAAG,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,uBAAuB,GAAG,KAAK;IAC/B,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,cAAc,GAAG,IAAI;IAErB;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,WAAW,GAAG,YAAY;IAC1B,KAAAC,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAC,WAAW,GAAsB,IAAIC,GAAG,EAAE;IAC1C,KAAAC,iBAAiB,GAAW,QAAQ;IACpC,KAAAC,gBAAgB,GAAS,IAAIC,IAAI,EAAE;IACnC,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAhH,aAAa,GAAU,EAAE;IACzB,KAAAiH,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,gBAAgB,GAAG,KAAK;IAuLxB;IACS,KAAAC,CAAC,GAAG;MACXC,OAAO,EAAGC,CAAU,IAAK,IAAI,CAAChJ,cAAc,CAACiJ,WAAW,CAACD,CAAC,CAAC;MAC3DE,OAAO,EAAGF,CAAU,IAAK,IAAI,CAAChJ,cAAc,CAACmJ,WAAW,CAACH,CAAC;KAC3D;IAED,KAAAC,WAAW,GAAG,IAAI,CAACH,CAAC,CAACC,OAAO;IAC5B,KAAAI,WAAW,GAAG,IAAI,CAACL,CAAC,CAACI,OAAO;IAmLpB,KAAAE,iBAAiB,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,GAAG;IAClB,KAAAC,cAAc,GAAG,IAAI;IAuDtC;IACS,KAAAC,iBAAiB,GAAG;MAC3BC,aAAa,EAAEA,CAAA,KAAM,IAAI,CAACC,WAAW,CAAC,OAAO,CAAC;MAC9CC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACD,WAAW,CAAC,MAAM,CAAC;MACxCE,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACF,WAAW,CAAC,OAAO;KAC5C;IAED,KAAAG,mBAAmB,GAAG,IAAI,CAACL,iBAAiB,CAACC,aAAa;IAC1D,KAAAK,cAAc,GAAG,IAAI,CAACN,iBAAiB,CAACG,QAAQ;IAChD,KAAAI,iBAAiB,GAAG,IAAI,CAACP,iBAAiB,CAACI,WAAW;IAQtD;IACiB,KAAAI,aAAa,GAAG;MAC/BjF,cAAc,EAAEA,CAAA,KAAO,IAAI,CAACjE,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAmB;MAC1EmJ,SAAS,EAAEA,CAAA,KAAK;QACd,IAAI,CAACP,WAAW,CAAC,QAAQ,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAChJ,aAAa,EAAE,IAAI,CAACwJ,WAAW,EAAE;MAC7C,CAAC;MACDC,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACT,WAAW,CAAC,QAAQ,CAAC;MAChDU,oBAAoB,EAAEA,CAAA,KACnB,IAAI,CAACzG,wBAAwB,GAAG,CAAC,IAAI,CAACA,wBAAyB;MAClE0G,eAAe,EAAEA,CAAA,KACd,IAAI,CAACzG,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAoB;MACxD0G,YAAY,EAAEA,CAAA,KAAO,IAAI,CAACrC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAgB;MAClEsC,gBAAgB,EAAEA,CAAA,KACf,IAAI,CAACjJ,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAqB;MAC1DkJ,cAAc,EAAEA,CAAA,KAAO,IAAI,CAAChJ,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAmB;MAC1EiJ,kBAAkB,EAAEA,CAAA,KACjB,IAAI,CAAC/I,sBAAsB,GAAG,CAAC,IAAI,CAACA;KACxC;IAED,KAAAb,oBAAoB,GAAG,IAAI,CAACmJ,aAAa,CAACjF,cAAc;IACxD,KAAArF,eAAe,GAAG,IAAI,CAACsK,aAAa,CAACC,SAAS;IAC9C,KAAAS,oBAAoB,GAAG,IAAI,CAACV,aAAa,CAACG,cAAc;IACxD,KAAAQ,0BAA0B,GAAG,IAAI,CAACX,aAAa,CAACI,oBAAoB;IACpE,KAAAQ,qBAAqB,GAAG,IAAI,CAACZ,aAAa,CAACK,eAAe;IAC1D,KAAAQ,kBAAkB,GAAG,IAAI,CAACb,aAAa,CAACM,YAAY;IACpD,KAAAjJ,sBAAsB,GAAG,IAAI,CAAC2I,aAAa,CAACO,gBAAgB;IAC5D,KAAAhJ,oBAAoB,GAAG,IAAI,CAACyI,aAAa,CAACQ,cAAc;IACxD,KAAA/I,wBAAwB,GAAG,IAAI,CAACuI,aAAa,CAACS,kBAAkB;IAQvD,KAAAK,mBAAmB,GAAG;MAC7BC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACC,sBAAsB,CAAC,8BAA8B,CAAC;MAC3EC,YAAY,EAAEA,CAAA,KACZ,IAAI,CAACD,sBAAsB,CAAC,4BAA4B,CAAC;MAC3DE,KAAK,EAAEA,CAAA,KAAW;QAChB,IAAI,CAAC,IAAI,CAAC5I,YAAY,EAAE/D,EAAE,IAAI,IAAI,CAAC8D,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;UACxD,IAAI,CAACO,YAAY,CAACgJ,WAAW,CAAC,6BAA6B,CAAC;UAC5D;;QAGF,IACEC,OAAO,CACL,oHAAoH,CACrH,EACD;UACA,IAAI,CAAC/I,QAAQ,GAAG,EAAE;UAClB,IAAI,CAACmB,YAAY,GAAG,KAAK;UACzB,IAAI,CAACrB,YAAY,CAACkJ,WAAW,CAAC,gCAAgC,CAAC;;MAEnE,CAAC;MACDC,MAAM,EAAEA,CAAA,KAAW;QACjB,IAAI,CAAC,IAAI,CAAChJ,YAAY,EAAE/D,EAAE,IAAI,IAAI,CAAC8D,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;UACxD,IAAI,CAACO,YAAY,CAACgJ,WAAW,CAAC,gCAAgC,CAAC;UAC/D;;QAGF,MAAMI,gBAAgB,GAAG,IAAI,CAACjJ,YAAY,CAACkJ,OAAO,GAC9C,IAAI,CAAClJ,YAAY,CAACmJ,SAAS,IAAI,iBAAiB,GAChD,IAAI,CAAC9N,gBAAgB,EAAEC,QAAQ,IAAI,qBAAqB;QAE5D,MAAM8N,UAAU,GAAG;UACjBpJ,YAAY,EAAE;YACZ/D,EAAE,EAAE,IAAI,CAAC+D,YAAY,CAAC/D,EAAE;YACxBoN,IAAI,EAAEJ,gBAAgB;YACtBC,OAAO,EAAE,IAAI,CAAClJ,YAAY,CAACkJ,OAAO;YAClCI,YAAY,EAAE,IAAI,CAACtJ,YAAY,CAACsJ,YAAY;YAC5CC,SAAS,EAAE,IAAI,CAACvJ,YAAY,CAACuJ;WAC9B;UACDxJ,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACyJ,GAAG,CAAEC,GAAG,KAAM;YACpCxN,EAAE,EAAEwN,GAAG,CAACxN,EAAE;YACVG,OAAO,EAAEqN,GAAG,CAACrN,OAAO;YACpBJ,MAAM,EAAEyN,GAAG,CAACzN,MAAM;YAClBM,SAAS,EAAEmN,GAAG,CAACnN,SAAS;YACxBoN,IAAI,EAAED,GAAG,CAACC;WACX,CAAC,CAAC;UACHC,UAAU,EAAE,IAAIzD,IAAI,EAAE,CAAC0D,WAAW,EAAE;UACpCC,UAAU,EAAE,IAAI,CAAC1N;SAClB;QAED,MAAM2N,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAACb,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;UAC3DM,IAAI,EAAE;SACP,CAAC;QACF,MAAMQ,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;QAC5C,MAAMQ,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;QAEf,MAAMQ,YAAY,GAAGzB,gBAAgB,CAClC0B,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAC3BC,WAAW,EAAE;QAChB,MAAMC,OAAO,GAAG,IAAI3E,IAAI,EAAE,CAAC0D,WAAW,EAAE,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtDR,IAAI,CAACS,QAAQ,GAAG,gBAAgBL,YAAY,IAAIG,OAAO,OAAO;QAE9DP,IAAI,CAACU,KAAK,EAAE;QACZb,MAAM,CAACC,GAAG,CAACa,eAAe,CAACf,GAAG,CAAC;QAE/B,IAAI,CAAChJ,YAAY,GAAG,KAAK;QACzB,IAAI,CAACrB,YAAY,CAACkJ,WAAW,CAAC,mCAAmC,CAAC;MACpE;KACD;IAED;IACA,KAAAmC,sBAAsB,GAAG,IAAI,CAAC1C,mBAAmB,CAACC,QAAQ;IAC1D,KAAA0C,0BAA0B,GAAG,IAAI,CAAC3C,mBAAmB,CAACG,YAAY;IAClE,KAAAyC,iBAAiB,GAAG,IAAI,CAAC5C,mBAAmB,CAACI,KAAK;IAClD,KAAAyC,kBAAkB,GAAG,IAAI,CAAC7C,mBAAmB,CAACQ,MAAM;IA6GpD;IACS,KAAAsC,CAAC,GAAG;MACXC,UAAU,EAAG5E,CAA4B,IACvC,IAAI,CAAChJ,cAAc,CAACtB,iBAAiB,CAACsK,CAAC,CAAC;MAC1C6E,YAAY,EAAG7E,CAA4B,IACzC,IAAI,CAAChJ,cAAc,CAAClC,gBAAgB,CAACkL,CAAC,CAAC;MACzC8E,UAAU,EAAG9E,CAA4B,IACvC,IAAI,CAAChJ,cAAc,CAAC+N,iBAAiB,CAAC/E,CAAC,CAAC;MAC1CgF,cAAc,EAAG/H,CAAS,IACxB,IAAI,CAACjG,cAAc,CAACiO,oBAAoB,CAAC,IAAI,CAAC7L,QAAQ,EAAE6D,CAAC,CAAC;MAC5DiD,OAAO,EAAGgF,CAA6B,IACrC,IAAI,CAAClO,cAAc,CAACmO,cAAc,CAACD,CAAC,CAAC;MACvCE,QAAQ,EAAGF,CAA6B,IACtC,IAAI,CAAClO,cAAc,CAACoO,QAAQ,CAACF,CAAC,CAAC;MACjCG,OAAO,EAAGH,CAA6B,IACrC,IAAI,CAAClO,cAAc,CAACsO,cAAc,CAACJ,CAAC,CAAC;MACvCK,WAAW,EAAGL,CAA6B,IACzC,IAAI,CAAClO,cAAc,CAACwO,kBAAkB,CAACN,CAAC,CAAC;MAC3CO,gBAAgB,EAAGP,CAA6B,IAC9C,IAAI,CAAClO,cAAc,CAAC0O,uBAAuB,CAACR,CAAC,CAAC;MAChDS,cAAc,EAAG1I,CAAS,IAAK,IAAI,CAACjG,cAAc,CAAC4O,iBAAiB,CAAC3I,CAAC,CAAC;MACvE4I,WAAW,EAAGlB,CAAS,IAAK,IAAI,CAAC3N,cAAc,CAAC8O,mBAAmB,CAACnB,CAAC,CAAC;MACtEoB,WAAW,EAAGb,CAA6B,IACzC,IAAI,CAAClO,cAAc,CAAC+O,WAAW,CAACb,CAAC,CAAC;MACpCc,YAAY,EAAGd,CAA6B,IAC1C,IAAI,CAAClO,cAAc,CAACiP,mBAAmB,CAACf,CAAC,EAAE,IAAI,CAAC1P,aAAa;KAChE;IAED;IACA,KAAAE,iBAAiB,GAAG,IAAI,CAACiP,CAAC,CAACC,UAAU;IACrC,KAAA9P,gBAAgB,GAAG,IAAI,CAAC6P,CAAC,CAACE,YAAY;IACtC,KAAAE,iBAAiB,GAAG,IAAI,CAACJ,CAAC,CAACG,UAAU;IACrC,KAAAG,oBAAoB,GAAG,IAAI,CAACN,CAAC,CAACK,cAAc;IAC5C,KAAAG,cAAc,GAAG,IAAI,CAACR,CAAC,CAACzE,OAAO;IAC/B,KAAAkF,QAAQ,GAAG,IAAI,CAACT,CAAC,CAACS,QAAQ;IAC1B,KAAAE,cAAc,GAAG,IAAI,CAACX,CAAC,CAACU,OAAO;IAC/B,KAAAG,kBAAkB,GAAG,IAAI,CAACb,CAAC,CAACY,WAAW;IACvC,KAAAG,uBAAuB,GAAG,IAAI,CAACf,CAAC,CAACc,gBAAgB;IACjD,KAAAG,iBAAiB,GAAG,IAAI,CAACjB,CAAC,CAACgB,cAAc;IACzC,KAAAG,mBAAmB,GAAG,IAAI,CAACnB,CAAC,CAACkB,WAAW;IACxC,KAAAE,WAAW,GAAG,IAAI,CAACpB,CAAC,CAACoB,WAAW;IAChC,KAAAE,mBAAmB,GAAG,IAAI,CAACtB,CAAC,CAACqB,YAAY;IAkFzC;IACS,KAAAE,uBAAuB,GAAG;MACjCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvC,QAAQ,CAACwC,cAAc,CAAC,2BAA2B,CAAC,EAAE;UACzD,MAAMC,SAAS,GAAGzC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC/CwC,SAAS,CAAC/Q,EAAE,GAAG,2BAA2B;UAC1C+Q,SAAS,CAACC,SAAS,GAAG,wCAAwC;UAC9DD,SAAS,CAACE,SAAS,GACjB,uEAAuE;UACzE,IAAI,CAACC,iBAAiB,EAAEC,aAAa,EAAEC,OAAO,CAACL,SAAS,CAAC;;MAE7D,CAAC;MACDM,IAAI,EAAEA,CAAA,KAAK;QACT,MAAMN,SAAS,GAAGzC,QAAQ,CAACwC,cAAc,CAAC,2BAA2B,CAAC;QACtEC,SAAS,EAAEO,UAAU,EAAEC,WAAW,CAACR,SAAS,CAAC;MAC/C;KACD;IAEO,KAAAS,oBAAoB,GAAG,IAAI,CAACZ,uBAAuB,CAACC,IAAI;IACxD,KAAAY,oBAAoB,GAAG,IAAI,CAACb,uBAAuB,CAACS,IAAI;IAuHhE;IACS,KAAAK,qBAAqB,GAAG;MAC/BC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACrN,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;QAC9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE,IAAI,CAACC,sBAAsB,GAAG,CAAC;MAC7D,CAAC;MACDqN,QAAQ,EAAGC,SAAe,IAAI;QAC5B,IAAI,CAAC,IAAI,CAAC9N,YAAY,EAAE/D,EAAE,IAAI,CAAC,IAAI,CAACZ,gBAAgB,EAAEY,EAAE,EAAE;UACxD,IAAI,CAAC4D,YAAY,CAACkO,SAAS,CAAC,uCAAuC,CAAC;UACpE,IAAI,CAACxN,gBAAgB,GAAG,KAAK;UAC7B;;QAEF,MAAMyN,UAAU,GAAG,IAAI,CAAC3S,gBAAgB,EAAEY,EAAE,IAAI,EAAE;QAClD,IAAI,CAAC0B,cAAc,CAACsQ,gBAAgB,CAClCD,UAAU,EACVF,SAAS,EACT,IAAI,CAAC9N,YAAY,EAAE/D,EAAE,EACrB,IAAI,CAACuE,sBAAsB,CAC5B,CAAC0N,SAAS,CAAC;UACVC,IAAI,EAAGC,OAAO,IAAI;YAChB,IAAI,CAAC7N,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;YAC/B,IAAI,CAAC6N,cAAc,CAAC,IAAI,CAAC;UAC3B,CAAC;UACD3K,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC7D,YAAY,CAACkO,SAAS,CAAC,8BAA8B,CAAC;YAC3D,IAAI,CAACxN,gBAAgB,GAAG,KAAK;UAC/B;SACD,CAAC;MACJ,CAAC;MACD+N,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC/N,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;MACjC;KACD;IAED,KAAA+N,oBAAoB,GAAG,IAAI,CAACZ,qBAAqB,CAACC,MAAM;IACxD,KAAAY,wBAAwB,GAAG,IAAI,CAACb,qBAAqB,CAACE,QAAQ;IAC9D,KAAAY,yBAAyB,GAAG,IAAI,CAACd,qBAAqB,CAACW,MAAM;IA8I7D;IACS,KAAAI,WAAW,GAAG;MACrBC,QAAQ,EAAGjF,IAAuB,IAAI;QACpC,IAAI,CAAC,IAAI,CAACrO,gBAAgB,EAAEY,EAAE,EAAE;QAChC,IAAI,CAAC0B,cAAc,CAACO,YAAY,CAC9B,IAAI,CAAC7C,gBAAgB,CAACY,EAAE,EACxByN,IAAI,KAAK,OAAO,GAAGhP,QAAQ,CAACkU,KAAK,GAAGlU,QAAQ,CAACmU,KAAK,EAClD,IAAI,CAAC7O,YAAY,EAAE/D,EAAE,CACtB,CAACiS,SAAS,CAAC;UACVC,IAAI,EAAGW,IAAI,IAAI,CAAE,CAAC;UAClBpL,KAAK,EAAEA,CAAA,KAAM,IAAI,CAAC7D,YAAY,CAACkO,SAAS,CAAC,IAAI,CAACvQ,CAAC,CAACkG,KAAK;SACtD,CAAC;MACJ,CAAC;MACDqL,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC,IAAI,CAACxN,YAAY,EAAE;QACxB,IAAI,CAAC5D,cAAc,CAACqR,UAAU,CAAC,IAAI,CAACzN,YAAY,CAACtF,EAAE,CAAC,CAACiS,SAAS,CAAC;UAC7DC,IAAI,EAAGW,IAAI,IAAI;YACb,IAAI,CAACrN,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;YACxB,IAAI,CAACK,cAAc,GAAG,IAAI,CAACL,YAAY,EAAEmI,IAAI,KAAK,OAAO;YACzD,IAAI,CAAC9D,WAAW,GAAG,YAAY;YAC/B,IAAI,CAAC/F,YAAY,CAACkJ,WAAW,CAAC,gBAAgB,CAAC;UACjD,CAAC;UACDrF,KAAK,EAAEA,CAAA,KAAK;YACV,IAAI,CAAC7D,YAAY,CAACkO,SAAS,CAAC,IAAI,CAACvQ,CAAC,CAACkG,KAAK,CAAC;YACzC,IAAI,CAACjC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;UAC1B;SACD,CAAC;MACJ,CAAC;MACD0N,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC,IAAI,CAAC1N,YAAY,EAAE;QACxB,IAAI,CAAC5D,cAAc,CAACuR,UAAU,CAAC,IAAI,CAAC3N,YAAY,CAACtF,EAAE,CAAC,CAACiS,SAAS,CAAC;UAC7DC,IAAI,EAAGW,IAAI,IAAI;YACb,IAAI,CAACrN,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;UAC1B,CAAC;UACDmC,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACjC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;UAC1B;SACD,CAAC;MACJ,CAAC;MACD4N,GAAG,EAAEA,CAAA,KAAK;QACR,MAAMC,GAAG,GAAG,IAAI,CAACzR,cAAc,CAAC0R,WAAW,CAACnB,SAAS,CAAEY,IAAI,IAAI;UAC7D,IAAIA,IAAI,EAAE;YACR,IAAI,CAACnR,cAAc,CAAC2R,OAAO,CAACR,IAAI,CAAC7S,EAAE,CAAC,CAACiS,SAAS,CAAC;cAC7CC,IAAI,EAAGW,IAAI,IAAI,CAAE,CAAC;cAClBpL,KAAK,EAAGA,KAAK,IAAI,CAAE;aACpB,CAAC;;QAEN,CAAC,CAAC;QACF0L,GAAG,CAACG,WAAW,EAAE;MACnB;KACD;IAED,KAAArR,YAAY,GAAG,IAAI,CAACwQ,WAAW,CAACC,QAAQ;IACxC,KAAAK,UAAU,GAAG,IAAI,CAACN,WAAW,CAACK,MAAM;IACpC,KAAAG,UAAU,GAAG,IAAI,CAACR,WAAW,CAACO,MAAM;IACpC,KAAAK,OAAO,GAAG,IAAI,CAACZ,WAAW,CAACS,GAAG;IAE9B;IACS,KAAAK,kBAAkB,GAAG;MAC5BC,UAAU,EAAEA,CAAA,KAAK;QACf,IAAI,CAAC9N,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;QACpC,IAAI,CAAC6N,kBAAkB,CAACE,WAAW,EAAE;QACrC,IAAI,CAAC7P,YAAY,CAAC4I,QAAQ,CACxB,IAAI,CAAC9G,WAAW,GAAG,sBAAsB,GAAG,mBAAmB,CAChE;MACH,CAAC;MACDgO,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAAC/N,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAAC4N,kBAAkB,CAACE,WAAW,EAAE;QACrC,IAAI,CAAC7P,YAAY,CAAC4I,QAAQ,CACxB,IAAI,CAAC7G,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACD8N,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAAC/R,cAAc,CAACiS,WAAW,CAC7B,IAAI,CAACpO,UAAU,EAAEvF,EAAE,EACnB,CAAC,IAAI,CAAC0F,WAAW,EACjB,IAAI,CAACC,cAAc,CACpB,CAACsM,SAAS,CAAC;UACVC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;UACdzK,KAAK,EAAGA,KAAK,IACXmM,OAAO,CAACnM,KAAK,CAAC,2CAA2C,EAAEA,KAAK;SACnE,CAAC;MACJ;KACD;IAED,KAAAoM,cAAc,GAAG,IAAI,CAACN,kBAAkB,CAACC,UAAU;IACnD,KAAAM,eAAe,GAAG,IAAI,CAACP,kBAAkB,CAACG,WAAW;IAC7C,KAAAK,eAAe,GAAG,IAAI,CAACR,kBAAkB,CAACE,WAAW;IAE7D;IACS,KAAAO,YAAY,GAAG;MACtBC,cAAc,EAAEA,CAAA,KAAK;QACnB,IAAI,CAACrO,YAAY,GAAG,CAAC;QACrB,IAAI,CAACC,SAAS,GAAGqO,WAAW,CAAC,MAAK;UAChC,IAAI,CAACtO,YAAY,EAAE;UACnB,IAAI,IAAI,CAACA,YAAY,KAAK,CAAC,IAAI,IAAI,CAAC+D,WAAW,KAAK,YAAY,EAAE;YAChE,IAAI,CAACA,WAAW,GAAG,WAAW;;QAElC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDwK,aAAa,EAAEA,CAAA,KAAK;QAClB,IAAI,IAAI,CAACtO,SAAS,EAAE;UAClBuO,aAAa,CAAC,IAAI,CAACvO,SAAS,CAAC;UAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;MAEzB,CAAC;MACDwO,cAAc,EAAEA,CAAA,KAAO,IAAI,CAACzO,YAAY,GAAG;KAC5C;IAEO,KAAA0O,oBAAoB,GAAG,IAAI,CAACN,YAAY,CAACC,cAAc;IACvD,KAAAM,mBAAmB,GAAG,IAAI,CAACP,YAAY,CAACG,aAAa;IACrD,KAAAK,oBAAoB,GAAG,IAAI,CAACR,YAAY,CAACK,cAAc;IAE/D;IAEA;IACS,KAAAI,wBAAwB,GAAG;MAClCtJ,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAACA,WAAW,CAAC,cAAc,CAAC;QAChC,IAAI,IAAI,CAACvI,qBAAqB,EAAE;UAC9B,IAAI,CAAC8R,iBAAiB,EAAE;;MAE5B;KACD;IAED,KAAA/R,uBAAuB,GAAG,IAAI,CAAC8R,wBAAwB,CAACtJ,WAAW;IA8BnE;IACS,KAAAwJ,mBAAmB,GAAG;MAC7BC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACF,iBAAiB,EAAE;MACxCG,WAAW,EAAEA,CAAA,KACV,IAAI,CAAChS,uBAAuB,GAAG,IAAI,CAACiD,aAAa,CAAClH,MAAM,CACtDkW,CAAC,IAAK,CAACA,CAAC,CAACC,MAAM,CACjB,CAAC1R,MAAO;MACX2R,WAAW,EAAEA,CAAA,KAAM,IAAI,CAAClP,aAAa;MACrCmP,eAAe,EAAGC,cAAsB,IAAI;QAC1C,IAAI,IAAI,CAACnP,qBAAqB,CAACoP,GAAG,CAACD,cAAc,CAAC,EAAE;UAClD,IAAI,CAACnP,qBAAqB,CAACqP,MAAM,CAACF,cAAc,CAAC;SAClD,MAAM;UACL,IAAI,CAACnP,qBAAqB,CAACsP,GAAG,CAACH,cAAc,CAAC;;MAElD,CAAC;MACDI,eAAe,EAAEA,CAAA,KAAK;QACpB,MAAMC,qBAAqB,GAAG,IAAI,CAACZ,mBAAmB,CAACK,WAAW,EAAE;QACpE,MAAMQ,WAAW,GAAGD,qBAAqB,CAACE,KAAK,CAAEX,CAAC,IAChD,IAAI,CAAC/O,qBAAqB,CAACoP,GAAG,CAACL,CAAC,CAAC9U,EAAE,CAAC,CACrC;QACD,IAAIwV,WAAW,EAAE;UACfD,qBAAqB,CAACG,OAAO,CAAEZ,CAAC,IAC9B,IAAI,CAAC/O,qBAAqB,CAACqP,MAAM,CAACN,CAAC,CAAC9U,EAAE,CAAC,CACxC;SACF,MAAM;UACLuV,qBAAqB,CAACG,OAAO,CAAEZ,CAAC,IAC9B,IAAI,CAAC/O,qBAAqB,CAACsP,GAAG,CAACP,CAAC,CAAC9U,EAAE,CAAC,CACrC;;MAEL,CAAC;MACD2V,cAAc,EAAEA,CAAA,KAAK;QACnB,MAAMJ,qBAAqB,GAAG,IAAI,CAACZ,mBAAmB,CAACK,WAAW,EAAE;QACpE,OACEO,qBAAqB,CAAClS,MAAM,GAAG,CAAC,IAChCkS,qBAAqB,CAACE,KAAK,CAAEX,CAAC,IAAK,IAAI,CAAC/O,qBAAqB,CAACoP,GAAG,CAACL,CAAC,CAAC9U,EAAE,CAAC,CAAC;MAE5E;KACD;IAED,KAAA4V,qBAAqB,GAAG,IAAI,CAACjB,mBAAmB,CAACC,QAAQ;IACjD,KAAAiB,uBAAuB,GAAG,IAAI,CAAClB,mBAAmB,CAACE,WAAW;IACtE,KAAAiB,wBAAwB,GAAG,IAAI,CAACnB,mBAAmB,CAACK,WAAW;IAC/D,KAAAe,2BAA2B,GAAG,IAAI,CAACpB,mBAAmB,CAACM,eAAe;IACtE,KAAAe,4BAA4B,GAAG,IAAI,CAACrB,mBAAmB,CAACW,eAAe;IACvE,KAAAW,2BAA2B,GAAG,IAAI,CAACtB,mBAAmB,CAACgB,cAAc;IAErE;IACS,KAAAO,cAAc,GAAG;MACxBC,YAAY,EAAEA,CAAA,KAAK;QACjB,MAAMC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvQ,qBAAqB,CAAC;QAC1D,IAAIqQ,WAAW,CAAC/S,MAAM,KAAK,CAAC,EAAE;UAC5B,IAAI,CAACO,YAAY,CAACgJ,WAAW,CAAC,kCAAkC,CAAC;UACjE;;QAEF,IAAI,CAACsJ,cAAc,CAACK,UAAU,CAACH,WAAW,EAAE,MAAK;UAC/C,IAAI,CAACrQ,qBAAqB,CAAC4G,KAAK,EAAE;UAClC,IAAI,CAAC/I,YAAY,CAACkJ,WAAW,CAC3B,GAAGsJ,WAAW,CAAC/S,MAAM,0CAA0C,CAChE;QACH,CAAC,CAAC;MACJ,CAAC;MACDmT,OAAO,EAAEA,CAAA,KAAK;QACZ,MAAMC,mBAAmB,GAAG,IAAI,CAAC3Q,aAAa,CAAClH,MAAM,CAAEkW,CAAC,IAAK,CAACA,CAAC,CAACC,MAAM,CAAC;QACvE,IAAI0B,mBAAmB,CAACpT,MAAM,KAAK,CAAC,EAAE;UACpC,IAAI,CAACO,YAAY,CAAC4I,QAAQ,CAAC,6BAA6B,CAAC;UACzD;;QAEF,MAAMkK,SAAS,GAAGD,mBAAmB,CAAClJ,GAAG,CAAEuH,CAAC,IAAKA,CAAC,CAAC9U,EAAE,CAAC;QACtD,IAAI,CAACkW,cAAc,CAACK,UAAU,CAACG,SAAS,EAAE,MAAK;UAC7C,IAAI,CAAC9S,YAAY,CAACkJ,WAAW,CAC3B,sDAAsD,CACvD;QACH,CAAC,CAAC;MACJ,CAAC;MACDyJ,UAAU,EAAEA,CAACI,GAAa,EAAEC,SAAqB,KAAI;QACnD,MAAMC,OAAO,GAAG,IAAI,CAACnV,cAAc,CAAC6U,UAAU,CAACI,GAAG,CAAC,CAAC1E,SAAS,CAAC;UAC5DC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAAChR,aAAa,GAAG,IAAI,CAACA,aAAa,CAACyH,GAAG,CAAEuH,CAAC,IAC5C6B,GAAG,CAACI,QAAQ,CAACjC,CAAC,CAAC9U,EAAE,CAAC,GAAG;cAAE,GAAG8U,CAAC;cAAEC,MAAM,EAAE,IAAI;cAAEiC,MAAM,EAAE,IAAI/M,IAAI;YAAE,CAAE,GAAG6K,CAAC,CACpE;YACD,IAAI,CAACe,uBAAuB,EAAE;YAC9Be,SAAS,EAAE;UACb,CAAC;UACDnP,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC7D,YAAY,CAACkO,SAAS,CACzB,2CAA2C,CAC5C;UACH;SACD,CAAC;QACF,IAAI,CAAChN,aAAa,CAACuQ,GAAG,CAACwB,OAAO,CAAC;MACjC;KACD;IAED,KAAAI,kBAAkB,GAAG,IAAI,CAACf,cAAc,CAACC,YAAY;IACrD,KAAAe,aAAa,GAAG,IAAI,CAAChB,cAAc,CAACM,OAAO;IAE3C;IACS,KAAAW,yBAAyB,GAAG;MACnCC,8BAA8B,EAAEA,CAAA,KAAK;QACnC,IAAI,IAAI,CAACrR,qBAAqB,CAACsR,IAAI,KAAK,CAAC,EAAE;UACzC,IAAI,CAACzT,YAAY,CAACgJ,WAAW,CAAC,kCAAkC,CAAC;UACjE;;QAEF,IAAI,CAAC3G,sBAAsB,GAAG,IAAI;MACpC,CAAC;MACDqR,cAAc,EAAEA,CAAA,KAAK;QACnB,MAAMlB,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvQ,qBAAqB,CAAC;QAC1D,IAAIqQ,WAAW,CAAC/S,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACgG,uBAAuB,GAAG,IAAI;QACnC,IAAI,CAACpD,sBAAsB,GAAG,KAAK;QACnC,MAAMsR,SAAS,GAAG,IAAI,CAAC7V,cAAc,CAAC8V,2BAA2B,CAC/DpB,WAAW,CACZ,CAACnE,SAAS,CAAC;UACVC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAAChR,aAAa,GAAG,IAAI,CAACA,aAAa,CAAClH,MAAM,CAC3CkW,CAAC,IAAK,CAACsB,WAAW,CAACW,QAAQ,CAACjC,CAAC,CAAC9U,EAAE,CAAC,CACnC;YACD,IAAI,CAAC+F,qBAAqB,CAAC4G,KAAK,EAAE;YAClC,IAAI,CAACkJ,uBAAuB,EAAE;YAC9B,IAAI,CAACxM,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACzF,YAAY,CAACkJ,WAAW,CAC3B,GAAGgK,MAAM,CAACrU,KAAK,+BAA+B,CAC/C;UACH,CAAC;UACDgF,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC4B,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACzF,YAAY,CAACkO,SAAS,CACzB,iDAAiD,CAClD;UACH;SACD,CAAC;QACF,IAAI,CAAChN,aAAa,CAACuQ,GAAG,CAACkC,SAAS,CAAC;MACnC,CAAC;MACDE,SAAS,EAAGvC,cAAsB,IAAI;QACpC,MAAMqC,SAAS,GAAG,IAAI,CAAC7V,cAAc,CAACgW,kBAAkB,CACtDxC,cAAc,CACf,CAACjD,SAAS,CAAC;UACVC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAAChR,aAAa,GAAG,IAAI,CAACA,aAAa,CAAClH,MAAM,CAC3CkW,CAAC,IAAKA,CAAC,CAAC9U,EAAE,KAAKkV,cAAc,CAC/B;YACD,IAAI,CAACnP,qBAAqB,CAACqP,MAAM,CAACF,cAAc,CAAC;YACjD,IAAI,CAACW,uBAAuB,EAAE;YAC9B,IAAI,CAACjS,YAAY,CAACkJ,WAAW,CAAC,wBAAwB,CAAC;UACzD,CAAC;UACDrF,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC7D,YAAY,CAACkO,SAAS,CACzB,kDAAkD,CACnD;UACH;SACD,CAAC;QACF,IAAI,CAAChN,aAAa,CAACuQ,GAAG,CAACkC,SAAS,CAAC;MACnC,CAAC;MACDI,SAAS,EAAEA,CAAA,KAAK;QACd,IAAI,IAAI,CAAC7R,aAAa,CAACzC,MAAM,KAAK,CAAC,EAAE;QACrC,IACE,CAACwJ,OAAO,CACN,8FAA8F,CAC/F,EACD;UACA;;QAEF,IAAI,CAACxD,uBAAuB,GAAG,IAAI;QACnC,MAAMuO,YAAY,GAChB,IAAI,CAAClW,cAAc,CAACmW,sBAAsB,EAAE,CAAC5F,SAAS,CAAC;UACrDC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAAChR,aAAa,GAAG,EAAE;YACvB,IAAI,CAACC,qBAAqB,CAAC4G,KAAK,EAAE;YAClC,IAAI,CAACkJ,uBAAuB,EAAE;YAC9B,IAAI,CAACxM,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACzF,YAAY,CAACkJ,WAAW,CAC3B,GAAGgK,MAAM,CAACrU,KAAK,uCAAuC,CACvD;UACH,CAAC;UACDgF,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC4B,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACzF,YAAY,CAACkO,SAAS,CACzB,2DAA2D,CAC5D;UACH;SACD,CAAC;QACJ,IAAI,CAAChN,aAAa,CAACuQ,GAAG,CAACuC,YAAY,CAAC;MACtC,CAAC;MACDvF,MAAM,EAAEA,CAAA,KAAO,IAAI,CAACpM,sBAAsB,GAAG;KAC9C;IAED,KAAAmR,8BAA8B,GAC5B,IAAI,CAACD,yBAAyB,CAACC,8BAA8B;IAC/D,KAAAU,2BAA2B,GAAG,IAAI,CAACX,yBAAyB,CAACG,cAAc;IAC3E,KAAAI,kBAAkB,GAAG,IAAI,CAACP,yBAAyB,CAACM,SAAS;IAC7D,KAAAI,sBAAsB,GAAG,IAAI,CAACV,yBAAyB,CAACQ,SAAS;IACjE,KAAAI,yBAAyB,GAAG,IAAI,CAACZ,yBAAyB,CAAC9E,MAAM;IAEjE;IACS,KAAA2F,uBAAuB,GAAG;MACjCxI,UAAU,EAAGyI,IAA+B,IAC1C,IAAI,CAACvW,cAAc,CAAClC,gBAAgB,CAACyY,IAAI,CAAC;MAC5CxN,OAAO,EAAGgD,IAAY,IACpB,IAAI,CAAClM,CAAC,CAACuE,aAAa,CAAC2H,IAAyC,CAAC,EAAE3L,IAAI,IACrE,aAAa;MACfoW,QAAQ,EAAGzK,IAAY,IACrB,IAAI,CAAClM,CAAC,CAACuE,aAAa,CAAC2H,IAAyC,CAAC,EAAE1F,KAAK,IACtE,eAAe;MACjBL,SAAS,EAAEA,CAACyQ,KAAa,EAAEC,YAAiB,KAC1C,IAAI,CAAC7W,CAAC,CAACmG,SAAS,CAAC,CAAC,EAAE0Q,YAAY;KACnC;IAED,KAAAC,sBAAsB,GAAG,IAAI,CAACL,uBAAuB,CAACxI,UAAU;IAChE,KAAA8I,mBAAmB,GAAG,IAAI,CAACN,uBAAuB,CAACvN,OAAO;IAC1D,KAAA8N,oBAAoB,GAAG,IAAI,CAACP,uBAAuB,CAACE,QAAQ;IAC5D,KAAAM,qBAAqB,GAAG,IAAI,CAACR,uBAAuB,CAACtQ,SAAS;IA6K9D;IACS,KAAA+Q,YAAY,GAAG;MACtBC,eAAe,EAAEA,CAAA,KAAK;QACpB,MAAMC,MAAM,GAAG,EAAE;QACjB,IAAI,IAAI,CAACtT,mBAAmB,EAC1BsT,MAAM,CAACC,IAAI,CAAC;UACVlQ,GAAG,EAAE,YAAY;UACjB3G,KAAK,EAAE,cAAc;UACrBD,IAAI,EAAE,cAAc;UACpB+W,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACxT,mBAAmB,GAAG;SAChD,CAAC;QACJ,IAAI,IAAI,CAACtC,oBAAoB,EAC3B4V,MAAM,CAACC,IAAI,CAAC;UACVlQ,GAAG,EAAE,aAAa;UAClB3G,KAAK,EAAE,uBAAuB;UAC9BD,IAAI,EAAE,gBAAgB;UACtB+W,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC9V,oBAAoB,GAAG;SACjD,CAAC;QACJ,IAAI,IAAI,CAACE,kBAAkB,EACzB0V,MAAM,CAACC,IAAI,CAAC;UACVlQ,GAAG,EAAE,WAAW;UAChB3G,KAAK,EAAE,uBAAuB;UAC9BD,IAAI,EAAE,kBAAkB;UACxB+W,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC5V,kBAAkB,GAAG;SAC/C,CAAC;QACJ,IAAI,IAAI,CAACE,sBAAsB,EAC7BwV,MAAM,CAACC,IAAI,CAAC;UACVlQ,GAAG,EAAE,eAAe;UACpB3G,KAAK,EAAE,iBAAiB;UACxBD,IAAI,EAAE,mBAAmB;UACzB+W,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC1V,sBAAsB,GAAG;SACnD,CAAC;QACJ,OAAOwV,MAAM;MACf,CAAC;MACDG,gBAAgB,EAAEA,CAAA,KAChBC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACzX,CAAC,CAAC2G,MAAM,CAAC,CAACqF,GAAG,CAAC,CAAC,CAAC7E,GAAG,EAAEuQ,MAAM,CAAC,MAAM;QACpDvQ,GAAG;QACH,GAAGuQ;OACJ,CAAC,CAAC;MACLC,eAAe,EAAEA,CAAA,KAAM,IAAI,CAAC3X,CAAC,CAACkH;KAC/B;IAED,KAAAiQ,eAAe,GAAG,IAAI,CAACD,YAAY,CAACC,eAAe;IACnD,KAAAI,gBAAgB,GAAG,IAAI,CAACL,YAAY,CAACK,gBAAgB;IACrD,KAAAI,eAAe,GAAG,IAAI,CAACT,YAAY,CAACS,eAAe;IAEnD;IACS,KAAAC,aAAa,GAAG;MACvBC,OAAO,EAAGlR,MAAc,IACtB,IAAI,CAAC3G,CAAC,CAAC2G,MAAM,CAACA,MAAoC,CAAC,EAAEE,IAAI,IAAI,SAAS;MACxE8P,QAAQ,EAAGhQ,MAAc,IACvB,IAAI,CAAC3G,CAAC,CAAC2G,MAAM,CAACA,MAAoC,CAAC,EAAEH,KAAK,IAC1D,eAAe;MACjB0C,OAAO,EAAGvC,MAAc,IACtB,IAAI,CAAC3G,CAAC,CAAC2G,MAAM,CAACA,MAAoC,CAAC,EAAEpG,IAAI,IACzD;KACH;IAED,KAAAuX,aAAa,GAAG,IAAI,CAACF,aAAa,CAACC,OAAO;IAC1C,KAAAE,cAAc,GAAG,IAAI,CAACH,aAAa,CAACjB,QAAQ;IAC5C,KAAAqB,aAAa,GAAG,IAAI,CAACJ,aAAa,CAAC1O,OAAO;IAE1C;IACS,KAAA+O,cAAc,GAAG;MACxBC,cAAc,EAAGha,UAAuB,IACtCA,UAAU,GACN,IAAI,CAACiC,cAAc,CAAClC,gBAAgB,CAACC,UAAU,CAAC,GAChD,WAAW;MACjBia,mBAAmB,EAAEA,CAAA,KACnBrD,KAAK,CAACC,IAAI,CAAC,IAAI,CAACzM,WAAW,CAAC8P,MAAM,EAAE,CAAC,CAAC/a,MAAM,CAAEgb,IAAI,IAAKA,IAAI,CAACra,QAAQ,CAAC,CAClE8D,MAAM;MACXwW,gBAAgB,EAAEA,CAAA,KAAMxD,KAAK,CAACC,IAAI,CAAC,IAAI,CAACzM,WAAW,CAAC8P,MAAM,EAAE,CAAC;MAC7DG,eAAe,EAAGlb,MAAc,IAAM,IAAI,CAAC2L,gBAAgB,GAAG3L;KAC/D;IAED,KAAA6a,cAAc,GAAG,IAAI,CAACD,cAAc,CAACC,cAAc;IACnD,KAAAC,mBAAmB,GAAG,IAAI,CAACF,cAAc,CAACE,mBAAmB;IAC7D,KAAAI,eAAe,GAAG,IAAI,CAACN,cAAc,CAACM,eAAe;IACrD,KAAAD,gBAAgB,GAAG,IAAI,CAACL,cAAc,CAACK,gBAAgB;IAEvD;IACS,KAAAE,mBAAmB,GAAG;MAC7BC,UAAU,EAAG7H,OAAY,IAAM,IAAI,CAAC/L,iBAAiB,GAAG+L,OAAQ;MAChE8H,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC7T,iBAAiB,GAAG,IAAK;MAClD8T,gBAAgB,EAAG/H,OAAY,IAAI;QACjC,IAAI,CAACpL,iBAAiB,GAAGoL,OAAO;QAChC,IAAI,CAACrL,gBAAgB,GAAG,IAAI;MAC9B,CAAC;MACDqT,iBAAiB,EAAEA,CAAA,KAAK;QACtB,IAAI,CAACrT,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,qBAAqB,GAAG,EAAE;MACjC;KACD;IAED,KAAAoT,mBAAmB,GAAG,IAAI,CAACL,mBAAmB,CAACC,UAAU;IACzD,KAAAC,WAAW,GAAG,IAAI,CAACF,mBAAmB,CAACE,WAAW;IAClD,KAAAC,gBAAgB,GAAG,IAAI,CAACH,mBAAmB,CAACG,gBAAgB;IAC5D,KAAAC,iBAAiB,GAAG,IAAI,CAACJ,mBAAmB,CAACI,iBAAiB;IAE9D;IACS,KAAAE,cAAc,GAAG;MACxBC,UAAU,EAAGnI,OAAY,IACvB,IAAI,CAACoI,eAAe,CAACpI,OAAO,CAAC,GAAG,kBAAkB,GAAG,kBAAkB;MACzEqI,iBAAiB,EAAGrI,OAAY,IAC9B,IAAI,CAACoI,eAAe,CAACpI,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;MAC5DsI,cAAc,EAAGtI,OAAY,IAAKA,OAAO,CAACpS,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACE,aAAa;MAC3Eqa,eAAe,EAAGpI,OAAY,IAAKA,OAAO,CAACuI,QAAQ,IAAI;KACxD;IAED,KAAAJ,UAAU,GAAG,IAAI,CAACD,cAAc,CAACC,UAAU;IAC3C,KAAAE,iBAAiB,GAAG,IAAI,CAACH,cAAc,CAACG,iBAAiB;IACzD,KAAAC,cAAc,GAAG,IAAI,CAACJ,cAAc,CAACI,cAAc;IACnD,KAAAF,eAAe,GAAG,IAAI,CAACF,cAAc,CAACE,eAAe;IAErD;IACS,KAAAI,WAAW,GAAG;MACrBC,gBAAgB,EAAGzI,OAAY,IAAI;QACjC,IAAI,CAACjM,gBAAgB,GAAGiM,OAAO,CAACnS,EAAE;QAClC,IAAI,CAACmG,cAAc,GAAGgM,OAAO,CAAChS,OAAO;MACvC,CAAC;MACD0a,iBAAiB,EAAEA,CAAA,KAAK;QACtB,IAAI,CAAC3U,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACC,cAAc,GAAG,EAAE;MAC1B,CAAC;MACD2U,eAAe,EAAGC,SAAiB,IACjC,IAAI,CAACJ,WAAW,CAACE,iBAAiB,EAAE;MACtCG,cAAc,EAAEA,CAACC,KAAoB,EAAEF,SAAiB,KAAI;QAC1D,IAAIE,KAAK,CAACvS,GAAG,KAAK,OAAO,IAAI,CAACuS,KAAK,CAACC,QAAQ,EAAE;UAC5CD,KAAK,CAACE,cAAc,EAAE;UACtB,IAAI,CAACR,WAAW,CAACG,eAAe,CAACC,SAAS,CAAC;SAC5C,MAAM,IAAIE,KAAK,CAACvS,GAAG,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACiS,WAAW,CAACE,iBAAiB,EAAE;;MAExC;KACD;IAED,KAAAD,gBAAgB,GAAG,IAAI,CAACD,WAAW,CAACC,gBAAgB;IACpD,KAAAC,iBAAiB,GAAG,IAAI,CAACF,WAAW,CAACE,iBAAiB;IACtD,KAAAC,eAAe,GAAG,IAAI,CAACH,WAAW,CAACG,eAAe;IAClD,KAAAE,cAAc,GAAG,IAAI,CAACL,WAAW,CAACK,cAAc;IAUhD;IACS,KAAAI,aAAa,GAAG;MACvBC,kBAAkB,EAAGnT,MAAc,IACjC,IAAI,CAAC3G,CAAC,CAAC0H,gBAAgB,CAACf,MAA8C,CAAC,IACvE,eAAe;MACjBoT,eAAe,EAAG7N,IAAY,IAC5BA,IAAI,KAAK,OAAO,GAAG,cAAc,GAAG,cAAc;MACpD8N,kBAAkB,EAAGC,QAAgB,IAAI;QACvC,IAAI,CAACA,QAAQ,EAAE,OAAO,OAAO;QAC7B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAG,EAAE,CAAC;QACzC,MAAMI,OAAO,GAAGJ,QAAQ,GAAG,EAAE;QAC7B,OAAO,GAAGC,OAAO,CAAC5T,QAAQ,EAAE,CAACgU,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CACrD/T,QAAQ,EAAE,CACVgU,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACvB,CAAC;MACDC,cAAc,EAAGzb,SAAoC,IACnD,IAAI,CAACqB,cAAc,CAAC+N,iBAAiB,CAACpP,SAAS;KAClD;IAED,KAAAgb,kBAAkB,GAAG,IAAI,CAACD,aAAa,CAACC,kBAAkB;IAC1D,KAAAC,eAAe,GAAG,IAAI,CAACF,aAAa,CAACE,eAAe;IACpD,KAAAC,kBAAkB,GAAG,IAAI,CAACH,aAAa,CAACG,kBAAkB;IAC1D,KAAAO,cAAc,GAAG,IAAI,CAACV,aAAa,CAACU,cAAc;IAElD;IACS,KAAAC,YAAY,GAAG;MACtBC,eAAe,EAAGf,KAAY,IAAI;QAChC,MAAMgB,MAAM,GAAGhB,KAAK,CAACgB,MAAqB;QAC1C,MAAMC,YAAY,GAAG,CACnB;UACEC,SAAS,EAAE,CAAC,sBAAsB,EAAE,YAAY,CAAC;UACjDC,QAAQ,EAAE;SACX,EACD;UACED,SAAS,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC;UAC1CC,QAAQ,EAAE;SACX,CACF;QAEDF,YAAY,CAACxG,OAAO,CAAEuD,MAAM,IAAI;UAC9B,MAAMoD,cAAc,GAAG,CAACpD,MAAM,CAACkD,SAAS,CAACG,IAAI,CAAEC,QAAQ,IACrDN,MAAM,CAACO,OAAO,CAACD,QAAQ,CAAC,CACzB;UACD,IAAIF,cAAc,EAAE;YACjB,IAAY,CAACpD,MAAM,CAACmD,QAAQ,CAAC,GAAG,KAAK;;QAE1C,CAAC,CAAC;MACJ,CAAC;MACDK,oBAAoB,EAAG1B,SAAiB,IAAI;QAC1C,IAAI,CAACrU,iBAAiB,CAACqU,SAAS,CAAC,GAAG,KAAK;MAC3C;KACD;IAED,KAAAiB,eAAe,GAAG,IAAI,CAACD,YAAY,CAACC,eAAe;IACnD,KAAAS,oBAAoB,GAAG,IAAI,CAACV,YAAY,CAACU,oBAAoB;IAE7D;IACS,KAAAC,eAAe,GAAG;MACzBC,kBAAkB,EAAGxK,OAAY,IAAKA,OAAO,CAAC3Q,SAAS,IAAI,EAAE;MAC7Dob,eAAe,EAAEA,CAAC7B,SAAiB,EAAE8B,KAAa,KAAI;QACpD;MAAA,CACD;MACDC,cAAc,EAAEA,CAAC3K,OAAY,EAAE0K,KAAa,KAAK;KAClD;IAED,KAAAF,kBAAkB,GAAG,IAAI,CAACD,eAAe,CAACC,kBAAkB;IAC5D,KAAAC,eAAe,GAAG,IAAI,CAACF,eAAe,CAACE,eAAe;IACtD,KAAAE,cAAc,GAAG,IAAI,CAACJ,eAAe,CAACI,cAAc;IAEpD;IACS,KAAAC,4BAA4B,GAAG;MACtCpH,cAAc,EAAEA,CAAA,KACd,IAAI,CAAC3O,qBAAqB,CAAC3D,MAAM,KAAK,IAAI,CAAC4D,sBAAsB,CAAC5D,MAAM;MAC1E2Z,SAAS,EAAEA,CAAA,KACR,IAAI,CAAChW,qBAAqB,GAAG,IAAI,CAACC,sBAAsB,CAACsG,GAAG,CAC1DhM,CAAC,IAAKA,CAAC,CAACvB,EAAE,CACX;MACJid,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACjW,qBAAqB,GAAG,EAAG;MACpD2K,MAAM,EAAGuL,cAAsB,IAAI;QACjC,MAAM/E,KAAK,GAAG,IAAI,CAACnR,qBAAqB,CAACmW,OAAO,CAACD,cAAc,CAAC;QAChE/E,KAAK,GAAG,CAAC,CAAC,GACN,IAAI,CAACnR,qBAAqB,CAACoW,MAAM,CAACjF,KAAK,EAAE,CAAC,CAAC,GAC3C,IAAI,CAACnR,qBAAqB,CAAC4R,IAAI,CAACsE,cAAc,CAAC;MACrD,CAAC;MACDG,UAAU,EAAGH,cAAsB,IACjC,IAAI,CAAClW,qBAAqB,CAAC+P,QAAQ,CAACmG,cAAc,CAAC;MACrDI,eAAe,EAAGvZ,YAAiB,IACjCA,YAAY,CAACwZ,KAAK,IAAI,kCAAkC;MAC1DC,cAAc,EAAGzZ,YAAiB,IAAKA,YAAY,CAACqJ,IAAI,IAAI,cAAc;MAC1EqQ,cAAc,EAAEA,CAAA,KAAK;QACnB,IAAI,CAACvW,YAAY,GAAG,IAAI;QACxBwW,UAAU,CAAC,MAAK;UACd,IAAI,CAACxW,YAAY,GAAG,KAAK;UACzB,IAAI,CAACiT,iBAAiB,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV;KACD;IAED,KAAAwD,2BAA2B,GACzB,IAAI,CAACZ,4BAA4B,CAACpH,cAAc;IAClD,KAAAiI,sBAAsB,GAAG,IAAI,CAACb,4BAA4B,CAACC,SAAS;IACpE,KAAAa,wBAAwB,GAAG,IAAI,CAACd,4BAA4B,CAACE,WAAW;IACxE,KAAAa,2BAA2B,GAAG,IAAI,CAACf,4BAA4B,CAACpL,MAAM;IACtE,KAAAoM,sBAAsB,GAAG,IAAI,CAAChB,4BAA4B,CAACM,UAAU;IACrE,KAAAW,2BAA2B,GACzB,IAAI,CAACjB,4BAA4B,CAACO,eAAe;IACnD,KAAAW,0BAA0B,GAAG,IAAI,CAAClB,4BAA4B,CAACS,cAAc;IAC7E,KAAAC,cAAc,GAAG,IAAI,CAACV,4BAA4B,CAACU,cAAc;IAEjE;IACS,KAAAS,yBAAyB,GAAG;MACnCC,eAAe,EAAEA,CAAA,KAAO,IAAI,CAACvU,gBAAgB,GAAG,IAAK;MACrDwU,wBAAwB,EAAEA,CAAA,KAAK,CAAE,CAAC;MAClCC,qBAAqB,EAAGzf,MAAc,IACnC,IAAI,CAACsK,kBAAkB,GAAGtK;KAC9B;IAED,KAAAuf,eAAe,GAAG,IAAI,CAACD,yBAAyB,CAACC,eAAe;IAChE,KAAAC,wBAAwB,GACtB,IAAI,CAACF,yBAAyB,CAACE,wBAAwB;IACzD,KAAAC,qBAAqB,GAAG,IAAI,CAACH,yBAAyB,CAACG,qBAAqB;IAE5E;IACS,KAAAC,aAAa,GAAG;MACvBC,OAAO,EAAGtD,KAAU,IAAI;QACtB,IAAI,CAACja,WAAW,GAAGia,KAAK,CAACgB,MAAM,CAACuC,KAAK;QACrC,IAAI,CAACxd,WAAW,CAACqC,MAAM,IAAI,CAAC,GACxB,IAAI,CAACib,aAAa,CAACG,OAAO,EAAE,GAC5B,IAAI,CAACH,aAAa,CAAC3R,KAAK,EAAE;MAChC,CAAC;MACD+R,UAAU,EAAGzD,KAAoB,IAAI;QACnC,IAAIA,KAAK,CAACvS,GAAG,KAAK,OAAO,EAAE;UACzB,IAAI,CAAC4V,aAAa,CAACG,OAAO,EAAE;SAC7B,MAAM,IAAIxD,KAAK,CAACvS,GAAG,KAAK,QAAQ,EAAE;UACjC,IAAI,CAAC4V,aAAa,CAAC3R,KAAK,EAAE;;MAE9B,CAAC;MACD8R,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACpY,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,UAAU,GAAG,IAAI;QACtBoX,UAAU,CAAC,MAAK;UACd,IAAI,CAACnX,aAAa,GAAG,IAAI,CAACzC,QAAQ,CAAClF,MAAM,CAAEgR,CAAC,IAC1CA,CAAC,CAACzP,OAAO,EAAEwO,WAAW,EAAE,CAACoI,QAAQ,CAAC,IAAI,CAAC/V,WAAW,CAAC2N,WAAW,EAAE,CAAC,CAClE;UACD,IAAI,CAACtI,WAAW,GAAG,KAAK;QAC1B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDsG,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC3L,WAAW,GAAG,EAAE;QACrB,IAAI,CAACuF,aAAa,GAAG,EAAE;QACvB,IAAI,CAACF,WAAW,GAAG,KAAK;QACxB,IAAI,CAACC,UAAU,GAAG,KAAK;MACzB;KACD;IAED,KAAAqY,aAAa,GAAG,IAAI,CAACL,aAAa,CAACC,OAAO;IAC1C,KAAAK,gBAAgB,GAAG,IAAI,CAACN,aAAa,CAACI,UAAU;IAChD,KAAAG,aAAa,GAAG,IAAI,CAACP,aAAa,CAACG,OAAO;IAC1C,KAAA9S,WAAW,GAAG,IAAI,CAAC2S,aAAa,CAAC3R,KAAK;IAEtC;IACS,KAAAmS,mBAAmB,GAAG;MAC7BC,iBAAiB,EAAGhE,SAAiB,IAAI;QACvC;MAAA,CACD;MACDiE,qBAAqB,EAAGjE,SAAiB,IAAI;QAC3C;MAAA,CACD;MACD1Y,sBAAsB,EAAEA,CAAA,KAAM,IAAI,CAACmE,cAAc,CAACnD;KACnD;IAED,KAAA0b,iBAAiB,GAAG,IAAI,CAACD,mBAAmB,CAACC,iBAAiB;IAC9D,KAAAC,qBAAqB,GAAG,IAAI,CAACF,mBAAmB,CAACE,qBAAqB;IACtE,KAAA3c,sBAAsB,GAAG,IAAI,CAACyc,mBAAmB,CAACzc,sBAAsB;IAExE;IACS,KAAA4c,wBAAwB,GAAG;MAClCC,oBAAoB,EAAEA,CAAC/e,OAAe,EAAEgf,KAAa,KAAI;QACvD,IAAI,CAACA,KAAK,EAAE,OAAOhf,OAAO;QAC1B,MAAMif,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIF,KAAK,GAAG,EAAE,IAAI,CAAC;QAC5C,OAAOhf,OAAO,CAACuO,OAAO,CAAC0Q,KAAK,EAAE,iBAAiB,CAAC;MAClD,CAAC;MACDE,oBAAoB,EAAGvE,SAAiB,IACrC,IAAI,CAACtU,kBAAkB,CAACsU,SAAS,CAAC,GACjC,CAAC,IAAI,CAACtU,kBAAkB,CAACsU,SAAS,CAAE;MACxCwE,cAAc,EAAEA,CAACxE,SAAiB,EAAE8B,KAAa,KAC9C,IAAI,CAACpW,kBAAkB,CAACsU,SAAS,CAAC,GAAG,KAAM;MAC9CyE,oBAAoB,EAAGzE,SAAiB,IACrC,IAAI,CAAClU,kBAAkB,CAACkU,SAAS,CAAC,GACjC,CAAC,IAAI,CAAClU,kBAAkB,CAACkU,SAAS;KACvC;IAED,KAAAmE,oBAAoB,GAAG,IAAI,CAACD,wBAAwB,CAACC,oBAAoB;IACzE,KAAAI,oBAAoB,GAAG,IAAI,CAACL,wBAAwB,CAACK,oBAAoB;IACzE,KAAAC,cAAc,GAAG,IAAI,CAACN,wBAAwB,CAACM,cAAc;IAC7D,KAAAC,oBAAoB,GAAG,IAAI,CAACP,wBAAwB,CAACO,oBAAoB;IAEzE;IACS,KAAAC,mBAAmB,GAAG;MAC7BC,mBAAmB,EAAG3E,SAAiB,IACpC,IAAI,CAACpU,cAAc,CAACoU,SAAS,CAAC,GAAG,IAAK;MACzC4E,qBAAqB,EAAG5E,SAAiB,IACtC,IAAI,CAACpU,cAAc,CAACoU,SAAS,CAAC,GAAG,KAAM;MAC1C6E,sBAAsB,EAAG7E,SAAiB,IACvC,IAAI,CAACrU,iBAAiB,CAACqU,SAAS,CAAC,GAAG,IAAK;MAC5C8E,mBAAmB,EAAG9E,SAAiB,IACpC,IAAI,CAACrU,iBAAiB,CAACqU,SAAS,CAAC,GAAG;KACxC;IAED,KAAA2E,mBAAmB,GAAG,IAAI,CAACD,mBAAmB,CAACC,mBAAmB;IAClE,KAAAC,qBAAqB,GAAG,IAAI,CAACF,mBAAmB,CAACE,qBAAqB;IACtE,KAAAC,sBAAsB,GAAG,IAAI,CAACH,mBAAmB,CAACG,sBAAsB;IACxE,KAAAC,mBAAmB,GAAG,IAAI,CAACJ,mBAAmB,CAACI,mBAAmB;IAElE;IACS,KAAAC,OAAO,GAAG;MACjBC,aAAa,EAAEA,CAAA,KACb,CAAC,IAAI,CAACC,aAAa,EAAE,IAAI,CAAC9V,eAAe,EAAE,IAAI,CAACrE,SAAS,CAAC,CAAC6P,OAAO,CAC/DhL,CAAC,IAAKA,CAAC,IAAIuV,YAAY,CAACvV,CAAC,CAAC,CAC5B;MACHwV,cAAc,EAAEA,CAAA,KACd,IAAI,CAAChgB,aAAa,IAClB,IAAI,CAACwB,cAAc,CAACwe,cAAc,CAAC,IAAI,CAAChgB,aAAa,CAAC,CAAC+R,SAAS,EAAE;MACpEkO,mBAAmB,EAAEA,CAAA,KAAK;QACxB,IAAI,IAAI,CAACrV,iBAAiB,IAAI,IAAI,CAAC/G,YAAY,EAAE/D,EAAE,EAAE;UACnD,IAAI,CAAC8K,iBAAiB,GAAG,KAAK;UAC9BmV,YAAY,CAAC,IAAI,CAACG,WAAW,CAAC;UAC9B,IAAI,CAAC1e,cAAc,CAAC2e,UAAU,CAAC,IAAI,CAACtc,YAAY,CAAC/D,EAAE,CAAC,CAACiS,SAAS,CAAC;YAC7DC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;YACdzK,KAAK,EAAGA,KAAK,IAAI,CAAE;WACpB,CAAC;;MAEN;KACD;IA/3DC,IAAI,CAAC6Y,WAAW,GAAG,IAAI,CAAC7c,EAAE,CAAC8c,KAAK,CAAC;MAC/BpgB,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAACkiB,SAAS,CAAC,IAAI,CAAC,CAAC;KAC3C,CAAC;EACJ;EACAC,QAAQA,CAAA;IACN,IAAI,CAACvgB,aAAa,GAAG,IAAI,CAACsD,WAAW,CAACkd,gBAAgB,EAAE;IAExD,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,IAAIF,UAAU,EAAE;MACd,IAAI,CAAC5b,aAAa,GAAG4b,UAAU;;IAGjC,IAAI,CAACG,wBAAwB,EAAE;IAC/B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,qBAAqB,EAAE;IAE5B3S,QAAQ,CAAC4S,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAClF,eAAe,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;IAEnE,MAAMC,QAAQ,GAAG,IAAI,CAAC7d,KAAK,CAAC8d,MAAM,CAC/BC,IAAI,CACH1iB,MAAM,CAAEyiB,MAAM,IAAKA,MAAM,CAAC,IAAI,CAAC,CAAC,EAChC1iB,oBAAoB,EAAE,EACtBD,SAAS,CAAE2iB,MAAM,IAAI;MACnB,IAAI,CAACrd,OAAO,GAAG,IAAI;MACnB,IAAI,CAACF,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACa,WAAW,GAAG,CAAC;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI;MAE3B,OAAO,IAAI,CAACnD,cAAc,CAAC6f,eAAe,CACxCF,MAAM,CAAC,IAAI,CAAC,EACZ,IAAI,CAAC5c,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB;IACH,CAAC,CAAC,CACH,CACAsN,SAAS,CAAC;MACTC,IAAI,EAAGnO,YAAY,IAAI;QACrB,IAAI,CAACyd,wBAAwB,CAACzd,YAAY,CAAC;MAC7C,CAAC;MACD0D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACga,WAAW,CAAC,6BAA6B,EAAEha,KAAK,CAAC;MACxD;KACD,CAAC;IACJ,IAAI,CAAC3C,aAAa,CAACuQ,GAAG,CAAC+L,QAAQ,CAAC;EAClC;EAEA;EACQK,WAAWA,CACjBtP,OAAe,EACf1K,KAAU,EACVia,YAAA,GAAwB,IAAI;IAE5B9N,OAAO,CAACnM,KAAK,CAAC,aAAa,EAAE0K,OAAO,EAAE1K,KAAK,CAAC;IAC5C,IAAIia,YAAY,EAAE;MAChB,IAAI,CAAC1d,OAAO,GAAG,KAAK;MACpB,IAAI,CAACI,WAAW,GAAG,KAAK;MACxB,IAAI,CAACQ,aAAa,GAAG,KAAK;;IAE5B,IAAI,CAAC6C,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC7D,YAAY,CAACkO,SAAS,CAACK,OAAO,CAAC;EACtC;EAEA;EACQwP,aAAaA,CAACxP,OAAgB,EAAEyP,QAAqB;IAC3D,IAAIzP,OAAO,EAAE;MACX,IAAI,CAACvO,YAAY,CAACkJ,WAAW,CAACqF,OAAO,CAAC;;IAExC,IAAIyP,QAAQ,EAAE;MACZA,QAAQ,EAAE;;EAEd;EAWQJ,wBAAwBA,CAACzd,YAA0B;IACzD,IAAI,CAACA,YAAY,GAAGA,YAAY;IAEhC,IAAI,CAACA,YAAY,EAAED,QAAQ,IAAIC,YAAY,CAACD,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;MACjE,IAAI,CAACjE,gBAAgB,GACnB2E,YAAY,EAAEsJ,YAAY,EAAEwU,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAAC9hB,EAAE,KAAK,IAAI,CAACE,aAAa,IAAI4hB,CAAC,CAACC,GAAG,KAAK,IAAI,CAAC7hB,aAAa,CACnE,IAAI,IAAI;MACX,IAAI,CAAC4D,QAAQ,GAAG,EAAE;KACnB,MAAM;MACL,MAAMke,oBAAoB,GAAG,CAAC,IAAIje,YAAY,EAAED,QAAQ,IAAI,EAAE,CAAC,CAAC;MAEhEke,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACjC,MAAMC,KAAK,GACTF,CAAC,CAAC7hB,SAAS,YAAY4J,IAAI,GACvBiY,CAAC,CAAC7hB,SAAS,CAACgiB,OAAO,EAAE,GACrB,IAAIpY,IAAI,CAACiY,CAAC,CAAC7hB,SAAmB,CAAC,CAACgiB,OAAO,EAAE;QAC/C,MAAMC,KAAK,GACTH,CAAC,CAAC9hB,SAAS,YAAY4J,IAAI,GACvBkY,CAAC,CAAC9hB,SAAS,CAACgiB,OAAO,EAAE,GACrB,IAAIpY,IAAI,CAACkY,CAAC,CAAC9hB,SAAmB,CAAC,CAACgiB,OAAO,EAAE;QAC/C,OAAOD,KAAK,GAAGE,KAAK;MACtB,CAAC,CAAC;MAEF,IAAI,CAACxe,QAAQ,GAAGke,oBAAoB;;IAGtC,IAAI,CAAC5iB,gBAAgB,GACnB2E,YAAY,EAAEsJ,YAAY,EAAEwU,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAAC9hB,EAAE,KAAK,IAAI,CAACE,aAAa,IAAI4hB,CAAC,CAACC,GAAG,KAAK,IAAI,CAAC7hB,aAAa,CACnE,IAAI,IAAI;IAEX,IAAI,CAAC8D,OAAO,GAAG,KAAK;IACpB0Z,UAAU,CAAC,MAAM,IAAI,CAACtL,cAAc,EAAE,EAAE,GAAG,CAAC;IAE5C,IAAI,CAACmQ,kBAAkB,EAAE;IAEzB,IAAI,IAAI,CAACxe,YAAY,EAAE/D,EAAE,EAAE;MACzB,IAAI,CAACwiB,8BAA8B,CAAC,IAAI,CAACze,YAAY,CAAC/D,EAAE,CAAC;MACzD,IAAI,CAACyiB,sBAAsB,CAAC,IAAI,CAAC1e,YAAY,CAAC/D,EAAE,CAAC;MACjD,IAAI,CAAC0iB,2BAA2B,CAAC,IAAI,CAAC3e,YAAY,CAAC/D,EAAE,CAAC;;EAE1D;EAEQwiB,8BAA8BA,CAACtF,cAAsB;IAC3D,MAAM/J,GAAG,GAAG,IAAI,CAACzR,cAAc,CAAC8gB,8BAA8B,CAC5DtF,cAAc,CACf,CAACjL,SAAS,CAAC;MACVC,IAAI,EAAGyQ,mBAAmB,IAAI;QAC5B,IAAI,CAAC5e,YAAY,GAAG4e,mBAAmB;QACvC,IAAI,CAAC7e,QAAQ,GAAG6e,mBAAmB,CAAC7e,QAAQ,GACxC,CAAC,GAAG6e,mBAAmB,CAAC7e,QAAQ,CAAC,GACjC,EAAE;QACN,IAAI,CAACsO,cAAc,EAAE;MACvB,CAAC;MACD3K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7D,YAAY,CAACkO,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;IACF,IAAI,CAAChN,aAAa,CAACuQ,GAAG,CAAClC,GAAG,CAAC;EAC7B;EAEQsP,sBAAsBA,CAACvF,cAAsB;IACnD,MAAM/J,GAAG,GAAG,IAAI,CAACzR,cAAc,CAAC+gB,sBAAsB,CACpDvF,cAAc,CACf,CAACjL,SAAS,CAAC;MACVC,IAAI,EAAG0Q,UAAU,IAAI;QACnB,IAAIA,UAAU,EAAE1F,cAAc,KAAK,IAAI,CAACnZ,YAAY,EAAE/D,EAAE,EAAE;UACxD,IAAI,CAAC8D,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE8e,UAAU,CAAC,CAACX,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YAC3D,MAAMC,KAAK,GACTF,CAAC,CAAC7hB,SAAS,YAAY4J,IAAI,GACvBiY,CAAC,CAAC7hB,SAAS,CAACgiB,OAAO,EAAE,GACrB,IAAIpY,IAAI,CAACiY,CAAC,CAAC7hB,SAAmB,CAAC,CAACgiB,OAAO,EAAE;YAC/C,MAAMC,KAAK,GACTH,CAAC,CAAC9hB,SAAS,YAAY4J,IAAI,GACvBkY,CAAC,CAAC9hB,SAAS,CAACgiB,OAAO,EAAE,GACrB,IAAIpY,IAAI,CAACkY,CAAC,CAAC9hB,SAAmB,CAAC,CAACgiB,OAAO,EAAE;YAC/C,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEF5E,UAAU,CAAC,MAAM,IAAI,CAACtL,cAAc,EAAE,EAAE,GAAG,CAAC;UAE5C,IACEwQ,UAAU,CAAC7iB,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACE,aAAa,IAC5C0iB,UAAU,CAAC7iB,MAAM,EAAEgiB,GAAG,KAAK,IAAI,CAAC7hB,aAAa,EAC7C;YACA,IAAI0iB,UAAU,CAAC5iB,EAAE,EAAE;cACjB,IAAI,CAAC0B,cAAc,CAACmhB,iBAAiB,CAACD,UAAU,CAAC5iB,EAAE,CAAC,CAACiS,SAAS,EAAE;;;;MAIxE,CAAC;MACDxK,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7D,YAAY,CAACkO,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;IACF,IAAI,CAAChN,aAAa,CAACuQ,GAAG,CAAClC,GAAG,CAAC;EAC7B;EAEQuP,2BAA2BA,CAACxF,cAAsB;IACxD,MAAM/J,GAAG,GAAG,IAAI,CAACzR,cAAc,CAACohB,0BAA0B,CACxD5F,cAAc,CACf,CAACjL,SAAS,CAAC;MACVC,IAAI,EAAG+I,KAAK,IAAI;QACd,IAAIA,KAAK,CAAC8H,MAAM,KAAK,IAAI,CAAC7iB,aAAa,EAAE;UACvC,IAAI,CAACmE,QAAQ,GAAG4W,KAAK,CAAC5W,QAAQ;UAC9B,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB4b,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;YAChC,IAAI,CAACA,aAAa,GAAGtC,UAAU,CAAC,MAAK;cACnC,IAAI,CAACrZ,QAAQ,GAAG,KAAK;YACvB,CAAC,EAAE,IAAI,CAAC;;;MAGd;KACD,CAAC;IACF,IAAI,CAACS,aAAa,CAACuQ,GAAG,CAAClC,GAAG,CAAC;EAC7B;EAEQoP,kBAAkBA,CAAA;IACxB,MAAMS,cAAc,GAAG,IAAI,CAAClf,QAAQ,CAAClF,MAAM,CACxC4O,GAAG,IACF,CAACA,GAAG,CAACuH,MAAM,KACVvH,GAAG,CAACyV,QAAQ,EAAEjjB,EAAE,KAAK,IAAI,CAACE,aAAa,IACtCsN,GAAG,CAACyV,QAAQ,EAAElB,GAAG,KAAK,IAAI,CAAC7hB,aAAa,CAAC,CAC9C;IAED8iB,cAAc,CAACtN,OAAO,CAAElI,GAAG,IAAI;MAC7B,IAAIA,GAAG,CAACxN,EAAE,EAAE;QACV,MAAMmT,GAAG,GAAG,IAAI,CAACzR,cAAc,CAACmhB,iBAAiB,CAACrV,GAAG,CAACxN,EAAE,CAAC,CAACiS,SAAS,CAAC;UAClExK,KAAK,EAAGA,KAAK,IAAI,CAAE;SACpB,CAAC;QACF,IAAI,CAAC3C,aAAa,CAACuQ,GAAG,CAAClC,GAAG,CAAC;;IAE/B,CAAC,CAAC;EACJ;EAEA+P,cAAcA,CAACjI,KAAU;IACvB,MAAMkI,IAAI,GAAGlI,KAAK,CAACgB,MAAM,CAACmH,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACD,IAAI,EAAE;IAEX,IAAIA,IAAI,CAAC9L,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/B,IAAI,CAACzT,YAAY,CAACkO,SAAS,CAAC,mCAAmC,CAAC;MAChE;;IAGF,MAAMuR,UAAU,GAAG,CACjB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,oBAAoB,EACpB,yEAAyE,CAC1E;IACD,IAAI,CAACA,UAAU,CAACtM,QAAQ,CAACoM,IAAI,CAAC1V,IAAI,CAAC,EAAE;MACnC,IAAI,CAAC7J,YAAY,CAACkO,SAAS,CACzB,gEAAgE,CACjE;MACD;;IAGF,IAAI,CAAC5N,YAAY,GAAGif,IAAI;IACxB,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,IAAI,CAACrf,UAAU,GAAGmf,MAAM,CAACxM,MAAM;IACjC,CAAC;IACDwM,MAAM,CAACG,aAAa,CAACN,IAAI,CAAC;EAC5B;EAEAO,gBAAgBA,CAAA;IACd,IAAI,CAACxf,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAACwf,SAAS,EAAExS,aAAa,EAAE;MACjC,IAAI,CAACwS,SAAS,CAACxS,aAAa,CAACqN,KAAK,GAAG,EAAE;;EAE3C;EAOA;EACAoF,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC7f,YAAY,EAAE/D,EAAE,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;MACjD;;IAGF,MAAMgd,cAAc,GAAG,IAAI,CAACnZ,YAAY,CAAC/D,EAAE;IAC3CigB,YAAY,CAAC,IAAI,CAACG,WAAW,CAAC;IAE9B,IAAI,CAAC,IAAI,CAACtV,iBAAiB,EAAE;MAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACpJ,cAAc,CAACmiB,WAAW,CAAC3G,cAAc,CAAC,CAACjL,SAAS,CAAC;QACxDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;QACdzK,KAAK,EAAGA,KAAK,IAAI,CAAE;OACpB,CAAC;;IAGJ,IAAI,CAAC2Y,WAAW,GAAG1C,UAAU,CAAC,MAAK;MACjC,IAAI,IAAI,CAAC5S,iBAAiB,EAAE;QAC1B,IAAI,CAACA,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACpJ,cAAc,CAAC2e,UAAU,CAACnD,cAAc,CAAC,CAACjL,SAAS,CAAC;UACvDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;UACdzK,KAAK,EAAGA,KAAK,IAAI,CAAE;SACpB,CAAC;;IAEN,CAAC,EAAE,IAAI,CAACuD,cAAc,CAAC;EACzB;EAEA;EACQG,WAAWA,CAAC2Y,SAAiB,EAAEC,WAAA,GAAuB,IAAI;IAChE,MAAMpL,MAAM,GAAG;MACbqL,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,cAAc;MACpBpH,KAAK,EAAE,iBAAiB;MACxBzE,YAAY,EAAE,uBAAuB;MACrC8L,MAAM,EAAE,eAAe;MACvBhc,MAAM,EAAE;KACT;IAED,MAAMic,YAAY,GAAGxL,MAAM,CAACmL,SAAgC,CAAC;IAC7D,IAAIK,YAAY,EAAE;MACf,IAAY,CAACA,YAAY,CAAC,GAAG,CAAE,IAAY,CAACA,YAAY,CAAC;MAE1D,IAAIJ,WAAW,IAAK,IAAY,CAACI,YAAY,CAAC,EAAE;QAC9CpL,MAAM,CAACY,MAAM,CAAChB,MAAM,CAAC,CAACjD,OAAO,CAAE0O,KAAK,IAAI;UACtC,IAAIA,KAAK,KAAKD,YAAY,EAAE;YACzB,IAAY,CAACC,KAAK,CAAC,GAAG,KAAK;;QAEhC,CAAC,CAAC;;;EAGR;EAaAC,WAAWA,CAACL,KAAa;IACvB,IAAI,CAACjf,aAAa,GAAGif,KAAK;IAC1B,IAAI,CAAChf,iBAAiB,GAAG,KAAK;IAC9B4b,YAAY,CAAC0D,OAAO,CAAC,YAAY,EAAEN,KAAK,CAAC;EAC3C;EAgCA;EACQvX,sBAAsBA,CAAC8X,WAAmB;IAChD,IAAI,CAACtf,YAAY,GAAG,KAAK;IACzB,IAAI,CAACrB,YAAY,CAAC4I,QAAQ,CAAC,GAAG+X,WAAW,4BAA4B,CAAC;EACxE;EA8EAC,WAAWA,CAAA;IACT,IACG,IAAI,CAAClE,WAAW,CAACmE,OAAO,IAAI,CAAC,IAAI,CAACvgB,YAAY,IAC/C,CAAC,IAAI,CAAChE,aAAa,IACnB,CAAC,IAAI,CAACd,gBAAgB,EAAEY,EAAE,EAC1B;MACA;;IAGF,IAAI,CAAC8f,OAAO,CAACK,mBAAmB,EAAE;IAElC,MAAMhgB,OAAO,GAAG,IAAI,CAACmgB,WAAW,CAACoE,GAAG,CAAC,SAAS,CAAC,EAAElG,KAAK;IAEtD,MAAMmG,WAAW,GAAY;MAC3B3kB,EAAE,EAAE,OAAO,GAAG,IAAIiK,IAAI,EAAE,CAACoY,OAAO,EAAE;MAClCliB,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtBJ,MAAM,EAAE;QACNC,EAAE,EAAE,IAAI,CAACE,aAAa,IAAI,EAAE;QAC5Bb,QAAQ,EAAE,IAAI,CAAC4E;OAChB;MACDgf,QAAQ,EAAE;QACRjjB,EAAE,EAAE,IAAI,CAACZ,gBAAgB,CAACY,EAAE;QAC5BX,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC,QAAQ,IAAI;OAC7C;MACDgB,SAAS,EAAE,IAAI4J,IAAI,EAAE;MACrB8K,MAAM,EAAE,KAAK;MACb6P,SAAS,EAAE;KACZ;IAED,IAAI,IAAI,CAAC1gB,YAAY,EAAE;MACrB,IAAI2gB,QAAQ,GAAG,MAAM;MACrB,IAAI,IAAI,CAAC3gB,YAAY,CAACuJ,IAAI,CAACqX,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC/CD,QAAQ,GAAG,OAAO;QAElB,IAAI,IAAI,CAAC1gB,UAAU,EAAE;UACnBwgB,WAAW,CAACI,WAAW,GAAG,CACxB;YACE/kB,EAAE,EAAE,iBAAiB;YACrBiO,GAAG,EAAE,IAAI,CAAC9J,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC0D,QAAQ,EAAE,GAAG,EAAE;YACtD4F,IAAI,EAAEjP,WAAW,CAACwmB,KAAK;YACvB5X,IAAI,EAAE,IAAI,CAAClJ,YAAY,CAACkJ,IAAI;YAC5BiK,IAAI,EAAE,IAAI,CAACnT,YAAY,CAACmT;WACzB,CACF;;;MAIL,IAAIwN,QAAQ,KAAK,OAAO,EAAE;QACxBF,WAAW,CAAClX,IAAI,GAAGjP,WAAW,CAACwmB,KAAK;OACrC,MAAM,IAAIH,QAAQ,KAAK,MAAM,EAAE;QAC9BF,WAAW,CAAClX,IAAI,GAAGjP,WAAW,CAACymB,IAAI;;;IAIvC,IAAI,CAACnhB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE6gB,WAAW,CAAC;IAE/C,MAAMO,UAAU,GAAG,IAAI,CAAChhB,YAAY;IACpC,IAAI,CAACoc,WAAW,CAAC6E,KAAK,EAAE;IACxB,IAAI,CAACzB,gBAAgB,EAAE;IAEvBhG,UAAU,CAAC,MAAM,IAAI,CAACtL,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAE/C,IAAI,CAAChO,WAAW,GAAG,IAAI;IAEvB,MAAMghB,OAAO,GAAG,IAAI,CAAC1jB,cAAc,CAAC8iB,WAAW,CAC7C,IAAI,CAACplB,gBAAgB,CAACY,EAAE,EACxBG,OAAO,EACP+kB,UAAU,IAAIG,SAAS,EACvB7mB,WAAW,CAAC8mB,IAAI,EAChB,IAAI,CAACvhB,YAAY,EAAE/D,EAAE,CACtB,CAACiS,SAAS,CAAC;MACVC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAACoT,kBAAkB,CAACZ,WAAW,CAAC3kB,EAAG,EAAEmS,OAAO,CAAC;QACjD,IAAI,CAAC/N,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDqD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC8d,kBAAkB,CAACZ,WAAW,CAAC3kB,EAAG,EAAE,IAAI,EAAE,IAAI,CAAC;QACpD,IAAI,CAACoE,WAAW,GAAG,KAAK;QACxB,IAAI,CAACR,YAAY,CAACkO,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;IAEF,IAAI,CAAChN,aAAa,CAACuQ,GAAG,CAAC+P,OAAO,CAAC;EACjC;EAEA;EACQG,kBAAkBA,CACxBC,MAAc,EACd5C,UAA2B,EAC3B6C,OAAA,GAAmB,KAAK;IAExB,IAAI,CAAC3hB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACyJ,GAAG,CAAEC,GAAG,IAAI;MACxC,IAAIA,GAAG,CAACxN,EAAE,KAAKwlB,MAAM,EAAE;QACrB,IAAI5C,UAAU,EAAE;UACd,OAAOA,UAAU;SAClB,MAAM,IAAI6C,OAAO,EAAE;UAClB,OAAO;YACL,GAAGjY,GAAG;YACNoX,SAAS,EAAE,KAAK;YAChBa,OAAO,EAAE;WACV;;;MAGL,OAAOjY,GAAG;IACZ,CAAC,CAAC;EACJ;EA6CA;EACAkY,QAAQA,CAACzK,KAAU;IACjB,MAAM0K,SAAS,GAAG1K,KAAK,CAACgB,MAAM;IAC9B,MAAM2J,SAAS,GAAGD,SAAS,CAACC,SAAS;IAErC,IACEA,SAAS,GAAG,EAAE,IACd,CAAC,IAAI,CAAChhB,aAAa,IACnB,IAAI,CAACb,YAAY,EAAE/D,EAAE,IACrB,IAAI,CAAC6E,eAAe,EACpB;MACA,IAAI,CAAC2M,oBAAoB,EAAE;MAE3B,MAAMqU,eAAe,GAAGF,SAAS,CAACG,YAAY;MAC9C,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,EAAE;MAEzD,IAAI,CAACphB,aAAa,GAAG,IAAI;MAEzB,IAAI,CAACqhB,gBAAgB,EAAE;MAEvB;MACAC,qBAAqB,CAAC,MAAK;QACzB,MAAMC,sBAAsB,GAAGA,CAAA,KAAK;UAClC,IAAIJ,mBAAmB,EAAE;YACvB,MAAMK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAC5CN,mBAAmB,CAAC/lB,EAAE,CACvB;YACD,IAAIomB,cAAc,EAAE;cAClB;cACAA,cAAc,CAACE,cAAc,CAAC;gBAAEC,KAAK,EAAE;cAAQ,CAAE,CAAC;aACnD,MAAM;cACL;cACA,MAAMC,eAAe,GAAGb,SAAS,CAACG,YAAY;cAC9C,MAAMW,UAAU,GAAGD,eAAe,GAAGX,eAAe;cACpDF,SAAS,CAACC,SAAS,GAAGA,SAAS,GAAGa,UAAU;;;UAIhD;UACA,IAAI,CAAChV,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACAiM,UAAU,CAACyI,sBAAsB,EAAE,GAAG,CAAC;MACzC,CAAC,CAAC;;EAEN;EAEA;EACQH,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAAC9U,iBAAiB,EAAEC,aAAa,IAAI,CAAC,IAAI,CAACrN,QAAQ,CAACT,MAAM,EACjE,OAAO,IAAI;IAEb,MAAMsiB,SAAS,GAAG,IAAI,CAACzU,iBAAiB,CAACC,aAAa;IACtD,MAAMuV,eAAe,GAAGf,SAAS,CAACgB,gBAAgB,CAAC,eAAe,CAAC;IAEnE,KAAK,IAAIhf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+e,eAAe,CAACrjB,MAAM,EAAEsE,CAAC,EAAE,EAAE;MAC/C,MAAMif,OAAO,GAAGF,eAAe,CAAC/e,CAAC,CAAC;MAClC,MAAMkf,IAAI,GAAGD,OAAO,CAACE,qBAAqB,EAAE;MAE5C;MACA,IAAID,IAAI,CAACE,GAAG,IAAI,CAAC,IAAIF,IAAI,CAACG,MAAM,IAAIrB,SAAS,CAACsB,YAAY,EAAE;QAC1D,MAAMlM,SAAS,GAAG6L,OAAO,CAACM,YAAY,CAAC,iBAAiB,CAAC;QACzD,OAAO,IAAI,CAACpjB,QAAQ,CAAC+d,IAAI,CAAEjS,CAAC,IAAKA,CAAC,CAAC5P,EAAE,KAAK+a,SAAS,CAAC,IAAI,IAAI;;;IAIhE,OAAO,IAAI;EACb;EAEA;EACQsL,kBAAkBA,CACxBtL,SAA6B;IAE7B,IAAI,CAAC,IAAI,CAAC7J,iBAAiB,EAAEC,aAAa,IAAI,CAAC4J,SAAS,EAAE,OAAO,IAAI;IACrE,OAAO,IAAI,CAAC7J,iBAAiB,CAACC,aAAa,CAACgW,aAAa,CACvD,qBAAqBpM,SAAS,IAAI,CACnC;EACH;EAuBA;EACAkL,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACrhB,aAAa,IAAI,CAAC,IAAI,CAACb,YAAY,EAAE/D,EAAE,IAAI,CAAC,IAAI,CAAC6E,eAAe,EACvE;IAEF;IACA,IAAI,CAACD,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAACD,WAAW,EAAE;IAElB;IACA,IAAI,CAACjD,cAAc,CAAC6f,eAAe,CACjC,IAAI,CAACxd,YAAY,CAAC/D,EAAE,EACpB,IAAI,CAACyE,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB,CAACsN,SAAS,CAAC;MACVC,IAAI,EAAGnO,YAAY,IAAI;QACrB,IACEA,YAAY,IACZA,YAAY,CAACD,QAAQ,IACrBC,YAAY,CAACD,QAAQ,CAACT,MAAM,GAAG,CAAC,EAChC;UACA;UACA,MAAM+jB,WAAW,GAAG,CAAC,GAAG,IAAI,CAACtjB,QAAQ,CAAC;UAEtC;UACA,MAAMujB,WAAW,GAAG,IAAIrhB,GAAG,CAACohB,WAAW,CAAC7Z,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACxN,EAAE,CAAC,CAAC;UAE7D;UACA,MAAMsnB,WAAW,GAAGvjB,YAAY,CAACD,QAAQ,CACtClF,MAAM,CAAE4O,GAAG,IAAK,CAAC6Z,WAAW,CAAClS,GAAG,CAAC3H,GAAG,CAACxN,EAAE,CAAC,CAAC,CACzCiiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACb,MAAMC,KAAK,GAAG,IAAInY,IAAI,CAACiY,CAAC,CAAC7hB,SAAmB,CAAC,CAACgiB,OAAO,EAAE;YACvD,MAAMC,KAAK,GAAG,IAAIrY,IAAI,CAACkY,CAAC,CAAC9hB,SAAmB,CAAC,CAACgiB,OAAO,EAAE;YACvD,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEJ,IAAIgF,WAAW,CAACjkB,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAI,CAACS,QAAQ,GAAG,CAAC,GAAGwjB,WAAW,EAAE,GAAGF,WAAW,CAAC;YAEhD;YACA,IAAI,IAAI,CAACtjB,QAAQ,CAACT,MAAM,GAAG,IAAI,CAACqB,kBAAkB,EAAE;cAClD,IAAI,CAACZ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACyjB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC7iB,kBAAkB,CAAC;;YAGjE;YACA,IAAI,CAACG,eAAe,GAClByiB,WAAW,CAACjkB,MAAM,IAAI,IAAI,CAACoB,oBAAoB;WAClD,MAAM;YACL;YACA,IAAI,CAACI,eAAe,GAAG,KAAK;;SAE/B,MAAM;UACL,IAAI,CAACA,eAAe,GAAG,KAAK;;QAG9B;QACA;QACA6Y,UAAU,CAAC,MAAK;UACd,IAAI,CAAC9Y,aAAa,GAAG,KAAK;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD6C,KAAK,EAAGA,KAAK,IAAI;QACfmM,OAAO,CAACnM,KAAK,CAAC,aAAa,EAAE,8BAA8B,EAAEA,KAAK,CAAC;QACnE,IAAI,CAAC7C,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC6M,oBAAoB,EAAE;QAC3B,IAAI,CAAC7N,YAAY,CAACkO,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACQ0V,eAAeA,CACrBC,UAAqC,EACrCC,UAAqC;IAErC,IAAI,CAACD,UAAU,IAAI,CAACC,UAAU,EAAE,OAAO,KAAK;IAE5C,IAAI;MACF,MAAMC,KAAK,GACTF,UAAU,YAAYxd,IAAI,GACtBwd,UAAU,CAACpF,OAAO,EAAE,GACpB,IAAIpY,IAAI,CAACwd,UAAoB,CAAC,CAACpF,OAAO,EAAE;MAC9C,MAAMuF,KAAK,GACTF,UAAU,YAAYzd,IAAI,GACtByd,UAAU,CAACrF,OAAO,EAAE,GACpB,IAAIpY,IAAI,CAACyd,UAAoB,CAAC,CAACrF,OAAO,EAAE;MAC9C,OAAO3G,IAAI,CAACmM,GAAG,CAACF,KAAK,GAAGC,KAAK,CAAC,GAAG,IAAI;KACtC,CAAC,OAAOngB,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEA2K,cAAcA,CAAC0V,KAAA,GAAiB,KAAK;IACnC,IAAI;MACF,IAAI,CAAC,IAAI,CAAC5W,iBAAiB,EAAEC,aAAa,EAAE;MAE5C+U,qBAAqB,CAAC,MAAK;QACzB,MAAMP,SAAS,GAAG,IAAI,CAACzU,iBAAiB,CAACC,aAAa;QACtD,MAAM4W,kBAAkB,GACtBpC,SAAS,CAACG,YAAY,GAAGH,SAAS,CAACsB,YAAY,IAC/CtB,SAAS,CAACC,SAAS,GAAG,GAAG;QAE3B,IAAIkC,KAAK,IAAIC,kBAAkB,EAAE;UAC/BpC,SAAS,CAACqC,QAAQ,CAAC;YACjBjB,GAAG,EAAEpB,SAAS,CAACG,YAAY;YAC3BmC,QAAQ,EAAE;WACX,CAAC;;MAEN,CAAC,CAAC;KACH,CAAC,OAAOC,GAAG,EAAE;MACZtU,OAAO,CAACnM,KAAK,CAAC,aAAa,EAAE,4BAA4B,EAAEygB,GAAG,CAAC;;EAEnE;EA0CA;;;;EAIAC,mBAAmBA,CAACC,QAAgB;IAClCla,MAAM,CAACma,IAAI,CAACD,QAAQ,EAAE,QAAQ,CAAC;EACjC;EAEA;;;EAGAE,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAClW,cAAc,EAAE;IAErB;IACA;EACF;EAEA;;;EAGAmW,qBAAqBA,CAAA;IACnB,IAAI,CAAC5kB,MAAM,CAAC6kB,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEA;;;;EAIAC,WAAWA,CAAC5L,KAAa;IACvB,MAAM6L,OAAO,GAAG,IAAI,CAACpI,WAAW,CAACoE,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAIgE,OAAO,EAAE;MACX,MAAMC,YAAY,GAAGD,OAAO,CAAClK,KAAK,IAAI,EAAE;MACxCkK,OAAO,CAACE,QAAQ,CAACD,YAAY,GAAG9L,KAAK,CAAC;MACtC6L,OAAO,CAACG,WAAW,EAAE;MACrB;MACAnL,UAAU,CAAC,MAAK;QACd,MAAMoL,YAAY,GAAGxa,QAAQ,CAAC6Y,aAAa,CACzC,uBAAuB,CACJ;QACrB,IAAI2B,YAAY,EAAE;UAChBA,YAAY,CAACC,KAAK,EAAE;;MAExB,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQjI,wBAAwBA,CAAA;IAC9B,MAAMkI,eAAe,GACnB,IAAI,CAACtnB,cAAc,CAACunB,2BAA2B,EAAE,CAAChX,SAAS,CAAC;MAC1DC,IAAI,EAAGkG,YAAY,IAAI;QACrB,IAAI,CAACtS,aAAa,CAACojB,OAAO,CAAC9Q,YAAY,CAAC;QACxC,IAAI,CAACvC,uBAAuB,EAAE;QAE9B,IAAI,CAACnU,cAAc,CAACynB,IAAI,CAAC,cAAc,CAAC;QAExC,IACE/Q,YAAY,CAAC3K,IAAI,KAAK,aAAa,IACnC2K,YAAY,CAAC8E,cAAc,KAAK,IAAI,CAACnZ,YAAY,EAAE/D,EAAE,EACrD;UACA,IAAIoY,YAAY,CAACpY,EAAE,EAAE;YACnB,IAAI,CAAC0B,cAAc,CAAC6U,UAAU,CAAC,CAAC6B,YAAY,CAACpY,EAAE,CAAC,CAAC,CAACiS,SAAS,EAAE;;;MAGnE,CAAC;MACDxK,KAAK,EAAGA,KAAK,IAAI,CAAE;KACpB,CAAC;IACJ,IAAI,CAAC3C,aAAa,CAACuQ,GAAG,CAAC2T,eAAe,CAAC;IAEvC,MAAMI,oBAAoB,GAAG,IAAI,CAAC1nB,cAAc,CAAC2nB,cAAc,CAACpX,SAAS,CAAC;MACxEC,IAAI,EAAGpM,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;QAClC,IAAI,CAAC+P,uBAAuB,EAAE;MAChC,CAAC;MACDpO,KAAK,EAAGA,KAAK,IAAI,CAAE;KACpB,CAAC;IACF,IAAI,CAAC3C,aAAa,CAACuQ,GAAG,CAAC+T,oBAAoB,CAAC;IAE5C,MAAME,oBAAoB,GACxB,IAAI,CAAC5nB,cAAc,CAAC6nB,kBAAkB,CAACtX,SAAS,CAAC;MAC/CC,IAAI,EAAGzP,KAAK,IAAI;QACd,IAAI,CAACI,uBAAuB,GAAGJ,KAAK;MACtC;KACD,CAAC;IACJ,IAAI,CAACqC,aAAa,CAACuQ,GAAG,CAACiU,oBAAoB,CAAC;IAE5C,MAAME,OAAO,GAAG,IAAI,CAAC9nB,cAAc,CAAC+nB,aAAa,CAACxX,SAAS,CAAC;MAC1DC,IAAI,EAAGW,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACvN,YAAY,GAAGuN,IAAI;UACxB,IAAI,CAACrN,aAAa,GAAG,IAAI;UACzB,IAAI,CAAC9D,cAAc,CAACynB,IAAI,CAAC,UAAU,CAAC;SACrC,MAAM;UACL,IAAI,CAAC3jB,aAAa,GAAG,KAAK;UAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;;MAE5B;KACD,CAAC;IACF,IAAI,CAACR,aAAa,CAACuQ,GAAG,CAACmU,OAAO,CAAC;IAE/B,MAAME,aAAa,GAAG,IAAI,CAAChoB,cAAc,CAAC0R,WAAW,CAACnB,SAAS,CAAC;MAC9DC,IAAI,EAAGW,IAAI,IAAI;QACb,IAAI,CAACtN,UAAU,GAAGsN,IAAI;QACtB,IAAIA,IAAI,EAAE;UACR,IAAI,CAACpN,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAAC6O,oBAAoB,EAAE;SAC5B,MAAM;UACL,IAAI,CAAC7O,mBAAmB,GAAG,KAAK;UAChC,IAAI,CAAC8O,mBAAmB,EAAE;UAC1B,IAAI,CAACC,oBAAoB,EAAE;;MAE/B;KACD,CAAC;IACF,IAAI,CAAC1P,aAAa,CAACuQ,GAAG,CAACqU,aAAa,CAAC;IAErC;IACA,MAAMC,cAAc,GAAG,IAAI,CAACjoB,cAAc,CAACkoB,YAAY,CAAC3X,SAAS,CAAC;MAChEC,IAAI,EAAG2X,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAACxf,iBAAiB,EAAE;UACpC,IAAI,CAACA,iBAAiB,CAACyf,SAAS,GAAGD,MAAM;;MAE7C;KACD,CAAC;IACF,IAAI,CAAC/kB,aAAa,CAACuQ,GAAG,CAACsU,cAAc,CAAC;IAEtC;IACA,MAAMI,eAAe,GAAG,IAAI,CAACroB,cAAc,CAACsoB,aAAa,CAAC/X,SAAS,CAAC;MAClEC,IAAI,EAAG2X,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAACvf,kBAAkB,EAAE;UACrC,IAAI,CAACA,kBAAkB,CAACwf,SAAS,GAAGD,MAAM;;MAE9C;KACD,CAAC;IACF,IAAI,CAAC/kB,aAAa,CAACuQ,GAAG,CAAC0U,eAAe,CAAC;EACzC;EAsIA;;;EAGArV,iBAAiBA,CAACuV,OAAA,GAAmB,KAAK;IACxC,MAAMC,OAAO,GAAG,IAAI,CAACxoB,cAAc,CAACyoB,gBAAgB,CAClDF,OAAO,EACP,CAAC,EACD,EAAE,CACH,CAAChY,SAAS,CAAC;MACVC,IAAI,EAAGpM,aAAa,IAAI;QACtB,IAAImkB,OAAO,EAAE;UACX,IAAI,CAACnkB,aAAa,GAAGA,aAAa;SACnC,MAAM;UACL,IAAI,CAACA,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAGA,aAAa,CAAC;;QAGhE,IAAI,CAAC+P,uBAAuB,EAAE;MAChC,CAAC;MACDpO,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7D,YAAY,CAACkO,SAAS,CACzB,6CAA6C,CAC9C;MACH;KACD,CAAC;IAEF,IAAI,CAAChN,aAAa,CAACuQ,GAAG,CAAC6U,OAAO,CAAC;EACjC;EAsNA;;;EAGQnJ,qBAAqBA,CAAA;IAC3B,MAAMqJ,SAAS,GAAG,IAAI,CAAC1oB,cAAc,CAACqf,qBAAqB,EAAE,CAAC9O,SAAS,CAAC;MACtEC,IAAI,EAAG0H,IAAU,IAAI;QACnB,IAAI,CAACyQ,sBAAsB,CAACzQ,IAAI,CAAC;MACnC,CAAC;MACDnS,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;IAEF,IAAI,CAAC3C,aAAa,CAACuQ,GAAG,CAAC+U,SAAS,CAAC;EACnC;EAEA;;;EAGQC,sBAAsBA,CAACzQ,IAAU;IACvC,IAAI,CAACA,IAAI,CAAC5Z,EAAE,EAAE;IAEd,IAAI4Z,IAAI,CAACra,QAAQ,EAAE;MACjB,IAAI,CAACsK,WAAW,CAACygB,GAAG,CAAC1Q,IAAI,CAAC5Z,EAAE,EAAE4Z,IAAI,CAAC;KACpC,MAAM;MACL,IAAI,CAAC/P,WAAW,CAACuL,MAAM,CAACwE,IAAI,CAAC5Z,EAAE,CAAC;;IAGlC,IAAI,IAAI,CAACZ,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACY,EAAE,KAAK4Z,IAAI,CAAC5Z,EAAE,EAAE;MACjE,IAAI,CAACZ,gBAAgB,GAAG;QAAE,GAAG,IAAI,CAACA,gBAAgB;QAAE,GAAGwa;MAAI,CAAE;;EAEjE;EAEA;;;EAGQoH,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAAC9gB,aAAa,EAAE;IAEzB,MAAMqqB,YAAY,GAAG,IAAI,CAAC7oB,cAAc,CAAC8oB,aAAa,CACpD,IAAI,CAACtqB,aAAa,CACnB,CAAC+R,SAAS,CAAC;MACVC,IAAI,EAAG0H,IAAU,IAAI;QACnB,IAAI,CAAC7P,iBAAiB,GAAG,QAAQ;QACjC,IAAI,CAACC,gBAAgB,GAAG,IAAIC,IAAI,EAAE;MACpC,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI;QACfmM,OAAO,CAACnM,KAAK,CACX,aAAa,EACb,2CAA2C,EAC3CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3C,aAAa,CAACuQ,GAAG,CAACkV,YAAY,CAAC;EACtC;EAEA;;;EAGQtJ,qBAAqBA,CAAA;IAC3B,MAAMwJ,MAAM,GAAG,CACb,WAAW,EACX,WAAW,EACX,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,OAAO,CACR;IAEDA,MAAM,CAAC/U,OAAO,CAAEuF,KAAK,IAAI;MACvB3M,QAAQ,CAAC4S,gBAAgB,CAACjG,KAAK,EAAE,IAAI,CAACyP,cAAc,CAACvJ,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IACxE,CAAC,CAAC;EACJ;EAEA;;;EAGQuJ,cAAcA,CAAA;IACpB,IAAI,CAAC1gB,gBAAgB,GAAG,IAAIC,IAAI,EAAE;IAElC;IACA,IAAI,IAAI,CAACC,eAAe,EAAE;MACxB+V,YAAY,CAAC,IAAI,CAAC/V,eAAe,CAAC;;IAGpC;IACA,IACE,IAAI,CAACH,iBAAiB,KAAK,MAAM,IACjC,IAAI,CAACA,iBAAiB,KAAK,SAAS,EACpC;MACA,IAAI,CAAC4gB,gBAAgB,CAAC,QAAQ,CAAC;;IAGjC;IACA,IAAI,CAACzgB,eAAe,GAAGwT,UAAU,CAAC,MAAK;MACrC,IAAI,IAAI,CAAC3T,iBAAiB,KAAK,QAAQ,EAAE;QACvC,IAAI,CAAC4gB,gBAAgB,CAAC,MAAM,CAAC;;IAEjC,CAAC,EAAE,IAAI,CAACppB,CAAC,CAAC6F,MAAM,CAACC,IAAI,CAAC;EACxB;EAEA;;;EAGAsjB,gBAAgBA,CAACziB,MAAc;IAC7B,IAAI,CAAC,IAAI,CAAChI,aAAa,EAAE;IAEzB,MAAM0qB,cAAc,GAAG,IAAI,CAAC7gB,iBAAiB;IAE7C,IAAI8gB,gBAAgB;IACpB,IAAI3iB,MAAM,KAAK,QAAQ,EAAE;MACvB2iB,gBAAgB,GAAG,IAAI,CAACnpB,cAAc,CAAC8oB,aAAa,CAAC,IAAI,CAACtqB,aAAa,CAAC;KACzE,MAAM;MACL2qB,gBAAgB,GAAG,IAAI,CAACnpB,cAAc,CAACwe,cAAc,CAAC,IAAI,CAAChgB,aAAa,CAAC;;IAG3E,MAAM4qB,SAAS,GAAGD,gBAAgB,CAAC5Y,SAAS,CAAC;MAC3CC,IAAI,EAAG0H,IAAU,IAAI;QACnB,IAAI,CAAC7P,iBAAiB,GAAG7B,MAAM;QAE/B,IAAIA,MAAM,KAAK0iB,cAAc,EAAE;UAC7B,MAAMG,UAAU,GAAG,IAAI,CAAC1R,aAAa,CAACnR,MAAM,CAAC;UAC7C,IAAI,CAACtE,YAAY,CAAC4I,QAAQ,CAAC,YAAYue,UAAU,EAAE,CAAC;;MAExD,CAAC;MACDtjB,KAAK,EAAGA,KAAK,IAAI;QACfmM,OAAO,CAACnM,KAAK,CACX,aAAa,EACb,yCAAyC,EACzCA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3C,aAAa,CAACuQ,GAAG,CAACyV,SAAS,CAAC;EACnC;EAEA;;;EAGAE,eAAeA,CAAA;IACb,MAAMC,QAAQ,GAAG,IAAI,CAACvpB,cAAc,CAACwpB,WAAW,CAC9C,KAAK,EACL7F,SAAS,EACT,CAAC,EACD,EAAE,EACF,UAAU,EACV,KAAK,EACL,IAAI,CACL,CAACpT,SAAS,CAAC;MACVC,IAAI,EAAGiZ,KAAa,IAAI;QACtBA,KAAK,CAACzV,OAAO,CAAEkE,IAAI,IAAI;UACrB,IAAIA,IAAI,CAACra,QAAQ,IAAIqa,IAAI,CAAC5Z,EAAE,EAAE;YAC5B,IAAI,CAAC6J,WAAW,CAACygB,GAAG,CAAC1Q,IAAI,CAAC5Z,EAAE,EAAE4Z,IAAI,CAAC;;QAEvC,CAAC,CAAC;MACJ,CAAC;MACDnS,KAAK,EAAGA,KAAK,IAAI;QACfmM,OAAO,CAACnM,KAAK,CACX,aAAa,EACb,qDAAqD,EACrDA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3C,aAAa,CAACuQ,GAAG,CAAC4V,QAAQ,CAAC;EAClC;EAgJAG,gBAAgBA,CAACjZ,OAAY;IAC3B,IAAI,CAACvL,SAAS,CAACuL,OAAO,CAACnS,EAAE,CAAC,GAAG,IAAI;IACjC0d,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9W,SAAS,CAACuL,OAAO,CAACnS,EAAE,CAAC,GAAG,KAAK;MAClC,IAAI,CAAC2G,cAAc,CAACwL,OAAO,CAACnS,EAAE,CAAC,GAAG,KAAK;IACzC,CAAC,EAAE,IAAI,CAAC;EACV;EA6OAqrB,WAAWA,CAAA;IACT,IAAI,CAACvL,OAAO,CAACC,aAAa,EAAE;IAC5B,IAAI,CAACD,OAAO,CAACI,cAAc,EAAE;IAC7B,IAAI,CAACJ,OAAO,CAACK,mBAAmB,EAAE;IAClC,IAAI,CAACrb,aAAa,CAACwO,WAAW,EAAE;IAChChF,QAAQ,CAACgd,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACtP,eAAe,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;EACxE;;;uBAjsEW9f,oBAAoB,EAAAxC,EAAA,CAAA0sB,iBAAA,CAAAC,EAAA,CAAA9pB,cAAA,GAAA7C,EAAA,CAAA0sB,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAA7sB,EAAA,CAAA0sB,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAA/sB,EAAA,CAAA0sB,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAjtB,EAAA,CAAA0sB,iBAAA,CAAAQ,EAAA,CAAAC,iBAAA,GAAAntB,EAAA,CAAA0sB,iBAAA,CAAAE,EAAA,CAAAQ,MAAA,GAAAptB,EAAA,CAAA0sB,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAttB,EAAA,CAAA0sB,iBAAA,CAAA1sB,EAAA,CAAAutB,iBAAA;IAAA;EAAA;;;YAApB/qB,oBAAoB;MAAA8a,SAAA;MAAAkQ,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UC9BjC1tB,EAAA,CAAAC,cAAA,aAA4B;UAGhBD,EAAA,CAAA2B,UAAA,mBAAAisB,sDAAA;YAAA,OAASD,GAAA,CAAAjE,qBAAA,EAAuB;UAAA,EAAC;UACvC1pB,EAAA,CAAAa,SAAA,WAAiC;UACnCb,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAa,SAAA,aAIE;UAEFb,EAAA,CAAA6tB,UAAA,IAAAC,mCAAA,iBASM;UAEN9tB,EAAA,CAAAC,cAAA,aAA4B;UAGxBD,EAAA,CAAA2B,UAAA,mBAAAosB,sDAAA;YAAA,OAASJ,GAAA,CAAAvqB,YAAA,CAAa,OAAO,CAAC;UAAA,EAAC;UAG/BpD,EAAA,CAAAa,SAAA,WAA4B;UAC9Bb,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,gBAIC;UAFCD,EAAA,CAAA2B,UAAA,mBAAAqsB,sDAAA;YAAA,OAASL,GAAA,CAAAvqB,YAAA,CAAa,OAAO,CAAC;UAAA,EAAC;UAG/BpD,EAAA,CAAAa,SAAA,aAA4B;UAC9Bb,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA0E;UAA/CD,EAAA,CAAA2B,UAAA,mBAAAssB,uDAAA;YAAA,OAASN,GAAA,CAAArrB,eAAA,EAAiB;UAAA,EAAC;UACpDtC,EAAA,CAAAa,SAAA,aAA6B;UAC/Bb,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAIC;UAFCD,EAAA,CAAA2B,UAAA,mBAAAusB,uDAAA;YAAA,OAASP,GAAA,CAAA7pB,uBAAA,EAAyB;UAAA,EAAC;UAGnC9D,EAAA,CAAAa,SAAA,aAA2B;UAC7Bb,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,mBAA8C;UAC5CD,EAAA,CAAA6tB,UAAA,KAAAM,oCAAA,kBAKM;UAENnuB,EAAA,CAAA6tB,UAAA,KAAAO,oCAAA,kBAUM;UAENpuB,EAAA,CAAA6tB,UAAA,KAAAQ,oCAAA,kBAcM;UACRruB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAwB;UAGpBD,EAAA,CAAA2B,UAAA,sBAAA2sB,wDAAA;YAAA,OAAYX,GAAA,CAAAhI,WAAA,EAAa;UAAA,EAAC;UAG1B3lB,EAAA,CAAAa,SAAA,iBAKE;UACFb,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAa,SAAA,aAAkC;UACpCb,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAAoE;UAE5DD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,kBAA8D;UAApCD,EAAA,CAAA2B,UAAA,mBAAA4sB,uDAAA;YAAA,OAASZ,GAAA,CAAA7pB,uBAAA,EAAyB;UAAA,EAAC;UAC3D9D,EAAA,CAAAa,SAAA,aAA4B;UAC9Bb,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAA6tB,UAAA,KAAAW,oCAAA,kBASM;UACNxuB,EAAA,CAAA6tB,UAAA,KAAAY,oCAAA,kBAQM;UACRzuB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAA6tB,UAAA,KAAAa,oCAAA,kBAsCM;UACR1uB,EAAA,CAAAG,YAAA,EAAM;;;UA9KAH,EAAA,CAAAI,SAAA,GAAqE;UAArEJ,EAAA,CAAAc,UAAA,SAAA6sB,GAAA,CAAAptB,gBAAA,kBAAAotB,GAAA,CAAAptB,gBAAA,CAAAme,KAAA,yCAAA1e,EAAA,CAAA2uB,aAAA,CAAqE;UAK/C3uB,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAc,UAAA,SAAA6sB,GAAA,CAAAptB,gBAAA,CAAsB;UA0C3CP,EAAA,CAAAI,SAAA,IAAa;UAAbJ,EAAA,CAAAc,UAAA,SAAA6sB,GAAA,CAAAxoB,OAAA,CAAa;UAObnF,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAc,UAAA,UAAA6sB,GAAA,CAAAxoB,OAAA,IAAAwoB,GAAA,CAAA1oB,QAAA,CAAAT,MAAA,OAAuC;UAYpBxE,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAc,UAAA,YAAA6sB,GAAA,CAAA1oB,QAAA,CAAW;UAmB/BjF,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAc,UAAA,cAAA6sB,GAAA,CAAAlM,WAAA,CAAyB;UAQvBzhB,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAc,UAAA,cAAA6sB,GAAA,CAAAptB,gBAAA,CAA8B;UAK9BP,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAc,UAAA,aAAA6sB,GAAA,CAAAlM,WAAA,CAAAmE,OAAA,KAAA+H,GAAA,CAAAptB,gBAAA,CAAqD;UAQnCP,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAA4uB,eAAA,KAAAC,GAAA,EAAAlB,GAAA,CAAA5pB,qBAAA,EAA2C;UAQ9D/D,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAc,UAAA,SAAA6sB,GAAA,CAAA1mB,aAAA,CAAAzC,MAAA,OAAgC;UAURxE,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAc,UAAA,YAAA6sB,GAAA,CAAA1mB,aAAA,CAAgB;UAY1CjH,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAc,UAAA,SAAA6sB,GAAA,CAAArqB,aAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}