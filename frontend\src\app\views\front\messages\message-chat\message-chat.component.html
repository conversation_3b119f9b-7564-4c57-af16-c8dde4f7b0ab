<div class="chat-container">
  <!-- En-tête -->
  <div class="chat-header">
    <button (click)="goBackToConversations()" class="action-btn">
      <i class="fas fa-arrow-left"></i>
    </button>

    <img
      [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
      alt="User avatar"
      class="user-avatar"
    />

    <div class="user-info" *ngIf="otherParticipant">
      <h3>{{ otherParticipant.username }}</h3>
      <div class="user-status">
        {{
          otherParticipant.isOnline
            ? "En ligne"
            : formatLastActive(otherParticipant.lastActive)
        }}
      </div>
    </div>

    <div class="action-buttons">
      <button class="action-btn" (click)="initiateCall('AUDIO')" title="Appel audio">
        <i class="fas fa-phone"></i>
      </button>
      <button class="action-btn" (click)="initiateCall('VIDEO')" title="Appel vidéo">
        <i class="fas fa-video"></i>
      </button>
      <button class="action-btn" (click)="toggleSearchBar()" title="Rechercher">
        <i class="fas fa-search"></i>
      </button>
      <button class="action-btn" (click)="toggleNotificationPanel()" title="Notifications">
        <i class="fas fa-bell"></i>
      </button>
    </div>
  </div>

  <!-- Zone des messages -->
  <div class="messages-area" #messagesContainer>
    <div *ngIf="loading" style="text-align: center; padding: 2rem; color: #00f7ff;">
      <i class="fas fa-spinner fa-spin"></i> Chargement...
    </div>

    <div *ngIf="!loading && messages.length === 0" style="text-align: center; padding: 2rem; color: #888;">
      <i class="fas fa-comments" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
      <p>Aucun message pour le moment</p>
      <p style="font-size: 0.9rem;">Commencez la conversation !</p>
    </div>

    <div *ngFor="let message of messages" 
         class="message" 
         [ngClass]="{'current-user': message.sender?.id === currentUserId, 'other-user': message.sender?.id !== currentUserId}">
      <div class="message-bubble">
        <div>{{ message.content }}</div>
        <div style="font-size: 0.7rem; opacity: 0.7; margin-top: 0.5rem;">
          {{ formatMessageTime(message.timestamp) }}
        </div>
      </div>
    </div>
  </div>

  <!-- Zone de saisie -->
  <div class="input-area">
    <form [formGroup]="messageForm" (ngSubmit)="sendMessage()" class="input-form">
      <input
        formControlName="content"
        placeholder="Tapez votre message..."
        class="message-input"
        [disabled]="!otherParticipant"
      />
      <button type="submit" class="send-btn" [disabled]="messageForm.invalid || !otherParticipant">
        <i class="fas fa-paper-plane"></i>
      </button>
    </form>
  </div>

  <!-- Panneau de notifications -->
  <div class="side-panel" [ngClass]="{'open': showNotificationPanel}">
    <div class="panel-header">
      <h3>Notifications</h3>
      <button class="close-btn" (click)="toggleNotificationPanel()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div *ngIf="notifications.length === 0" style="padding: 1rem; text-align: center; color: #888;">
      <i class="fas fa-bell-slash" style="font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.5;"></i>
      <p>Aucune notification</p>
    </div>
    <div *ngFor="let notification of notifications" style="padding: 0.5rem; border-bottom: 1px solid rgba(0, 247, 255, 0.1);">
      <p style="margin: 0; font-size: 0.9rem;">{{ notification.content }}</p>
      <small style="opacity: 0.7;">{{ formatMessageTime(notification.timestamp) }}</small>
    </div>
  </div>

  <!-- Barre de recherche -->
  <div *ngIf="showSearchBar" style="position: absolute; top: 60px; left: 0; right: 0; background: rgba(0, 0, 0, 0.9); padding: 1rem; z-index: 100;">
    <div style="display: flex; align-items: center; gap: 0.5rem;">
      <input
        [(ngModel)]="searchQuery"
        placeholder="Rechercher dans la conversation..."
        style="flex: 1; padding: 0.5rem; background: rgba(0, 0, 0, 0.5); border: 1px solid #00f7ff; border-radius: 5px; color: #e0e0e0;"
      />
      <button (click)="toggleSearchBar()" style="background: transparent; border: 1px solid #00f7ff; color: #00f7ff; padding: 0.5rem; border-radius: 5px;">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>
</div>
