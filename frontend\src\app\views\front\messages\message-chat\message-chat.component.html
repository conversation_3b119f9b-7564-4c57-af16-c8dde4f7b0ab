<!-- ========== CHAT WHATSAPP MODERNE SIMPLIFIÉ ========== -->
<div class="whatsapp-chat-container" [ngClass]="selectedTheme">
  <!-- ========== EN-TÊTE ========== -->
  <div class="whatsapp-header">
    <!-- Bouton retour -->
    <button (click)="goBackToConversations()" class="whatsapp-header-action">
      <i class="fas fa-arrow-left"></i>
    </button>

    <!-- Informations utilisateur -->
    <div class="whatsapp-header-info">
      <div class="whatsapp-header-avatar">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          alt="Avatar utilisateur"
          class="whatsapp-avatar"
          (click)="openUserProfile(otherParticipant?.id!)"
        />
        <div
          *ngIf="otherParticipant?.isOnline"
          class="whatsapp-online-indicator"
        ></div>
      </div>

      <div class="whatsapp-header-details">
        <h3 class="whatsapp-header-name">
          {{ otherParticipant?.username || "Utilisateur" }}
        </h3>
        <div class="whatsapp-header-status">
          <!-- Indicateur de frappe -->
          <div *ngIf="isUserTyping" class="whatsapp-typing-indicator">
            <span>En train d'écrire</span>
            <div class="whatsapp-typing-dot"></div>
            <div class="whatsapp-typing-dot"></div>
            <div class="whatsapp-typing-dot"></div>
          </div>
          <!-- Statut normal -->
          <span *ngIf="!isUserTyping">
            {{
              otherParticipant?.isOnline
                ? "En ligne"
                : formatLastActive(otherParticipant?.lastActive)
            }}
          </span>
        </div>
      </div>
    </div>

    <!-- Actions de l'en-tête -->
    <div class="whatsapp-actions">
      <!-- Appel vidéo -->
      <button
        (click)="startVideoCall()"
        class="whatsapp-action-button"
        title="Appel vidéo"
      >
        <i class="fas fa-video"></i>
      </button>

      <!-- Appel vocal -->
      <button
        (click)="startVoiceCall()"
        class="whatsapp-action-button"
        title="Appel vocal"
      >
        <i class="fas fa-phone"></i>
      </button>

      <!-- Recherche -->
      <button
        (click)="toggleSearch()"
        class="whatsapp-action-button"
        [ngClass]="{ active: searchMode }"
        title="Rechercher"
      >
        <i class="fas fa-search"></i>
      </button>

      <!-- Menu principal -->
      <button
        (click)="toggleMainMenu()"
        class="whatsapp-action-button"
        title="Menu"
      >
        <i class="fas fa-ellipsis-v"></i>
      </button>
    </div>
  </div>

  <!-- Zone de messages -->
  <div
    class="whatsapp-messages-container"
    #messagesContainer
    (scroll)="onScroll($event)"
  >
    <!-- Indicateur de chargement -->
    <div *ngIf="loading" class="whatsapp-loading-state">
      <div class="whatsapp-loading-content">
        <div class="whatsapp-loading-spinner"></div>
        <span class="whatsapp-loading-text">Chargement des messages...</span>
      </div>
    </div>

    <!-- État vide -->
    <div *ngIf="!loading && messages.length === 0" class="whatsapp-empty-state">
      <div class="whatsapp-empty-content">
        <div class="whatsapp-empty-icon">
          <i class="fas fa-comments"></i>
        </div>
        <h3 class="whatsapp-empty-title">Aucun message</h3>
        <p class="whatsapp-empty-description">
          Commencez votre conversation avec {{ otherParticipant?.username }}
        </p>
      </div>
    </div>

    <!-- Liste des messages -->
    <div class="whatsapp-messages-list" *ngIf="!loading && messages.length > 0">
      <ng-container
        *ngFor="
          let message of messages;
          let i = index;
          trackBy: trackByMessageId
        "
      >
        <!-- Séparateur de date -->
        <div *ngIf="shouldShowDateSeparator(i)" class="whatsapp-date-separator">
          <div class="whatsapp-date-separator-content">
            <span>{{ formatDateSeparator(message.timestamp) }}</span>
          </div>
        </div>

        <!-- Message -->
        <div
          class="whatsapp-message-wrapper"
          [ngClass]="{
            'own-message': message.sender?.id === currentUserId,
            'other-message': message.sender?.id !== currentUserId,
            highlighted: highlightedMessageId === message.id,
            selected: selectedMessages.has(message.id!)
          }"
          [id]="'message-' + message.id"
          (click)="onMessageClick(message, $event)"
          (contextmenu)="onMessageContextMenu(message, $event)"
        >
          <!-- Avatar pour les messages des autres -->
          <div
            *ngIf="message.sender?.id !== currentUserId && shouldShowAvatar(i)"
            class="whatsapp-message-avatar"
          >
            <img
              [src]="
                message.sender?.image || 'assets/images/default-avatar.png'
              "
              [alt]="message.sender?.username"
              class="whatsapp-avatar-img"
              (click)="openUserProfile(message.sender?.id!)"
            />
          </div>

          <!-- Contenu du message -->
          <div class="whatsapp-message-content">
            <!-- Nom de l'expéditeur (groupes) -->
            <div
              *ngIf="
                isGroupConversation() &&
                message.sender?.id !== currentUserId &&
                shouldShowSenderName(i)
              "
              class="whatsapp-sender-name"
              [style.color]="getUserColor(message.sender?.id!)"
            >
              {{ message.sender?.username }}
            </div>

            <!-- Bulle de message -->
            <div class="whatsapp-message-bubble">
              <!-- Contenu texte -->
              <div
                *ngIf="getMessageType(message) === 'text'"
                class="whatsapp-text-content"
              >
                <div
                  class="whatsapp-text-body"
                  [innerHTML]="formatMessageContent(message.content)"
                ></div>
              </div>

              <!-- Image -->
              <div *ngIf="hasImage(message)" class="whatsapp-image-content">
                <div
                  class="whatsapp-image-container"
                  (click)="openImageViewer(message)"
                >
                  <img
                    [src]="getImageUrl(message)"
                    [alt]="message.content || 'Image'"
                    class="whatsapp-message-image"
                  />
                  <div class="whatsapp-image-overlay">
                    <i class="fas fa-expand"></i>
                  </div>
                </div>
                <div *ngIf="message.content" class="whatsapp-image-caption">
                  <div
                    [innerHTML]="formatMessageContent(message.content)"
                  ></div>
                </div>
              </div>

              <!-- Fichier -->
              <div *ngIf="hasFile(message)" class="whatsapp-file-content">
                <div
                  class="whatsapp-file-container"
                  (click)="downloadFile(message)"
                >
                  <div class="whatsapp-file-icon">
                    <i [class]="getFileIcon(message)"></i>
                  </div>
                  <div class="whatsapp-file-info">
                    <div class="whatsapp-file-name">
                      {{ getFileName(message) }}
                    </div>
                    <div class="whatsapp-file-size">
                      {{ getFileSize(message) }}
                    </div>
                  </div>
                  <div class="whatsapp-file-actions">
                    <button class="whatsapp-file-download" title="Télécharger">
                      <i class="fas fa-download"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Métadonnées -->
              <div class="whatsapp-message-meta">
                <span class="whatsapp-message-time">{{
                  formatMessageTime(message.timestamp)
                }}</span>

                <!-- Statut de livraison -->
                <div
                  *ngIf="message.sender?.id === currentUserId"
                  class="whatsapp-message-status"
                >
                  <i
                    class="fas fa-clock"
                    *ngIf="message.status === 'SENDING'"
                    title="Envoi en cours"
                  ></i>
                  <i
                    class="fas fa-check"
                    *ngIf="message.status === 'SENT'"
                    title="Envoyé"
                  ></i>
                  <i
                    class="fas fa-check-double"
                    *ngIf="message.status === 'DELIVERED'"
                    title="Livré"
                  ></i>
                  <i
                    class="fas fa-check-double whatsapp-status-read"
                    *ngIf="message.status === 'READ'"
                    title="Lu"
                  ></i>
                </div>
              </div>
            </div>

            <!-- Réactions -->
            <div
              *ngIf="message.reactions && message.reactions.length > 0"
              class="whatsapp-message-reactions"
            >
              <button
                *ngFor="let reaction of message.reactions"
                (click)="toggleReaction(message.id!, reaction.emoji)"
                class="whatsapp-reaction"
                [ngClass]="{
                  'own-reaction': hasUserReacted(reaction, currentUserId || '')
                }"
              >
                <span class="whatsapp-reaction-emoji">{{
                  reaction.emoji
                }}</span>
                <span class="whatsapp-reaction-count">1</span>
              </button>
            </div>
          </div>
        </div>
      </ng-container>

      <!-- Indicateur de frappe -->
      <div *ngIf="isTyping" class="whatsapp-typing-indicator">
        <div class="whatsapp-typing-avatar">
          <img
            [src]="
              otherParticipant?.image || 'assets/images/default-avatar.png'
            "
            [alt]="otherParticipant?.username"
          />
        </div>
        <div class="whatsapp-typing-bubble">
          <div class="whatsapp-typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Zone d'input -->
  <div class="whatsapp-input-container">
    <form [formGroup]="messageForm" (ngSubmit)="sendMessage()">
      <div class="whatsapp-input-wrapper">
        <!-- Boutons d'action gauche -->
        <div class="whatsapp-input-actions-left">
          <button
            type="button"
            (click)="toggleEmojiPicker()"
            class="whatsapp-action-btn"
            [ngClass]="{ active: showEmojiPicker }"
            title="Émojis"
          >
            <i class="fas fa-smile"></i>
          </button>

          <button
            type="button"
            (click)="toggleAttachmentMenu()"
            class="whatsapp-action-btn"
            [ngClass]="{ active: showAttachmentMenu }"
            title="Joindre un fichier"
          >
            <i class="fas fa-paperclip"></i>
          </button>
        </div>

        <!-- Champ de saisie -->
        <div class="whatsapp-input-field-container">
          <textarea
            formControlName="content"
            #messageTextarea
            placeholder="Tapez votre message..."
            class="whatsapp-message-input"
            [disabled]="
              !otherParticipant || isRecordingVoice || isSendingMessage
            "
            (input)="onInputChange($event)"
            (keydown)="onInputKeyDown($event)"
            (focus)="onInputFocus()"
            (blur)="onInputBlur()"
            rows="1"
            maxlength="4096"
            autocomplete="off"
            spellcheck="true"
          ></textarea>
        </div>

        <!-- Boutons d'action droite -->
        <div class="whatsapp-input-actions-right">
          <!-- Bouton d'enregistrement vocal -->
          <button
            *ngIf="!messageForm.get('content')?.value?.trim()"
            type="button"
            (mousedown)="startVoiceRecording()"
            (mouseup)="stopVoiceRecording()"
            (mouseleave)="cancelVoiceRecording()"
            class="whatsapp-action-btn whatsapp-voice-btn"
            [ngClass]="{ recording: isRecordingVoice }"
            title="Maintenir pour enregistrer"
          >
            <i class="fas fa-microphone"></i>
          </button>

          <!-- Bouton d'envoi -->
          <button
            *ngIf="messageForm.get('content')?.value?.trim()"
            type="button"
            (click)="sendMessage()"
            class="whatsapp-send-btn"
            [disabled]="isSendingMessage"
            title="Envoyer"
          >
            <i class="fas fa-paper-plane" *ngIf="!isSendingMessage"></i>
            <i class="fas fa-spinner fa-spin" *ngIf="isSendingMessage"></i>
          </button>
        </div>
      </div>
    </form>
  </div>

  <!-- Sélecteur d'émojis -->
  <div *ngIf="showEmojiPicker" class="whatsapp-emoji-picker">
    <div class="whatsapp-emoji-picker-content">
      <div class="whatsapp-emoji-categories">
        <button
          *ngFor="let category of emojiCategories"
          (click)="selectEmojiCategory(category)"
          class="whatsapp-emoji-category"
          [ngClass]="{ active: selectedEmojiCategory === category }"
        >
          {{ category.icon }}
        </button>
      </div>
      <div class="whatsapp-emoji-grid">
        <button
          *ngFor="let emoji of getEmojisForCategory(selectedEmojiCategory)"
          (click)="insertEmoji(emoji)"
          class="whatsapp-emoji-btn"
          [title]="emoji.name"
        >
          {{ emoji.emoji }}
        </button>
      </div>
    </div>
  </div>

  <!-- Menu des pièces jointes -->
  <div *ngIf="showAttachmentMenu" class="whatsapp-attachment-menu">
    <div class="whatsapp-attachment-grid">
      <button
        (click)="triggerFileInput('image')"
        class="whatsapp-attachment-option"
      >
        <div class="whatsapp-attachment-icon">
          <i class="fas fa-image"></i>
        </div>
        <span>Photos</span>
      </button>
      <button
        (click)="triggerFileInput('video')"
        class="whatsapp-attachment-option"
      >
        <div class="whatsapp-attachment-icon">
          <i class="fas fa-video"></i>
        </div>
        <span>Vidéos</span>
      </button>
      <button
        (click)="triggerFileInput('document')"
        class="whatsapp-attachment-option"
      >
        <div class="whatsapp-attachment-icon">
          <i class="fas fa-file"></i>
        </div>
        <span>Documents</span>
      </button>
      <button (click)="openCamera()" class="whatsapp-attachment-option">
        <div class="whatsapp-attachment-icon">
          <i class="fas fa-camera"></i>
        </div>
        <span>Caméra</span>
      </button>
    </div>
  </div>

  <!-- Inputs cachés pour les fichiers -->
  <input
    #fileInput
    type="file"
    style="display: none"
    (change)="onFileSelected($event)"
    [accept]="getFileAcceptTypes()"
    multiple
  />

  <!-- Overlay pour fermer les menus -->
  <div
    *ngIf="showEmojiPicker || showAttachmentMenu"
    class="whatsapp-modal-backdrop"
    (click)="closeAllMenus()"
  ></div>
</div>
