<div class="chat-container">
  <!-- En-tête -->
  <div class="chat-header">
    <button (click)="goBackToConversations()" class="action-btn">
      <i class="fas fa-arrow-left"></i>
    </button>

    <img
      [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
      alt="User avatar"
      class="user-avatar"
    />

    <div class="user-info" *ngIf="otherParticipant">
      <h3>{{ otherParticipant.username }}</h3>
      <div class="user-status">
        {{
          otherParticipant.isOnline
            ? "En ligne"
            : formatLastActive(otherParticipant.lastActive)
        }}
      </div>
    </div>

    <div class="whatsapp-actions">
      <!-- Boutons d'action consolidés -->
      <ng-container *ngFor="let action of getHeaderActions()">
        <button
          [class]="'whatsapp-action-button ' + action.class"
          [ngClass]="
            action.activeClass && action.isActive ? action.activeClass : {}
          "
          [title]="action.title"
          (click)="action.onClick()"
        >
          <i [class]="action.icon"></i>
          <!-- Badge universel -->
          <span
            *ngIf="action.badge && action.badge.count > 0"
            [class]="
              'absolute -top-2 -right-2 min-w-[20px] h-5 px-1.5 text-white text-xs rounded-md flex items-center justify-center font-bold shadow-lg border border-white/20 ' +
              action.badge.class
            "
            [ngClass]="{ 'animate-pulse': action.badge.animate }"
          >
            {{ action.badge.count > 99 ? "99+" : action.badge.count }}
          </span>
        </button>
      </ng-container>

      <!-- Bouton du statut utilisateur -->
      <div class="relative">
        <button
          (click)="toggleStatusSelector()"
          class="whatsapp-action-button relative"
          [ngClass]="{
            'text-[#4f5fad] dark:text-[#6d78c9]': showStatusSelector
          }"
          title="Statut utilisateur"
        >
          <i
            [class]="getStatusIcon(currentUserStatus)"
            [ngClass]="getStatusColor(currentUserStatus)"
          ></i>
          <!-- Indicateur de mise à jour -->
          <span
            *ngIf="isUpdatingStatus"
            class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse"
          ></span>
        </button>

        <!-- Menu déroulant du statut -->
        <div
          *ngIf="showStatusSelector"
          class="absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden"
        >
          <div
            class="p-3 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] bg-gradient-to-r from-[#4f5fad]/10 to-[#6d78c9]/10"
          >
            <div class="flex items-center justify-between">
              <span>Statut actuel</span>
              <span
                class="font-medium"
                [ngClass]="getStatusColor(currentUserStatus)"
              >
                {{ getStatusText(currentUserStatus) }}
              </span>
            </div>
          </div>

          <div class="p-1">
            <!-- Boutons de statut consolidés avec *ngFor -->
            <button
              *ngFor="let status of getStatusOptions()"
              (click)="updateUserStatus(status.key); toggleStatusSelector()"
              [disabled]="isUpdatingStatus"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
              [ngClass]="{
                'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20':
                  currentUserStatus === status.key
              }"
            >
              <div class="flex items-center">
                <i
                  [class]="status.icon + ' ' + status.color + ' mr-3 text-xs'"
                ></i>
                <div>
                  <div class="font-medium">{{ status.label }}</div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    {{ status.description }}
                  </div>
                </div>
              </div>
            </button>
          </div>

          <div class="border-t border-[#edf1f4]/50 dark:border-[#2a2a2a] p-1">
            <button
              (click)="toggleUserStatusPanel(); toggleStatusSelector()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <i
                  class="fas fa-users text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xs"
                ></i>
                <div>
                  <div class="font-medium">Voir tous les utilisateurs</div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    {{ getOnlineUsersCount() }} en ligne
                  </div>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- Sélecteur de thème -->
      <div class="relative">
        <button
          (click)="toggleThemeSelector()"
          class="whatsapp-action-button btn-theme"
        >
          <i class="fas fa-palette"></i>
        </button>

        <!-- Menu déroulant des thèmes -->
        <div
          *ngIf="showThemeSelector"
          class="theme-selector-menu absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden"
        >
          <div
            class="p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]"
          >
            Choisir un thème
          </div>
          <div class="p-1">
            <!-- Boutons de thème consolidés avec *ngFor -->
            <a
              *ngFor="let theme of getThemeOptions()"
              href="javascript:void(0)"
              (click)="changeTheme(theme.key)"
              class="block w-full text-left px-3 py-2 text-sm rounded-md transition-colors"
              [ngClass]="
                'hover:bg-' +
                theme.hoverColor +
                '/10 dark:hover:bg-' +
                theme.hoverColor +
                '/10'
              "
            >
              <div class="flex items-center">
                <div
                  [ngClass]="'w-4 h-4 rounded-full bg-' + theme.color + ' mr-2'"
                ></div>
                <div>{{ theme.label }}</div>
              </div>
            </a>
          </div>
        </div>
      </div>

      <!-- Bouton menu principal -->
      <div class="relative">
        <button
          (click)="toggleMainMenu()"
          class="whatsapp-action-button btn-menu"
          title="Menu principal"
        >
          <i class="fas fa-ellipsis-v"></i>
        </button>

        <!-- Menu déroulant principal -->
        <div
          *ngIf="showMainMenu"
          class="absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden"
        >
          <div
            class="p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]"
          >
            Options de conversation
          </div>
          <div class="p-1">
            <button
              (click)="clearConversation()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-red-500/10 dark:hover:bg-red-500/10 transition-colors text-red-600 dark:text-red-400"
            >
              <div class="flex items-center">
                <i class="fas fa-trash mr-3 text-xs"></i>
                <div>Vider la conversation</div>
              </div>
            </button>
            <button
              (click)="exportConversation()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <i class="fas fa-download mr-3 text-xs"></i>
                <div>Exporter la conversation</div>
              </div>
            </button>
            <button
              (click)="toggleConversationInfo()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <i class="fas fa-info-circle mr-3 text-xs"></i>
                <div>Informations</div>
              </div>
            </button>
            <button
              (click)="toggleConversationSettings()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <i class="fas fa-cog mr-3 text-xs"></i>
                <div>Paramètres</div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Zone des messages avec fonctionnalités complètes -->
  <div
    class="whatsapp-messages-container flex-1 overflow-hidden relative"
    #messagesContainer
    (scroll)="onScroll($event)"
  >
    <!-- Indicateur de chargement initial -->
    <div *ngIf="loading" class="whatsapp-loading-state">
      <div class="whatsapp-loading-content">
        <div class="whatsapp-loading-spinner"></div>
        <span class="whatsapp-loading-text">Chargement des messages...</span>
      </div>
    </div>

    <!-- État vide avec suggestions -->
    <div *ngIf="!loading && messages.length === 0" class="whatsapp-empty-state">
      <div class="whatsapp-empty-content">
        <div class="whatsapp-empty-icon">
          <i class="fas fa-comments"></i>
        </div>
        <h3 class="whatsapp-empty-title">Aucun message</h3>
        <p class="whatsapp-empty-description">
          Commencez votre conversation avec {{ otherParticipant?.username }}
        </p>
        <div class="whatsapp-empty-suggestions">
          <button
            (click)="sendQuickMessage('Salut ! 👋')"
            class="whatsapp-quick-message-btn"
          >
            <i class="fas fa-hand-wave"></i>
            Dire bonjour
          </button>
          <button
            (click)="sendQuickMessage('Comment ça va ?')"
            class="whatsapp-quick-message-btn"
          >
            <i class="fas fa-smile"></i>
            Prendre des nouvelles
          </button>
          <button
            (click)="sendQuickMessage('Quoi de neuf ?')"
            class="whatsapp-quick-message-btn"
          >
            <i class="fas fa-question"></i>
            Demander des nouvelles
          </button>
        </div>
      </div>
    </div>

    <!-- Bouton charger plus de messages -->
    <div
      *ngIf="hasMoreMessages && !loading"
      class="whatsapp-load-more-container"
    >
      <button
        (click)="loadMoreMessages()"
        [disabled]="isLoadingMore"
        class="whatsapp-load-more-btn"
      >
        <i class="fas fa-chevron-up" *ngIf="!isLoadingMore"></i>
        <i class="fas fa-spinner fa-spin" *ngIf="isLoadingMore"></i>
        <span>{{ isLoadingMore ? "Chargement..." : "Charger plus" }}</span>
      </button>
    </div>

    <!-- Messages avec toutes les fonctionnalités -->
    <div
      class="whatsapp-messages-list"
      [ngClass]="{ 'search-mode': searchMode, 'selection-mode': selectionMode }"
    >
      <ng-container
        *ngFor="
          let message of messages;
          let i = index;
          trackBy: trackByMessageId
        "
      >
        <!-- Séparateur de date -->
        <div *ngIf="shouldShowDateSeparator(i)" class="whatsapp-date-separator">
          <div class="whatsapp-date-separator-content">
            <span>{{ formatDateSeparator(message.timestamp) }}</span>
          </div>
        </div>

        <!-- Notification système -->
        <div *ngIf="message.type === 'system'" class="whatsapp-system-message">
          <div class="whatsapp-system-content">
            <i [class]="getSystemMessageIcon(message)"></i>
            <span>{{ message.content }}</span>
          </div>
        </div>

        <!-- Message normal -->
        <div
          *ngIf="message.type !== 'system'"
          class="whatsapp-message-wrapper"
          [ngClass]="{
            'own-message': message.sender?.id === currentUserId,
            'other-message': message.sender?.id !== currentUserId,
            highlighted: highlightedMessageId === message.id,
            editing: editingMessageId === message.id,
            selected: selectedMessages.includes(message.id!),
            'search-result': searchResultIds.includes(message.id!),
            pinned: message.pinned,
            forwarded: message.forwarded,
            reply: message.replyTo
          }"
          [id]="'message-' + message.id"
          (click)="onMessageClick(message, $event)"
          (contextmenu)="onMessageContextMenu(message, $event)"
          (mouseenter)="onMessageHover(message.id!, true)"
          (mouseleave)="onMessageHover(message.id!, false)"
        >
          <!-- Checkbox de sélection (mode sélection multiple) -->
          <div *ngIf="selectionMode" class="whatsapp-message-checkbox">
            <input
              type="checkbox"
              [checked]="selectedMessages.includes(message.id!)"
              (change)="toggleMessageSelection(message.id!)"
              class="whatsapp-checkbox"
            />
          </div>

          <!-- Avatar pour les messages des autres -->
          <div
            *ngIf="message.sender?.id !== currentUserId && shouldShowAvatar(i)"
            class="whatsapp-message-avatar"
          >
            <img
              [src]="
                message.sender?.image || 'assets/images/default-avatar.png'
              "
              [alt]="message.sender?.username"
              class="whatsapp-avatar-img"
              (error)="onAvatarError($event)"
              (click)="openUserProfile(message.sender?.id!)"
            />
            <div
              *ngIf="message.sender?.isOnline"
              class="whatsapp-online-indicator"
            ></div>
          </div>

          <!-- Contenu du message -->
          <div class="whatsapp-message-content">
            <!-- Nom de l'expéditeur (groupes) -->
            <div
              *ngIf="
                isGroupConversation &&
                message.sender?.id !== currentUserId &&
                shouldShowSenderName(i)
              "
              class="whatsapp-sender-name"
              [style.color]="getUserColor(message.sender?.id!)"
            >
              {{ message.sender?.username }}
            </div>

            <!-- Indicateur de transfert -->
            <div *ngIf="message.forwarded" class="whatsapp-forwarded-indicator">
              <i class="fas fa-share"></i>
              <span>Transféré</span>
            </div>

            <!-- Message de réponse -->
            <div
              *ngIf="message.replyTo"
              class="whatsapp-reply-message"
              (click)="scrollToMessage(message.replyTo.id!)"
            >
              <div
                class="whatsapp-reply-border"
                [style.border-color]="getUserColor(message.replyTo.sender?.id!)"
              ></div>
              <div class="whatsapp-reply-content">
                <div
                  class="whatsapp-reply-sender"
                  [style.color]="getUserColor(message.replyTo.sender?.id!)"
                >
                  {{ message.replyTo.sender?.username }}
                </div>
                <div class="whatsapp-reply-text">
                  <span *ngIf="message.replyTo.content">{{
                    truncateText(message.replyTo.content, 50)
                  }}</span>
                  <span
                    *ngIf="hasImage(message.replyTo)"
                    class="whatsapp-reply-media"
                  >
                    <i class="fas fa-image"></i> Photo
                  </span>
                  <span
                    *ngIf="isVoiceMessage(message.replyTo)"
                    class="whatsapp-reply-media"
                  >
                    <i class="fas fa-microphone"></i> Message vocal
                  </span>
                  <span
                    *ngIf="hasFile(message.replyTo)"
                    class="whatsapp-reply-media"
                  >
                    <i class="fas fa-file"></i>
                    {{ getFileName(message.replyTo) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Bulle de message -->
            <div
              class="whatsapp-message-bubble"
              [ngClass]="getMessageBubbleClass(message)"
            >
              <!-- Mode édition -->
              <div
                *ngIf="editingMessageId === message.id"
                class="whatsapp-edit-mode"
              >
                <div class="whatsapp-edit-input-container">
                  <textarea
                    [(ngModel)]="editingContent"
                    class="whatsapp-edit-input"
                    (keydown)="onEditKeyDown($event, message.id!)"
                    #editTextarea
                    rows="1"
                    placeholder="Modifier le message..."
                    maxlength="4096"
                  ></textarea>
                  <div class="whatsapp-edit-char-count">
                    {{ editingContent?.length || 0 }}/4096
                  </div>
                </div>
                <div class="whatsapp-edit-actions">
                  <button
                    (click)="cancelEdit()"
                    class="whatsapp-edit-cancel"
                    title="Annuler"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                  <button
                    (click)="saveEdit(message.id!)"
                    class="whatsapp-edit-save"
                    title="Sauvegarder"
                    [disabled]="!editingContent?.trim()"
                  >
                    <i class="fas fa-check"></i>
                  </button>
                </div>
              </div>

              <!-- Contenu normal du message -->
              <div
                *ngIf="editingMessageId !== message.id"
                class="whatsapp-message-body"
              >
                <!-- Message texte avec formatage -->
                <div
                  *ngIf="getMessageType(message) === 'text'"
                  class="whatsapp-text-content"
                >
                  <div
                    class="whatsapp-text-body"
                    [innerHTML]="formatMessageContent(message.content)"
                  ></div>
                </div>

                <!-- Message image avec prévisualisation -->
                <div *ngIf="hasImage(message)" class="whatsapp-image-content">
                  <div
                    class="whatsapp-image-container"
                    (click)="openImageViewer(message)"
                  >
                    <img
                      [src]="getImageUrl(message)"
                      [alt]="message.content || 'Image'"
                      class="whatsapp-message-image"
                      (load)="onImageLoad($event)"
                      (error)="onImageError($event)"
                      [style.filter]="
                        isImageLoading(message) ? 'blur(5px)' : 'none'
                      "
                    />
                    <div
                      *ngIf="isImageLoading(message)"
                      class="whatsapp-image-loading"
                    >
                      <div class="whatsapp-image-spinner"></div>
                      <span>{{ getImageLoadingProgress(message) }}%</span>
                    </div>
                    <div class="whatsapp-image-overlay">
                      <i class="fas fa-expand"></i>
                    </div>
                    <div
                      *ngIf="getImageDimensions(message)"
                      class="whatsapp-image-info"
                    >
                      {{ getImageDimensions(message) }}
                    </div>
                  </div>
                  <div *ngIf="message.content" class="whatsapp-image-caption">
                    <div
                      [innerHTML]="formatMessageContent(message.content)"
                    ></div>
                  </div>
                </div>

                <!-- Message vocal avec contrôles avancés -->
                <div
                  *ngIf="isVoiceMessage(message)"
                  class="whatsapp-voice-content"
                >
                  <div class="whatsapp-voice-player">
                    <button
                      (click)="toggleVoicePlayback(message.id!)"
                      class="whatsapp-voice-play-btn"
                      [ngClass]="{
                        playing: playingVoiceId === message.id,
                        loading: isVoiceLoading(message.id!)
                      }"
                    >
                      <i
                        class="fas fa-play"
                        *ngIf="
                          playingVoiceId !== message.id &&
                          !isVoiceLoading(message.id!)
                        "
                      ></i>
                      <i
                        class="fas fa-pause"
                        *ngIf="
                          playingVoiceId === message.id &&
                          !isVoiceLoading(message.id!)
                        "
                      ></i>
                      <i
                        class="fas fa-spinner fa-spin"
                        *ngIf="isVoiceLoading(message.id!)"
                      ></i>
                    </button>

                    <div
                      class="whatsapp-voice-waveform"
                      (click)="seekVoiceMessage(message.id!, $event)"
                    >
                      <div
                        *ngFor="
                          let bar of getVoiceWaveform(message);
                          let j = index
                        "
                        class="whatsapp-voice-bar"
                        [style.height.px]="bar.height"
                        [ngClass]="{
                          active: j <= getVoiceProgress(message.id!),
                          played: j < getVoiceProgress(message.id!)
                        }"
                      ></div>
                    </div>

                    <div class="whatsapp-voice-info">
                      <div class="whatsapp-voice-duration">
                        {{
                          formatVoiceDuration(
                            getVoiceCurrentTime(message.id!),
                            getVoiceTotalDuration(message.id!)
                          )
                        }}
                      </div>
                      <div
                        class="whatsapp-voice-speed"
                        *ngIf="playingVoiceId === message.id"
                      >
                        <button
                          (click)="changeVoiceSpeed(message.id!)"
                          class="whatsapp-voice-speed-btn"
                        >
                          {{ getVoiceSpeed(message.id!) }}x
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Message fichier avec prévisualisation -->
                <div *ngIf="hasFile(message)" class="whatsapp-file-content">
                  <div
                    class="whatsapp-file-container"
                    (click)="downloadFile(message)"
                  >
                    <div class="whatsapp-file-icon">
                      <i [class]="getFileIcon(message)"></i>
                    </div>
                    <div class="whatsapp-file-info">
                      <div class="whatsapp-file-name">
                        {{ getFileName(message) }}
                      </div>
                      <div class="whatsapp-file-details">
                        <span class="whatsapp-file-size">{{
                          getFileSize(message)
                        }}</span>
                        <span class="whatsapp-file-type">{{
                          getFileType(message)
                        }}</span>
                      </div>
                    </div>
                    <div class="whatsapp-file-actions">
                      <button
                        (click)="
                          downloadFile(message); $event.stopPropagation()
                        "
                        class="whatsapp-file-download"
                        title="Télécharger"
                      >
                        <i class="fas fa-download"></i>
                      </button>
                      <button
                        *ngIf="canPreviewFile(message)"
                        (click)="previewFile(message); $event.stopPropagation()"
                        class="whatsapp-file-preview"
                        title="Aperçu"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                    </div>
                  </div>
                  <div
                    *ngIf="isFileDownloading(message)"
                    class="whatsapp-file-progress"
                  >
                    <div class="whatsapp-progress-bar">
                      <div
                        class="whatsapp-progress-fill"
                        [style.width.%]="getFileDownloadProgress(message)"
                      ></div>
                    </div>
                    <span class="whatsapp-progress-text"
                      >{{ getFileDownloadProgress(message) }}%</span
                    >
                  </div>
                </div>

                <!-- Message vidéo -->
                <div *ngIf="hasVideo(message)" class="whatsapp-video-content">
                  <div
                    class="whatsapp-video-container"
                    (click)="openVideoPlayer(message)"
                  >
                    <video
                      [src]="getVideoUrl(message)"
                      class="whatsapp-message-video"
                      [poster]="getVideoThumbnail(message)"
                      preload="metadata"
                      (loadedmetadata)="onVideoLoaded($event, message)"
                    ></video>
                    <div class="whatsapp-video-overlay">
                      <button class="whatsapp-video-play-btn">
                        <i class="fas fa-play"></i>
                      </button>
                    </div>
                    <div class="whatsapp-video-info">
                      <span class="whatsapp-video-duration">{{
                        getVideoDuration(message)
                      }}</span>
                    </div>
                  </div>
                  <div *ngIf="message.content" class="whatsapp-video-caption">
                    <div
                      [innerHTML]="formatMessageContent(message.content)"
                    ></div>
                  </div>
                </div>

                <!-- Message de localisation -->
                <div
                  *ngIf="isLocationMessage(message)"
                  class="whatsapp-location-content"
                >
                  <div
                    class="whatsapp-location-container"
                    (click)="openLocationViewer(message)"
                  >
                    <div class="whatsapp-location-map">
                      <img
                        [src]="getLocationMapUrl(message)"
                        alt="Carte"
                        class="whatsapp-location-image"
                      />
                      <div class="whatsapp-location-overlay">
                        <i class="fas fa-map-marker-alt"></i>
                      </div>
                    </div>
                    <div class="whatsapp-location-info">
                      <div class="whatsapp-location-name">
                        {{ getLocationName(message) }}
                      </div>
                      <div class="whatsapp-location-address">
                        {{ getLocationAddress(message) }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Message de contact -->
                <div
                  *ngIf="isContactMessage(message)"
                  class="whatsapp-contact-content"
                >
                  <div
                    class="whatsapp-contact-container"
                    (click)="openContactViewer(message)"
                  >
                    <div class="whatsapp-contact-avatar">
                      <img
                        [src]="getContactAvatar(message)"
                        [alt]="getContactName(message)"
                      />
                    </div>
                    <div class="whatsapp-contact-info">
                      <div class="whatsapp-contact-name">
                        {{ getContactName(message) }}
                      </div>
                      <div class="whatsapp-contact-phone">
                        {{ getContactPhone(message) }}
                      </div>
                    </div>
                    <div class="whatsapp-contact-actions">
                      <button
                        class="whatsapp-contact-add"
                        title="Ajouter aux contacts"
                      >
                        <i class="fas fa-user-plus"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Métadonnées du message -->
                <div class="whatsapp-message-meta">
                  <span class="whatsapp-message-time">{{
                    formatMessageTime(message.timestamp)
                  }}</span>

                  <!-- Statut de livraison (messages envoyés) -->
                  <div
                    *ngIf="message.sender?.id === currentUserId"
                    class="whatsapp-message-status"
                  >
                    <i
                      class="fas fa-clock whatsapp-status-sending"
                      *ngIf="message.status === 'sending'"
                      title="Envoi en cours"
                    ></i>
                    <i
                      class="fas fa-check whatsapp-status-sent"
                      *ngIf="message.status === 'sent'"
                      title="Envoyé"
                    ></i>
                    <i
                      class="fas fa-check-double whatsapp-status-delivered"
                      *ngIf="message.status === 'delivered'"
                      title="Livré"
                    ></i>
                    <i
                      class="fas fa-check-double whatsapp-status-read"
                      *ngIf="message.status === 'read'"
                      title="Lu"
                    ></i>
                    <i
                      class="fas fa-exclamation-triangle whatsapp-status-failed"
                      *ngIf="message.status === 'failed'"
                      title="Échec d'envoi"
                    ></i>
                  </div>

                  <!-- Indicateur de modification -->
                  <span
                    *ngIf="message.edited"
                    class="whatsapp-edited-indicator"
                    [title]="
                      'Modifié le ' + formatMessageTime(message.editedAt!)
                    "
                  >
                    modifié
                  </span>

                  <!-- Indicateur d'épinglage -->
                  <i
                    *ngIf="message.pinned"
                    class="fas fa-thumbtack whatsapp-pinned-indicator"
                    title="Message épinglé"
                  ></i>
                </div>
              </div>

              <!-- Réactions -->
              <div
                *ngIf="message.reactions && message.reactions.length > 0"
                class="whatsapp-message-reactions"
              >
                <div class="whatsapp-reactions-container">
                  <button
                    *ngFor="let reaction of message.reactions"
                    (click)="toggleReaction(message.id!, reaction.emoji)"
                    class="whatsapp-reaction-item"
                    [ngClass]="{
                      'own-reaction': hasUserReacted(reaction, currentUserId)
                    }"
                    [title]="getReactionTooltip(reaction)"
                  >
                    <span class="whatsapp-reaction-emoji">{{
                      reaction.emoji
                    }}</span>
                    <span class="whatsapp-reaction-count">{{
                      reaction.count
                    }}</span>
                  </button>
                  <button
                    (click)="toggleReactionPicker(message.id!)"
                    class="whatsapp-reaction-add"
                    title="Ajouter une réaction"
                  >
                    <i class="fas fa-plus"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Menu d'options du message -->
            <div
              *ngIf="showMessageOptions === message.id"
              class="whatsapp-message-options"
              [ngClass]="{
                'own-message': message.sender?.id === currentUserId
              }"
            >
              <button
                (click)="replyToMessage(message)"
                class="whatsapp-option-btn"
                title="Répondre"
              >
                <i class="fas fa-reply"></i>
                <span>Répondre</span>
              </button>
              <button
                (click)="toggleReactionPicker(message.id!)"
                class="whatsapp-option-btn"
                title="Réagir"
              >
                <i class="fas fa-smile"></i>
                <span>Réagir</span>
              </button>
              <button
                (click)="forwardMessage(message)"
                class="whatsapp-option-btn"
                title="Transférer"
              >
                <i class="fas fa-share"></i>
                <span>Transférer</span>
              </button>
              <button
                (click)="copyMessage(message.content!)"
                class="whatsapp-option-btn"
                title="Copier"
              >
                <i class="fas fa-copy"></i>
                <span>Copier</span>
              </button>
              <button
                (click)="togglePinMessage(message.id!)"
                class="whatsapp-option-btn"
                [title]="message.pinned ? 'Désépingler' : 'Épingler'"
              >
                <i
                  class="fas fa-thumbtack"
                  [ngClass]="{ pinned: message.pinned }"
                ></i>
                <span>{{ message.pinned ? "Désépingler" : "Épingler" }}</span>
              </button>
              <button
                (click)="selectMessage(message.id!)"
                class="whatsapp-option-btn"
                title="Sélectionner"
              >
                <i class="fas fa-check-square"></i>
                <span>Sélectionner</span>
              </button>
              <button
                *ngIf="message.sender?.id === currentUserId"
                (click)="startEditMessage(message)"
                class="whatsapp-option-btn"
                title="Modifier"
              >
                <i class="fas fa-edit"></i>
                <span>Modifier</span>
              </button>
              <button
                *ngIf="canDeleteMessage(message)"
                (click)="deleteMessage(message.id!)"
                class="whatsapp-option-btn whatsapp-delete-btn"
                title="Supprimer"
              >
                <i class="fas fa-trash"></i>
                <span>Supprimer</span>
              </button>
            </div>

            <!-- Sélecteur de réactions -->
            <div
              *ngIf="showReactionPicker === message.id"
              class="whatsapp-reaction-picker"
            >
              <div class="whatsapp-reaction-picker-content">
                <button
                  *ngFor="let emoji of commonEmojis"
                  (click)="addReaction(message.id!, emoji)"
                  class="whatsapp-reaction-picker-btn"
                  [title]="getEmojiName(emoji)"
                >
                  {{ emoji }}
                </button>
                <button
                  (click)="openEmojiPicker(message.id!)"
                  class="whatsapp-reaction-picker-more"
                  title="Plus d'émojis"
                >
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Bouton d'options (trois points) -->
          <button
            *ngIf="!selectionMode"
            (click)="toggleMessageOptions(message.id!)"
            class="whatsapp-message-menu-btn"
            [ngClass]="{
              visible:
                showMessageOptions === message.id ||
                hoveredMessageId === message.id
            }"
          >
            <i class="fas fa-ellipsis-v"></i>
          </button>
        </div>
      </ng-container>

      <!-- Indicateur de frappe -->
      <div *ngIf="isTyping" class="whatsapp-typing-indicator">
        <div class="whatsapp-typing-avatar">
          <img
            [src]="
              otherParticipant?.image || 'assets/images/default-avatar.png'
            "
            [alt]="otherParticipant?.username"
          />
        </div>
        <div class="whatsapp-typing-bubble">
          <div class="whatsapp-typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <div class="whatsapp-typing-text">
          {{ otherParticipant?.username }} est en train d'écrire...
        </div>
      </div>
    </div>

    <!-- Bouton de retour en bas -->
    <div *ngIf="showScrollToBottom" class="whatsapp-scroll-to-bottom">
      <button (click)="scrollToBottom()" class="whatsapp-scroll-btn">
        <i class="fas fa-chevron-down"></i>
        <span *ngIf="unreadMessagesCount > 0" class="whatsapp-unread-count">
          {{ unreadMessagesCount > 99 ? "99+" : unreadMessagesCount }}
        </span>
      </button>
    </div>

    <!-- Overlay de sélection multiple -->
    <div *ngIf="selectionMode" class="whatsapp-selection-overlay">
      <div class="whatsapp-selection-header">
        <button (click)="exitSelectionMode()" class="whatsapp-selection-close">
          <i class="fas fa-times"></i>
        </button>
        <span class="whatsapp-selection-count"
          >{{ selectedMessages.length }} sélectionné(s)</span
        >
        <div class="whatsapp-selection-actions">
          <button
            (click)="deleteSelectedMessages()"
            class="whatsapp-selection-action"
            title="Supprimer"
          >
            <i class="fas fa-trash"></i>
          </button>
          <button
            (click)="forwardSelectedMessages()"
            class="whatsapp-selection-action"
            title="Transférer"
          >
            <i class="fas fa-share"></i>
          </button>
          <button
            (click)="copySelectedMessages()"
            class="whatsapp-selection-action"
            title="Copier"
          >
            <i class="fas fa-copy"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Message de réponse en cours -->
  <div *ngIf="replyingToMessage" class="whatsapp-reply-preview">
    <div class="whatsapp-reply-preview-content">
      <div class="whatsapp-reply-preview-header">
        <div class="whatsapp-reply-preview-info">
          <i class="fas fa-reply"></i>
          <span>Répondre à {{ replyingToMessage.sender?.username }}</span>
        </div>
        <button (click)="cancelReply()" class="whatsapp-reply-cancel">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="whatsapp-reply-preview-message">
        <span *ngIf="replyingToMessage.content">{{
          replyingToMessage.content
        }}</span>
        <span *ngIf="hasImage(replyingToMessage)" class="whatsapp-reply-media">
          <i class="fas fa-image"></i> Photo
        </span>
        <span
          *ngIf="isVoiceMessage(replyingToMessage)"
          class="whatsapp-reply-media"
        >
          <i class="fas fa-microphone"></i> Message vocal
        </span>
      </div>
    </div>
  </div>

  <!-- Prévisualisation de fichier -->
  <div *ngIf="selectedFile" class="whatsapp-file-preview">
    <div class="whatsapp-file-preview-content">
      <div class="whatsapp-file-preview-header">
        <span>Fichier sélectionné</span>
        <button (click)="removeSelectedFile()" class="whatsapp-file-remove">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="whatsapp-file-preview-body">
        <div *ngIf="previewUrl" class="whatsapp-file-preview-image">
          <img [src]="previewUrl" [alt]="selectedFile.name" />
        </div>
        <div class="whatsapp-file-preview-info">
          <div class="whatsapp-file-preview-name">{{ selectedFile.name }}</div>
          <div class="whatsapp-file-preview-size">
            {{ formatFileSize(selectedFile.size) }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enregistrement vocal -->
  <div *ngIf="isRecordingVoice" class="whatsapp-voice-recording">
    <div class="whatsapp-voice-recording-content">
      <div class="whatsapp-voice-recording-info">
        <div class="whatsapp-voice-recording-icon">
          <i class="fas fa-microphone"></i>
        </div>
        <div class="whatsapp-voice-recording-details">
          <span class="whatsapp-voice-recording-text"
            >Enregistrement en cours...</span
          >
          <span class="whatsapp-voice-recording-duration">{{
            formatRecordingDuration(voiceRecordingDuration)
          }}</span>
        </div>
      </div>
      <div class="whatsapp-voice-recording-actions">
        <button (click)="cancelVoiceRecording()" class="whatsapp-voice-cancel">
          <i class="fas fa-trash"></i>
        </button>
        <button (click)="stopVoiceRecording()" class="whatsapp-voice-send">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>
    <div class="whatsapp-voice-recording-waveform">
      <div
        *ngFor="let bar of recordingWaveform"
        class="whatsapp-voice-recording-bar"
        [style.height.px]="bar"
      ></div>
    </div>
  </div>

  <!-- Zone de saisie principale -->
  <div class="whatsapp-input-container">
    <div class="whatsapp-input-wrapper">
      <!-- Boutons d'action gauche -->
      <div class="whatsapp-input-actions-left">
        <button
          type="button"
          (click)="toggleEmojiPicker()"
          class="whatsapp-input-action-btn"
          [ngClass]="{ active: showEmojiPicker }"
          title="Émojis"
        >
          <i class="fas fa-smile"></i>
        </button>

        <div class="whatsapp-attachment-menu">
          <button
            type="button"
            (click)="toggleAttachmentMenu()"
            class="whatsapp-input-action-btn"
            [ngClass]="{ active: showAttachmentMenu }"
            title="Joindre un fichier"
          >
            <i class="fas fa-paperclip"></i>
          </button>

          <!-- Menu des pièces jointes avancé -->
          <div *ngIf="showAttachmentMenu" class="whatsapp-attachment-dropdown">
            <div class="whatsapp-attachment-grid">
              <button
                (click)="triggerFileInput('image')"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-image"></i>
                </div>
                <span>Photos</span>
                <small>JPG, PNG, GIF</small>
              </button>
              <button
                (click)="triggerFileInput('video')"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-video"></i>
                </div>
                <span>Vidéos</span>
                <small>MP4, AVI, MOV</small>
              </button>
              <button
                (click)="triggerFileInput('audio')"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-music"></i>
                </div>
                <span>Audio</span>
                <small>MP3, WAV, OGG</small>
              </button>
              <button
                (click)="triggerFileInput('document')"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-file"></i>
                </div>
                <span>Documents</span>
                <small>PDF, DOC, TXT</small>
              </button>
              <button (click)="openCamera()" class="whatsapp-attachment-option">
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-camera"></i>
                </div>
                <span>Caméra</span>
                <small>Photo/Vidéo</small>
              </button>
              <button
                (click)="openLocationPicker()"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <span>Position</span>
                <small>Partager lieu</small>
              </button>
              <button
                (click)="openContactPicker()"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-user"></i>
                </div>
                <span>Contact</span>
                <small>Partager contact</small>
              </button>
              <button
                (click)="openPollCreator()"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-poll"></i>
                </div>
                <span>Sondage</span>
                <small>Créer sondage</small>
              </button>
            </div>

            <!-- Options rapides -->
            <div class="whatsapp-attachment-quick">
              <button (click)="openGifPicker()" class="whatsapp-quick-option">
                <i class="fas fa-gif"></i>
                <span>GIF</span>
              </button>
              <button
                (click)="openStickerPicker()"
                class="whatsapp-quick-option"
              >
                <i class="fas fa-sticky-note"></i>
                <span>Autocollants</span>
              </button>
              <button (click)="openDrawingTool()" class="whatsapp-quick-option">
                <i class="fas fa-paint-brush"></i>
                <span>Dessiner</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Zone de saisie de texte -->
      <div class="whatsapp-input-text-container">
        <form
          [formGroup]="messageForm"
          (ngSubmit)="sendMessage()"
          class="whatsapp-input-form"
        >
          <div class="whatsapp-input-field-wrapper">
            <textarea
              formControlName="content"
              #messageTextarea
              placeholder="Tapez un message"
              class="whatsapp-input-field"
              [disabled]="!otherParticipant || isRecordingVoice"
              (input)="onInputChange($event)"
              (keydown)="onInputKeyDown($event)"
              (focus)="onInputFocus()"
              (blur)="onInputBlur()"
              rows="1"
              maxlength="4096"
            ></textarea>

            <!-- Compteur de caractères -->
            <div
              *ngIf="messageForm.get('content')?.value?.length > 3000"
              class="whatsapp-char-counter"
            >
              {{ 4096 - (messageForm.get("content")?.value?.length || 0) }}
            </div>
          </div>
        </form>
      </div>

      <!-- Boutons d'action droite -->
      <div class="whatsapp-input-actions-right">
        <!-- Bouton d'envoi ou microphone -->
        <div class="whatsapp-send-container">
          <button
            *ngIf="hasMessageContent() || selectedFile"
            type="submit"
            (click)="sendMessage()"
            class="whatsapp-send-btn"
            [disabled]="!canSendMessage()"
            [ngClass]="{ sending: isSendingMessage }"
            title="Envoyer"
          >
            <i class="fas fa-paper-plane" *ngIf="!isSendingMessage"></i>
            <i class="fas fa-spinner fa-spin" *ngIf="isSendingMessage"></i>
          </button>

          <button
            *ngIf="!hasMessageContent() && !selectedFile"
            type="button"
            (mousedown)="startVoiceRecording()"
            (mouseup)="stopVoiceRecording()"
            (mouseleave)="cancelVoiceRecording()"
            (touchstart)="startVoiceRecording()"
            (touchend)="stopVoiceRecording()"
            class="whatsapp-voice-btn"
            [ngClass]="{ recording: isRecordingVoice }"
            title="Maintenir pour enregistrer"
          >
            <i class="fas fa-microphone"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Sélecteur d'émojis -->
    <div *ngIf="showEmojiPicker" class="whatsapp-emoji-picker">
      <div class="whatsapp-emoji-picker-header">
        <div class="whatsapp-emoji-categories">
          <button
            *ngFor="let category of emojiCategories"
            (click)="selectEmojiCategory(category.name)"
            class="whatsapp-emoji-category-btn"
            [ngClass]="{ active: selectedEmojiCategory === category.name }"
            [title]="category.label"
          >
            {{ category.icon }}
          </button>
        </div>
        <button (click)="toggleEmojiPicker()" class="whatsapp-emoji-close">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="whatsapp-emoji-search">
        <input
          type="text"
          [(ngModel)]="emojiSearchQuery"
          (input)="searchEmojis()"
          placeholder="Rechercher un emoji..."
          class="whatsapp-emoji-search-input"
        />
      </div>

      <div class="whatsapp-emoji-grid">
        <button
          *ngFor="let emoji of getFilteredEmojis()"
          (click)="insertEmoji(emoji.char)"
          class="whatsapp-emoji-btn"
          [title]="emoji.name"
        >
          {{ emoji.char }}
        </button>
      </div>

      <!-- Émojis récents -->
      <div *ngIf="recentEmojis.length > 0" class="whatsapp-recent-emojis">
        <div class="whatsapp-recent-emojis-header">Récemment utilisés</div>
        <div class="whatsapp-recent-emojis-grid">
          <button
            *ngFor="let emoji of recentEmojis"
            (click)="insertEmoji(emoji)"
            class="whatsapp-emoji-btn"
          >
            {{ emoji }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Inputs de fichier cachés -->
  <input
    #fileInputImage
    type="file"
    accept="image/*"
    (change)="onFileSelected($event, 'image')"
    style="display: none"
    multiple
  />
  <input
    #fileInputVideo
    type="file"
    accept="video/*"
    (change)="onFileSelected($event, 'video')"
    style="display: none"
  />
  <input
    #fileInputAudio
    type="file"
    accept="audio/*"
    (change)="onFileSelected($event, 'audio')"
    style="display: none"
  />
  <input
    #fileInputDocument
    type="file"
    accept=".pdf,.doc,.docx,.txt,.xlsx,.pptx"
    (change)="onFileSelected($event, 'document')"
    style="display: none"
  />

  <!-- Panneau de notifications -->
  <div class="side-panel" [ngClass]="{ open: showNotificationPanel }">
    <div class="panel-header">
      <h3>Notifications</h3>
      <button class="close-btn" (click)="toggleNotificationPanel()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div
      *ngIf="notifications.length === 0"
      style="padding: 1rem; text-align: center; color: #888"
    >
      <i
        class="fas fa-bell-slash"
        style="font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.5"
      ></i>
      <p>Aucune notification</p>
    </div>
    <div
      *ngFor="let notification of notifications"
      style="padding: 0.5rem; border-bottom: 1px solid rgba(0, 247, 255, 0.1)"
    >
      <p style="margin: 0; font-size: 0.9rem">{{ notification.content }}</p>
      <small style="opacity: 0.7">{{
        formatMessageTime(notification.timestamp)
      }}</small>
    </div>
  </div>

  <!-- Barre de recherche -->
  <div
    *ngIf="showSearchBar"
    class="search-bar bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] px-4 py-3 relative z-10"
  >
    <div class="flex items-center space-x-3">
      <!-- Icône de recherche -->
      <div class="text-[#6d6870] dark:text-[#a0a0a0]">
        <i class="fas fa-search text-sm"></i>
      </div>

      <!-- Champ de recherche -->
      <div class="flex-1 relative">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          (input)="onSearchInput($event)"
          (keydown)="onSearchKeyPress($event)"
          placeholder="Rechercher dans cette conversation..."
          class="w-full px-3 py-2 text-sm bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 border border-[#edf1f4] dark:border-[#3a3a3a] rounded-lg focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 transition-colors"
          autofocus
        />

        <!-- Indicateur de chargement -->
        <div
          *ngIf="isSearching"
          class="absolute right-3 top-1/2 transform -translate-y-1/2"
        >
          <div
            class="w-4 h-4 border-2 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin"
          ></div>
        </div>

        <!-- Bouton de suppression -->
        <button
          *ngIf="searchQuery && !isSearching"
          (click)="clearSearch()"
          class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
        >
          <i class="fas fa-times text-xs"></i>
        </button>
      </div>

      <!-- Bouton de fermeture -->
      <button
        (click)="toggleSearchBar()"
        class="text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Résultats de recherche -->
    <div
      *ngIf="searchMode && searchResults.length > 0"
      class="mt-3 max-h-40 overflow-y-auto border border-[#edf1f4]/50 dark:border-[#3a3a3a] rounded-lg bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30"
    >
      <div
        class="p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#3a3a3a]"
      >
        {{ searchResults.length }} résultat(s) trouvé(s)
      </div>
      <div class="max-h-32 overflow-y-auto">
        <button
          *ngFor="let result of searchResults"
          (click)="result.id && navigateToMessage(result.id)"
          class="w-full text-left px-3 py-2 hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0"
        >
          <div class="flex items-start space-x-2">
            <div
              class="w-6 h-6 rounded-full bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 flex items-center justify-center flex-shrink-0 mt-0.5"
            >
              <i
                class="fas fa-comment text-xs text-[#4f5fad] dark:text-[#6d78c9]"
              ></i>
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0] mb-1">
                {{ formatMessageTime(result.timestamp) }}
              </div>
              <div
                class="text-sm text-[#6d6870] dark:text-[#a0a0a0] truncate"
                [innerHTML]="
                  highlightSearchTerms(result.content || '', searchQuery)
                "
              ></div>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- Message aucun résultat -->
    <div
      *ngIf="
        searchMode &&
        searchResults.length === 0 &&
        !isSearching &&
        searchQuery.length >= 2
      "
      class="mt-3 p-3 text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30 rounded-lg"
    >
      <i
        class="fas fa-search text-lg mb-2 block text-[#6d6870]/50 dark:text-[#a0a0a0]/50"
      ></i>
      Aucun message trouvé pour "{{ searchQuery }}"
    </div>
  </div>

  <!-- Panneau des messages épinglés -->
  <div
    *ngIf="showPinnedMessages"
    class="pinned-messages-panel bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] relative z-10"
  >
    <!-- En-tête du panneau -->
    <div
      class="flex items-center justify-between p-4 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]"
    >
      <div class="flex items-center space-x-2">
        <i class="fas fa-thumbtack text-[#4f5fad] dark:text-[#6d78c9]"></i>
        <h3 class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]">
          Messages épinglés ({{ getPinnedMessagesCount() }})
        </h3>
      </div>
      <button
        (click)="togglePinnedMessages()"
        class="w-6 h-6 flex items-center justify-center text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-full transition-colors"
      >
        <i class="fas fa-times text-xs"></i>
      </button>
    </div>

    <!-- Liste des messages épinglés -->
    <div class="max-h-48 overflow-y-auto">
      <!-- État de chargement -->
      <div *ngIf="pinnedMessages.length === 0" class="p-4 text-center">
        <div class="text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
          <i class="fas fa-thumbtack text-2xl mb-2 block opacity-50"></i>
          <div class="text-sm">Aucun message épinglé</div>
        </div>
      </div>

      <!-- Messages épinglés -->
      <div
        *ngIf="pinnedMessages.length > 0"
        class="divide-y divide-[#edf1f4]/30 dark:divide-[#3a3a3a]/30"
      >
        <button
          *ngFor="let pinnedMessage of pinnedMessages"
          (click)="scrollToPinnedMessage(pinnedMessage.id!)"
          class="w-full text-left p-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors"
        >
          <div class="flex items-start space-x-3">
            <!-- Avatar de l'expéditeur -->
            <div class="flex-shrink-0">
              <img
                [src]="
                  pinnedMessage.sender?.image ||
                  'assets/images/default-avatar.png'
                "
                [alt]="pinnedMessage.sender?.username || 'User'"
                class="w-8 h-8 rounded-full object-cover"
                onerror="this.src='assets/images/default-avatar.png'"
              />
            </div>

            <!-- Contenu du message -->
            <div class="flex-1 min-w-0">
              <!-- En-tête avec nom et date -->
              <div class="flex items-center justify-between mb-1">
                <span
                  class="text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9]"
                >
                  {{ pinnedMessage.sender?.username || "Utilisateur inconnu" }}
                </span>
                <span class="text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
                  {{ formatMessageTime(pinnedMessage.timestamp) }}
                </span>
              </div>

              <!-- Contenu du message -->
              <div
                class="text-sm text-[#6d6870] dark:text-[#a0a0a0] line-clamp-2"
              >
                <span *ngIf="pinnedMessage.content">{{
                  pinnedMessage.content
                }}</span>
                <span
                  *ngIf="hasImage(pinnedMessage)"
                  class="italic flex items-center"
                >
                  <i class="fas fa-image mr-1"></i>
                  Image
                </span>
                <span
                  *ngIf="isVoiceMessage(pinnedMessage)"
                  class="italic flex items-center"
                >
                  <i class="fas fa-microphone mr-1"></i>
                  Message vocal
                </span>
              </div>

              <!-- Indicateur d'épinglage -->
              <div class="flex items-center mt-1">
                <i
                  class="fas fa-thumbtack text-xs text-[#4f5fad] dark:text-[#6d78c9] mr-1"
                ></i>
                <span class="text-xs text-[#4f5fad] dark:text-[#6d78c9]">
                  Épinglé
                  {{
                    pinnedMessage.pinnedAt
                      ? "le " + formatMessageDate(pinnedMessage.pinnedAt)
                      : ""
                  }}
                </span>
              </div>
            </div>

            <!-- Icône de navigation -->
            <div class="flex-shrink-0 text-[#6d6870]/50 dark:text-[#a0a0a0]/50">
              <i class="fas fa-chevron-right text-xs"></i>
            </div>
          </div>
        </button>
      </div>
    </div>
  </div>

  <!-- Modales et panneaux latéraux -->

  <!-- Modal d'appel entrant -->
  <div *ngIf="incomingCall" class="whatsapp-call-modal incoming-call">
    <div class="whatsapp-call-modal-overlay"></div>
    <div class="whatsapp-call-modal-content">
      <div class="whatsapp-call-info">
        <div class="whatsapp-call-avatar">
          <img
            [src]="
              incomingCall.caller?.image || 'assets/images/default-avatar.png'
            "
            [alt]="incomingCall.caller?.username"
          />
        </div>
        <div class="whatsapp-call-details">
          <h3>{{ incomingCall.caller?.username }}</h3>
          <p>
            {{
              incomingCall.type === "AUDIO"
                ? "Appel audio entrant"
                : "Appel vidéo entrant"
            }}
          </p>
        </div>
      </div>
      <div class="whatsapp-call-actions">
        <button (click)="declineCall()" class="whatsapp-call-decline">
          <i class="fas fa-phone-slash"></i>
        </button>
        <button (click)="acceptCall()" class="whatsapp-call-accept">
          <i class="fas fa-phone"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Modal d'appel actif -->
  <div
    *ngIf="activeCall"
    class="whatsapp-call-modal active-call"
    [ngClass]="{ minimized: isCallMinimized }"
  >
    <div class="whatsapp-call-modal-content">
      <div class="whatsapp-call-header">
        <div class="whatsapp-call-participant">
          <img
            [src]="
              activeCall.participant?.image ||
              'assets/images/default-avatar.png'
            "
            [alt]="activeCall.participant?.username"
          />
          <div class="whatsapp-call-participant-info">
            <h4>{{ activeCall.participant?.username }}</h4>
            <span class="whatsapp-call-duration">{{
              formatCallDuration(callDuration)
            }}</span>
            <span class="whatsapp-call-status">{{ getCallStatusText() }}</span>
          </div>
        </div>
        <button (click)="toggleCallMinimize()" class="whatsapp-call-minimize">
          <i class="fas fa-minus" *ngIf="!isCallMinimized"></i>
          <i class="fas fa-expand" *ngIf="isCallMinimized"></i>
        </button>
      </div>

      <div *ngIf="activeCall.type === 'VIDEO'" class="whatsapp-video-container">
        <video #remoteVideo class="whatsapp-remote-video" autoplay></video>
        <video #localVideo class="whatsapp-local-video" autoplay muted></video>
      </div>

      <div
        class="whatsapp-call-controls"
        [ngClass]="{ show: showCallControls || !isCallMinimized }"
      >
        <button
          (click)="toggleMute()"
          class="whatsapp-call-control"
          [ngClass]="{ active: isCallMuted }"
        >
          <i class="fas fa-microphone" *ngIf="!isCallMuted"></i>
          <i class="fas fa-microphone-slash" *ngIf="isCallMuted"></i>
        </button>
        <button
          *ngIf="activeCall.type === 'VIDEO'"
          (click)="toggleVideo()"
          class="whatsapp-call-control"
          [ngClass]="{ active: !isVideoEnabled }"
        >
          <i class="fas fa-video" *ngIf="isVideoEnabled"></i>
          <i class="fas fa-video-slash" *ngIf="!isVideoEnabled"></i>
        </button>
        <button (click)="endCall()" class="whatsapp-call-end">
          <i class="fas fa-phone-slash"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Modal de confirmation de suppression -->
  <div *ngIf="showDeleteConfirmModal" class="whatsapp-modal-overlay">
    <div class="whatsapp-modal-content">
      <div class="whatsapp-modal-header">
        <h3>Confirmer la suppression</h3>
      </div>
      <div class="whatsapp-modal-body">
        <p>
          Êtes-vous sûr de vouloir supprimer ce message ? Cette action est
          irréversible.
        </p>
      </div>
      <div class="whatsapp-modal-actions">
        <button (click)="cancelDelete()" class="whatsapp-modal-cancel">
          Annuler
        </button>
        <button (click)="confirmDelete()" class="whatsapp-modal-confirm">
          Supprimer
        </button>
      </div>
    </div>
  </div>

  <!-- Modal de transfert de message -->
  <div *ngIf="showForwardModal" class="whatsapp-modal-overlay">
    <div class="whatsapp-modal-content whatsapp-forward-modal">
      <div class="whatsapp-modal-header">
        <h3>Transférer le message</h3>
        <button (click)="closeForwardModal()" class="whatsapp-modal-close">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="whatsapp-modal-body">
        <div class="whatsapp-forward-search">
          <input
            type="text"
            [(ngModel)]="forwardSearchQuery"
            placeholder="Rechercher des contacts..."
            class="whatsapp-forward-search-input"
          />
        </div>
        <div class="whatsapp-forward-contacts">
          <div
            *ngFor="let contact of getFilteredContacts()"
            class="whatsapp-forward-contact"
            (click)="toggleContactSelection(contact.id)"
          >
            <input
              type="checkbox"
              [checked]="selectedContacts.includes(contact.id)"
            />
            <img
              [src]="contact.image || 'assets/images/default-avatar.png'"
              [alt]="contact.username"
            />
            <span>{{ contact.username }}</span>
          </div>
        </div>
      </div>
      <div class="whatsapp-modal-actions">
        <button (click)="closeForwardModal()" class="whatsapp-modal-cancel">
          Annuler
        </button>
        <button
          (click)="confirmForward()"
          class="whatsapp-modal-confirm"
          [disabled]="selectedContacts.length === 0"
        >
          Transférer ({{ selectedContacts.length }})
        </button>
      </div>
    </div>
  </div>

  <!-- Visualiseur d'images -->
  <div *ngIf="showImageViewer" class="whatsapp-image-viewer">
    <div
      class="whatsapp-image-viewer-overlay"
      (click)="closeImageViewer()"
    ></div>
    <div class="whatsapp-image-viewer-content">
      <div class="whatsapp-image-viewer-header">
        <div class="whatsapp-image-viewer-info">
          <span>{{ currentImageIndex + 1 }} / {{ imageGallery.length }}</span>
        </div>
        <button
          (click)="closeImageViewer()"
          class="whatsapp-image-viewer-close"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="whatsapp-image-viewer-body">
        <button
          *ngIf="imageGallery.length > 1"
          (click)="previousImage()"
          class="whatsapp-image-nav whatsapp-image-prev"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        <img
          [src]="imageGallery[currentImageIndex]?.url"
          [alt]="imageGallery[currentImageIndex]?.caption"
          class="whatsapp-image-viewer-img"
        />
        <button
          *ngIf="imageGallery.length > 1"
          (click)="nextImage()"
          class="whatsapp-image-nav whatsapp-image-next"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
      <div
        *ngIf="imageGallery[currentImageIndex]?.caption"
        class="whatsapp-image-viewer-caption"
      >
        {{ imageGallery[currentImageIndex]?.caption }}
      </div>
    </div>
  </div>

  <!-- Toast de notification -->
  <div *ngIf="showToast" class="whatsapp-toast" [ngClass]="toastType">
    <div class="whatsapp-toast-content">
      <i [class]="getToastIcon()"></i>
      <span>{{ toastMessage }}</span>
    </div>
  </div>

  <!-- Panneau d'informations de conversation -->
  <div
    *ngIf="showConversationInfo"
    class="whatsapp-side-panel whatsapp-conversation-info"
  >
    <div class="whatsapp-side-panel-header">
      <h3>Informations de conversation</h3>
      <button
        (click)="toggleConversationInfo()"
        class="whatsapp-side-panel-close"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="whatsapp-side-panel-body">
      <div class="whatsapp-conversation-participant">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
        />
        <div class="whatsapp-participant-details">
          <h4>{{ otherParticipant?.username }}</h4>
          <p>{{ otherParticipant?.email }}</p>
          <span class="whatsapp-participant-status">{{
            otherParticipant?.isOnline ? "En ligne" : "Hors ligne"
          }}</span>
        </div>
      </div>
      <div class="whatsapp-conversation-stats">
        <div class="whatsapp-stat">
          <span class="whatsapp-stat-label">Messages</span>
          <span class="whatsapp-stat-value">{{ getTotalMessagesCount() }}</span>
        </div>
        <div class="whatsapp-stat">
          <span class="whatsapp-stat-label">Photos</span>
          <span class="whatsapp-stat-value">{{ getPhotosCount() }}</span>
        </div>
        <div class="whatsapp-stat">
          <span class="whatsapp-stat-label">Fichiers</span>
          <span class="whatsapp-stat-value">{{ getFilesCount() }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau de paramètres de conversation -->
  <div
    *ngIf="showConversationSettings"
    class="whatsapp-side-panel whatsapp-conversation-settings"
  >
    <div class="whatsapp-side-panel-header">
      <h3>Paramètres de conversation</h3>
      <button
        (click)="toggleConversationSettings()"
        class="whatsapp-side-panel-close"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="whatsapp-side-panel-body">
      <div class="whatsapp-setting-group">
        <h4>Notifications</h4>
        <div class="whatsapp-setting-item">
          <label>
            <input
              type="checkbox"
              [(ngModel)]="conversationSettings.notifications"
              (change)="updateConversationSettings()"
            />
            <span>Recevoir des notifications</span>
          </label>
        </div>
        <div class="whatsapp-setting-item">
          <label>
            <input
              type="checkbox"
              [(ngModel)]="conversationSettings.soundNotifications"
              (change)="updateConversationSettings()"
            />
            <span>Sons de notification</span>
          </label>
        </div>
      </div>
      <div class="whatsapp-setting-group">
        <h4>Confidentialité</h4>
        <div class="whatsapp-setting-item">
          <label>
            <input
              type="checkbox"
              [(ngModel)]="conversationSettings.readReceipts"
              (change)="updateConversationSettings()"
            />
            <span>Accusés de lecture</span>
          </label>
        </div>
        <div class="whatsapp-setting-item">
          <label>
            <input
              type="checkbox"
              [(ngModel)]="conversationSettings.typingIndicators"
              (change)="updateConversationSettings()"
            />
            <span>Indicateurs de frappe</span>
          </label>
        </div>
      </div>
    </div>
  </div>
</div>
