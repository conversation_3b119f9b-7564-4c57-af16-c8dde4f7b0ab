<!-- ========== CONTENEUR PRINCIPAL ULTRA-AVANCÉ ========== -->
<div class="whatsapp-chat-container" [ngClass]="selectedTheme">
  <!-- ========== EN-TÊTE ULTRA-MODERNE ========== -->
  <div class="whatsapp-header">
    <!-- Bouton retour -->
    <button (click)="goBackToConversations()" class="whatsapp-header-action">
      <i class="fas fa-arrow-left"></i>
    </button>

    <!-- Informations utilisateur -->
    <div class="whatsapp-header-info">
      <div class="whatsapp-header-avatar">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          alt="Avatar utilisateur"
          class="whatsapp-avatar"
          (click)="openUserProfile(otherParticipant?.id!)"
        />
        <div
          *ngIf="otherParticipant?.isOnline"
          class="whatsapp-online-indicator"
        ></div>
      </div>

      <div class="whatsapp-header-details">
        <h3 class="whatsapp-header-name">
          {{ otherParticipant?.username || "Utilisateur" }}
        </h3>
        <div class="whatsapp-header-status">
          <!-- Indicateur de frappe -->
          <div *ngIf="isUserTyping" class="whatsapp-typing-indicator">
            <span>En train d'écrire</span>
            <div class="whatsapp-typing-dot"></div>
            <div class="whatsapp-typing-dot"></div>
            <div class="whatsapp-typing-dot"></div>
          </div>
          <!-- Statut normal -->
          <span *ngIf="!isUserTyping">
            {{
              otherParticipant?.isOnline
                ? "En ligne"
                : formatLastActive(otherParticipant?.lastActive)
            }}
          </span>
        </div>
      </div>
    </div>

    <div class="whatsapp-actions">
      <!-- Boutons d'action consolidés -->
      <ng-container *ngFor="let action of getHeaderActions()">
        <button
          [class]="'whatsapp-action-button ' + action.class"
          [ngClass]="
            action.activeClass && action.isActive ? action.activeClass : {}
          "
          [title]="action.title"
          (click)="action.onClick()"
        >
          <i [class]="action.icon"></i>
          <!-- Badge universel -->
          <span
            *ngIf="action.badge && action.badge.count > 0"
            [class]="
              'absolute -top-2 -right-2 min-w-[20px] h-5 px-1.5 text-white text-xs rounded-md flex items-center justify-center font-bold shadow-lg border border-white/20 ' +
              action.badge.class
            "
            [ngClass]="{ 'animate-pulse': action.badge.animate }"
          >
            {{ action.badge.count > 99 ? "99+" : action.badge.count }}
          </span>
        </button>
      </ng-container>

      <!-- Bouton du statut utilisateur -->
      <div class="relative">
        <button
          (click)="toggleStatusSelector()"
          class="whatsapp-action-button relative"
          [ngClass]="{
            'text-[#4f5fad] dark:text-[#6d78c9]': showStatusSelector
          }"
          title="Statut utilisateur"
        >
          <i
            [class]="getStatusIcon(currentUserStatus)"
            [ngClass]="getStatusColor(currentUserStatus)"
          ></i>
          <!-- Indicateur de mise à jour -->
          <span
            *ngIf="isUpdatingStatus"
            class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse"
          ></span>
        </button>

        <!-- Menu déroulant du statut -->
        <div
          *ngIf="showStatusSelector"
          class="absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden"
        >
          <div
            class="p-3 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] bg-gradient-to-r from-[#4f5fad]/10 to-[#6d78c9]/10"
          >
            <div class="flex items-center justify-between">
              <span>Statut actuel</span>
              <span
                class="font-medium"
                [ngClass]="getStatusColor(currentUserStatus)"
              >
                {{ getStatusText(currentUserStatus) }}
              </span>
            </div>
          </div>

          <div class="p-1">
            <!-- Boutons de statut consolidés avec *ngFor -->
            <button
              *ngFor="let status of getStatusOptions()"
              (click)="updateUserStatus(status.key); toggleStatusSelector()"
              [disabled]="isUpdatingStatus"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
              [ngClass]="{
                'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20':
                  currentUserStatus === status.key
              }"
            >
              <div class="flex items-center">
                <i
                  [class]="status.icon + ' ' + status.color + ' mr-3 text-xs'"
                ></i>
                <div>
                  <div class="font-medium">{{ status.label }}</div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    {{ status.description }}
                  </div>
                </div>
              </div>
            </button>
          </div>

          <div class="border-t border-[#edf1f4]/50 dark:border-[#2a2a2a] p-1">
            <button
              (click)="toggleUserStatusPanel(); toggleStatusSelector()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <i
                  class="fas fa-users text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xs"
                ></i>
                <div>
                  <div class="font-medium">Voir tous les utilisateurs</div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    {{ getOnlineUsersCount() }} en ligne
                  </div>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- Sélecteur de thème -->
      <div class="relative">
        <button
          (click)="toggleThemeSelector()"
          class="whatsapp-action-button btn-theme"
        >
          <i class="fas fa-palette"></i>
        </button>

        <!-- Menu déroulant des thèmes -->
        <div
          *ngIf="showThemeSelector"
          class="theme-selector-menu absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden"
        >
          <div
            class="p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]"
          >
            Choisir un thème
          </div>
          <div class="p-1">
            <!-- Boutons de thème consolidés avec *ngFor -->
            <a
              *ngFor="let theme of getThemeOptions()"
              href="javascript:void(0)"
              (click)="changeTheme(theme.key)"
              class="block w-full text-left px-3 py-2 text-sm rounded-md transition-colors"
              [ngClass]="
                'hover:bg-' +
                theme.hoverColor +
                '/10 dark:hover:bg-' +
                theme.hoverColor +
                '/10'
              "
            >
              <div class="flex items-center">
                <div
                  [ngClass]="'w-4 h-4 rounded-full bg-' + theme.color + ' mr-2'"
                ></div>
                <div>{{ theme.label }}</div>
              </div>
            </a>
          </div>
        </div>
      </div>

      <!-- Bouton menu principal -->
      <div class="relative">
        <button
          (click)="toggleMainMenu()"
          class="whatsapp-action-button btn-menu"
          title="Menu principal"
        >
          <i class="fas fa-ellipsis-v"></i>
        </button>

        <!-- Menu déroulant principal -->
        <div
          *ngIf="showMainMenu"
          class="absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden"
        >
          <div
            class="p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]"
          >
            Options de conversation
          </div>
          <div class="p-1">
            <button
              (click)="clearConversation()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-red-500/10 dark:hover:bg-red-500/10 transition-colors text-red-600 dark:text-red-400"
            >
              <div class="flex items-center">
                <i class="fas fa-trash mr-3 text-xs"></i>
                <div>Vider la conversation</div>
              </div>
            </button>
            <button
              (click)="exportConversation()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <i class="fas fa-download mr-3 text-xs"></i>
                <div>Exporter la conversation</div>
              </div>
            </button>
            <button
              (click)="toggleConversationInfo()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <i class="fas fa-info-circle mr-3 text-xs"></i>
                <div>Informations</div>
              </div>
            </button>
            <button
              (click)="toggleConversationSettings()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <i class="fas fa-cog mr-3 text-xs"></i>
                <div>Paramètres</div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Zone des messages avec fonctionnalités complètes -->
  <div
    class="whatsapp-messages-container flex-1 overflow-hidden relative"
    #messagesContainer
    (scroll)="onScroll($event)"
  >
    <!-- Indicateur de chargement initial -->
    <div *ngIf="loading" class="whatsapp-loading-state">
      <div class="whatsapp-loading-content">
        <div class="whatsapp-loading-spinner"></div>
        <span class="whatsapp-loading-text">Chargement des messages...</span>
      </div>
    </div>

    <!-- État vide avec suggestions -->
    <div *ngIf="!loading && messages.length === 0" class="whatsapp-empty-state">
      <div class="whatsapp-empty-content">
        <div class="whatsapp-empty-icon">
          <i class="fas fa-comments"></i>
        </div>
        <h3 class="whatsapp-empty-title">Aucun message</h3>
        <p class="whatsapp-empty-description">
          Commencez votre conversation avec {{ otherParticipant?.username }}
        </p>
        <div class="whatsapp-empty-suggestions">
          <button
            (click)="sendQuickMessage('Salut ! 👋')"
            class="whatsapp-quick-message-btn"
          >
            <i class="fas fa-hand-wave"></i>
            Dire bonjour
          </button>
          <button
            (click)="sendQuickMessage('Comment ça va ?')"
            class="whatsapp-quick-message-btn"
          >
            <i class="fas fa-smile"></i>
            Prendre des nouvelles
          </button>
          <button
            (click)="sendQuickMessage('Quoi de neuf ?')"
            class="whatsapp-quick-message-btn"
          >
            <i class="fas fa-question"></i>
            Demander des nouvelles
          </button>
        </div>
      </div>
    </div>

    <!-- Bouton charger plus de messages -->
    <div
      *ngIf="hasMoreMessages && !loading"
      class="whatsapp-load-more-container"
    >
      <button
        (click)="loadMoreMessages()"
        [disabled]="isLoadingMore"
        class="whatsapp-load-more-btn"
      >
        <i class="fas fa-chevron-up" *ngIf="!isLoadingMore"></i>
        <i class="fas fa-spinner fa-spin" *ngIf="isLoadingMore"></i>
        <span>{{ isLoadingMore ? "Chargement..." : "Charger plus" }}</span>
      </button>
    </div>

    <!-- Messages avec toutes les fonctionnalités -->
    <div
      class="whatsapp-messages-list"
      [ngClass]="{ 'search-mode': searchMode, 'selection-mode': selectionMode }"
    >
      <ng-container
        *ngFor="
          let message of messages;
          let i = index;
          trackBy: trackByMessageId
        "
      >
        <!-- Séparateur de date -->
        <div *ngIf="shouldShowDateSeparator(i)" class="whatsapp-date-separator">
          <div class="whatsapp-date-separator-content">
            <span>{{ formatDateSeparator(message.timestamp) }}</span>
          </div>
        </div>

        <!-- Notification système -->
        <div *ngIf="message.type === 'system'" class="whatsapp-system-message">
          <div class="whatsapp-system-content">
            <i [class]="getSystemMessageIcon(message)"></i>
            <span>{{ message.content }}</span>
          </div>
        </div>

        <!-- Message normal -->
        <div
          *ngIf="message.type !== 'system'"
          class="whatsapp-message-wrapper"
          [ngClass]="{
            'own-message': message.sender?.id === currentUserId,
            'other-message': message.sender?.id !== currentUserId,
            highlighted: highlightedMessageId === message.id,
            editing: editingMessageId === message.id,
            selected: selectedMessages.has(message.id!),
            'search-result': searchResultIds.includes(message.id!),
            pinned: message.pinned,
            forwarded: message.forwarded,
            reply: message.replyTo
          }"
          [id]="'message-' + message.id"
          (click)="onMessageClick(message, $event)"
          (contextmenu)="onMessageContextMenu(message, $event)"
          (mouseenter)="onMessageHover(message.id!, true)"
          (mouseleave)="onMessageHover(message.id!, false)"
        >
          <!-- Checkbox de sélection (mode sélection multiple) -->
          <div *ngIf="selectionMode" class="whatsapp-message-checkbox">
            <input
              type="checkbox"
              [checked]="selectedMessages.has(message.id!)"
              (change)="toggleMessageSelection(message.id!)"
              class="whatsapp-checkbox"
            />
          </div>

          <!-- Avatar pour les messages des autres -->
          <div
            *ngIf="message.sender?.id !== currentUserId && shouldShowAvatar(i)"
            class="whatsapp-message-avatar"
          >
            <img
              [src]="
                message.sender?.image || 'assets/images/default-avatar.png'
              "
              [alt]="message.sender?.username"
              class="whatsapp-avatar-img"
              (error)="onAvatarError($event)"
              (click)="openUserProfile(message.sender?.id!)"
            />
            <div
              *ngIf="message.sender?.isOnline"
              class="whatsapp-online-indicator"
            ></div>
          </div>

          <!-- Contenu du message -->
          <div class="whatsapp-message-content">
            <!-- Nom de l'expéditeur (groupes) -->
            <div
              *ngIf="
                isGroupConversation() &&
                message.sender?.id !== currentUserId &&
                shouldShowSenderName(i)
              "
              class="whatsapp-sender-name"
              [style.color]="getUserColor(message.sender?.id!)"
            >
              {{ message.sender?.username }}
            </div>

            <!-- Indicateur de transfert -->
            <div *ngIf="message.forwarded" class="whatsapp-forwarded-indicator">
              <i class="fas fa-share"></i>
              <span>Transféré</span>
            </div>

            <!-- Message de réponse -->
            <div
              *ngIf="message.replyTo"
              class="whatsapp-reply-message"
              (click)="scrollToMessage(message.replyTo.id!)"
            >
              <div
                class="whatsapp-reply-border"
                [style.border-color]="getUserColor(message.replyTo.sender?.id!)"
              ></div>
              <div class="whatsapp-reply-content">
                <div
                  class="whatsapp-reply-sender"
                  [style.color]="getUserColor(message.replyTo.sender?.id!)"
                >
                  {{ message.replyTo.sender?.username }}
                </div>
                <div class="whatsapp-reply-text">
                  <span *ngIf="message.replyTo.content">{{
                    truncateText(message.replyTo.content, 50)
                  }}</span>
                  <span
                    *ngIf="hasImage(message.replyTo)"
                    class="whatsapp-reply-media"
                  >
                    <i class="fas fa-image"></i> Photo
                  </span>
                  <span
                    *ngIf="isVoiceMessage(message.replyTo)"
                    class="whatsapp-reply-media"
                  >
                    <i class="fas fa-microphone"></i> Message vocal
                  </span>
                  <span
                    *ngIf="hasFile(message.replyTo)"
                    class="whatsapp-reply-media"
                  >
                    <i class="fas fa-file"></i>
                    {{ getFileName(message.replyTo) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Bulle de message -->
            <div
              class="whatsapp-message-bubble"
              [ngClass]="getMessageBubbleClass(message)"
            >
              <!-- Mode édition -->
              <div
                *ngIf="editingMessageId === message.id"
                class="whatsapp-edit-mode"
              >
                <div class="whatsapp-edit-input-container">
                  <textarea
                    [(ngModel)]="editingContent"
                    class="whatsapp-edit-input"
                    (keydown)="onEditKeyDown($event, message.id!)"
                    #editTextarea
                    rows="1"
                    placeholder="Modifier le message..."
                    maxlength="4096"
                  ></textarea>
                  <div class="whatsapp-edit-char-count">
                    {{ editingContent.length || 0 }}/4096
                  </div>
                </div>
                <div class="whatsapp-edit-actions">
                  <button
                    (click)="cancelEdit()"
                    class="whatsapp-edit-cancel"
                    title="Annuler"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                  <button
                    (click)="saveEdit(message.id!)"
                    class="whatsapp-edit-save"
                    title="Sauvegarder"
                    [disabled]="!editingContent.trim()"
                  >
                    <i class="fas fa-check"></i>
                  </button>
                </div>
              </div>

              <!-- Contenu normal du message -->
              <div
                *ngIf="editingMessageId !== message.id"
                class="whatsapp-message-body"
              >
                <!-- Message texte avec formatage -->
                <div
                  *ngIf="getMessageType(message) === 'text'"
                  class="whatsapp-text-content"
                >
                  <div
                    class="whatsapp-text-body"
                    [innerHTML]="formatMessageContent(message.content)"
                  ></div>
                </div>

                <!-- Message image avec prévisualisation -->
                <div *ngIf="hasImage(message)" class="whatsapp-image-content">
                  <div
                    class="whatsapp-image-container"
                    (click)="openImageViewer(message)"
                  >
                    <img
                      [src]="getImageUrl(message)"
                      [alt]="message.content || 'Image'"
                      class="whatsapp-message-image"
                      (load)="onImageLoad($event)"
                      (error)="onImageError($event)"
                      [style.filter]="
                        isImageLoading(message) ? 'blur(5px)' : 'none'
                      "
                    />
                    <div
                      *ngIf="isImageLoading(message)"
                      class="whatsapp-image-loading"
                    >
                      <div class="whatsapp-image-spinner"></div>
                      <span>{{ getImageLoadingProgress(message) }}%</span>
                    </div>
                    <div class="whatsapp-image-overlay">
                      <i class="fas fa-expand"></i>
                    </div>
                    <div
                      *ngIf="getImageDimensions(message)"
                      class="whatsapp-image-info"
                    >
                      {{ getImageDimensions(message) }}
                    </div>
                  </div>
                  <div *ngIf="message.content" class="whatsapp-image-caption">
                    <div
                      [innerHTML]="formatMessageContent(message.content)"
                    ></div>
                  </div>
                </div>

                <!-- Message vocal avec contrôles avancés -->
                <div
                  *ngIf="isVoiceMessage(message)"
                  class="whatsapp-voice-content"
                >
                  <div class="whatsapp-voice-player">
                    <button
                      (click)="toggleVoicePlayback(message.id!)"
                      class="whatsapp-voice-play-btn"
                      [ngClass]="{
                        playing: playingVoiceId === message.id,
                        loading: isVoiceLoading(message.id!)
                      }"
                    >
                      <i
                        class="fas fa-play"
                        *ngIf="
                          playingVoiceId !== message.id &&
                          !isVoiceLoading(message.id!)
                        "
                      ></i>
                      <i
                        class="fas fa-pause"
                        *ngIf="
                          playingVoiceId === message.id &&
                          !isVoiceLoading(message.id!)
                        "
                      ></i>
                      <i
                        class="fas fa-spinner fa-spin"
                        *ngIf="isVoiceLoading(message.id!)"
                      ></i>
                    </button>

                    <div
                      class="whatsapp-voice-waveform"
                      (click)="seekVoiceMessage(message.id!, $event)"
                    >
                      <div
                        *ngFor="
                          let bar of getVoiceWaveform(message);
                          let j = index
                        "
                        class="whatsapp-voice-bar"
                        [style.height.px]="bar.height"
                        [ngClass]="{
                          active: j <= getVoiceProgress(message.id!),
                          played: j < getVoiceProgress(message.id!)
                        }"
                      ></div>
                    </div>

                    <div class="whatsapp-voice-info">
                      <div class="whatsapp-voice-duration">
                        {{
                          formatVoiceDuration(
                            getVoiceCurrentTime(message.id!),
                            getVoiceTotalDuration(message.id!)
                          )
                        }}
                      </div>
                      <div
                        class="whatsapp-voice-speed"
                        *ngIf="playingVoiceId === message.id"
                      >
                        <button
                          (click)="changeVoiceSpeed(message.id!)"
                          class="whatsapp-voice-speed-btn"
                        >
                          {{ getVoiceSpeed(message.id!) }}x
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Message fichier avec prévisualisation -->
                <div *ngIf="hasFile(message)" class="whatsapp-file-content">
                  <div
                    class="whatsapp-file-container"
                    (click)="downloadFile(message)"
                  >
                    <div class="whatsapp-file-icon">
                      <i [class]="getFileIcon(message)"></i>
                    </div>
                    <div class="whatsapp-file-info">
                      <div class="whatsapp-file-name">
                        {{ getFileName(message) }}
                      </div>
                      <div class="whatsapp-file-details">
                        <span class="whatsapp-file-size">{{
                          getFileSize(message)
                        }}</span>
                        <span class="whatsapp-file-type">{{
                          getFileType(message)
                        }}</span>
                      </div>
                    </div>
                    <div class="whatsapp-file-actions">
                      <button
                        (click)="
                          downloadFile(message); $event.stopPropagation()
                        "
                        class="whatsapp-file-download"
                        title="Télécharger"
                      >
                        <i class="fas fa-download"></i>
                      </button>
                      <button
                        *ngIf="canPreviewFile(message)"
                        (click)="previewFile(message); $event.stopPropagation()"
                        class="whatsapp-file-preview"
                        title="Aperçu"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                    </div>
                  </div>
                  <div
                    *ngIf="isFileDownloading(message)"
                    class="whatsapp-file-progress"
                  >
                    <div class="whatsapp-progress-bar">
                      <div
                        class="whatsapp-progress-fill"
                        [style.width.%]="getFileDownloadProgress(message)"
                      ></div>
                    </div>
                    <span class="whatsapp-progress-text"
                      >{{ getFileDownloadProgress(message) }}%</span
                    >
                  </div>
                </div>

                <!-- Message vidéo -->
                <div *ngIf="hasVideo(message)" class="whatsapp-video-content">
                  <div
                    class="whatsapp-video-container"
                    (click)="openVideoPlayer(message)"
                  >
                    <video
                      [src]="getVideoUrl(message)"
                      class="whatsapp-message-video"
                      [poster]="getVideoThumbnail(message)"
                      preload="metadata"
                      (loadedmetadata)="onVideoLoaded($event, message)"
                    ></video>
                    <div class="whatsapp-video-overlay">
                      <button class="whatsapp-video-play-btn">
                        <i class="fas fa-play"></i>
                      </button>
                    </div>
                    <div class="whatsapp-video-info">
                      <span class="whatsapp-video-duration">{{
                        getVideoDuration(message)
                      }}</span>
                    </div>
                  </div>
                  <div *ngIf="message.content" class="whatsapp-video-caption">
                    <div
                      [innerHTML]="formatMessageContent(message.content)"
                    ></div>
                  </div>
                </div>

                <!-- Message de localisation -->
                <div
                  *ngIf="isLocationMessage(message)"
                  class="whatsapp-location-content"
                >
                  <div
                    class="whatsapp-location-container"
                    (click)="openLocationViewer(message)"
                  >
                    <div class="whatsapp-location-map">
                      <img
                        [src]="getLocationMapUrl(message)"
                        alt="Carte"
                        class="whatsapp-location-image"
                      />
                      <div class="whatsapp-location-overlay">
                        <i class="fas fa-map-marker-alt"></i>
                      </div>
                    </div>
                    <div class="whatsapp-location-info">
                      <div class="whatsapp-location-name">
                        {{ getLocationName(message) }}
                      </div>
                      <div class="whatsapp-location-address">
                        {{ getLocationAddress(message) }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Message de contact -->
                <div
                  *ngIf="isContactMessage(message)"
                  class="whatsapp-contact-content"
                >
                  <div
                    class="whatsapp-contact-container"
                    (click)="openContactViewer(message)"
                  >
                    <div class="whatsapp-contact-avatar">
                      <img
                        [src]="getContactAvatar(message)"
                        [alt]="getContactName(message)"
                      />
                    </div>
                    <div class="whatsapp-contact-info">
                      <div class="whatsapp-contact-name">
                        {{ getContactName(message) }}
                      </div>
                      <div class="whatsapp-contact-phone">
                        {{ getContactPhone(message) }}
                      </div>
                    </div>
                    <div class="whatsapp-contact-actions">
                      <button
                        class="whatsapp-contact-add"
                        title="Ajouter aux contacts"
                      >
                        <i class="fas fa-user-plus"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Métadonnées du message -->
                <div class="whatsapp-message-meta">
                  <span class="whatsapp-message-time">{{
                    formatMessageTime(message.timestamp)
                  }}</span>

                  <!-- Statut de livraison (messages envoyés) -->
                  <div
                    *ngIf="message.sender?.id === currentUserId"
                    class="whatsapp-message-status"
                  >
                    <i
                      class="fas fa-clock whatsapp-status-sending"
                      *ngIf="message.status === 'SENDING'"
                      title="Envoi en cours"
                    ></i>
                    <i
                      class="fas fa-check whatsapp-status-sent"
                      *ngIf="message.status === 'SENT'"
                      title="Envoyé"
                    ></i>
                    <i
                      class="fas fa-check-double whatsapp-status-delivered"
                      *ngIf="message.status === 'DELIVERED'"
                      title="Livré"
                    ></i>
                    <i
                      class="fas fa-check-double whatsapp-status-read"
                      *ngIf="message.status === 'READ'"
                      title="Lu"
                    ></i>
                    <i
                      class="fas fa-exclamation-triangle whatsapp-status-failed"
                      *ngIf="message.status === 'FAILED'"
                      title="Échec d'envoi"
                    ></i>
                  </div>

                  <!-- Indicateur de modification -->
                  <span
                    *ngIf="message.isEdited"
                    class="whatsapp-edited-indicator"
                    [title]="
                      'Modifié le ' + formatMessageTime(message.timestamp!)
                    "
                  >
                    modifié
                  </span>

                  <!-- Indicateur d'épinglage -->
                  <i
                    *ngIf="message.pinned"
                    class="fas fa-thumbtack whatsapp-pinned-indicator"
                    title="Message épinglé"
                  ></i>
                </div>
              </div>

              <!-- Réactions -->
              <div
                *ngIf="message.reactions && message.reactions.length > 0"
                class="whatsapp-message-reactions"
              >
                <div class="whatsapp-reactions-container">
                  <button
                    *ngFor="let reaction of message.reactions"
                    (click)="toggleReaction(message.id!, reaction.emoji)"
                    class="whatsapp-reaction-item"
                    [ngClass]="{
                      'own-reaction': hasUserReacted(
                        reaction,
                        currentUserId || ''
                      )
                    }"
                    [title]="getReactionTooltip(reaction)"
                  >
                    <span class="whatsapp-reaction-emoji">{{
                      reaction.emoji
                    }}</span>
                    <span class="whatsapp-reaction-count">1</span>
                  </button>
                  <button
                    (click)="toggleReactionPicker(message.id!)"
                    class="whatsapp-reaction-add"
                    title="Ajouter une réaction"
                  >
                    <i class="fas fa-plus"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Menu d'options du message -->
            <div
              *ngIf="showMessageOptions === message.id"
              class="whatsapp-message-options"
              [ngClass]="{
                'own-message': message.sender?.id === currentUserId
              }"
            >
              <button
                (click)="replyToMessage(message)"
                class="whatsapp-option-btn"
                title="Répondre"
              >
                <i class="fas fa-reply"></i>
                <span>Répondre</span>
              </button>
              <button
                (click)="toggleReactionPicker(message.id!)"
                class="whatsapp-option-btn"
                title="Réagir"
              >
                <i class="fas fa-smile"></i>
                <span>Réagir</span>
              </button>
              <button
                (click)="forwardMessage(message)"
                class="whatsapp-option-btn"
                title="Transférer"
              >
                <i class="fas fa-share"></i>
                <span>Transférer</span>
              </button>
              <button
                (click)="copyMessage(message.content!)"
                class="whatsapp-option-btn"
                title="Copier"
              >
                <i class="fas fa-copy"></i>
                <span>Copier</span>
              </button>
              <button
                (click)="togglePinMessage(message.id!)"
                class="whatsapp-option-btn"
                [title]="message.pinned ? 'Désépingler' : 'Épingler'"
              >
                <i
                  class="fas fa-thumbtack"
                  [ngClass]="{ pinned: message.pinned }"
                ></i>
                <span>{{ message.pinned ? "Désépingler" : "Épingler" }}</span>
              </button>
              <button
                (click)="selectMessage(message.id!)"
                class="whatsapp-option-btn"
                title="Sélectionner"
              >
                <i class="fas fa-check-square"></i>
                <span>Sélectionner</span>
              </button>
              <button
                *ngIf="message.sender?.id === currentUserId"
                (click)="startEditMessage(message)"
                class="whatsapp-option-btn"
                title="Modifier"
              >
                <i class="fas fa-edit"></i>
                <span>Modifier</span>
              </button>
              <button
                *ngIf="canDeleteMessage(message)"
                (click)="deleteMessage(message.id!)"
                class="whatsapp-option-btn whatsapp-delete-btn"
                title="Supprimer"
              >
                <i class="fas fa-trash"></i>
                <span>Supprimer</span>
              </button>
            </div>

            <!-- Sélecteur de réactions -->
            <div
              *ngIf="showReactionPicker === message.id"
              class="whatsapp-reaction-picker"
            >
              <div class="whatsapp-reaction-picker-content">
                <button
                  *ngFor="let emoji of commonEmojis"
                  (click)="addReaction(message.id!, emoji)"
                  class="whatsapp-reaction-picker-btn"
                  [title]="getEmojiName(emoji)"
                >
                  {{ emoji }}
                </button>
                <button
                  (click)="openEmojiPicker(message.id!)"
                  class="whatsapp-reaction-picker-more"
                  title="Plus d'émojis"
                >
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Bouton d'options (trois points) -->
          <button
            *ngIf="!selectionMode"
            (click)="toggleMessageOptions(message.id!)"
            class="whatsapp-message-menu-btn"
            [ngClass]="{
              visible:
                showMessageOptions === message.id ||
                hoveredMessageId === message.id
            }"
          >
            <i class="fas fa-ellipsis-v"></i>
          </button>
        </div>
      </ng-container>

      <!-- Indicateur de frappe -->
      <div *ngIf="isTyping" class="whatsapp-typing-indicator">
        <div class="whatsapp-typing-avatar">
          <img
            [src]="
              otherParticipant?.image || 'assets/images/default-avatar.png'
            "
            [alt]="otherParticipant?.username"
          />
        </div>
        <div class="whatsapp-typing-bubble">
          <div class="whatsapp-typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <div class="whatsapp-typing-text">
          {{ otherParticipant?.username }} est en train d'écrire...
        </div>
      </div>
    </div>

    <!-- Bouton de retour en bas -->
    <div *ngIf="showScrollToBottom" class="whatsapp-scroll-to-bottom">
      <button (click)="scrollToBottom()" class="whatsapp-scroll-btn">
        <i class="fas fa-chevron-down"></i>
        <span *ngIf="unreadMessagesCount > 0" class="whatsapp-unread-count">
          {{ unreadMessagesCount > 99 ? "99+" : unreadMessagesCount }}
        </span>
      </button>
    </div>

    <!-- Overlay de sélection multiple -->
    <div *ngIf="selectionMode" class="whatsapp-selection-overlay">
      <div class="whatsapp-selection-header">
        <button (click)="exitSelectionMode()" class="whatsapp-selection-close">
          <i class="fas fa-times"></i>
        </button>
        <span class="whatsapp-selection-count"
          >{{ selectedMessages.size }} sélectionné(s)</span
        >
        <div class="whatsapp-selection-actions">
          <button
            (click)="deleteSelectedMessages()"
            class="whatsapp-selection-action"
            title="Supprimer"
          >
            <i class="fas fa-trash"></i>
          </button>
          <button
            (click)="forwardSelectedMessages()"
            class="whatsapp-selection-action"
            title="Transférer"
          >
            <i class="fas fa-share"></i>
          </button>
          <button
            (click)="copySelectedMessages()"
            class="whatsapp-selection-action"
            title="Copier"
          >
            <i class="fas fa-copy"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Message de réponse en cours -->
  <div *ngIf="replyingToMessage" class="whatsapp-reply-preview">
    <div class="whatsapp-reply-preview-content">
      <div class="whatsapp-reply-preview-header">
        <div class="whatsapp-reply-preview-info">
          <i class="fas fa-reply"></i>
          <span>Répondre à {{ replyingToMessage.sender?.username }}</span>
        </div>
        <button (click)="cancelReply()" class="whatsapp-reply-cancel">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="whatsapp-reply-preview-message">
        <span *ngIf="replyingToMessage.content">{{
          replyingToMessage.content
        }}</span>
        <span *ngIf="hasImage(replyingToMessage)" class="whatsapp-reply-media">
          <i class="fas fa-image"></i> Photo
        </span>
        <span
          *ngIf="isVoiceMessage(replyingToMessage)"
          class="whatsapp-reply-media"
        >
          <i class="fas fa-microphone"></i> Message vocal
        </span>
      </div>
    </div>
  </div>

  <!-- Prévisualisation de fichier -->
  <div *ngIf="selectedFile" class="whatsapp-file-preview">
    <div class="whatsapp-file-preview-content">
      <div class="whatsapp-file-preview-header">
        <span>Fichier sélectionné</span>
        <button (click)="removeSelectedFile()" class="whatsapp-file-remove">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="whatsapp-file-preview-body">
        <div *ngIf="previewUrl" class="whatsapp-file-preview-image">
          <img [src]="previewUrl" [alt]="selectedFile.name" />
        </div>
        <div class="whatsapp-file-preview-info">
          <div class="whatsapp-file-preview-name">{{ selectedFile.name }}</div>
          <div class="whatsapp-file-preview-size">
            {{ formatFileSize(selectedFile.size) }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enregistrement vocal -->
  <div *ngIf="isRecordingVoice" class="whatsapp-voice-recording">
    <div class="whatsapp-voice-recording-content">
      <div class="whatsapp-voice-recording-info">
        <div class="whatsapp-voice-recording-icon">
          <i class="fas fa-microphone"></i>
        </div>
        <div class="whatsapp-voice-recording-details">
          <span class="whatsapp-voice-recording-text"
            >Enregistrement en cours...</span
          >
          <span class="whatsapp-voice-recording-duration">{{
            formatRecordingDuration(voiceRecordingDuration)
          }}</span>
        </div>
      </div>
      <div class="whatsapp-voice-recording-actions">
        <button (click)="cancelVoiceRecording()" class="whatsapp-voice-cancel">
          <i class="fas fa-trash"></i>
        </button>
        <button (click)="stopVoiceRecording()" class="whatsapp-voice-send">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>
    <div class="whatsapp-voice-recording-waveform">
      <div
        *ngFor="let bar of recordingWaveform"
        class="whatsapp-voice-recording-bar"
        [style.height.px]="bar"
      ></div>
    </div>
  </div>

  <!-- Zone de saisie principale -->
  <div class="whatsapp-input-container">
    <div class="whatsapp-input-wrapper">
      <!-- Boutons d'action gauche -->
      <div class="whatsapp-input-actions-left">
        <button
          type="button"
          (click)="toggleEmojiPicker()"
          class="whatsapp-input-action-btn"
          [ngClass]="{ active: showEmojiPicker }"
          title="Émojis"
        >
          <i class="fas fa-smile"></i>
        </button>

        <div class="whatsapp-attachment-menu">
          <button
            type="button"
            (click)="toggleAttachmentMenu()"
            class="whatsapp-input-action-btn"
            [ngClass]="{ active: showAttachmentMenu }"
            title="Joindre un fichier"
          >
            <i class="fas fa-paperclip"></i>
          </button>

          <!-- Menu des pièces jointes avancé -->
          <div *ngIf="showAttachmentMenu" class="whatsapp-attachment-dropdown">
            <div class="whatsapp-attachment-grid">
              <button
                (click)="triggerFileInput('image')"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-image"></i>
                </div>
                <span>Photos</span>
                <small>JPG, PNG, GIF</small>
              </button>
              <button
                (click)="triggerFileInput('video')"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-video"></i>
                </div>
                <span>Vidéos</span>
                <small>MP4, AVI, MOV</small>
              </button>
              <button
                (click)="triggerFileInput('audio')"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-music"></i>
                </div>
                <span>Audio</span>
                <small>MP3, WAV, OGG</small>
              </button>
              <button
                (click)="triggerFileInput('document')"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-file"></i>
                </div>
                <span>Documents</span>
                <small>PDF, DOC, TXT</small>
              </button>
              <button (click)="openCamera()" class="whatsapp-attachment-option">
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-camera"></i>
                </div>
                <span>Caméra</span>
                <small>Photo/Vidéo</small>
              </button>
              <button
                (click)="openLocationPicker()"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <span>Position</span>
                <small>Partager lieu</small>
              </button>
              <button
                (click)="openContactPicker()"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-user"></i>
                </div>
                <span>Contact</span>
                <small>Partager contact</small>
              </button>
              <button
                (click)="openPollCreator()"
                class="whatsapp-attachment-option"
              >
                <div class="whatsapp-attachment-icon">
                  <i class="fas fa-poll"></i>
                </div>
                <span>Sondage</span>
                <small>Créer sondage</small>
              </button>
            </div>

            <!-- Options rapides -->
            <div class="whatsapp-attachment-quick">
              <button (click)="openGifPicker()" class="whatsapp-quick-option">
                <i class="fas fa-gif"></i>
                <span>GIF</span>
              </button>
              <button
                (click)="openStickerPicker()"
                class="whatsapp-quick-option"
              >
                <i class="fas fa-sticky-note"></i>
                <span>Autocollants</span>
              </button>
              <button (click)="openDrawingTool()" class="whatsapp-quick-option">
                <i class="fas fa-paint-brush"></i>
                <span>Dessiner</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Zone de saisie de texte avancée -->
      <div class="whatsapp-input-text-container">
        <form
          [formGroup]="messageForm"
          (ngSubmit)="sendMessage()"
          class="whatsapp-input-form"
        >
          <div class="whatsapp-input-field-wrapper">
            <!-- Suggestions de réponse rapide -->
            <div
              *ngIf="showQuickReplies && quickReplies.length > 0"
              class="whatsapp-quick-replies"
            >
              <div class="whatsapp-quick-replies-header">
                <span>Réponses rapides</span>
                <button
                  (click)="hideQuickReplies()"
                  class="whatsapp-quick-replies-close"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="whatsapp-quick-replies-list">
                <button
                  *ngFor="let reply of quickReplies"
                  (click)="insertQuickReply(reply)"
                  class="whatsapp-quick-reply-btn"
                >
                  {{ reply }}
                </button>
              </div>
            </div>

            <!-- Champ de saisie principal avec overlay -->
            <div class="whatsapp-input-field-container">
              <textarea
                formControlName="content"
                #messageTextarea
                placeholder="{{ getInputPlaceholder() }}"
                class="whatsapp-input-field"
                [disabled]="
                  !otherParticipant || isRecordingVoice || isSendingMessage
                "
                (input)="onInputChange($event)"
                (keydown)="onInputKeyDown($event)"
                (keyup)="onInputKeyUp($event)"
                (focus)="onInputFocus()"
                (blur)="onInputBlur()"
                (paste)="onInputPaste($event)"
                (scroll)="onInputScroll($event)"
                rows="1"
                maxlength="4096"
                [style.height]="inputHeight + 'px'"
                [style.resize]="'none'"
                autocomplete="off"
                spellcheck="true"
              ></textarea>

              <!-- Overlay pour mentions et hashtags -->
              <div
                class="whatsapp-input-overlay"
                [style.height]="inputHeight + 'px'"
              >
                <!-- Mentions surlignées -->
                <div
                  *ngFor="let mention of activeMentions"
                  class="whatsapp-mention-highlight"
                  [style.left.px]="mention.position.left"
                  [style.top.px]="mention.position.top"
                  [style.width.px]="mention.position.width"
                  [style.height.px]="mention.position.height"
                >
                  @{{ mention.username }}
                </div>

                <!-- Hashtags surlignés -->
                <div
                  *ngFor="let hashtag of activeHashtags"
                  class="whatsapp-hashtag-highlight"
                  [style.left.px]="hashtag.position.left"
                  [style.top.px]="hashtag.position.top"
                  [style.width.px]="hashtag.position.width"
                  [style.height.px]="hashtag.position.height"
                >
                  #{{ hashtag.tag }}
                </div>

                <!-- Liens surlignés -->
                <div
                  *ngFor="let link of activeLinks"
                  class="whatsapp-link-highlight"
                  [style.left.px]="link.position.left"
                  [style.top.px]="link.position.top"
                  [style.width.px]="link.position.width"
                  [style.height.px]="link.position.height"
                >
                  {{ link.url }}
                </div>
              </div>

              <!-- Suggestions de mentions -->
              <div
                *ngIf="showMentionSuggestions && mentionSuggestions.length > 0"
                class="whatsapp-mention-suggestions"
              >
                <div class="whatsapp-mention-suggestions-header">
                  <i class="fas fa-at"></i>
                  <span>Mentionner quelqu'un</span>
                </div>
                <div class="whatsapp-mention-suggestions-list">
                  <button
                    *ngFor="let user of mentionSuggestions; let i = index"
                    (click)="insertMention(user)"
                    (mouseenter)="setSelectedMentionIndex(i)"
                    class="whatsapp-mention-suggestion"
                    [ngClass]="{ selected: selectedMentionIndex === i }"
                  >
                    <img
                      [src]="user.image || 'assets/images/default-avatar.png'"
                      [alt]="user.username"
                    />
                    <div class="whatsapp-mention-user-info">
                      <span class="whatsapp-mention-username">{{
                        user.username
                      }}</span>
                      <span class="whatsapp-mention-status">
                        <i
                          class="fas fa-circle"
                          [ngClass]="{
                            online: user.isOnline,
                            offline: !user.isOnline
                          }"
                        ></i>
                        {{ user.isOnline ? "En ligne" : "Hors ligne" }}
                      </span>
                    </div>
                    <div class="whatsapp-mention-role" *ngIf="user.role">
                      {{ user.role }}
                    </div>
                  </button>
                </div>
              </div>

              <!-- Suggestions de hashtags -->
              <div
                *ngIf="showHashtagSuggestions && hashtagSuggestions.length > 0"
                class="whatsapp-hashtag-suggestions"
              >
                <div class="whatsapp-hashtag-suggestions-header">
                  <i class="fas fa-hashtag"></i>
                  <span>Hashtags populaires</span>
                </div>
                <div class="whatsapp-hashtag-suggestions-list">
                  <button
                    *ngFor="let hashtag of hashtagSuggestions; let i = index"
                    (click)="insertHashtag(hashtag)"
                    class="whatsapp-hashtag-suggestion"
                    [ngClass]="{ selected: selectedHashtagIndex === i }"
                  >
                    <span class="whatsapp-hashtag-text"
                      >#{{ hashtag.tag }}</span
                    >
                    <span class="whatsapp-hashtag-count"
                      >{{ hashtag.count }} utilisations</span
                    >
                  </button>
                </div>
              </div>

              <!-- Suggestions de commandes -->
              <div
                *ngIf="showCommandSuggestions && commandSuggestions.length > 0"
                class="whatsapp-command-suggestions"
              >
                <div class="whatsapp-command-suggestions-header">
                  <i class="fas fa-terminal"></i>
                  <span>Commandes disponibles</span>
                </div>
                <div class="whatsapp-command-suggestions-list">
                  <button
                    *ngFor="let command of commandSuggestions; let i = index"
                    (click)="insertCommand(command)"
                    class="whatsapp-command-suggestion"
                    [ngClass]="{ selected: selectedCommandIndex === i }"
                  >
                    <div class="whatsapp-command-icon">
                      <i [class]="command.icon"></i>
                    </div>
                    <div class="whatsapp-command-info">
                      <span class="whatsapp-command-name"
                        >/{{ command.name }}</span
                      >
                      <span class="whatsapp-command-description">{{
                        command.description
                      }}</span>
                    </div>
                    <div
                      class="whatsapp-command-shortcut"
                      *ngIf="command.shortcut"
                    >
                      {{ command.shortcut }}
                    </div>
                  </button>
                </div>
              </div>
            </div>

            <!-- Indicateurs et compteurs -->
            <div class="whatsapp-input-indicators">
              <!-- Compteur de caractères -->
              <div
                *ngIf="messageForm.get('content')?.value?.length > 3000"
                class="whatsapp-char-counter"
              >
                <span
                  [ngClass]="{
                    warning: getCharacterCount() > 3500,
                    danger: getCharacterCount() > 3900
                  }"
                >
                  {{ 4096 - getCharacterCount() }}
                </span>
              </div>

              <!-- Indicateur de frappe -->
              <div *ngIf="isUserTyping" class="whatsapp-typing-indicator-self">
                <i class="fas fa-keyboard"></i>
                <span>En cours de frappe...</span>
              </div>

              <!-- Indicateurs de formatage actif -->
              <div
                *ngIf="hasFormattedText()"
                class="whatsapp-formatting-indicator"
              >
                <span
                  class="whatsapp-format-indicator"
                  *ngIf="hasFormatting('bold')"
                  title="Gras"
                >
                  <i class="fas fa-bold"></i>
                </span>
                <span
                  class="whatsapp-format-indicator"
                  *ngIf="hasFormatting('italic')"
                  title="Italique"
                >
                  <i class="fas fa-italic"></i>
                </span>
                <span
                  class="whatsapp-format-indicator"
                  *ngIf="hasFormatting('strikethrough')"
                  title="Barré"
                >
                  <i class="fas fa-strikethrough"></i>
                </span>
                <span
                  class="whatsapp-format-indicator"
                  *ngIf="hasFormatting('code')"
                  title="Code"
                >
                  <i class="fas fa-code"></i>
                </span>
                <span
                  class="whatsapp-format-indicator"
                  *ngIf="hasFormatting('quote')"
                  title="Citation"
                >
                  <i class="fas fa-quote-left"></i>
                </span>
              </div>

              <!-- Indicateur de langue détectée -->
              <div
                *ngIf="detectedLanguage && detectedLanguage !== 'fr'"
                class="whatsapp-language-indicator"
              >
                <i class="fas fa-language"></i>
                <span>{{ getLanguageName(detectedLanguage) }}</span>
                <button
                  (click)="translateMessage()"
                  class="whatsapp-translate-btn"
                  title="Traduire"
                >
                  <i class="fas fa-exchange-alt"></i>
                </button>
              </div>

              <!-- Indicateur de correction automatique -->
              <div
                *ngIf="autoCorrections.length > 0"
                class="whatsapp-autocorrect-indicator"
              >
                <i class="fas fa-spell-check"></i>
                <span>{{ autoCorrections.length }} correction(s)</span>
                <button
                  (click)="showAutoCorrections()"
                  class="whatsapp-corrections-btn"
                >
                  <i class="fas fa-eye"></i>
                </button>
              </div>
            </div>

            <!-- Barre d'outils de formatage -->
            <div
              *ngIf="showFormattingToolbar"
              class="whatsapp-formatting-toolbar"
            >
              <div class="whatsapp-formatting-section">
                <button
                  (click)="applyFormatting('bold')"
                  class="whatsapp-format-btn"
                  [ngClass]="{ active: hasFormatting('bold') }"
                  title="Gras (Ctrl+B)"
                >
                  <i class="fas fa-bold"></i>
                </button>
                <button
                  (click)="applyFormatting('italic')"
                  class="whatsapp-format-btn"
                  [ngClass]="{ active: hasFormatting('italic') }"
                  title="Italique (Ctrl+I)"
                >
                  <i class="fas fa-italic"></i>
                </button>
                <button
                  (click)="applyFormatting('strikethrough')"
                  class="whatsapp-format-btn"
                  [ngClass]="{ active: hasFormatting('strikethrough') }"
                  title="Barré (Ctrl+Shift+X)"
                >
                  <i class="fas fa-strikethrough"></i>
                </button>
                <button
                  (click)="applyFormatting('underline')"
                  class="whatsapp-format-btn"
                  [ngClass]="{ active: hasFormatting('underline') }"
                  title="Souligné (Ctrl+U)"
                >
                  <i class="fas fa-underline"></i>
                </button>
              </div>

              <div class="whatsapp-format-separator"></div>

              <div class="whatsapp-formatting-section">
                <button
                  (click)="applyFormatting('code')"
                  class="whatsapp-format-btn"
                  [ngClass]="{ active: hasFormatting('code') }"
                  title="Code (Ctrl+Shift+C)"
                >
                  <i class="fas fa-code"></i>
                </button>
                <button
                  (click)="applyFormatting('quote')"
                  class="whatsapp-format-btn"
                  [ngClass]="{ active: hasFormatting('quote') }"
                  title="Citation (Ctrl+Shift+Q)"
                >
                  <i class="fas fa-quote-left"></i>
                </button>
                <button
                  (click)="applyFormatting('spoiler')"
                  class="whatsapp-format-btn"
                  [ngClass]="{ active: hasFormatting('spoiler') }"
                  title="Spoiler"
                >
                  <i class="fas fa-eye-slash"></i>
                </button>
              </div>

              <div class="whatsapp-format-separator"></div>

              <div class="whatsapp-formatting-section">
                <button
                  (click)="insertLink()"
                  class="whatsapp-format-btn"
                  title="Insérer un lien (Ctrl+K)"
                >
                  <i class="fas fa-link"></i>
                </button>
                <button
                  (click)="insertTable()"
                  class="whatsapp-format-btn"
                  title="Insérer un tableau"
                >
                  <i class="fas fa-table"></i>
                </button>
                <button
                  (click)="insertList('ul')"
                  class="whatsapp-format-btn"
                  title="Liste à puces"
                >
                  <i class="fas fa-list-ul"></i>
                </button>
                <button
                  (click)="insertList('ol')"
                  class="whatsapp-format-btn"
                  title="Liste numérotée"
                >
                  <i class="fas fa-list-ol"></i>
                </button>
              </div>

              <div class="whatsapp-format-separator"></div>

              <div class="whatsapp-formatting-section">
                <button
                  (click)="insertEmoji('😀')"
                  class="whatsapp-format-btn"
                  title="Insérer emoji"
                >
                  <i class="fas fa-smile"></i>
                </button>
                <button
                  (click)="insertMentionSymbol()"
                  class="whatsapp-format-btn"
                  title="Mentionner (@)"
                >
                  <i class="fas fa-at"></i>
                </button>
                <button
                  (click)="insertHashtagSymbol()"
                  class="whatsapp-format-btn"
                  title="Hashtag (#)"
                >
                  <i class="fas fa-hashtag"></i>
                </button>
              </div>

              <div class="whatsapp-format-separator"></div>

              <div class="whatsapp-formatting-section">
                <button
                  (click)="clearFormatting()"
                  class="whatsapp-format-btn"
                  title="Effacer le formatage (Ctrl+Shift+Z)"
                >
                  <i class="fas fa-remove-format"></i>
                </button>
                <button
                  (click)="toggleFormattingToolbar()"
                  class="whatsapp-format-btn"
                  title="Fermer la barre d'outils"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>

            <!-- Suggestions de correction automatique -->
            <div
              *ngIf="
                showAutoCorrectSuggestions && autoCorrectSuggestions.length > 0
              "
              class="whatsapp-autocorrect-suggestions"
            >
              <div class="whatsapp-autocorrect-header">
                <i class="fas fa-spell-check"></i>
                <span>Corrections suggérées</span>
                <button
                  (click)="hideAutoCorrectSuggestions()"
                  class="whatsapp-autocorrect-close"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="whatsapp-autocorrect-list">
                <div
                  *ngFor="let suggestion of autoCorrectSuggestions"
                  class="whatsapp-autocorrect-item"
                >
                  <div class="whatsapp-autocorrect-original">
                    <span class="whatsapp-error-word">{{
                      suggestion.original
                    }}</span>
                    <i class="fas fa-arrow-right"></i>
                  </div>
                  <div class="whatsapp-autocorrect-suggestions-list">
                    <button
                      *ngFor="let correction of suggestion.suggestions"
                      (click)="applyCorrection(suggestion.original, correction)"
                      class="whatsapp-correction-btn"
                    >
                      {{ correction }}
                    </button>
                  </div>
                  <button
                    (click)="ignoreCorrection(suggestion.original)"
                    class="whatsapp-ignore-btn"
                    title="Ignorer"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Boutons d'action droite -->
      <div class="whatsapp-input-actions-right">
        <!-- Bouton d'envoi ou microphone -->
        <div class="whatsapp-send-container">
          <button
            *ngIf="hasMessageContent() || selectedFile"
            type="submit"
            (click)="sendMessage()"
            class="whatsapp-send-btn"
            [disabled]="!canSendMessage()"
            [ngClass]="{ sending: isSendingMessage }"
            title="Envoyer"
          >
            <i class="fas fa-paper-plane" *ngIf="!isSendingMessage"></i>
            <i class="fas fa-spinner fa-spin" *ngIf="isSendingMessage"></i>
          </button>

          <button
            *ngIf="!hasMessageContent() && !selectedFile"
            type="button"
            (mousedown)="startVoiceRecording()"
            (mouseup)="stopVoiceRecording()"
            (mouseleave)="cancelVoiceRecording()"
            (touchstart)="startVoiceRecording()"
            (touchend)="stopVoiceRecording()"
            class="whatsapp-voice-btn"
            [ngClass]="{ recording: isRecordingVoice }"
            title="Maintenir pour enregistrer"
          >
            <i class="fas fa-microphone"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Sélecteur d'émojis -->
    <div *ngIf="showEmojiPicker" class="whatsapp-emoji-picker">
      <div class="whatsapp-emoji-picker-header">
        <div class="whatsapp-emoji-categories">
          <button
            *ngFor="let category of emojiCategories"
            (click)="selectEmojiCategory(category.name)"
            class="whatsapp-emoji-category-btn"
            [ngClass]="{ active: selectedEmojiCategory === category.name }"
            [title]="category.label"
          >
            {{ category.icon }}
          </button>
        </div>
        <button (click)="toggleEmojiPicker()" class="whatsapp-emoji-close">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="whatsapp-emoji-search">
        <input
          type="text"
          [(ngModel)]="emojiSearchQuery"
          (input)="searchEmojis()"
          placeholder="Rechercher un emoji..."
          class="whatsapp-emoji-search-input"
        />
      </div>

      <div class="whatsapp-emoji-grid">
        <button
          *ngFor="let emoji of getFilteredEmojis()"
          (click)="insertEmoji(emoji.char)"
          class="whatsapp-emoji-btn"
          [title]="emoji.name"
        >
          {{ emoji.char }}
        </button>
      </div>

      <!-- Émojis récents -->
      <div *ngIf="recentEmojis.length > 0" class="whatsapp-recent-emojis">
        <div class="whatsapp-recent-emojis-header">Récemment utilisés</div>
        <div class="whatsapp-recent-emojis-grid">
          <button
            *ngFor="let emoji of recentEmojis"
            (click)="insertEmoji(emoji)"
            class="whatsapp-emoji-btn"
          >
            {{ emoji }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Inputs de fichier cachés -->
  <input
    #fileInputImage
    type="file"
    accept="image/*"
    (change)="onFileSelected($event)"
    style="display: none"
    multiple
  />
  <input
    #fileInputVideo
    type="file"
    accept="video/*"
    (change)="onFileSelected($event)"
    style="display: none"
  />
  <input
    #fileInputAudio
    type="file"
    accept="audio/*"
    (change)="onFileSelected($event)"
    style="display: none"
  />
  <input
    #fileInputDocument
    type="file"
    accept=".pdf,.doc,.docx,.txt,.xlsx,.pptx"
    (change)="onFileSelected($event)"
    style="display: none"
  />

  <!-- Panneau de notifications -->
  <div class="side-panel" [ngClass]="{ open: showNotificationPanel }">
    <div class="panel-header">
      <h3>Notifications</h3>
      <button class="close-btn" (click)="toggleNotificationPanel()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div
      *ngIf="notifications.length === 0"
      style="padding: 1rem; text-align: center; color: #888"
    >
      <i
        class="fas fa-bell-slash"
        style="font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.5"
      ></i>
      <p>Aucune notification</p>
    </div>
    <div
      *ngFor="let notification of notifications"
      style="padding: 0.5rem; border-bottom: 1px solid rgba(0, 247, 255, 0.1)"
    >
      <p style="margin: 0; font-size: 0.9rem">{{ notification.content }}</p>
      <small style="opacity: 0.7">{{
        formatMessageTime(notification.timestamp)
      }}</small>
    </div>
  </div>

  <!-- Barre de recherche -->
  <div
    *ngIf="showSearchBar"
    class="search-bar bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] px-4 py-3 relative z-10"
  >
    <div class="flex items-center space-x-3">
      <!-- Icône de recherche -->
      <div class="text-[#6d6870] dark:text-[#a0a0a0]">
        <i class="fas fa-search text-sm"></i>
      </div>

      <!-- Champ de recherche -->
      <div class="flex-1 relative">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          (input)="onSearchInput($event)"
          (keydown)="onSearchKeyPress($event)"
          placeholder="Rechercher dans cette conversation..."
          class="w-full px-3 py-2 text-sm bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 border border-[#edf1f4] dark:border-[#3a3a3a] rounded-lg focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 transition-colors"
          autofocus
        />

        <!-- Indicateur de chargement -->
        <div
          *ngIf="isSearching"
          class="absolute right-3 top-1/2 transform -translate-y-1/2"
        >
          <div
            class="w-4 h-4 border-2 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin"
          ></div>
        </div>

        <!-- Bouton de suppression -->
        <button
          *ngIf="searchQuery && !isSearching"
          (click)="clearSearch()"
          class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
        >
          <i class="fas fa-times text-xs"></i>
        </button>
      </div>

      <!-- Bouton de fermeture -->
      <button
        (click)="toggleSearchBar()"
        class="text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Résultats de recherche -->
    <div
      *ngIf="searchMode && searchResults.length > 0"
      class="mt-3 max-h-40 overflow-y-auto border border-[#edf1f4]/50 dark:border-[#3a3a3a] rounded-lg bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30"
    >
      <div
        class="p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#3a3a3a]"
      >
        {{ searchResults.length }} résultat(s) trouvé(s)
      </div>
      <div class="max-h-32 overflow-y-auto">
        <button
          *ngFor="let result of searchResults"
          (click)="result.id && navigateToMessage(result.id)"
          class="w-full text-left px-3 py-2 hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0"
        >
          <div class="flex items-start space-x-2">
            <div
              class="w-6 h-6 rounded-full bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 flex items-center justify-center flex-shrink-0 mt-0.5"
            >
              <i
                class="fas fa-comment text-xs text-[#4f5fad] dark:text-[#6d78c9]"
              ></i>
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0] mb-1">
                {{ formatMessageTime(result.timestamp) }}
              </div>
              <div
                class="text-sm text-[#6d6870] dark:text-[#a0a0a0] truncate"
                [innerHTML]="
                  highlightSearchTerms(result.content || '', searchQuery)
                "
              ></div>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- Message aucun résultat -->
    <div
      *ngIf="
        searchMode &&
        searchResults.length === 0 &&
        !isSearching &&
        searchQuery.length >= 2
      "
      class="mt-3 p-3 text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30 rounded-lg"
    >
      <i
        class="fas fa-search text-lg mb-2 block text-[#6d6870]/50 dark:text-[#a0a0a0]/50"
      ></i>
      Aucun message trouvé pour "{{ searchQuery }}"
    </div>
  </div>

  <!-- Panneau des messages épinglés -->
  <div
    *ngIf="showPinnedMessages"
    class="pinned-messages-panel bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] relative z-10"
  >
    <!-- En-tête du panneau -->
    <div
      class="flex items-center justify-between p-4 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]"
    >
      <div class="flex items-center space-x-2">
        <i class="fas fa-thumbtack text-[#4f5fad] dark:text-[#6d78c9]"></i>
        <h3 class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]">
          Messages épinglés ({{ getPinnedMessagesCount() }})
        </h3>
      </div>
      <button
        (click)="togglePinnedMessages()"
        class="w-6 h-6 flex items-center justify-center text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-full transition-colors"
      >
        <i class="fas fa-times text-xs"></i>
      </button>
    </div>

    <!-- Liste des messages épinglés -->
    <div class="max-h-48 overflow-y-auto">
      <!-- État de chargement -->
      <div *ngIf="pinnedMessages.length === 0" class="p-4 text-center">
        <div class="text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
          <i class="fas fa-thumbtack text-2xl mb-2 block opacity-50"></i>
          <div class="text-sm">Aucun message épinglé</div>
        </div>
      </div>

      <!-- Messages épinglés -->
      <div
        *ngIf="pinnedMessages.length > 0"
        class="divide-y divide-[#edf1f4]/30 dark:divide-[#3a3a3a]/30"
      >
        <button
          *ngFor="let pinnedMessage of pinnedMessages"
          (click)="scrollToPinnedMessage(pinnedMessage.id!)"
          class="w-full text-left p-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors"
        >
          <div class="flex items-start space-x-3">
            <!-- Avatar de l'expéditeur -->
            <div class="flex-shrink-0">
              <img
                [src]="
                  pinnedMessage.sender?.image ||
                  'assets/images/default-avatar.png'
                "
                [alt]="pinnedMessage.sender?.username || 'User'"
                class="w-8 h-8 rounded-full object-cover"
                onerror="this.src='assets/images/default-avatar.png'"
              />
            </div>

            <!-- Contenu du message -->
            <div class="flex-1 min-w-0">
              <!-- En-tête avec nom et date -->
              <div class="flex items-center justify-between mb-1">
                <span
                  class="text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9]"
                >
                  {{ pinnedMessage.sender?.username || "Utilisateur inconnu" }}
                </span>
                <span class="text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
                  {{ formatMessageTime(pinnedMessage.timestamp) }}
                </span>
              </div>

              <!-- Contenu du message -->
              <div
                class="text-sm text-[#6d6870] dark:text-[#a0a0a0] line-clamp-2"
              >
                <span *ngIf="pinnedMessage.content">{{
                  pinnedMessage.content
                }}</span>
                <span
                  *ngIf="hasImage(pinnedMessage)"
                  class="italic flex items-center"
                >
                  <i class="fas fa-image mr-1"></i>
                  Image
                </span>
                <span
                  *ngIf="isVoiceMessage(pinnedMessage)"
                  class="italic flex items-center"
                >
                  <i class="fas fa-microphone mr-1"></i>
                  Message vocal
                </span>
              </div>

              <!-- Indicateur d'épinglage -->
              <div class="flex items-center mt-1">
                <i
                  class="fas fa-thumbtack text-xs text-[#4f5fad] dark:text-[#6d78c9] mr-1"
                ></i>
                <span class="text-xs text-[#4f5fad] dark:text-[#6d78c9]">
                  Épinglé
                  {{
                    pinnedMessage.pinnedAt
                      ? "le " + formatMessageDate(pinnedMessage.pinnedAt)
                      : ""
                  }}
                </span>
              </div>
            </div>

            <!-- Icône de navigation -->
            <div class="flex-shrink-0 text-[#6d6870]/50 dark:text-[#a0a0a0]/50">
              <i class="fas fa-chevron-right text-xs"></i>
            </div>
          </div>
        </button>
      </div>
    </div>
  </div>

  <!-- Modales et panneaux latéraux -->

  <!-- Modal d'appel entrant -->
  <div *ngIf="incomingCall" class="whatsapp-call-modal incoming-call">
    <div class="whatsapp-call-modal-overlay"></div>
    <div class="whatsapp-call-modal-content">
      <div class="whatsapp-call-info">
        <div class="whatsapp-call-avatar">
          <img
            [src]="
              incomingCall.caller?.image || 'assets/images/default-avatar.png'
            "
            [alt]="incomingCall.caller?.username"
          />
        </div>
        <div class="whatsapp-call-details">
          <h3>{{ incomingCall.caller?.username }}</h3>
          <p>
            {{
              incomingCall.type === "AUDIO"
                ? "Appel audio entrant"
                : "Appel vidéo entrant"
            }}
          </p>
        </div>
      </div>
      <div class="whatsapp-call-actions">
        <button (click)="declineCall()" class="whatsapp-call-decline">
          <i class="fas fa-phone-slash"></i>
        </button>
        <button (click)="acceptCall()" class="whatsapp-call-accept">
          <i class="fas fa-phone"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Modal d'appel actif -->
  <div
    *ngIf="activeCall"
    class="whatsapp-call-modal active-call"
    [ngClass]="{ minimized: isCallMinimized }"
  >
    <div class="whatsapp-call-modal-content">
      <div class="whatsapp-call-header">
        <div class="whatsapp-call-participant">
          <img
            [src]="
              activeCall.participant?.image ||
              'assets/images/default-avatar.png'
            "
            [alt]="activeCall.participant?.username"
          />
          <div class="whatsapp-call-participant-info">
            <h4>{{ activeCall.participant?.username }}</h4>
            <span class="whatsapp-call-duration">{{
              formatCallDuration(callDuration)
            }}</span>
            <span class="whatsapp-call-status">{{ getCallStatusText() }}</span>
          </div>
        </div>
        <button (click)="toggleCallMinimize()" class="whatsapp-call-minimize">
          <i class="fas fa-minus" *ngIf="!isCallMinimized"></i>
          <i class="fas fa-expand" *ngIf="isCallMinimized"></i>
        </button>
      </div>

      <div *ngIf="activeCall.type === 'VIDEO'" class="whatsapp-video-container">
        <video #remoteVideo class="whatsapp-remote-video" autoplay></video>
        <video #localVideo class="whatsapp-local-video" autoplay muted></video>
      </div>

      <div
        class="whatsapp-call-controls"
        [ngClass]="{ show: showCallControls || !isCallMinimized }"
      >
        <button
          (click)="toggleMute()"
          class="whatsapp-call-control"
          [ngClass]="{ active: isCallMuted }"
        >
          <i class="fas fa-microphone" *ngIf="!isCallMuted"></i>
          <i class="fas fa-microphone-slash" *ngIf="isCallMuted"></i>
        </button>
        <button
          *ngIf="activeCall.type === 'VIDEO'"
          (click)="toggleVideo()"
          class="whatsapp-call-control"
          [ngClass]="{ active: !isVideoEnabled }"
        >
          <i class="fas fa-video" *ngIf="isVideoEnabled"></i>
          <i class="fas fa-video-slash" *ngIf="!isVideoEnabled"></i>
        </button>
        <button (click)="endCall()" class="whatsapp-call-end">
          <i class="fas fa-phone-slash"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Modal de confirmation de suppression -->
  <div *ngIf="showDeleteConfirmModal" class="whatsapp-modal-overlay">
    <div class="whatsapp-modal-content">
      <div class="whatsapp-modal-header">
        <h3>Confirmer la suppression</h3>
      </div>
      <div class="whatsapp-modal-body">
        <p>
          Êtes-vous sûr de vouloir supprimer ce message ? Cette action est
          irréversible.
        </p>
      </div>
      <div class="whatsapp-modal-actions">
        <button (click)="cancelDelete()" class="whatsapp-modal-cancel">
          Annuler
        </button>
        <button (click)="confirmDelete()" class="whatsapp-modal-confirm">
          Supprimer
        </button>
      </div>
    </div>
  </div>

  <!-- Modal de transfert de message -->
  <div *ngIf="showForwardModal" class="whatsapp-modal-overlay">
    <div class="whatsapp-modal-content whatsapp-forward-modal">
      <div class="whatsapp-modal-header">
        <h3>Transférer le message</h3>
        <button (click)="closeForwardModal()" class="whatsapp-modal-close">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="whatsapp-modal-body">
        <div class="whatsapp-forward-search">
          <input
            type="text"
            [(ngModel)]="forwardSearchQuery"
            placeholder="Rechercher des contacts..."
            class="whatsapp-forward-search-input"
          />
        </div>
        <div class="whatsapp-forward-contacts">
          <div
            *ngFor="let contact of getFilteredContacts()"
            class="whatsapp-forward-contact"
            (click)="toggleContactSelection(contact.id)"
          >
            <input
              type="checkbox"
              [checked]="selectedContacts.includes(contact.id)"
            />
            <img
              [src]="contact.image || 'assets/images/default-avatar.png'"
              [alt]="contact.username"
            />
            <span>{{ contact.username }}</span>
          </div>
        </div>
      </div>
      <div class="whatsapp-modal-actions">
        <button (click)="closeForwardModal()" class="whatsapp-modal-cancel">
          Annuler
        </button>
        <button
          (click)="confirmForward()"
          class="whatsapp-modal-confirm"
          [disabled]="selectedContacts.length === 0"
        >
          Transférer ({{ selectedContacts.length }})
        </button>
      </div>
    </div>
  </div>

  <!-- Visualiseur d'images -->
  <div *ngIf="showImageViewer" class="whatsapp-image-viewer">
    <div
      class="whatsapp-image-viewer-overlay"
      (click)="closeImageViewer()"
    ></div>
    <div class="whatsapp-image-viewer-content">
      <div class="whatsapp-image-viewer-header">
        <div class="whatsapp-image-viewer-info">
          <span>{{ currentImageIndex + 1 }} / {{ imageGallery.length }}</span>
        </div>
        <button
          (click)="closeImageViewer()"
          class="whatsapp-image-viewer-close"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="whatsapp-image-viewer-body">
        <button
          *ngIf="imageGallery.length > 1"
          (click)="previousImage()"
          class="whatsapp-image-nav whatsapp-image-prev"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        <img
          [src]="imageGallery[currentImageIndex]?.url"
          [alt]="imageGallery[currentImageIndex]?.caption"
          class="whatsapp-image-viewer-img"
        />
        <button
          *ngIf="imageGallery.length > 1"
          (click)="nextImage()"
          class="whatsapp-image-nav whatsapp-image-next"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
      <div
        *ngIf="imageGallery[currentImageIndex]?.caption"
        class="whatsapp-image-viewer-caption"
      >
        {{ imageGallery[currentImageIndex]?.caption }}
      </div>
    </div>
  </div>

  <!-- Toast de notification -->
  <div *ngIf="showToast" class="whatsapp-toast" [ngClass]="toastType">
    <div class="whatsapp-toast-content">
      <i [class]="getToastIcon()"></i>
      <span>{{ toastMessage }}</span>
    </div>
  </div>

  <!-- Panneau d'informations de conversation -->
  <div
    *ngIf="showConversationInfo"
    class="whatsapp-side-panel whatsapp-conversation-info"
  >
    <div class="whatsapp-side-panel-header">
      <h3>Informations de conversation</h3>
      <button
        (click)="toggleConversationInfo()"
        class="whatsapp-side-panel-close"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="whatsapp-side-panel-body">
      <div class="whatsapp-conversation-participant">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
        />
        <div class="whatsapp-participant-details">
          <h4>{{ otherParticipant?.username }}</h4>
          <p>{{ otherParticipant?.email }}</p>
          <span class="whatsapp-participant-status">{{
            otherParticipant?.isOnline ? "En ligne" : "Hors ligne"
          }}</span>
        </div>
      </div>
      <div class="whatsapp-conversation-stats">
        <div class="whatsapp-stat">
          <span class="whatsapp-stat-label">Messages</span>
          <span class="whatsapp-stat-value">{{ getTotalMessagesCount() }}</span>
        </div>
        <div class="whatsapp-stat">
          <span class="whatsapp-stat-label">Photos</span>
          <span class="whatsapp-stat-value">{{ getPhotosCount() }}</span>
        </div>
        <div class="whatsapp-stat">
          <span class="whatsapp-stat-label">Fichiers</span>
          <span class="whatsapp-stat-value">{{ getFilesCount() }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau de paramètres de conversation -->
  <div
    *ngIf="showConversationSettings"
    class="whatsapp-side-panel whatsapp-conversation-settings"
  >
    <div class="whatsapp-side-panel-header">
      <h3>Paramètres de conversation</h3>
      <button
        (click)="toggleConversationSettings()"
        class="whatsapp-side-panel-close"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="whatsapp-side-panel-body">
      <div class="whatsapp-setting-group">
        <h4>Notifications</h4>
        <div class="whatsapp-setting-item">
          <label>
            <input
              type="checkbox"
              [(ngModel)]="conversationSettings.notifications"
              (change)="updateConversationSettings()"
            />
            <span>Recevoir des notifications</span>
          </label>
        </div>
        <div class="whatsapp-setting-item">
          <label>
            <input
              type="checkbox"
              [(ngModel)]="conversationSettings.soundNotifications"
              (change)="updateConversationSettings()"
            />
            <span>Sons de notification</span>
          </label>
        </div>
      </div>
      <div class="whatsapp-setting-group">
        <h4>Confidentialité</h4>
        <div class="whatsapp-setting-item">
          <label>
            <input
              type="checkbox"
              [(ngModel)]="conversationSettings.readReceipts"
              (change)="updateConversationSettings()"
            />
            <span>Accusés de lecture</span>
          </label>
        </div>
        <div class="whatsapp-setting-item">
          <label>
            <input
              type="checkbox"
              [(ngModel)]="conversationSettings.typingIndicators"
              (change)="updateConversationSettings()"
            />
            <span>Indicateurs de frappe</span>
          </label>
        </div>
      </div>
    </div>
  </div>

  <!-- Panneau de traduction avancé -->
  <div *ngIf="showTranslationPanel" class="whatsapp-translation-panel">
    <div class="whatsapp-translation-header">
      <h3>Traduction automatique</h3>
      <button
        (click)="closeTranslationPanel()"
        class="whatsapp-translation-close"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="whatsapp-translation-content">
      <div class="whatsapp-translation-languages">
        <select [(ngModel)]="translationFrom" class="whatsapp-language-select">
          <option value="auto">Détection automatique</option>
          <option *ngFor="let lang of supportedLanguages" [value]="lang.code">
            {{ lang.name }}
          </option>
        </select>
        <button (click)="swapLanguages()" class="whatsapp-swap-languages">
          <i class="fas fa-exchange-alt"></i>
        </button>
        <select [(ngModel)]="translationTo" class="whatsapp-language-select">
          <option *ngFor="let lang of supportedLanguages" [value]="lang.code">
            {{ lang.name }}
          </option>
        </select>
      </div>
      <div class="whatsapp-translation-text">
        <div class="whatsapp-translation-original">
          <label>Texte original :</label>
          <div class="whatsapp-original-text">
            {{ originalTextForTranslation }}
          </div>
        </div>
        <div class="whatsapp-translation-result">
          <label>Traduction :</label>
          <div class="whatsapp-translated-text" *ngIf="!isTranslating">
            {{ translatedText }}
          </div>
          <div class="whatsapp-translation-loading" *ngIf="isTranslating">
            <i class="fas fa-spinner fa-spin"></i>
            Traduction en cours...
          </div>
        </div>
      </div>
      <div class="whatsapp-translation-actions">
        <button (click)="copyTranslation()" class="whatsapp-translation-btn">
          <i class="fas fa-copy"></i>
          Copier
        </button>
        <button (click)="insertTranslation()" class="whatsapp-translation-btn">
          <i class="fas fa-plus"></i>
          Insérer
        </button>
        <button (click)="shareTranslation()" class="whatsapp-translation-btn">
          <i class="fas fa-share"></i>
          Partager
        </button>
      </div>
    </div>
  </div>

  <!-- Créateur de sondage avancé -->
  <div *ngIf="showPollCreator" class="whatsapp-poll-creator">
    <div class="whatsapp-poll-header">
      <h3>Créer un sondage</h3>
      <button (click)="closePollCreator()" class="whatsapp-poll-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="whatsapp-poll-content">
      <div class="whatsapp-poll-question">
        <label>Question du sondage :</label>
        <input
          type="text"
          [(ngModel)]="pollQuestion"
          placeholder="Posez votre question..."
          class="whatsapp-poll-input"
          maxlength="200"
        />
        <span class="whatsapp-poll-counter"
          >{{ pollQuestion.length || 0 }}/200</span
        >
      </div>
      <div class="whatsapp-poll-options">
        <label>Options de réponse :</label>
        <div class="whatsapp-poll-options-list">
          <div
            *ngFor="let option of pollOptions; let i = index"
            class="whatsapp-poll-option"
          >
            <input
              type="text"
              [(ngModel)]="option.text"
              [placeholder]="'Option ' + (i + 1)"
              class="whatsapp-poll-option-input"
              maxlength="100"
            />
            <button
              (click)="removePollOption(i)"
              class="whatsapp-poll-remove-option"
              *ngIf="pollOptions.length > 2"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <button
          (click)="addPollOption()"
          class="whatsapp-poll-add-option"
          *ngIf="pollOptions.length < 10"
        >
          <i class="fas fa-plus"></i>
          Ajouter une option
        </button>
      </div>
      <div class="whatsapp-poll-settings">
        <div class="whatsapp-poll-setting">
          <label>
            <input type="checkbox" [(ngModel)]="pollSettings.allowMultiple" />
            Autoriser plusieurs réponses
          </label>
        </div>
        <div class="whatsapp-poll-setting">
          <label>
            <input type="checkbox" [(ngModel)]="pollSettings.anonymous" />
            Sondage anonyme
          </label>
        </div>
        <div class="whatsapp-poll-setting">
          <label>
            <input type="checkbox" [(ngModel)]="pollSettings.showResults" />
            Afficher les résultats en temps réel
          </label>
        </div>
        <div class="whatsapp-poll-setting" *ngIf="!pollSettings.anonymous">
          <label>
            <input type="checkbox" [(ngModel)]="pollSettings.showVoters" />
            Afficher qui a voté
          </label>
        </div>
      </div>
      <div class="whatsapp-poll-expiry">
        <label>Expiration du sondage :</label>
        <select [(ngModel)]="pollExpiry" class="whatsapp-poll-expiry-select">
          <option value="">Pas d'expiration</option>
          <option value="1h">1 heure</option>
          <option value="6h">6 heures</option>
          <option value="1d">1 jour</option>
          <option value="3d">3 jours</option>
          <option value="1w">1 semaine</option>
          <option value="custom">Personnalisé</option>
        </select>
        <input
          *ngIf="pollExpiry === 'custom'"
          type="datetime-local"
          [(ngModel)]="customPollExpiry"
          class="whatsapp-poll-custom-expiry"
        />
      </div>
    </div>
    <div class="whatsapp-poll-actions">
      <button (click)="closePollCreator()" class="whatsapp-poll-cancel">
        Annuler
      </button>
      <button
        (click)="createPoll()"
        class="whatsapp-poll-create"
        [disabled]="!canCreatePoll()"
      >
        Créer le sondage
      </button>
    </div>
  </div>

  <!-- Sélecteur de localisation avancé -->
  <div *ngIf="showLocationPicker" class="whatsapp-location-picker">
    <div class="whatsapp-location-header">
      <h3>Partager votre position</h3>
      <button (click)="closeLocationPicker()" class="whatsapp-location-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="whatsapp-location-content">
      <div class="whatsapp-location-map-container">
        <div id="locationMap" class="whatsapp-location-map"></div>
        <div class="whatsapp-location-controls">
          <button
            (click)="getCurrentLocation()"
            class="whatsapp-location-current"
          >
            <i class="fas fa-crosshairs"></i>
            Ma position actuelle
          </button>
          <button (click)="searchLocation()" class="whatsapp-location-search">
            <i class="fas fa-search"></i>
            Rechercher un lieu
          </button>
        </div>
      </div>
      <div class="whatsapp-location-search-panel" *ngIf="showLocationSearch">
        <input
          type="text"
          [(ngModel)]="locationSearchQuery"
          (input)="onLocationSearch()"
          placeholder="Rechercher une adresse..."
          class="whatsapp-location-search-input"
        />
        <div
          class="whatsapp-location-results"
          *ngIf="locationSearchResults.length > 0"
        >
          <div
            *ngFor="let result of locationSearchResults"
            (click)="selectLocationResult(result)"
            class="whatsapp-location-result"
          >
            <div class="whatsapp-location-result-icon">
              <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="whatsapp-location-result-info">
              <div class="whatsapp-location-result-name">{{ result.name }}</div>
              <div class="whatsapp-location-result-address">
                {{ result.address }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="whatsapp-location-selected" *ngIf="selectedLocation">
        <div class="whatsapp-location-selected-info">
          <h4>{{ selectedLocation.name }}</h4>
          <p>{{ selectedLocation.address }}</p>
          <small
            >{{ selectedLocation.coordinates.lat }},
            {{ selectedLocation.coordinates.lng }}</small
          >
        </div>
        <div class="whatsapp-location-selected-actions">
          <button
            (click)="editLocationMessage()"
            class="whatsapp-location-edit"
          >
            <i class="fas fa-edit"></i>
            Ajouter un message
          </button>
        </div>
      </div>
      <div class="whatsapp-location-message" *ngIf="showLocationMessage">
        <textarea
          [(ngModel)]="locationMessage"
          placeholder="Ajouter un message à votre position..."
          class="whatsapp-location-message-input"
          maxlength="500"
        ></textarea>
        <span class="whatsapp-location-message-counter"
          >{{ locationMessage.length || 0 }}/500</span
        >
      </div>
    </div>
    <div class="whatsapp-location-actions">
      <button (click)="closeLocationPicker()" class="whatsapp-location-cancel">
        Annuler
      </button>
      <button
        (click)="shareLocation()"
        class="whatsapp-location-share"
        [disabled]="!selectedLocation"
      >
        <i class="fas fa-share"></i>
        Partager la position
      </button>
    </div>
  </div>

  <!-- Sélecteur de contact avancé -->
  <div *ngIf="showContactPicker" class="whatsapp-contact-picker">
    <div class="whatsapp-contact-header">
      <h3>Partager un contact</h3>
      <button (click)="closeContactPicker()" class="whatsapp-contact-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="whatsapp-contact-content">
      <div class="whatsapp-contact-search">
        <input
          type="text"
          [(ngModel)]="contactSearchQuery"
          (input)="onContactSearch()"
          placeholder="Rechercher un contact..."
          class="whatsapp-contact-search-input"
        />
      </div>
      <div class="whatsapp-contact-list">
        <div
          *ngFor="let contact of getFilteredContactsForSharing()"
          (click)="selectContactForSharing(contact)"
          class="whatsapp-contact-item"
          [ngClass]="{ selected: selectedContactForSharing?.id === contact.id }"
        >
          <img
            [src]="contact.image || 'assets/images/default-avatar.png'"
            [alt]="contact.username"
          />
          <div class="whatsapp-contact-item-info">
            <div class="whatsapp-contact-item-name">{{ contact.username }}</div>
            <div class="whatsapp-contact-item-phone">
              {{ contact.phone || contact.email }}
            </div>
            <div
              class="whatsapp-contact-item-status"
              *ngIf="contact.isOnline !== undefined"
            >
              <i
                class="fas fa-circle"
                [ngClass]="{
                  online: contact.isOnline,
                  offline: !contact.isOnline
                }"
              ></i>
              {{ contact.isOnline ? "En ligne" : "Hors ligne" }}
            </div>
          </div>
          <div class="whatsapp-contact-item-actions">
            <button
              (click)="viewContactDetails(contact); $event.stopPropagation()"
              class="whatsapp-contact-view"
            >
              <i class="fas fa-eye"></i>
            </button>
          </div>
        </div>
      </div>
      <div class="whatsapp-contact-create">
        <button
          (click)="createNewContact()"
          class="whatsapp-contact-create-btn"
        >
          <i class="fas fa-plus"></i>
          Créer un nouveau contact
        </button>
      </div>
    </div>
    <div class="whatsapp-contact-actions">
      <button (click)="closeContactPicker()" class="whatsapp-contact-cancel">
        Annuler
      </button>
      <button
        (click)="shareContact()"
        class="whatsapp-contact-share"
        [disabled]="!selectedContactForSharing"
      >
        <i class="fas fa-share"></i>
        Partager le contact
      </button>
    </div>
  </div>

  <!-- Sélecteur d'émojis et autocollants ultra-avancé -->
  <div *ngIf="showEmojiPicker" class="whatsapp-emoji-picker">
    <div class="whatsapp-emoji-picker-header">
      <div class="whatsapp-emoji-tabs">
        <button
          *ngFor="let category of emojiCategories"
          (click)="selectEmojiCategory(category.id)"
          class="whatsapp-emoji-tab"
          [ngClass]="{ active: selectedEmojiCategory === category.id }"
          [title]="category.name"
        >
          <i [class]="category.icon"></i>
        </button>
        <button
          (click)="openStickerPicker()"
          class="whatsapp-emoji-tab"
          [ngClass]="{ active: showStickerPicker }"
          title="Autocollants"
        >
          <i class="fas fa-sticky-note"></i>
        </button>
        <button
          (click)="openGifPicker()"
          class="whatsapp-emoji-tab"
          [ngClass]="{ active: showGifPicker }"
          title="GIFs"
        >
          <i class="fas fa-gif"></i>
        </button>
      </div>
      <div class="whatsapp-emoji-search">
        <input
          type="text"
          [(ngModel)]="emojiSearchQuery"
          (input)="onEmojiSearch()"
          placeholder="Rechercher un emoji..."
          class="whatsapp-emoji-search-input"
        />
        <i class="fas fa-search"></i>
      </div>
    </div>

    <div class="whatsapp-emoji-picker-body">
      <!-- Émojis récents -->
      <div
        *ngIf="recentEmojis.length > 0 && selectedEmojiCategory === 'recent'"
        class="whatsapp-emoji-section"
      >
        <div class="whatsapp-emoji-section-title">Récemment utilisés</div>
        <div class="whatsapp-emoji-grid">
          <button
            *ngFor="let emoji of recentEmojis"
            (click)="insertEmoji(emoji.emoji)"
            class="whatsapp-emoji-btn"
            [title]="emoji.name"
          >
            {{ emoji.emoji }}
          </button>
        </div>
      </div>

      <!-- Émojis par catégorie -->
      <div
        *ngFor="let category of getFilteredEmojiCategories()"
        class="whatsapp-emoji-section"
      >
        <div class="whatsapp-emoji-section-title">{{ category.name }}</div>
        <div class="whatsapp-emoji-grid">
          <button
            *ngFor="let emoji of category.emojis"
            (click)="insertEmoji(emoji.emoji)"
            (mouseenter)="previewEmoji(emoji)"
            class="whatsapp-emoji-btn"
            [title]="emoji.name"
          >
            {{ emoji.emoji }}
          </button>
        </div>
      </div>

      <!-- Autocollants -->
      <div *ngIf="showStickerPicker" class="whatsapp-sticker-section">
        <div class="whatsapp-sticker-packs">
          <div
            *ngFor="let pack of stickerPacks"
            (click)="selectStickerPack(pack.id)"
            class="whatsapp-sticker-pack"
            [ngClass]="{ active: selectedStickerPack === pack.id }"
          >
            <img [src]="pack.preview" [alt]="pack.name" />
            <span>{{ pack.name }}</span>
          </div>
        </div>
        <div class="whatsapp-sticker-grid">
          <button
            *ngFor="let sticker of getSelectedStickerPack()?.stickers"
            (click)="insertSticker(sticker)"
            class="whatsapp-sticker-btn"
          >
            <img [src]="sticker.url" [alt]="sticker.name" />
          </button>
        </div>
      </div>

      <!-- GIFs -->
      <div *ngIf="showGifPicker" class="whatsapp-gif-section">
        <div class="whatsapp-gif-search">
          <input
            type="text"
            [(ngModel)]="gifSearchQuery"
            (input)="onGifSearch()"
            placeholder="Rechercher un GIF..."
            class="whatsapp-gif-search-input"
          />
        </div>
        <div class="whatsapp-gif-categories">
          <button
            *ngFor="let category of gifCategories"
            (click)="selectGifCategory(category)"
            class="whatsapp-gif-category"
            [ngClass]="{ active: selectedGifCategory === category }"
          >
            {{ category }}
          </button>
        </div>
        <div class="whatsapp-gif-grid">
          <div
            *ngFor="let gif of getFilteredGifs()"
            (click)="insertGif(gif)"
            class="whatsapp-gif-item"
          >
            <img [src]="gif.preview" [alt]="gif.title" />
            <div class="whatsapp-gif-overlay">
              <span>{{ gif.title }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="whatsapp-emoji-picker-footer">
      <div class="whatsapp-emoji-preview" *ngIf="previewedEmoji">
        <span class="whatsapp-emoji-preview-char">{{
          previewedEmoji.emoji
        }}</span>
        <span class="whatsapp-emoji-preview-name">{{
          previewedEmoji.name
        }}</span>
      </div>
      <div class="whatsapp-emoji-actions">
        <button (click)="manageStickerPacks()" class="whatsapp-emoji-action">
          <i class="fas fa-cog"></i>
          Gérer les packs
        </button>
        <button (click)="toggleEmojiPicker()" class="whatsapp-emoji-action">
          <i class="fas fa-times"></i>
          Fermer
        </button>
      </div>
    </div>
  </div>

  <!-- Outil de dessin avancé -->
  <div *ngIf="showDrawingTool" class="whatsapp-drawing-tool">
    <div class="whatsapp-drawing-header">
      <h3>Outil de dessin</h3>
      <button (click)="closeDrawingTool()" class="whatsapp-drawing-close">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="whatsapp-drawing-content">
      <div class="whatsapp-drawing-toolbar">
        <div class="whatsapp-drawing-tools">
          <button
            (click)="selectDrawingTool('pen')"
            class="whatsapp-drawing-tool-btn"
            [ngClass]="{ active: selectedDrawingTool === 'pen' }"
            title="Crayon"
          >
            <i class="fas fa-pen"></i>
          </button>
          <button
            (click)="selectDrawingTool('brush')"
            class="whatsapp-drawing-tool-btn"
            [ngClass]="{ active: selectedDrawingTool === 'brush' }"
            title="Pinceau"
          >
            <i class="fas fa-paint-brush"></i>
          </button>
          <button
            (click)="selectDrawingTool('eraser')"
            class="whatsapp-drawing-tool-btn"
            [ngClass]="{ active: selectedDrawingTool === 'eraser' }"
            title="Gomme"
          >
            <i class="fas fa-eraser"></i>
          </button>
          <button
            (click)="selectDrawingTool('line')"
            class="whatsapp-drawing-tool-btn"
            [ngClass]="{ active: selectedDrawingTool === 'line' }"
            title="Ligne"
          >
            <i class="fas fa-minus"></i>
          </button>
          <button
            (click)="selectDrawingTool('rectangle')"
            class="whatsapp-drawing-tool-btn"
            [ngClass]="{ active: selectedDrawingTool === 'rectangle' }"
            title="Rectangle"
          >
            <i class="fas fa-square"></i>
          </button>
          <button
            (click)="selectDrawingTool('circle')"
            class="whatsapp-drawing-tool-btn"
            [ngClass]="{ active: selectedDrawingTool === 'circle' }"
            title="Cercle"
          >
            <i class="fas fa-circle"></i>
          </button>
          <button
            (click)="selectDrawingTool('text')"
            class="whatsapp-drawing-tool-btn"
            [ngClass]="{ active: selectedDrawingTool === 'text' }"
            title="Texte"
          >
            <i class="fas fa-font"></i>
          </button>
        </div>

        <div class="whatsapp-drawing-colors">
          <div
            *ngFor="let color of drawingColors"
            (click)="selectDrawingColor(color)"
            class="whatsapp-drawing-color"
            [style.background-color]="color"
            [ngClass]="{ active: selectedDrawingColor === color }"
          ></div>
          <input
            type="color"
            [(ngModel)]="customDrawingColor"
            (change)="selectDrawingColor(customDrawingColor)"
            class="whatsapp-drawing-color-picker"
            title="Couleur personnalisée"
          />
        </div>

        <div class="whatsapp-drawing-size">
          <label>Taille :</label>
          <input
            type="range"
            [(ngModel)]="drawingSize"
            min="1"
            max="50"
            class="whatsapp-drawing-size-slider"
          />
          <span>{{ drawingSize }}px</span>
        </div>

        <div class="whatsapp-drawing-actions">
          <button
            (click)="undoDrawing()"
            class="whatsapp-drawing-action"
            title="Annuler"
          >
            <i class="fas fa-undo"></i>
          </button>
          <button
            (click)="redoDrawing()"
            class="whatsapp-drawing-action"
            title="Refaire"
          >
            <i class="fas fa-redo"></i>
          </button>
          <button
            (click)="clearDrawing()"
            class="whatsapp-drawing-action"
            title="Effacer tout"
          >
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </div>

      <div class="whatsapp-drawing-canvas-container">
        <canvas
          #drawingCanvas
          class="whatsapp-drawing-canvas"
          (mousedown)="startDrawing($event)"
          (mousemove)="draw($event)"
          (mouseup)="stopDrawing()"
          (touchstart)="startDrawing($event)"
          (touchmove)="draw($event)"
          (touchend)="stopDrawing()"
        ></canvas>
      </div>
    </div>
    <div class="whatsapp-drawing-footer">
      <button (click)="closeDrawingTool()" class="whatsapp-drawing-cancel">
        Annuler
      </button>
      <button (click)="saveDrawing()" class="whatsapp-drawing-save">
        <i class="fas fa-paper-plane"></i>
        Envoyer le dessin
      </button>
    </div>
  </div>

  <!-- Caméra et éditeur d'images -->
  <div *ngIf="showCamera" class="whatsapp-camera">
    <div class="whatsapp-camera-header">
      <button (click)="closeCamera()" class="whatsapp-camera-close">
        <i class="fas fa-times"></i>
      </button>
      <div class="whatsapp-camera-modes">
        <button
          (click)="setCameraMode('photo')"
          class="whatsapp-camera-mode"
          [ngClass]="{ active: cameraMode === 'photo' }"
        >
          Photo
        </button>
        <button
          (click)="setCameraMode('video')"
          class="whatsapp-camera-mode"
          [ngClass]="{ active: cameraMode === 'video' }"
        >
          Vidéo
        </button>
      </div>
      <div class="whatsapp-camera-settings">
        <button (click)="toggleCameraFlash()" class="whatsapp-camera-setting">
          <i [class]="flashEnabled ? 'fas fa-bolt' : 'fas fa-bolt-slash'"></i>
        </button>
        <button (click)="switchCamera()" class="whatsapp-camera-setting">
          <i class="fas fa-sync-alt"></i>
        </button>
      </div>
    </div>

    <div class="whatsapp-camera-content">
      <video #cameraVideo class="whatsapp-camera-video" autoplay muted></video>
      <canvas
        #cameraCanvas
        class="whatsapp-camera-canvas"
        style="display: none"
      ></canvas>

      <div class="whatsapp-camera-overlay">
        <div class="whatsapp-camera-grid" *ngIf="showCameraGrid">
          <div class="whatsapp-grid-line whatsapp-grid-vertical-1"></div>
          <div class="whatsapp-grid-line whatsapp-grid-vertical-2"></div>
          <div class="whatsapp-grid-line whatsapp-grid-horizontal-1"></div>
          <div class="whatsapp-grid-line whatsapp-grid-horizontal-2"></div>
        </div>

        <div
          class="whatsapp-camera-focus"
          *ngIf="showFocusIndicator"
          [style.left.px]="focusX"
          [style.top.px]="focusY"
        >
          <div class="whatsapp-focus-ring"></div>
        </div>
      </div>
    </div>

    <div class="whatsapp-camera-controls">
      <div class="whatsapp-camera-gallery">
        <button (click)="openGallery()" class="whatsapp-gallery-btn">
          <img
            [src]="lastCapturedImage || 'assets/images/gallery-icon.png'"
            alt="Galerie"
          />
        </button>
      </div>

      <div class="whatsapp-camera-capture">
        <button
          (click)="capturePhoto()"
          *ngIf="cameraMode === 'photo'"
          class="whatsapp-capture-btn whatsapp-photo-btn"
        >
          <div class="whatsapp-capture-ring"></div>
        </button>
        <button
          (mousedown)="startVideoRecording()"
          (mouseup)="stopVideoRecording()"
          (touchstart)="startVideoRecording()"
          (touchend)="stopVideoRecording()"
          *ngIf="cameraMode === 'video'"
          class="whatsapp-capture-btn whatsapp-video-btn"
          [ngClass]="{ recording: isRecordingVideo }"
        >
          <div
            class="whatsapp-capture-ring"
            [ngClass]="{ recording: isRecordingVideo }"
          ></div>
        </button>
      </div>

      <div class="whatsapp-camera-options">
        <button (click)="toggleCameraGrid()" class="whatsapp-camera-option">
          <i [class]="showCameraGrid ? 'fas fa-th' : 'fas fa-th-large'"></i>
        </button>
      </div>
    </div>

    <div class="whatsapp-camera-timer" *ngIf="isRecordingVideo">
      <i class="fas fa-circle" style="color: red"></i>
      <span>{{ formatRecordingTime(videoRecordingDuration) }}</span>
    </div>
  </div>

  <!-- Éditeur d'images avancé -->
  <div *ngIf="showImageEditor" class="whatsapp-image-editor">
    <div class="whatsapp-image-editor-header">
      <button (click)="closeImageEditor()" class="whatsapp-image-editor-close">
        <i class="fas fa-times"></i>
      </button>
      <h3>Éditer l'image</h3>
      <button (click)="saveEditedImage()" class="whatsapp-image-editor-save">
        <i class="fas fa-check"></i>
        Terminé
      </button>
    </div>

    <div class="whatsapp-image-editor-content">
      <div class="whatsapp-image-editor-canvas-container">
        <canvas
          #imageEditorCanvas
          class="whatsapp-image-editor-canvas"
        ></canvas>
        <div class="whatsapp-image-editor-overlay">
          <!-- Outils de recadrage -->
          <div *ngIf="imageEditorMode === 'crop'" class="whatsapp-crop-overlay">
            <div
              class="whatsapp-crop-area"
              [style.left.px]="cropArea.x"
              [style.top.px]="cropArea.y"
              [style.width.px]="cropArea.width"
              [style.height.px]="cropArea.height"
            >
              <div
                class="whatsapp-crop-handle whatsapp-crop-tl"
                (mousedown)="startCropResize('tl', $event)"
              ></div>
              <div
                class="whatsapp-crop-handle whatsapp-crop-tr"
                (mousedown)="startCropResize('tr', $event)"
              ></div>
              <div
                class="whatsapp-crop-handle whatsapp-crop-bl"
                (mousedown)="startCropResize('bl', $event)"
              ></div>
              <div
                class="whatsapp-crop-handle whatsapp-crop-br"
                (mousedown)="startCropResize('br', $event)"
              ></div>
            </div>
          </div>

          <!-- Outils de texte -->
          <div *ngIf="imageEditorMode === 'text'" class="whatsapp-text-overlay">
            <div
              *ngFor="let textElement of imageTextElements; let i = index"
              class="whatsapp-text-element"
              [style.left.px]="textElement.x"
              [style.top.px]="textElement.y"
              [style.color]="textElement.color"
              [style.font-size.px]="textElement.fontSize"
              [style.font-family]="textElement.fontFamily"
              (click)="editTextElement(i)"
            >
              {{ textElement.text }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="whatsapp-image-editor-toolbar">
      <div class="whatsapp-image-editor-modes">
        <button
          (click)="setImageEditorMode('crop')"
          class="whatsapp-image-editor-mode"
          [ngClass]="{ active: imageEditorMode === 'crop' }"
        >
          <i class="fas fa-crop"></i>
          <span>Recadrer</span>
        </button>
        <button
          (click)="setImageEditorMode('filter')"
          class="whatsapp-image-editor-mode"
          [ngClass]="{ active: imageEditorMode === 'filter' }"
        >
          <i class="fas fa-magic"></i>
          <span>Filtres</span>
        </button>
        <button
          (click)="setImageEditorMode('adjust')"
          class="whatsapp-image-editor-mode"
          [ngClass]="{ active: imageEditorMode === 'adjust' }"
        >
          <i class="fas fa-sliders-h"></i>
          <span>Ajuster</span>
        </button>
        <button
          (click)="setImageEditorMode('text')"
          class="whatsapp-image-editor-mode"
          [ngClass]="{ active: imageEditorMode === 'text' }"
        >
          <i class="fas fa-font"></i>
          <span>Texte</span>
        </button>
        <button
          (click)="setImageEditorMode('draw')"
          class="whatsapp-image-editor-mode"
          [ngClass]="{ active: imageEditorMode === 'draw' }"
        >
          <i class="fas fa-paint-brush"></i>
          <span>Dessiner</span>
        </button>
      </div>

      <!-- Options de recadrage -->
      <div *ngIf="imageEditorMode === 'crop'" class="whatsapp-crop-options">
        <button
          (click)="setCropRatio('free')"
          class="whatsapp-crop-ratio"
          [ngClass]="{ active: cropRatio === 'free' }"
        >
          Libre
        </button>
        <button
          (click)="setCropRatio('1:1')"
          class="whatsapp-crop-ratio"
          [ngClass]="{ active: cropRatio === '1:1' }"
        >
          1:1
        </button>
        <button
          (click)="setCropRatio('4:3')"
          class="whatsapp-crop-ratio"
          [ngClass]="{ active: cropRatio === '4:3' }"
        >
          4:3
        </button>
        <button
          (click)="setCropRatio('16:9')"
          class="whatsapp-crop-ratio"
          [ngClass]="{ active: cropRatio === '16:9' }"
        >
          16:9
        </button>
        <button (click)="rotateImage(90)" class="whatsapp-crop-action">
          <i class="fas fa-redo"></i>
          Rotation
        </button>
        <button (click)="flipImage('horizontal')" class="whatsapp-crop-action">
          <i class="fas fa-arrows-alt-h"></i>
          Miroir H
        </button>
        <button (click)="flipImage('vertical')" class="whatsapp-crop-action">
          <i class="fas fa-arrows-alt-v"></i>
          Miroir V
        </button>
      </div>

      <!-- Filtres -->
      <div *ngIf="imageEditorMode === 'filter'" class="whatsapp-filter-options">
        <div class="whatsapp-filter-grid">
          <button
            *ngFor="let filter of imageFilters"
            (click)="applyImageFilter(filter.name)"
            class="whatsapp-filter-btn"
            [ngClass]="{ active: selectedImageFilter === filter.name }"
          >
            <div
              class="whatsapp-filter-preview"
              [style.filter]="filter.css"
            ></div>
            <span>{{ filter.label }}</span>
          </button>
        </div>
      </div>

      <!-- Ajustements -->
      <div *ngIf="imageEditorMode === 'adjust'" class="whatsapp-adjust-options">
        <div class="whatsapp-adjust-slider">
          <label>Luminosité</label>
          <input
            type="range"
            [(ngModel)]="imageAdjustments.brightness"
            min="-100"
            max="100"
            (input)="updateImageAdjustments()"
          />
          <span>{{ imageAdjustments.brightness }}</span>
        </div>
        <div class="whatsapp-adjust-slider">
          <label>Contraste</label>
          <input
            type="range"
            [(ngModel)]="imageAdjustments.contrast"
            min="-100"
            max="100"
            (input)="updateImageAdjustments()"
          />
          <span>{{ imageAdjustments.contrast }}</span>
        </div>
        <div class="whatsapp-adjust-slider">
          <label>Saturation</label>
          <input
            type="range"
            [(ngModel)]="imageAdjustments.saturation"
            min="-100"
            max="100"
            (input)="updateImageAdjustments()"
          />
          <span>{{ imageAdjustments.saturation }}</span>
        </div>
        <div class="whatsapp-adjust-slider">
          <label>Teinte</label>
          <input
            type="range"
            [(ngModel)]="imageAdjustments.hue"
            min="-180"
            max="180"
            (input)="updateImageAdjustments()"
          />
          <span>{{ imageAdjustments.hue }}°</span>
        </div>
        <button (click)="resetImageAdjustments()" class="whatsapp-adjust-reset">
          <i class="fas fa-undo"></i>
          Réinitialiser
        </button>
      </div>

      <!-- Options de texte -->
      <div *ngIf="imageEditorMode === 'text'" class="whatsapp-text-options">
        <input
          type="text"
          [(ngModel)]="newTextContent"
          placeholder="Tapez votre texte..."
          class="whatsapp-text-input"
          (keydown.enter)="addTextToImage()"
        />
        <div class="whatsapp-text-controls">
          <select [(ngModel)]="textFontFamily" class="whatsapp-text-font">
            <option *ngFor="let font of availableFonts" [value]="font.value">
              {{ font.label }}
            </option>
          </select>
          <input
            type="range"
            [(ngModel)]="textFontSize"
            min="12"
            max="72"
            class="whatsapp-text-size"
          />
          <input
            type="color"
            [(ngModel)]="textColor"
            class="whatsapp-text-color"
          />
          <button (click)="addTextToImage()" class="whatsapp-text-add">
            <i class="fas fa-plus"></i>
            Ajouter
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Gestionnaire de fichiers avancé -->
  <div *ngIf="showFileManager" class="whatsapp-file-manager">
    <div class="whatsapp-file-manager-header">
      <h3>Gestionnaire de fichiers</h3>
      <div class="whatsapp-file-manager-actions">
        <button (click)="createNewFolder()" class="whatsapp-file-action">
          <i class="fas fa-folder-plus"></i>
          Nouveau dossier
        </button>
        <button (click)="uploadFiles()" class="whatsapp-file-action">
          <i class="fas fa-upload"></i>
          Télécharger
        </button>
        <button (click)="toggleFileView()" class="whatsapp-file-action">
          <i
            [class]="fileViewMode === 'grid' ? 'fas fa-list' : 'fas fa-th'"
          ></i>
          {{ fileViewMode === "grid" ? "Liste" : "Grille" }}
        </button>
      </div>
      <button (click)="closeFileManager()" class="whatsapp-file-manager-close">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="whatsapp-file-manager-content">
      <div class="whatsapp-file-manager-sidebar">
        <div class="whatsapp-file-tree">
          <div
            class="whatsapp-file-tree-item"
            *ngFor="let folder of fileFolders"
          >
            <div
              class="whatsapp-folder-item"
              (click)="selectFolder(folder)"
              [ngClass]="{ active: selectedFolder?.id === folder.id }"
            >
              <i
                class="fas fa-folder"
                [ngClass]="{ 'fa-folder-open': folder.expanded }"
              ></i>
              <span>{{ folder.name }}</span>
              <div class="whatsapp-folder-actions">
                <button
                  (click)="renameFolder(folder); $event.stopPropagation()"
                  title="Renommer"
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  (click)="deleteFolder(folder); $event.stopPropagation()"
                  title="Supprimer"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
            <div class="whatsapp-file-tree-children" *ngIf="folder.expanded">
              <div
                *ngFor="let subfolder of folder.children"
                class="whatsapp-subfolder-item"
                (click)="selectFolder(subfolder)"
              >
                <i class="fas fa-folder"></i>
                <span>{{ subfolder.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="whatsapp-file-stats">
          <div class="whatsapp-stat-item">
            <span class="whatsapp-stat-label">Espace utilisé</span>
            <div class="whatsapp-storage-bar">
              <div
                class="whatsapp-storage-used"
                [style.width.%]="getStorageUsagePercentage()"
              ></div>
            </div>
            <span class="whatsapp-stat-value"
              >{{ getStorageUsed() }} / {{ getStorageTotal() }}</span
            >
          </div>
          <div class="whatsapp-stat-item">
            <span class="whatsapp-stat-label">Fichiers</span>
            <span class="whatsapp-stat-value">{{ getTotalFilesCount() }}</span>
          </div>
        </div>
      </div>

      <div class="whatsapp-file-manager-main">
        <div class="whatsapp-file-breadcrumb">
          <button
            *ngFor="let crumb of fileBreadcrumbs; let i = index"
            (click)="navigateToFolder(crumb)"
            class="whatsapp-breadcrumb-item"
          >
            <i class="fas fa-folder" *ngIf="i > 0"></i>
            <i class="fas fa-home" *ngIf="i === 0"></i>
            <span>{{ crumb.name }}</span>
            <i
              class="fas fa-chevron-right"
              *ngIf="i < fileBreadcrumbs.length - 1"
            ></i>
          </button>
        </div>

        <div class="whatsapp-file-search">
          <input
            type="text"
            [(ngModel)]="fileSearchQuery"
            (input)="onFileSearch()"
            placeholder="Rechercher des fichiers..."
            class="whatsapp-file-search-input"
          />
          <div class="whatsapp-file-filters">
            <select
              [(ngModel)]="fileTypeFilter"
              (change)="applyFileFilters()"
              class="whatsapp-file-filter"
            >
              <option value="">Tous les types</option>
              <option value="image">Images</option>
              <option value="video">Vidéos</option>
              <option value="audio">Audio</option>
              <option value="document">Documents</option>
              <option value="archive">Archives</option>
            </select>
            <select
              [(ngModel)]="fileSortBy"
              (change)="sortFiles()"
              class="whatsapp-file-sort"
            >
              <option value="name">Nom</option>
              <option value="date">Date</option>
              <option value="size">Taille</option>
              <option value="type">Type</option>
            </select>
          </div>
        </div>

        <div
          class="whatsapp-file-list"
          [ngClass]="{
            'grid-view': fileViewMode === 'grid',
            'list-view': fileViewMode === 'list'
          }"
        >
          <div
            *ngFor="let file of getFilteredFiles()"
            class="whatsapp-file-item"
            [ngClass]="{ selected: selectedFiles.includes(file.id) }"
            (click)="toggleFileSelection(file)"
            (dblclick)="openFile(file)"
          >
            <div class="whatsapp-file-checkbox">
              <input
                type="checkbox"
                [checked]="selectedFiles.includes(file.id)"
                (change)="toggleFileSelection(file)"
              />
            </div>
            <div class="whatsapp-file-icon">
              <i [class]="getFileIcon(file)"></i>
              <img
                *ngIf="file.thumbnail"
                [src]="file.thumbnail"
                [alt]="file.name"
                class="whatsapp-file-thumbnail"
              />
            </div>
            <div class="whatsapp-file-info">
              <div class="whatsapp-file-name" [title]="file.name">
                {{ file.name }}
              </div>
              <div class="whatsapp-file-details">
                <span class="whatsapp-file-size">{{
                  formatFileSize(file.size)
                }}</span>
                <span class="whatsapp-file-date">{{
                  formatFileDate(file.modifiedAt)
                }}</span>
              </div>
            </div>
            <div class="whatsapp-file-actions">
              <button
                (click)="previewFile(file); $event.stopPropagation()"
                title="Aperçu"
              >
                <i class="fas fa-eye"></i>
              </button>
              <button
                (click)="downloadFile(file); $event.stopPropagation()"
                title="Télécharger"
              >
                <i class="fas fa-download"></i>
              </button>
              <button
                (click)="shareFile(file); $event.stopPropagation()"
                title="Partager"
              >
                <i class="fas fa-share"></i>
              </button>
              <button
                (click)="deleteFile(file); $event.stopPropagation()"
                title="Supprimer"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="whatsapp-file-manager-footer">
      <div
        class="whatsapp-selected-files-info"
        *ngIf="selectedFiles.length > 0"
      >
        <span>{{ selectedFiles.length }} fichier(s) sélectionné(s)</span>
        <div class="whatsapp-bulk-actions">
          <button
            (click)="downloadSelectedFiles()"
            class="whatsapp-bulk-action"
          >
            <i class="fas fa-download"></i>
            Télécharger
          </button>
          <button (click)="shareSelectedFiles()" class="whatsapp-bulk-action">
            <i class="fas fa-share"></i>
            Partager
          </button>
          <button (click)="deleteSelectedFiles()" class="whatsapp-bulk-action">
            <i class="fas fa-trash"></i>
            Supprimer
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Tableau de bord analytique -->
  <div *ngIf="showAnalyticsDashboard" class="whatsapp-analytics-dashboard">
    <div class="whatsapp-analytics-header">
      <h3>Tableau de bord analytique</h3>
      <div class="whatsapp-analytics-controls">
        <select
          [(ngModel)]="analyticsTimeRange"
          (change)="updateAnalytics()"
          class="whatsapp-analytics-range"
        >
          <option value="24h">Dernières 24h</option>
          <option value="7d">7 derniers jours</option>
          <option value="30d">30 derniers jours</option>
          <option value="90d">3 derniers mois</option>
          <option value="1y">Dernière année</option>
        </select>
        <button (click)="exportAnalytics()" class="whatsapp-analytics-export">
          <i class="fas fa-download"></i>
          Exporter
        </button>
      </div>
      <button
        (click)="closeAnalyticsDashboard()"
        class="whatsapp-analytics-close"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="whatsapp-analytics-content">
      <div class="whatsapp-analytics-overview">
        <div class="whatsapp-metric-card">
          <div class="whatsapp-metric-icon">
            <i class="fas fa-comments"></i>
          </div>
          <div class="whatsapp-metric-info">
            <div class="whatsapp-metric-value">
              {{ analyticsData.totalMessages }}
            </div>
            <div class="whatsapp-metric-label">Messages envoyés</div>
            <div
              class="whatsapp-metric-change"
              [ngClass]="{
                positive: analyticsData.messagesChange > 0,
                negative: analyticsData.messagesChange < 0
              }"
            >
              <i
                [class]="
                  analyticsData.messagesChange > 0
                    ? 'fas fa-arrow-up'
                    : 'fas fa-arrow-down'
                "
              ></i>
              {{ Math.abs(analyticsData.messagesChange) }}%
            </div>
          </div>
        </div>

        <div class="whatsapp-metric-card">
          <div class="whatsapp-metric-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="whatsapp-metric-info">
            <div class="whatsapp-metric-value">
              {{ analyticsData.activeUsers }}
            </div>
            <div class="whatsapp-metric-label">Utilisateurs actifs</div>
            <div
              class="whatsapp-metric-change"
              [ngClass]="{
                positive: analyticsData.usersChange > 0,
                negative: analyticsData.usersChange < 0
              }"
            >
              <i
                [class]="
                  analyticsData.usersChange > 0
                    ? 'fas fa-arrow-up'
                    : 'fas fa-arrow-down'
                "
              ></i>
              {{ Math.abs(analyticsData.usersChange) }}%
            </div>
          </div>
        </div>

        <div class="whatsapp-metric-card">
          <div class="whatsapp-metric-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="whatsapp-metric-info">
            <div class="whatsapp-metric-value">
              {{ analyticsData.avgResponseTime }}
            </div>
            <div class="whatsapp-metric-label">Temps de réponse moyen</div>
            <div
              class="whatsapp-metric-change"
              [ngClass]="{
                positive: analyticsData.responseTimeChange < 0,
                negative: analyticsData.responseTimeChange > 0
              }"
            >
              <i
                [class]="
                  analyticsData.responseTimeChange < 0
                    ? 'fas fa-arrow-down'
                    : 'fas fa-arrow-up'
                "
              ></i>
              {{ Math.abs(analyticsData.responseTimeChange) }}%
            </div>
          </div>
        </div>

        <div class="whatsapp-metric-card">
          <div class="whatsapp-metric-icon">
            <i class="fas fa-file"></i>
          </div>
          <div class="whatsapp-metric-info">
            <div class="whatsapp-metric-value">
              {{ analyticsData.filesShared }}
            </div>
            <div class="whatsapp-metric-label">Fichiers partagés</div>
            <div
              class="whatsapp-metric-change"
              [ngClass]="{
                positive: analyticsData.filesChange > 0,
                negative: analyticsData.filesChange < 0
              }"
            >
              <i
                [class]="
                  analyticsData.filesChange > 0
                    ? 'fas fa-arrow-up'
                    : 'fas fa-arrow-down'
                "
              ></i>
              {{ Math.abs(analyticsData.filesChange) }}%
            </div>
          </div>
        </div>
      </div>

      <div class="whatsapp-analytics-charts">
        <div class="whatsapp-chart-container">
          <h4>Activité des messages</h4>
          <canvas #messagesChart class="whatsapp-chart"></canvas>
        </div>

        <div class="whatsapp-chart-container">
          <h4>Types de contenu</h4>
          <canvas #contentChart class="whatsapp-chart"></canvas>
        </div>

        <div class="whatsapp-chart-container">
          <h4>Heures d'activité</h4>
          <canvas #activityChart class="whatsapp-chart"></canvas>
        </div>

        <div class="whatsapp-chart-container">
          <h4>Utilisateurs les plus actifs</h4>
          <div class="whatsapp-top-users">
            <div
              *ngFor="let user of analyticsData.topUsers; let i = index"
              class="whatsapp-top-user"
            >
              <div class="whatsapp-user-rank">{{ i + 1 }}</div>
              <img
                [src]="user.image || 'assets/images/default-avatar.png'"
                [alt]="user.username"
              />
              <div class="whatsapp-user-info">
                <div class="whatsapp-user-name">{{ user.username }}</div>
                <div class="whatsapp-user-messages">
                  {{ user.messageCount }} messages
                </div>
              </div>
              <div class="whatsapp-user-activity">
                <div
                  class="whatsapp-activity-bar"
                  [style.width.%]="
                    (user.messageCount /
                      analyticsData.topUsers[0].messageCount) *
                    100
                  "
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="whatsapp-analytics-details">
        <div class="whatsapp-analytics-section">
          <h4>Répartition par type de message</h4>
          <div class="whatsapp-message-types">
            <div
              *ngFor="let type of analyticsData.messageTypes"
              class="whatsapp-message-type"
            >
              <div class="whatsapp-type-icon">
                <i [class]="getMessageTypeIcon(type.type)"></i>
              </div>
              <div class="whatsapp-type-info">
                <div class="whatsapp-type-name">{{ type.name }}</div>
                <div class="whatsapp-type-count">
                  {{ type.count }} ({{ type.percentage }}%)
                </div>
              </div>
              <div class="whatsapp-type-bar">
                <div
                  class="whatsapp-type-progress"
                  [style.width.%]="type.percentage"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div class="whatsapp-analytics-section">
          <h4>Tendances de conversation</h4>
          <div class="whatsapp-conversation-trends">
            <div class="whatsapp-trend-item">
              <span class="whatsapp-trend-label"
                >Conversations les plus longues</span
              >
              <span class="whatsapp-trend-value"
                >{{ analyticsData.longestConversation }} messages</span
              >
            </div>
            <div class="whatsapp-trend-item">
              <span class="whatsapp-trend-label"
                >Durée moyenne de conversation</span
              >
              <span class="whatsapp-trend-value">{{
                analyticsData.avgConversationDuration
              }}</span>
            </div>
            <div class="whatsapp-trend-item">
              <span class="whatsapp-trend-label">Émojis les plus utilisés</span>
              <div class="whatsapp-top-emojis">
                <span
                  *ngFor="let emoji of analyticsData.topEmojis"
                  class="whatsapp-top-emoji"
                  [title]="emoji.name + ' (' + emoji.count + ' fois)'"
                >
                  {{ emoji.emoji }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Intégrations et webhooks -->
  <div *ngIf="showIntegrations" class="whatsapp-integrations">
    <div class="whatsapp-integrations-header">
      <h3>Intégrations et automatisations</h3>
      <button (click)="createNewIntegration()" class="whatsapp-integration-add">
        <i class="fas fa-plus"></i>
        Nouvelle intégration
      </button>
      <button (click)="closeIntegrations()" class="whatsapp-integrations-close">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="whatsapp-integrations-content">
      <div class="whatsapp-integrations-sidebar">
        <div class="whatsapp-integration-categories">
          <button
            *ngFor="let category of integrationCategories"
            (click)="selectIntegrationCategory(category)"
            class="whatsapp-integration-category"
            [ngClass]="{ active: selectedIntegrationCategory === category }"
          >
            <i [class]="getIntegrationCategoryIcon(category)"></i>
            <span>{{ category }}</span>
          </button>
        </div>
      </div>

      <div class="whatsapp-integrations-main">
        <div class="whatsapp-available-integrations">
          <h4>Intégrations disponibles</h4>
          <div class="whatsapp-integration-grid">
            <div
              *ngFor="let integration of getAvailableIntegrations()"
              class="whatsapp-integration-card"
            >
              <div class="whatsapp-integration-logo">
                <img [src]="integration.logo" [alt]="integration.name" />
              </div>
              <div class="whatsapp-integration-info">
                <h5>{{ integration.name }}</h5>
                <p>{{ integration.description }}</p>
                <div class="whatsapp-integration-features">
                  <span
                    *ngFor="let feature of integration.features"
                    class="whatsapp-integration-feature"
                  >
                    {{ feature }}
                  </span>
                </div>
              </div>
              <div class="whatsapp-integration-actions">
                <button
                  (click)="configureIntegration(integration)"
                  class="whatsapp-integration-configure"
                  *ngIf="!integration.configured"
                >
                  <i class="fas fa-cog"></i>
                  Configurer
                </button>
                <button
                  (click)="toggleIntegration(integration)"
                  class="whatsapp-integration-toggle"
                  *ngIf="integration.configured"
                  [ngClass]="{ active: integration.enabled }"
                >
                  <i
                    [class]="
                      integration.enabled
                        ? 'fas fa-toggle-on'
                        : 'fas fa-toggle-off'
                    "
                  ></i>
                  {{ integration.enabled ? "Activé" : "Désactivé" }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <div
          class="whatsapp-active-integrations"
          *ngIf="getActiveIntegrations().length > 0"
        >
          <h4>Intégrations actives</h4>
          <div class="whatsapp-active-integration-list">
            <div
              *ngFor="let integration of getActiveIntegrations()"
              class="whatsapp-active-integration"
            >
              <div class="whatsapp-integration-status">
                <div
                  class="whatsapp-status-indicator"
                  [ngClass]="{
                    online: integration.status === 'connected',
                    offline: integration.status === 'disconnected',
                    error: integration.status === 'error'
                  }"
                ></div>
                <span class="whatsapp-status-text">{{
                  getIntegrationStatusText(integration.status)
                }}</span>
              </div>
              <div class="whatsapp-integration-details">
                <img [src]="integration.logo" [alt]="integration.name" />
                <div class="whatsapp-integration-name">
                  {{ integration.name }}
                </div>
                <div class="whatsapp-integration-stats">
                  <span
                    >{{ integration.messagesProcessed }} messages traités</span
                  >
                  <span
                    >Dernière activité:
                    {{ formatLastActivity(integration.lastActivity) }}</span
                  >
                </div>
              </div>
              <div class="whatsapp-integration-controls">
                <button
                  (click)="viewIntegrationLogs(integration)"
                  title="Voir les logs"
                >
                  <i class="fas fa-list"></i>
                </button>
                <button (click)="editIntegration(integration)" title="Modifier">
                  <i class="fas fa-edit"></i>
                </button>
                <button (click)="testIntegration(integration)" title="Tester">
                  <i class="fas fa-play"></i>
                </button>
                <button
                  (click)="removeIntegration(integration)"
                  title="Supprimer"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="whatsapp-webhooks-section">
          <h4>Webhooks</h4>
          <div class="whatsapp-webhook-list">
            <div *ngFor="let webhook of webhooks" class="whatsapp-webhook-item">
              <div class="whatsapp-webhook-info">
                <div class="whatsapp-webhook-url">{{ webhook.url }}</div>
                <div class="whatsapp-webhook-events">
                  <span
                    *ngFor="let event of webhook.events"
                    class="whatsapp-webhook-event"
                    >{{ event }}</span
                  >
                </div>
              </div>
              <div class="whatsapp-webhook-status">
                <div
                  class="whatsapp-status-indicator"
                  [ngClass]="{
                    online: webhook.status === 'active',
                    offline: webhook.status === 'inactive',
                    error: webhook.status === 'error'
                  }"
                ></div>
                <span>{{ webhook.status }}</span>
              </div>
              <div class="whatsapp-webhook-actions">
                <button (click)="editWebhook(webhook)" title="Modifier">
                  <i class="fas fa-edit"></i>
                </button>
                <button (click)="testWebhook(webhook)" title="Tester">
                  <i class="fas fa-play"></i>
                </button>
                <button (click)="deleteWebhook(webhook)" title="Supprimer">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
          <button (click)="createWebhook()" class="whatsapp-webhook-add">
            <i class="fas fa-plus"></i>
            Ajouter un webhook
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Overlay global pour les modales -->
  <div
    *ngIf="hasActiveModal()"
    class="whatsapp-modal-backdrop"
    (click)="closeActiveModal()"
  ></div>
</div>
