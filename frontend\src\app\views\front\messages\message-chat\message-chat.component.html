<!-- Chat WhatsApp Moderne avec Tailwind CSS -->
<div
  class="flex flex-col h-screen bg-gradient-to-br from-gray-50 to-green-50 dark:from-gray-900 dark:to-gray-800"
>
  <!-- En-tête -->
  <header
    class="flex items-center px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm"
  >
    <!-- Bouton retour -->
    <button
      (click)="goBackToConversations()"
      class="p-2 mr-3 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
    >
      <i class="fas fa-arrow-left text-gray-600 dark:text-gray-300"></i>
    </button>

    <!-- Info utilisateur -->
    <div class="flex items-center flex-1 min-w-0">
      <div class="relative mr-3">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          class="w-10 h-10 rounded-full object-cover border-2 border-green-500 cursor-pointer hover:scale-105 transition-transform"
          (click)="openUserProfile(otherParticipant?.id!)"
        />
        <div
          *ngIf="otherParticipant?.isOnline"
          class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse"
        ></div>
      </div>

      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-gray-900 dark:text-white truncate">
          {{ otherParticipant?.username || "Utilisateur" }}
        </h3>
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <div
            *ngIf="isUserTyping"
            class="flex items-center gap-1 text-green-600"
          >
            <span>En train d'écrire</span>
            <div class="flex gap-1">
              <div
                class="w-1 h-1 bg-green-600 rounded-full animate-bounce"
              ></div>
              <div
                class="w-1 h-1 bg-green-600 rounded-full animate-bounce"
                style="animation-delay: 0.1s"
              ></div>
              <div
                class="w-1 h-1 bg-green-600 rounded-full animate-bounce"
                style="animation-delay: 0.2s"
              ></div>
            </div>
          </div>
          <span *ngIf="!isUserTyping">
            {{
              otherParticipant?.isOnline
                ? "En ligne"
                : formatLastActive(otherParticipant?.lastActive)
            }}
          </span>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex items-center gap-2">
      <button
        (click)="startVideoCall()"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
        title="Appel vidéo"
      >
        <i class="fas fa-video"></i>
      </button>
      <button
        (click)="startVoiceCall()"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
        title="Appel vocal"
      >
        <i class="fas fa-phone"></i>
      </button>
      <button
        (click)="toggleSearch()"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
        [class.bg-green-100]="searchMode"
        [class.text-green-600]="searchMode"
        title="Rechercher"
      >
        <i class="fas fa-search"></i>
      </button>
      <button
        (click)="toggleMainMenu()"
        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
        title="Menu"
      >
        <i class="fas fa-ellipsis-v"></i>
      </button>

      <!-- Bouton de test temporaire -->
      <button
        (click)="testAddMessage()"
        class="p-2 rounded-full hover:bg-red-100 dark:hover:bg-red-700 text-red-600 dark:text-red-300 transition-colors"
        title="Test: Ajouter message"
      >
        <i class="fas fa-plus"></i>
      </button>
    </div>
  </header>

  <!-- Zone de messages -->
  <main
    class="flex-1 overflow-y-auto p-4 space-y-4"
    #messagesContainer
    (scroll)="onScroll($event)"
  >
    <!-- Chargement -->
    <div
      *ngIf="isLoading"
      class="flex flex-col items-center justify-center py-8"
    >
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-4"
      ></div>
      <span class="text-gray-500 dark:text-gray-400"
        >Chargement des messages...</span
      >
    </div>

    <!-- État vide -->
    <div
      *ngIf="!isLoading && messages.length === 0"
      class="flex flex-col items-center justify-center py-16"
    >
      <div class="text-6xl text-gray-300 dark:text-gray-600 mb-4">
        <i class="fas fa-comments"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
        Aucun message
      </h3>
      <p class="text-gray-500 dark:text-gray-400 text-center">
        Commencez votre conversation avec {{ otherParticipant?.username }}
      </p>
    </div>

    <!-- Messages -->
    <div *ngIf="!isLoading && messages.length > 0" class="space-y-2">
      <ng-container
        *ngFor="
          let message of messages;
          let i = index;
          trackBy: trackByMessageId
        "
      >
        <!-- Séparateur de date -->
        <div
          *ngIf="shouldShowDateSeparator(i)"
          class="flex justify-center my-4"
        >
          <div
            class="bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-sm"
          >
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatDateSeparator(message.timestamp) }}
            </span>
          </div>
        </div>

        <!-- Message -->
        <div
          class="flex"
          [class.justify-end]="message.sender?.id === currentUserId"
          [class.justify-start]="message.sender?.id !== currentUserId"
          [id]="'message-' + message.id"
          (click)="onMessageClick(message, $event)"
          (contextmenu)="onMessageContextMenu(message, $event)"
        >
          <!-- Avatar pour les autres -->
          <div
            *ngIf="message.sender?.id !== currentUserId && shouldShowAvatar(i)"
            class="mr-2 flex-shrink-0"
          >
            <img
              [src]="
                message.sender?.image || 'assets/images/default-avatar.png'
              "
              [alt]="message.sender?.username"
              class="w-8 h-8 rounded-full object-cover cursor-pointer hover:scale-105 transition-transform"
              (click)="openUserProfile(message.sender?.id!)"
            />
          </div>

          <!-- Bulle de message -->
          <div
            class="max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm relative group"
            [class.bg-green-500]="message.sender?.id === currentUserId"
            [class.text-white]="message.sender?.id === currentUserId"
            [class.bg-white]="message.sender?.id !== currentUserId"
            [class.text-gray-900]="message.sender?.id !== currentUserId"
            [class.dark:bg-gray-700]="message.sender?.id !== currentUserId"
            [class.dark:text-white]="message.sender?.id !== currentUserId"
          >
            <!-- Nom expéditeur (groupes) -->
            <div
              *ngIf="
                isGroupConversation() &&
                message.sender?.id !== currentUserId &&
                shouldShowSenderName(i)
              "
              class="text-xs font-semibold mb-1 opacity-75"
              [style.color]="getUserColor(message.sender?.id!)"
            >
              {{ message.sender?.username }}
            </div>

            <!-- Contenu texte -->
            <div *ngIf="getMessageType(message) === 'text'" class="break-words">
              <div [innerHTML]="formatMessageContent(message.content)"></div>
            </div>

            <!-- Image -->
            <div *ngIf="hasImage(message)" class="space-y-2">
              <div
                class="relative cursor-pointer rounded-lg overflow-hidden"
                (click)="openImageViewer(message)"
              >
                <img
                  [src]="getImageUrl(message)"
                  [alt]="message.content || 'Image'"
                  class="max-w-full h-auto rounded-lg hover:opacity-90 transition-opacity"
                />
                <div
                  class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all flex items-center justify-center"
                >
                  <i
                    class="fas fa-expand text-white opacity-0 group-hover:opacity-100 transition-opacity"
                  ></i>
                </div>
              </div>
              <div
                *ngIf="message.content"
                class="text-sm"
                [innerHTML]="formatMessageContent(message.content)"
              ></div>
            </div>

            <!-- Fichier -->
            <div
              *ngIf="hasFile(message)"
              class="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors"
              (click)="downloadFile(message)"
            >
              <div class="text-2xl text-gray-500 dark:text-gray-400">
                <i [class]="getFileIcon(message)"></i>
              </div>
              <div class="flex-1 min-w-0">
                <div class="font-medium text-sm truncate">
                  {{ getFileName(message) }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ getFileSize(message) }}
                </div>
              </div>
              <button
                class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-400 transition-colors"
              >
                <i class="fas fa-download text-sm"></i>
              </button>
            </div>

            <!-- Métadonnées -->
            <div
              class="flex items-center justify-end gap-1 mt-1 text-xs opacity-75"
            >
              <span>{{ formatMessageTime(message.timestamp) }}</span>
              <div
                *ngIf="message.sender?.id === currentUserId"
                class="flex items-center"
              >
                <i
                  class="fas fa-clock"
                  *ngIf="message.status === 'SENDING'"
                  title="Envoi en cours"
                ></i>
                <i
                  class="fas fa-check"
                  *ngIf="message.status === 'SENT'"
                  title="Envoyé"
                ></i>
                <i
                  class="fas fa-check-double"
                  *ngIf="message.status === 'DELIVERED'"
                  title="Livré"
                ></i>
                <i
                  class="fas fa-check-double text-blue-400"
                  *ngIf="message.status === 'READ'"
                  title="Lu"
                ></i>
              </div>
            </div>

            <!-- Réactions -->
            <div
              *ngIf="message.reactions && message.reactions.length > 0"
              class="flex gap-1 mt-2"
            >
              <button
                *ngFor="let reaction of message.reactions"
                (click)="toggleReaction(message.id!, reaction.emoji)"
                class="flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                [class.bg-green-100]="
                  hasUserReacted(reaction, currentUserId || '')
                "
                [class.text-green-600]="
                  hasUserReacted(reaction, currentUserId || '')
                "
              >
                <span>{{ reaction.emoji }}</span>
                <span>1</span>
              </button>
            </div>
          </div>
        </div>
      </ng-container>

      <!-- Indicateur de frappe (seulement quand l'autre personne tape) -->
      <div *ngIf="otherUserIsTyping" class="flex items-start gap-2">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          [alt]="otherParticipant?.username"
          class="w-8 h-8 rounded-full object-cover"
        />
        <div class="bg-white dark:bg-gray-700 px-4 py-2 rounded-2xl shadow-sm">
          <div class="flex gap-1">
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div
              class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
              style="animation-delay: 0.1s"
            ></div>
            <div
              class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
              style="animation-delay: 0.2s"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Zone d'input -->
  <footer
    class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4"
  >
    <form
      [formGroup]="messageForm"
      (ngSubmit)="sendMessage()"
      class="flex items-end gap-3"
    >
      <!-- Actions gauche -->
      <div class="flex gap-2">
        <button
          type="button"
          (click)="toggleEmojiPicker()"
          class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
          [class.bg-green-100]="showEmojiPicker"
          [class.text-green-600]="showEmojiPicker"
          title="Émojis"
        >
          <i class="fas fa-smile"></i>
        </button>
        <button
          type="button"
          (click)="toggleAttachmentMenu()"
          class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors"
          [class.bg-green-100]="showAttachmentMenu"
          [class.text-green-600]="showAttachmentMenu"
          title="Joindre un fichier"
        >
          <i class="fas fa-paperclip"></i>
        </button>
      </div>

      <!-- Champ de saisie -->
      <div class="flex-1">
        <textarea
          formControlName="content"
          #messageTextarea
          placeholder="Tapez votre message..."
          class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          [class.opacity-50]="isInputDisabled()"
          [class.cursor-not-allowed]="isInputDisabled()"
          (input)="onInputChange($event)"
          (keydown)="onInputKeyDown($event)"
          (focus)="onInputFocus()"
          (blur)="onInputBlur()"
          rows="1"
          maxlength="4096"
          autocomplete="off"
          spellcheck="true"
        >
        </textarea>
      </div>

      <!-- Actions droite -->
      <div class="flex gap-2">
        <!-- Enregistrement vocal -->
        <button
          *ngIf="!messageForm.get('content')?.value?.trim()"
          type="button"
          (mousedown)="startVoiceRecording()"
          (mouseup)="stopVoiceRecording()"
          (mouseleave)="cancelVoiceRecording()"
          class="p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors"
          [class.bg-red-500]="isRecordingVoice"
          [class.hover:bg-red-600]="isRecordingVoice"
          [class.animate-pulse]="isRecordingVoice"
          title="Maintenir pour enregistrer"
        >
          <i class="fas fa-microphone"></i>
        </button>

        <!-- Bouton d'envoi -->
        <button
          *ngIf="messageForm.get('content')?.value?.trim()"
          type="button"
          (click)="sendMessage()"
          class="p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50"
          [disabled]="isSendingMessage"
          title="Envoyer"
        >
          <i class="fas fa-paper-plane" *ngIf="!isSendingMessage"></i>
          <i class="fas fa-spinner fa-spin" *ngIf="isSendingMessage"></i>
        </button>
      </div>
    </form>
  </footer>

  <!-- Sélecteur d'émojis -->
  <div
    *ngIf="showEmojiPicker"
    class="absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50"
  >
    <div class="p-4">
      <div class="flex gap-2 mb-4 overflow-x-auto">
        <button
          *ngFor="let category of emojiCategories"
          (click)="selectEmojiCategory(category)"
          class="px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0"
          [class.bg-green-100]="selectedEmojiCategory === category"
          [class.text-green-600]="selectedEmojiCategory === category"
          [class.hover:bg-gray-100]="selectedEmojiCategory !== category"
          [class.dark:hover:bg-gray-700]="selectedEmojiCategory !== category"
        >
          {{ category.icon }}
        </button>
      </div>
      <div class="grid grid-cols-8 gap-2 max-h-48 overflow-y-auto">
        <button
          *ngFor="let emoji of getEmojisForCategory(selectedEmojiCategory)"
          (click)="insertEmoji(emoji)"
          class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl"
          [title]="emoji.name"
        >
          {{ emoji.emoji }}
        </button>
      </div>
    </div>
  </div>

  <!-- Menu des pièces jointes -->
  <div
    *ngIf="showAttachmentMenu"
    class="absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50"
  >
    <div class="p-4">
      <div class="grid grid-cols-2 gap-3">
        <button
          (click)="triggerFileInput('image')"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-image text-blue-600 dark:text-blue-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Photos</span
          >
        </button>
        <button
          (click)="triggerFileInput('video')"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-video text-purple-600 dark:text-purple-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Vidéos</span
          >
        </button>
        <button
          (click)="triggerFileInput('document')"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-file text-orange-600 dark:text-orange-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Documents</span
          >
        </button>
        <button
          (click)="openCamera()"
          class="flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div
            class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-camera text-green-600 dark:text-green-400"></i>
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Caméra</span
          >
        </button>
      </div>
    </div>
  </div>

  <!-- Input caché pour fichiers -->
  <input
    #fileInput
    type="file"
    class="hidden"
    (change)="onFileSelected($event)"
    [accept]="getFileAcceptTypes()"
    multiple
  />

  <!-- Overlay pour fermer les menus -->
  <div
    *ngIf="showEmojiPicker || showAttachmentMenu"
    class="fixed inset-0 bg-black bg-opacity-25 z-40"
    (click)="closeAllMenus()"
  ></div>
</div>

<!-- Interface d'appel WebRTC -->
<app-call-interface
  [isVisible]="isInCall"
  [activeCall]="activeCall"
  [callType]="callType"
  [otherParticipant]="otherParticipant"
  (callEnded)="endCall()"
  (callAccepted)="onCallAccepted($event)"
  (callRejected)="onCallRejected()"
></app-call-interface>
