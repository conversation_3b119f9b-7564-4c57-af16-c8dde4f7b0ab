/* ========== CSS ULTRA-AVANCÉ POUR TOUTES LES FONCTIONNALITÉS ========== */

/* === VARIABLES GLOBALES === */
:root {
  --whatsapp-primary: #00d4aa;
  --whatsapp-primary-dark: #00bfa5;
  --whatsapp-secondary: #128c7e;
  --whatsapp-accent: #25d366;
  --whatsapp-bg-light: #f0f2f5;
  --whatsapp-bg-dark: #0b141a;
  --whatsapp-surface: #ffffff;
  --whatsapp-surface-dark: #1f2937;
  --whatsapp-text: #111b21;
  --whatsapp-text-secondary: #667781;
  --whatsapp-border: #e9edef;
  --whatsapp-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --whatsapp-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.15);
  --whatsapp-radius: 8px;
  --whatsapp-radius-lg: 12px;
  --whatsapp-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --whatsapp-animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* === CONTENEUR PRINCIPAL === */
.whatsapp-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(
    135deg,
    var(--whatsapp-bg-light) 0%,
    #e8f5e8 100%
  );
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

/* === EN-TÊTE ULTRA-AVANCÉ === */
.whatsapp-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: var(--whatsapp-surface);
  border-bottom: 1px solid var(--whatsapp-border);
  box-shadow: var(--whatsapp-shadow);
  position: relative;
  z-index: 100;
}

.whatsapp-header-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.whatsapp-header-avatar {
  position: relative;
  margin-right: 12px;
}

.whatsapp-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--whatsapp-primary);
  transition: var(--whatsapp-transition);
}

.whatsapp-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: var(--whatsapp-accent);
  border: 2px solid var(--whatsapp-surface);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.whatsapp-header-details {
  flex: 1;
  min-width: 0;
}

.whatsapp-header-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--whatsapp-text);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.whatsapp-header-status {
  font-size: 13px;
  color: var(--whatsapp-text-secondary);
  margin: 2px 0 0 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.whatsapp-typing-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--whatsapp-primary);
}

.whatsapp-typing-dot {
  width: 4px;
  height: 4px;
  background: var(--whatsapp-primary);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.whatsapp-typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.whatsapp-typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.whatsapp-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.whatsapp-header-action {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--whatsapp-text-secondary);
  transition: var(--whatsapp-transition);
  position: relative;
}

.whatsapp-header-action:hover {
  background: var(--whatsapp-bg-light);
  color: var(--whatsapp-primary);
  transform: scale(1.05);
}

.whatsapp-header-action.active {
  background: var(--whatsapp-primary);
  color: white;
}

.whatsapp-action-badge {
  position: absolute;
  top: 6px;
  right: 6px;
  min-width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border-radius: 9px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: bounce 0.5s var(--whatsapp-animation-bounce);
}

@keyframes bounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* === BARRE DE RECHERCHE AVANCÉE === */
.whatsapp-search-container {
  padding: 12px 16px;
  background: var(--whatsapp-surface);
  border-bottom: 1px solid var(--whatsapp-border);
  transform: translateY(-100%);
  transition: var(--whatsapp-transition);
}

.whatsapp-search-container.active {
  transform: translateY(0);
}

.whatsapp-search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--whatsapp-bg-light);
  border-radius: var(--whatsapp-radius-lg);
  padding: 8px 12px;
  gap: 8px;
}

.whatsapp-search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 14px;
  color: var(--whatsapp-text);
}

.whatsapp-search-input::placeholder {
  color: var(--whatsapp-text-secondary);
}

.whatsapp-search-actions {
  display: flex;
  gap: 4px;
}

.whatsapp-search-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--whatsapp-text-secondary);
  transition: var(--whatsapp-transition);
}

.whatsapp-search-btn:hover {
  background: var(--whatsapp-primary);
  color: white;
}

.whatsapp-search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--whatsapp-surface);
  border-radius: 0 0 var(--whatsapp-radius-lg) var(--whatsapp-radius-lg);
  box-shadow: var(--whatsapp-shadow-lg);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
}

.whatsapp-search-result {
  padding: 12px 16px;
  border-bottom: 1px solid var(--whatsapp-border);
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-search-result:hover {
  background: var(--whatsapp-bg-light);
}

.whatsapp-search-result:last-child {
  border-bottom: none;
}

.whatsapp-search-highlight {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
}

/* === MESSAGES ÉPINGLÉS === */
.whatsapp-pinned-messages {
  background: linear-gradient(135deg, #e8f5e8, #f0f9ff);
  border-bottom: 1px solid var(--whatsapp-border);
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: var(--whatsapp-transition);
}

.whatsapp-pinned-icon {
  color: var(--whatsapp-primary);
  font-size: 16px;
  transform: rotate(45deg);
}

.whatsapp-pinned-content {
  flex: 1;
  min-width: 0;
}

.whatsapp-pinned-text {
  font-size: 14px;
  color: var(--whatsapp-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.whatsapp-pinned-count {
  font-size: 12px;
  color: var(--whatsapp-text-secondary);
  margin-top: 2px;
}

.whatsapp-pinned-actions {
  display: flex;
  gap: 4px;
}

.whatsapp-pinned-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--whatsapp-text-secondary);
  transition: var(--whatsapp-transition);
}

.whatsapp-pinned-btn:hover {
  background: var(--whatsapp-primary);
  color: white;
  transform: scale(1.1);
}

/* === ZONE DE MESSAGES ULTRA-COMPLÈTE === */
.whatsapp-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: linear-gradient(135deg, #e5ddd5 0%, #f0f2f5 100%);
  position: relative;
  scroll-behavior: smooth;
}

.whatsapp-messages-container::-webkit-scrollbar {
  width: 6px;
}

.whatsapp-messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.whatsapp-messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.whatsapp-messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* === ÉTAT VIDE === */
.whatsapp-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px 20px;
}

.whatsapp-empty-content {
  max-width: 400px;
}

.whatsapp-empty-icon {
  font-size: 64px;
  color: var(--whatsapp-text-secondary);
  margin-bottom: 24px;
  opacity: 0.5;
}

.whatsapp-empty-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--whatsapp-text);
  margin: 0 0 12px 0;
}

.whatsapp-empty-description {
  font-size: 16px;
  color: var(--whatsapp-text-secondary);
  margin: 0 0 32px 0;
  line-height: 1.5;
}

.whatsapp-empty-suggestions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.whatsapp-quick-message-btn {
  padding: 12px 24px;
  border: 2px solid var(--whatsapp-primary);
  background: transparent;
  color: var(--whatsapp-primary);
  border-radius: var(--whatsapp-radius-lg);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--whatsapp-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.whatsapp-quick-message-btn:hover {
  background: var(--whatsapp-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

/* === CHARGEMENT === */
.whatsapp-loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.whatsapp-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.whatsapp-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--whatsapp-bg-light);
  border-top: 3px solid var(--whatsapp-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.whatsapp-loading-text {
  font-size: 14px;
  color: var(--whatsapp-text-secondary);
}

/* === BOUTON CHARGER PLUS === */
.whatsapp-load-more-container {
  display: flex;
  justify-content: center;
  padding: 16px;
}

.whatsapp-load-more-btn {
  padding: 8px 16px;
  border: 1px solid var(--whatsapp-border);
  background: var(--whatsapp-surface);
  color: var(--whatsapp-primary);
  border-radius: var(--whatsapp-radius);
  font-size: 13px;
  cursor: pointer;
  transition: var(--whatsapp-transition);
  display: flex;
  align-items: center;
  gap: 8px;
}

.whatsapp-load-more-btn:hover {
  background: var(--whatsapp-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--whatsapp-shadow);
}

.whatsapp-load-more-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* === SÉPARATEURS DE DATE === */
.whatsapp-date-separator {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.whatsapp-date-separator-content {
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 12px;
  border-radius: var(--whatsapp-radius);
  font-size: 12px;
  color: var(--whatsapp-text-secondary);
  box-shadow: var(--whatsapp-shadow);
  backdrop-filter: blur(10px);
}

/* === MESSAGES SYSTÈME === */
.whatsapp-system-message {
  display: flex;
  justify-content: center;
  margin: 12px 0;
}

.whatsapp-system-content {
  background: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: var(--whatsapp-radius-lg);
  font-size: 13px;
  color: var(--whatsapp-text-secondary);
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: var(--whatsapp-shadow);
  backdrop-filter: blur(10px);
}

/* === WRAPPER DE MESSAGE === */
.whatsapp-message-wrapper {
  display: flex;
  margin-bottom: 8px;
  padding: 4px 8px;
  border-radius: var(--whatsapp-radius);
  transition: var(--whatsapp-transition);
  position: relative;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.whatsapp-message-wrapper.own-message {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.whatsapp-message-wrapper.other-message {
  flex-direction: row;
  justify-content: flex-start;
}

.whatsapp-message-wrapper.highlighted {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  animation: highlight 1s ease-in-out;
}

@keyframes highlight {
  0%,
  100% {
    background: transparent;
  }
  50% {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  }
}

.whatsapp-message-wrapper.selected {
  background: rgba(0, 212, 170, 0.1);
  border: 2px solid var(--whatsapp-primary);
}

.whatsapp-message-wrapper.search-result {
  background: rgba(255, 235, 59, 0.2);
  border-left: 4px solid #ffeb3b;
}

.whatsapp-message-wrapper.pinned::before {
  content: "📌";
  position: absolute;
  top: -8px;
  right: 8px;
  font-size: 12px;
  z-index: 10;
}

.whatsapp-message-wrapper.forwarded::before {
  content: "↗️";
  position: absolute;
  top: -8px;
  left: 8px;
  font-size: 12px;
  z-index: 10;
}

/* === CHECKBOX DE SÉLECTION === */
.whatsapp-message-checkbox {
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
  margin-right: 8px;
}

.whatsapp-checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid var(--whatsapp-border);
  border-radius: 4px;
  background: var(--whatsapp-surface);
  cursor: pointer;
  transition: var(--whatsapp-transition);
  appearance: none;
  position: relative;
}

.whatsapp-checkbox:checked {
  background: var(--whatsapp-primary);
  border-color: var(--whatsapp-primary);
}

.whatsapp-checkbox:checked::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* === AVATAR DE MESSAGE === */
.whatsapp-message-avatar {
  position: relative;
  margin-right: 8px;
  margin-top: 4px;
}

.whatsapp-avatar-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-avatar-img:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* === CONTENU DE MESSAGE === */
.whatsapp-message-content {
  flex: 1;
  min-width: 0;
  max-width: 65%;
  position: relative;
}

.whatsapp-message-wrapper.own-message .whatsapp-message-content {
  margin-left: auto;
}

.whatsapp-message-wrapper.other-message .whatsapp-message-content {
  margin-right: auto;
}

/* === NOM DE L'EXPÉDITEUR === */
.whatsapp-sender-name {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 4px;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-sender-name:hover {
  text-decoration: underline;
}

/* === INDICATEUR DE TRANSFERT === */
.whatsapp-forwarded-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--whatsapp-text-secondary);
  margin-bottom: 4px;
  font-style: italic;
}

/* === MESSAGE DE RÉPONSE === */
.whatsapp-reply-message {
  background: rgba(0, 0, 0, 0.05);
  border-left: 4px solid var(--whatsapp-primary);
  border-radius: var(--whatsapp-radius);
  padding: 8px 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: var(--whatsapp-transition);
  position: relative;
  overflow: hidden;
}

.whatsapp-reply-message:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: translateX(4px);
}

.whatsapp-reply-border {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--whatsapp-primary);
}

.whatsapp-reply-content {
  position: relative;
  z-index: 1;
}

.whatsapp-reply-sender {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 2px;
}

.whatsapp-reply-text {
  font-size: 14px;
  color: var(--whatsapp-text-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
}

.whatsapp-reply-media {
  display: flex;
  align-items: center;
  gap: 4px;
  font-style: italic;
}

/* === BULLE DE MESSAGE === */
.whatsapp-message-bubble {
  background: var(--whatsapp-surface);
  border-radius: 12px 12px 12px 4px;
  padding: 8px 12px;
  box-shadow: var(--whatsapp-shadow);
  position: relative;
  word-wrap: break-word;
  transition: var(--whatsapp-transition);
}

.whatsapp-message-wrapper.own-message .whatsapp-message-bubble {
  background: linear-gradient(
    135deg,
    var(--whatsapp-primary),
    var(--whatsapp-primary-dark)
  );
  color: white;
  border-radius: 12px 12px 4px 12px;
}

.whatsapp-message-wrapper.other-message .whatsapp-message-bubble {
  background: var(--whatsapp-surface);
  color: var(--whatsapp-text);
  border-radius: 12px 12px 12px 4px;
}

.whatsapp-message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: var(--whatsapp-shadow-lg);
}

/* === MODE ÉDITION === */
.whatsapp-edit-mode {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--whatsapp-radius);
  padding: 8px;
  border: 2px solid var(--whatsapp-primary);
}

.whatsapp-edit-input-container {
  position: relative;
}

.whatsapp-edit-input {
  width: 100%;
  border: 1px solid var(--whatsapp-border);
  border-radius: var(--whatsapp-radius);
  padding: 8px 12px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 40px;
  outline: none;
  transition: var(--whatsapp-transition);
}

.whatsapp-edit-input:focus {
  border-color: var(--whatsapp-primary);
  box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

.whatsapp-edit-char-count {
  position: absolute;
  bottom: 4px;
  right: 8px;
  font-size: 11px;
  color: var(--whatsapp-text-secondary);
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 4px;
}

.whatsapp-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.whatsapp-edit-cancel,
.whatsapp-edit-save {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-edit-cancel {
  background: #ff6b6b;
  color: white;
}

.whatsapp-edit-cancel:hover {
  background: #ee5a52;
  transform: scale(1.1);
}

.whatsapp-edit-save {
  background: var(--whatsapp-primary);
  color: white;
}

.whatsapp-edit-save:hover {
  background: var(--whatsapp-primary-dark);
  transform: scale(1.1);
}

.whatsapp-edit-save:disabled {
  background: var(--whatsapp-text-secondary);
  cursor: not-allowed;
  transform: none;
}

/* === CONTENU TEXTE === */
.whatsapp-text-content {
  line-height: 1.4;
}

.whatsapp-text-body {
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;
}

/* === CONTENU IMAGE === */
.whatsapp-image-content {
  max-width: 300px;
}

.whatsapp-image-container {
  position: relative;
  border-radius: var(--whatsapp-radius);
  overflow: hidden;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-image-container:hover {
  transform: scale(1.02);
}

.whatsapp-message-image {
  width: 100%;
  height: auto;
  display: block;
  transition: var(--whatsapp-transition);
}

.whatsapp-image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: var(--whatsapp-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.whatsapp-image-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.whatsapp-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--whatsapp-transition);
}

.whatsapp-image-container:hover .whatsapp-image-overlay {
  opacity: 1;
}

.whatsapp-image-overlay i {
  color: white;
  font-size: 24px;
}

.whatsapp-image-info {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
}

.whatsapp-image-caption {
  padding: 8px 0 0 0;
  font-size: 14px;
  line-height: 1.4;
}

/* === CONTENU VOCAL === */
.whatsapp-voice-content {
  min-width: 200px;
  max-width: 300px;
}

.whatsapp-voice-player {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px;
}

.whatsapp-voice-play-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: var(--whatsapp-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--whatsapp-transition);
  flex-shrink: 0;
}

.whatsapp-voice-play-btn:hover {
  background: var(--whatsapp-primary-dark);
  transform: scale(1.1);
}

.whatsapp-voice-play-btn.playing {
  background: #ff6b6b;
}

.whatsapp-voice-play-btn.loading {
  background: var(--whatsapp-text-secondary);
  cursor: not-allowed;
}

.whatsapp-voice-waveform {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 2px;
  height: 32px;
  cursor: pointer;
  padding: 4px;
  border-radius: var(--whatsapp-radius);
  transition: var(--whatsapp-transition);
}

.whatsapp-voice-waveform:hover {
  background: rgba(0, 0, 0, 0.05);
}

.whatsapp-voice-bar {
  width: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  transition: var(--whatsapp-transition);
  min-height: 4px;
}

.whatsapp-message-wrapper.own-message .whatsapp-voice-bar {
  background: rgba(255, 255, 255, 0.4);
}

.whatsapp-message-wrapper.other-message .whatsapp-voice-bar {
  background: var(--whatsapp-border);
}

.whatsapp-voice-bar.active {
  background: white;
}

.whatsapp-message-wrapper.other-message .whatsapp-voice-bar.active {
  background: var(--whatsapp-primary);
}

.whatsapp-voice-bar.played {
  background: rgba(255, 255, 255, 0.6);
}

.whatsapp-message-wrapper.other-message .whatsapp-voice-bar.played {
  background: var(--whatsapp-primary-dark);
}

.whatsapp-voice-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  flex-shrink: 0;
}

.whatsapp-voice-duration {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.whatsapp-message-wrapper.other-message .whatsapp-voice-duration {
  color: var(--whatsapp-text-secondary);
}

.whatsapp-voice-speed-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 10px;
  color: white;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-message-wrapper.other-message .whatsapp-voice-speed-btn {
  background: var(--whatsapp-bg-light);
  color: var(--whatsapp-text);
}

.whatsapp-voice-speed-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* === CONTENU FICHIER === */
.whatsapp-file-content {
  min-width: 250px;
  max-width: 350px;
}

.whatsapp-file-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--whatsapp-radius);
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-message-wrapper.other-message .whatsapp-file-container {
  background: var(--whatsapp-bg-light);
}

.whatsapp-file-container:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.whatsapp-message-wrapper.other-message .whatsapp-file-container:hover {
  background: var(--whatsapp-border);
}

.whatsapp-file-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--whatsapp-radius);
  background: var(--whatsapp-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.whatsapp-file-info {
  flex: 1;
  min-width: 0;
}

.whatsapp-file-name {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.whatsapp-message-wrapper.other-message .whatsapp-file-name {
  color: var(--whatsapp-text);
}

.whatsapp-file-details {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.whatsapp-message-wrapper.other-message .whatsapp-file-details {
  color: var(--whatsapp-text-secondary);
}

.whatsapp-file-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.whatsapp-file-download,
.whatsapp-file-preview {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-message-wrapper.other-message .whatsapp-file-download,
.whatsapp-message-wrapper.other-message .whatsapp-file-preview {
  background: var(--whatsapp-primary);
}

.whatsapp-file-download:hover,
.whatsapp-file-preview:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.whatsapp-file-progress {
  margin-top: 8px;
  padding: 0 12px;
}

.whatsapp-progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.whatsapp-message-wrapper.other-message .whatsapp-progress-bar {
  background: var(--whatsapp-border);
}

.whatsapp-progress-fill {
  height: 100%;
  background: white;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.whatsapp-message-wrapper.other-message .whatsapp-progress-fill {
  background: var(--whatsapp-primary);
}

.whatsapp-progress-text {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4px;
  text-align: center;
}

.whatsapp-message-wrapper.other-message .whatsapp-progress-text {
  color: var(--whatsapp-text-secondary);
}

/* === CONTENU VIDÉO === */
.whatsapp-video-content {
  max-width: 300px;
}

.whatsapp-video-container {
  position: relative;
  border-radius: var(--whatsapp-radius);
  overflow: hidden;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-video-container:hover {
  transform: scale(1.02);
}

.whatsapp-message-video {
  width: 100%;
  height: auto;
  display: block;
}

.whatsapp-video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--whatsapp-transition);
}

.whatsapp-video-container:hover .whatsapp-video-overlay {
  opacity: 1;
}

.whatsapp-video-play-btn {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: var(--whatsapp-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-video-play-btn:hover {
  background: white;
  transform: scale(1.1);
}

.whatsapp-video-info {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
}

.whatsapp-video-caption {
  padding: 8px 0 0 0;
  font-size: 14px;
  line-height: 1.4;
}

/* === CONTENU LOCALISATION === */
.whatsapp-location-content {
  max-width: 300px;
}

.whatsapp-location-container {
  border-radius: var(--whatsapp-radius);
  overflow: hidden;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-location-container:hover {
  transform: scale(1.02);
}

.whatsapp-location-map {
  position: relative;
  height: 150px;
  background: var(--whatsapp-bg-light);
}

.whatsapp-location-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-location-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--whatsapp-primary);
  font-size: 32px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.whatsapp-location-info {
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.whatsapp-message-wrapper.other-message .whatsapp-location-info {
  background: var(--whatsapp-bg-light);
}

.whatsapp-location-name {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
}

.whatsapp-message-wrapper.other-message .whatsapp-location-name {
  color: var(--whatsapp-text);
}

.whatsapp-location-address {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.3;
}

.whatsapp-message-wrapper.other-message .whatsapp-location-address {
  color: var(--whatsapp-text-secondary);
}

/* === CONTENU CONTACT === */
.whatsapp-contact-content {
  min-width: 250px;
  max-width: 300px;
}

.whatsapp-contact-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--whatsapp-radius);
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-message-wrapper.other-message .whatsapp-contact-container {
  background: var(--whatsapp-bg-light);
}

.whatsapp-contact-container:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.whatsapp-message-wrapper.other-message .whatsapp-contact-container:hover {
  background: var(--whatsapp-border);
}

.whatsapp-contact-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.whatsapp-contact-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-contact-info {
  flex: 1;
  min-width: 0;
}

.whatsapp-contact-name {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.whatsapp-message-wrapper.other-message .whatsapp-contact-name {
  color: var(--whatsapp-text);
}

.whatsapp-contact-phone {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.whatsapp-message-wrapper.other-message .whatsapp-contact-phone {
  color: var(--whatsapp-text-secondary);
}

.whatsapp-contact-actions {
  flex-shrink: 0;
}

.whatsapp-contact-add {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--whatsapp-transition);
}

.whatsapp-message-wrapper.other-message .whatsapp-contact-add {
  background: var(--whatsapp-primary);
}

.whatsapp-contact-add:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}
