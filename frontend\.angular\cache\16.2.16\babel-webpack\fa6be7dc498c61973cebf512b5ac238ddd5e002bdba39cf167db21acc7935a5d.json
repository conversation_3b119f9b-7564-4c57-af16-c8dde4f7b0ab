{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nexport let MessageChatComponent = class MessageChatComponent {\n  constructor(fb, route, MessageService, authUserService, toastService, cdr) {\n    this.fb = fb;\n    this.route = route;\n    this.MessageService = MessageService;\n    this.authUserService = authUserService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    // === DONNÉES PRINCIPALES ===\n    this.conversation = null;\n    this.messages = [];\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    // === ÉTATS DE L'INTERFACE ===\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n    this.searchQuery = '';\n    this.searchResults = [];\n    // === ENREGISTREMENT VOCAL ===\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    // === APPELS ===\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // === ÉMOJIS ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    // === PAGINATION ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    // === AUTRES ÉTATS ===\n    this.isTyping = false;\n    this.typingTimeout = null;\n    this.subscriptions = new Subscription();\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  ngOnInit() {\n    this.initializeComponent();\n  }\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n  }\n  loadCurrentUser() {\n    try {\n      const user = this.authUserService.getCurrentUser();\n      if (user) {\n        this.currentUserId = user.id || null;\n        this.currentUsername = user.username || 'You';\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement de l'utilisateur:\", error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  loadConversation() {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n    this.isLoading = true;\n    this.MessageService.getConversation(conversationId, this.currentUserId).subscribe({\n      next: conversation => {\n        this.conversation = conversation;\n        this.loadMessages();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError('Erreur lors du chargement de la conversation');\n        this.isLoading = false;\n      }\n    });\n  }\n  loadMessages() {\n    if (!this.conversation?.id) return;\n    this.MessageService.getMessages(this.conversation.id, undefined, undefined, this.currentPage, this.MAX_MESSAGES_TO_LOAD, this.currentUserId).subscribe({\n      next: messages => {\n        this.messages = messages || [];\n        this.isLoading = false;\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoading = false;\n      }\n    });\n  }\n  setupSubscriptions() {\n    // Abonnements GraphQL pour les mises à jour en temps réel\n    // À implémenter selon vos besoins\n  }\n  // === ENVOI DE MESSAGES ===\n  sendMessage() {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n    const receiverId = this.conversation.participants?.find(p => p.id !== this.currentUserId)?.id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({\n      next: message => {\n        this.messages.push(message);\n        this.messageForm.reset();\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n      }\n    });\n  }\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n    for (let file of files) {\n      this.uploadFile(file);\n    }\n  }\n  uploadFile(file) {\n    const receiverId = this.conversation?.participants?.find(p => p.id !== this.currentUserId)?.id;\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n    this.MessageService.sendFileMessage(receiverId, file, this.conversation.id).subscribe({\n      next: message => {\n        this.messages.push(message);\n        this.scrollToBottom();\n        this.toastService.showSuccess('Fichier envoyé avec succès');\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'envoi du fichier:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n      }\n    });\n  }\n  // === ENREGISTREMENT VOCAL ===\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          }\n        });\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: 'audio/webm;codecs=opus'\n        });\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          _this.cdr.detectChanges();\n        }, 1000);\n        _this.mediaRecorder.ondataavailable = event => {\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          _this.processRecordedAudio();\n        };\n        _this.mediaRecorder.start(100);\n        _this.toastService.showSuccess('Enregistrement vocal démarré');\n      } catch (error) {\n        console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n        _this.toastService.showError(\"Impossible d'accéder au microphone\");\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this2.audioChunks.length === 0) {\n          _this2.toastService.showWarning('Aucun audio enregistré');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: 'audio/webm;codecs=opus'\n        });\n        if (_this2.voiceRecordingDuration < 1) {\n          _this2.toastService.showWarning('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n          type: 'audio/webm;codecs=opus'\n        });\n        yield _this2.sendVoiceMessage(audioFile);\n        _this2.toastService.showSuccess('Message vocal envoyé');\n      } catch (error) {\n        console.error(\"Erreur lors du traitement de l'audio:\", error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n      } finally {\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this3.conversation?.participants?.find(p => p.id !== _this3.currentUserId)?.id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this3.MessageService.sendFileMessage(receiverId, audioFile, _this3.conversation.id).subscribe({\n          next: message => {\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === SYSTÈME D'ÉMOJIS ===\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + (emoji.emoji || emoji);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.showEmojiPicker = false;\n    setTimeout(() => {\n      const textarea = document.querySelector('textarea[formControlName=\"content\"]');\n      if (textarea) {\n        textarea.focus();\n      }\n    }, 100);\n  }\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    this.showAttachmentMenu = false;\n  }\n  // === APPELS VIDÉO/AUDIO ===\n  startCall(type) {\n    this.callType = type;\n    this.isInCall = true;\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n    this.toastService.showSuccess(`Appel ${type === 'VIDEO' ? 'vidéo' : 'audio'} démarré`);\n  }\n  endCall() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.toastService.showSuccess('Appel terminé');\n  }\n  startVideoCall() {\n    this.startCall('VIDEO');\n  }\n  startVoiceCall() {\n    this.startCall('AUDIO');\n  }\n  // === RECHERCHE ===\n  toggleSearch() {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n  searchMessages() {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  // === PAGINATION ===\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.hasMoreMessages) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    this.MessageService.getMessages(this.conversation.id, undefined, undefined, this.currentPage, this.MAX_MESSAGES_TO_LOAD, this.currentUserId).subscribe({\n      next: messages => {\n        if (messages && messages.length > 0) {\n          this.messages = [...messages, ...this.messages];\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.isLoadingMore = false;\n      }\n    });\n  }\n  // === GÉOLOCALISATION ===\n  shareLocation() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const position = yield _this4.getCurrentPosition();\n        const {\n          latitude,\n          longitude\n        } = position.coords;\n        const locationMessage = {\n          type: 'location',\n          latitude,\n          longitude,\n          address: yield _this4.getAddressFromCoordinates(latitude, longitude),\n          mapUrl: `https://maps.google.com/maps?q=${latitude},${longitude}&z=15`,\n          timestamp: new Date()\n        };\n        yield _this4.sendLocationMessage(locationMessage);\n        _this4.toastService.showSuccess('Position partagée avec succès');\n      } catch (error) {\n        console.error('Erreur lors du partage de localisation:', error);\n        _this4.toastService.showError(\"Impossible d'obtenir votre position\");\n      }\n    })();\n  }\n  getCurrentPosition() {\n    return new Promise((resolve, reject) => {\n      navigator.geolocation.getCurrentPosition(resolve, reject, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 60000\n      });\n    });\n  }\n  getAddressFromCoordinates(lat, lng) {\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);\n        if (response.ok) {\n          const data = yield response.json();\n          return data.display_name || `${lat}, ${lng}`;\n        }\n      } catch (error) {\n        console.error('Erreur lors du géocodage inverse:', error);\n      }\n      return `${lat}, ${lng}`;\n    })();\n  }\n  sendLocationMessage(locationData) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this5.conversation?.participants?.find(p => p.id !== _this5.currentUserId)?.id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this5.MessageService.sendMessage(receiverId, `📍 Position partagée: ${locationData.address}`, undefined, 'TEXT', _this5.conversation.id).subscribe({\n          next: message => {\n            _this5.messages.push(message);\n            _this5.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message de localisation:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  // === PARTAGE DE CONTACTS ===\n  shareContact() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const contact = yield _this6.selectContact();\n        if (contact) {\n          yield _this6.sendContactMessage(contact);\n          _this6.toastService.showSuccess('Contact partagé avec succès');\n        }\n      } catch (error) {\n        console.error('Erreur lors du partage de contact:', error);\n        _this6.toastService.showError('Erreur lors du partage du contact');\n      }\n    })();\n  }\n  selectContact() {\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        setTimeout(() => {\n          resolve({\n            name: 'John Doe',\n            phone: '+33 6 12 34 56 78',\n            email: '<EMAIL>',\n            avatar: 'assets/images/default-avatar.png'\n          });\n        }, 1000);\n      });\n    })();\n  }\n  sendContactMessage(contactData) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const receiverId = _this7.conversation?.participants?.find(p => p.id !== _this7.currentUserId)?.id;\n      if (!receiverId) {\n        throw new Error('Destinataire introuvable');\n      }\n      return new Promise((resolve, reject) => {\n        _this7.MessageService.sendMessage(receiverId, `👤 Contact partagé: ${contactData.name}\\n📞 ${contactData.phone}\\n📧 ${contactData.email}`, undefined, 'TEXT', _this7.conversation.id).subscribe({\n          next: message => {\n            _this7.messages.push(message);\n            _this7.scrollToBottom();\n            resolve();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message de contact:\", error);\n            reject(error);\n          }\n        });\n      });\n    })();\n  }\n  // === UTILITAIRES ===\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  // === GESTION DES ERREURS ===\n  handleError(error, context) {\n    console.error(`Erreur dans ${context}:`, error);\n    let message = \"Une erreur inattendue s'est produite\";\n    if (error?.message) {\n      message = error.message;\n    } else if (typeof error === 'string') {\n      message = error;\n    }\n    this.toastService.showError(message);\n  }\n  // === NETTOYAGE ===\n  ngOnDestroy() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    this.subscriptions.unsubscribe();\n  }\n};\n__decorate([ViewChild('messagesContainer')], MessageChatComponent.prototype, \"messagesContainer\", void 0);\n__decorate([ViewChild('fileInput', {\n  static: false\n})], MessageChatComponent.prototype, \"fileInput\", void 0);\nMessageChatComponent = __decorate([Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html'\n})], MessageChatComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "Validators", "Subscription", "MessageChatComponent", "constructor", "fb", "route", "MessageService", "authUserService", "toastService", "cdr", "conversation", "messages", "currentUserId", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "isRecordingVoice", "voiceRecordingDuration", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "isInCall", "callType", "callDuration", "callTimer", "emojiCategories", "id", "name", "icon", "emojis", "emoji", "selectedEmojiCategory", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "typingTimeout", "subscriptions", "messageForm", "group", "content", "required", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "initializeComponent", "loadCurrentUser", "loadConversation", "setupSubscriptions", "user", "getCurrentUser", "username", "error", "console", "conversationId", "snapshot", "paramMap", "get", "showError", "getConversation", "subscribe", "next", "loadMessages", "getMessages", "undefined", "scrollToBottom", "sendMessage", "valid", "value", "trim", "receiverId", "participants", "find", "p", "message", "push", "reset", "onFileSelected", "event", "files", "target", "length", "file", "uploadFile", "sendFileMessage", "showSuccess", "startVoiceRecording", "_this", "_asyncToGenerator", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "MediaRecorder", "mimeType", "setInterval", "detectChanges", "ondataavailable", "data", "size", "onstop", "processRecordedAudio", "start", "cancelVoiceRecording", "stopVoiceRecording", "state", "stop", "getTracks", "for<PERSON>ach", "track", "clearInterval", "_this2", "showWarning", "audioBlob", "Blob", "type", "audioFile", "File", "Date", "now", "sendVoiceMessage", "_this3", "Error", "Promise", "resolve", "reject", "formatRecordingDuration", "duration", "minutes", "Math", "floor", "seconds", "toString", "padStart", "getEmojisForCategory", "category", "selectEmojiCategory", "insert<PERSON><PERSON><PERSON>", "currentC<PERSON>nt", "newContent", "patchValue", "setTimeout", "textarea", "document", "querySelector", "focus", "toggleEmojiPicker", "startCall", "endCall", "startVideoCall", "startVoiceCall", "toggleSearch", "searchMessages", "filter", "toLowerCase", "includes", "sender", "loadMoreMessages", "shareLocation", "_this4", "position", "getCurrentPosition", "latitude", "longitude", "coords", "locationMessage", "address", "getAddressFromCoordinates", "mapUrl", "timestamp", "sendLocationMessage", "geolocation", "enableHighAccuracy", "timeout", "maximumAge", "lat", "lng", "response", "fetch", "ok", "json", "display_name", "locationData", "_this5", "shareContact", "_this6", "contact", "selectContact", "sendContactMessage", "phone", "email", "avatar", "contactData", "_this7", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "formatFileSize", "bytes", "k", "sizes", "i", "log", "parseFloat", "pow", "toFixed", "formatDuration", "hours", "secs", "getFileAcceptTypes", "closeAllMenus", "toggleAttachmentMenu", "triggerFileInput", "fileInput", "click", "handleError", "context", "ngOnDestroy", "clearTimeout", "unsubscribe", "__decorate", "static", "selector", "templateUrl"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts"], "sourcesContent": ["import {\n  <PERSON>mpo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../../services/message.service';\nimport { AuthService } from '../../../../services/auth.service';\nimport { ToastService } from '../../../../services/toast.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent implements OnInit, OnDestroy {\n  // === RÉFÉRENCES DOM ===\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === DONNÉES PRINCIPALES ===\n  conversation: any = null;\n  messages: any[] = [];\n  currentUserId: string | null = null;\n  currentUsername = 'You';\n  messageForm: FormGroup;\n\n  // === ÉTATS DE L'INTERFACE ===\n  isLoading = false;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  showEmojiPicker = false;\n  showAttachmentMenu = false;\n  showSearch = false;\n  searchQuery = '';\n  searchResults: any[] = [];\n\n  // === ENREGISTREMENT VOCAL ===\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n\n  // === APPELS ===\n  isInCall = false;\n  callType: CallType | null = null;\n  callDuration = 0;\n  private callTimer: any = null;\n\n  // === ÉMOJIS ===\n  emojiCategories: any[] = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n      ],\n    },\n  ];\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  // === PAGINATION ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n\n  // === AUTRES ÉTATS ===\n  isTyping = false;\n  private typingTimeout: any = null;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private MessageService: MessageService,\n    private authUserService: AuthUserService,\n    private toastService: ToastService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  ngOnInit(): void {\n    this.initializeComponent();\n  }\n\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      const user = this.authUserService.getCurrentUser();\n      if (user) {\n        this.currentUserId = user.id || null;\n        this.currentUsername = user.username || 'You';\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement de l'utilisateur:\", error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  private loadConversation(): void {\n    const conversationId = this.route.snapshot.paramMap.get('id');\n    if (!conversationId) {\n      this.toastService.showError('ID de conversation manquant');\n      return;\n    }\n\n    this.isLoading = true;\n    this.MessageService.getConversation(\n      conversationId,\n      this.currentUserId!\n    ).subscribe({\n      next: (conversation) => {\n        this.conversation = conversation;\n        this.loadMessages();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors du chargement de la conversation'\n        );\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private loadMessages(): void {\n    if (!this.conversation?.id) return;\n\n    this.MessageService.getMessages(\n      this.conversation.id,\n      undefined,\n      undefined,\n      this.currentPage,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentUserId!\n    ).subscribe({\n      next: (messages) => {\n        this.messages = messages || [];\n        this.isLoading = false;\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.toastService.showError('Erreur lors du chargement des messages');\n        this.isLoading = false;\n      },\n    });\n  }\n\n  private setupSubscriptions(): void {\n    // Abonnements GraphQL pour les mises à jour en temps réel\n    // À implémenter selon vos besoins\n  }\n\n  // === ENVOI DE MESSAGES ===\n  sendMessage(): void {\n    if (!this.messageForm.valid || !this.conversation?.id) return;\n\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content) return;\n\n    const receiverId = this.conversation.participants?.find(\n      (p: any) => p.id !== this.currentUserId\n    )?.id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    this.MessageService.sendMessage(\n      receiverId,\n      content,\n      undefined,\n      'TEXT' as any,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        this.messages.push(message);\n        this.messageForm.reset();\n        this.scrollToBottom();\n      },\n      error: (error: any) => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n      },\n    });\n  }\n\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event: any): void {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n\n    for (let file of files) {\n      this.uploadFile(file);\n    }\n  }\n\n  private uploadFile(file: File): void {\n    const receiverId = this.conversation?.participants?.find(\n      (p: any) => p.id !== this.currentUserId\n    )?.id;\n\n    if (!receiverId) {\n      this.toastService.showError('Destinataire introuvable');\n      return;\n    }\n\n    this.MessageService.sendFileMessage(\n      receiverId,\n      file,\n      this.conversation.id\n    ).subscribe({\n      next: (message: any) => {\n        this.messages.push(message);\n        this.scrollToBottom();\n        this.toastService.showSuccess('Fichier envoyé avec succès');\n      },\n      error: (error: any) => {\n        console.error(\"Erreur lors de l'envoi du fichier:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\n      },\n    });\n  }\n\n  // === ENREGISTREMENT VOCAL ===\n  async startVoiceRecording(): Promise<void> {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n        },\n      });\n\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus',\n      });\n\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        this.cdr.detectChanges();\n      }, 1000);\n\n      this.mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      this.mediaRecorder.onstop = () => {\n        this.processRecordedAudio();\n      };\n\n      this.mediaRecorder.start(100);\n      this.toastService.showSuccess('Enregistrement vocal démarré');\n    } catch (error) {\n      console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n      this.toastService.showError(\"Impossible d'accéder au microphone\");\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    try {\n      if (this.audioChunks.length === 0) {\n        this.toastService.showWarning('Aucun audio enregistré');\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      const audioBlob = new Blob(this.audioChunks, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      if (this.voiceRecordingDuration < 1) {\n        this.toastService.showWarning(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      await this.sendVoiceMessage(audioFile);\n      this.toastService.showSuccess('Message vocal envoyé');\n    } catch (error) {\n      console.error(\"Erreur lors du traitement de l'audio:\", error);\n      this.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n    } finally {\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    const receiverId = this.conversation?.participants?.find(\n      (p: any) => p.id !== this.currentUserId\n    )?.id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendFileMessage(\n        receiverId,\n        audioFile,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === SYSTÈME D'ÉMOJIS ===\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + (emoji.emoji || emoji);\n    this.messageForm.patchValue({ content: newContent });\n\n    this.showEmojiPicker = false;\n\n    setTimeout(() => {\n      const textarea = document.querySelector(\n        'textarea[formControlName=\"content\"]'\n      ) as HTMLTextAreaElement;\n      if (textarea) {\n        textarea.focus();\n      }\n    }, 100);\n  }\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    this.showAttachmentMenu = false;\n  }\n\n  // === APPELS VIDÉO/AUDIO ===\n  startCall(type: CallType): void {\n    this.callType = type;\n    this.isInCall = true;\n    this.callDuration = 0;\n\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n      this.cdr.detectChanges();\n    }, 1000);\n\n    this.toastService.showSuccess(\n      `Appel ${type === 'VIDEO' ? 'vidéo' : 'audio'} démarré`\n    );\n  }\n\n  endCall(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n\n    this.isInCall = false;\n    this.callType = null;\n    this.callDuration = 0;\n    this.toastService.showSuccess('Appel terminé');\n  }\n\n  startVideoCall(): void {\n    this.startCall('VIDEO' as CallType);\n  }\n\n  startVoiceCall(): void {\n    this.startCall('AUDIO' as CallType);\n  }\n\n  // === RECHERCHE ===\n  toggleSearch(): void {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n\n  searchMessages(): void {\n    if (!this.searchQuery.trim()) {\n      this.searchResults = [];\n      return;\n    }\n\n    this.searchResults = this.messages.filter(\n      (message) =>\n        message.content\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase()) ||\n        message.sender?.username\n          ?.toLowerCase()\n          .includes(this.searchQuery.toLowerCase())\n    );\n  }\n\n  // === PAGINATION ===\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.hasMoreMessages) return;\n\n    this.isLoadingMore = true;\n    this.currentPage++;\n\n    this.MessageService.getMessages(\n      this.conversation.id,\n      undefined,\n      undefined,\n      this.currentPage,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentUserId!\n    ).subscribe({\n      next: (messages) => {\n        if (messages && messages.length > 0) {\n          this.messages = [...messages, ...this.messages];\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des messages:', error);\n        this.isLoadingMore = false;\n      },\n    });\n  }\n\n  // === GÉOLOCALISATION ===\n  async shareLocation(): Promise<void> {\n    try {\n      const position = await this.getCurrentPosition();\n      const { latitude, longitude } = position.coords;\n\n      const locationMessage = {\n        type: 'location',\n        latitude,\n        longitude,\n        address: await this.getAddressFromCoordinates(latitude, longitude),\n        mapUrl: `https://maps.google.com/maps?q=${latitude},${longitude}&z=15`,\n        timestamp: new Date(),\n      };\n\n      await this.sendLocationMessage(locationMessage);\n      this.toastService.showSuccess('Position partagée avec succès');\n    } catch (error) {\n      console.error('Erreur lors du partage de localisation:', error);\n      this.toastService.showError(\"Impossible d'obtenir votre position\");\n    }\n  }\n\n  private getCurrentPosition(): Promise<GeolocationPosition> {\n    return new Promise((resolve, reject) => {\n      navigator.geolocation.getCurrentPosition(resolve, reject, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 60000,\n      });\n    });\n  }\n\n  private async getAddressFromCoordinates(\n    lat: number,\n    lng: number\n  ): Promise<string> {\n    try {\n      const response = await fetch(\n        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        return data.display_name || `${lat}, ${lng}`;\n      }\n    } catch (error) {\n      console.error('Erreur lors du géocodage inverse:', error);\n    }\n\n    return `${lat}, ${lng}`;\n  }\n\n  private async sendLocationMessage(locationData: any): Promise<void> {\n    const receiverId = this.conversation?.participants?.find(\n      (p: any) => p.id !== this.currentUserId\n    )?.id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        `📍 Position partagée: ${locationData.address}`,\n        undefined,\n        'TEXT' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\n            \"Erreur lors de l'envoi du message de localisation:\",\n            error\n          );\n          reject(error);\n        },\n      });\n    });\n  }\n\n  // === PARTAGE DE CONTACTS ===\n  async shareContact(): Promise<void> {\n    try {\n      const contact = await this.selectContact();\n\n      if (contact) {\n        await this.sendContactMessage(contact);\n        this.toastService.showSuccess('Contact partagé avec succès');\n      }\n    } catch (error) {\n      console.error('Erreur lors du partage de contact:', error);\n      this.toastService.showError('Erreur lors du partage du contact');\n    }\n  }\n\n  private async selectContact(): Promise<any> {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        resolve({\n          name: 'John Doe',\n          phone: '+33 6 12 34 56 78',\n          email: '<EMAIL>',\n          avatar: 'assets/images/default-avatar.png',\n        });\n      }, 1000);\n    });\n  }\n\n  private async sendContactMessage(contactData: any): Promise<void> {\n    const receiverId = this.conversation?.participants?.find(\n      (p: any) => p.id !== this.currentUserId\n    )?.id;\n\n    if (!receiverId) {\n      throw new Error('Destinataire introuvable');\n    }\n\n    return new Promise((resolve, reject) => {\n      this.MessageService.sendMessage(\n        receiverId,\n        `👤 Contact partagé: ${contactData.name}\\n📞 ${contactData.phone}\\n📧 ${contactData.email}`,\n        undefined,\n        'TEXT' as any,\n        this.conversation.id\n      ).subscribe({\n        next: (message: any) => {\n          this.messages.push(message);\n          this.scrollToBottom();\n          resolve();\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de l'envoi du message de contact:\", error);\n          reject(error);\n        },\n      });\n    });\n  }\n\n  // === UTILITAIRES ===\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  formatDuration(seconds: number): string {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs\n        .toString()\n        .padStart(2, '0')}`;\n    }\n\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.showSearch = false;\n  }\n\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n    this.showEmojiPicker = false;\n  }\n\n  triggerFileInput(): void {\n    this.fileInput.nativeElement.click();\n  }\n\n  // === GESTION DES ERREURS ===\n  handleError(error: any, context: string): void {\n    console.error(`Erreur dans ${context}:`, error);\n    let message = \"Une erreur inattendue s'est produite\";\n\n    if (error?.message) {\n      message = error.message;\n    } else if (typeof error === 'string') {\n      message = error;\n    }\n\n    this.toastService.showError(message);\n  }\n\n  // === NETTOYAGE ===\n  ngOnDestroy(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n    }\n\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    this.subscriptions.unsubscribe();\n  }\n}\n"], "mappings": ";;AAAA,SACEA,SAAS,EAGTC,SAAS,QAGJ,eAAe;AACtB,SAAiCC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,MAAM;AAS5B,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAmG/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,cAA8B,EAC9BC,eAAgC,EAChCC,YAA0B,EAC1BC,GAAsB;IALtB,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IAnGb;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,eAAe,GAAG,KAAK;IAGvB;IACA,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IAEzB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAC1B,KAAAC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAElC;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAAoB,IAAI;IAChC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA,KAAAC,eAAe,GAAU,CACvB;MACEC,EAAE,EAAE,SAAS;MACbC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAEC,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAe,CAAE,EACtC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACED,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAEC,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACED,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAEC,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAY,CAAE,EACnC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAa,CAAE,EACpC;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEG,KAAK,EAAE,IAAI;QAAEH,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAI,qBAAqB,GAAG,IAAI,CAACN,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAAO,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IACR,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAI1C,YAAY,EAAE;IAUxC,IAAI,CAAC2C,WAAW,GAAG,IAAI,CAACxC,EAAE,CAACyC,KAAK,CAAC;MAC/BC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC9C,UAAU,CAAC+C,QAAQ,EAAE/C,UAAU,CAACgD,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQF,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMG,IAAI,GAAG,IAAI,CAAC/C,eAAe,CAACgD,cAAc,EAAE;MAClD,IAAID,IAAI,EAAE;QACR,IAAI,CAAC1C,aAAa,GAAG0C,IAAI,CAACrB,EAAE,IAAI,IAAI;QACpC,IAAI,CAACpB,eAAe,GAAGyC,IAAI,CAACE,QAAQ,IAAI,KAAK;;KAEhD,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,IAAI,CAAC7C,aAAa,GAAG,IAAI;MACzB,IAAI,CAACC,eAAe,GAAG,KAAK;;EAEhC;EAEQuC,gBAAgBA,CAAA;IACtB,MAAMO,cAAc,GAAG,IAAI,CAACtD,KAAK,CAACuD,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAC7D,IAAI,CAACH,cAAc,EAAE;MACnB,IAAI,CAACnD,YAAY,CAACuD,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAACjD,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,cAAc,CAAC0D,eAAe,CACjCL,cAAc,EACd,IAAI,CAAC/C,aAAc,CACpB,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAGxD,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACyD,YAAY,EAAE;MACrB,CAAC;MACDV,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAACjD,YAAY,CAACuD,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAACjD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQqD,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACzD,YAAY,EAAEuB,EAAE,EAAE;IAE5B,IAAI,CAAC3B,cAAc,CAAC8D,WAAW,CAC7B,IAAI,CAAC1D,YAAY,CAACuB,EAAE,EACpBoC,SAAS,EACTA,SAAS,EACT,IAAI,CAAC7B,WAAW,EAChB,IAAI,CAACD,oBAAoB,EACzB,IAAI,CAAC3B,aAAc,CACpB,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAGvD,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,IAAI,EAAE;QAC9B,IAAI,CAACG,SAAS,GAAG,KAAK;QACtB,IAAI,CAACwD,cAAc,EAAE;MACvB,CAAC;MACDb,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACjD,YAAY,CAACuD,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAACjD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQuC,kBAAkBA,CAAA;IACxB;IACA;EAAA;EAGF;EACAkB,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC3B,WAAW,CAAC4B,KAAK,IAAI,CAAC,IAAI,CAAC9D,YAAY,EAAEuB,EAAE,EAAE;IAEvD,MAAMa,OAAO,GAAG,IAAI,CAACF,WAAW,CAACkB,GAAG,CAAC,SAAS,CAAC,EAAEW,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAI,CAAC5B,OAAO,EAAE;IAEd,MAAM6B,UAAU,GAAG,IAAI,CAACjE,YAAY,CAACkE,YAAY,EAAEC,IAAI,CACpDC,CAAM,IAAKA,CAAC,CAAC7C,EAAE,KAAK,IAAI,CAACrB,aAAa,CACxC,EAAEqB,EAAE;IAEL,IAAI,CAAC0C,UAAU,EAAE;MACf,IAAI,CAACnE,YAAY,CAACuD,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF,IAAI,CAACzD,cAAc,CAACiE,WAAW,CAC7BI,UAAU,EACV7B,OAAO,EACPuB,SAAS,EACT,MAAa,EACb,IAAI,CAAC3D,YAAY,CAACuB,EAAE,CACrB,CAACgC,SAAS,CAAC;MACVC,IAAI,EAAGa,OAAY,IAAI;QACrB,IAAI,CAACpE,QAAQ,CAACqE,IAAI,CAACD,OAAO,CAAC;QAC3B,IAAI,CAACnC,WAAW,CAACqC,KAAK,EAAE;QACxB,IAAI,CAACX,cAAc,EAAE;MACvB,CAAC;MACDb,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAACjD,YAAY,CAACuD,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEA;EACAmB,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IAElC,KAAK,IAAIC,IAAI,IAAIH,KAAK,EAAE;MACtB,IAAI,CAACI,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3B,MAAMZ,UAAU,GAAG,IAAI,CAACjE,YAAY,EAAEkE,YAAY,EAAEC,IAAI,CACrDC,CAAM,IAAKA,CAAC,CAAC7C,EAAE,KAAK,IAAI,CAACrB,aAAa,CACxC,EAAEqB,EAAE;IAEL,IAAI,CAAC0C,UAAU,EAAE;MACf,IAAI,CAACnE,YAAY,CAACuD,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF,IAAI,CAACzD,cAAc,CAACmF,eAAe,CACjCd,UAAU,EACVY,IAAI,EACJ,IAAI,CAAC7E,YAAY,CAACuB,EAAE,CACrB,CAACgC,SAAS,CAAC;MACVC,IAAI,EAAGa,OAAY,IAAI;QACrB,IAAI,CAACpE,QAAQ,CAACqE,IAAI,CAACD,OAAO,CAAC;QAC3B,IAAI,CAACT,cAAc,EAAE;QACrB,IAAI,CAAC9D,YAAY,CAACkF,WAAW,CAAC,4BAA4B,CAAC;MAC7D,CAAC;MACDjC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAACjD,YAAY,CAACuD,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEA;EACM4B,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDC,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE;;SAEpB,CAAC;QAEFT,KAAI,CAACnE,aAAa,GAAG,IAAI6E,aAAa,CAACR,MAAM,EAAE;UAC7CS,QAAQ,EAAE;SACX,CAAC;QAEFX,KAAI,CAAClE,WAAW,GAAG,EAAE;QACrBkE,KAAI,CAACtE,gBAAgB,GAAG,IAAI;QAC5BsE,KAAI,CAACrE,sBAAsB,GAAG,CAAC;QAC/BqE,KAAI,CAACpE,mBAAmB,GAAG,WAAW;QAEtCoE,KAAI,CAACjE,cAAc,GAAG6E,WAAW,CAAC,MAAK;UACrCZ,KAAI,CAACrE,sBAAsB,EAAE;UAC7BqE,KAAI,CAACnF,GAAG,CAACgG,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAERb,KAAI,CAACnE,aAAa,CAACiF,eAAe,GAAIvB,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAACwB,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;YACvBhB,KAAI,CAAClE,WAAW,CAACsD,IAAI,CAACG,KAAK,CAACwB,IAAI,CAAC;;QAErC,CAAC;QAEDf,KAAI,CAACnE,aAAa,CAACoF,MAAM,GAAG,MAAK;UAC/BjB,KAAI,CAACkB,oBAAoB,EAAE;QAC7B,CAAC;QAEDlB,KAAI,CAACnE,aAAa,CAACsF,KAAK,CAAC,GAAG,CAAC;QAC7BnB,KAAI,CAACpF,YAAY,CAACkF,WAAW,CAAC,8BAA8B,CAAC;OAC9D,CAAC,OAAOjC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrEmC,KAAI,CAACpF,YAAY,CAACuD,SAAS,CAAC,oCAAoC,CAAC;QACjE6B,KAAI,CAACoB,oBAAoB,EAAE;;IAC5B;EACH;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACxF,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyF,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACzF,aAAa,CAAC0F,IAAI,EAAE;MACzB,IAAI,CAAC1F,aAAa,CAACqE,MAAM,CAACsB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAACxF,cAAc,EAAE;MACvB4F,aAAa,CAAC,IAAI,CAAC5F,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACL,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,mBAAmB,GAAG,YAAY;EACzC;EAEAwF,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACvF,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACyF,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAACzF,aAAa,CAAC0F,IAAI,EAAE;;MAE3B,IAAI,CAAC1F,aAAa,CAACqE,MAAM,CAACsB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;MACtE,IAAI,CAAC1F,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB4F,aAAa,CAAC,IAAI,CAAC5F,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACL,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEcoF,oBAAoBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAA3B,iBAAA;MAChC,IAAI;QACF,IAAI2B,MAAI,CAAC9F,WAAW,CAAC4D,MAAM,KAAK,CAAC,EAAE;UACjCkC,MAAI,CAAChH,YAAY,CAACiH,WAAW,CAAC,wBAAwB,CAAC;UACvDD,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGF,MAAMU,SAAS,GAAG,IAAIC,IAAI,CAACH,MAAI,CAAC9F,WAAW,EAAE;UAC3CkG,IAAI,EAAE;SACP,CAAC;QAEF,IAAIJ,MAAI,CAACjG,sBAAsB,GAAG,CAAC,EAAE;UACnCiG,MAAI,CAAChH,YAAY,CAACiH,WAAW,CAC3B,+CAA+C,CAChD;UACDD,MAAI,CAACR,oBAAoB,EAAE;UAC3B;;QAGF,MAAMa,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACJ,SAAS,CAAC,EAAE,SAASK,IAAI,CAACC,GAAG,EAAE,OAAO,EAAE;UAClEJ,IAAI,EAAE;SACP,CAAC;QAEF,MAAMJ,MAAI,CAACS,gBAAgB,CAACJ,SAAS,CAAC;QACtCL,MAAI,CAAChH,YAAY,CAACkF,WAAW,CAAC,sBAAsB,CAAC;OACtD,CAAC,OAAOjC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D+D,MAAI,CAAChH,YAAY,CAACuD,SAAS,CAAC,yCAAyC,CAAC;OACvE,SAAS;QACRyD,MAAI,CAAChG,mBAAmB,GAAG,MAAM;QACjCgG,MAAI,CAACjG,sBAAsB,GAAG,CAAC;QAC/BiG,MAAI,CAAC9F,WAAW,GAAG,EAAE;;IACtB;EACH;EAEcuG,gBAAgBA,CAACJ,SAAe;IAAA,IAAAK,MAAA;IAAA,OAAArC,iBAAA;MAC5C,MAAMlB,UAAU,GAAGuD,MAAI,CAACxH,YAAY,EAAEkE,YAAY,EAAEC,IAAI,CACrDC,CAAM,IAAKA,CAAC,CAAC7C,EAAE,KAAKiG,MAAI,CAACtH,aAAa,CACxC,EAAEqB,EAAE;MAEL,IAAI,CAAC0C,UAAU,EAAE;QACf,MAAM,IAAIwD,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCJ,MAAI,CAAC5H,cAAc,CAACmF,eAAe,CACjCd,UAAU,EACVkD,SAAS,EACTK,MAAI,CAACxH,YAAY,CAACuB,EAAE,CACrB,CAACgC,SAAS,CAAC;UACVC,IAAI,EAAGa,OAAY,IAAI;YACrBmD,MAAI,CAACvH,QAAQ,CAACqE,IAAI,CAACD,OAAO,CAAC;YAC3BmD,MAAI,CAAC5D,cAAc,EAAE;YACrB+D,OAAO,EAAE;UACX,CAAC;UACD5E,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChE6E,MAAM,CAAC7E,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA8E,uBAAuBA,CAACC,QAAgB;IACtC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMI,OAAO,GAAGJ,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGC,OAAO,IAAIG,OAAO,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACAC,oBAAoBA,CAACC,QAAa;IAChC,OAAOA,QAAQ,EAAE5G,MAAM,IAAI,EAAE;EAC/B;EAEA6G,mBAAmBA,CAACD,QAAa;IAC/B,IAAI,CAAC1G,qBAAqB,GAAG0G,QAAQ;EACvC;EAEAE,WAAWA,CAAC7G,KAAU;IACpB,MAAM8G,cAAc,GAAG,IAAI,CAACvG,WAAW,CAACkB,GAAG,CAAC,SAAS,CAAC,EAAEW,KAAK,IAAI,EAAE;IACnE,MAAM2E,UAAU,GAAGD,cAAc,IAAI9G,KAAK,CAACA,KAAK,IAAIA,KAAK,CAAC;IAC1D,IAAI,CAACO,WAAW,CAACyG,UAAU,CAAC;MAAEvG,OAAO,EAAEsG;IAAU,CAAE,CAAC;IAEpD,IAAI,CAACnI,eAAe,GAAG,KAAK;IAE5BqI,UAAU,CAAC,MAAK;MACd,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CACrC,qCAAqC,CACf;MACxB,IAAIF,QAAQ,EAAE;QACZA,QAAQ,CAACG,KAAK,EAAE;;IAEpB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC1I,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,CAACC,kBAAkB,GAAG,KAAK;EACjC;EAEA;EACA0I,SAASA,CAAChC,IAAc;IACtB,IAAI,CAAC/F,QAAQ,GAAG+F,IAAI;IACpB,IAAI,CAAChG,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACE,YAAY,GAAG,CAAC;IAErB,IAAI,CAACC,SAAS,GAAGyE,WAAW,CAAC,MAAK;MAChC,IAAI,CAAC1E,YAAY,EAAE;MACnB,IAAI,CAACrB,GAAG,CAACgG,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;IAER,IAAI,CAACjG,YAAY,CAACkF,WAAW,CAC3B,SAASkC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,UAAU,CACxD;EACH;EAEAiC,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC9H,SAAS,EAAE;MAClBwF,aAAa,CAAC,IAAI,CAACxF,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACtB,YAAY,CAACkF,WAAW,CAAC,eAAe,CAAC;EAChD;EAEAoE,cAAcA,CAAA;IACZ,IAAI,CAACF,SAAS,CAAC,OAAmB,CAAC;EACrC;EAEAG,cAAcA,CAAA;IACZ,IAAI,CAACH,SAAS,CAAC,OAAmB,CAAC;EACrC;EAEA;EACAI,YAAYA,CAAA;IACV,IAAI,CAAC7I,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB,IAAI,CAACC,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,EAAE;;EAE3B;EAEA4I,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC7I,WAAW,CAACsD,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACrD,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACV,QAAQ,CAACuJ,MAAM,CACtCnF,OAAO,IACNA,OAAO,CAACjC,OAAO,EACXqH,WAAW,EAAE,CACdC,QAAQ,CAAC,IAAI,CAAChJ,WAAW,CAAC+I,WAAW,EAAE,CAAC,IAC3CpF,OAAO,CAACsF,MAAM,EAAE7G,QAAQ,EACpB2G,WAAW,EAAE,CACdC,QAAQ,CAAC,IAAI,CAAChJ,WAAW,CAAC+I,WAAW,EAAE,CAAC,CAC9C;EACH;EAEA;EACAG,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACvJ,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;IAEjD,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACyB,WAAW,EAAE;IAElB,IAAI,CAAClC,cAAc,CAAC8D,WAAW,CAC7B,IAAI,CAAC1D,YAAY,CAACuB,EAAE,EACpBoC,SAAS,EACTA,SAAS,EACT,IAAI,CAAC7B,WAAW,EAChB,IAAI,CAACD,oBAAoB,EACzB,IAAI,CAAC3B,aAAc,CACpB,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAGvD,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAAC2E,MAAM,GAAG,CAAC,EAAE;UACnC,IAAI,CAAC3E,QAAQ,GAAG,CAAC,GAAGA,QAAQ,EAAE,GAAG,IAAI,CAACA,QAAQ,CAAC;SAChD,MAAM;UACL,IAAI,CAACK,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD0C,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC1C,aAAa,GAAG,KAAK;MAC5B;KACD,CAAC;EACJ;EAEA;EACMwJ,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA3E,iBAAA;MACjB,IAAI;QACF,MAAM4E,QAAQ,SAASD,MAAI,CAACE,kBAAkB,EAAE;QAChD,MAAM;UAAEC,QAAQ;UAAEC;QAAS,CAAE,GAAGH,QAAQ,CAACI,MAAM;QAE/C,MAAMC,eAAe,GAAG;UACtBlD,IAAI,EAAE,UAAU;UAChB+C,QAAQ;UACRC,SAAS;UACTG,OAAO,QAAQP,MAAI,CAACQ,yBAAyB,CAACL,QAAQ,EAAEC,SAAS,CAAC;UAClEK,MAAM,EAAE,kCAAkCN,QAAQ,IAAIC,SAAS,OAAO;UACtEM,SAAS,EAAE,IAAInD,IAAI;SACpB;QAED,MAAMyC,MAAI,CAACW,mBAAmB,CAACL,eAAe,CAAC;QAC/CN,MAAI,CAAChK,YAAY,CAACkF,WAAW,CAAC,+BAA+B,CAAC;OAC/D,CAAC,OAAOjC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D+G,MAAI,CAAChK,YAAY,CAACuD,SAAS,CAAC,qCAAqC,CAAC;;IACnE;EACH;EAEQ2G,kBAAkBA,CAAA;IACxB,OAAO,IAAItC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrCvC,SAAS,CAACqF,WAAW,CAACV,kBAAkB,CAACrC,OAAO,EAAEC,MAAM,EAAE;QACxD+C,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;EACJ;EAEcP,yBAAyBA,CACrCQ,GAAW,EACXC,GAAW;IAAA,OAAA5F,iBAAA;MAEX,IAAI;QACF,MAAM6F,QAAQ,SAASC,KAAK,CAC1B,+DAA+DH,GAAG,QAAQC,GAAG,2BAA2B,CACzG;QAED,IAAIC,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMjF,IAAI,SAAS+E,QAAQ,CAACG,IAAI,EAAE;UAClC,OAAOlF,IAAI,CAACmF,YAAY,IAAI,GAAGN,GAAG,KAAKC,GAAG,EAAE;;OAE/C,CAAC,OAAOhI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;MAG3D,OAAO,GAAG+H,GAAG,KAAKC,GAAG,EAAE;IAAC;EAC1B;EAEcN,mBAAmBA,CAACY,YAAiB;IAAA,IAAAC,MAAA;IAAA,OAAAnG,iBAAA;MACjD,MAAMlB,UAAU,GAAGqH,MAAI,CAACtL,YAAY,EAAEkE,YAAY,EAAEC,IAAI,CACrDC,CAAM,IAAKA,CAAC,CAAC7C,EAAE,KAAK+J,MAAI,CAACpL,aAAa,CACxC,EAAEqB,EAAE;MAEL,IAAI,CAAC0C,UAAU,EAAE;QACf,MAAM,IAAIwD,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrC0D,MAAI,CAAC1L,cAAc,CAACiE,WAAW,CAC7BI,UAAU,EACV,yBAAyBoH,YAAY,CAAChB,OAAO,EAAE,EAC/C1G,SAAS,EACT,MAAa,EACb2H,MAAI,CAACtL,YAAY,CAACuB,EAAE,CACrB,CAACgC,SAAS,CAAC;UACVC,IAAI,EAAGa,OAAY,IAAI;YACrBiH,MAAI,CAACrL,QAAQ,CAACqE,IAAI,CAACD,OAAO,CAAC;YAC3BiH,MAAI,CAAC1H,cAAc,EAAE;YACrB+D,OAAO,EAAE;UACX,CAAC;UACD5E,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;YACD6E,MAAM,CAAC7E,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA;EACMwI,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArG,iBAAA;MAChB,IAAI;QACF,MAAMsG,OAAO,SAASD,MAAI,CAACE,aAAa,EAAE;QAE1C,IAAID,OAAO,EAAE;UACX,MAAMD,MAAI,CAACG,kBAAkB,CAACF,OAAO,CAAC;UACtCD,MAAI,CAAC1L,YAAY,CAACkF,WAAW,CAAC,6BAA6B,CAAC;;OAE/D,CAAC,OAAOjC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DyI,MAAI,CAAC1L,YAAY,CAACuD,SAAS,CAAC,mCAAmC,CAAC;;IACjE;EACH;EAEcqI,aAAaA,CAAA;IAAA,OAAAvG,iBAAA;MACzB,OAAO,IAAIuC,OAAO,CAAEC,OAAO,IAAI;QAC7BiB,UAAU,CAAC,MAAK;UACdjB,OAAO,CAAC;YACNnG,IAAI,EAAE,UAAU;YAChBoK,KAAK,EAAE,mBAAmB;YAC1BC,KAAK,EAAE,sBAAsB;YAC7BC,MAAM,EAAE;WACT,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IAAC;EACL;EAEcH,kBAAkBA,CAACI,WAAgB;IAAA,IAAAC,MAAA;IAAA,OAAA7G,iBAAA;MAC/C,MAAMlB,UAAU,GAAG+H,MAAI,CAAChM,YAAY,EAAEkE,YAAY,EAAEC,IAAI,CACrDC,CAAM,IAAKA,CAAC,CAAC7C,EAAE,KAAKyK,MAAI,CAAC9L,aAAa,CACxC,EAAEqB,EAAE;MAEL,IAAI,CAAC0C,UAAU,EAAE;QACf,MAAM,IAAIwD,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCoE,MAAI,CAACpM,cAAc,CAACiE,WAAW,CAC7BI,UAAU,EACV,uBAAuB8H,WAAW,CAACvK,IAAI,QAAQuK,WAAW,CAACH,KAAK,QAAQG,WAAW,CAACF,KAAK,EAAE,EAC3FlI,SAAS,EACT,MAAa,EACbqI,MAAI,CAAChM,YAAY,CAACuB,EAAE,CACrB,CAACgC,SAAS,CAAC;UACVC,IAAI,EAAGa,OAAY,IAAI;YACrB2H,MAAI,CAAC/L,QAAQ,CAACqE,IAAI,CAACD,OAAO,CAAC;YAC3B2H,MAAI,CAACpI,cAAc,EAAE;YACrB+D,OAAO,EAAE;UACX,CAAC;UACD5E,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;YACrE6E,MAAM,CAAC7E,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA;EACAa,cAAcA,CAAA;IACZgF,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACqD,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAG1E,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC2E,GAAG,CAACJ,KAAK,CAAC,GAAGvE,IAAI,CAAC2E,GAAG,CAACH,CAAC,CAAC,CAAC;IACnD,OAAOI,UAAU,CAAC,CAACL,KAAK,GAAGvE,IAAI,CAAC6E,GAAG,CAACL,CAAC,EAAEE,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGL,KAAK,CAACC,CAAC,CAAC;EACzE;EAEAK,cAAcA,CAAC7E,OAAe;IAC5B,MAAM8E,KAAK,GAAGhF,IAAI,CAACC,KAAK,CAACC,OAAO,GAAG,IAAI,CAAC;IACxC,MAAMH,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAEC,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;IACjD,MAAM+E,IAAI,GAAG/E,OAAO,GAAG,EAAE;IAEzB,IAAI8E,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIjF,OAAO,CAACI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI6E,IAAI,CAC3D9E,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAGvB,OAAO,GAAGL,OAAO,IAAIkF,IAAI,CAAC9E,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACzD;EAEA8E,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC5M,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,UAAU,GAAG,KAAK;EACzB;EAEA2M,oBAAoBA,CAAA;IAClB,IAAI,CAAC5M,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,CAACD,eAAe,GAAG,KAAK;EAC9B;EAEA8M,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACnB,aAAa,CAACoB,KAAK,EAAE;EACtC;EAEA;EACAC,WAAWA,CAACzK,KAAU,EAAE0K,OAAe;IACrCzK,OAAO,CAACD,KAAK,CAAC,eAAe0K,OAAO,GAAG,EAAE1K,KAAK,CAAC;IAC/C,IAAIsB,OAAO,GAAG,sCAAsC;IAEpD,IAAItB,KAAK,EAAEsB,OAAO,EAAE;MAClBA,OAAO,GAAGtB,KAAK,CAACsB,OAAO;KACxB,MAAM,IAAI,OAAOtB,KAAK,KAAK,QAAQ,EAAE;MACpCsB,OAAO,GAAGtB,KAAK;;IAGjB,IAAI,CAACjD,YAAY,CAACuD,SAAS,CAACgB,OAAO,CAAC;EACtC;EAEA;EACAqJ,WAAWA,CAAA;IACT,IAAI,IAAI,CAACrM,SAAS,EAAE;MAClBwF,aAAa,CAAC,IAAI,CAACxF,SAAS,CAAC;;IAG/B,IAAI,IAAI,CAACJ,cAAc,EAAE;MACvB4F,aAAa,CAAC,IAAI,CAAC5F,cAAc,CAAC;;IAGpC,IAAI,IAAI,CAACe,aAAa,EAAE;MACtB2L,YAAY,CAAC,IAAI,CAAC3L,aAAa,CAAC;;IAGlC,IAAI,IAAI,CAACjB,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyF,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAACzF,aAAa,CAAC0F,IAAI,EAAE;MACzB,IAAI,CAAC1F,aAAa,CAACqE,MAAM,CAACsB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGxE,IAAI,CAACxE,aAAa,CAAC2L,WAAW,EAAE;EAClC;CACD;AA3vByCC,UAAA,EAAvCxO,SAAS,CAAC,mBAAmB,CAAC,C,8DAAwC;AAEvEwO,UAAA,EADCxO,SAAS,CAAC,WAAW,EAAE;EAAEyO,MAAM,EAAE;AAAK,CAAE,CAAC,C,sDACD;AAJ9BtO,oBAAoB,GAAAqO,UAAA,EAJhCzO,SAAS,CAAC;EACT2O,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE;CACd,CAAC,C,EACWxO,oBAAoB,CA6vBhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}