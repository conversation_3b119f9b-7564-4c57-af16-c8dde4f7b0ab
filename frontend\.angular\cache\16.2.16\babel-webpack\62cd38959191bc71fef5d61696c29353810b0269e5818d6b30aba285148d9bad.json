{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { MessageType, CallType } from 'src/app/models/message.model';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/services/user-status.service\";\nimport * as i6 from \"src/app/services/toast.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 70);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.otherParticipant.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.otherParticipant.isOnline ? \"En ligne\" : ctx_r0.formatLastActive(ctx_r0.otherParticipant.lastActive), \" \");\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"animate-pulse\": a0\n  };\n};\nfunction MessageChatComponent_ng_container_7_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r35 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassMap(\"absolute -top-2 -right-2 min-w-[20px] h-5 px-1.5 text-white text-xs rounded-md flex items-center justify-center font-bold shadow-lg border border-white/20 \" + action_r35.badge.class);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c2, action_r35.badge.animate));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", action_r35.badge.count > 99 ? \"99+\" : action_r35.badge.count, \" \");\n  }\n}\nconst _c3 = function () {\n  return {};\n};\nfunction MessageChatComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_7_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const action_r35 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(action_r35.onClick());\n    });\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵtemplate(3, MessageChatComponent_ng_container_7_span_3_Template, 2, 6, \"span\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const action_r35 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"whatsapp-action-button \" + action_r35.class);\n    i0.ɵɵproperty(\"ngClass\", action_r35.activeClass && action_r35.isActive ? action_r35.activeClass : i0.ɵɵpureFunction0(7, _c3))(\"title\", action_r35.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(action_r35.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", action_r35.badge && action_r35.badge.count > 0);\n  }\n}\nfunction MessageChatComponent_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 73);\n  }\n}\nconst _c4 = function (a0) {\n  return {\n    \"bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20\": a0\n  };\n};\nfunction MessageChatComponent_div_12_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_12_button_8_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r43);\n      const status_r41 = restoredCtx.$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      ctx_r42.updateUserStatus(status_r41.key);\n      return i0.ɵɵresetView(ctx_r42.toggleStatusSelector());\n    });\n    i0.ɵɵelementStart(1, \"div\", 82);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementStart(3, \"div\")(4, \"div\", 84);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 85);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const status_r41 = ctx.$implicit;\n    const ctx_r40 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r40.isUpdatingStatus)(\"ngClass\", i0.ɵɵpureFunction1(6, _c4, ctx_r40.currentUserStatus === status_r41.key));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(status_r41.icon + \" \" + status_r41.color + \" mr-3 text-xs\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(status_r41.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", status_r41.description, \" \");\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"div\", 76)(3, \"span\");\n    i0.ɵɵtext(4, \"Statut actuel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 77);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 78);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_12_button_8_Template, 8, 8, \"button\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 80)(10, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_12_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      ctx_r44.toggleUserStatusPanel();\n      return i0.ɵɵresetView(ctx_r44.toggleStatusSelector());\n    });\n    i0.ɵɵelementStart(11, \"div\", 82);\n    i0.ɵɵelement(12, \"i\", 83);\n    i0.ɵɵelementStart(13, \"div\")(14, \"div\", 84);\n    i0.ɵɵtext(15, \"Voir tous les utilisateurs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 85);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getStatusColor(ctx_r3.currentUserStatus));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getStatusText(ctx_r3.currentUserStatus), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getStatusOptions());\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getOnlineUsersCount(), \" en ligne \");\n  }\n}\nfunction MessageChatComponent_div_16_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 90);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_16_a_4_Template_a_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r49);\n      const theme_r47 = restoredCtx.$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.changeTheme(theme_r47.key));\n    });\n    i0.ɵɵelementStart(1, \"div\", 82);\n    i0.ɵɵelement(2, \"div\", 10);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const theme_r47 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"hover:bg-\" + theme_r47.hoverColor + \"/10 dark:hover:bg-\" + theme_r47.hoverColor + \"/10\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", \"w-4 h-4 rounded-full bg-\" + theme_r47.color + \" mr-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(theme_r47.label);\n  }\n}\nfunction MessageChatComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88);\n    i0.ɵɵtext(2, \" Choisir un th\\u00E8me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 78);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_16_a_4_Template, 5, 3, \"a\", 89);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getThemeOptions());\n  }\n}\nfunction MessageChatComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 88);\n    i0.ɵɵtext(2, \" Options de conversation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 78)(4, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_20_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.clearConversation());\n    });\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵelement(6, \"i\", 92);\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \"Vider la conversation\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_20_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.exportConversation());\n    });\n    i0.ɵɵelementStart(10, \"div\", 82);\n    i0.ɵɵelement(11, \"i\", 93);\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13, \"Exporter la conversation\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_20_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.toggleConversationInfo());\n    });\n    i0.ɵɵelementStart(15, \"div\", 82);\n    i0.ɵɵelement(16, \"i\", 94);\n    i0.ɵɵelementStart(17, \"div\");\n    i0.ɵɵtext(18, \"Informations\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_20_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r54 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r54.toggleConversationSettings());\n    });\n    i0.ɵɵelementStart(20, \"div\", 82);\n    i0.ɵɵelement(21, \"i\", 95);\n    i0.ɵɵelementStart(22, \"div\");\n    i0.ɵɵtext(23, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"i\", 97);\n    i0.ɵɵtext(2, \" Chargement... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelement(1, \"i\", 99);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun message pour le moment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 100);\n    i0.ɵɵtext(5, \"Commencez la conversation !\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c5 = function (a0, a1) {\n  return {\n    \"current-user\": a0,\n    \"other-user\": a1\n  };\n};\nfunction MessageChatComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"div\", 102)(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 103);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r55 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c5, (message_r55.sender == null ? null : message_r55.sender.id) === ctx_r9.currentUserId, (message_r55.sender == null ? null : message_r55.sender.id) !== ctx_r9.currentUserId));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r55.content);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.formatMessageTime(message_r55.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_26_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r56.replyingToMessage.content);\n  }\n}\nfunction MessageChatComponent_div_26_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 113);\n    i0.ɵɵelement(1, \"i\", 114);\n    i0.ɵɵtext(2, \" Photo \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_26_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 113);\n    i0.ɵɵelement(1, \"i\", 115);\n    i0.ɵɵtext(2, \" Message vocal \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 104)(1, \"div\", 105)(2, \"div\", 106)(3, \"div\", 107);\n    i0.ɵɵelement(4, \"i\", 108);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_26_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.cancelReply());\n    });\n    i0.ɵɵelement(8, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 110);\n    i0.ɵɵtemplate(10, MessageChatComponent_div_26_span_10_Template, 2, 1, \"span\", 111);\n    i0.ɵɵtemplate(11, MessageChatComponent_div_26_span_11_Template, 3, 0, \"span\", 112);\n    i0.ɵɵtemplate(12, MessageChatComponent_div_26_span_12_Template, 3, 0, \"span\", 112);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"R\\u00E9pondre \\u00E0 \", ctx_r10.replyingToMessage.sender == null ? null : ctx_r10.replyingToMessage.sender.username, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.replyingToMessage.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.hasImage(ctx_r10.replyingToMessage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isVoiceMessage(ctx_r10.replyingToMessage));\n  }\n}\nfunction MessageChatComponent_div_27_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125);\n    i0.ɵɵelement(1, \"img\", 126);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r61.previewUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r61.selectedFile.name);\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 116)(1, \"div\", 117)(2, \"div\", 118)(3, \"span\");\n    i0.ɵɵtext(4, \"Fichier s\\u00E9lectionn\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.removeSelectedFile());\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 120);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_27_div_8_Template, 2, 2, \"div\", 121);\n    i0.ɵɵelementStart(9, \"div\", 122)(10, \"div\", 123);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 124);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.previewUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r11.selectedFile.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.formatFileSize(ctx_r11.selectedFile.size), \" \");\n  }\n}\nfunction MessageChatComponent_div_28_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 141);\n  }\n  if (rf & 2) {\n    const bar_r65 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"height\", bar_r65, \"px\");\n  }\n}\nfunction MessageChatComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"div\", 128)(2, \"div\", 129)(3, \"div\", 130);\n    i0.ɵɵelement(4, \"i\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 131)(6, \"span\", 132);\n    i0.ɵɵtext(7, \"Enregistrement en cours...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 133);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 134)(11, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_28_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.cancelVoiceRecording());\n    });\n    i0.ɵɵelement(12, \"i\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 137);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_28_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r68 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r68.stopVoiceRecording());\n    });\n    i0.ɵɵelement(14, \"i\", 138);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 139);\n    i0.ɵɵtemplate(16, MessageChatComponent_div_28_div_16_Template, 1, 2, \"div\", 140);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r12.formatRecordingDuration(ctx_r12.voiceRecordingDuration));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.recordingWaveform);\n  }\n}\nfunction MessageChatComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 142)(1, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_37_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelement(2, \"i\", 114);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Photo\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_37_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r71.triggerFileInput(\"video\"));\n    });\n    i0.ɵɵelement(6, \"i\", 144);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Vid\\u00E9o\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_37_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.triggerFileInput(\"audio\"));\n    });\n    i0.ɵɵelement(10, \"i\", 145);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Audio\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_37_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelement(14, \"i\", 146);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Document\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_37_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.openCamera());\n    });\n    i0.ɵɵelement(18, \"i\", 147);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", 4096 - (((tmp_0_0 = ctx_r15.messageForm.get(\"content\")) == null ? null : tmp_0_0.value == null ? null : tmp_0_0.value.length) || 0), \" \");\n  }\n}\nfunction MessageChatComponent_button_46_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 138);\n  }\n}\nfunction MessageChatComponent_button_46_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 97);\n  }\n}\nconst _c6 = function (a0) {\n  return {\n    sending: a0\n  };\n};\nfunction MessageChatComponent_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r78 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 149);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r78);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.sendMessage());\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_button_46_i_1_Template, 1, 0, \"i\", 150);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_46_i_2_Template, 1, 0, \"i\", 151);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r16.canSendMessage())(\"ngClass\", i0.ɵɵpureFunction1(4, _c6, ctx_r16.isSendingMessage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.isSendingMessage);\n  }\n}\nconst _c7 = function (a0) {\n  return {\n    recording: a0\n  };\n};\nfunction MessageChatComponent_button_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r80 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 152);\n    i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_button_47_Template_button_mousedown_0_listener() {\n      i0.ɵɵrestoreView(_r80);\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.startVoiceRecording());\n    })(\"mouseup\", function MessageChatComponent_button_47_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r80);\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.stopVoiceRecording());\n    })(\"mouseleave\", function MessageChatComponent_button_47_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r80);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.cancelVoiceRecording());\n    })(\"touchstart\", function MessageChatComponent_button_47_Template_button_touchstart_0_listener() {\n      i0.ɵɵrestoreView(_r80);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.startVoiceRecording());\n    })(\"touchend\", function MessageChatComponent_button_47_Template_button_touchend_0_listener() {\n      i0.ɵɵrestoreView(_r80);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.stopVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 115);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c7, ctx_r17.isRecordingVoice));\n  }\n}\nconst _c8 = function (a0) {\n  return {\n    active: a0\n  };\n};\nfunction MessageChatComponent_div_48_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r90 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_48_button_3_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r90);\n      const category_r88 = restoredCtx.$implicit;\n      const ctx_r89 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r89.selectEmojiCategory(category_r88.name));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r88 = ctx.$implicit;\n    const ctx_r85 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c8, ctx_r85.selectedEmojiCategory === category_r88.name))(\"title\", category_r88.label);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r88.icon, \" \");\n  }\n}\nfunction MessageChatComponent_div_48_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r93 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 164);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_48_button_9_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r93);\n      const emoji_r91 = restoredCtx.$implicit;\n      const ctx_r92 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r92.insertEmoji(emoji_r91.char));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r91 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r91.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r91.char, \" \");\n  }\n}\nfunction MessageChatComponent_div_48_div_10_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r97 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 169);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_48_div_10_button_4_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r97);\n      const emoji_r95 = restoredCtx.$implicit;\n      const ctx_r96 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r96.insertEmoji(emoji_r95));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r95 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r95, \" \");\n  }\n}\nfunction MessageChatComponent_div_48_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 165)(1, \"div\", 166);\n    i0.ɵɵtext(2, \"R\\u00E9cemment utilis\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 167);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_48_div_10_button_4_Template, 2, 1, \"button\", 168);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r87 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r87.recentEmojis);\n  }\n}\nfunction MessageChatComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r99 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 153)(1, \"div\", 154)(2, \"div\", 155);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_48_button_3_Template, 2, 5, \"button\", 156);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 157);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_48_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r99);\n      const ctx_r98 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r98.toggleEmojiPicker());\n    });\n    i0.ɵɵelement(5, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 158)(7, \"input\", 159);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_48_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r99);\n      const ctx_r100 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r100.emojiSearchQuery = $event);\n    })(\"input\", function MessageChatComponent_div_48_Template_input_input_7_listener() {\n      i0.ɵɵrestoreView(_r99);\n      const ctx_r101 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r101.searchEmojis());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 160);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_48_button_9_Template, 2, 2, \"button\", 161);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, MessageChatComponent_div_48_div_10_Template, 5, 1, \"div\", 162);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.emojiCategories);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r18.emojiSearchQuery);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.getFilteredEmojis());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.recentEmojis.length > 0);\n  }\n}\nfunction MessageChatComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 170);\n    i0.ɵɵelement(1, \"i\", 171);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune notification\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 172)(1, \"p\", 173);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"small\", 174);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notification_r102 = ctx.$implicit;\n    const ctx_r24 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r102.content);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r24.formatMessageTime(notification_r102.timestamp));\n  }\n}\nfunction MessageChatComponent_div_65_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 186);\n    i0.ɵɵelement(1, \"div\", 187);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_65_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r108 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 188);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_65_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r108);\n      const ctx_r107 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r107.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 189);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_65_div_10_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r112 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 194);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_65_div_10_button_4_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r112);\n      const result_r110 = restoredCtx.$implicit;\n      const ctx_r111 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(result_r110.id && ctx_r111.navigateToMessage(result_r110.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 195)(2, \"div\", 196);\n    i0.ɵɵelement(3, \"i\", 197);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 198)(5, \"div\", 199);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 200);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const result_r110 = ctx.$implicit;\n    const ctx_r109 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r109.formatMessageTime(result_r110.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r109.highlightSearchTerms(result_r110.content || \"\", ctx_r109.searchQuery), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_65_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 191);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 192);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_65_div_10_button_4_Template, 8, 2, \"button\", 193);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r105 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r105.searchResults.length, \" r\\u00E9sultat(s) trouv\\u00E9(s) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r105.searchResults);\n  }\n}\nfunction MessageChatComponent_div_65_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 201);\n    i0.ɵɵelement(1, \"i\", 202);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r106 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Aucun message trouv\\u00E9 pour \\\"\", ctx_r106.searchQuery, \"\\\" \");\n  }\n}\nfunction MessageChatComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r114 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 175)(1, \"div\", 176)(2, \"div\", 177);\n    i0.ɵɵelement(3, \"i\", 178);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 179)(5, \"input\", 180);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_65_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r114);\n      const ctx_r113 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r113.searchQuery = $event);\n    })(\"input\", function MessageChatComponent_div_65_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r114);\n      const ctx_r115 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r115.onSearchInput($event));\n    })(\"keydown\", function MessageChatComponent_div_65_Template_input_keydown_5_listener($event) {\n      i0.ɵɵrestoreView(_r114);\n      const ctx_r116 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r116.onSearchKeyPress($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, MessageChatComponent_div_65_div_6_Template, 2, 0, \"div\", 181);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_65_button_7_Template, 2, 0, \"button\", 182);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 183);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_65_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r114);\n      const ctx_r117 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r117.toggleSearchBar());\n    });\n    i0.ɵɵelement(9, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, MessageChatComponent_div_65_div_10_Template, 5, 2, \"div\", 184);\n    i0.ɵɵtemplate(11, MessageChatComponent_div_65_div_11_Template, 3, 1, \"div\", 185);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r25.searchQuery);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.isSearching);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.searchQuery && !ctx_r25.isSearching);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.searchMode && ctx_r25.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.searchMode && ctx_r25.searchResults.length === 0 && !ctx_r25.isSearching && ctx_r25.searchQuery.length >= 2);\n  }\n}\nfunction MessageChatComponent_div_66_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 212)(1, \"div\", 213);\n    i0.ɵɵelement(2, \"i\", 214);\n    i0.ɵɵelementStart(3, \"div\", 215);\n    i0.ɵɵtext(4, \"Aucun message \\u00E9pingl\\u00E9\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_66_div_10_button_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pinnedMessage_r121 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(pinnedMessage_r121.content);\n  }\n}\nfunction MessageChatComponent_div_66_div_10_button_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 232);\n    i0.ɵɵelement(1, \"i\", 233);\n    i0.ɵɵtext(2, \" Image \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_66_div_10_button_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 232);\n    i0.ɵɵelement(1, \"i\", 234);\n    i0.ɵɵtext(2, \" Message vocal \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_66_div_10_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r127 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 218);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_66_div_10_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r127);\n      const pinnedMessage_r121 = restoredCtx.$implicit;\n      const ctx_r126 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r126.scrollToPinnedMessage(pinnedMessage_r121.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 219)(2, \"div\", 220);\n    i0.ɵɵelement(3, \"img\", 221);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 198)(5, \"div\", 222)(6, \"span\", 223);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 224);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 225);\n    i0.ɵɵtemplate(11, MessageChatComponent_div_66_div_10_button_1_span_11_Template, 2, 1, \"span\", 111);\n    i0.ɵɵtemplate(12, MessageChatComponent_div_66_div_10_button_1_span_12_Template, 3, 0, \"span\", 226);\n    i0.ɵɵtemplate(13, MessageChatComponent_div_66_div_10_button_1_span_13_Template, 3, 0, \"span\", 226);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 227);\n    i0.ɵɵelement(15, \"i\", 228);\n    i0.ɵɵelementStart(16, \"span\", 229);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 230);\n    i0.ɵɵelement(19, \"i\", 231);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const pinnedMessage_r121 = ctx.$implicit;\n    const ctx_r120 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (pinnedMessage_r121.sender == null ? null : pinnedMessage_r121.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", (pinnedMessage_r121.sender == null ? null : pinnedMessage_r121.sender.username) || \"User\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (pinnedMessage_r121.sender == null ? null : pinnedMessage_r121.sender.username) || \"Utilisateur inconnu\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r120.formatMessageTime(pinnedMessage_r121.timestamp), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", pinnedMessage_r121.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r120.hasImage(pinnedMessage_r121));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r120.isVoiceMessage(pinnedMessage_r121));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u00C9pingl\\u00E9 \", pinnedMessage_r121.pinnedAt ? \"le \" + ctx_r120.formatMessageDate(pinnedMessage_r121.pinnedAt) : \"\", \" \");\n  }\n}\nfunction MessageChatComponent_div_66_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 216);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_66_div_10_button_1_Template, 20, 8, \"button\", 217);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r119 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r119.pinnedMessages);\n  }\n}\nfunction MessageChatComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r129 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 203)(1, \"div\", 204)(2, \"div\", 205);\n    i0.ɵɵelement(3, \"i\", 206);\n    i0.ɵɵelementStart(4, \"h3\", 207);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 208);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_66_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r129);\n      const ctx_r128 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r128.togglePinnedMessages());\n    });\n    i0.ɵɵelement(7, \"i\", 189);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 209);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_66_div_9_Template, 5, 0, \"div\", 210);\n    i0.ɵɵtemplate(10, MessageChatComponent_div_66_div_10_Template, 2, 1, \"div\", 211);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Messages \\u00E9pingl\\u00E9s (\", ctx_r26.getPinnedMessagesCount(), \") \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r26.pinnedMessages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r26.pinnedMessages.length > 0);\n  }\n}\nfunction MessageChatComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r131 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 235);\n    i0.ɵɵelement(1, \"div\", 236);\n    i0.ɵɵelementStart(2, \"div\", 237)(3, \"div\", 238)(4, \"div\", 239);\n    i0.ɵɵelement(5, \"img\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 240)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 241)(12, \"button\", 242);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_67_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r131);\n      const ctx_r130 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r130.declineCall());\n    });\n    i0.ɵɵelement(13, \"i\", 243);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 244);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_67_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r131);\n      const ctx_r132 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r132.acceptCall());\n    });\n    i0.ɵɵelement(15, \"i\", 245);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", (ctx_r27.incomingCall.caller == null ? null : ctx_r27.incomingCall.caller.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r27.incomingCall.caller == null ? null : ctx_r27.incomingCall.caller.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r27.incomingCall.caller == null ? null : ctx_r27.incomingCall.caller.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r27.incomingCall.type === \"AUDIO\" ? \"Appel audio entrant\" : \"Appel vid\\u00E9o entrant\", \" \");\n  }\n}\nfunction MessageChatComponent_div_68_i_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 262);\n  }\n}\nfunction MessageChatComponent_div_68_i_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 263);\n  }\n}\nfunction MessageChatComponent_div_68_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 264);\n    i0.ɵɵelement(1, \"video\", 265, 266)(3, \"video\", 267, 268);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_68_i_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 115);\n  }\n}\nfunction MessageChatComponent_div_68_i_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 269);\n  }\n}\nfunction MessageChatComponent_div_68_button_20_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 144);\n  }\n}\nfunction MessageChatComponent_div_68_button_20_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 272);\n  }\n}\nfunction MessageChatComponent_div_68_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r144 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 257);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_68_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r144);\n      const ctx_r143 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r143.toggleVideo());\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_div_68_button_20_i_1_Template, 1, 0, \"i\", 270);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_68_button_20_i_2_Template, 1, 0, \"i\", 271);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r138 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c8, !ctx_r138.isVideoEnabled));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r138.isVideoEnabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r138.isVideoEnabled);\n  }\n}\nconst _c9 = function (a0) {\n  return {\n    minimized: a0\n  };\n};\nconst _c10 = function (a0) {\n  return {\n    show: a0\n  };\n};\nfunction MessageChatComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r146 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 246)(1, \"div\", 237)(2, \"div\", 247)(3, \"div\", 248);\n    i0.ɵɵelement(4, \"img\", 126);\n    i0.ɵɵelementStart(5, \"div\", 249)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 250);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 251);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"button\", 252);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_68_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r146);\n      const ctx_r145 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r145.toggleCallMinimize());\n    });\n    i0.ɵɵtemplate(13, MessageChatComponent_div_68_i_13_Template, 1, 0, \"i\", 253);\n    i0.ɵɵtemplate(14, MessageChatComponent_div_68_i_14_Template, 1, 0, \"i\", 254);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, MessageChatComponent_div_68_div_15_Template, 5, 0, \"div\", 255);\n    i0.ɵɵelementStart(16, \"div\", 256)(17, \"button\", 257);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_68_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r146);\n      const ctx_r147 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r147.toggleMute());\n    });\n    i0.ɵɵtemplate(18, MessageChatComponent_div_68_i_18_Template, 1, 0, \"i\", 258);\n    i0.ɵɵtemplate(19, MessageChatComponent_div_68_i_19_Template, 1, 0, \"i\", 259);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, MessageChatComponent_div_68_button_20_Template, 3, 5, \"button\", 260);\n    i0.ɵɵelementStart(21, \"button\", 261);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_68_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r146);\n      const ctx_r148 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r148.endCall());\n    });\n    i0.ɵɵelement(22, \"i\", 243);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c9, ctx_r28.isCallMinimized));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (ctx_r28.activeCall.participant == null ? null : ctx_r28.activeCall.participant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r28.activeCall.participant == null ? null : ctx_r28.activeCall.participant.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r28.activeCall.participant == null ? null : ctx_r28.activeCall.participant.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r28.formatCallDuration(ctx_r28.callDuration));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r28.getCallStatusText());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r28.isCallMinimized);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.isCallMinimized);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.activeCall.type === \"VIDEO\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(16, _c10, ctx_r28.showCallControls || !ctx_r28.isCallMinimized));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(18, _c8, ctx_r28.isCallMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r28.isCallMuted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.isCallMuted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.activeCall.type === \"VIDEO\");\n  }\n}\nfunction MessageChatComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r150 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 273)(1, \"div\", 274)(2, \"div\", 275)(3, \"h3\");\n    i0.ɵɵtext(4, \"Confirmer la suppression\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 276)(6, \"p\");\n    i0.ɵɵtext(7, \" \\u00CAtes-vous s\\u00FBr de vouloir supprimer ce message ? Cette action est irr\\u00E9versible. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 277)(9, \"button\", 278);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_69_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r150);\n      const ctx_r149 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r149.cancelDelete());\n    });\n    i0.ɵɵtext(10, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 279);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_69_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r150);\n      const ctx_r151 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r151.confirmDelete());\n    });\n    i0.ɵɵtext(12, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction MessageChatComponent_div_70_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r155 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 287);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_70_div_11_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r155);\n      const contact_r153 = restoredCtx.$implicit;\n      const ctx_r154 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r154.toggleContactSelection(contact_r153.id));\n    });\n    i0.ɵɵelement(1, \"input\", 288)(2, \"img\", 126);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r153 = ctx.$implicit;\n    const ctx_r152 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"checked\", ctx_r152.selectedContacts.includes(contact_r153.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", contact_r153.image || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", contact_r153.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r153.username);\n  }\n}\nfunction MessageChatComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r157 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 273)(1, \"div\", 280)(2, \"div\", 275)(3, \"h3\");\n    i0.ɵɵtext(4, \"Transf\\u00E9rer le message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 281);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_70_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r156 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r156.closeForwardModal());\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 276)(8, \"div\", 282)(9, \"input\", 283);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_70_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r158 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r158.forwardSearchQuery = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 284);\n    i0.ɵɵtemplate(11, MessageChatComponent_div_70_div_11_Template, 5, 4, \"div\", 285);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 277)(13, \"button\", 278);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_70_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r159 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r159.closeForwardModal());\n    });\n    i0.ɵɵtext(14, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 286);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_70_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r160 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r160.confirmForward());\n    });\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r30.forwardSearchQuery);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r30.getFilteredContacts());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r30.selectedContacts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Transf\\u00E9rer (\", ctx_r30.selectedContacts.length, \") \");\n  }\n}\nfunction MessageChatComponent_div_71_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r165 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 300);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_71_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r165);\n      const ctx_r164 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r164.previousImage());\n    });\n    i0.ɵɵelement(1, \"i\", 301);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_71_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r167 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 302);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_71_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r167);\n      const ctx_r166 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r166.nextImage());\n    });\n    i0.ɵɵelement(1, \"i\", 303);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_71_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 304);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r163 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r163.imageGallery[ctx_r163.currentImageIndex] == null ? null : ctx_r163.imageGallery[ctx_r163.currentImageIndex].caption, \" \");\n  }\n}\nfunction MessageChatComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r169 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 289)(1, \"div\", 290);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_71_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r168 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r168.closeImageViewer());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 291)(3, \"div\", 292)(4, \"div\", 293)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 294);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_71_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r170 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r170.closeImageViewer());\n    });\n    i0.ɵɵelement(8, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 295);\n    i0.ɵɵtemplate(10, MessageChatComponent_div_71_button_10_Template, 2, 0, \"button\", 296);\n    i0.ɵɵelement(11, \"img\", 297);\n    i0.ɵɵtemplate(12, MessageChatComponent_div_71_button_12_Template, 2, 0, \"button\", 298);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, MessageChatComponent_div_71_div_13_Template, 2, 1, \"div\", 299);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r31.currentImageIndex + 1, \" / \", ctx_r31.imageGallery.length, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.imageGallery.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r31.imageGallery[ctx_r31.currentImageIndex] == null ? null : ctx_r31.imageGallery[ctx_r31.currentImageIndex].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r31.imageGallery[ctx_r31.currentImageIndex] == null ? null : ctx_r31.imageGallery[ctx_r31.currentImageIndex].caption);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.imageGallery.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.imageGallery[ctx_r31.currentImageIndex] == null ? null : ctx_r31.imageGallery[ctx_r31.currentImageIndex].caption);\n  }\n}\nfunction MessageChatComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 305)(1, \"div\", 306);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r32.toastType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r32.getToastIcon());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r32.toastMessage);\n  }\n}\nfunction MessageChatComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r172 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 307)(1, \"div\", 308)(2, \"h3\");\n    i0.ɵɵtext(3, \"Informations de conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 309);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_73_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r172);\n      const ctx_r171 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r171.toggleConversationInfo());\n    });\n    i0.ɵɵelement(5, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 310)(7, \"div\", 311);\n    i0.ɵɵelement(8, \"img\", 126);\n    i0.ɵɵelementStart(9, \"div\", 312)(10, \"h4\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 313);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 314)(17, \"div\", 315)(18, \"span\", 316);\n    i0.ɵɵtext(19, \"Messages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 317);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 315)(23, \"span\", 316);\n    i0.ɵɵtext(24, \"Photos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 317);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 315)(28, \"span\", 316);\n    i0.ɵɵtext(29, \"Fichiers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 317);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"src\", (ctx_r33.otherParticipant == null ? null : ctx_r33.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r33.otherParticipant == null ? null : ctx_r33.otherParticipant.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r33.otherParticipant == null ? null : ctx_r33.otherParticipant.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r33.otherParticipant == null ? null : ctx_r33.otherParticipant.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((ctx_r33.otherParticipant == null ? null : ctx_r33.otherParticipant.isOnline) ? \"En ligne\" : \"Hors ligne\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r33.getTotalMessagesCount());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r33.getPhotosCount());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r33.getFilesCount());\n  }\n}\nfunction MessageChatComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r174 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 318)(1, \"div\", 308)(2, \"h3\");\n    i0.ɵɵtext(3, \"Param\\u00E8tres de conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 309);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_74_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r174);\n      const ctx_r173 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r173.toggleConversationSettings());\n    });\n    i0.ɵɵelement(5, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 310)(7, \"div\", 319)(8, \"h4\");\n    i0.ɵɵtext(9, \"Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 320)(11, \"label\")(12, \"input\", 321);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_74_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r174);\n      const ctx_r175 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r175.conversationSettings.notifications = $event);\n    })(\"change\", function MessageChatComponent_div_74_Template_input_change_12_listener() {\n      i0.ɵɵrestoreView(_r174);\n      const ctx_r176 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r176.updateConversationSettings());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Recevoir des notifications\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 320)(16, \"label\")(17, \"input\", 321);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_74_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r174);\n      const ctx_r177 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r177.conversationSettings.soundNotifications = $event);\n    })(\"change\", function MessageChatComponent_div_74_Template_input_change_17_listener() {\n      i0.ɵɵrestoreView(_r174);\n      const ctx_r178 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r178.updateConversationSettings());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Sons de notification\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 319)(21, \"h4\");\n    i0.ɵɵtext(22, \"Confidentialit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 320)(24, \"label\")(25, \"input\", 321);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_74_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r174);\n      const ctx_r179 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r179.conversationSettings.readReceipts = $event);\n    })(\"change\", function MessageChatComponent_div_74_Template_input_change_25_listener() {\n      i0.ɵɵrestoreView(_r174);\n      const ctx_r180 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r180.updateConversationSettings());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \"Accus\\u00E9s de lecture\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 320)(29, \"label\")(30, \"input\", 321);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_74_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r174);\n      const ctx_r181 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r181.conversationSettings.typingIndicators = $event);\n    })(\"change\", function MessageChatComponent_div_74_Template_input_change_30_listener() {\n      i0.ɵɵrestoreView(_r174);\n      const ctx_r182 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r182.updateConversationSettings());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \"Indicateurs de frappe\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r34.conversationSettings.notifications);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r34.conversationSettings.soundNotifications);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r34.conversationSettings.readReceipts);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r34.conversationSettings.typingIndicators);\n  }\n}\nconst _c11 = function (a0) {\n  return {\n    \"text-[#4f5fad] dark:text-[#6d78c9]\": a0\n  };\n};\nconst _c12 = function (a0) {\n  return {\n    open: a0\n  };\n};\nexport class MessageChatComponent {\n  // Getter pour compatibilité\n  get availableReactions() {\n    return this.c.reactions;\n  }\n  // Emojis du service\n  get commonEmojis() {\n    return this.MessageService.getCommonEmojis();\n  }\n  // Configuration des boutons d'action de l'en-tête\n  getHeaderActions() {\n    return [{\n      class: 'btn-audio-call',\n      icon: 'fas fa-phone-alt',\n      title: 'Appel audio',\n      onClick: () => this.initiateCall('AUDIO'),\n      isActive: false\n    }, {\n      class: 'btn-video-call',\n      icon: 'fas fa-video',\n      title: 'Appel vidéo',\n      onClick: () => this.initiateCall('VIDEO'),\n      isActive: false\n    }, {\n      class: 'btn-search',\n      icon: 'fas fa-search',\n      title: 'Rechercher',\n      onClick: () => this.toggleSearchBar(),\n      isActive: this.showSearchBar,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-pinned relative',\n      icon: 'fas fa-thumbtack',\n      title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n      onClick: () => this.togglePinnedMessages(),\n      isActive: this.showPinnedMessages,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.getPinnedMessagesCount() > 0 ? {\n        count: this.getPinnedMessagesCount(),\n        class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n        animate: false\n      } : null\n    }, {\n      class: 'btn-notifications relative',\n      icon: 'fas fa-bell',\n      title: 'Notifications',\n      onClick: () => this.toggleNotificationPanel(),\n      isActive: this.showNotificationPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.unreadNotificationCount > 0 ? {\n        count: this.unreadNotificationCount,\n        class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n        animate: true\n      } : null\n    }, {\n      class: 'btn-history relative',\n      icon: 'fas fa-history',\n      title: 'Historique des appels',\n      onClick: () => this.toggleCallHistoryPanel(),\n      isActive: this.showCallHistoryPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-stats relative',\n      icon: 'fas fa-chart-bar',\n      title: \"Statistiques d'appels\",\n      onClick: () => this.toggleCallStatsPanel(),\n      isActive: this.showCallStatsPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-voice-messages relative',\n      icon: 'fas fa-microphone',\n      title: 'Messages vocaux',\n      onClick: () => this.toggleVoiceMessagesPanel(),\n      isActive: this.showVoiceMessagesPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.voiceMessages.length > 0 ? {\n        count: this.voiceMessages.length,\n        class: 'bg-[#4f5fad]',\n        animate: false\n      } : null\n    }];\n  }\n  constructor(MessageService, route, authService, fb, statusService, router, toastService, cdr) {\n    this.MessageService = MessageService;\n    this.route = route;\n    this.authService = authService;\n    this.fb = fb;\n    this.statusService = statusService;\n    this.router = router;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    this.messages = [];\n    this.conversation = null;\n    this.loading = true;\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    this.selectedFile = null;\n    this.previewUrl = null;\n    this.isUploading = false;\n    this.isTyping = false;\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.MAX_MESSAGES_PER_SIDE = 5;\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.MAX_TOTAL_MESSAGES = 100;\n    this.currentPage = 1;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.subscriptions = new Subscription();\n    // Interface\n    this.selectedTheme = 'theme-default';\n    // États\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n    this.showEmojiPicker = false;\n    this.showSearchBar = false;\n    this.showPinnedMessages = false;\n    this.showStatusSelector = false;\n    this.showNotificationPanel = false;\n    this.showNotificationSettings = false;\n    this.showUserStatusPanel = false;\n    this.showCallHistoryPanel = false;\n    this.showCallStatsPanel = false;\n    this.showVoiceMessagesPanel = false;\n    // Appels\n    this.incomingCall = null;\n    this.activeCall = null;\n    this.showCallModal = false;\n    this.showActiveCallModal = false;\n    this.isCallMuted = false;\n    this.isVideoEnabled = true;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // Notifications et messages\n    this.notifications = [];\n    this.unreadNotificationCount = 0;\n    this.selectedNotifications = new Set();\n    this.showDeleteConfirmModal = false;\n    this.editingMessageId = null;\n    this.editingContent = '';\n    this.replyingToMessage = null;\n    // Recherche\n    this.searchQuery = '';\n    this.isSearching = false;\n    this.searchMode = false;\n    this.searchResults = [];\n    // Panneaux\n    this.pinnedMessages = [];\n    this.showReactionPicker = {};\n    this.showDeleteConfirm = {};\n    this.showPinConfirm = {};\n    this.isPinning = {};\n    this.showMessageOptions = {};\n    // Variables de transfert\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n    this.availableConversations = [];\n    this.isForwarding = false;\n    this.isLoadingConversations = false;\n    // Constantes optimisées\n    this.c = {\n      reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n      delays: {\n        away: 300000,\n        typing: 3000,\n        scroll: 100,\n        anim: 300\n      },\n      error: 'Erreur. Veuillez réessayer.',\n      trackById: (i, item) => item?.id || i.toString(),\n      notifications: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      status: {\n        online: {\n          text: 'En ligne',\n          color: 'text-green-500',\n          icon: 'fas fa-circle',\n          label: 'En ligne',\n          description: 'Disponible pour discuter'\n        },\n        offline: {\n          text: 'Hors ligne',\n          color: 'text-gray-500',\n          icon: 'far fa-circle',\n          label: 'Hors ligne',\n          description: 'Invisible pour tous'\n        },\n        away: {\n          text: 'Absent',\n          color: 'text-yellow-500',\n          icon: 'fas fa-clock',\n          label: 'Absent',\n          description: 'Absent temporairement'\n        },\n        busy: {\n          text: 'Occupé',\n          color: 'text-red-500',\n          icon: 'fas fa-minus-circle',\n          label: 'Occupé',\n          description: 'Ne pas déranger'\n        }\n      },\n      themes: [{\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad',\n        hoverColor: '#4f5fad'\n      }, {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d',\n        hoverColor: '#ff6b9d'\n      }, {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6',\n        hoverColor: '#3d85c6'\n      }, {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f',\n        hoverColor: '#6aa84f'\n      }],\n      calls: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      },\n      // Alias pour compatibilité\n      notificationConfig: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      callStatusColors: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      }\n    };\n    // Variables de notification\n    this.notificationFilter = 'all';\n    this.isLoadingNotifications = false;\n    this.isMarkingAsRead = false;\n    this.isDeletingNotifications = false;\n    this.hasMoreNotifications = false;\n    this.notificationSounds = true;\n    this.notificationPreview = true;\n    this.autoMarkAsRead = true;\n    // Variables d'appel\n    this.isCallMinimized = false;\n    this.callQuality = 'connecting';\n    this.showCallControls = false;\n    // Variables de statut utilisateur\n    this.onlineUsers = new Map();\n    this.currentUserStatus = 'online';\n    this.lastActivityTime = new Date();\n    this.autoAwayTimeout = null;\n    this.isUpdatingStatus = false;\n    this.callHistory = [];\n    this.voiceMessages = [];\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.statusFilterType = 'all';\n    // Services de fichiers optimisés\n    this.f = {\n      getIcon: t => this.MessageService.getFileIcon(t),\n      getType: t => this.MessageService.getFileType(t)\n    };\n    this.getFileIcon = this.f.getIcon;\n    this.getFileType = this.f.getType;\n    this.isCurrentlyTyping = false;\n    this.TYPING_DELAY = 500;\n    this.TYPING_TIMEOUT = 3000;\n    // Méthodes de basculement principales consolidées\n    this.mainToggleMethods = {\n      themeSelector: () => this.togglePanel('theme'),\n      mainMenu: () => this.togglePanel('menu'),\n      emojiPicker: () => this.togglePanel('emoji')\n    };\n    this.toggleThemeSelector = this.mainToggleMethods.themeSelector;\n    this.toggleMainMenu = this.mainToggleMethods.mainMenu;\n    this.toggleEmojiPicker = this.mainToggleMethods.emojiPicker;\n    // Méthodes toggle consolidées\n    this.toggleMethods = {\n      pinnedMessages: () => this.showPinnedMessages = !this.showPinnedMessages,\n      searchBar: () => {\n        this.togglePanel('search');\n        if (!this.showSearchBar) this.clearSearch();\n      },\n      statusSelector: () => this.togglePanel('status'),\n      notificationSettings: () => this.showNotificationSettings = !this.showNotificationSettings,\n      userStatusPanel: () => this.showUserStatusPanel = !this.showUserStatusPanel,\n      callMinimize: () => this.isCallMinimized = !this.isCallMinimized,\n      callHistoryPanel: () => this.showCallHistoryPanel = !this.showCallHistoryPanel,\n      callStatsPanel: () => this.showCallStatsPanel = !this.showCallStatsPanel,\n      voiceMessagesPanel: () => this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel\n    };\n    this.togglePinnedMessages = this.toggleMethods.pinnedMessages;\n    this.toggleSearchBar = this.toggleMethods.searchBar;\n    this.toggleStatusSelector = this.toggleMethods.statusSelector;\n    this.toggleNotificationSettings = this.toggleMethods.notificationSettings;\n    this.toggleUserStatusPanel = this.toggleMethods.userStatusPanel;\n    this.toggleCallMinimize = this.toggleMethods.callMinimize;\n    this.toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;\n    this.toggleCallStatsPanel = this.toggleMethods.callStatsPanel;\n    this.toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;\n    this.conversationMethods = {\n      showInfo: () => this.showDevelopmentFeature('Informations de conversation'),\n      showSettings: () => this.showDevelopmentFeature('Paramètres de conversation'),\n      clear: () => {\n        if (!this.conversation?.id || this.messages.length === 0) {\n          this.toastService.showWarning('Aucune conversation à vider');\n          return;\n        }\n        if (confirm('Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.')) {\n          this.messages = [];\n          this.showMainMenu = false;\n          this.toastService.showSuccess('Conversation vidée avec succès');\n        }\n      },\n      export: () => {\n        if (!this.conversation?.id || this.messages.length === 0) {\n          this.toastService.showWarning('Aucune conversation à exporter');\n          return;\n        }\n        const conversationName = this.conversation.isGroup ? this.conversation.groupName || 'Groupe sans nom' : this.otherParticipant?.username || 'Conversation privée';\n        const exportData = {\n          conversation: {\n            id: this.conversation.id,\n            name: conversationName,\n            isGroup: this.conversation.isGroup,\n            participants: this.conversation.participants,\n            createdAt: this.conversation.createdAt\n          },\n          messages: this.messages.map(msg => ({\n            id: msg.id,\n            content: msg.content,\n            sender: msg.sender,\n            timestamp: msg.timestamp,\n            type: msg.type\n          })),\n          exportedAt: new Date().toISOString(),\n          exportedBy: this.currentUserId\n        };\n        const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n          type: 'application/json'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        const safeFileName = conversationName.replace(/[^a-z0-9]/gi, '_').toLowerCase();\n        const dateStr = new Date().toISOString().split('T')[0];\n        link.download = `conversation-${safeFileName}-${dateStr}.json`;\n        link.click();\n        window.URL.revokeObjectURL(url);\n        this.showMainMenu = false;\n        this.toastService.showSuccess('Conversation exportée avec succès');\n      }\n    };\n    // Template methods\n    this.toggleConversationInfo = this.conversationMethods.showInfo;\n    this.toggleConversationSettings = this.conversationMethods.showSettings;\n    this.clearConversation = this.conversationMethods.clear;\n    this.exportConversation = this.conversationMethods.export;\n    // Service - méthodes optimisées\n    this.s = {\n      formatTime: t => this.MessageService.formatMessageTime(t),\n      formatActive: t => this.MessageService.formatLastActive(t),\n      formatDate: t => this.MessageService.formatMessageDate(t),\n      showDateHeader: i => this.MessageService.shouldShowDateHeader(this.messages, i),\n      getType: m => this.MessageService.getMessageType(m),\n      hasImage: m => this.MessageService.hasImage(m),\n      isVoice: m => this.MessageService.isVoiceMessage(m),\n      getVoiceUrl: m => this.MessageService.getVoiceMessageUrl(m),\n      getVoiceDuration: m => this.MessageService.getVoiceMessageDuration(m),\n      getVoiceHeight: i => this.MessageService.getVoiceBarHeight(i),\n      formatVoice: s => this.MessageService.formatVoiceDuration(s),\n      getImageUrl: m => this.MessageService.getImageUrl(m),\n      getTypeClass: m => this.MessageService.getMessageTypeClass(m, this.currentUserId)\n    };\n    // Méthodes exposées optimisées\n    this.formatMessageTime = this.s.formatTime;\n    this.formatLastActive = this.s.formatActive;\n    this.formatMessageDate = this.s.formatDate;\n    this.shouldShowDateHeader = this.s.showDateHeader;\n    this.getMessageType = this.s.getType;\n    this.hasImage = this.s.hasImage;\n    this.isVoiceMessage = this.s.isVoice;\n    this.getVoiceMessageUrl = this.s.getVoiceUrl;\n    this.getVoiceMessageDuration = this.s.getVoiceDuration;\n    this.getVoiceBarHeight = this.s.getVoiceHeight;\n    this.formatVoiceDuration = this.s.formatVoice;\n    this.getImageUrl = this.s.getImageUrl;\n    this.getMessageTypeClass = this.s.getTypeClass;\n    // Indicateurs de chargement consolidés\n    this.loadingIndicatorMethods = {\n      show: () => {\n        if (!document.getElementById('message-loading-indicator')) {\n          const indicator = document.createElement('div');\n          indicator.id = 'message-loading-indicator';\n          indicator.className = 'text-center py-2 text-gray-500 text-sm';\n          indicator.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n          this.messagesContainer?.nativeElement?.prepend(indicator);\n        }\n      },\n      hide: () => {\n        const indicator = document.getElementById('message-loading-indicator');\n        indicator?.parentNode?.removeChild(indicator);\n      }\n    };\n    this.showLoadingIndicator = this.loadingIndicatorMethods.show;\n    this.hideLoadingIndicator = this.loadingIndicatorMethods.hide;\n    // Méthodes d'enregistrement vocal consolidées\n    this.voiceRecordingMethods = {\n      toggle: () => {\n        this.isRecordingVoice = !this.isRecordingVoice;\n        if (!this.isRecordingVoice) this.voiceRecordingDuration = 0;\n      },\n      complete: audioBlob => {\n        if (!this.conversation?.id && !this.otherParticipant?.id) {\n          this.toastService.showError('No conversation or recipient selected');\n          this.isRecordingVoice = false;\n          return;\n        }\n        const receiverId = this.otherParticipant?.id || '';\n        this.MessageService.sendVoiceMessage(receiverId, audioBlob, this.conversation?.id, this.voiceRecordingDuration).subscribe({\n          next: message => {\n            this.isRecordingVoice = false;\n            this.voiceRecordingDuration = 0;\n            this.scrollToBottom(true);\n          },\n          error: error => {\n            this.toastService.showError('Failed to send voice message');\n            this.isRecordingVoice = false;\n          }\n        });\n      },\n      cancel: () => {\n        this.isRecordingVoice = false;\n        this.voiceRecordingDuration = 0;\n      }\n    };\n    this.toggleVoiceRecording = this.voiceRecordingMethods.toggle;\n    this.onVoiceRecordingComplete = this.voiceRecordingMethods.complete;\n    this.onVoiceRecordingCancelled = this.voiceRecordingMethods.cancel;\n    // Méthodes d'appel consolidées\n    this.callMethods = {\n      initiate: type => {\n        if (!this.otherParticipant?.id) return;\n        this.MessageService.initiateCall(this.otherParticipant.id, type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO, this.conversation?.id).subscribe({\n          next: call => {},\n          error: () => this.toastService.showError(this.c.error)\n        });\n      },\n      accept: () => {\n        if (!this.incomingCall) return;\n        this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n          next: call => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n            this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n            this.callQuality = 'connecting';\n            this.toastService.showSuccess('Appel connecté');\n          },\n          error: () => {\n            this.toastService.showError(this.c.error);\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        });\n      },\n      reject: () => {\n        if (!this.incomingCall) return;\n        this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n          next: call => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          },\n          error: error => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        });\n      },\n      end: () => {\n        const sub = this.MessageService.activeCall$.subscribe(call => {\n          if (call) {\n            this.MessageService.endCall(call.id).subscribe({\n              next: call => {},\n              error: error => {}\n            });\n          }\n        });\n        sub.unsubscribe();\n      }\n    };\n    this.initiateCall = this.callMethods.initiate;\n    this.acceptCall = this.callMethods.accept;\n    this.rejectCall = this.callMethods.reject;\n    this.endCall = this.callMethods.end;\n    // Méthodes de contrôle d'appel consolidées\n    this.callControlMethods = {\n      toggleMute: () => {\n        this.isCallMuted = !this.isCallMuted;\n        this.callControlMethods.updateMedia();\n        this.toastService.showInfo(this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé');\n      },\n      toggleVideo: () => {\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.callControlMethods.updateMedia();\n        this.toastService.showInfo(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      updateMedia: () => {\n        this.MessageService.toggleMedia(this.activeCall?.id, !this.isCallMuted, this.isVideoEnabled).subscribe({\n          next: () => {},\n          error: error => console.error('Erreur lors de la mise à jour des médias:', error)\n        });\n      }\n    };\n    this.toggleCallMute = this.callControlMethods.toggleMute;\n    this.toggleCallVideo = this.callControlMethods.toggleVideo;\n    this.updateCallMedia = this.callControlMethods.updateMedia;\n    // Méthodes de timer consolidées\n    this.timerMethods = {\n      startCallTimer: () => {\n        this.callDuration = 0;\n        this.callTimer = setInterval(() => {\n          this.callDuration++;\n          if (this.callDuration === 3 && this.callQuality === 'connecting') {\n            this.callQuality = 'excellent';\n          }\n        }, 1000);\n      },\n      stopCallTimer: () => {\n        if (this.callTimer) {\n          clearInterval(this.callTimer);\n          this.callTimer = null;\n        }\n      },\n      resetCallState: () => this.callDuration = 0\n    };\n    this.startCallTimerMethod = this.timerMethods.startCallTimer;\n    this.stopCallTimerMethod = this.timerMethods.stopCallTimer;\n    this.resetCallStateMethod = this.timerMethods.resetCallState;\n    // Notifications\n    // Méthode de basculement de notification consolidée\n    this.notificationToggleMethod = {\n      togglePanel: () => {\n        this.togglePanel('notification');\n        if (this.showNotificationPanel) {\n          this.loadNotifications();\n        }\n      }\n    };\n    this.toggleNotificationPanel = this.notificationToggleMethod.togglePanel;\n    // Méthodes de notification consolidées\n    this.notificationMethods = {\n      loadMore: () => this.loadNotifications(),\n      updateCount: () => this.unreadNotificationCount = this.notifications.filter(n => !n.isRead).length,\n      getFiltered: () => this.notifications,\n      toggleSelection: notificationId => {\n        if (this.selectedNotifications.has(notificationId)) {\n          this.selectedNotifications.delete(notificationId);\n        } else {\n          this.selectedNotifications.add(notificationId);\n        }\n      },\n      toggleSelectAll: () => {\n        const filteredNotifications = this.notificationMethods.getFiltered();\n        const allSelected = filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n        if (allSelected) {\n          filteredNotifications.forEach(n => this.selectedNotifications.delete(n.id));\n        } else {\n          filteredNotifications.forEach(n => this.selectedNotifications.add(n.id));\n        }\n      },\n      areAllSelected: () => {\n        const filteredNotifications = this.notificationMethods.getFiltered();\n        return filteredNotifications.length > 0 && filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n      }\n    };\n    this.loadMoreNotifications = this.notificationMethods.loadMore;\n    this.updateNotificationCount = this.notificationMethods.updateCount;\n    this.getFilteredNotifications = this.notificationMethods.getFiltered;\n    this.toggleNotificationSelection = this.notificationMethods.toggleSelection;\n    this.toggleSelectAllNotifications = this.notificationMethods.toggleSelectAll;\n    this.areAllNotificationsSelected = this.notificationMethods.areAllSelected;\n    // Méthodes de marquage consolidées\n    this.markingMethods = {\n      markSelected: () => {\n        const selectedIds = Array.from(this.selectedNotifications);\n        if (selectedIds.length === 0) {\n          this.toastService.showWarning('Aucune notification sélectionnée');\n          return;\n        }\n        this.markingMethods.markAsRead(selectedIds, () => {\n          this.selectedNotifications.clear();\n          this.toastService.showSuccess(`${selectedIds.length} notification(s) marquée(s) comme lue(s)`);\n        });\n      },\n      markAll: () => {\n        const unreadNotifications = this.notifications.filter(n => !n.isRead);\n        if (unreadNotifications.length === 0) {\n          this.toastService.showInfo('Aucune notification non lue');\n          return;\n        }\n        const unreadIds = unreadNotifications.map(n => n.id);\n        this.markingMethods.markAsRead(unreadIds, () => {\n          this.toastService.showSuccess('Toutes les notifications ont été marquées comme lues');\n        });\n      },\n      markAsRead: (ids, onSuccess) => {\n        const markSub = this.MessageService.markAsRead(ids).subscribe({\n          next: result => {\n            this.notifications = this.notifications.map(n => ids.includes(n.id) ? {\n              ...n,\n              isRead: true,\n              readAt: new Date()\n            } : n);\n            this.updateNotificationCount();\n            onSuccess();\n          },\n          error: error => {\n            this.toastService.showError('Erreur lors du marquage des notifications');\n          }\n        });\n        this.subscriptions.add(markSub);\n      }\n    };\n    this.markSelectedAsRead = this.markingMethods.markSelected;\n    this.markAllAsRead = this.markingMethods.markAll;\n    // Méthodes de suppression de notifications consolidées\n    this.notificationDeleteMethods = {\n      showDeleteSelectedConfirmation: () => {\n        if (this.selectedNotifications.size === 0) {\n          this.toastService.showWarning('Aucune notification sélectionnée');\n          return;\n        }\n        this.showDeleteConfirmModal = true;\n      },\n      deleteSelected: () => {\n        const selectedIds = Array.from(this.selectedNotifications);\n        if (selectedIds.length === 0) return;\n        this.isDeletingNotifications = true;\n        this.showDeleteConfirmModal = false;\n        const deleteSub = this.MessageService.deleteMultipleNotifications(selectedIds).subscribe({\n          next: result => {\n            this.notifications = this.notifications.filter(n => !selectedIds.includes(n.id));\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(`${result.count} notification(s) supprimée(s)`);\n          },\n          error: error => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError('Erreur lors de la suppression des notifications');\n          }\n        });\n        this.subscriptions.add(deleteSub);\n      },\n      deleteOne: notificationId => {\n        const deleteSub = this.MessageService.deleteNotification(notificationId).subscribe({\n          next: result => {\n            this.notifications = this.notifications.filter(n => n.id !== notificationId);\n            this.selectedNotifications.delete(notificationId);\n            this.updateNotificationCount();\n            this.toastService.showSuccess('Notification supprimée');\n          },\n          error: error => {\n            this.toastService.showError('Erreur lors de la suppression de la notification');\n          }\n        });\n        this.subscriptions.add(deleteSub);\n      },\n      deleteAll: () => {\n        if (this.notifications.length === 0) return;\n        if (!confirm('Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.')) {\n          return;\n        }\n        this.isDeletingNotifications = true;\n        const deleteAllSub = this.MessageService.deleteAllNotifications().subscribe({\n          next: result => {\n            this.notifications = [];\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(`${result.count} notifications supprimées avec succès`);\n          },\n          error: error => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError('Erreur lors de la suppression de toutes les notifications');\n          }\n        });\n        this.subscriptions.add(deleteAllSub);\n      },\n      cancel: () => this.showDeleteConfirmModal = false\n    };\n    this.showDeleteSelectedConfirmation = this.notificationDeleteMethods.showDeleteSelectedConfirmation;\n    this.deleteSelectedNotifications = this.notificationDeleteMethods.deleteSelected;\n    this.deleteNotification = this.notificationDeleteMethods.deleteOne;\n    this.deleteAllNotifications = this.notificationDeleteMethods.deleteAll;\n    this.cancelDeleteNotifications = this.notificationDeleteMethods.cancel;\n    // Méthodes utilitaires de notification consolidées\n    this.notificationUtilMethods = {\n      formatDate: date => this.MessageService.formatLastActive(date),\n      getIcon: type => this.c.notifications[type]?.icon || 'fas fa-bell',\n      getColor: type => this.c.notifications[type]?.color || 'text-cyan-500',\n      trackById: (index, notification) => this.c.trackById(0, notification)\n    };\n    this.formatNotificationDate = this.notificationUtilMethods.formatDate;\n    this.getNotificationIcon = this.notificationUtilMethods.getIcon;\n    this.getNotificationColor = this.notificationUtilMethods.getColor;\n    this.trackByNotificationId = this.notificationUtilMethods.trackById;\n    // Méthodes de gestion des panneaux consolidées\n    this.panelMethods = {\n      getActivePanels: () => {\n        const panels = [];\n        if (this.showUserStatusPanel) panels.push({\n          key: 'userStatus',\n          title: 'Utilisateurs',\n          icon: 'fas fa-users',\n          closeAction: () => this.showUserStatusPanel = false\n        });\n        if (this.showCallHistoryPanel) panels.push({\n          key: 'callHistory',\n          title: 'Historique des appels',\n          icon: 'fas fa-history',\n          closeAction: () => this.showCallHistoryPanel = false\n        });\n        if (this.showCallStatsPanel) panels.push({\n          key: 'callStats',\n          title: \"Statistiques d'appels\",\n          icon: 'fas fa-chart-bar',\n          closeAction: () => this.showCallStatsPanel = false\n        });\n        if (this.showVoiceMessagesPanel) panels.push({\n          key: 'voiceMessages',\n          title: 'Messages vocaux',\n          icon: 'fas fa-microphone',\n          closeAction: () => this.showVoiceMessagesPanel = false\n        });\n        return panels;\n      },\n      getStatusOptions: () => Object.entries(this.c.status).map(([key, config]) => ({\n        key,\n        ...config\n      })),\n      getThemeOptions: () => this.c.themes\n    };\n    this.getActivePanels = this.panelMethods.getActivePanels;\n    this.getStatusOptions = this.panelMethods.getStatusOptions;\n    this.getThemeOptions = this.panelMethods.getThemeOptions;\n    // Méthodes de statut simplifiées\n    this.statusMethods = {\n      getText: status => this.c.status[status]?.text || 'Inconnu',\n      getColor: status => this.c.status[status]?.color || 'text-gray-400',\n      getIcon: status => this.c.status[status]?.icon || 'fas fa-question-circle'\n    };\n    this.getStatusText = this.statusMethods.getText;\n    this.getStatusColor = this.statusMethods.getColor;\n    this.getStatusIcon = this.statusMethods.getIcon;\n    // Méthodes utilitaires consolidées\n    this.utilityMethods = {\n      formatLastSeen: lastActive => lastActive ? this.MessageService.formatLastActive(lastActive) : 'Jamais vu',\n      getOnlineUsersCount: () => Array.from(this.onlineUsers.values()).filter(user => user.isOnline).length,\n      getFilteredUsers: () => Array.from(this.onlineUsers.values()),\n      setStatusFilter: filter => this.statusFilterType = filter\n    };\n    this.formatLastSeen = this.utilityMethods.formatLastSeen;\n    this.getOnlineUsersCount = this.utilityMethods.getOnlineUsersCount;\n    this.setStatusFilter = this.utilityMethods.setStatusFilter;\n    this.getFilteredUsers = this.utilityMethods.getFilteredUsers;\n    // Méthodes de réponse et transfert consolidées\n    this.replyForwardMethods = {\n      startReply: message => this.replyingToMessage = message,\n      cancelReply: () => this.replyingToMessage = null,\n      openForwardModal: message => {\n        this.forwardingMessage = message;\n        this.showForwardModal = true;\n      },\n      closeForwardModal: () => {\n        this.showForwardModal = false;\n        this.forwardingMessage = null;\n        this.selectedConversations = [];\n      }\n    };\n    this.startReplyToMessage = this.replyForwardMethods.startReply;\n    this.cancelReply = this.replyForwardMethods.cancelReply;\n    this.openForwardModal = this.replyForwardMethods.openForwardModal;\n    this.closeForwardModal = this.replyForwardMethods.closeForwardModal;\n    // Messages - méthodes consolidées\n    this.messageMethods = {\n      getPinIcon: message => this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack',\n      getPinDisplayText: message => this.isMessagePinned(message) ? 'Désépingler' : 'Épingler',\n      canEditMessage: message => message.sender?.id === this.currentUserId,\n      isMessagePinned: message => message.isPinned || false\n    };\n    this.getPinIcon = this.messageMethods.getPinIcon;\n    this.getPinDisplayText = this.messageMethods.getPinDisplayText;\n    this.canEditMessage = this.messageMethods.canEditMessage;\n    this.isMessagePinned = this.messageMethods.isMessagePinned;\n    // Méthodes d'édition consolidées\n    this.editMethods = {\n      startEditMessage: message => {\n        this.editingMessageId = message.id;\n        this.editingContent = message.content;\n      },\n      cancelEditMessage: () => {\n        this.editingMessageId = null;\n        this.editingContent = '';\n      },\n      saveEditMessage: messageId => this.editMethods.cancelEditMessage(),\n      onEditKeyPress: (event, messageId) => {\n        if (event.key === 'Enter' && !event.shiftKey) {\n          event.preventDefault();\n          this.editMethods.saveEditMessage(messageId);\n        } else if (event.key === 'Escape') {\n          this.editMethods.cancelEditMessage();\n        }\n      }\n    };\n    this.startEditMessage = this.editMethods.startEditMessage;\n    this.cancelEditMessage = this.editMethods.cancelEditMessage;\n    this.saveEditMessage = this.editMethods.saveEditMessage;\n    this.onEditKeyPress = this.editMethods.onEditKeyPress;\n    // Utilitaires d'appel consolidées\n    this.callUtilities = {\n      getCallStatusColor: status => this.c.callStatusColors[status] || 'text-gray-500',\n      getCallTypeIcon: type => type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',\n      formatCallDuration: duration => {\n        if (!duration) return '00:00';\n        const minutes = Math.floor(duration / 60);\n        const seconds = duration % 60;\n        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      },\n      formatCallDate: timestamp => this.MessageService.formatMessageDate(timestamp)\n    };\n    this.getCallStatusColor = this.callUtilities.getCallStatusColor;\n    this.getCallTypeIcon = this.callUtilities.getCallTypeIcon;\n    this.formatCallDuration = this.callUtilities.formatCallDuration;\n    this.formatCallDate = this.callUtilities.formatCallDate;\n    // Méthodes d'événements consolidées\n    this.eventMethods = {\n      onDocumentClick: event => {\n        const target = event.target;\n        const closeConfigs = [{\n          selectors: ['.theme-selector-menu', '.btn-theme'],\n          property: 'showThemeSelector'\n        }, {\n          selectors: ['.emoji-picker', '.btn-emoji'],\n          property: 'showEmojiPicker'\n        }];\n        closeConfigs.forEach(config => {\n          const isClickOutside = !config.selectors.some(selector => target.closest(selector));\n          if (isClickOutside) {\n            this[config.property] = false;\n          }\n        });\n      },\n      confirmDeleteMessage: messageId => {\n        this.showDeleteConfirm[messageId] = false;\n      }\n    };\n    this.onDocumentClick = this.eventMethods.onDocumentClick;\n    this.confirmDeleteMessage = this.eventMethods.confirmDeleteMessage;\n    // Méthodes de réaction consolidées\n    this.reactionMethods = {\n      getUniqueReactions: message => message.reactions || [],\n      onReactionClick: (messageId, emoji) => {\n        // Implémentation des réactions\n      },\n      hasUserReacted: (message, emoji) => false\n    };\n    this.getUniqueReactions = this.reactionMethods.getUniqueReactions;\n    this.onReactionClick = this.reactionMethods.onReactionClick;\n    this.hasUserReacted = this.reactionMethods.hasUserReacted;\n    // Méthodes de conversation consolidées\n    this.conversationSelectionMethods = {\n      areAllSelected: () => this.selectedConversations.length === this.availableConversations.length,\n      selectAll: () => this.selectedConversations = this.availableConversations.map(c => c.id),\n      deselectAll: () => this.selectedConversations = [],\n      toggle: conversationId => {\n        const index = this.selectedConversations.indexOf(conversationId);\n        index > -1 ? this.selectedConversations.splice(index, 1) : this.selectedConversations.push(conversationId);\n      },\n      isSelected: conversationId => this.selectedConversations.includes(conversationId),\n      getDisplayImage: conversation => conversation.image || 'assets/images/default-avatar.png',\n      getDisplayName: conversation => conversation.name || 'Conversation',\n      forwardMessage: () => {\n        this.isForwarding = true;\n        setTimeout(() => {\n          this.isForwarding = false;\n          this.closeForwardModal();\n        }, 1000);\n      }\n    };\n    this.areAllConversationsSelected = this.conversationSelectionMethods.areAllSelected;\n    this.selectAllConversations = this.conversationSelectionMethods.selectAll;\n    this.deselectAllConversations = this.conversationSelectionMethods.deselectAll;\n    this.toggleConversationSelection = this.conversationSelectionMethods.toggle;\n    this.isConversationSelected = this.conversationSelectionMethods.isSelected;\n    this.getConversationDisplayImage = this.conversationSelectionMethods.getDisplayImage;\n    this.getConversationDisplayName = this.conversationSelectionMethods.getDisplayName;\n    this.forwardMessage = this.conversationSelectionMethods.forwardMessage;\n    // Méthodes de notification simplifiées consolidées\n    this.simpleNotificationMethods = {\n      onCallMouseMove: () => this.showCallControls = true,\n      saveNotificationSettings: () => {},\n      setNotificationFilter: filter => this.notificationFilter = filter\n    };\n    this.onCallMouseMove = this.simpleNotificationMethods.onCallMouseMove;\n    this.saveNotificationSettings = this.simpleNotificationMethods.saveNotificationSettings;\n    this.setNotificationFilter = this.simpleNotificationMethods.setNotificationFilter;\n    // Méthodes de recherche consolidées\n    this.searchMethods = {\n      onInput: event => {\n        this.searchQuery = event.target.value;\n        this.searchQuery.length >= 2 ? this.searchMethods.perform() : this.searchMethods.clear();\n      },\n      onKeyPress: event => {\n        if (event.key === 'Enter') {\n          this.searchMethods.perform();\n        } else if (event.key === 'Escape') {\n          this.searchMethods.clear();\n        }\n      },\n      perform: () => {\n        this.isSearching = true;\n        this.searchMode = true;\n        setTimeout(() => {\n          this.searchResults = this.messages.filter(m => m.content?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n          this.isSearching = false;\n        }, 500);\n      },\n      clear: () => {\n        this.searchQuery = '';\n        this.searchResults = [];\n        this.isSearching = false;\n        this.searchMode = false;\n      }\n    };\n    this.onSearchInput = this.searchMethods.onInput;\n    this.onSearchKeyPress = this.searchMethods.onKeyPress;\n    this.performSearch = this.searchMethods.perform;\n    this.clearSearch = this.searchMethods.clear;\n    // Méthodes utilitaires finales consolidées\n    this.finalUtilityMethods = {\n      navigateToMessage: messageId => {\n        // Navigation vers un message spécifique\n      },\n      scrollToPinnedMessage: messageId => {\n        // Défilement vers un message épinglé\n      },\n      getPinnedMessagesCount: () => this.pinnedMessages.length\n    };\n    this.navigateToMessage = this.finalUtilityMethods.navigateToMessage;\n    this.scrollToPinnedMessage = this.finalUtilityMethods.scrollToPinnedMessage;\n    this.getPinnedMessagesCount = this.finalUtilityMethods.getPinnedMessagesCount;\n    // Méthodes de recherche et réaction consolidées\n    this.searchAndReactionMethods = {\n      highlightSearchTerms: (content, query) => {\n        if (!query) return content;\n        const regex = new RegExp(`(${query})`, 'gi');\n        return content.replace(regex, '<mark>$1</mark>');\n      },\n      toggleReactionPicker: messageId => this.showReactionPicker[messageId] = !this.showReactionPicker[messageId],\n      reactToMessage: (messageId, emoji) => this.showReactionPicker[messageId] = false,\n      toggleMessageOptions: messageId => this.showMessageOptions[messageId] = !this.showMessageOptions[messageId]\n    };\n    this.highlightSearchTerms = this.searchAndReactionMethods.highlightSearchTerms;\n    this.toggleReactionPicker = this.searchAndReactionMethods.toggleReactionPicker;\n    this.reactToMessage = this.searchAndReactionMethods.reactToMessage;\n    this.toggleMessageOptions = this.searchAndReactionMethods.toggleMessageOptions;\n    // Confirmations consolidées\n    this.confirmationMethods = {\n      showPinConfirmation: messageId => this.showPinConfirm[messageId] = true,\n      cancelPinConfirmation: messageId => this.showPinConfirm[messageId] = false,\n      showDeleteConfirmation: messageId => this.showDeleteConfirm[messageId] = true,\n      cancelDeleteMessage: messageId => this.showDeleteConfirm[messageId] = false\n    };\n    this.showPinConfirmation = this.confirmationMethods.showPinConfirmation;\n    this.cancelPinConfirmation = this.confirmationMethods.cancelPinConfirmation;\n    this.showDeleteConfirmation = this.confirmationMethods.showDeleteConfirmation;\n    this.cancelDeleteMessage = this.confirmationMethods.cancelDeleteMessage;\n    // Méthodes de nettoyage optimisées\n    this.cleanup = {\n      clearTimeouts: () => [this.typingTimeout, this.autoAwayTimeout, this.callTimer].forEach(t => t && clearTimeout(t)),\n      setUserOffline: () => this.currentUserId && this.MessageService.setUserOffline(this.currentUserId).subscribe(),\n      stopTypingIndicator: () => {\n        if (this.isCurrentlyTyping && this.conversation?.id) {\n          this.isCurrentlyTyping = false;\n          clearTimeout(this.typingTimer);\n          this.MessageService.stopTyping(this.conversation.id).subscribe({\n            next: () => {},\n            error: error => {}\n          });\n        }\n      }\n    };\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]]\n    });\n  }\n  ngOnInit() {\n    this.currentUserId = this.authService.getCurrentUserId();\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n    }\n    this.subscribeToNotifications();\n    this.subscribeToUserStatus();\n    this.initializeUserStatus();\n    this.startActivityTracking();\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n    const routeSub = this.route.params.pipe(filter(params => params['id']), distinctUntilChanged(), switchMap(params => {\n      this.loading = true;\n      this.messages = [];\n      this.currentPage = 1;\n      this.hasMoreMessages = true;\n      return this.MessageService.getConversation(params['id'], this.MAX_MESSAGES_TO_LOAD, this.currentPage);\n    })).subscribe({\n      next: conversation => {\n        this.handleConversationLoaded(conversation);\n      },\n      error: error => {\n        this.handleError('Failed to load conversation', error);\n      }\n    });\n    this.subscriptions.add(routeSub);\n  }\n  // Gestion centralisée des erreurs\n  handleError(message, error, resetLoading = true) {\n    console.error('MessageChat', message, error);\n    if (resetLoading) {\n      this.loading = false;\n      this.isUploading = false;\n      this.isLoadingMore = false;\n    }\n    this.error = error;\n    this.toastService.showError(message);\n  }\n  // Gestion centralisée des succès\n  handleSuccess(message, callback) {\n    if (message) {\n      this.toastService.showSuccess(message);\n    }\n    if (callback) {\n      callback();\n    }\n  }\n  handleConversationLoaded(conversation) {\n    this.conversation = conversation;\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n      this.messages = [];\n    } else {\n      const conversationMessages = [...(conversation?.messages || [])];\n      conversationMessages.sort((a, b) => {\n        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n        return timeA - timeB;\n      });\n      this.messages = conversationMessages;\n    }\n    this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n    this.markMessagesAsRead();\n    if (this.conversation?.id) {\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub = this.MessageService.subscribeToConversationUpdates(conversationId).subscribe({\n      next: updatedConversation => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages ? [...updatedConversation.messages] : [];\n        this.scrollToBottom();\n      },\n      error: error => {\n        this.toastService.showError('Connection to conversation updates lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToNewMessages(conversationId) {\n    const sub = this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n      next: newMessage => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n            const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          setTimeout(() => this.scrollToBottom(), 100);\n          if (newMessage.sender?.id !== this.currentUserId && newMessage.sender?._id !== this.currentUserId) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: error => {\n        this.toastService.showError('Connection to new messages lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToTypingIndicators(conversationId) {\n    const sub = this.MessageService.subscribeToTypingIndicator(conversationId).subscribe({\n      next: event => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  markMessagesAsRead() {\n    const unreadMessages = this.messages.filter(msg => !msg.isRead && (msg.receiver?.id === this.currentUserId || msg.receiver?._id === this.currentUserId));\n    unreadMessages.forEach(msg => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: error => {}\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (!file) return;\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError('Invalid file type. Only images, PDFs and Word docs are allowed');\n      return;\n    }\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n  removeAttachment() {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n  // Frappe\n  onTyping() {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n    const conversationId = this.conversation.id;\n    clearTimeout(this.typingTimer);\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {},\n        error: error => {}\n      });\n    }\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {},\n          error: error => {}\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n  // Panneaux\n  togglePanel(panelName, closeOthers = true) {\n    const panels = {\n      theme: 'showThemeSelector',\n      menu: 'showMainMenu',\n      emoji: 'showEmojiPicker',\n      notification: 'showNotificationPanel',\n      search: 'showSearchBar',\n      status: 'showStatusSelector'\n    };\n    const currentPanel = panels[panelName];\n    if (currentPanel) {\n      this[currentPanel] = !this[currentPanel];\n      if (closeOthers && this[currentPanel]) {\n        Object.values(panels).forEach(panel => {\n          if (panel !== currentPanel) {\n            this[panel] = false;\n          }\n        });\n      }\n    }\n  }\n  changeTheme(theme) {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    localStorage.setItem('chat-theme', theme);\n  }\n  // Conversation - méthode utilitaire\n  showDevelopmentFeature(featureName) {\n    this.showMainMenu = false;\n    this.toastService.showInfo(`${featureName} en cours de développement`);\n  }\n  sendMessage() {\n    if (this.messageForm.invalid && !this.selectedFile || !this.currentUserId || !this.otherParticipant?.id) {\n      return;\n    }\n    this.cleanup.stopTypingIndicator();\n    const content = this.messageForm.get('content')?.value;\n    const tempMessage = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient'\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true\n    };\n    if (this.selectedFile) {\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n        if (this.previewUrl) {\n          tempMessage.attachments = [{\n            id: 'temp-attachment',\n            url: this.previewUrl ? this.previewUrl.toString() : '',\n            type: MessageType.IMAGE,\n            name: this.selectedFile.name,\n            size: this.selectedFile.size\n          }];\n        }\n      }\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n    this.messages = [...this.messages, tempMessage];\n    const fileToSend = this.selectedFile;\n    this.messageForm.reset();\n    this.removeAttachment();\n    setTimeout(() => this.scrollToBottom(true), 50);\n    this.isUploading = true;\n    const sendSub = this.MessageService.sendMessage(this.otherParticipant.id, content, fileToSend || undefined, MessageType.TEXT, this.conversation?.id).subscribe({\n      next: message => {\n        this.updateMessageState(tempMessage.id, message);\n        this.isUploading = false;\n      },\n      error: error => {\n        this.updateMessageState(tempMessage.id, null, true);\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      }\n    });\n    this.subscriptions.add(sendSub);\n  }\n  // Méthode consolidée pour mettre à jour l'état des messages\n  updateMessageState(tempId, newMessage, isError = false) {\n    this.messages = this.messages.map(msg => {\n      if (msg.id === tempId) {\n        if (newMessage) {\n          return newMessage;\n        } else if (isError) {\n          return {\n            ...msg,\n            isPending: false,\n            isError: true\n          };\n        }\n      }\n      return msg;\n    });\n  }\n  // Défilement\n  onScroll(event) {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n    if (scrollTop < 50 && !this.isLoadingMore && this.conversation?.id && this.hasMoreMessages) {\n      this.showLoadingIndicator();\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n      this.isLoadingMore = true;\n      this.loadMoreMessages();\n      // Maintenir la position de défilement\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(firstVisibleMessage.id);\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({\n                block: 'center'\n              });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n  // Méthode pour trouver le premier message visible dans la vue\n  getFirstVisibleMessage() {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length) return null;\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find(m => m.id === messageId) || null;\n      }\n    }\n    return null;\n  }\n  // Méthode pour trouver un élément de message par ID\n  findMessageElement(messageId) {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(`[data-message-id=\"${messageId}\"]`);\n  }\n  // Charger plus de messages\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages) return;\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n    // Augmenter la page\n    this.currentPage++;\n    // Charger plus de messages\n    this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        if (conversation && conversation.messages && conversation.messages.length > 0) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n          // Créer un Set des IDs existants\n          const existingIds = new Set(oldMessages.map(msg => msg.id));\n          // Filtrer et trier les nouveaux messages\n          const newMessages = conversation.messages.filter(msg => !existingIds.has(msg.id)).sort((a, b) => {\n            const timeA = new Date(a.timestamp).getTime();\n            const timeB = new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages = newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: error => {\n        console.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      }\n    });\n  }\n  // Méthode utilitaire pour comparer les timestamps\n  isSameTimestamp(timestamp1, timestamp2) {\n    if (!timestamp1 || !timestamp2) return false;\n    try {\n      const time1 = timestamp1 instanceof Date ? timestamp1.getTime() : new Date(timestamp1).getTime();\n      const time2 = timestamp2 instanceof Date ? timestamp2.getTime() : new Date(timestamp2).getTime();\n      return Math.abs(time1 - time2) < 1000;\n    } catch (error) {\n      return false;\n    }\n  }\n  scrollToBottom(force = false) {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 150;\n        if (force || isScrolledToBottom) {\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth'\n          });\n        }\n      });\n    } catch (err) {\n      console.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl) {\n    window.open(imageUrl, '_blank');\n  }\n  /**\n   * Détecte les changements après chaque vérification de la vue - Optimisé\n   */\n  ngAfterViewChecked() {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n    // Détection des changements optimisée - Suppression de la vérification inutile\n    // La détection automatique d'Angular gère déjà les messages vocaux\n  }\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations() {\n    this.router.navigate(['/messages/conversations']);\n  }\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji) {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector('.whatsapp-input-field');\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  subscribeToNotifications() {\n    const notificationSub = this.MessageService.subscribeToNewNotifications().subscribe({\n      next: notification => {\n        this.notifications.unshift(notification);\n        this.updateNotificationCount();\n        this.MessageService.play('notification');\n        if (notification.type === 'NEW_MESSAGE' && notification.conversationId === this.conversation?.id) {\n          if (notification.id) {\n            this.MessageService.markAsRead([notification.id]).subscribe();\n          }\n        }\n      },\n      error: error => {}\n    });\n    this.subscriptions.add(notificationSub);\n    const notificationsListSub = this.MessageService.notifications$.subscribe({\n      next: notifications => {\n        this.notifications = notifications;\n        this.updateNotificationCount();\n      },\n      error: error => {}\n    });\n    this.subscriptions.add(notificationsListSub);\n    const notificationCountSub = this.MessageService.notificationCount$.subscribe({\n      next: count => {\n        this.unreadNotificationCount = count;\n      }\n    });\n    this.subscriptions.add(notificationCountSub);\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: call => {\n        if (call) {\n          this.incomingCall = call;\n          this.showCallModal = true;\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      }\n    });\n    this.subscriptions.add(callSub);\n    const activeCallSub = this.MessageService.activeCall$.subscribe({\n      next: call => {\n        this.activeCall = call;\n        if (call) {\n          this.showActiveCallModal = true;\n          this.startCallTimerMethod();\n        } else {\n          this.showActiveCallModal = false;\n          this.stopCallTimerMethod();\n          this.resetCallStateMethod();\n        }\n      }\n    });\n    this.subscriptions.add(activeCallSub);\n    // S'abonner aux flux vidéo locaux\n    const localStreamSub = this.MessageService.localStream$.subscribe({\n      next: stream => {\n        if (stream && this.localVideoElement) {\n          this.localVideoElement.srcObject = stream;\n        }\n      }\n    });\n    this.subscriptions.add(localStreamSub);\n    // S'abonner aux flux vidéo distants\n    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n      next: stream => {\n        if (stream && this.remoteVideoElement) {\n          this.remoteVideoElement.srcObject = stream;\n        }\n      }\n    });\n    this.subscriptions.add(remoteStreamSub);\n  }\n  /**\n   * Charge les notifications\n   */\n  loadNotifications(refresh = false) {\n    const loadSub = this.MessageService.getNotifications(refresh, 1, 20).subscribe({\n      next: notifications => {\n        if (refresh) {\n          this.notifications = notifications;\n        } else {\n          this.notifications = [...this.notifications, ...notifications];\n        }\n        this.updateNotificationCount();\n      },\n      error: error => {\n        this.toastService.showError('Erreur lors du chargement des notifications');\n      }\n    });\n    this.subscriptions.add(loadSub);\n  }\n  /**\n   * S'abonne au statut utilisateur en temps réel\n   */\n  subscribeToUserStatus() {\n    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n      next: user => {\n        this.handleUserStatusUpdate(user);\n      },\n      error: error => {\n        // Error handled silently\n      }\n    });\n    this.subscriptions.add(statusSub);\n  }\n  /**\n   * Gère la mise à jour du statut d'un utilisateur\n   */\n  handleUserStatusUpdate(user) {\n    if (!user.id) return;\n    if (user.isOnline) {\n      this.onlineUsers.set(user.id, user);\n    } else {\n      this.onlineUsers.delete(user.id);\n    }\n    if (this.otherParticipant && this.otherParticipant.id === user.id) {\n      this.otherParticipant = {\n        ...this.otherParticipant,\n        ...user\n      };\n    }\n  }\n  /**\n   * Initialise le statut de l'utilisateur actuel\n   */\n  initializeUserStatus() {\n    if (!this.currentUserId) return;\n    const setOnlineSub = this.MessageService.setUserOnline(this.currentUserId).subscribe({\n      next: user => {\n        this.currentUserStatus = 'online';\n        this.lastActivityTime = new Date();\n      },\n      error: error => {\n        console.error('MessageChat', \"Erreur lors de l'initialisation du statut\", error);\n      }\n    });\n    this.subscriptions.add(setOnlineSub);\n  }\n  /**\n   * Démarre le suivi d'activité automatique\n   */\n  startActivityTracking() {\n    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];\n    events.forEach(event => {\n      document.addEventListener(event, this.onUserActivity.bind(this), true);\n    });\n  }\n  /**\n   * Gère l'activité de l'utilisateur\n   */\n  onUserActivity() {\n    this.lastActivityTime = new Date();\n    // Réinitialiser le timer\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n    // Remettre en ligne si absent\n    if (this.currentUserStatus === 'away' || this.currentUserStatus === 'offline') {\n      this.updateUserStatus('online');\n    }\n    // Programmer la mise en absence\n    this.autoAwayTimeout = setTimeout(() => {\n      if (this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      }\n    }, this.c.delays.away);\n  }\n  /**\n   * Met à jour le statut de l'utilisateur\n   */\n  updateUserStatus(status) {\n    if (!this.currentUserId) return;\n    const previousStatus = this.currentUserStatus;\n    let updateObservable;\n    if (status === 'online') {\n      updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n    } else {\n      updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n    }\n    const updateSub = updateObservable.subscribe({\n      next: user => {\n        this.currentUserStatus = status;\n        if (status !== previousStatus) {\n          const statusText = this.getStatusText(status);\n          this.toastService.showInfo(`Statut : ${statusText}`);\n        }\n      },\n      error: error => {\n        console.error('MessageChat', 'Erreur lors de la mise à jour du statut', error);\n      }\n    });\n    this.subscriptions.add(updateSub);\n  }\n  /**\n   * Charge la liste des utilisateurs en ligne\n   */\n  loadOnlineUsers() {\n    const usersSub = this.MessageService.getAllUsers(false, undefined, 1, 50, 'username', 'asc', true).subscribe({\n      next: users => {\n        users.forEach(user => {\n          if (user.isOnline && user.id) {\n            this.onlineUsers.set(user.id, user);\n          }\n        });\n      },\n      error: error => {\n        console.error('MessageChat', 'Erreur lors du chargement des utilisateurs en ligne', error);\n      }\n    });\n    this.subscriptions.add(usersSub);\n  }\n  togglePinMessage(message) {\n    this.isPinning[message.id] = true;\n    setTimeout(() => {\n      this.isPinning[message.id] = false;\n      this.showPinConfirm[message.id] = false;\n    }, 1000);\n  }\n  ngOnDestroy() {\n    this.cleanup.clearTimeouts();\n    this.cleanup.setUserOffline();\n    this.cleanup.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.UserStatusService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 75,\n      vars: 47,\n      consts: [[1, \"chat-container\"], [1, \"chat-header\"], [1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [\"alt\", \"User avatar\", 1, \"user-avatar\", 3, \"src\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"whatsapp-actions\"], [4, \"ngFor\", \"ngForOf\"], [1, \"relative\"], [\"title\", \"Statut utilisateur\", 1, \"whatsapp-action-button\", \"relative\", 3, \"ngClass\", \"click\"], [3, \"ngClass\"], [\"class\", \"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\", 4, \"ngIf\"], [\"class\", \"absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\", 4, \"ngIf\"], [1, \"whatsapp-action-button\", \"btn-theme\", 3, \"click\"], [1, \"fas\", \"fa-palette\"], [\"class\", \"theme-selector-menu absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\", 4, \"ngIf\"], [\"title\", \"Menu principal\", 1, \"whatsapp-action-button\", \"btn-menu\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"messages-area\"], [\"messagesContainer\", \"\"], [\"style\", \"text-align: center; padding: 2rem; color: #00f7ff\", 4, \"ngIf\"], [\"style\", \"text-align: center; padding: 2rem; color: #888\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"whatsapp-reply-preview\", 4, \"ngIf\"], [\"class\", \"whatsapp-file-preview\", 4, \"ngIf\"], [\"class\", \"whatsapp-voice-recording\", 4, \"ngIf\"], [1, \"whatsapp-input-container\"], [1, \"whatsapp-input-wrapper\"], [1, \"whatsapp-input-actions-left\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", 1, \"whatsapp-input-action-btn\", 3, \"ngClass\", \"click\"], [1, \"fas\", \"fa-smile\"], [1, \"whatsapp-attachment-menu\"], [\"type\", \"button\", \"title\", \"Joindre un fichier\", 1, \"whatsapp-input-action-btn\", 3, \"ngClass\", \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"class\", \"whatsapp-attachment-dropdown\", 4, \"ngIf\"], [1, \"whatsapp-input-text-container\"], [1, \"whatsapp-input-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"whatsapp-input-field-wrapper\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez un message\", \"rows\", \"1\", \"maxlength\", \"4096\", 1, \"whatsapp-input-field\", 3, \"disabled\", \"input\", \"keydown\", \"focus\", \"blur\"], [\"messageTextarea\", \"\"], [\"class\", \"whatsapp-char-counter\", 4, \"ngIf\"], [1, \"whatsapp-input-actions-right\"], [1, \"whatsapp-send-container\"], [\"type\", \"submit\", \"class\", \"whatsapp-send-btn\", \"title\", \"Envoyer\", 3, \"disabled\", \"ngClass\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"whatsapp-voice-btn\", \"title\", \"Maintenir pour enregistrer\", 3, \"ngClass\", \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\", 4, \"ngIf\"], [\"class\", \"whatsapp-emoji-picker\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInputImage\", \"\"], [\"type\", \"file\", \"accept\", \"video/*\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInputVideo\", \"\"], [\"type\", \"file\", \"accept\", \"audio/*\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInputAudio\", \"\"], [\"type\", \"file\", \"accept\", \".pdf,.doc,.docx,.txt,.xlsx,.pptx\", 2, \"display\", \"none\", 3, \"change\"], [\"fileInputDocument\", \"\"], [1, \"side-panel\", 3, \"ngClass\"], [1, \"panel-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"style\", \"padding: 1rem; text-align: center; color: #888\", 4, \"ngIf\"], [\"style\", \"padding: 0.5rem; border-bottom: 1px solid rgba(0, 247, 255, 0.1)\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"search-bar bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] px-4 py-3 relative z-10\", 4, \"ngIf\"], [\"class\", \"pinned-messages-panel bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] relative z-10\", 4, \"ngIf\"], [\"class\", \"whatsapp-call-modal incoming-call\", 4, \"ngIf\"], [\"class\", \"whatsapp-call-modal active-call\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"whatsapp-modal-overlay\", 4, \"ngIf\"], [\"class\", \"whatsapp-image-viewer\", 4, \"ngIf\"], [\"class\", \"whatsapp-toast\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"whatsapp-side-panel whatsapp-conversation-info\", 4, \"ngIf\"], [\"class\", \"whatsapp-side-panel whatsapp-conversation-settings\", 4, \"ngIf\"], [1, \"user-info\"], [1, \"user-status\"], [3, \"ngClass\", \"title\", \"click\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [1, \"absolute\", \"-top-1\", \"-right-1\", \"w-3\", \"h-3\", \"bg-blue-500\", \"rounded-full\", \"animate-pulse\"], [1, \"absolute\", \"right-0\", \"mt-2\", \"w-56\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"shadow-lg\", \"z-50\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"p-3\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"bg-gradient-to-r\", \"from-[#4f5fad]/10\", \"to-[#6d78c9]/10\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"font-medium\", 3, \"ngClass\"], [1, \"p-1\"], [\"class\", \"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\", 3, \"disabled\", \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"p-1\"], [1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"text-xs\"], [1, \"font-medium\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", 3, \"disabled\", \"ngClass\", \"click\"], [1, \"theme-selector-menu\", \"absolute\", \"right-0\", \"mt-2\", \"w-48\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"shadow-lg\", \"z-50\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"p-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [\"href\", \"javascript:void(0)\", \"class\", \"block w-full text-left px-3 py-2 text-sm rounded-md transition-colors\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"transition-colors\", 3, \"ngClass\", \"click\"], [1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-red-500/10\", \"dark:hover:bg-red-500/10\", \"transition-colors\", \"text-red-600\", \"dark:text-red-400\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"mr-3\", \"text-xs\"], [1, \"fas\", \"fa-download\", \"mr-3\", \"text-xs\"], [1, \"fas\", \"fa-info-circle\", \"mr-3\", \"text-xs\"], [1, \"fas\", \"fa-cog\", \"mr-3\", \"text-xs\"], [2, \"text-align\", \"center\", \"padding\", \"2rem\", \"color\", \"#00f7ff\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [2, \"text-align\", \"center\", \"padding\", \"2rem\", \"color\", \"#888\"], [1, \"fas\", \"fa-comments\", 2, \"font-size\", \"3rem\", \"margin-bottom\", \"1rem\", \"opacity\", \"0.5\"], [2, \"font-size\", \"0.9rem\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-bubble\"], [2, \"font-size\", \"0.7rem\", \"opacity\", \"0.7\", \"margin-top\", \"0.5rem\"], [1, \"whatsapp-reply-preview\"], [1, \"whatsapp-reply-preview-content\"], [1, \"whatsapp-reply-preview-header\"], [1, \"whatsapp-reply-preview-info\"], [1, \"fas\", \"fa-reply\"], [1, \"whatsapp-reply-cancel\", 3, \"click\"], [1, \"whatsapp-reply-preview-message\"], [4, \"ngIf\"], [\"class\", \"whatsapp-reply-media\", 4, \"ngIf\"], [1, \"whatsapp-reply-media\"], [1, \"fas\", \"fa-image\"], [1, \"fas\", \"fa-microphone\"], [1, \"whatsapp-file-preview\"], [1, \"whatsapp-file-preview-content\"], [1, \"whatsapp-file-preview-header\"], [1, \"whatsapp-file-remove\", 3, \"click\"], [1, \"whatsapp-file-preview-body\"], [\"class\", \"whatsapp-file-preview-image\", 4, \"ngIf\"], [1, \"whatsapp-file-preview-info\"], [1, \"whatsapp-file-preview-name\"], [1, \"whatsapp-file-preview-size\"], [1, \"whatsapp-file-preview-image\"], [3, \"src\", \"alt\"], [1, \"whatsapp-voice-recording\"], [1, \"whatsapp-voice-recording-content\"], [1, \"whatsapp-voice-recording-info\"], [1, \"whatsapp-voice-recording-icon\"], [1, \"whatsapp-voice-recording-details\"], [1, \"whatsapp-voice-recording-text\"], [1, \"whatsapp-voice-recording-duration\"], [1, \"whatsapp-voice-recording-actions\"], [1, \"whatsapp-voice-cancel\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"whatsapp-voice-send\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"whatsapp-voice-recording-waveform\"], [\"class\", \"whatsapp-voice-recording-bar\", 3, \"height\", 4, \"ngFor\", \"ngForOf\"], [1, \"whatsapp-voice-recording-bar\"], [1, \"whatsapp-attachment-dropdown\"], [1, \"whatsapp-attachment-option\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [1, \"fas\", \"fa-music\"], [1, \"fas\", \"fa-file\"], [1, \"fas\", \"fa-camera\"], [1, \"whatsapp-char-counter\"], [\"type\", \"submit\", \"title\", \"Envoyer\", 1, \"whatsapp-send-btn\", 3, \"disabled\", \"ngClass\", \"click\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer\", 1, \"whatsapp-voice-btn\", 3, \"ngClass\", \"mousedown\", \"mouseup\", \"mouseleave\", \"touchstart\", \"touchend\"], [1, \"whatsapp-emoji-picker\"], [1, \"whatsapp-emoji-picker-header\"], [1, \"whatsapp-emoji-categories\"], [\"class\", \"whatsapp-emoji-category-btn\", 3, \"ngClass\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"whatsapp-emoji-close\", 3, \"click\"], [1, \"whatsapp-emoji-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un emoji...\", 1, \"whatsapp-emoji-search-input\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"whatsapp-emoji-grid\"], [\"class\", \"whatsapp-emoji-btn\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"whatsapp-recent-emojis\", 4, \"ngIf\"], [1, \"whatsapp-emoji-category-btn\", 3, \"ngClass\", \"title\", \"click\"], [1, \"whatsapp-emoji-btn\", 3, \"title\", \"click\"], [1, \"whatsapp-recent-emojis\"], [1, \"whatsapp-recent-emojis-header\"], [1, \"whatsapp-recent-emojis-grid\"], [\"class\", \"whatsapp-emoji-btn\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"whatsapp-emoji-btn\", 3, \"click\"], [2, \"padding\", \"1rem\", \"text-align\", \"center\", \"color\", \"#888\"], [1, \"fas\", \"fa-bell-slash\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"0.5rem\", \"opacity\", \"0.5\"], [2, \"padding\", \"0.5rem\", \"border-bottom\", \"1px solid rgba(0, 247, 255, 0.1)\"], [2, \"margin\", \"0\", \"font-size\", \"0.9rem\"], [2, \"opacity\", \"0.7\"], [1, \"search-bar\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"px-4\", \"py-3\", \"relative\", \"z-10\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-search\", \"text-sm\"], [1, \"flex-1\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"Rechercher dans cette conversation...\", \"autofocus\", \"\", 1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"bg-[#edf1f4]/50\", \"dark:bg-[#2a2a2a]/50\", \"border\", \"border-[#edf1f4]\", \"dark:border-[#3a3a3a]\", \"rounded-lg\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]/50\", \"transition-colors\", 3, \"ngModel\", \"ngModelChange\", \"input\", \"keydown\"], [\"class\", \"absolute right-3 top-1/2 transform -translate-y-1/2\", 4, \"ngIf\"], [\"class\", \"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"click\"], [\"class\", \"mt-3 max-h-40 overflow-y-auto border border-[#edf1f4]/50 dark:border-[#3a3a3a] rounded-lg bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30\", 4, \"ngIf\"], [\"class\", \"mt-3 p-3 text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30 rounded-lg\", 4, \"ngIf\"], [1, \"absolute\", \"right-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\"], [1, \"w-4\", \"h-4\", \"border-2\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"right-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"w-4\", \"h-4\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-xs\"], [1, \"mt-3\", \"max-h-40\", \"overflow-y-auto\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\", \"rounded-lg\", \"bg-[#edf1f4]/30\", \"dark:bg-[#2a2a2a]/30\"], [1, \"p-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\"], [1, \"max-h-32\", \"overflow-y-auto\"], [\"class\", \"w-full text-left px-3 py-2 hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", \"border-b\", \"border-[#edf1f4]/30\", \"dark:border-[#3a3a3a]/30\", \"last:border-b-0\", 3, \"click\"], [1, \"flex\", \"items-start\", \"space-x-2\"], [1, \"w-6\", \"h-6\", \"rounded-full\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"flex\", \"items-center\", \"justify-center\", \"flex-shrink-0\", \"mt-0.5\"], [1, \"fas\", \"fa-comment\", \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"truncate\", 3, \"innerHTML\"], [1, \"mt-3\", \"p-3\", \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"bg-[#edf1f4]/30\", \"dark:bg-[#2a2a2a]/30\", \"rounded-lg\"], [1, \"fas\", \"fa-search\", \"text-lg\", \"mb-2\", \"block\", \"text-[#6d6870]/50\", \"dark:text-[#a0a0a0]/50\"], [1, \"pinned-messages-panel\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"z-10\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-4\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"fas\", \"fa-thumbtack\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"w-6\", \"h-6\", \"flex\", \"items-center\", \"justify-center\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"rounded-full\", \"transition-colors\", 3, \"click\"], [1, \"max-h-48\", \"overflow-y-auto\"], [\"class\", \"p-4 text-center\", 4, \"ngIf\"], [\"class\", \"divide-y divide-[#edf1f4]/30 dark:divide-[#3a3a3a]/30\", 4, \"ngIf\"], [1, \"p-4\", \"text-center\"], [1, \"text-[#6d6870]/70\", \"dark:text-[#a0a0a0]/70\"], [1, \"fas\", \"fa-thumbtack\", \"text-2xl\", \"mb-2\", \"block\", \"opacity-50\"], [1, \"text-sm\"], [1, \"divide-y\", \"divide-[#edf1f4]/30\", \"dark:divide-[#3a3a3a]/30\"], [\"class\", \"w-full text-left p-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"text-left\", \"p-3\", \"hover:bg-[#4f5fad]/5\", \"dark:hover:bg-[#6d78c9]/5\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"space-x-3\"], [1, \"flex-shrink-0\"], [\"onerror\", \"this.src='assets/images/default-avatar.png'\", 1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-1\"], [1, \"text-xs\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-xs\", \"text-[#6d6870]/70\", \"dark:text-[#a0a0a0]/70\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"line-clamp-2\"], [\"class\", \"italic flex items-center\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"mt-1\"], [1, \"fas\", \"fa-thumbtack\", \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-1\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"flex-shrink-0\", \"text-[#6d6870]/50\", \"dark:text-[#a0a0a0]/50\"], [1, \"fas\", \"fa-chevron-right\", \"text-xs\"], [1, \"italic\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-image\", \"mr-1\"], [1, \"fas\", \"fa-microphone\", \"mr-1\"], [1, \"whatsapp-call-modal\", \"incoming-call\"], [1, \"whatsapp-call-modal-overlay\"], [1, \"whatsapp-call-modal-content\"], [1, \"whatsapp-call-info\"], [1, \"whatsapp-call-avatar\"], [1, \"whatsapp-call-details\"], [1, \"whatsapp-call-actions\"], [1, \"whatsapp-call-decline\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\"], [1, \"whatsapp-call-accept\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [1, \"whatsapp-call-modal\", \"active-call\", 3, \"ngClass\"], [1, \"whatsapp-call-header\"], [1, \"whatsapp-call-participant\"], [1, \"whatsapp-call-participant-info\"], [1, \"whatsapp-call-duration\"], [1, \"whatsapp-call-status\"], [1, \"whatsapp-call-minimize\", 3, \"click\"], [\"class\", \"fas fa-minus\", 4, \"ngIf\"], [\"class\", \"fas fa-expand\", 4, \"ngIf\"], [\"class\", \"whatsapp-video-container\", 4, \"ngIf\"], [1, \"whatsapp-call-controls\", 3, \"ngClass\"], [1, \"whatsapp-call-control\", 3, \"ngClass\", \"click\"], [\"class\", \"fas fa-microphone\", 4, \"ngIf\"], [\"class\", \"fas fa-microphone-slash\", 4, \"ngIf\"], [\"class\", \"whatsapp-call-control\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [1, \"whatsapp-call-end\", 3, \"click\"], [1, \"fas\", \"fa-minus\"], [1, \"fas\", \"fa-expand\"], [1, \"whatsapp-video-container\"], [\"autoplay\", \"\", 1, \"whatsapp-remote-video\"], [\"remoteVideo\", \"\"], [\"autoplay\", \"\", \"muted\", \"\", 1, \"whatsapp-local-video\"], [\"localVideo\", \"\"], [1, \"fas\", \"fa-microphone-slash\"], [\"class\", \"fas fa-video\", 4, \"ngIf\"], [\"class\", \"fas fa-video-slash\", 4, \"ngIf\"], [1, \"fas\", \"fa-video-slash\"], [1, \"whatsapp-modal-overlay\"], [1, \"whatsapp-modal-content\"], [1, \"whatsapp-modal-header\"], [1, \"whatsapp-modal-body\"], [1, \"whatsapp-modal-actions\"], [1, \"whatsapp-modal-cancel\", 3, \"click\"], [1, \"whatsapp-modal-confirm\", 3, \"click\"], [1, \"whatsapp-modal-content\", \"whatsapp-forward-modal\"], [1, \"whatsapp-modal-close\", 3, \"click\"], [1, \"whatsapp-forward-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher des contacts...\", 1, \"whatsapp-forward-search-input\", 3, \"ngModel\", \"ngModelChange\"], [1, \"whatsapp-forward-contacts\"], [\"class\", \"whatsapp-forward-contact\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"whatsapp-modal-confirm\", 3, \"disabled\", \"click\"], [1, \"whatsapp-forward-contact\", 3, \"click\"], [\"type\", \"checkbox\", 3, \"checked\"], [1, \"whatsapp-image-viewer\"], [1, \"whatsapp-image-viewer-overlay\", 3, \"click\"], [1, \"whatsapp-image-viewer-content\"], [1, \"whatsapp-image-viewer-header\"], [1, \"whatsapp-image-viewer-info\"], [1, \"whatsapp-image-viewer-close\", 3, \"click\"], [1, \"whatsapp-image-viewer-body\"], [\"class\", \"whatsapp-image-nav whatsapp-image-prev\", 3, \"click\", 4, \"ngIf\"], [1, \"whatsapp-image-viewer-img\", 3, \"src\", \"alt\"], [\"class\", \"whatsapp-image-nav whatsapp-image-next\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"whatsapp-image-viewer-caption\", 4, \"ngIf\"], [1, \"whatsapp-image-nav\", \"whatsapp-image-prev\", 3, \"click\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"whatsapp-image-nav\", \"whatsapp-image-next\", 3, \"click\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"whatsapp-image-viewer-caption\"], [1, \"whatsapp-toast\", 3, \"ngClass\"], [1, \"whatsapp-toast-content\"], [1, \"whatsapp-side-panel\", \"whatsapp-conversation-info\"], [1, \"whatsapp-side-panel-header\"], [1, \"whatsapp-side-panel-close\", 3, \"click\"], [1, \"whatsapp-side-panel-body\"], [1, \"whatsapp-conversation-participant\"], [1, \"whatsapp-participant-details\"], [1, \"whatsapp-participant-status\"], [1, \"whatsapp-conversation-stats\"], [1, \"whatsapp-stat\"], [1, \"whatsapp-stat-label\"], [1, \"whatsapp-stat-value\"], [1, \"whatsapp-side-panel\", \"whatsapp-conversation-settings\"], [1, \"whatsapp-setting-group\"], [1, \"whatsapp-setting-item\"], [\"type\", \"checkbox\", 3, \"ngModel\", \"ngModelChange\", \"change\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵtemplate(5, MessageChatComponent_div_5_Template, 5, 2, \"div\", 5);\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵtemplate(7, MessageChatComponent_ng_container_7_Template, 4, 8, \"ng-container\", 7);\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_9_listener() {\n            return ctx.toggleStatusSelector();\n          });\n          i0.ɵɵelement(10, \"i\", 10);\n          i0.ɵɵtemplate(11, MessageChatComponent_span_11_Template, 1, 0, \"span\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 18, 4, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_14_listener() {\n            return ctx.toggleThemeSelector();\n          });\n          i0.ɵɵelement(15, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, MessageChatComponent_div_16_Template, 5, 1, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_18_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(19, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, MessageChatComponent_div_20_Template, 24, 0, \"div\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 18, 19);\n          i0.ɵɵtemplate(23, MessageChatComponent_div_23_Template, 3, 0, \"div\", 20);\n          i0.ɵɵtemplate(24, MessageChatComponent_div_24_Template, 6, 0, \"div\", 21);\n          i0.ɵɵtemplate(25, MessageChatComponent_div_25_Template, 6, 6, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(26, MessageChatComponent_div_26_Template, 13, 4, \"div\", 23);\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 14, 3, \"div\", 24);\n          i0.ɵɵtemplate(28, MessageChatComponent_div_28_Template, 17, 2, \"div\", 25);\n          i0.ɵɵelementStart(29, \"div\", 26)(30, \"div\", 27)(31, \"div\", 28)(32, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_32_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(33, \"i\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 31)(35, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_35_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(36, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, MessageChatComponent_div_37_Template, 21, 0, \"div\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 35)(39, \"form\", 36);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_39_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(40, \"div\", 37)(41, \"textarea\", 38, 39);\n          i0.ɵɵlistener(\"input\", function MessageChatComponent_Template_textarea_input_41_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"keydown\", function MessageChatComponent_Template_textarea_keydown_41_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_41_listener() {\n            return ctx.onInputFocus();\n          })(\"blur\", function MessageChatComponent_Template_textarea_blur_41_listener() {\n            return ctx.onInputBlur();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(43, MessageChatComponent_div_43_Template, 2, 1, \"div\", 40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 41)(45, \"div\", 42);\n          i0.ɵɵtemplate(46, MessageChatComponent_button_46_Template, 3, 6, \"button\", 43);\n          i0.ɵɵtemplate(47, MessageChatComponent_button_47_Template, 2, 3, \"button\", 44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(48, MessageChatComponent_div_48_Template, 11, 4, \"div\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"input\", 46, 47);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_49_listener($event) {\n            return ctx.onFileSelected($event, \"image\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"input\", 48, 49);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_51_listener($event) {\n            return ctx.onFileSelected($event, \"video\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"input\", 50, 51);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_53_listener($event) {\n            return ctx.onFileSelected($event, \"audio\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"input\", 52, 53);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_55_listener($event) {\n            return ctx.onFileSelected($event, \"document\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 54)(58, \"div\", 55)(59, \"h3\");\n          i0.ɵɵtext(60, \"Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"button\", 56);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_61_listener() {\n            return ctx.toggleNotificationPanel();\n          });\n          i0.ɵɵelement(62, \"i\", 57);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(63, MessageChatComponent_div_63_Template, 4, 0, \"div\", 58);\n          i0.ɵɵtemplate(64, MessageChatComponent_div_64_Template, 5, 2, \"div\", 59);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(65, MessageChatComponent_div_65_Template, 12, 5, \"div\", 60);\n          i0.ɵɵtemplate(66, MessageChatComponent_div_66_Template, 11, 3, \"div\", 61);\n          i0.ɵɵtemplate(67, MessageChatComponent_div_67_Template, 16, 4, \"div\", 62);\n          i0.ɵɵtemplate(68, MessageChatComponent_div_68_Template, 23, 20, \"div\", 63);\n          i0.ɵɵtemplate(69, MessageChatComponent_div_69_Template, 13, 0, \"div\", 64);\n          i0.ɵɵtemplate(70, MessageChatComponent_div_70_Template, 17, 4, \"div\", 64);\n          i0.ɵɵtemplate(71, MessageChatComponent_div_71_Template, 14, 7, \"div\", 65);\n          i0.ɵɵtemplate(72, MessageChatComponent_div_72_Template, 5, 4, \"div\", 66);\n          i0.ɵɵtemplate(73, MessageChatComponent_div_73_Template, 32, 8, \"div\", 67);\n          i0.ɵɵtemplate(74, MessageChatComponent_div_74_Template, 33, 4, \"div\", 68);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_21_0;\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getHeaderActions());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(39, _c11, ctx.showStatusSelector));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.getStatusIcon(ctx.currentUserStatus));\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusColor(ctx.currentUserStatus));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUpdatingStatus);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showStatusSelector);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showThemeSelector);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.replyingToMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedFile);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(41, _c8, ctx.showEmojiPicker));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(43, _c8, ctx.showAttachmentMenu));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.otherParticipant || ctx.isRecordingVoice);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_21_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_21_0.value == null ? null : tmp_21_0.value.length) > 3000);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasMessageContent() || ctx.selectedFile);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasMessageContent() && !ctx.selectedFile);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(45, _c12, ctx.showNotificationPanel));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.notifications.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showSearchBar);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPinnedMessages);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.incomingCall);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeCall);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDeleteConfirmModal);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showForwardModal);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showImageViewer);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showToast);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showConversationInfo);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showConversationSettings);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.CheckboxControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.MaxLengthValidator, i4.NgModel, i4.FormGroupDirective, i4.FormControlName],\n      styles: [\"\\n\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);\\n  color: #e0e0e0;\\n}\\n\\n\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 1rem;\\n  background: rgba(0, 247, 255, 0.1);\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  margin-right: 1rem;\\n  border: 2px solid #00f7ff;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #00f7ff;\\n  font-size: 1.1rem;\\n}\\n\\n.user-status[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #888;\\n}\\n\\n\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 1px solid #00f7ff;\\n  color: #00f7ff;\\n  padding: 0.5rem;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: #00f7ff;\\n  color: #000;\\n  box-shadow: 0 0 10px #00f7ff;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  background: linear-gradient(135deg, #ff6b69, #ff4757);\\n  color: white;\\n  font-size: 0.7rem;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.messages-area[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  background: rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.message[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1rem;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.message.current-user[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  max-width: 70%;\\n  padding: 0.8rem 1rem;\\n  border-radius: 18px;\\n  position: relative;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.message.current-user[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff, #0066ff);\\n  color: #fff;\\n  border-radius: 18px 18px 4px 18px;\\n}\\n\\n.message.other-user[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: #e0e0e0;\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  border-radius: 18px 18px 18px 4px;\\n}\\n\\n\\n\\n.input-area[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background: rgba(0, 247, 255, 0.05);\\n  border-top: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.input-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.8rem 1rem;\\n  background: rgba(0, 0, 0, 0.5);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  border-radius: 25px;\\n  color: #e0e0e0;\\n  outline: none;\\n  transition: all 0.3s ease;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  border-color: #00f7ff;\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.3);\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00f7ff, #0066ff);\\n  border: none;\\n  color: #fff;\\n  padding: 0.8rem;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.send-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n.side-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: -300px;\\n  width: 300px;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.9);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-left: 1px solid rgba(0, 247, 255, 0.3);\\n  transition: right 0.3s ease;\\n  z-index: 1000;\\n  padding: 1rem;\\n}\\n\\n.side-panel.open[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: none;\\n  color: #00f7ff;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .chat-header[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n\\n  .message-bubble[_ngcontent-%COMP%] {\\n    max-width: 85%;\\n  }\\n\\n  .side-panel[_ngcontent-%COMP%] {\\n    width: 100%;\\n    right: -100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvbWVzc2FnZS1jaGF0L21lc3NhZ2UtY2hhdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLG9EQUFvRDs7QUFFcEQsd0JBQXdCO0FBQ3hCO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixhQUFhO0VBQ2IsNkRBQTZEO0VBQzdELGNBQWM7QUFDaEI7O0FBRUEsWUFBWTtBQUNaO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixhQUFhO0VBQ2Isa0NBQWtDO0VBQ2xDLCtDQUErQztFQUMvQyxtQ0FBMkI7VUFBM0IsMkJBQTJCO0FBQzdCOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsa0JBQWtCO0VBQ2xCLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLFNBQVM7RUFDVCxjQUFjO0VBQ2QsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7QUFDYjs7QUFFQSxxQkFBcUI7QUFDckI7RUFDRSxpQkFBaUI7RUFDakIsYUFBYTtFQUNiLFdBQVc7QUFDYjs7QUFFQTtFQUNFLHVCQUF1QjtFQUN2Qix5QkFBeUI7RUFDekIsY0FBYztFQUNkLGVBQWU7RUFDZixrQkFBa0I7RUFDbEIsZUFBZTtFQUNmLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixXQUFXO0VBQ1gsNEJBQTRCO0FBQzlCOztBQUVBO0VBQ0Usa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLFNBQVM7RUFDVCxXQUFXO0VBQ1gscURBQXFEO0VBQ3JELFlBQVk7RUFDWixpQkFBaUI7RUFDakIsZ0JBQWdCO0VBQ2hCLG1CQUFtQjtFQUNuQixlQUFlO0VBQ2YsWUFBWTtFQUNaLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGlCQUFpQjtFQUNqQix3Q0FBd0M7QUFDMUM7O0FBRUEsc0JBQXNCO0FBQ3RCO0VBQ0UsT0FBTztFQUNQLGdCQUFnQjtFQUNoQixhQUFhO0VBQ2IsOEJBQThCO0FBQ2hDOztBQUVBLFlBQVk7QUFDWjtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsMkJBQTJCO0FBQzdCOztBQUVBO0VBQ0UseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsY0FBYztFQUNkLG9CQUFvQjtFQUNwQixtQkFBbUI7RUFDbkIsa0JBQWtCO0VBQ2xCLGtDQUEwQjtVQUExQiwwQkFBMEI7QUFDNUI7O0FBRUE7RUFDRSxxREFBcUQ7RUFDckQsV0FBVztFQUNYLGlDQUFpQztBQUNuQzs7QUFFQTtFQUNFLG9DQUFvQztFQUNwQyxjQUFjO0VBQ2Qsd0NBQXdDO0VBQ3hDLGlDQUFpQztBQUNuQzs7QUFFQSxtQkFBbUI7QUFDbkI7RUFDRSxhQUFhO0VBQ2IsbUNBQW1DO0VBQ25DLDRDQUE0QztBQUM5Qzs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsV0FBVztBQUNiOztBQUVBO0VBQ0UsT0FBTztFQUNQLG9CQUFvQjtFQUNwQiw4QkFBOEI7RUFDOUIsd0NBQXdDO0VBQ3hDLG1CQUFtQjtFQUNuQixjQUFjO0VBQ2QsYUFBYTtFQUNiLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLHFCQUFxQjtFQUNyQiwyQ0FBMkM7QUFDN0M7O0FBRUE7RUFDRSxxREFBcUQ7RUFDckQsWUFBWTtFQUNaLFdBQVc7RUFDWCxlQUFlO0VBQ2Ysa0JBQWtCO0VBQ2xCLGVBQWU7RUFDZix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxxQkFBcUI7RUFDckIsMkNBQTJDO0FBQzdDOztBQUVBLHNCQUFzQjtBQUN0QjtFQUNFLGVBQWU7RUFDZixNQUFNO0VBQ04sYUFBYTtFQUNiLFlBQVk7RUFDWixhQUFhO0VBQ2IsOEJBQThCO0VBQzlCLG1DQUEyQjtVQUEzQiwyQkFBMkI7RUFDM0IsNkNBQTZDO0VBQzdDLDJCQUEyQjtFQUMzQixhQUFhO0VBQ2IsYUFBYTtBQUNmOztBQUVBO0VBQ0UsUUFBUTtBQUNWOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDhCQUE4QjtFQUM5QixtQkFBbUI7RUFDbkIsbUJBQW1CO0VBQ25CLG9CQUFvQjtFQUNwQiwrQ0FBK0M7QUFDakQ7O0FBRUE7RUFDRSx1QkFBdUI7RUFDdkIsWUFBWTtFQUNaLGNBQWM7RUFDZCxpQkFBaUI7RUFDakIsZUFBZTtBQUNqQjs7QUFFQSxlQUFlO0FBQ2Y7RUFDRTtJQUNFLFVBQVU7SUFDViwyQkFBMkI7RUFDN0I7RUFDQTtJQUNFLFVBQVU7SUFDVix3QkFBd0I7RUFDMUI7QUFDRjs7QUFFQSxlQUFlO0FBQ2Y7RUFDRTtJQUNFLGVBQWU7RUFDakI7O0VBRUE7SUFDRSxjQUFjO0VBQ2hCOztFQUVBO0lBQ0UsV0FBVztJQUNYLFlBQVk7RUFDZDtBQUNGOztBQUtBLGcvUkFBZy9SIiwic291cmNlc0NvbnRlbnQiOlsiLyogQ1NTIFNpbXBsZSBldCBGdXR1cmlzdGUgLSBGb25jdGlvbm5hbGl0w4PCqXMgUHVyZXMgKi9cclxuXHJcbi8qIENvbnRhaW5lciBwcmluY2lwYWwgKi9cclxuLmNoYXQtY29udGFpbmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgaGVpZ2h0OiAxMDB2aDtcclxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMGEwYTBhIDAlLCAjMWExYTJlIDEwMCUpO1xyXG4gIGNvbG9yOiAjZTBlMGUwO1xyXG59XHJcblxyXG4vKiBFbi10w4PCqnRlICovXHJcbi5jaGF0LWhlYWRlciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIHBhZGRpbmc6IDFyZW07XHJcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcclxuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTtcclxuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XHJcbn1cclxuXHJcbi51c2VyLWF2YXRhciB7XHJcbiAgd2lkdGg6IDQwcHg7XHJcbiAgaGVpZ2h0OiA0MHB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBtYXJnaW4tcmlnaHQ6IDFyZW07XHJcbiAgYm9yZGVyOiAycHggc29saWQgIzAwZjdmZjtcclxufVxyXG5cclxuLnVzZXItaW5mbyBoMyB7XHJcbiAgbWFyZ2luOiAwO1xyXG4gIGNvbG9yOiAjMDBmN2ZmO1xyXG4gIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG59XHJcblxyXG4udXNlci1zdGF0dXMge1xyXG4gIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gIGNvbG9yOiAjODg4O1xyXG59XHJcblxyXG4vKiBCb3V0b25zIGQnYWN0aW9uICovXHJcbi5hY3Rpb24tYnV0dG9ucyB7XHJcbiAgbWFyZ2luLWxlZnQ6IGF1dG87XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBnYXA6IDAuNXJlbTtcclxufVxyXG5cclxuLmFjdGlvbi1idG4ge1xyXG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICMwMGY3ZmY7XHJcbiAgY29sb3I6ICMwMGY3ZmY7XHJcbiAgcGFkZGluZzogMC41cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxufVxyXG5cclxuLmFjdGlvbi1idG46aG92ZXIge1xyXG4gIGJhY2tncm91bmQ6ICMwMGY3ZmY7XHJcbiAgY29sb3I6ICMwMDA7XHJcbiAgYm94LXNoYWRvdzogMCAwIDEwcHggIzAwZjdmZjtcclxufVxyXG5cclxuLmFjdGlvbi1idG4ge1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxufVxyXG5cclxuLmJhZGdlIHtcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgdG9wOiAtOHB4O1xyXG4gIHJpZ2h0OiAtOHB4O1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjZiNjksICNmZjQ3NTcpO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxuICBmb250LXNpemU6IDAuN3JlbTtcclxuICBwYWRkaW5nOiAycHggNnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XHJcbiAgbWluLXdpZHRoOiAxOHB4O1xyXG4gIGhlaWdodDogMThweDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4zKTtcclxufVxyXG5cclxuLyogWm9uZSBkZXMgbWVzc2FnZXMgKi9cclxuLm1lc3NhZ2VzLWFyZWEge1xyXG4gIGZsZXg6IDE7XHJcbiAgb3ZlcmZsb3cteTogYXV0bztcclxuICBwYWRkaW5nOiAxcmVtO1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4zKTtcclxufVxyXG5cclxuLyogTWVzc2FnZSAqL1xyXG4ubWVzc2FnZSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG4gIGFuaW1hdGlvbjogZmFkZUluIDAuM3MgZWFzZTtcclxufVxyXG5cclxuLm1lc3NhZ2UuY3VycmVudC11c2VyIHtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xyXG59XHJcblxyXG4ubWVzc2FnZS1idWJibGUge1xyXG4gIG1heC13aWR0aDogNzAlO1xyXG4gIHBhZGRpbmc6IDAuOHJlbSAxcmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDE4cHg7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cig1cHgpO1xyXG59XHJcblxyXG4ubWVzc2FnZS5jdXJyZW50LXVzZXIgLm1lc3NhZ2UtYnViYmxlIHtcclxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMDBmN2ZmLCAjMDA2NmZmKTtcclxuICBjb2xvcjogI2ZmZjtcclxuICBib3JkZXItcmFkaXVzOiAxOHB4IDE4cHggNHB4IDE4cHg7XHJcbn1cclxuXHJcbi5tZXNzYWdlLm90aGVyLXVzZXIgLm1lc3NhZ2UtYnViYmxlIHtcclxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbiAgY29sb3I6ICNlMGUwZTA7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTtcclxuICBib3JkZXItcmFkaXVzOiAxOHB4IDE4cHggMThweCA0cHg7XHJcbn1cclxuXHJcbi8qIFpvbmUgZGUgc2Fpc2llICovXHJcbi5pbnB1dC1hcmVhIHtcclxuICBwYWRkaW5nOiAxcmVtO1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMjQ3LCAyNTUsIDAuMDUpO1xyXG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xyXG59XHJcblxyXG4uaW5wdXQtZm9ybSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogMC41cmVtO1xyXG59XHJcblxyXG4ubWVzc2FnZS1pbnB1dCB7XHJcbiAgZmxleDogMTtcclxuICBwYWRkaW5nOiAwLjhyZW0gMXJlbTtcclxuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNSk7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTtcclxuICBib3JkZXItcmFkaXVzOiAyNXB4O1xyXG4gIGNvbG9yOiAjZTBlMGUwO1xyXG4gIG91dGxpbmU6IG5vbmU7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxufVxyXG5cclxuLm1lc3NhZ2UtaW5wdXQ6Zm9jdXMge1xyXG4gIGJvcmRlci1jb2xvcjogIzAwZjdmZjtcclxuICBib3gtc2hhZG93OiAwIDAgMTBweCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xyXG59XHJcblxyXG4uc2VuZC1idG4ge1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMwMGY3ZmYsICMwMDY2ZmYpO1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICBjb2xvcjogI2ZmZjtcclxuICBwYWRkaW5nOiAwLjhyZW07XHJcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG59XHJcblxyXG4uc2VuZC1idG46aG92ZXIge1xyXG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcclxuICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDI0NywgMjU1LCAwLjUpO1xyXG59XHJcblxyXG4vKiBQYW5uZWF1eCBsYXTDg8KpcmF1eCAqL1xyXG4uc2lkZS1wYW5lbCB7XHJcbiAgcG9zaXRpb246IGZpeGVkO1xyXG4gIHRvcDogMDtcclxuICByaWdodDogLTMwMHB4O1xyXG4gIHdpZHRoOiAzMDBweDtcclxuICBoZWlnaHQ6IDEwMHZoO1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC45KTtcclxuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XHJcbiAgYm9yZGVyLWxlZnQ6IDFweCBzb2xpZCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xyXG4gIHRyYW5zaXRpb246IHJpZ2h0IDAuM3MgZWFzZTtcclxuICB6LWluZGV4OiAxMDAwO1xyXG4gIHBhZGRpbmc6IDFyZW07XHJcbn1cclxuXHJcbi5zaWRlLXBhbmVsLm9wZW4ge1xyXG4gIHJpZ2h0OiAwO1xyXG59XHJcblxyXG4ucGFuZWwtaGVhZGVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbiAgcGFkZGluZy1ib3R0b206IDFyZW07XHJcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHJnYmEoMCwgMjQ3LCAyNTUsIDAuMyk7XHJcbn1cclxuXHJcbi5jbG9zZS1idG4ge1xyXG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICBjb2xvcjogIzAwZjdmZjtcclxuICBmb250LXNpemU6IDEuMnJlbTtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbn1cclxuXHJcbi8qIEFuaW1hdGlvbnMgKi9cclxuQGtleWZyYW1lcyBmYWRlSW4ge1xyXG4gIGZyb20ge1xyXG4gICAgb3BhY2l0eTogMDtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgxMHB4KTtcclxuICB9XHJcbiAgdG8ge1xyXG4gICAgb3BhY2l0eTogMTtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcclxuICB9XHJcbn1cclxuXHJcbi8qIFJlc3BvbnNpdmUgKi9cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLmNoYXQtaGVhZGVyIHtcclxuICAgIHBhZGRpbmc6IDAuNXJlbTtcclxuICB9XHJcblxyXG4gIC5tZXNzYWdlLWJ1YmJsZSB7XHJcbiAgICBtYXgtd2lkdGg6IDg1JTtcclxuICB9XHJcblxyXG4gIC5zaWRlLXBhbmVsIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgcmlnaHQ6IC0xMDAlO1xyXG4gIH1cclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "MessageType", "CallType", "switchMap", "distinctUntilChanged", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "otherParticipant", "username", "ɵɵtextInterpolate1", "isOnline", "formatLastActive", "lastActive", "ɵɵclassMap", "action_r35", "badge", "class", "ɵɵproperty", "ɵɵpureFunction1", "_c2", "animate", "count", "ɵɵelementContainerStart", "ɵɵlistener", "MessageChatComponent_ng_container_7_Template_button_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r39", "$implicit", "ɵɵresetView", "onClick", "ɵɵelement", "ɵɵtemplate", "MessageChatComponent_ng_container_7_span_3_Template", "ɵɵelementContainerEnd", "activeClass", "isActive", "ɵɵpureFunction0", "_c3", "title", "icon", "MessageChatComponent_div_12_button_8_Template_button_click_0_listener", "_r43", "status_r41", "ctx_r42", "ɵɵnextContext", "updateUserStatus", "key", "toggleStatusSelector", "ctx_r40", "isUpdatingStatus", "_c4", "currentUserStatus", "color", "label", "description", "MessageChatComponent_div_12_button_8_Template", "MessageChatComponent_div_12_Template_button_click_10_listener", "_r45", "ctx_r44", "toggleUserStatusPanel", "ctx_r3", "getStatusColor", "getStatusText", "getStatusOptions", "getOnlineUsersCount", "MessageChatComponent_div_16_a_4_Template_a_click_0_listener", "_r49", "theme_r47", "ctx_r48", "changeTheme", "hoverColor", "MessageChatComponent_div_16_a_4_Template", "ctx_r4", "getThemeOptions", "MessageChatComponent_div_20_Template_button_click_4_listener", "_r51", "ctx_r50", "clearConversation", "MessageChatComponent_div_20_Template_button_click_9_listener", "ctx_r52", "exportConversation", "MessageChatComponent_div_20_Template_button_click_14_listener", "ctx_r53", "toggleConversationInfo", "MessageChatComponent_div_20_Template_button_click_19_listener", "ctx_r54", "toggleConversationSettings", "ɵɵpureFunction2", "_c5", "message_r55", "sender", "id", "ctx_r9", "currentUserId", "content", "formatMessageTime", "timestamp", "ctx_r56", "replyingToMessage", "MessageChatComponent_div_26_Template_button_click_7_listener", "_r60", "ctx_r59", "cancelReply", "MessageChatComponent_div_26_span_10_Template", "MessageChatComponent_div_26_span_11_Template", "MessageChatComponent_div_26_span_12_Template", "ctx_r10", "hasImage", "isVoiceMessage", "ctx_r61", "previewUrl", "ɵɵsanitizeUrl", "selectedFile", "name", "MessageChatComponent_div_27_Template_button_click_5_listener", "_r63", "ctx_r62", "removeSelectedFile", "MessageChatComponent_div_27_div_8_Template", "ctx_r11", "formatFileSize", "size", "ɵɵstyleProp", "bar_r65", "MessageChatComponent_div_28_Template_button_click_11_listener", "_r67", "ctx_r66", "cancelVoiceRecording", "MessageChatComponent_div_28_Template_button_click_13_listener", "ctx_r68", "stopVoiceRecording", "MessageChatComponent_div_28_div_16_Template", "ctx_r12", "formatRecordingDuration", "voiceRecordingDuration", "recordingWaveform", "MessageChatComponent_div_37_Template_button_click_1_listener", "_r70", "ctx_r69", "triggerFileInput", "MessageChatComponent_div_37_Template_button_click_5_listener", "ctx_r71", "MessageChatComponent_div_37_Template_button_click_9_listener", "ctx_r72", "MessageChatComponent_div_37_Template_button_click_13_listener", "ctx_r73", "MessageChatComponent_div_37_Template_button_click_17_listener", "ctx_r74", "openCamera", "tmp_0_0", "ctx_r15", "messageForm", "get", "value", "length", "MessageChatComponent_button_46_Template_button_click_0_listener", "_r78", "ctx_r77", "sendMessage", "MessageChatComponent_button_46_i_1_Template", "MessageChatComponent_button_46_i_2_Template", "ctx_r16", "canSendMessage", "_c6", "isSendingMessage", "MessageChatComponent_button_47_Template_button_mousedown_0_listener", "_r80", "ctx_r79", "startVoiceRecording", "MessageChatComponent_button_47_Template_button_mouseup_0_listener", "ctx_r81", "MessageChatComponent_button_47_Template_button_mouseleave_0_listener", "ctx_r82", "MessageChatComponent_button_47_Template_button_touchstart_0_listener", "ctx_r83", "MessageChatComponent_button_47_Template_button_touchend_0_listener", "ctx_r84", "_c7", "ctx_r17", "isRecordingVoice", "MessageChatComponent_div_48_button_3_Template_button_click_0_listener", "_r90", "category_r88", "ctx_r89", "selectEmojiCategory", "_c8", "ctx_r85", "selectedEmojiCategory", "MessageChatComponent_div_48_button_9_Template_button_click_0_listener", "_r93", "emoji_r91", "ctx_r92", "insert<PERSON><PERSON><PERSON>", "char", "MessageChatComponent_div_48_div_10_button_4_Template_button_click_0_listener", "_r97", "emoji_r95", "ctx_r96", "MessageChatComponent_div_48_div_10_button_4_Template", "ctx_r87", "recentEmojis", "MessageChatComponent_div_48_button_3_Template", "MessageChatComponent_div_48_Template_button_click_4_listener", "_r99", "ctx_r98", "toggleEmojiPicker", "MessageChatComponent_div_48_Template_input_ngModelChange_7_listener", "$event", "ctx_r100", "emojiSearchQuery", "MessageChatComponent_div_48_Template_input_input_7_listener", "ctx_r101", "searchEmojis", "MessageChatComponent_div_48_button_9_Template", "MessageChatComponent_div_48_div_10_Template", "ctx_r18", "emojiCategories", "getFilteredEmojis", "notification_r102", "ctx_r24", "MessageChatComponent_div_65_button_7_Template_button_click_0_listener", "_r108", "ctx_r107", "clearSearch", "MessageChatComponent_div_65_div_10_button_4_Template_button_click_0_listener", "_r112", "result_r110", "ctx_r111", "navigateToMessage", "ctx_r109", "highlightSearchTerms", "searchQuery", "ɵɵsanitizeHtml", "MessageChatComponent_div_65_div_10_button_4_Template", "ctx_r105", "searchResults", "ctx_r106", "MessageChatComponent_div_65_Template_input_ngModelChange_5_listener", "_r114", "ctx_r113", "MessageChatComponent_div_65_Template_input_input_5_listener", "ctx_r115", "onSearchInput", "MessageChatComponent_div_65_Template_input_keydown_5_listener", "ctx_r116", "onSearchKeyPress", "MessageChatComponent_div_65_div_6_Template", "MessageChatComponent_div_65_button_7_Template", "MessageChatComponent_div_65_Template_button_click_8_listener", "ctx_r117", "toggleSearchBar", "MessageChatComponent_div_65_div_10_Template", "MessageChatComponent_div_65_div_11_Template", "ctx_r25", "isSearching", "searchMode", "pinnedMessage_r121", "MessageChatComponent_div_66_div_10_button_1_Template_button_click_0_listener", "_r127", "ctx_r126", "scrollToPinnedMessage", "MessageChatComponent_div_66_div_10_button_1_span_11_Template", "MessageChatComponent_div_66_div_10_button_1_span_12_Template", "MessageChatComponent_div_66_div_10_button_1_span_13_Template", "image", "ctx_r120", "pinnedAt", "formatMessageDate", "MessageChatComponent_div_66_div_10_button_1_Template", "ctx_r119", "pinnedMessages", "MessageChatComponent_div_66_Template_button_click_6_listener", "_r129", "ctx_r128", "togglePinnedMessages", "MessageChatComponent_div_66_div_9_Template", "MessageChatComponent_div_66_div_10_Template", "ctx_r26", "getPinnedMessagesCount", "MessageChatComponent_div_67_Template_button_click_12_listener", "_r131", "ctx_r130", "declineCall", "MessageChatComponent_div_67_Template_button_click_14_listener", "ctx_r132", "acceptCall", "ctx_r27", "incomingCall", "caller", "type", "MessageChatComponent_div_68_button_20_Template_button_click_0_listener", "_r144", "ctx_r143", "toggleVideo", "MessageChatComponent_div_68_button_20_i_1_Template", "MessageChatComponent_div_68_button_20_i_2_Template", "ctx_r138", "isVideoEnabled", "MessageChatComponent_div_68_Template_button_click_12_listener", "_r146", "ctx_r145", "toggleCallMinimize", "MessageChatComponent_div_68_i_13_Template", "MessageChatComponent_div_68_i_14_Template", "MessageChatComponent_div_68_div_15_Template", "MessageChatComponent_div_68_Template_button_click_17_listener", "ctx_r147", "toggleMute", "MessageChatComponent_div_68_i_18_Template", "MessageChatComponent_div_68_i_19_Template", "MessageChatComponent_div_68_button_20_Template", "MessageChatComponent_div_68_Template_button_click_21_listener", "ctx_r148", "endCall", "_c9", "ctx_r28", "isCallMinimized", "activeCall", "participant", "formatCallDuration", "callDuration", "getCallStatusText", "_c10", "showCallControls", "isCallMuted", "MessageChatComponent_div_69_Template_button_click_9_listener", "_r150", "ctx_r149", "cancelDelete", "MessageChatComponent_div_69_Template_button_click_11_listener", "ctx_r151", "confirmDelete", "MessageChatComponent_div_70_div_11_Template_div_click_0_listener", "_r155", "contact_r153", "ctx_r154", "toggleContactSelection", "ctx_r152", "selectedContacts", "includes", "MessageChatComponent_div_70_Template_button_click_5_listener", "_r157", "ctx_r156", "closeForwardModal", "MessageChatComponent_div_70_Template_input_ngModelChange_9_listener", "ctx_r158", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MessageChatComponent_div_70_div_11_Template", "MessageChatComponent_div_70_Template_button_click_13_listener", "ctx_r159", "MessageChatComponent_div_70_Template_button_click_15_listener", "ctx_r160", "confirmForward", "ctx_r30", "getFilteredContacts", "MessageChatComponent_div_71_button_10_Template_button_click_0_listener", "_r165", "ctx_r164", "previousImage", "MessageChatComponent_div_71_button_12_Template_button_click_0_listener", "_r167", "ctx_r166", "nextImage", "ctx_r163", "imageGallery", "currentImageIndex", "caption", "MessageChatComponent_div_71_Template_div_click_1_listener", "_r169", "ctx_r168", "closeImageViewer", "MessageChatComponent_div_71_Template_button_click_7_listener", "ctx_r170", "MessageChatComponent_div_71_button_10_Template", "MessageChatComponent_div_71_button_12_Template", "MessageChatComponent_div_71_div_13_Template", "ɵɵtextInterpolate2", "ctx_r31", "url", "ctx_r32", "toastType", "getToastIcon", "toastMessage", "MessageChatComponent_div_73_Template_button_click_4_listener", "_r172", "ctx_r171", "ctx_r33", "email", "getTotalMessagesCount", "getPhotosCount", "getFilesCount", "MessageChatComponent_div_74_Template_button_click_4_listener", "_r174", "ctx_r173", "MessageChatComponent_div_74_Template_input_ngModelChange_12_listener", "ctx_r175", "conversationSettings", "notifications", "MessageChatComponent_div_74_Template_input_change_12_listener", "ctx_r176", "updateConversationSettings", "MessageChatComponent_div_74_Template_input_ngModelChange_17_listener", "ctx_r177", "soundNotifications", "MessageChatComponent_div_74_Template_input_change_17_listener", "ctx_r178", "MessageChatComponent_div_74_Template_input_ngModelChange_25_listener", "ctx_r179", "readReceipts", "MessageChatComponent_div_74_Template_input_change_25_listener", "ctx_r180", "MessageChatComponent_div_74_Template_input_ngModelChange_30_listener", "ctx_r181", "typingIndicators", "MessageChatComponent_div_74_Template_input_change_30_listener", "ctx_r182", "ctx_r34", "MessageChatComponent", "availableReactions", "c", "reactions", "commonEmojis", "MessageService", "getCommonEmojis", "getHeaderActions", "initiateCall", "showSearchBar", "showPinnedMessages", "toggleNotificationPanel", "showNotificationPanel", "unreadNotificationCount", "toggleCallHistoryPanel", "showCallHistoryPanel", "toggleCallStatsPanel", "showCallStatsPanel", "toggleVoiceMessagesPanel", "showVoiceMessagesPanel", "voiceMessages", "constructor", "route", "authService", "fb", "statusService", "router", "toastService", "cdr", "messages", "conversation", "loading", "currentUsername", "isUploading", "isTyping", "MAX_MESSAGES_PER_SIDE", "MAX_MESSAGES_TO_LOAD", "MAX_TOTAL_MESSAGES", "currentPage", "isLoadingMore", "hasMoreMessages", "subscriptions", "selectedTheme", "showThemeSelector", "showMainMenu", "showEmojiPicker", "showStatusSelector", "showNotificationSettings", "showUserStatusPanel", "showCallModal", "showActiveCallModal", "callTimer", "selectedNotifications", "Set", "showDeleteConfirmModal", "editingMessageId", "<PERSON><PERSON><PERSON><PERSON>", "showReactionPicker", "showDeleteConfirm", "showPinConfirm", "isPinning", "showMessageOptions", "showForwardModal", "forwardingMessage", "selectedConversations", "availableConversations", "isForwarding", "isLoadingConversations", "delays", "away", "typing", "scroll", "anim", "error", "trackById", "i", "item", "toString", "NEW_MESSAGE", "CALL_MISSED", "SYSTEM", "status", "online", "text", "offline", "busy", "themes", "calls", "COMPLETED", "MISSED", "REJECTED", "notificationConfig", "callStatusColors", "notificationFilter", "isLoadingNotifications", "isMarkingAsRead", "isDeletingNotifications", "hasMoreNotifications", "notificationSounds", "notificationPreview", "autoMarkAsRead", "callQuality", "onlineUsers", "Map", "lastActivityTime", "Date", "autoAwayTimeout", "callHistory", "localVideoElement", "remoteVideoElement", "statusFilterType", "f", "getIcon", "t", "getFileIcon", "getType", "getFileType", "isCurrentlyTyping", "TYPING_DELAY", "TYPING_TIMEOUT", "mainToggleMethods", "themeSelector", "togglePanel", "mainMenu", "emojiPicker", "toggleThemeSelector", "toggleMainMenu", "toggleMethods", "searchBar", "statusSelector", "notificationSettings", "userStatusPanel", "callMinimize", "callHistoryPanel", "callStatsPanel", "voiceMessagesPanel", "toggleNotificationSettings", "conversationMethods", "showInfo", "showDevelopmentFeature", "showSettings", "clear", "showWarning", "confirm", "showSuccess", "export", "conversation<PERSON>ame", "isGroup", "groupName", "exportData", "participants", "createdAt", "map", "msg", "exportedAt", "toISOString", "exportedBy", "blob", "Blob", "JSON", "stringify", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "safeFileName", "replace", "toLowerCase", "dateStr", "split", "download", "click", "revokeObjectURL", "s", "formatTime", "formatActive", "formatDate", "showDateHeader", "shouldShowDateHeader", "m", "getMessageType", "isVoice", "getVoiceUrl", "getVoiceMessageUrl", "getVoiceDuration", "getVoiceMessageDuration", "getVoiceHeight", "getVoiceBarHeight", "formatVoice", "formatVoiceDuration", "getImageUrl", "getTypeClass", "getMessageTypeClass", "loadingIndicatorMethods", "show", "getElementById", "indicator", "className", "innerHTML", "messagesContainer", "nativeElement", "prepend", "hide", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showLoadingIndicator", "hideLoadingIndicator", "voiceRecordingMethods", "toggle", "complete", "audioBlob", "showError", "receiverId", "sendVoiceMessage", "subscribe", "next", "message", "scrollToBottom", "cancel", "toggleVoiceRecording", "onVoiceRecordingComplete", "onVoiceRecordingCancelled", "callMethods", "initiate", "AUDIO", "VIDEO", "call", "accept", "reject", "rejectCall", "end", "sub", "activeCall$", "unsubscribe", "callControlMethods", "updateMedia", "toggleMedia", "console", "toggleCallMute", "toggleCallVideo", "updateCallMedia", "timerMethods", "startCallTimer", "setInterval", "stopCallTimer", "clearInterval", "resetCallState", "startCallTimerMethod", "stopCallTimerMethod", "resetCallStateMethod", "notificationToggleMethod", "loadNotifications", "notificationMethods", "loadMore", "updateCount", "n", "isRead", "getFiltered", "toggleSelection", "notificationId", "has", "delete", "add", "toggleSelectAll", "filteredNotifications", "allSelected", "every", "for<PERSON>ach", "areAllSelected", "loadMoreNotifications", "updateNotificationCount", "getFilteredNotifications", "toggleNotificationSelection", "toggleSelectAllNotifications", "areAllNotificationsSelected", "markingMethods", "markSelected", "selectedIds", "Array", "from", "mark<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "unreadNotifications", "unreadIds", "ids", "onSuccess", "mark<PERSON>ub", "result", "readAt", "markSelectedAsRead", "markAllAsRead", "notificationDeleteMethods", "showDeleteSelectedConfirmation", "deleteSelected", "deleteSub", "deleteMultipleNotifications", "deleteOne", "deleteNotification", "deleteAll", "deleteAllSub", "deleteAllNotifications", "deleteSelectedNotifications", "cancelDeleteNotifications", "notificationUtilMethods", "date", "getColor", "index", "notification", "formatNotificationDate", "getNotificationIcon", "getNotificationColor", "trackByNotificationId", "panelMethods", "getActivePanels", "panels", "push", "closeAction", "Object", "entries", "config", "statusMethods", "getText", "getStatusIcon", "utilityMethods", "formatLastSeen", "values", "user", "getFilteredUsers", "setStatus<PERSON>ilter", "replyForwardMethods", "startReply", "openForwardModal", "startReplyToMessage", "messageMethods", "getPinIcon", "isMessagePinned", "getPinDisplayText", "canEditMessage", "isPinned", "editMethods", "startEditMessage", "cancelEditMessage", "saveEditMessage", "messageId", "onEditKeyPress", "event", "shift<PERSON>ey", "preventDefault", "callUtilities", "getCallStatusColor", "getCallTypeIcon", "duration", "minutes", "Math", "floor", "seconds", "padStart", "formatCallDate", "eventMethods", "onDocumentClick", "target", "closeConfigs", "selectors", "property", "isClickOutside", "some", "selector", "closest", "confirmDeleteMessage", "reactionMethods", "getUniqueReactions", "onReactionClick", "emoji", "hasUserReacted", "conversationSelectionMethods", "selectAll", "deselectAll", "conversationId", "indexOf", "splice", "isSelected", "getDisplayImage", "getDisplayName", "forwardMessage", "setTimeout", "areAllConversationsSelected", "selectAllConversations", "deselectAllConversations", "toggleConversationSelection", "isConversationSelected", "getConversationDisplayImage", "getConversationDisplayName", "simpleNotificationMethods", "onCallMouseMove", "saveNotificationSettings", "setNotificationFilter", "searchMethods", "onInput", "perform", "onKeyPress", "performSearch", "finalUtilityMethods", "searchAndReactionMethods", "query", "regex", "RegExp", "toggleReactionPicker", "reactToMessage", "toggleMessageOptions", "confirmationMethods", "showPinConfirmation", "cancelPinConfirmation", "showDeleteConfirmation", "cancelDeleteMessage", "cleanup", "clearTimeouts", "typingTimeout", "clearTimeout", "setUserOffline", "stopTypingIndicator", "typingTimer", "stopTyping", "group", "max<PERSON><PERSON><PERSON>", "ngOnInit", "getCurrentUserId", "savedTheme", "localStorage", "getItem", "subscribeToNotifications", "subscribeToUserStatus", "initializeUserStatus", "startActivityTracking", "addEventListener", "bind", "routeSub", "params", "pipe", "getConversation", "handleConversationLoaded", "handleError", "resetLoading", "handleSuccess", "callback", "find", "p", "_id", "conversationMessages", "sort", "a", "b", "timeA", "getTime", "timeB", "markMessagesAsRead", "subscribeToConversationUpdates", "subscribeToNewMessages", "subscribeToTypingIndicators", "updatedConversation", "newMessage", "markMessageAsRead", "subscribeToTypingIndicator", "userId", "unreadMessages", "receiver", "onFileSelected", "file", "files", "validTypes", "reader", "FileReader", "onload", "readAsDataURL", "removeAttachment", "fileInput", "onTyping", "startTyping", "panelName", "closeOthers", "theme", "menu", "search", "currentPanel", "panel", "setItem", "featureName", "invalid", "tempMessage", "isPending", "fileType", "startsWith", "attachments", "IMAGE", "FILE", "fileToSend", "reset", "sendSub", "undefined", "TEXT", "updateMessageState", "tempId", "isError", "onScroll", "container", "scrollTop", "oldScrollHeight", "scrollHeight", "firstVisibleMessage", "getFirstVisibleMessage", "loadMoreMessages", "requestAnimationFrame", "preserveScrollPosition", "messageElement", "findMessageElement", "scrollIntoView", "block", "newScrollHeight", "scrollDiff", "messageElements", "querySelectorAll", "element", "rect", "getBoundingClientRect", "top", "bottom", "clientHeight", "getAttribute", "querySelector", "oldMessages", "existingIds", "newMessages", "slice", "isSameTimestamp", "timestamp1", "timestamp2", "time1", "time2", "abs", "force", "isScrolledToBottom", "scrollTo", "behavior", "err", "openImageFullscreen", "imageUrl", "open", "ngAfterViewChecked", "goBackToConversations", "navigate", "control", "currentValue", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputElement", "focus", "notificationSub", "subscribeToNewNotifications", "unshift", "play", "notificationsListSub", "notifications$", "notificationCountSub", "notificationCount$", "callSub", "incomingCall$", "activeCallSub", "localStreamSub", "localStream$", "stream", "srcObject", "remoteStreamSub", "remoteStream$", "refresh", "loadSub", "getNotifications", "statusSub", "handleUserStatusUpdate", "set", "setOnlineSub", "setUserOnline", "events", "onUserActivity", "previousStatus", "updateObservable", "updateSub", "statusText", "loadOnlineUsers", "usersSub", "getAllUsers", "users", "togglePinMessage", "ngOnDestroy", "removeEventListener", "ɵɵdirectiveInject", "i1", "i2", "ActivatedRoute", "i3", "AuthuserService", "i4", "FormBuilder", "i5", "UserStatusService", "Router", "i6", "ToastService", "ChangeDetectorRef", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_div_5_Template", "MessageChatComponent_ng_container_7_Template", "MessageChatComponent_Template_button_click_9_listener", "MessageChatComponent_span_11_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_Template_button_click_14_listener", "MessageChatComponent_div_16_Template", "MessageChatComponent_Template_button_click_18_listener", "MessageChatComponent_div_20_Template", "MessageChatComponent_div_23_Template", "MessageChatComponent_div_24_Template", "MessageChatComponent_div_25_Template", "MessageChatComponent_div_26_Template", "MessageChatComponent_div_27_Template", "MessageChatComponent_div_28_Template", "MessageChatComponent_Template_button_click_32_listener", "MessageChatComponent_Template_button_click_35_listener", "toggleAttachmentMenu", "MessageChatComponent_div_37_Template", "MessageChatComponent_Template_form_ngSubmit_39_listener", "MessageChatComponent_Template_textarea_input_41_listener", "onInputChange", "MessageChatComponent_Template_textarea_keydown_41_listener", "onInputKeyDown", "MessageChatComponent_Template_textarea_focus_41_listener", "onInputFocus", "MessageChatComponent_Template_textarea_blur_41_listener", "onInputBlur", "MessageChatComponent_div_43_Template", "MessageChatComponent_button_46_Template", "MessageChatComponent_button_47_Template", "MessageChatComponent_div_48_Template", "MessageChatComponent_Template_input_change_49_listener", "MessageChatComponent_Template_input_change_51_listener", "MessageChatComponent_Template_input_change_53_listener", "MessageChatComponent_Template_input_change_55_listener", "MessageChatComponent_Template_button_click_61_listener", "MessageChatComponent_div_63_Template", "MessageChatComponent_div_64_Template", "MessageChatComponent_div_65_Template", "MessageChatComponent_div_66_Template", "MessageChatComponent_div_67_Template", "MessageChatComponent_div_68_Template", "MessageChatComponent_div_69_Template", "MessageChatComponent_div_70_Template", "MessageChatComponent_div_71_Template", "MessageChatComponent_div_72_Template", "MessageChatComponent_div_73_Template", "MessageChatComponent_div_74_Template", "_c11", "showAttachmentMenu", "tmp_21_0", "hasMessageContent", "_c12", "showImageViewer", "showToast", "showConversationInfo", "showConversationSettings"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ElementRef,\n  AfterViewChecked,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { User } from '@app/models/user.model';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport {\n  Message,\n  Conversation,\n  MessageType,\n  CallType,\n} from 'src/app/models/message.model';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { MessageService } from '@app/services/message.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: 'message-chat.component.html',\n  styleUrls: ['./message-chat.component.css'],\n})\nexport class MessageChatComponent\n  implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, AfterViewChecked\n{\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  messages: Message[] = [];\n  messageForm: FormGroup;\n  conversation: Conversation | null = null;\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  currentUsername: string = 'You';\n  otherParticipant: User | null = null;\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n  isUploading = false;\n  isTyping = false;\n  typingTimeout: any;\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n\n  private readonly MAX_MESSAGES_PER_SIDE = 5;\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private readonly MAX_TOTAL_MESSAGES = 100;\n  private currentPage = 1;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  private subscriptions = new Subscription();\n\n  // Interface\n  selectedTheme: string = 'theme-default';\n\n  // États\n  showThemeSelector = false;\n  showMainMenu = false;\n  showEmojiPicker = false;\n  showSearchBar = false;\n  showPinnedMessages = false;\n  showStatusSelector = false;\n  showNotificationPanel = false;\n  showNotificationSettings = false;\n  showUserStatusPanel = false;\n  showCallHistoryPanel = false;\n  showCallStatsPanel = false;\n  showVoiceMessagesPanel = false;\n\n  // Appels\n  incomingCall: any = null;\n  activeCall: any = null;\n  showCallModal = false;\n  showActiveCallModal = false;\n  isCallMuted = false;\n  isVideoEnabled = true;\n  callDuration = 0;\n  callTimer: any = null;\n\n  // Notifications et messages\n  notifications: any[] = [];\n  unreadNotificationCount: number = 0;\n  selectedNotifications: Set<string> = new Set();\n  showDeleteConfirmModal: boolean = false;\n  editingMessageId: string | null = null;\n  editingContent = '';\n  replyingToMessage: any = null;\n\n  // Recherche\n  searchQuery = '';\n  isSearching = false;\n  searchMode = false;\n  searchResults: any[] = [];\n\n  // Panneaux\n  pinnedMessages: any[] = [];\n  showReactionPicker: { [key: string]: boolean } = {};\n  showDeleteConfirm: { [key: string]: boolean } = {};\n  showPinConfirm: { [key: string]: boolean } = {};\n  isPinning: { [key: string]: boolean } = {};\n  showMessageOptions: { [key: string]: boolean } = {};\n\n  // Variables de transfert\n  showForwardModal = false;\n  forwardingMessage: any = null;\n  selectedConversations: string[] = [];\n  availableConversations: any[] = [];\n  isForwarding = false;\n  isLoadingConversations = false;\n\n  // Constantes optimisées\n  readonly c = {\n    reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n    delays: { away: 300000, typing: 3000, scroll: 100, anim: 300 },\n    error: 'Erreur. Veuillez réessayer.',\n    trackById: (i: number, item: any): string => item?.id || i.toString(),\n    notifications: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    status: {\n      online: {\n        text: 'En ligne',\n        color: 'text-green-500',\n        icon: 'fas fa-circle',\n        label: 'En ligne',\n        description: 'Disponible pour discuter',\n      },\n      offline: {\n        text: 'Hors ligne',\n        color: 'text-gray-500',\n        icon: 'far fa-circle',\n        label: 'Hors ligne',\n        description: 'Invisible pour tous',\n      },\n      away: {\n        text: 'Absent',\n        color: 'text-yellow-500',\n        icon: 'fas fa-clock',\n        label: 'Absent',\n        description: 'Absent temporairement',\n      },\n      busy: {\n        text: 'Occupé',\n        color: 'text-red-500',\n        icon: 'fas fa-minus-circle',\n        label: 'Occupé',\n        description: 'Ne pas déranger',\n      },\n    },\n    themes: [\n      {\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad',\n        hoverColor: '#4f5fad',\n      },\n      {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d',\n        hoverColor: '#ff6b9d',\n      },\n      {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6',\n        hoverColor: '#3d85c6',\n      },\n      {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f',\n        hoverColor: '#6aa84f',\n      },\n    ],\n    calls: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n    // Alias pour compatibilité\n    notificationConfig: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    callStatusColors: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n  };\n\n  // Getter pour compatibilité\n  get availableReactions(): string[] {\n    return this.c.reactions;\n  }\n\n  // Variables de notification\n  notificationFilter = 'all';\n  isLoadingNotifications = false;\n  isMarkingAsRead = false;\n  isDeletingNotifications = false;\n  hasMoreNotifications = false;\n  notificationSounds = true;\n  notificationPreview = true;\n  autoMarkAsRead = true;\n\n  // Variables d'appel\n  isCallMinimized = false;\n  callQuality = 'connecting';\n  showCallControls = false;\n\n  // Variables de statut utilisateur\n  onlineUsers: Map<string, User> = new Map();\n  currentUserStatus: string = 'online';\n  lastActivityTime: Date = new Date();\n  autoAwayTimeout: any = null;\n  isUpdatingStatus = false;\n  callHistory: any[] = [];\n  voiceMessages: any[] = [];\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n  statusFilterType = 'all';\n\n  // Emojis du service\n  get commonEmojis(): string[] {\n    return this.MessageService.getCommonEmojis();\n  }\n\n  // Configuration des boutons d'action de l'en-tête\n  getHeaderActions() {\n    return [\n      {\n        class: 'btn-audio-call',\n        icon: 'fas fa-phone-alt',\n        title: 'Appel audio',\n        onClick: () => this.initiateCall('AUDIO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-video-call',\n        icon: 'fas fa-video',\n        title: 'Appel vidéo',\n        onClick: () => this.initiateCall('VIDEO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-search',\n        icon: 'fas fa-search',\n        title: 'Rechercher',\n        onClick: () => this.toggleSearchBar(),\n        isActive: this.showSearchBar,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-pinned relative',\n        icon: 'fas fa-thumbtack',\n        title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n        onClick: () => this.togglePinnedMessages(),\n        isActive: this.showPinnedMessages,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.getPinnedMessagesCount() > 0\n            ? {\n                count: this.getPinnedMessagesCount(),\n                class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n                animate: false,\n              }\n            : null,\n      },\n      {\n        class: 'btn-notifications relative',\n        icon: 'fas fa-bell',\n        title: 'Notifications',\n        onClick: () => this.toggleNotificationPanel(),\n        isActive: this.showNotificationPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.unreadNotificationCount > 0\n            ? {\n                count: this.unreadNotificationCount,\n                class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n                animate: true,\n              }\n            : null,\n      },\n      {\n        class: 'btn-history relative',\n        icon: 'fas fa-history',\n        title: 'Historique des appels',\n        onClick: () => this.toggleCallHistoryPanel(),\n        isActive: this.showCallHistoryPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-stats relative',\n        icon: 'fas fa-chart-bar',\n        title: \"Statistiques d'appels\",\n        onClick: () => this.toggleCallStatsPanel(),\n        isActive: this.showCallStatsPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-voice-messages relative',\n        icon: 'fas fa-microphone',\n        title: 'Messages vocaux',\n        onClick: () => this.toggleVoiceMessagesPanel(),\n        isActive: this.showVoiceMessagesPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.voiceMessages.length > 0\n            ? {\n                count: this.voiceMessages.length,\n                class: 'bg-[#4f5fad]',\n                animate: false,\n              }\n            : null,\n      },\n    ];\n  }\n\n  constructor(\n    private MessageService: MessageService,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private fb: FormBuilder,\n    public statusService: UserStatusService,\n    public router: Router,\n    private toastService: ToastService,\n\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]],\n    });\n  }\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n    }\n\n    this.subscribeToNotifications();\n    this.subscribeToUserStatus();\n    this.initializeUserStatus();\n    this.startActivityTracking();\n\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n\n    const routeSub = this.route.params\n      .pipe(\n        filter((params) => params['id']),\n        distinctUntilChanged(),\n        switchMap((params) => {\n          this.loading = true;\n          this.messages = [];\n          this.currentPage = 1;\n          this.hasMoreMessages = true;\n\n          return this.MessageService.getConversation(\n            params['id'],\n            this.MAX_MESSAGES_TO_LOAD,\n            this.currentPage\n          );\n        })\n      )\n      .subscribe({\n        next: (conversation) => {\n          this.handleConversationLoaded(conversation);\n        },\n        error: (error) => {\n          this.handleError('Failed to load conversation', error);\n        },\n      });\n    this.subscriptions.add(routeSub);\n  }\n\n  // Gestion centralisée des erreurs\n  private handleError(\n    message: string,\n    error: any,\n    resetLoading: boolean = true\n  ): void {\n    console.error('MessageChat', message, error);\n    if (resetLoading) {\n      this.loading = false;\n      this.isUploading = false;\n      this.isLoadingMore = false;\n    }\n    this.error = error;\n    this.toastService.showError(message);\n  }\n\n  // Gestion centralisée des succès\n  private handleSuccess(message?: string, callback?: () => void): void {\n    if (message) {\n      this.toastService.showSuccess(message);\n    }\n    if (callback) {\n      callback();\n    }\n  }\n\n  // Services de fichiers optimisés\n  readonly f = {\n    getIcon: (t?: string) => this.MessageService.getFileIcon(t),\n    getType: (t?: string) => this.MessageService.getFileType(t),\n  };\n\n  getFileIcon = this.f.getIcon;\n  getFileType = this.f.getType;\n\n  private handleConversationLoaded(conversation: Conversation): void {\n    this.conversation = conversation;\n\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.otherParticipant =\n        conversation?.participants?.find(\n          (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n        ) || null;\n      this.messages = [];\n    } else {\n      const conversationMessages = [...(conversation?.messages || [])];\n\n      conversationMessages.sort((a, b) => {\n        const timeA =\n          a.timestamp instanceof Date\n            ? a.timestamp.getTime()\n            : new Date(a.timestamp as string).getTime();\n        const timeB =\n          b.timestamp instanceof Date\n            ? b.timestamp.getTime()\n            : new Date(b.timestamp as string).getTime();\n        return timeA - timeB;\n      });\n\n      this.messages = conversationMessages;\n    }\n\n    this.otherParticipant =\n      conversation?.participants?.find(\n        (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n      ) || null;\n\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n\n    this.markMessagesAsRead();\n\n    if (this.conversation?.id) {\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n  }\n\n  private subscribeToConversationUpdates(conversationId: string): void {\n    const sub = this.MessageService.subscribeToConversationUpdates(\n      conversationId\n    ).subscribe({\n      next: (updatedConversation) => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages\n          ? [...updatedConversation.messages]\n          : [];\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to conversation updates lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToNewMessages(conversationId: string): void {\n    const sub = this.MessageService.subscribeToNewMessages(\n      conversationId\n    ).subscribe({\n      next: (newMessage) => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA =\n              a.timestamp instanceof Date\n                ? a.timestamp.getTime()\n                : new Date(a.timestamp as string).getTime();\n            const timeB =\n              b.timestamp instanceof Date\n                ? b.timestamp.getTime()\n                : new Date(b.timestamp as string).getTime();\n            return timeA - timeB;\n          });\n\n          setTimeout(() => this.scrollToBottom(), 100);\n\n          if (\n            newMessage.sender?.id !== this.currentUserId &&\n            newMessage.sender?._id !== this.currentUserId\n          ) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to new messages lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToTypingIndicators(conversationId: string): void {\n    const sub = this.MessageService.subscribeToTypingIndicator(\n      conversationId\n    ).subscribe({\n      next: (event) => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private markMessagesAsRead(): void {\n    const unreadMessages = this.messages.filter(\n      (msg) =>\n        !msg.isRead &&\n        (msg.receiver?.id === this.currentUserId ||\n          msg.receiver?._id === this.currentUserId)\n    );\n\n    unreadMessages.forEach((msg) => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: (error) => {},\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n\n    const validTypes = [\n      'image/jpeg',\n      'image/png',\n      'image/gif',\n      'application/pdf',\n      'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    ];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError(\n        'Invalid file type. Only images, PDFs and Word docs are allowed'\n      );\n      return;\n    }\n\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n\n  removeAttachment(): void {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n\n  private typingTimer: any;\n  private isCurrentlyTyping = false;\n  private readonly TYPING_DELAY = 500;\n  private readonly TYPING_TIMEOUT = 3000;\n\n  // Frappe\n  onTyping(): void {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n\n    const conversationId = this.conversation.id;\n    clearTimeout(this.typingTimer);\n\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {},\n        error: (error) => {},\n      });\n    }\n\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {},\n          error: (error) => {},\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n\n  // Panneaux\n  private togglePanel(panelName: string, closeOthers: boolean = true): void {\n    const panels = {\n      theme: 'showThemeSelector',\n      menu: 'showMainMenu',\n      emoji: 'showEmojiPicker',\n      notification: 'showNotificationPanel',\n      search: 'showSearchBar',\n      status: 'showStatusSelector',\n    };\n\n    const currentPanel = panels[panelName as keyof typeof panels];\n    if (currentPanel) {\n      (this as any)[currentPanel] = !(this as any)[currentPanel];\n\n      if (closeOthers && (this as any)[currentPanel]) {\n        Object.values(panels).forEach((panel) => {\n          if (panel !== currentPanel) {\n            (this as any)[panel] = false;\n          }\n        });\n      }\n    }\n  }\n\n  // Méthodes de basculement principales consolidées\n  readonly mainToggleMethods = {\n    themeSelector: () => this.togglePanel('theme'),\n    mainMenu: () => this.togglePanel('menu'),\n    emojiPicker: () => this.togglePanel('emoji'),\n  };\n\n  toggleThemeSelector = this.mainToggleMethods.themeSelector;\n  toggleMainMenu = this.mainToggleMethods.mainMenu;\n  toggleEmojiPicker = this.mainToggleMethods.emojiPicker;\n\n  changeTheme(theme: string): void {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    localStorage.setItem('chat-theme', theme);\n  }\n\n  // Méthodes toggle consolidées\n  private readonly toggleMethods = {\n    pinnedMessages: () => (this.showPinnedMessages = !this.showPinnedMessages),\n    searchBar: () => {\n      this.togglePanel('search');\n      if (!this.showSearchBar) this.clearSearch();\n    },\n    statusSelector: () => this.togglePanel('status'),\n    notificationSettings: () =>\n      (this.showNotificationSettings = !this.showNotificationSettings),\n    userStatusPanel: () =>\n      (this.showUserStatusPanel = !this.showUserStatusPanel),\n    callMinimize: () => (this.isCallMinimized = !this.isCallMinimized),\n    callHistoryPanel: () =>\n      (this.showCallHistoryPanel = !this.showCallHistoryPanel),\n    callStatsPanel: () => (this.showCallStatsPanel = !this.showCallStatsPanel),\n    voiceMessagesPanel: () =>\n      (this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel),\n  };\n\n  togglePinnedMessages = this.toggleMethods.pinnedMessages;\n  toggleSearchBar = this.toggleMethods.searchBar;\n  toggleStatusSelector = this.toggleMethods.statusSelector;\n  toggleNotificationSettings = this.toggleMethods.notificationSettings;\n  toggleUserStatusPanel = this.toggleMethods.userStatusPanel;\n  toggleCallMinimize = this.toggleMethods.callMinimize;\n  toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;\n  toggleCallStatsPanel = this.toggleMethods.callStatsPanel;\n  toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;\n\n  // Conversation - méthode utilitaire\n  private showDevelopmentFeature(featureName: string): void {\n    this.showMainMenu = false;\n    this.toastService.showInfo(`${featureName} en cours de développement`);\n  }\n\n  readonly conversationMethods = {\n    showInfo: () => this.showDevelopmentFeature('Informations de conversation'),\n    showSettings: () =>\n      this.showDevelopmentFeature('Paramètres de conversation'),\n    clear: (): void => {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à vider');\n        return;\n      }\n\n      if (\n        confirm(\n          'Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.'\n        )\n      ) {\n        this.messages = [];\n        this.showMainMenu = false;\n        this.toastService.showSuccess('Conversation vidée avec succès');\n      }\n    },\n    export: (): void => {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à exporter');\n        return;\n      }\n\n      const conversationName = this.conversation.isGroup\n        ? this.conversation.groupName || 'Groupe sans nom'\n        : this.otherParticipant?.username || 'Conversation privée';\n\n      const exportData = {\n        conversation: {\n          id: this.conversation.id,\n          name: conversationName,\n          isGroup: this.conversation.isGroup,\n          participants: this.conversation.participants,\n          createdAt: this.conversation.createdAt,\n        },\n        messages: this.messages.map((msg) => ({\n          id: msg.id,\n          content: msg.content,\n          sender: msg.sender,\n          timestamp: msg.timestamp,\n          type: msg.type,\n        })),\n        exportedAt: new Date().toISOString(),\n        exportedBy: this.currentUserId,\n      };\n\n      const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n        type: 'application/json',\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n\n      const safeFileName = conversationName\n        .replace(/[^a-z0-9]/gi, '_')\n        .toLowerCase();\n      const dateStr = new Date().toISOString().split('T')[0];\n      link.download = `conversation-${safeFileName}-${dateStr}.json`;\n\n      link.click();\n      window.URL.revokeObjectURL(url);\n\n      this.showMainMenu = false;\n      this.toastService.showSuccess('Conversation exportée avec succès');\n    },\n  };\n\n  // Template methods\n  toggleConversationInfo = this.conversationMethods.showInfo;\n  toggleConversationSettings = this.conversationMethods.showSettings;\n  clearConversation = this.conversationMethods.clear;\n  exportConversation = this.conversationMethods.export;\n\n  sendMessage(): void {\n    if (\n      (this.messageForm.invalid && !this.selectedFile) ||\n      !this.currentUserId ||\n      !this.otherParticipant?.id\n    ) {\n      return;\n    }\n\n    this.cleanup.stopTypingIndicator();\n\n    const content = this.messageForm.get('content')?.value;\n\n    const tempMessage: Message = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername,\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient',\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true,\n    };\n\n    if (this.selectedFile) {\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n\n        if (this.previewUrl) {\n          tempMessage.attachments = [\n            {\n              id: 'temp-attachment',\n              url: this.previewUrl ? this.previewUrl.toString() : '',\n              type: MessageType.IMAGE,\n              name: this.selectedFile.name,\n              size: this.selectedFile.size,\n            },\n          ];\n        }\n      }\n\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n\n    this.messages = [...this.messages, tempMessage];\n\n    const fileToSend = this.selectedFile;\n    this.messageForm.reset();\n    this.removeAttachment();\n\n    setTimeout(() => this.scrollToBottom(true), 50);\n\n    this.isUploading = true;\n\n    const sendSub = this.MessageService.sendMessage(\n      this.otherParticipant.id,\n      content,\n      fileToSend || undefined,\n      MessageType.TEXT,\n      this.conversation?.id\n    ).subscribe({\n      next: (message) => {\n        this.updateMessageState(tempMessage.id!, message);\n        this.isUploading = false;\n      },\n      error: (error) => {\n        this.updateMessageState(tempMessage.id!, null, true);\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      },\n    });\n\n    this.subscriptions.add(sendSub);\n  }\n\n  // Méthode consolidée pour mettre à jour l'état des messages\n  private updateMessageState(\n    tempId: string,\n    newMessage?: Message | null,\n    isError: boolean = false\n  ): void {\n    this.messages = this.messages.map((msg) => {\n      if (msg.id === tempId) {\n        if (newMessage) {\n          return newMessage;\n        } else if (isError) {\n          return {\n            ...msg,\n            isPending: false,\n            isError: true,\n          };\n        }\n      }\n      return msg;\n    });\n  }\n\n  // Service - méthodes optimisées\n  readonly s = {\n    formatTime: (t: string | Date | undefined) =>\n      this.MessageService.formatMessageTime(t),\n    formatActive: (t: string | Date | undefined) =>\n      this.MessageService.formatLastActive(t),\n    formatDate: (t: string | Date | undefined) =>\n      this.MessageService.formatMessageDate(t),\n    showDateHeader: (i: number) =>\n      this.MessageService.shouldShowDateHeader(this.messages, i),\n    getType: (m: Message | null | undefined) =>\n      this.MessageService.getMessageType(m),\n    hasImage: (m: Message | null | undefined) =>\n      this.MessageService.hasImage(m),\n    isVoice: (m: Message | null | undefined) =>\n      this.MessageService.isVoiceMessage(m),\n    getVoiceUrl: (m: Message | null | undefined) =>\n      this.MessageService.getVoiceMessageUrl(m),\n    getVoiceDuration: (m: Message | null | undefined) =>\n      this.MessageService.getVoiceMessageDuration(m),\n    getVoiceHeight: (i: number) => this.MessageService.getVoiceBarHeight(i),\n    formatVoice: (s: number) => this.MessageService.formatVoiceDuration(s),\n    getImageUrl: (m: Message | null | undefined) =>\n      this.MessageService.getImageUrl(m),\n    getTypeClass: (m: Message | null | undefined) =>\n      this.MessageService.getMessageTypeClass(m, this.currentUserId),\n  };\n\n  // Méthodes exposées optimisées\n  formatMessageTime = this.s.formatTime;\n  formatLastActive = this.s.formatActive;\n  formatMessageDate = this.s.formatDate;\n  shouldShowDateHeader = this.s.showDateHeader;\n  getMessageType = this.s.getType;\n  hasImage = this.s.hasImage;\n  isVoiceMessage = this.s.isVoice;\n  getVoiceMessageUrl = this.s.getVoiceUrl;\n  getVoiceMessageDuration = this.s.getVoiceDuration;\n  getVoiceBarHeight = this.s.getVoiceHeight;\n  formatVoiceDuration = this.s.formatVoice;\n  getImageUrl = this.s.getImageUrl;\n  getMessageTypeClass = this.s.getTypeClass;\n\n  // Défilement\n  onScroll(event: any): void {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n\n    if (\n      scrollTop < 50 &&\n      !this.isLoadingMore &&\n      this.conversation?.id &&\n      this.hasMoreMessages\n    ) {\n      this.showLoadingIndicator();\n\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n\n      this.isLoadingMore = true;\n\n      this.loadMoreMessages();\n\n      // Maintenir la position de défilement\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(\n              firstVisibleMessage.id\n            );\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({ block: 'center' });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n\n  // Méthode pour trouver le premier message visible dans la vue\n  private getFirstVisibleMessage(): Message | null {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length)\n      return null;\n\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find((m) => m.id === messageId) || null;\n      }\n    }\n\n    return null;\n  }\n\n  // Méthode pour trouver un élément de message par ID\n  private findMessageElement(\n    messageId: string | undefined\n  ): HTMLElement | null {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(\n      `[data-message-id=\"${messageId}\"]`\n    );\n  }\n\n  // Indicateurs de chargement consolidés\n  readonly loadingIndicatorMethods = {\n    show: () => {\n      if (!document.getElementById('message-loading-indicator')) {\n        const indicator = document.createElement('div');\n        indicator.id = 'message-loading-indicator';\n        indicator.className = 'text-center py-2 text-gray-500 text-sm';\n        indicator.innerHTML =\n          '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n        this.messagesContainer?.nativeElement?.prepend(indicator);\n      }\n    },\n    hide: () => {\n      const indicator = document.getElementById('message-loading-indicator');\n      indicator?.parentNode?.removeChild(indicator);\n    },\n  };\n\n  private showLoadingIndicator = this.loadingIndicatorMethods.show;\n  private hideLoadingIndicator = this.loadingIndicatorMethods.hide;\n\n  // Charger plus de messages\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages)\n      return;\n\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n\n    // Augmenter la page\n    this.currentPage++;\n\n    // Charger plus de messages\n    this.MessageService.getConversation(\n      this.conversation.id,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        if (\n          conversation &&\n          conversation.messages &&\n          conversation.messages.length > 0\n        ) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n\n          // Créer un Set des IDs existants\n          const existingIds = new Set(oldMessages.map((msg) => msg.id));\n\n          // Filtrer et trier les nouveaux messages\n          const newMessages = conversation.messages\n            .filter((msg) => !existingIds.has(msg.id))\n            .sort((a, b) => {\n              const timeA = new Date(a.timestamp as string).getTime();\n              const timeB = new Date(b.timestamp as string).getTime();\n              return timeA - timeB;\n            });\n\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages =\n              newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: (error) => {\n        console.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      },\n    });\n  }\n\n  // Méthode utilitaire pour comparer les timestamps\n  private isSameTimestamp(\n    timestamp1: string | Date | undefined,\n    timestamp2: string | Date | undefined\n  ): boolean {\n    if (!timestamp1 || !timestamp2) return false;\n\n    try {\n      const time1 =\n        timestamp1 instanceof Date\n          ? timestamp1.getTime()\n          : new Date(timestamp1 as string).getTime();\n      const time2 =\n        timestamp2 instanceof Date\n          ? timestamp2.getTime()\n          : new Date(timestamp2 as string).getTime();\n      return Math.abs(time1 - time2) < 1000;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  scrollToBottom(force: boolean = false): void {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom =\n          container.scrollHeight - container.clientHeight <=\n          container.scrollTop + 150;\n\n        if (force || isScrolledToBottom) {\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth',\n          });\n        }\n      });\n    } catch (err) {\n      console.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n\n  // Méthodes d'enregistrement vocal consolidées\n  readonly voiceRecordingMethods = {\n    toggle: () => {\n      this.isRecordingVoice = !this.isRecordingVoice;\n      if (!this.isRecordingVoice) this.voiceRecordingDuration = 0;\n    },\n    complete: (audioBlob: Blob) => {\n      if (!this.conversation?.id && !this.otherParticipant?.id) {\n        this.toastService.showError('No conversation or recipient selected');\n        this.isRecordingVoice = false;\n        return;\n      }\n      const receiverId = this.otherParticipant?.id || '';\n      this.MessageService.sendVoiceMessage(\n        receiverId,\n        audioBlob,\n        this.conversation?.id,\n        this.voiceRecordingDuration\n      ).subscribe({\n        next: (message) => {\n          this.isRecordingVoice = false;\n          this.voiceRecordingDuration = 0;\n          this.scrollToBottom(true);\n        },\n        error: (error) => {\n          this.toastService.showError('Failed to send voice message');\n          this.isRecordingVoice = false;\n        },\n      });\n    },\n    cancel: () => {\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n    },\n  };\n\n  toggleVoiceRecording = this.voiceRecordingMethods.toggle;\n  onVoiceRecordingComplete = this.voiceRecordingMethods.complete;\n  onVoiceRecordingCancelled = this.voiceRecordingMethods.cancel;\n\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl: string): void {\n    window.open(imageUrl, '_blank');\n  }\n\n  /**\n   * Détecte les changements après chaque vérification de la vue - Optimisé\n   */\n  ngAfterViewChecked(): void {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n\n    // Détection des changements optimisée - Suppression de la vérification inutile\n    // La détection automatique d'Angular gère déjà les messages vocaux\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji: string): void {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector(\n          '.whatsapp-input-field'\n        ) as HTMLInputElement;\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  private subscribeToNotifications(): void {\n    const notificationSub =\n      this.MessageService.subscribeToNewNotifications().subscribe({\n        next: (notification) => {\n          this.notifications.unshift(notification);\n          this.updateNotificationCount();\n\n          this.MessageService.play('notification');\n\n          if (\n            notification.type === 'NEW_MESSAGE' &&\n            notification.conversationId === this.conversation?.id\n          ) {\n            if (notification.id) {\n              this.MessageService.markAsRead([notification.id]).subscribe();\n            }\n          }\n        },\n        error: (error) => {},\n      });\n    this.subscriptions.add(notificationSub);\n\n    const notificationsListSub = this.MessageService.notifications$.subscribe({\n      next: (notifications) => {\n        this.notifications = notifications;\n        this.updateNotificationCount();\n      },\n      error: (error) => {},\n    });\n    this.subscriptions.add(notificationsListSub);\n\n    const notificationCountSub =\n      this.MessageService.notificationCount$.subscribe({\n        next: (count) => {\n          this.unreadNotificationCount = count;\n        },\n      });\n    this.subscriptions.add(notificationCountSub);\n\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: (call) => {\n        if (call) {\n          this.incomingCall = call;\n          this.showCallModal = true;\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      },\n    });\n    this.subscriptions.add(callSub);\n\n    const activeCallSub = this.MessageService.activeCall$.subscribe({\n      next: (call) => {\n        this.activeCall = call;\n        if (call) {\n          this.showActiveCallModal = true;\n          this.startCallTimerMethod();\n        } else {\n          this.showActiveCallModal = false;\n          this.stopCallTimerMethod();\n          this.resetCallStateMethod();\n        }\n      },\n    });\n    this.subscriptions.add(activeCallSub);\n\n    // S'abonner aux flux vidéo locaux\n    const localStreamSub = this.MessageService.localStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.localVideoElement) {\n          this.localVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(localStreamSub);\n\n    // S'abonner aux flux vidéo distants\n    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.remoteVideoElement) {\n          this.remoteVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(remoteStreamSub);\n  }\n\n  // Méthodes d'appel consolidées\n  readonly callMethods = {\n    initiate: (type: 'AUDIO' | 'VIDEO') => {\n      if (!this.otherParticipant?.id) return;\n      this.MessageService.initiateCall(\n        this.otherParticipant.id,\n        type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO,\n        this.conversation?.id\n      ).subscribe({\n        next: (call) => {},\n        error: () => this.toastService.showError(this.c.error),\n      });\n    },\n    accept: () => {\n      if (!this.incomingCall) return;\n      this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n        next: (call) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n          this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n          this.callQuality = 'connecting';\n          this.toastService.showSuccess('Appel connecté');\n        },\n        error: () => {\n          this.toastService.showError(this.c.error);\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n      });\n    },\n    reject: () => {\n      if (!this.incomingCall) return;\n      this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n        next: (call) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n        error: (error) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n      });\n    },\n    end: () => {\n      const sub = this.MessageService.activeCall$.subscribe((call) => {\n        if (call) {\n          this.MessageService.endCall(call.id).subscribe({\n            next: (call) => {},\n            error: (error) => {},\n          });\n        }\n      });\n      sub.unsubscribe();\n    },\n  };\n\n  initiateCall = this.callMethods.initiate;\n  acceptCall = this.callMethods.accept;\n  rejectCall = this.callMethods.reject;\n  endCall = this.callMethods.end;\n\n  // Méthodes de contrôle d'appel consolidées\n  readonly callControlMethods = {\n    toggleMute: () => {\n      this.isCallMuted = !this.isCallMuted;\n      this.callControlMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé'\n      );\n    },\n    toggleVideo: () => {\n      this.isVideoEnabled = !this.isVideoEnabled;\n      this.callControlMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n      );\n    },\n    updateMedia: () => {\n      this.MessageService.toggleMedia(\n        this.activeCall?.id,\n        !this.isCallMuted,\n        this.isVideoEnabled\n      ).subscribe({\n        next: () => {},\n        error: (error) =>\n          console.error('Erreur lors de la mise à jour des médias:', error),\n      });\n    },\n  };\n\n  toggleCallMute = this.callControlMethods.toggleMute;\n  toggleCallVideo = this.callControlMethods.toggleVideo;\n  private updateCallMedia = this.callControlMethods.updateMedia;\n\n  // Méthodes de timer consolidées\n  readonly timerMethods = {\n    startCallTimer: () => {\n      this.callDuration = 0;\n      this.callTimer = setInterval(() => {\n        this.callDuration++;\n        if (this.callDuration === 3 && this.callQuality === 'connecting') {\n          this.callQuality = 'excellent';\n        }\n      }, 1000);\n    },\n    stopCallTimer: () => {\n      if (this.callTimer) {\n        clearInterval(this.callTimer);\n        this.callTimer = null;\n      }\n    },\n    resetCallState: () => (this.callDuration = 0),\n  };\n\n  private startCallTimerMethod = this.timerMethods.startCallTimer;\n  private stopCallTimerMethod = this.timerMethods.stopCallTimer;\n  private resetCallStateMethod = this.timerMethods.resetCallState;\n\n  // Notifications\n\n  // Méthode de basculement de notification consolidée\n  readonly notificationToggleMethod = {\n    togglePanel: () => {\n      this.togglePanel('notification');\n      if (this.showNotificationPanel) {\n        this.loadNotifications();\n      }\n    },\n  };\n\n  toggleNotificationPanel = this.notificationToggleMethod.togglePanel;\n\n  /**\n   * Charge les notifications\n   */\n  loadNotifications(refresh: boolean = false): void {\n    const loadSub = this.MessageService.getNotifications(\n      refresh,\n      1,\n      20\n    ).subscribe({\n      next: (notifications) => {\n        if (refresh) {\n          this.notifications = notifications;\n        } else {\n          this.notifications = [...this.notifications, ...notifications];\n        }\n\n        this.updateNotificationCount();\n      },\n      error: (error) => {\n        this.toastService.showError(\n          'Erreur lors du chargement des notifications'\n        );\n      },\n    });\n\n    this.subscriptions.add(loadSub);\n  }\n\n  // Méthodes de notification consolidées\n  readonly notificationMethods = {\n    loadMore: () => this.loadNotifications(),\n    updateCount: () =>\n      (this.unreadNotificationCount = this.notifications.filter(\n        (n) => !n.isRead\n      ).length),\n    getFiltered: () => this.notifications,\n    toggleSelection: (notificationId: string) => {\n      if (this.selectedNotifications.has(notificationId)) {\n        this.selectedNotifications.delete(notificationId);\n      } else {\n        this.selectedNotifications.add(notificationId);\n      }\n    },\n    toggleSelectAll: () => {\n      const filteredNotifications = this.notificationMethods.getFiltered();\n      const allSelected = filteredNotifications.every((n) =>\n        this.selectedNotifications.has(n.id)\n      );\n      if (allSelected) {\n        filteredNotifications.forEach((n) =>\n          this.selectedNotifications.delete(n.id)\n        );\n      } else {\n        filteredNotifications.forEach((n) =>\n          this.selectedNotifications.add(n.id)\n        );\n      }\n    },\n    areAllSelected: () => {\n      const filteredNotifications = this.notificationMethods.getFiltered();\n      return (\n        filteredNotifications.length > 0 &&\n        filteredNotifications.every((n) => this.selectedNotifications.has(n.id))\n      );\n    },\n  };\n\n  loadMoreNotifications = this.notificationMethods.loadMore;\n  private updateNotificationCount = this.notificationMethods.updateCount;\n  getFilteredNotifications = this.notificationMethods.getFiltered;\n  toggleNotificationSelection = this.notificationMethods.toggleSelection;\n  toggleSelectAllNotifications = this.notificationMethods.toggleSelectAll;\n  areAllNotificationsSelected = this.notificationMethods.areAllSelected;\n\n  // Méthodes de marquage consolidées\n  readonly markingMethods = {\n    markSelected: () => {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      this.markingMethods.markAsRead(selectedIds, () => {\n        this.selectedNotifications.clear();\n        this.toastService.showSuccess(\n          `${selectedIds.length} notification(s) marquée(s) comme lue(s)`\n        );\n      });\n    },\n    markAll: () => {\n      const unreadNotifications = this.notifications.filter((n) => !n.isRead);\n      if (unreadNotifications.length === 0) {\n        this.toastService.showInfo('Aucune notification non lue');\n        return;\n      }\n      const unreadIds = unreadNotifications.map((n) => n.id);\n      this.markingMethods.markAsRead(unreadIds, () => {\n        this.toastService.showSuccess(\n          'Toutes les notifications ont été marquées comme lues'\n        );\n      });\n    },\n    markAsRead: (ids: string[], onSuccess: () => void) => {\n      const markSub = this.MessageService.markAsRead(ids).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.map((n) =>\n            ids.includes(n.id) ? { ...n, isRead: true, readAt: new Date() } : n\n          );\n          this.updateNotificationCount();\n          onSuccess();\n        },\n        error: (error) => {\n          this.toastService.showError(\n            'Erreur lors du marquage des notifications'\n          );\n        },\n      });\n      this.subscriptions.add(markSub);\n    },\n  };\n\n  markSelectedAsRead = this.markingMethods.markSelected;\n  markAllAsRead = this.markingMethods.markAll;\n\n  // Méthodes de suppression de notifications consolidées\n  readonly notificationDeleteMethods = {\n    showDeleteSelectedConfirmation: () => {\n      if (this.selectedNotifications.size === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      this.showDeleteConfirmModal = true;\n    },\n    deleteSelected: () => {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) return;\n      this.isDeletingNotifications = true;\n      this.showDeleteConfirmModal = false;\n      const deleteSub = this.MessageService.deleteMultipleNotifications(\n        selectedIds\n      ).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.filter(\n            (n) => !selectedIds.includes(n.id)\n          );\n          this.selectedNotifications.clear();\n          this.updateNotificationCount();\n          this.isDeletingNotifications = false;\n          this.toastService.showSuccess(\n            `${result.count} notification(s) supprimée(s)`\n          );\n        },\n        error: (error) => {\n          this.isDeletingNotifications = false;\n          this.toastService.showError(\n            'Erreur lors de la suppression des notifications'\n          );\n        },\n      });\n      this.subscriptions.add(deleteSub);\n    },\n    deleteOne: (notificationId: string) => {\n      const deleteSub = this.MessageService.deleteNotification(\n        notificationId\n      ).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.filter(\n            (n) => n.id !== notificationId\n          );\n          this.selectedNotifications.delete(notificationId);\n          this.updateNotificationCount();\n          this.toastService.showSuccess('Notification supprimée');\n        },\n        error: (error) => {\n          this.toastService.showError(\n            'Erreur lors de la suppression de la notification'\n          );\n        },\n      });\n      this.subscriptions.add(deleteSub);\n    },\n    deleteAll: () => {\n      if (this.notifications.length === 0) return;\n      if (\n        !confirm(\n          'Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.'\n        )\n      ) {\n        return;\n      }\n      this.isDeletingNotifications = true;\n      const deleteAllSub =\n        this.MessageService.deleteAllNotifications().subscribe({\n          next: (result) => {\n            this.notifications = [];\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(\n              `${result.count} notifications supprimées avec succès`\n            );\n          },\n          error: (error) => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError(\n              'Erreur lors de la suppression de toutes les notifications'\n            );\n          },\n        });\n      this.subscriptions.add(deleteAllSub);\n    },\n    cancel: () => (this.showDeleteConfirmModal = false),\n  };\n\n  showDeleteSelectedConfirmation =\n    this.notificationDeleteMethods.showDeleteSelectedConfirmation;\n  deleteSelectedNotifications = this.notificationDeleteMethods.deleteSelected;\n  deleteNotification = this.notificationDeleteMethods.deleteOne;\n  deleteAllNotifications = this.notificationDeleteMethods.deleteAll;\n  cancelDeleteNotifications = this.notificationDeleteMethods.cancel;\n\n  // Méthodes utilitaires de notification consolidées\n  readonly notificationUtilMethods = {\n    formatDate: (date: string | Date | undefined) =>\n      this.MessageService.formatLastActive(date),\n    getIcon: (type: string) =>\n      this.c.notifications[type as keyof typeof this.c.notifications]?.icon ||\n      'fas fa-bell',\n    getColor: (type: string) =>\n      this.c.notifications[type as keyof typeof this.c.notifications]?.color ||\n      'text-cyan-500',\n    trackById: (index: number, notification: any) =>\n      this.c.trackById(0, notification),\n  };\n\n  formatNotificationDate = this.notificationUtilMethods.formatDate;\n  getNotificationIcon = this.notificationUtilMethods.getIcon;\n  getNotificationColor = this.notificationUtilMethods.getColor;\n  trackByNotificationId = this.notificationUtilMethods.trackById;\n\n  /**\n   * S'abonne au statut utilisateur en temps réel\n   */\n  private subscribeToUserStatus(): void {\n    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n      next: (user: User) => {\n        this.handleUserStatusUpdate(user);\n      },\n      error: (error) => {\n        // Error handled silently\n      },\n    });\n\n    this.subscriptions.add(statusSub);\n  }\n\n  /**\n   * Gère la mise à jour du statut d'un utilisateur\n   */\n  private handleUserStatusUpdate(user: User): void {\n    if (!user.id) return;\n\n    if (user.isOnline) {\n      this.onlineUsers.set(user.id, user);\n    } else {\n      this.onlineUsers.delete(user.id);\n    }\n\n    if (this.otherParticipant && this.otherParticipant.id === user.id) {\n      this.otherParticipant = { ...this.otherParticipant, ...user };\n    }\n  }\n\n  /**\n   * Initialise le statut de l'utilisateur actuel\n   */\n  private initializeUserStatus(): void {\n    if (!this.currentUserId) return;\n\n    const setOnlineSub = this.MessageService.setUserOnline(\n      this.currentUserId\n    ).subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = 'online';\n        this.lastActivityTime = new Date();\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          \"Erreur lors de l'initialisation du statut\",\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(setOnlineSub);\n  }\n\n  /**\n   * Démarre le suivi d'activité automatique\n   */\n  private startActivityTracking(): void {\n    const events = [\n      'mousedown',\n      'mousemove',\n      'keypress',\n      'scroll',\n      'touchstart',\n      'click',\n    ];\n\n    events.forEach((event) => {\n      document.addEventListener(event, this.onUserActivity.bind(this), true);\n    });\n  }\n\n  /**\n   * Gère l'activité de l'utilisateur\n   */\n  private onUserActivity(): void {\n    this.lastActivityTime = new Date();\n\n    // Réinitialiser le timer\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n\n    // Remettre en ligne si absent\n    if (\n      this.currentUserStatus === 'away' ||\n      this.currentUserStatus === 'offline'\n    ) {\n      this.updateUserStatus('online');\n    }\n\n    // Programmer la mise en absence\n    this.autoAwayTimeout = setTimeout(() => {\n      if (this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      }\n    }, this.c.delays.away);\n  }\n\n  /**\n   * Met à jour le statut de l'utilisateur\n   */\n  updateUserStatus(status: string): void {\n    if (!this.currentUserId) return;\n\n    const previousStatus = this.currentUserStatus;\n\n    let updateObservable;\n    if (status === 'online') {\n      updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n    } else {\n      updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n    }\n\n    const updateSub = updateObservable.subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = status;\n\n        if (status !== previousStatus) {\n          const statusText = this.getStatusText(status);\n          this.toastService.showInfo(`Statut : ${statusText}`);\n        }\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          'Erreur lors de la mise à jour du statut',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(updateSub);\n  }\n\n  /**\n   * Charge la liste des utilisateurs en ligne\n   */\n  loadOnlineUsers(): void {\n    const usersSub = this.MessageService.getAllUsers(\n      false,\n      undefined,\n      1,\n      50,\n      'username',\n      'asc',\n      true\n    ).subscribe({\n      next: (users: User[]) => {\n        users.forEach((user) => {\n          if (user.isOnline && user.id) {\n            this.onlineUsers.set(user.id, user);\n          }\n        });\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          'Erreur lors du chargement des utilisateurs en ligne',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(usersSub);\n  }\n\n  // Méthodes de gestion des panneaux consolidées\n  readonly panelMethods = {\n    getActivePanels: () => {\n      const panels = [];\n      if (this.showUserStatusPanel)\n        panels.push({\n          key: 'userStatus',\n          title: 'Utilisateurs',\n          icon: 'fas fa-users',\n          closeAction: () => (this.showUserStatusPanel = false),\n        });\n      if (this.showCallHistoryPanel)\n        panels.push({\n          key: 'callHistory',\n          title: 'Historique des appels',\n          icon: 'fas fa-history',\n          closeAction: () => (this.showCallHistoryPanel = false),\n        });\n      if (this.showCallStatsPanel)\n        panels.push({\n          key: 'callStats',\n          title: \"Statistiques d'appels\",\n          icon: 'fas fa-chart-bar',\n          closeAction: () => (this.showCallStatsPanel = false),\n        });\n      if (this.showVoiceMessagesPanel)\n        panels.push({\n          key: 'voiceMessages',\n          title: 'Messages vocaux',\n          icon: 'fas fa-microphone',\n          closeAction: () => (this.showVoiceMessagesPanel = false),\n        });\n      return panels;\n    },\n    getStatusOptions: () =>\n      Object.entries(this.c.status).map(([key, config]) => ({\n        key,\n        ...config,\n      })),\n    getThemeOptions: () => this.c.themes,\n  };\n\n  getActivePanels = this.panelMethods.getActivePanels;\n  getStatusOptions = this.panelMethods.getStatusOptions;\n  getThemeOptions = this.panelMethods.getThemeOptions;\n\n  // Méthodes de statut simplifiées\n  readonly statusMethods = {\n    getText: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.text || 'Inconnu',\n    getColor: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.color ||\n      'text-gray-400',\n    getIcon: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.icon ||\n      'fas fa-question-circle',\n  };\n\n  getStatusText = this.statusMethods.getText;\n  getStatusColor = this.statusMethods.getColor;\n  getStatusIcon = this.statusMethods.getIcon;\n\n  // Méthodes utilitaires consolidées\n  readonly utilityMethods = {\n    formatLastSeen: (lastActive: Date | null) =>\n      lastActive\n        ? this.MessageService.formatLastActive(lastActive)\n        : 'Jamais vu',\n    getOnlineUsersCount: () =>\n      Array.from(this.onlineUsers.values()).filter((user) => user.isOnline)\n        .length,\n    getFilteredUsers: () => Array.from(this.onlineUsers.values()),\n    setStatusFilter: (filter: string) => (this.statusFilterType = filter),\n  };\n\n  formatLastSeen = this.utilityMethods.formatLastSeen;\n  getOnlineUsersCount = this.utilityMethods.getOnlineUsersCount;\n  setStatusFilter = this.utilityMethods.setStatusFilter;\n  getFilteredUsers = this.utilityMethods.getFilteredUsers;\n\n  // Méthodes de réponse et transfert consolidées\n  readonly replyForwardMethods = {\n    startReply: (message: any) => (this.replyingToMessage = message),\n    cancelReply: () => (this.replyingToMessage = null),\n    openForwardModal: (message: any) => {\n      this.forwardingMessage = message;\n      this.showForwardModal = true;\n    },\n    closeForwardModal: () => {\n      this.showForwardModal = false;\n      this.forwardingMessage = null;\n      this.selectedConversations = [];\n    },\n  };\n\n  startReplyToMessage = this.replyForwardMethods.startReply;\n  cancelReply = this.replyForwardMethods.cancelReply;\n  openForwardModal = this.replyForwardMethods.openForwardModal;\n  closeForwardModal = this.replyForwardMethods.closeForwardModal;\n\n  // Messages - méthodes consolidées\n  readonly messageMethods = {\n    getPinIcon: (message: any) =>\n      this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack',\n    getPinDisplayText: (message: any) =>\n      this.isMessagePinned(message) ? 'Désépingler' : 'Épingler',\n    canEditMessage: (message: any) => message.sender?.id === this.currentUserId,\n    isMessagePinned: (message: any) => message.isPinned || false,\n  };\n\n  getPinIcon = this.messageMethods.getPinIcon;\n  getPinDisplayText = this.messageMethods.getPinDisplayText;\n  canEditMessage = this.messageMethods.canEditMessage;\n  isMessagePinned = this.messageMethods.isMessagePinned;\n\n  // Méthodes d'édition consolidées\n  readonly editMethods = {\n    startEditMessage: (message: any) => {\n      this.editingMessageId = message.id;\n      this.editingContent = message.content;\n    },\n    cancelEditMessage: () => {\n      this.editingMessageId = null;\n      this.editingContent = '';\n    },\n    saveEditMessage: (messageId: string) =>\n      this.editMethods.cancelEditMessage(),\n    onEditKeyPress: (event: KeyboardEvent, messageId: string) => {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.editMethods.saveEditMessage(messageId);\n      } else if (event.key === 'Escape') {\n        this.editMethods.cancelEditMessage();\n      }\n    },\n  };\n\n  startEditMessage = this.editMethods.startEditMessage;\n  cancelEditMessage = this.editMethods.cancelEditMessage;\n  saveEditMessage = this.editMethods.saveEditMessage;\n  onEditKeyPress = this.editMethods.onEditKeyPress;\n\n  togglePinMessage(message: any): void {\n    this.isPinning[message.id] = true;\n    setTimeout(() => {\n      this.isPinning[message.id] = false;\n      this.showPinConfirm[message.id] = false;\n    }, 1000);\n  }\n\n  // Utilitaires d'appel consolidées\n  readonly callUtilities = {\n    getCallStatusColor: (status: string) =>\n      this.c.callStatusColors[status as keyof typeof this.c.callStatusColors] ||\n      'text-gray-500',\n    getCallTypeIcon: (type: string) =>\n      type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',\n    formatCallDuration: (duration: number) => {\n      if (!duration) return '00:00';\n      const minutes = Math.floor(duration / 60);\n      const seconds = duration % 60;\n      return `${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    },\n    formatCallDate: (timestamp: string | Date | undefined) =>\n      this.MessageService.formatMessageDate(timestamp),\n  };\n\n  getCallStatusColor = this.callUtilities.getCallStatusColor;\n  getCallTypeIcon = this.callUtilities.getCallTypeIcon;\n  formatCallDuration = this.callUtilities.formatCallDuration;\n  formatCallDate = this.callUtilities.formatCallDate;\n\n  // Méthodes d'événements consolidées\n  readonly eventMethods = {\n    onDocumentClick: (event: Event) => {\n      const target = event.target as HTMLElement;\n      const closeConfigs = [\n        {\n          selectors: ['.theme-selector-menu', '.btn-theme'],\n          property: 'showThemeSelector',\n        },\n        {\n          selectors: ['.emoji-picker', '.btn-emoji'],\n          property: 'showEmojiPicker',\n        },\n      ];\n\n      closeConfigs.forEach((config) => {\n        const isClickOutside = !config.selectors.some((selector) =>\n          target.closest(selector)\n        );\n        if (isClickOutside) {\n          (this as any)[config.property] = false;\n        }\n      });\n    },\n    confirmDeleteMessage: (messageId: string) => {\n      this.showDeleteConfirm[messageId] = false;\n    },\n  };\n\n  onDocumentClick = this.eventMethods.onDocumentClick;\n  confirmDeleteMessage = this.eventMethods.confirmDeleteMessage;\n\n  // Méthodes de réaction consolidées\n  readonly reactionMethods = {\n    getUniqueReactions: (message: any) => message.reactions || [],\n    onReactionClick: (messageId: string, emoji: string) => {\n      // Implémentation des réactions\n    },\n    hasUserReacted: (message: any, emoji: string) => false,\n  };\n\n  getUniqueReactions = this.reactionMethods.getUniqueReactions;\n  onReactionClick = this.reactionMethods.onReactionClick;\n  hasUserReacted = this.reactionMethods.hasUserReacted;\n\n  // Méthodes de conversation consolidées\n  readonly conversationSelectionMethods = {\n    areAllSelected: () =>\n      this.selectedConversations.length === this.availableConversations.length,\n    selectAll: () =>\n      (this.selectedConversations = this.availableConversations.map(\n        (c) => c.id\n      )),\n    deselectAll: () => (this.selectedConversations = []),\n    toggle: (conversationId: string) => {\n      const index = this.selectedConversations.indexOf(conversationId);\n      index > -1\n        ? this.selectedConversations.splice(index, 1)\n        : this.selectedConversations.push(conversationId);\n    },\n    isSelected: (conversationId: string) =>\n      this.selectedConversations.includes(conversationId),\n    getDisplayImage: (conversation: any) =>\n      conversation.image || 'assets/images/default-avatar.png',\n    getDisplayName: (conversation: any) => conversation.name || 'Conversation',\n    forwardMessage: () => {\n      this.isForwarding = true;\n      setTimeout(() => {\n        this.isForwarding = false;\n        this.closeForwardModal();\n      }, 1000);\n    },\n  };\n\n  areAllConversationsSelected =\n    this.conversationSelectionMethods.areAllSelected;\n  selectAllConversations = this.conversationSelectionMethods.selectAll;\n  deselectAllConversations = this.conversationSelectionMethods.deselectAll;\n  toggleConversationSelection = this.conversationSelectionMethods.toggle;\n  isConversationSelected = this.conversationSelectionMethods.isSelected;\n  getConversationDisplayImage =\n    this.conversationSelectionMethods.getDisplayImage;\n  getConversationDisplayName = this.conversationSelectionMethods.getDisplayName;\n  forwardMessage = this.conversationSelectionMethods.forwardMessage;\n\n  // Méthodes de notification simplifiées consolidées\n  readonly simpleNotificationMethods = {\n    onCallMouseMove: () => (this.showCallControls = true),\n    saveNotificationSettings: () => {},\n    setNotificationFilter: (filter: string) =>\n      (this.notificationFilter = filter),\n  };\n\n  onCallMouseMove = this.simpleNotificationMethods.onCallMouseMove;\n  saveNotificationSettings =\n    this.simpleNotificationMethods.saveNotificationSettings;\n  setNotificationFilter = this.simpleNotificationMethods.setNotificationFilter;\n\n  // Méthodes de recherche consolidées\n  readonly searchMethods = {\n    onInput: (event: any) => {\n      this.searchQuery = event.target.value;\n      this.searchQuery.length >= 2\n        ? this.searchMethods.perform()\n        : this.searchMethods.clear();\n    },\n    onKeyPress: (event: KeyboardEvent) => {\n      if (event.key === 'Enter') {\n        this.searchMethods.perform();\n      } else if (event.key === 'Escape') {\n        this.searchMethods.clear();\n      }\n    },\n    perform: () => {\n      this.isSearching = true;\n      this.searchMode = true;\n      setTimeout(() => {\n        this.searchResults = this.messages.filter((m) =>\n          m.content?.toLowerCase().includes(this.searchQuery.toLowerCase())\n        );\n        this.isSearching = false;\n      }, 500);\n    },\n    clear: () => {\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.isSearching = false;\n      this.searchMode = false;\n    },\n  };\n\n  onSearchInput = this.searchMethods.onInput;\n  onSearchKeyPress = this.searchMethods.onKeyPress;\n  performSearch = this.searchMethods.perform;\n  clearSearch = this.searchMethods.clear;\n\n  // Méthodes utilitaires finales consolidées\n  readonly finalUtilityMethods = {\n    navigateToMessage: (messageId: string) => {\n      // Navigation vers un message spécifique\n    },\n    scrollToPinnedMessage: (messageId: string) => {\n      // Défilement vers un message épinglé\n    },\n    getPinnedMessagesCount: () => this.pinnedMessages.length,\n  };\n\n  navigateToMessage = this.finalUtilityMethods.navigateToMessage;\n  scrollToPinnedMessage = this.finalUtilityMethods.scrollToPinnedMessage;\n  getPinnedMessagesCount = this.finalUtilityMethods.getPinnedMessagesCount;\n\n  // Méthodes de recherche et réaction consolidées\n  readonly searchAndReactionMethods = {\n    highlightSearchTerms: (content: string, query: string) => {\n      if (!query) return content;\n      const regex = new RegExp(`(${query})`, 'gi');\n      return content.replace(regex, '<mark>$1</mark>');\n    },\n    toggleReactionPicker: (messageId: string) =>\n      (this.showReactionPicker[messageId] =\n        !this.showReactionPicker[messageId]),\n    reactToMessage: (messageId: string, emoji: string) =>\n      (this.showReactionPicker[messageId] = false),\n    toggleMessageOptions: (messageId: string) =>\n      (this.showMessageOptions[messageId] =\n        !this.showMessageOptions[messageId]),\n  };\n\n  highlightSearchTerms = this.searchAndReactionMethods.highlightSearchTerms;\n  toggleReactionPicker = this.searchAndReactionMethods.toggleReactionPicker;\n  reactToMessage = this.searchAndReactionMethods.reactToMessage;\n  toggleMessageOptions = this.searchAndReactionMethods.toggleMessageOptions;\n\n  // Confirmations consolidées\n  readonly confirmationMethods = {\n    showPinConfirmation: (messageId: string) =>\n      (this.showPinConfirm[messageId] = true),\n    cancelPinConfirmation: (messageId: string) =>\n      (this.showPinConfirm[messageId] = false),\n    showDeleteConfirmation: (messageId: string) =>\n      (this.showDeleteConfirm[messageId] = true),\n    cancelDeleteMessage: (messageId: string) =>\n      (this.showDeleteConfirm[messageId] = false),\n  };\n\n  showPinConfirmation = this.confirmationMethods.showPinConfirmation;\n  cancelPinConfirmation = this.confirmationMethods.cancelPinConfirmation;\n  showDeleteConfirmation = this.confirmationMethods.showDeleteConfirmation;\n  cancelDeleteMessage = this.confirmationMethods.cancelDeleteMessage;\n\n  // Méthodes de nettoyage optimisées\n  readonly cleanup = {\n    clearTimeouts: () =>\n      [this.typingTimeout, this.autoAwayTimeout, this.callTimer].forEach(\n        (t) => t && clearTimeout(t)\n      ),\n    setUserOffline: () =>\n      this.currentUserId &&\n      this.MessageService.setUserOffline(this.currentUserId).subscribe(),\n    stopTypingIndicator: () => {\n      if (this.isCurrentlyTyping && this.conversation?.id) {\n        this.isCurrentlyTyping = false;\n        clearTimeout(this.typingTimer);\n        this.MessageService.stopTyping(this.conversation.id).subscribe({\n          next: () => {},\n          error: (error) => {},\n        });\n      }\n    },\n  };\n\n  ngOnDestroy(): void {\n    this.cleanup.clearTimeouts();\n    this.cleanup.setUserOffline();\n    this.cleanup.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n}\n", "<div class=\"chat-container\">\n  <!-- En-tête -->\n  <div class=\"chat-header\">\n    <button (click)=\"goBackToConversations()\" class=\"action-btn\">\n      <i class=\"fas fa-arrow-left\"></i>\n    </button>\n\n    <img\n      [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n      alt=\"User avatar\"\n      class=\"user-avatar\"\n    />\n\n    <div class=\"user-info\" *ngIf=\"otherParticipant\">\n      <h3>{{ otherParticipant.username }}</h3>\n      <div class=\"user-status\">\n        {{\n          otherParticipant.isOnline\n            ? \"En ligne\"\n            : formatLastActive(otherParticipant.lastActive)\n        }}\n      </div>\n    </div>\n\n    <div class=\"whatsapp-actions\">\n      <!-- Boutons d'action consolidés -->\n      <ng-container *ngFor=\"let action of getHeaderActions()\">\n        <button\n          [class]=\"'whatsapp-action-button ' + action.class\"\n          [ngClass]=\"\n            action.activeClass && action.isActive ? action.activeClass : {}\n          \"\n          [title]=\"action.title\"\n          (click)=\"action.onClick()\"\n        >\n          <i [class]=\"action.icon\"></i>\n          <!-- Badge universel -->\n          <span\n            *ngIf=\"action.badge && action.badge.count > 0\"\n            [class]=\"\n              'absolute -top-2 -right-2 min-w-[20px] h-5 px-1.5 text-white text-xs rounded-md flex items-center justify-center font-bold shadow-lg border border-white/20 ' +\n              action.badge.class\n            \"\n            [ngClass]=\"{ 'animate-pulse': action.badge.animate }\"\n          >\n            {{ action.badge.count > 99 ? \"99+\" : action.badge.count }}\n          </span>\n        </button>\n      </ng-container>\n\n      <!-- Bouton du statut utilisateur -->\n      <div class=\"relative\">\n        <button\n          (click)=\"toggleStatusSelector()\"\n          class=\"whatsapp-action-button relative\"\n          [ngClass]=\"{\n            'text-[#4f5fad] dark:text-[#6d78c9]': showStatusSelector\n          }\"\n          title=\"Statut utilisateur\"\n        >\n          <i\n            [class]=\"getStatusIcon(currentUserStatus)\"\n            [ngClass]=\"getStatusColor(currentUserStatus)\"\n          ></i>\n          <!-- Indicateur de mise à jour -->\n          <span\n            *ngIf=\"isUpdatingStatus\"\n            class=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"\n          ></span>\n        </button>\n\n        <!-- Menu déroulant du statut -->\n        <div\n          *ngIf=\"showStatusSelector\"\n          class=\"absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-3 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] bg-gradient-to-r from-[#4f5fad]/10 to-[#6d78c9]/10\"\n          >\n            <div class=\"flex items-center justify-between\">\n              <span>Statut actuel</span>\n              <span\n                class=\"font-medium\"\n                [ngClass]=\"getStatusColor(currentUserStatus)\"\n              >\n                {{ getStatusText(currentUserStatus) }}\n              </span>\n            </div>\n          </div>\n\n          <div class=\"p-1\">\n            <!-- Boutons de statut consolidés avec *ngFor -->\n            <button\n              *ngFor=\"let status of getStatusOptions()\"\n              (click)=\"updateUserStatus(status.key); toggleStatusSelector()\"\n              [disabled]=\"isUpdatingStatus\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n              [ngClass]=\"{\n                'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20':\n                  currentUserStatus === status.key\n              }\"\n            >\n              <div class=\"flex items-center\">\n                <i\n                  [class]=\"status.icon + ' ' + status.color + ' mr-3 text-xs'\"\n                ></i>\n                <div>\n                  <div class=\"font-medium\">{{ status.label }}</div>\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n                    {{ status.description }}\n                  </div>\n                </div>\n              </div>\n            </button>\n          </div>\n\n          <div class=\"border-t border-[#edf1f4]/50 dark:border-[#2a2a2a] p-1\">\n            <button\n              (click)=\"toggleUserStatusPanel(); toggleStatusSelector()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i\n                  class=\"fas fa-users text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xs\"\n                ></i>\n                <div>\n                  <div class=\"font-medium\">Voir tous les utilisateurs</div>\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n                    {{ getOnlineUsersCount() }} en ligne\n                  </div>\n                </div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Sélecteur de thème -->\n      <div class=\"relative\">\n        <button\n          (click)=\"toggleThemeSelector()\"\n          class=\"whatsapp-action-button btn-theme\"\n        >\n          <i class=\"fas fa-palette\"></i>\n        </button>\n\n        <!-- Menu déroulant des thèmes -->\n        <div\n          *ngIf=\"showThemeSelector\"\n          class=\"theme-selector-menu absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            Choisir un thème\n          </div>\n          <div class=\"p-1\">\n            <!-- Boutons de thème consolidés avec *ngFor -->\n            <a\n              *ngFor=\"let theme of getThemeOptions()\"\n              href=\"javascript:void(0)\"\n              (click)=\"changeTheme(theme.key)\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md transition-colors\"\n              [ngClass]=\"\n                'hover:bg-' +\n                theme.hoverColor +\n                '/10 dark:hover:bg-' +\n                theme.hoverColor +\n                '/10'\n              \"\n            >\n              <div class=\"flex items-center\">\n                <div\n                  [ngClass]=\"'w-4 h-4 rounded-full bg-' + theme.color + ' mr-2'\"\n                ></div>\n                <div>{{ theme.label }}</div>\n              </div>\n            </a>\n          </div>\n        </div>\n      </div>\n\n      <!-- Bouton menu principal -->\n      <div class=\"relative\">\n        <button\n          (click)=\"toggleMainMenu()\"\n          class=\"whatsapp-action-button btn-menu\"\n          title=\"Menu principal\"\n        >\n          <i class=\"fas fa-ellipsis-v\"></i>\n        </button>\n\n        <!-- Menu déroulant principal -->\n        <div\n          *ngIf=\"showMainMenu\"\n          class=\"absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            Options de conversation\n          </div>\n          <div class=\"p-1\">\n            <button\n              (click)=\"clearConversation()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-red-500/10 dark:hover:bg-red-500/10 transition-colors text-red-600 dark:text-red-400\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-trash mr-3 text-xs\"></i>\n                <div>Vider la conversation</div>\n              </div>\n            </button>\n            <button\n              (click)=\"exportConversation()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-download mr-3 text-xs\"></i>\n                <div>Exporter la conversation</div>\n              </div>\n            </button>\n            <button\n              (click)=\"toggleConversationInfo()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-info-circle mr-3 text-xs\"></i>\n                <div>Informations</div>\n              </div>\n            </button>\n            <button\n              (click)=\"toggleConversationSettings()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-cog mr-3 text-xs\"></i>\n                <div>Paramètres</div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Zone des messages -->\n  <div class=\"messages-area\" #messagesContainer>\n    <div\n      *ngIf=\"loading\"\n      style=\"text-align: center; padding: 2rem; color: #00f7ff\"\n    >\n      <i class=\"fas fa-spinner fa-spin\"></i> Chargement...\n    </div>\n\n    <div\n      *ngIf=\"!loading && messages.length === 0\"\n      style=\"text-align: center; padding: 2rem; color: #888\"\n    >\n      <i\n        class=\"fas fa-comments\"\n        style=\"font-size: 3rem; margin-bottom: 1rem; opacity: 0.5\"\n      ></i>\n      <p>Aucun message pour le moment</p>\n      <p style=\"font-size: 0.9rem\">Commencez la conversation !</p>\n    </div>\n\n    <div\n      *ngFor=\"let message of messages\"\n      class=\"message\"\n      [ngClass]=\"{\n        'current-user': message.sender?.id === currentUserId,\n        'other-user': message.sender?.id !== currentUserId\n      }\"\n    >\n      <div class=\"message-bubble\">\n        <div>{{ message.content }}</div>\n        <div style=\"font-size: 0.7rem; opacity: 0.7; margin-top: 0.5rem\">\n          {{ formatMessageTime(message.timestamp) }}\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Message de réponse en cours -->\n  <div *ngIf=\"replyingToMessage\" class=\"whatsapp-reply-preview\">\n    <div class=\"whatsapp-reply-preview-content\">\n      <div class=\"whatsapp-reply-preview-header\">\n        <div class=\"whatsapp-reply-preview-info\">\n          <i class=\"fas fa-reply\"></i>\n          <span>Répondre à {{ replyingToMessage.sender?.username }}</span>\n        </div>\n        <button (click)=\"cancelReply()\" class=\"whatsapp-reply-cancel\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n      <div class=\"whatsapp-reply-preview-message\">\n        <span *ngIf=\"replyingToMessage.content\">{{\n          replyingToMessage.content\n        }}</span>\n        <span *ngIf=\"hasImage(replyingToMessage)\" class=\"whatsapp-reply-media\">\n          <i class=\"fas fa-image\"></i> Photo\n        </span>\n        <span\n          *ngIf=\"isVoiceMessage(replyingToMessage)\"\n          class=\"whatsapp-reply-media\"\n        >\n          <i class=\"fas fa-microphone\"></i> Message vocal\n        </span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Prévisualisation de fichier -->\n  <div *ngIf=\"selectedFile\" class=\"whatsapp-file-preview\">\n    <div class=\"whatsapp-file-preview-content\">\n      <div class=\"whatsapp-file-preview-header\">\n        <span>Fichier sélectionné</span>\n        <button (click)=\"removeSelectedFile()\" class=\"whatsapp-file-remove\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n      <div class=\"whatsapp-file-preview-body\">\n        <div *ngIf=\"previewUrl\" class=\"whatsapp-file-preview-image\">\n          <img [src]=\"previewUrl\" [alt]=\"selectedFile.name\" />\n        </div>\n        <div class=\"whatsapp-file-preview-info\">\n          <div class=\"whatsapp-file-preview-name\">{{ selectedFile.name }}</div>\n          <div class=\"whatsapp-file-preview-size\">\n            {{ formatFileSize(selectedFile.size) }}\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Enregistrement vocal -->\n  <div *ngIf=\"isRecordingVoice\" class=\"whatsapp-voice-recording\">\n    <div class=\"whatsapp-voice-recording-content\">\n      <div class=\"whatsapp-voice-recording-info\">\n        <div class=\"whatsapp-voice-recording-icon\">\n          <i class=\"fas fa-microphone\"></i>\n        </div>\n        <div class=\"whatsapp-voice-recording-details\">\n          <span class=\"whatsapp-voice-recording-text\"\n            >Enregistrement en cours...</span\n          >\n          <span class=\"whatsapp-voice-recording-duration\">{{\n            formatRecordingDuration(voiceRecordingDuration)\n          }}</span>\n        </div>\n      </div>\n      <div class=\"whatsapp-voice-recording-actions\">\n        <button (click)=\"cancelVoiceRecording()\" class=\"whatsapp-voice-cancel\">\n          <i class=\"fas fa-trash\"></i>\n        </button>\n        <button (click)=\"stopVoiceRecording()\" class=\"whatsapp-voice-send\">\n          <i class=\"fas fa-paper-plane\"></i>\n        </button>\n      </div>\n    </div>\n    <div class=\"whatsapp-voice-recording-waveform\">\n      <div\n        *ngFor=\"let bar of recordingWaveform\"\n        class=\"whatsapp-voice-recording-bar\"\n        [style.height.px]=\"bar\"\n      ></div>\n    </div>\n  </div>\n\n  <!-- Zone de saisie principale -->\n  <div class=\"whatsapp-input-container\">\n    <div class=\"whatsapp-input-wrapper\">\n      <!-- Boutons d'action gauche -->\n      <div class=\"whatsapp-input-actions-left\">\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          class=\"whatsapp-input-action-btn\"\n          [ngClass]=\"{ active: showEmojiPicker }\"\n          title=\"Émojis\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n\n        <div class=\"whatsapp-attachment-menu\">\n          <button\n            type=\"button\"\n            (click)=\"toggleAttachmentMenu()\"\n            class=\"whatsapp-input-action-btn\"\n            [ngClass]=\"{ active: showAttachmentMenu }\"\n            title=\"Joindre un fichier\"\n          >\n            <i class=\"fas fa-paperclip\"></i>\n          </button>\n\n          <!-- Menu des pièces jointes -->\n          <div *ngIf=\"showAttachmentMenu\" class=\"whatsapp-attachment-dropdown\">\n            <button\n              (click)=\"triggerFileInput('image')\"\n              class=\"whatsapp-attachment-option\"\n            >\n              <i class=\"fas fa-image\"></i>\n              <span>Photo</span>\n            </button>\n            <button\n              (click)=\"triggerFileInput('video')\"\n              class=\"whatsapp-attachment-option\"\n            >\n              <i class=\"fas fa-video\"></i>\n              <span>Vidéo</span>\n            </button>\n            <button\n              (click)=\"triggerFileInput('audio')\"\n              class=\"whatsapp-attachment-option\"\n            >\n              <i class=\"fas fa-music\"></i>\n              <span>Audio</span>\n            </button>\n            <button\n              (click)=\"triggerFileInput('document')\"\n              class=\"whatsapp-attachment-option\"\n            >\n              <i class=\"fas fa-file\"></i>\n              <span>Document</span>\n            </button>\n            <button (click)=\"openCamera()\" class=\"whatsapp-attachment-option\">\n              <i class=\"fas fa-camera\"></i>\n              <span>Caméra</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Zone de saisie de texte -->\n      <div class=\"whatsapp-input-text-container\">\n        <form\n          [formGroup]=\"messageForm\"\n          (ngSubmit)=\"sendMessage()\"\n          class=\"whatsapp-input-form\"\n        >\n          <div class=\"whatsapp-input-field-wrapper\">\n            <textarea\n              formControlName=\"content\"\n              #messageTextarea\n              placeholder=\"Tapez un message\"\n              class=\"whatsapp-input-field\"\n              [disabled]=\"!otherParticipant || isRecordingVoice\"\n              (input)=\"onInputChange($event)\"\n              (keydown)=\"onInputKeyDown($event)\"\n              (focus)=\"onInputFocus()\"\n              (blur)=\"onInputBlur()\"\n              rows=\"1\"\n              maxlength=\"4096\"\n            ></textarea>\n\n            <!-- Compteur de caractères -->\n            <div\n              *ngIf=\"messageForm.get('content')?.value?.length > 3000\"\n              class=\"whatsapp-char-counter\"\n            >\n              {{ 4096 - (messageForm.get(\"content\")?.value?.length || 0) }}\n            </div>\n          </div>\n        </form>\n      </div>\n\n      <!-- Boutons d'action droite -->\n      <div class=\"whatsapp-input-actions-right\">\n        <!-- Bouton d'envoi ou microphone -->\n        <div class=\"whatsapp-send-container\">\n          <button\n            *ngIf=\"hasMessageContent() || selectedFile\"\n            type=\"submit\"\n            (click)=\"sendMessage()\"\n            class=\"whatsapp-send-btn\"\n            [disabled]=\"!canSendMessage()\"\n            [ngClass]=\"{ sending: isSendingMessage }\"\n            title=\"Envoyer\"\n          >\n            <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n            <i class=\"fas fa-spinner fa-spin\" *ngIf=\"isSendingMessage\"></i>\n          </button>\n\n          <button\n            *ngIf=\"!hasMessageContent() && !selectedFile\"\n            type=\"button\"\n            (mousedown)=\"startVoiceRecording()\"\n            (mouseup)=\"stopVoiceRecording()\"\n            (mouseleave)=\"cancelVoiceRecording()\"\n            (touchstart)=\"startVoiceRecording()\"\n            (touchend)=\"stopVoiceRecording()\"\n            class=\"whatsapp-voice-btn\"\n            [ngClass]=\"{ recording: isRecordingVoice }\"\n            title=\"Maintenir pour enregistrer\"\n          >\n            <i class=\"fas fa-microphone\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Sélecteur d'émojis -->\n    <div *ngIf=\"showEmojiPicker\" class=\"whatsapp-emoji-picker\">\n      <div class=\"whatsapp-emoji-picker-header\">\n        <div class=\"whatsapp-emoji-categories\">\n          <button\n            *ngFor=\"let category of emojiCategories\"\n            (click)=\"selectEmojiCategory(category.name)\"\n            class=\"whatsapp-emoji-category-btn\"\n            [ngClass]=\"{ active: selectedEmojiCategory === category.name }\"\n            [title]=\"category.label\"\n          >\n            {{ category.icon }}\n          </button>\n        </div>\n        <button (click)=\"toggleEmojiPicker()\" class=\"whatsapp-emoji-close\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <div class=\"whatsapp-emoji-search\">\n        <input\n          type=\"text\"\n          [(ngModel)]=\"emojiSearchQuery\"\n          (input)=\"searchEmojis()\"\n          placeholder=\"Rechercher un emoji...\"\n          class=\"whatsapp-emoji-search-input\"\n        />\n      </div>\n\n      <div class=\"whatsapp-emoji-grid\">\n        <button\n          *ngFor=\"let emoji of getFilteredEmojis()\"\n          (click)=\"insertEmoji(emoji.char)\"\n          class=\"whatsapp-emoji-btn\"\n          [title]=\"emoji.name\"\n        >\n          {{ emoji.char }}\n        </button>\n      </div>\n\n      <!-- Émojis récents -->\n      <div *ngIf=\"recentEmojis.length > 0\" class=\"whatsapp-recent-emojis\">\n        <div class=\"whatsapp-recent-emojis-header\">Récemment utilisés</div>\n        <div class=\"whatsapp-recent-emojis-grid\">\n          <button\n            *ngFor=\"let emoji of recentEmojis\"\n            (click)=\"insertEmoji(emoji)\"\n            class=\"whatsapp-emoji-btn\"\n          >\n            {{ emoji }}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Inputs de fichier cachés -->\n  <input\n    #fileInputImage\n    type=\"file\"\n    accept=\"image/*\"\n    (change)=\"onFileSelected($event, 'image')\"\n    style=\"display: none\"\n    multiple\n  />\n  <input\n    #fileInputVideo\n    type=\"file\"\n    accept=\"video/*\"\n    (change)=\"onFileSelected($event, 'video')\"\n    style=\"display: none\"\n  />\n  <input\n    #fileInputAudio\n    type=\"file\"\n    accept=\"audio/*\"\n    (change)=\"onFileSelected($event, 'audio')\"\n    style=\"display: none\"\n  />\n  <input\n    #fileInputDocument\n    type=\"file\"\n    accept=\".pdf,.doc,.docx,.txt,.xlsx,.pptx\"\n    (change)=\"onFileSelected($event, 'document')\"\n    style=\"display: none\"\n  />\n\n  <!-- Panneau de notifications -->\n  <div class=\"side-panel\" [ngClass]=\"{ open: showNotificationPanel }\">\n    <div class=\"panel-header\">\n      <h3>Notifications</h3>\n      <button class=\"close-btn\" (click)=\"toggleNotificationPanel()\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n    <div\n      *ngIf=\"notifications.length === 0\"\n      style=\"padding: 1rem; text-align: center; color: #888\"\n    >\n      <i\n        class=\"fas fa-bell-slash\"\n        style=\"font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.5\"\n      ></i>\n      <p>Aucune notification</p>\n    </div>\n    <div\n      *ngFor=\"let notification of notifications\"\n      style=\"padding: 0.5rem; border-bottom: 1px solid rgba(0, 247, 255, 0.1)\"\n    >\n      <p style=\"margin: 0; font-size: 0.9rem\">{{ notification.content }}</p>\n      <small style=\"opacity: 0.7\">{{\n        formatMessageTime(notification.timestamp)\n      }}</small>\n    </div>\n  </div>\n\n  <!-- Barre de recherche -->\n  <div\n    *ngIf=\"showSearchBar\"\n    class=\"search-bar bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] px-4 py-3 relative z-10\"\n  >\n    <div class=\"flex items-center space-x-3\">\n      <!-- Icône de recherche -->\n      <div class=\"text-[#6d6870] dark:text-[#a0a0a0]\">\n        <i class=\"fas fa-search text-sm\"></i>\n      </div>\n\n      <!-- Champ de recherche -->\n      <div class=\"flex-1 relative\">\n        <input\n          type=\"text\"\n          [(ngModel)]=\"searchQuery\"\n          (input)=\"onSearchInput($event)\"\n          (keydown)=\"onSearchKeyPress($event)\"\n          placeholder=\"Rechercher dans cette conversation...\"\n          class=\"w-full px-3 py-2 text-sm bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 border border-[#edf1f4] dark:border-[#3a3a3a] rounded-lg focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 transition-colors\"\n          autofocus\n        />\n\n        <!-- Indicateur de chargement -->\n        <div\n          *ngIf=\"isSearching\"\n          class=\"absolute right-3 top-1/2 transform -translate-y-1/2\"\n        >\n          <div\n            class=\"w-4 h-4 border-2 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\n          ></div>\n        </div>\n\n        <!-- Bouton de suppression -->\n        <button\n          *ngIf=\"searchQuery && !isSearching\"\n          (click)=\"clearSearch()\"\n          class=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\n        >\n          <i class=\"fas fa-times text-xs\"></i>\n        </button>\n      </div>\n\n      <!-- Bouton de fermeture -->\n      <button\n        (click)=\"toggleSearchBar()\"\n        class=\"text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <!-- Résultats de recherche -->\n    <div\n      *ngIf=\"searchMode && searchResults.length > 0\"\n      class=\"mt-3 max-h-40 overflow-y-auto border border-[#edf1f4]/50 dark:border-[#3a3a3a] rounded-lg bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30\"\n    >\n      <div\n        class=\"p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#3a3a3a]\"\n      >\n        {{ searchResults.length }} résultat(s) trouvé(s)\n      </div>\n      <div class=\"max-h-32 overflow-y-auto\">\n        <button\n          *ngFor=\"let result of searchResults\"\n          (click)=\"result.id && navigateToMessage(result.id)\"\n          class=\"w-full text-left px-3 py-2 hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0\"\n        >\n          <div class=\"flex items-start space-x-2\">\n            <div\n              class=\"w-6 h-6 rounded-full bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 flex items-center justify-center flex-shrink-0 mt-0.5\"\n            >\n              <i\n                class=\"fas fa-comment text-xs text-[#4f5fad] dark:text-[#6d78c9]\"\n              ></i>\n            </div>\n            <div class=\"flex-1 min-w-0\">\n              <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mb-1\">\n                {{ formatMessageTime(result.timestamp) }}\n              </div>\n              <div\n                class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] truncate\"\n                [innerHTML]=\"\n                  highlightSearchTerms(result.content || '', searchQuery)\n                \"\n              ></div>\n            </div>\n          </div>\n        </button>\n      </div>\n    </div>\n\n    <!-- Message aucun résultat -->\n    <div\n      *ngIf=\"\n        searchMode &&\n        searchResults.length === 0 &&\n        !isSearching &&\n        searchQuery.length >= 2\n      \"\n      class=\"mt-3 p-3 text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30 rounded-lg\"\n    >\n      <i\n        class=\"fas fa-search text-lg mb-2 block text-[#6d6870]/50 dark:text-[#a0a0a0]/50\"\n      ></i>\n      Aucun message trouvé pour \"{{ searchQuery }}\"\n    </div>\n  </div>\n\n  <!-- Panneau des messages épinglés -->\n  <div\n    *ngIf=\"showPinnedMessages\"\n    class=\"pinned-messages-panel bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] relative z-10\"\n  >\n    <!-- En-tête du panneau -->\n    <div\n      class=\"flex items-center justify-between p-4 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n    >\n      <div class=\"flex items-center space-x-2\">\n        <i class=\"fas fa-thumbtack text-[#4f5fad] dark:text-[#6d78c9]\"></i>\n        <h3 class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">\n          Messages épinglés ({{ getPinnedMessagesCount() }})\n        </h3>\n      </div>\n      <button\n        (click)=\"togglePinnedMessages()\"\n        class=\"w-6 h-6 flex items-center justify-center text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-full transition-colors\"\n      >\n        <i class=\"fas fa-times text-xs\"></i>\n      </button>\n    </div>\n\n    <!-- Liste des messages épinglés -->\n    <div class=\"max-h-48 overflow-y-auto\">\n      <!-- État de chargement -->\n      <div *ngIf=\"pinnedMessages.length === 0\" class=\"p-4 text-center\">\n        <div class=\"text-[#6d6870]/70 dark:text-[#a0a0a0]/70\">\n          <i class=\"fas fa-thumbtack text-2xl mb-2 block opacity-50\"></i>\n          <div class=\"text-sm\">Aucun message épinglé</div>\n        </div>\n      </div>\n\n      <!-- Messages épinglés -->\n      <div\n        *ngIf=\"pinnedMessages.length > 0\"\n        class=\"divide-y divide-[#edf1f4]/30 dark:divide-[#3a3a3a]/30\"\n      >\n        <button\n          *ngFor=\"let pinnedMessage of pinnedMessages\"\n          (click)=\"scrollToPinnedMessage(pinnedMessage.id!)\"\n          class=\"w-full text-left p-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors\"\n        >\n          <div class=\"flex items-start space-x-3\">\n            <!-- Avatar de l'expéditeur -->\n            <div class=\"flex-shrink-0\">\n              <img\n                [src]=\"\n                  pinnedMessage.sender?.image ||\n                  'assets/images/default-avatar.png'\n                \"\n                [alt]=\"pinnedMessage.sender?.username || 'User'\"\n                class=\"w-8 h-8 rounded-full object-cover\"\n                onerror=\"this.src='assets/images/default-avatar.png'\"\n              />\n            </div>\n\n            <!-- Contenu du message -->\n            <div class=\"flex-1 min-w-0\">\n              <!-- En-tête avec nom et date -->\n              <div class=\"flex items-center justify-between mb-1\">\n                <span\n                  class=\"text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9]\"\n                >\n                  {{ pinnedMessage.sender?.username || \"Utilisateur inconnu\" }}\n                </span>\n                <span class=\"text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70\">\n                  {{ formatMessageTime(pinnedMessage.timestamp) }}\n                </span>\n              </div>\n\n              <!-- Contenu du message -->\n              <div\n                class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] line-clamp-2\"\n              >\n                <span *ngIf=\"pinnedMessage.content\">{{\n                  pinnedMessage.content\n                }}</span>\n                <span\n                  *ngIf=\"hasImage(pinnedMessage)\"\n                  class=\"italic flex items-center\"\n                >\n                  <i class=\"fas fa-image mr-1\"></i>\n                  Image\n                </span>\n                <span\n                  *ngIf=\"isVoiceMessage(pinnedMessage)\"\n                  class=\"italic flex items-center\"\n                >\n                  <i class=\"fas fa-microphone mr-1\"></i>\n                  Message vocal\n                </span>\n              </div>\n\n              <!-- Indicateur d'épinglage -->\n              <div class=\"flex items-center mt-1\">\n                <i\n                  class=\"fas fa-thumbtack text-xs text-[#4f5fad] dark:text-[#6d78c9] mr-1\"\n                ></i>\n                <span class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\n                  Épinglé\n                  {{\n                    pinnedMessage.pinnedAt\n                      ? \"le \" + formatMessageDate(pinnedMessage.pinnedAt)\n                      : \"\"\n                  }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Icône de navigation -->\n            <div class=\"flex-shrink-0 text-[#6d6870]/50 dark:text-[#a0a0a0]/50\">\n              <i class=\"fas fa-chevron-right text-xs\"></i>\n            </div>\n          </div>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modales et panneaux latéraux -->\n\n  <!-- Modal d'appel entrant -->\n  <div *ngIf=\"incomingCall\" class=\"whatsapp-call-modal incoming-call\">\n    <div class=\"whatsapp-call-modal-overlay\"></div>\n    <div class=\"whatsapp-call-modal-content\">\n      <div class=\"whatsapp-call-info\">\n        <div class=\"whatsapp-call-avatar\">\n          <img\n            [src]=\"\n              incomingCall.caller?.image || 'assets/images/default-avatar.png'\n            \"\n            [alt]=\"incomingCall.caller?.username\"\n          />\n        </div>\n        <div class=\"whatsapp-call-details\">\n          <h3>{{ incomingCall.caller?.username }}</h3>\n          <p>\n            {{\n              incomingCall.type === \"AUDIO\"\n                ? \"Appel audio entrant\"\n                : \"Appel vidéo entrant\"\n            }}\n          </p>\n        </div>\n      </div>\n      <div class=\"whatsapp-call-actions\">\n        <button (click)=\"declineCall()\" class=\"whatsapp-call-decline\">\n          <i class=\"fas fa-phone-slash\"></i>\n        </button>\n        <button (click)=\"acceptCall()\" class=\"whatsapp-call-accept\">\n          <i class=\"fas fa-phone\"></i>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modal d'appel actif -->\n  <div\n    *ngIf=\"activeCall\"\n    class=\"whatsapp-call-modal active-call\"\n    [ngClass]=\"{ minimized: isCallMinimized }\"\n  >\n    <div class=\"whatsapp-call-modal-content\">\n      <div class=\"whatsapp-call-header\">\n        <div class=\"whatsapp-call-participant\">\n          <img\n            [src]=\"\n              activeCall.participant?.image ||\n              'assets/images/default-avatar.png'\n            \"\n            [alt]=\"activeCall.participant?.username\"\n          />\n          <div class=\"whatsapp-call-participant-info\">\n            <h4>{{ activeCall.participant?.username }}</h4>\n            <span class=\"whatsapp-call-duration\">{{\n              formatCallDuration(callDuration)\n            }}</span>\n            <span class=\"whatsapp-call-status\">{{ getCallStatusText() }}</span>\n          </div>\n        </div>\n        <button (click)=\"toggleCallMinimize()\" class=\"whatsapp-call-minimize\">\n          <i class=\"fas fa-minus\" *ngIf=\"!isCallMinimized\"></i>\n          <i class=\"fas fa-expand\" *ngIf=\"isCallMinimized\"></i>\n        </button>\n      </div>\n\n      <div *ngIf=\"activeCall.type === 'VIDEO'\" class=\"whatsapp-video-container\">\n        <video #remoteVideo class=\"whatsapp-remote-video\" autoplay></video>\n        <video #localVideo class=\"whatsapp-local-video\" autoplay muted></video>\n      </div>\n\n      <div\n        class=\"whatsapp-call-controls\"\n        [ngClass]=\"{ show: showCallControls || !isCallMinimized }\"\n      >\n        <button\n          (click)=\"toggleMute()\"\n          class=\"whatsapp-call-control\"\n          [ngClass]=\"{ active: isCallMuted }\"\n        >\n          <i class=\"fas fa-microphone\" *ngIf=\"!isCallMuted\"></i>\n          <i class=\"fas fa-microphone-slash\" *ngIf=\"isCallMuted\"></i>\n        </button>\n        <button\n          *ngIf=\"activeCall.type === 'VIDEO'\"\n          (click)=\"toggleVideo()\"\n          class=\"whatsapp-call-control\"\n          [ngClass]=\"{ active: !isVideoEnabled }\"\n        >\n          <i class=\"fas fa-video\" *ngIf=\"isVideoEnabled\"></i>\n          <i class=\"fas fa-video-slash\" *ngIf=\"!isVideoEnabled\"></i>\n        </button>\n        <button (click)=\"endCall()\" class=\"whatsapp-call-end\">\n          <i class=\"fas fa-phone-slash\"></i>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modal de confirmation de suppression -->\n  <div *ngIf=\"showDeleteConfirmModal\" class=\"whatsapp-modal-overlay\">\n    <div class=\"whatsapp-modal-content\">\n      <div class=\"whatsapp-modal-header\">\n        <h3>Confirmer la suppression</h3>\n      </div>\n      <div class=\"whatsapp-modal-body\">\n        <p>\n          Êtes-vous sûr de vouloir supprimer ce message ? Cette action est\n          irréversible.\n        </p>\n      </div>\n      <div class=\"whatsapp-modal-actions\">\n        <button (click)=\"cancelDelete()\" class=\"whatsapp-modal-cancel\">\n          Annuler\n        </button>\n        <button (click)=\"confirmDelete()\" class=\"whatsapp-modal-confirm\">\n          Supprimer\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modal de transfert de message -->\n  <div *ngIf=\"showForwardModal\" class=\"whatsapp-modal-overlay\">\n    <div class=\"whatsapp-modal-content whatsapp-forward-modal\">\n      <div class=\"whatsapp-modal-header\">\n        <h3>Transférer le message</h3>\n        <button (click)=\"closeForwardModal()\" class=\"whatsapp-modal-close\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n      <div class=\"whatsapp-modal-body\">\n        <div class=\"whatsapp-forward-search\">\n          <input\n            type=\"text\"\n            [(ngModel)]=\"forwardSearchQuery\"\n            placeholder=\"Rechercher des contacts...\"\n            class=\"whatsapp-forward-search-input\"\n          />\n        </div>\n        <div class=\"whatsapp-forward-contacts\">\n          <div\n            *ngFor=\"let contact of getFilteredContacts()\"\n            class=\"whatsapp-forward-contact\"\n            (click)=\"toggleContactSelection(contact.id)\"\n          >\n            <input\n              type=\"checkbox\"\n              [checked]=\"selectedContacts.includes(contact.id)\"\n            />\n            <img\n              [src]=\"contact.image || 'assets/images/default-avatar.png'\"\n              [alt]=\"contact.username\"\n            />\n            <span>{{ contact.username }}</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"whatsapp-modal-actions\">\n        <button (click)=\"closeForwardModal()\" class=\"whatsapp-modal-cancel\">\n          Annuler\n        </button>\n        <button\n          (click)=\"confirmForward()\"\n          class=\"whatsapp-modal-confirm\"\n          [disabled]=\"selectedContacts.length === 0\"\n        >\n          Transférer ({{ selectedContacts.length }})\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Visualiseur d'images -->\n  <div *ngIf=\"showImageViewer\" class=\"whatsapp-image-viewer\">\n    <div\n      class=\"whatsapp-image-viewer-overlay\"\n      (click)=\"closeImageViewer()\"\n    ></div>\n    <div class=\"whatsapp-image-viewer-content\">\n      <div class=\"whatsapp-image-viewer-header\">\n        <div class=\"whatsapp-image-viewer-info\">\n          <span>{{ currentImageIndex + 1 }} / {{ imageGallery.length }}</span>\n        </div>\n        <button\n          (click)=\"closeImageViewer()\"\n          class=\"whatsapp-image-viewer-close\"\n        >\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n      <div class=\"whatsapp-image-viewer-body\">\n        <button\n          *ngIf=\"imageGallery.length > 1\"\n          (click)=\"previousImage()\"\n          class=\"whatsapp-image-nav whatsapp-image-prev\"\n        >\n          <i class=\"fas fa-chevron-left\"></i>\n        </button>\n        <img\n          [src]=\"imageGallery[currentImageIndex]?.url\"\n          [alt]=\"imageGallery[currentImageIndex]?.caption\"\n          class=\"whatsapp-image-viewer-img\"\n        />\n        <button\n          *ngIf=\"imageGallery.length > 1\"\n          (click)=\"nextImage()\"\n          class=\"whatsapp-image-nav whatsapp-image-next\"\n        >\n          <i class=\"fas fa-chevron-right\"></i>\n        </button>\n      </div>\n      <div\n        *ngIf=\"imageGallery[currentImageIndex]?.caption\"\n        class=\"whatsapp-image-viewer-caption\"\n      >\n        {{ imageGallery[currentImageIndex]?.caption }}\n      </div>\n    </div>\n  </div>\n\n  <!-- Toast de notification -->\n  <div *ngIf=\"showToast\" class=\"whatsapp-toast\" [ngClass]=\"toastType\">\n    <div class=\"whatsapp-toast-content\">\n      <i [class]=\"getToastIcon()\"></i>\n      <span>{{ toastMessage }}</span>\n    </div>\n  </div>\n\n  <!-- Panneau d'informations de conversation -->\n  <div\n    *ngIf=\"showConversationInfo\"\n    class=\"whatsapp-side-panel whatsapp-conversation-info\"\n  >\n    <div class=\"whatsapp-side-panel-header\">\n      <h3>Informations de conversation</h3>\n      <button\n        (click)=\"toggleConversationInfo()\"\n        class=\"whatsapp-side-panel-close\"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n    <div class=\"whatsapp-side-panel-body\">\n      <div class=\"whatsapp-conversation-participant\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n        />\n        <div class=\"whatsapp-participant-details\">\n          <h4>{{ otherParticipant?.username }}</h4>\n          <p>{{ otherParticipant?.email }}</p>\n          <span class=\"whatsapp-participant-status\">{{\n            otherParticipant?.isOnline ? \"En ligne\" : \"Hors ligne\"\n          }}</span>\n        </div>\n      </div>\n      <div class=\"whatsapp-conversation-stats\">\n        <div class=\"whatsapp-stat\">\n          <span class=\"whatsapp-stat-label\">Messages</span>\n          <span class=\"whatsapp-stat-value\">{{ getTotalMessagesCount() }}</span>\n        </div>\n        <div class=\"whatsapp-stat\">\n          <span class=\"whatsapp-stat-label\">Photos</span>\n          <span class=\"whatsapp-stat-value\">{{ getPhotosCount() }}</span>\n        </div>\n        <div class=\"whatsapp-stat\">\n          <span class=\"whatsapp-stat-label\">Fichiers</span>\n          <span class=\"whatsapp-stat-value\">{{ getFilesCount() }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Panneau de paramètres de conversation -->\n  <div\n    *ngIf=\"showConversationSettings\"\n    class=\"whatsapp-side-panel whatsapp-conversation-settings\"\n  >\n    <div class=\"whatsapp-side-panel-header\">\n      <h3>Paramètres de conversation</h3>\n      <button\n        (click)=\"toggleConversationSettings()\"\n        class=\"whatsapp-side-panel-close\"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n    <div class=\"whatsapp-side-panel-body\">\n      <div class=\"whatsapp-setting-group\">\n        <h4>Notifications</h4>\n        <div class=\"whatsapp-setting-item\">\n          <label>\n            <input\n              type=\"checkbox\"\n              [(ngModel)]=\"conversationSettings.notifications\"\n              (change)=\"updateConversationSettings()\"\n            />\n            <span>Recevoir des notifications</span>\n          </label>\n        </div>\n        <div class=\"whatsapp-setting-item\">\n          <label>\n            <input\n              type=\"checkbox\"\n              [(ngModel)]=\"conversationSettings.soundNotifications\"\n              (change)=\"updateConversationSettings()\"\n            />\n            <span>Sons de notification</span>\n          </label>\n        </div>\n      </div>\n      <div class=\"whatsapp-setting-group\">\n        <h4>Confidentialité</h4>\n        <div class=\"whatsapp-setting-item\">\n          <label>\n            <input\n              type=\"checkbox\"\n              [(ngModel)]=\"conversationSettings.readReceipts\"\n              (change)=\"updateConversationSettings()\"\n            />\n            <span>Accusés de lecture</span>\n          </label>\n        </div>\n        <div class=\"whatsapp-setting-item\">\n          <label>\n            <input\n              type=\"checkbox\"\n              [(ngModel)]=\"conversationSettings.typingIndicators\"\n              (change)=\"updateConversationSettings()\"\n            />\n            <span>Indicateurs de frappe</span>\n          </label>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAWA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAGEC,WAAW,EACXC,QAAQ,QACH,8BAA8B;AAErC,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;ICTpEC,EAAA,CAAAC,cAAA,cAAgD;IAC1CD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAPFH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAAC,QAAA,CAA+B;IAEjCR,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAS,kBAAA,MAAAH,MAAA,CAAAC,gBAAA,CAAAG,QAAA,gBAAAJ,MAAA,CAAAK,gBAAA,CAAAL,MAAA,CAAAC,gBAAA,CAAAK,UAAA,OAKF;;;;;;;;;;IAgBIZ,EAAA,CAAAC,cAAA,eAOC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAPLH,EAAA,CAAAa,UAAA,iKAAAC,UAAA,CAAAC,KAAA,CAAAC,KAAA,CAGC;IACDhB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAL,UAAA,CAAAC,KAAA,CAAAK,OAAA,EAAqD;IAErDpB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAK,UAAA,CAAAC,KAAA,CAAAM,KAAA,gBAAAP,UAAA,CAAAC,KAAA,CAAAM,KAAA,MACF;;;;;;;;;IApBJrB,EAAA,CAAAsB,uBAAA,GAAwD;IACtDtB,EAAA,CAAAC,cAAA,iBAOC;IADCD,EAAA,CAAAuB,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAC,IAAA;MAAA,MAAAb,UAAA,GAAAW,WAAA,CAAAG,SAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAf,UAAA,CAAAgB,OAAA,EAAgB;IAAA,EAAC;IAE1B9B,EAAA,CAAA+B,SAAA,QAA6B;IAE7B/B,EAAA,CAAAgC,UAAA,IAAAC,mDAAA,mBASO;IACTjC,EAAA,CAAAG,YAAA,EAAS;IACXH,EAAA,CAAAkC,qBAAA,EAAe;;;;IApBXlC,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAa,UAAA,6BAAAC,UAAA,CAAAE,KAAA,CAAkD;IAClDhB,EAAA,CAAAiB,UAAA,YAAAH,UAAA,CAAAqB,WAAA,IAAArB,UAAA,CAAAsB,QAAA,GAAAtB,UAAA,CAAAqB,WAAA,GAAAnC,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAEC,UAAAxB,UAAA,CAAAyB,KAAA;IAIEvC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAa,UAAA,CAAAC,UAAA,CAAA0B,IAAA,CAAqB;IAGrBxC,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAiB,UAAA,SAAAH,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,CAAAM,KAAA,KAA4C;;;;;IA2B/CrB,EAAA,CAAA+B,SAAA,eAGQ;;;;;;;;;;;IAwBN/B,EAAA,CAAAC,cAAA,iBASC;IAPCD,EAAA,CAAAuB,UAAA,mBAAAkB,sEAAA;MAAA,MAAAhB,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAgB,IAAA;MAAA,MAAAC,UAAA,GAAAlB,WAAA,CAAAG,SAAA;MAAA,MAAAgB,OAAA,GAAA5C,EAAA,CAAA6C,aAAA;MAASD,OAAA,CAAAE,gBAAA,CAAAH,UAAA,CAAAI,GAAA,CAA4B;MAAA,OAAE/C,EAAA,CAAA6B,WAAA,CAAAe,OAAA,CAAAI,oBAAA,EAAsB;IAAA,EAAC;IAQ9DhD,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA+B,SAAA,QAEK;IACL/B,EAAA,CAAAC,cAAA,UAAK;IACsBD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAfVH,EAAA,CAAAiB,UAAA,aAAAgC,OAAA,CAAAC,gBAAA,CAA6B,YAAAlD,EAAA,CAAAkB,eAAA,IAAAiC,GAAA,EAAAF,OAAA,CAAAG,iBAAA,KAAAT,UAAA,CAAAI,GAAA;IASzB/C,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAa,UAAA,CAAA8B,UAAA,CAAAH,IAAA,SAAAG,UAAA,CAAAU,KAAA,mBAA4D;IAGnCrD,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAsC,UAAA,CAAAW,KAAA,CAAkB;IAEzCtD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAkC,UAAA,CAAAY,WAAA,MACF;;;;;;IAtCVvD,EAAA,CAAAC,cAAA,cAGC;IAKWD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1BH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIXH,EAAA,CAAAC,cAAA,cAAiB;IAEfD,EAAA,CAAAgC,UAAA,IAAAwB,6CAAA,qBAqBS;IACXxD,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAoE;IAEhED,EAAA,CAAAuB,UAAA,mBAAAkC,8DAAA;MAAAzD,EAAA,CAAA0B,aAAA,CAAAgC,IAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAA6C,aAAA;MAASc,OAAA,CAAAC,qBAAA,EAAuB;MAAA,OAAE5D,EAAA,CAAA6B,WAAA,CAAA8B,OAAA,CAAAX,oBAAA,EAAsB;IAAA,EAAC;IAGzDhD,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA+B,SAAA,aAEK;IACL/B,EAAA,CAAAC,cAAA,WAAK;IACsBD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA9CRH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAiB,UAAA,YAAA4C,MAAA,CAAAC,cAAA,CAAAD,MAAA,CAAAT,iBAAA,EAA6C;IAE7CpD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAoD,MAAA,CAAAE,aAAA,CAAAF,MAAA,CAAAT,iBAAA,OACF;IAOmBpD,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAiB,UAAA,YAAA4C,MAAA,CAAAG,gBAAA,GAAqB;IAmClChE,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAoD,MAAA,CAAAI,mBAAA,iBACF;;;;;;IA6BNjE,EAAA,CAAAC,cAAA,YAYC;IATCD,EAAA,CAAAuB,UAAA,mBAAA2C,4DAAA;MAAA,MAAAzC,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAyC,IAAA;MAAA,MAAAC,SAAA,GAAA3C,WAAA,CAAAG,SAAA;MAAA,MAAAyC,OAAA,GAAArE,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAwC,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAArB,GAAA,CAAsB;IAAA,EAAC;IAUhC/C,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA+B,SAAA,cAEO;IACP/B,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAZ9BH,EAAA,CAAAiB,UAAA,0BAAAmD,SAAA,CAAAG,UAAA,0BAAAH,SAAA,CAAAG,UAAA,SAMC;IAIGvE,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAiB,UAAA,yCAAAmD,SAAA,CAAAf,KAAA,WAA8D;IAE3DrD,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA+D,SAAA,CAAAd,KAAA,CAAiB;;;;;IA5B9BtD,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAiB;IAEfD,EAAA,CAAAgC,UAAA,IAAAwC,wCAAA,gBAmBI;IACNxE,EAAA,CAAAG,YAAA,EAAM;;;;IAnBgBH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAiB,UAAA,YAAAwD,MAAA,CAAAC,eAAA,GAAoB;;;;;;IAkC5C1E,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAiB;IAEbD,EAAA,CAAAuB,UAAA,mBAAAoD,6DAAA;MAAA3E,EAAA,CAAA0B,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAA7E,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAgD,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAG7B9E,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA+B,SAAA,YAAyC;IACzC/B,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGpCH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAwD,6DAAA;MAAA/E,EAAA,CAAA0B,aAAA,CAAAkD,IAAA;MAAA,MAAAI,OAAA,GAAAhF,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAmD,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BjF,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA+B,SAAA,aAA4C;IAC5C/B,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGvCH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA2D,8DAAA;MAAAlF,EAAA,CAAA0B,aAAA,CAAAkD,IAAA;MAAA,MAAAO,OAAA,GAAAnF,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAsD,OAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAGlCpF,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA+B,SAAA,aAA+C;IAC/C/B,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG3BH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA8D,8DAAA;MAAArF,EAAA,CAAA0B,aAAA,CAAAkD,IAAA;MAAA,MAAAU,OAAA,GAAAtF,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAyD,OAAA,CAAAC,0BAAA,EAA4B;IAAA,EAAC;IAGtCvF,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAA+B,SAAA,aAAuC;IACvC/B,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,uBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWjCH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA+B,SAAA,YAAsC;IAAC/B,EAAA,CAAAE,MAAA,sBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA+B,SAAA,YAGK;IACL/B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACnCH,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;IAG9DH,EAAA,CAAAC,cAAA,eAOC;IAEQD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChCH,EAAA,CAAAC,cAAA,eAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IATRH,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAwF,eAAA,IAAAC,GAAA,GAAAC,WAAA,CAAAC,MAAA,kBAAAD,WAAA,CAAAC,MAAA,CAAAC,EAAA,MAAAC,MAAA,CAAAC,aAAA,GAAAJ,WAAA,CAAAC,MAAA,kBAAAD,WAAA,CAAAC,MAAA,CAAAC,EAAA,MAAAC,MAAA,CAAAC,aAAA,EAGE;IAGK9F,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAqF,WAAA,CAAAK,OAAA,CAAqB;IAExB/F,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAoF,MAAA,CAAAG,iBAAA,CAAAN,WAAA,CAAAO,SAAA,OACF;;;;;IAkBAjG,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,GAEtC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAF+BH,EAAA,CAAAI,SAAA,GAEtC;IAFsCJ,EAAA,CAAAK,iBAAA,CAAA6F,OAAA,CAAAC,iBAAA,CAAAJ,OAAA,CAEtC;;;;;IACF/F,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAA+B,SAAA,aAA4B;IAAC/B,EAAA,CAAAE,MAAA,cAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAA+B,SAAA,aAAiC;IAAC/B,EAAA,CAAAE,MAAA,sBACpC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAvBbH,EAAA,CAAAC,cAAA,eAA8D;IAItDD,EAAA,CAAA+B,SAAA,aAA4B;IAC5B/B,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElEH,EAAA,CAAAC,cAAA,kBAA8D;IAAtDD,EAAA,CAAAuB,UAAA,mBAAA6E,6DAAA;MAAApG,EAAA,CAAA0B,aAAA,CAAA2E,IAAA;MAAA,MAAAC,OAAA,GAAAtG,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAyE,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC7BvG,EAAA,CAAA+B,SAAA,YAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAA4C;IAC1CD,EAAA,CAAAgC,UAAA,KAAAwE,4CAAA,oBAES;IACTxG,EAAA,CAAAgC,UAAA,KAAAyE,4CAAA,oBAEO;IACPzG,EAAA,CAAAgC,UAAA,KAAA0E,4CAAA,oBAKO;IACT1G,EAAA,CAAAG,YAAA,EAAM;;;;IAnBIH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAS,kBAAA,0BAAAkG,OAAA,CAAAR,iBAAA,CAAAR,MAAA,kBAAAgB,OAAA,CAAAR,iBAAA,CAAAR,MAAA,CAAAnF,QAAA,KAAmD;IAOpDR,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAiB,UAAA,SAAA0F,OAAA,CAAAR,iBAAA,CAAAJ,OAAA,CAA+B;IAG/B/F,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAiB,UAAA,SAAA0F,OAAA,CAAAC,QAAA,CAAAD,OAAA,CAAAR,iBAAA,EAAiC;IAIrCnG,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAiB,UAAA,SAAA0F,OAAA,CAAAE,cAAA,CAAAF,OAAA,CAAAR,iBAAA,EAAuC;;;;;IAmB1CnG,EAAA,CAAAC,cAAA,eAA4D;IAC1DD,EAAA,CAAA+B,SAAA,eAAoD;IACtD/B,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAiB,UAAA,QAAA6F,OAAA,CAAAC,UAAA,EAAA/G,EAAA,CAAAgH,aAAA,CAAkB,QAAAF,OAAA,CAAAG,YAAA,CAAAC,IAAA;;;;;;IAV/BlH,EAAA,CAAAC,cAAA,eAAwD;IAG5CD,EAAA,CAAAE,MAAA,oCAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,kBAAoE;IAA5DD,EAAA,CAAAuB,UAAA,mBAAA4F,6DAAA;MAAAnH,EAAA,CAAA0B,aAAA,CAAA0F,IAAA;MAAA,MAAAC,OAAA,GAAArH,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAwF,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IACpCtH,EAAA,CAAA+B,SAAA,YAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAgC,UAAA,IAAAuF,0CAAA,mBAEM;IACNvH,EAAA,CAAAC,cAAA,eAAwC;IACED,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrEH,EAAA,CAAAC,cAAA,gBAAwC;IACtCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAPFH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAiB,UAAA,SAAAuG,OAAA,CAAAT,UAAA,CAAgB;IAIoB/G,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAmH,OAAA,CAAAP,YAAA,CAAAC,IAAA,CAAuB;IAE7DlH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAA+G,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAAP,YAAA,CAAAS,IAAA,OACF;;;;;IAgCJ1H,EAAA,CAAA+B,SAAA,eAIO;;;;IADL/B,EAAA,CAAA2H,WAAA,WAAAC,OAAA,OAAuB;;;;;;IA5B7B5H,EAAA,CAAAC,cAAA,eAA+D;IAIvDD,EAAA,CAAA+B,SAAA,aAAiC;IACnC/B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA8C;IAEzCD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAC5B;IACDH,EAAA,CAAAC,cAAA,gBAAgD;IAAAD,EAAA,CAAAE,MAAA,GAE9C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGbH,EAAA,CAAAC,cAAA,gBAA8C;IACpCD,EAAA,CAAAuB,UAAA,mBAAAsG,8DAAA;MAAA7H,EAAA,CAAA0B,aAAA,CAAAoG,IAAA;MAAA,MAAAC,OAAA,GAAA/H,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAkG,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IACtChI,EAAA,CAAA+B,SAAA,cAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAmE;IAA3DD,EAAA,CAAAuB,UAAA,mBAAA0G,8DAAA;MAAAjI,EAAA,CAAA0B,aAAA,CAAAoG,IAAA;MAAA,MAAAI,OAAA,GAAAlI,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAqG,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IACpCnI,EAAA,CAAA+B,SAAA,cAAkC;IACpC/B,EAAA,CAAAG,YAAA,EAAS;IAGbH,EAAA,CAAAC,cAAA,gBAA+C;IAC7CD,EAAA,CAAAgC,UAAA,KAAAoG,2CAAA,mBAIO;IACTpI,EAAA,CAAAG,YAAA,EAAM;;;;IApBgDH,EAAA,CAAAI,SAAA,GAE9C;IAF8CJ,EAAA,CAAAK,iBAAA,CAAAgI,OAAA,CAAAC,uBAAA,CAAAD,OAAA,CAAAE,sBAAA,EAE9C;IAcYvI,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAiB,UAAA,YAAAoH,OAAA,CAAAG,iBAAA,CAAoB;;;;;;IAkClCxI,EAAA,CAAAC,cAAA,eAAqE;IAEjED,EAAA,CAAAuB,UAAA,mBAAAkH,6DAAA;MAAAzI,EAAA,CAAA0B,aAAA,CAAAgH,IAAA;MAAA,MAAAC,OAAA,GAAA3I,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA8G,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC5I,EAAA,CAAA+B,SAAA,aAA4B;IAC5B/B,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpBH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAsH,6DAAA;MAAA7I,EAAA,CAAA0B,aAAA,CAAAgH,IAAA;MAAA,MAAAI,OAAA,GAAA9I,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAiH,OAAA,CAAAF,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC5I,EAAA,CAAA+B,SAAA,aAA4B;IAC5B/B,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpBH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAwH,6DAAA;MAAA/I,EAAA,CAAA0B,aAAA,CAAAgH,IAAA;MAAA,MAAAM,OAAA,GAAAhJ,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAmH,OAAA,CAAAJ,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC5I,EAAA,CAAA+B,SAAA,cAA4B;IAC5B/B,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpBH,EAAA,CAAAC,cAAA,mBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA0H,8DAAA;MAAAjJ,EAAA,CAAA0B,aAAA,CAAAgH,IAAA;MAAA,MAAAQ,OAAA,GAAAlJ,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAqH,OAAA,CAAAN,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAGtC5I,EAAA,CAAA+B,SAAA,cAA2B;IAC3B/B,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvBH,EAAA,CAAAC,cAAA,mBAAkE;IAA1DD,EAAA,CAAAuB,UAAA,mBAAA4H,8DAAA;MAAAnJ,EAAA,CAAA0B,aAAA,CAAAgH,IAAA;MAAA,MAAAU,OAAA,GAAApJ,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAuH,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAC5BrJ,EAAA,CAAA+B,SAAA,cAA6B;IAC7B/B,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,mBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA6BrBH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,gBAAA6I,OAAA,GAAAC,OAAA,CAAAC,WAAA,CAAAC,GAAA,8BAAAH,OAAA,CAAAI,KAAA,kBAAAJ,OAAA,CAAAI,KAAA,CAAAC,MAAA,aACF;;;;;IAkBA3J,EAAA,CAAA+B,SAAA,aAA4D;;;;;IAC5D/B,EAAA,CAAA+B,SAAA,YAA+D;;;;;;;;;;;IAVjE/B,EAAA,CAAAC,cAAA,kBAQC;IALCD,EAAA,CAAAuB,UAAA,mBAAAqI,gEAAA;MAAA5J,EAAA,CAAA0B,aAAA,CAAAmI,IAAA;MAAA,MAAAC,OAAA,GAAA9J,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAiI,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAMvB/J,EAAA,CAAAgC,UAAA,IAAAgI,2CAAA,iBAA4D;IAC5DhK,EAAA,CAAAgC,UAAA,IAAAiI,2CAAA,iBAA+D;IACjEjK,EAAA,CAAAG,YAAA,EAAS;;;;IANPH,EAAA,CAAAiB,UAAA,cAAAiJ,OAAA,CAAAC,cAAA,GAA8B,YAAAnK,EAAA,CAAAkB,eAAA,IAAAkJ,GAAA,EAAAF,OAAA,CAAAG,gBAAA;IAICrK,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAiB,UAAA,UAAAiJ,OAAA,CAAAG,gBAAA,CAAuB;IACnBrK,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAiB,UAAA,SAAAiJ,OAAA,CAAAG,gBAAA,CAAsB;;;;;;;;;;;IAG3DrK,EAAA,CAAAC,cAAA,kBAWC;IARCD,EAAA,CAAAuB,UAAA,uBAAA+I,oEAAA;MAAAtK,EAAA,CAAA0B,aAAA,CAAA6I,IAAA;MAAA,MAAAC,OAAA,GAAAxK,EAAA,CAAA6C,aAAA;MAAA,OAAa7C,EAAA,CAAA6B,WAAA,CAAA2I,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC,qBAAAC,kEAAA;MAAA1K,EAAA,CAAA0B,aAAA,CAAA6I,IAAA;MAAA,MAAAI,OAAA,GAAA3K,EAAA,CAAA6C,aAAA;MAAA,OACxB7C,EAAA,CAAA6B,WAAA,CAAA8I,OAAA,CAAAxC,kBAAA,EAAoB;IAAA,EADI,wBAAAyC,qEAAA;MAAA5K,EAAA,CAAA0B,aAAA,CAAA6I,IAAA;MAAA,MAAAM,OAAA,GAAA7K,EAAA,CAAA6C,aAAA;MAAA,OAErB7C,EAAA,CAAA6B,WAAA,CAAAgJ,OAAA,CAAA7C,oBAAA,EAAsB;IAAA,EAFD,wBAAA8C,qEAAA;MAAA9K,EAAA,CAAA0B,aAAA,CAAA6I,IAAA;MAAA,MAAAQ,OAAA,GAAA/K,EAAA,CAAA6C,aAAA;MAAA,OAGrB7C,EAAA,CAAA6B,WAAA,CAAAkJ,OAAA,CAAAN,mBAAA,EAAqB;IAAA,EAHA,sBAAAO,mEAAA;MAAAhL,EAAA,CAAA0B,aAAA,CAAA6I,IAAA;MAAA,MAAAU,OAAA,GAAAjL,EAAA,CAAA6C,aAAA;MAAA,OAIvB7C,EAAA,CAAA6B,WAAA,CAAAoJ,OAAA,CAAA9C,kBAAA,EAAoB;IAAA,EAJG;IASnCnI,EAAA,CAAA+B,SAAA,aAAiC;IACnC/B,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,IAAAgK,GAAA,EAAAC,OAAA,CAAAC,gBAAA,EAA2C;;;;;;;;;;;IAa7CpL,EAAA,CAAAC,cAAA,kBAMC;IAJCD,EAAA,CAAAuB,UAAA,mBAAA8J,sEAAA;MAAA,MAAA5J,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAA4J,IAAA;MAAA,MAAAC,YAAA,GAAA9J,WAAA,CAAAG,SAAA;MAAA,MAAA4J,OAAA,GAAAxL,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA2J,OAAA,CAAAC,mBAAA,CAAAF,YAAA,CAAArE,IAAA,CAAkC;IAAA,EAAC;IAK5ClH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAJPH,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,IAAAwK,GAAA,EAAAC,OAAA,CAAAC,qBAAA,KAAAL,YAAA,CAAArE,IAAA,EAA+D,UAAAqE,YAAA,CAAAjI,KAAA;IAG/DtD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAA8K,YAAA,CAAA/I,IAAA,MACF;;;;;;IAkBFxC,EAAA,CAAAC,cAAA,kBAKC;IAHCD,EAAA,CAAAuB,UAAA,mBAAAsK,sEAAA;MAAA,MAAApK,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAoK,IAAA;MAAA,MAAAC,SAAA,GAAAtK,WAAA,CAAAG,SAAA;MAAA,MAAAoK,OAAA,GAAAhM,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAmK,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAAG,IAAA,CAAuB;IAAA,EAAC;IAIjClM,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAAiB,UAAA,UAAA8K,SAAA,CAAA7E,IAAA,CAAoB;IAEpBlH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAsL,SAAA,CAAAG,IAAA,MACF;;;;;;IAOElM,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA4K,6EAAA;MAAA,MAAA1K,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAA0K,IAAA;MAAA,MAAAC,SAAA,GAAA5K,WAAA,CAAAG,SAAA;MAAA,MAAA0K,OAAA,GAAAtM,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAyK,OAAA,CAAAL,WAAA,CAAAI,SAAA,CAAkB;IAAA,EAAC;IAG5BrM,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAA4L,SAAA,MACF;;;;;IATJrM,EAAA,CAAAC,cAAA,eAAoE;IACvBD,EAAA,CAAAE,MAAA,mCAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnEH,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAgC,UAAA,IAAAuK,oDAAA,sBAMS;IACXvM,EAAA,CAAAG,YAAA,EAAM;;;;IANgBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAiB,UAAA,YAAAuL,OAAA,CAAAC,YAAA,CAAe;;;;;;IA5CzCzM,EAAA,CAAAC,cAAA,eAA2D;IAGrDD,EAAA,CAAAgC,UAAA,IAAA0K,6CAAA,sBAQS;IACX1M,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAAmE;IAA3DD,EAAA,CAAAuB,UAAA,mBAAAoL,6DAAA;MAAA3M,EAAA,CAAA0B,aAAA,CAAAkL,IAAA;MAAA,MAAAC,OAAA,GAAA7M,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAgL,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACnC9M,EAAA,CAAA+B,SAAA,YAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,eAAmC;IAG/BD,EAAA,CAAAuB,UAAA,2BAAAwL,oEAAAC,MAAA;MAAAhN,EAAA,CAAA0B,aAAA,CAAAkL,IAAA;MAAA,MAAAK,QAAA,GAAAjN,EAAA,CAAA6C,aAAA;MAAA,OAAA7C,EAAA,CAAA6B,WAAA,CAAAoL,QAAA,CAAAC,gBAAA,GAAAF,MAAA;IAAA,EAA8B,mBAAAG,4DAAA;MAAAnN,EAAA,CAAA0B,aAAA,CAAAkL,IAAA;MAAA,MAAAQ,QAAA,GAAApN,EAAA,CAAA6C,aAAA;MAAA,OACrB7C,EAAA,CAAA6B,WAAA,CAAAuL,QAAA,CAAAC,YAAA,EAAc;IAAA,EADO;IAFhCrN,EAAA,CAAAG,YAAA,EAME;IAGJH,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAgC,UAAA,IAAAsL,6CAAA,sBAOS;IACXtN,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAgC,UAAA,KAAAuL,2CAAA,mBAWM;IACRvN,EAAA,CAAAG,YAAA,EAAM;;;;IAhDuBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAiB,UAAA,YAAAuM,OAAA,CAAAC,eAAA,CAAkB;IAiBzCzN,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAiB,UAAA,YAAAuM,OAAA,CAAAN,gBAAA,CAA8B;IASZlN,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAiB,UAAA,YAAAuM,OAAA,CAAAE,iBAAA,GAAsB;IAUtC1N,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAiB,UAAA,SAAAuM,OAAA,CAAAf,YAAA,CAAA9C,MAAA,KAA6B;;;;;IAsDrC3J,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAA+B,SAAA,aAGK;IACL/B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAE5BH,EAAA,CAAAC,cAAA,eAGC;IACyCD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAC,cAAA,iBAA4B;IAAAD,EAAA,CAAAE,MAAA,GAE1B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAH8BH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAsN,iBAAA,CAAA5H,OAAA,CAA0B;IACtC/F,EAAA,CAAAI,SAAA,GAE1B;IAF0BJ,EAAA,CAAAK,iBAAA,CAAAuN,OAAA,CAAA5H,iBAAA,CAAA2H,iBAAA,CAAA1H,SAAA,EAE1B;;;;;IA4BAjG,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAA+B,SAAA,eAEO;IACT/B,EAAA,CAAAG,YAAA,EAAM;;;;;;IAGNH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAsM,sEAAA;MAAA7N,EAAA,CAAA0B,aAAA,CAAAoM,KAAA;MAAA,MAAAC,QAAA,GAAA/N,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAkM,QAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvBhO,EAAA,CAAA+B,SAAA,aAAoC;IACtC/B,EAAA,CAAAG,YAAA,EAAS;;;;;;IAuBTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA0M,6EAAA;MAAA,MAAAxM,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAwM,KAAA;MAAA,MAAAC,WAAA,GAAA1M,WAAA,CAAAG,SAAA;MAAA,MAAAwM,QAAA,GAAApO,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAsM,WAAA,CAAAvI,EAAA,IAAawI,QAAA,CAAAC,iBAAA,CAAAF,WAAA,CAAAvI,EAAA,CAA4B;IAAA,EAAC;IAGnD5F,EAAA,CAAAC,cAAA,eAAwC;IAIpCD,EAAA,CAAA+B,SAAA,aAEK;IACP/B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAExBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA+B,SAAA,eAKO;IACT/B,EAAA,CAAAG,YAAA,EAAM;;;;;IARFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAA6N,QAAA,CAAAtI,iBAAA,CAAAmI,WAAA,CAAAlI,SAAA,OACF;IAGEjG,EAAA,CAAAI,SAAA,GAEC;IAFDJ,EAAA,CAAAiB,UAAA,cAAAqN,QAAA,CAAAC,oBAAA,CAAAJ,WAAA,CAAApI,OAAA,QAAAuI,QAAA,CAAAE,WAAA,GAAAxO,EAAA,CAAAyO,cAAA,CAEC;;;;;IA/BbzO,EAAA,CAAAC,cAAA,eAGC;IAIGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAsC;IACpCD,EAAA,CAAAgC,UAAA,IAAA0M,oDAAA,sBAyBS;IACX1O,EAAA,CAAAG,YAAA,EAAM;;;;IA7BJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAkO,QAAA,CAAAC,aAAA,CAAAjF,MAAA,sCACF;IAGuB3J,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAiB,UAAA,YAAA0N,QAAA,CAAAC,aAAA,CAAgB;;;;;IA6BzC5O,EAAA,CAAAC,cAAA,eAQC;IACCD,EAAA,CAAA+B,SAAA,aAEK;IACL/B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,uCAAAoO,QAAA,CAAAL,WAAA,QACF;;;;;;IAzGFxO,EAAA,CAAAC,cAAA,eAGC;IAIKD,EAAA,CAAA+B,SAAA,aAAqC;IACvC/B,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA6B;IAGzBD,EAAA,CAAAuB,UAAA,2BAAAuN,oEAAA9B,MAAA;MAAAhN,EAAA,CAAA0B,aAAA,CAAAqN,KAAA;MAAA,MAAAC,QAAA,GAAAhP,EAAA,CAAA6C,aAAA;MAAA,OAAA7C,EAAA,CAAA6B,WAAA,CAAAmN,QAAA,CAAAR,WAAA,GAAAxB,MAAA;IAAA,EAAyB,mBAAAiC,4DAAAjC,MAAA;MAAAhN,EAAA,CAAA0B,aAAA,CAAAqN,KAAA;MAAA,MAAAG,QAAA,GAAAlP,EAAA,CAAA6C,aAAA;MAAA,OAChB7C,EAAA,CAAA6B,WAAA,CAAAqN,QAAA,CAAAC,aAAA,CAAAnC,MAAA,CAAqB;IAAA,EADL,qBAAAoC,8DAAApC,MAAA;MAAAhN,EAAA,CAAA0B,aAAA,CAAAqN,KAAA;MAAA,MAAAM,QAAA,GAAArP,EAAA,CAAA6C,aAAA;MAAA,OAEd7C,EAAA,CAAA6B,WAAA,CAAAwN,QAAA,CAAAC,gBAAA,CAAAtC,MAAA,CAAwB;IAAA,EAFV;IAF3BhN,EAAA,CAAAG,YAAA,EAQE;IAGFH,EAAA,CAAAgC,UAAA,IAAAuN,0CAAA,mBAOM;IAGNvP,EAAA,CAAAgC,UAAA,IAAAwN,6CAAA,sBAMS;IACXxP,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAkO,6DAAA;MAAAzP,EAAA,CAAA0B,aAAA,CAAAqN,KAAA;MAAA,MAAAW,QAAA,GAAA1P,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA6N,QAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAG3B3P,EAAA,CAAA+B,SAAA,YAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAgC,UAAA,KAAA4N,2CAAA,mBAqCM;IAGN5P,EAAA,CAAAgC,UAAA,KAAA6N,2CAAA,mBAaM;IACR7P,EAAA,CAAAG,YAAA,EAAM;;;;IA5FEH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAiB,UAAA,YAAA6O,OAAA,CAAAtB,WAAA,CAAyB;IAUxBxO,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAiB,UAAA,SAAA6O,OAAA,CAAAC,WAAA,CAAiB;IAUjB/P,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAiB,UAAA,SAAA6O,OAAA,CAAAtB,WAAA,KAAAsB,OAAA,CAAAC,WAAA,CAAiC;IAmBrC/P,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAiB,UAAA,SAAA6O,OAAA,CAAAE,UAAA,IAAAF,OAAA,CAAAlB,aAAA,CAAAjF,MAAA,KAA4C;IAwC5C3J,EAAA,CAAAI,SAAA,GAMP;IANOJ,EAAA,CAAAiB,UAAA,SAAA6O,OAAA,CAAAE,UAAA,IAAAF,OAAA,CAAAlB,aAAA,CAAAjF,MAAA,WAAAmG,OAAA,CAAAC,WAAA,IAAAD,OAAA,CAAAtB,WAAA,CAAA7E,MAAA,MAMP;;;;;IAmCM3J,EAAA,CAAAC,cAAA,eAAiE;IAE7DD,EAAA,CAAA+B,SAAA,aAA+D;IAC/D/B,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,sCAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA8C1CH,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAE,MAAA,GAElC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAF2BH,EAAA,CAAAI,SAAA,GAElC;IAFkCJ,EAAA,CAAAK,iBAAA,CAAA4P,kBAAA,CAAAlK,OAAA,CAElC;;;;;IACF/F,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAA+B,SAAA,aAAiC;IACjC/B,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAA+B,SAAA,aAAsC;IACtC/B,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IArDfH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA2O,6EAAA;MAAA,MAAAzO,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAyO,KAAA;MAAA,MAAAF,kBAAA,GAAAxO,WAAA,CAAAG,SAAA;MAAA,MAAAwO,QAAA,GAAApQ,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAuO,QAAA,CAAAC,qBAAA,CAAAJ,kBAAA,CAAArK,EAAA,CAAwC;IAAA,EAAC;IAGlD5F,EAAA,CAAAC,cAAA,eAAwC;IAGpCD,EAAA,CAAA+B,SAAA,eAQE;IACJ/B,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA4B;IAMtBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAgC,UAAA,KAAAsO,4DAAA,oBAES;IACTtQ,EAAA,CAAAgC,UAAA,KAAAuO,4DAAA,oBAMO;IACPvQ,EAAA,CAAAgC,UAAA,KAAAwO,4DAAA,oBAMO;IACTxQ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAoC;IAClCD,EAAA,CAAA+B,SAAA,cAEK;IACL/B,EAAA,CAAAC,cAAA,iBAAyD;IACvDD,EAAA,CAAAE,MAAA,IAMF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKXH,EAAA,CAAAC,cAAA,gBAAoE;IAClED,EAAA,CAAA+B,SAAA,cAA4C;IAC9C/B,EAAA,CAAAG,YAAA,EAAM;;;;;IAlEFH,EAAA,CAAAI,SAAA,GAGC;IAHDJ,EAAA,CAAAiB,UAAA,SAAAgP,kBAAA,CAAAtK,MAAA,kBAAAsK,kBAAA,CAAAtK,MAAA,CAAA8K,KAAA,yCAAAzQ,EAAA,CAAAgH,aAAA,CAGC,SAAAiJ,kBAAA,CAAAtK,MAAA,kBAAAsK,kBAAA,CAAAtK,MAAA,CAAAnF,QAAA;IAcCR,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,OAAAwP,kBAAA,CAAAtK,MAAA,kBAAAsK,kBAAA,CAAAtK,MAAA,CAAAnF,QAAA,gCACF;IAEER,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAiQ,QAAA,CAAA1K,iBAAA,CAAAiK,kBAAA,CAAAhK,SAAA,OACF;IAOOjG,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAiB,UAAA,SAAAgP,kBAAA,CAAAlK,OAAA,CAA2B;IAI/B/F,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAiB,UAAA,SAAAyP,QAAA,CAAA9J,QAAA,CAAAqJ,kBAAA,EAA6B;IAO7BjQ,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAiB,UAAA,SAAAyP,QAAA,CAAA7J,cAAA,CAAAoJ,kBAAA,EAAmC;IAcpCjQ,EAAA,CAAAI,SAAA,GAMF;IANEJ,EAAA,CAAAS,kBAAA,wBAAAwP,kBAAA,CAAAU,QAAA,WAAAD,QAAA,CAAAE,iBAAA,CAAAX,kBAAA,CAAAU,QAAA,YAMF;;;;;IAxEV3Q,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAgC,UAAA,IAAA6O,oDAAA,uBA6ES;IACX7Q,EAAA,CAAAG,YAAA,EAAM;;;;IA7EwBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAiB,UAAA,YAAA6P,QAAA,CAAAC,cAAA,CAAiB;;;;;;IAtCnD/Q,EAAA,CAAAC,cAAA,eAGC;IAMKD,EAAA,CAAA+B,SAAA,aAAmE;IACnE/B,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAyP,6DAAA;MAAAhR,EAAA,CAAA0B,aAAA,CAAAuP,KAAA;MAAA,MAAAC,QAAA,GAAAlR,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAqP,QAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCnR,EAAA,CAAA+B,SAAA,aAAoC;IACtC/B,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,eAAsC;IAEpCD,EAAA,CAAAgC,UAAA,IAAAoP,0CAAA,mBAKM;IAGNpR,EAAA,CAAAgC,UAAA,KAAAqP,2CAAA,mBAkFM;IACRrR,EAAA,CAAAG,YAAA,EAAM;;;;IAzGAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,mCAAA6Q,OAAA,CAAAC,sBAAA,SACF;IAaIvR,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAiB,UAAA,SAAAqQ,OAAA,CAAAP,cAAA,CAAApH,MAAA,OAAiC;IASpC3J,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAiB,UAAA,SAAAqQ,OAAA,CAAAP,cAAA,CAAApH,MAAA,KAA+B;;;;;;IAwFtC3J,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAA+B,SAAA,eAA+C;IAC/C/B,EAAA,CAAAC,cAAA,eAAyC;IAGnCD,EAAA,CAAA+B,SAAA,eAKE;IACJ/B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmC;IAC7BD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,QAAG;IACDD,EAAA,CAAAE,MAAA,IAKF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGRH,EAAA,CAAAC,cAAA,gBAAmC;IACzBD,EAAA,CAAAuB,UAAA,mBAAAiQ,8DAAA;MAAAxR,EAAA,CAAA0B,aAAA,CAAA+P,KAAA;MAAA,MAAAC,QAAA,GAAA1R,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA6P,QAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC7B3R,EAAA,CAAA+B,SAAA,cAAkC;IACpC/B,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAA4D;IAApDD,EAAA,CAAAuB,UAAA,mBAAAqQ,8DAAA;MAAA5R,EAAA,CAAA0B,aAAA,CAAA+P,KAAA;MAAA,MAAAI,QAAA,GAAA7R,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAgQ,QAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAC5B9R,EAAA,CAAA+B,SAAA,cAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;;;;IAvBLH,EAAA,CAAAI,SAAA,GAEC;IAFDJ,EAAA,CAAAiB,UAAA,SAAA8Q,OAAA,CAAAC,YAAA,CAAAC,MAAA,kBAAAF,OAAA,CAAAC,YAAA,CAAAC,MAAA,CAAAxB,KAAA,yCAAAzQ,EAAA,CAAAgH,aAAA,CAEC,QAAA+K,OAAA,CAAAC,YAAA,CAAAC,MAAA,kBAAAF,OAAA,CAAAC,YAAA,CAAAC,MAAA,CAAAzR,QAAA;IAKCR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,CAAA0R,OAAA,CAAAC,YAAA,CAAAC,MAAA,kBAAAF,OAAA,CAAAC,YAAA,CAAAC,MAAA,CAAAzR,QAAA,CAAmC;IAErCR,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAS,kBAAA,MAAAsR,OAAA,CAAAC,YAAA,CAAAE,IAAA,uEAKF;;;;;IAuCAlS,EAAA,CAAA+B,SAAA,aAAqD;;;;;IACrD/B,EAAA,CAAA+B,SAAA,aAAqD;;;;;IAIzD/B,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAA+B,SAAA,sBAAmE;IAErE/B,EAAA,CAAAG,YAAA,EAAM;;;;;IAWFH,EAAA,CAAA+B,SAAA,aAAsD;;;;;IACtD/B,EAAA,CAAA+B,SAAA,aAA2D;;;;;IAQ3D/B,EAAA,CAAA+B,SAAA,aAAmD;;;;;IACnD/B,EAAA,CAAA+B,SAAA,aAA0D;;;;;;IAP5D/B,EAAA,CAAAC,cAAA,kBAKC;IAHCD,EAAA,CAAAuB,UAAA,mBAAA4Q,uEAAA;MAAAnS,EAAA,CAAA0B,aAAA,CAAA0Q,KAAA;MAAA,MAAAC,QAAA,GAAArS,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAwQ,QAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAIvBtS,EAAA,CAAAgC,UAAA,IAAAuQ,kDAAA,iBAAmD;IACnDvS,EAAA,CAAAgC,UAAA,IAAAwQ,kDAAA,iBAA0D;IAC5DxS,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,IAAAwK,GAAA,GAAA+G,QAAA,CAAAC,cAAA,EAAuC;IAEd1S,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAiB,UAAA,SAAAwR,QAAA,CAAAC,cAAA,CAAoB;IACd1S,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAiB,UAAA,UAAAwR,QAAA,CAAAC,cAAA,CAAqB;;;;;;;;;;;;;;;;IArD5D1S,EAAA,CAAAC,cAAA,eAIC;IAIOD,EAAA,CAAA+B,SAAA,eAME;IACF/B,EAAA,CAAAC,cAAA,eAA4C;IACtCD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,gBAAqC;IAAAD,EAAA,CAAAE,MAAA,GAEnC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,iBAAmC;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGvEH,EAAA,CAAAC,cAAA,mBAAsE;IAA9DD,EAAA,CAAAuB,UAAA,mBAAAoR,8DAAA;MAAA3S,EAAA,CAAA0B,aAAA,CAAAkR,KAAA;MAAA,MAAAC,QAAA,GAAA7S,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAgR,QAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IACpC9S,EAAA,CAAAgC,UAAA,KAAA+Q,yCAAA,iBAAqD;IACrD/S,EAAA,CAAAgC,UAAA,KAAAgR,yCAAA,iBAAqD;IACvDhT,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAgC,UAAA,KAAAiR,2CAAA,mBAGM;IAENjT,EAAA,CAAAC,cAAA,gBAGC;IAEGD,EAAA,CAAAuB,UAAA,mBAAA2R,8DAAA;MAAAlT,EAAA,CAAA0B,aAAA,CAAAkR,KAAA;MAAA,MAAAO,QAAA,GAAAnT,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAsR,QAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAItBpT,EAAA,CAAAgC,UAAA,KAAAqR,yCAAA,iBAAsD;IACtDrT,EAAA,CAAAgC,UAAA,KAAAsR,yCAAA,iBAA2D;IAC7DtT,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAgC,UAAA,KAAAuR,8CAAA,sBAQS;IACTvT,EAAA,CAAAC,cAAA,mBAAsD;IAA9CD,EAAA,CAAAuB,UAAA,mBAAAiS,8DAAA;MAAAxT,EAAA,CAAA0B,aAAA,CAAAkR,KAAA;MAAA,MAAAa,QAAA,GAAAzT,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA4R,QAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IACzB1T,EAAA,CAAA+B,SAAA,cAAkC;IACpC/B,EAAA,CAAAG,YAAA,EAAS;;;;IAtDbH,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAAyS,GAAA,EAAAC,OAAA,CAAAC,eAAA,EAA0C;IAMlC7T,EAAA,CAAAI,SAAA,GAGC;IAHDJ,EAAA,CAAAiB,UAAA,SAAA2S,OAAA,CAAAE,UAAA,CAAAC,WAAA,kBAAAH,OAAA,CAAAE,UAAA,CAAAC,WAAA,CAAAtD,KAAA,yCAAAzQ,EAAA,CAAAgH,aAAA,CAGC,QAAA4M,OAAA,CAAAE,UAAA,CAAAC,WAAA,kBAAAH,OAAA,CAAAE,UAAA,CAAAC,WAAA,CAAAvT,QAAA;IAIGR,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAAuT,OAAA,CAAAE,UAAA,CAAAC,WAAA,kBAAAH,OAAA,CAAAE,UAAA,CAAAC,WAAA,CAAAvT,QAAA,CAAsC;IACLR,EAAA,CAAAI,SAAA,GAEnC;IAFmCJ,EAAA,CAAAK,iBAAA,CAAAuT,OAAA,CAAAI,kBAAA,CAAAJ,OAAA,CAAAK,YAAA,EAEnC;IACiCjU,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAuT,OAAA,CAAAM,iBAAA,GAAyB;IAIrClU,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAiB,UAAA,UAAA2S,OAAA,CAAAC,eAAA,CAAsB;IACrB7T,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAiB,UAAA,SAAA2S,OAAA,CAAAC,eAAA,CAAqB;IAI7C7T,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAiB,UAAA,SAAA2S,OAAA,CAAAE,UAAA,CAAA5B,IAAA,aAAiC;IAOrClS,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAAiT,IAAA,EAAAP,OAAA,CAAAQ,gBAAA,KAAAR,OAAA,CAAAC,eAAA,EAA0D;IAKxD7T,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAAwK,GAAA,EAAAkI,OAAA,CAAAS,WAAA,EAAmC;IAELrU,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAiB,UAAA,UAAA2S,OAAA,CAAAS,WAAA,CAAkB;IACZrU,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAiB,UAAA,SAAA2S,OAAA,CAAAS,WAAA,CAAiB;IAGpDrU,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAiB,UAAA,SAAA2S,OAAA,CAAAE,UAAA,CAAA5B,IAAA,aAAiC;;;;;;IAgB1ClS,EAAA,CAAAC,cAAA,eAAmE;IAGzDD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEnCH,EAAA,CAAAC,cAAA,eAAiC;IAE7BD,EAAA,CAAAE,MAAA,sGAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAC,cAAA,eAAoC;IAC1BD,EAAA,CAAAuB,UAAA,mBAAA+S,6DAAA;MAAAtU,EAAA,CAAA0B,aAAA,CAAA6S,KAAA;MAAA,MAAAC,QAAA,GAAAxU,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA2S,QAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC9BzU,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAiE;IAAzDD,EAAA,CAAAuB,UAAA,mBAAAmT,8DAAA;MAAA1U,EAAA,CAAA0B,aAAA,CAAA6S,KAAA;MAAA,MAAAI,QAAA,GAAA3U,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA8S,QAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC/B5U,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAwBPH,EAAA,CAAAC,cAAA,eAIC;IADCD,EAAA,CAAAuB,UAAA,mBAAAsT,iEAAA;MAAA,MAAApT,WAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAAoT,KAAA;MAAA,MAAAC,YAAA,GAAAtT,WAAA,CAAAG,SAAA;MAAA,MAAAoT,QAAA,GAAAhV,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAmT,QAAA,CAAAC,sBAAA,CAAAF,YAAA,CAAAnP,EAAA,CAAkC;IAAA,EAAC;IAE5C5F,EAAA,CAAA+B,SAAA,iBAGE;IAKF/B,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IANjCH,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAiB,UAAA,YAAAiU,QAAA,CAAAC,gBAAA,CAAAC,QAAA,CAAAL,YAAA,CAAAnP,EAAA,EAAiD;IAGjD5F,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAiB,UAAA,QAAA8T,YAAA,CAAAtE,KAAA,wCAAAzQ,EAAA,CAAAgH,aAAA,CAA2D,QAAA+N,YAAA,CAAAvU,QAAA;IAGvDR,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAA0U,YAAA,CAAAvU,QAAA,CAAsB;;;;;;IA/BtCR,EAAA,CAAAC,cAAA,eAA6D;IAGnDD,EAAA,CAAAE,MAAA,iCAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,kBAAmE;IAA3DD,EAAA,CAAAuB,UAAA,mBAAA8T,6DAAA;MAAArV,EAAA,CAAA0B,aAAA,CAAA4T,KAAA;MAAA,MAAAC,QAAA,GAAAvV,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA0T,QAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACnCxV,EAAA,CAAA+B,SAAA,YAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAiC;IAI3BD,EAAA,CAAAuB,UAAA,2BAAAkU,oEAAAzI,MAAA;MAAAhN,EAAA,CAAA0B,aAAA,CAAA4T,KAAA;MAAA,MAAAI,QAAA,GAAA1V,EAAA,CAAA6C,aAAA;MAAA,OAAA7C,EAAA,CAAA6B,WAAA,CAAA6T,QAAA,CAAAC,kBAAA,GAAA3I,MAAA;IAAA,EAAgC;IAFlChN,EAAA,CAAAG,YAAA,EAKE;IAEJH,EAAA,CAAAC,cAAA,gBAAuC;IACrCD,EAAA,CAAAgC,UAAA,KAAA4T,2CAAA,mBAcM;IACR5V,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,gBAAoC;IAC1BD,EAAA,CAAAuB,UAAA,mBAAAsU,8DAAA;MAAA7V,EAAA,CAAA0B,aAAA,CAAA4T,KAAA;MAAA,MAAAQ,QAAA,GAAA9V,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAiU,QAAA,CAAAN,iBAAA,EAAmB;IAAA,EAAC;IACnCxV,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAIC;IAHCD,EAAA,CAAAuB,UAAA,mBAAAwU,8DAAA;MAAA/V,EAAA,CAAA0B,aAAA,CAAA4T,KAAA;MAAA,MAAAU,QAAA,GAAAhW,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAmU,QAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAI1BjW,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAjCLH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAiB,UAAA,YAAAiV,OAAA,CAAAP,kBAAA,CAAgC;IAOZ3V,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAiB,UAAA,YAAAiV,OAAA,CAAAC,mBAAA,GAAwB;IAuB9CnW,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAiB,UAAA,aAAAiV,OAAA,CAAAf,gBAAA,CAAAxL,MAAA,OAA0C;IAE1C3J,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,uBAAAyV,OAAA,CAAAf,gBAAA,CAAAxL,MAAA,OACF;;;;;;IAwBA3J,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA6U,uEAAA;MAAApW,EAAA,CAAA0B,aAAA,CAAA2U,KAAA;MAAA,MAAAC,QAAA,GAAAtW,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAyU,QAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzBvW,EAAA,CAAA+B,SAAA,aAAmC;IACrC/B,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAiV,uEAAA;MAAAxW,EAAA,CAAA0B,aAAA,CAAA+U,KAAA;MAAA,MAAAC,QAAA,GAAA1W,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA6U,QAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAGrB3W,EAAA,CAAA+B,SAAA,aAAoC;IACtC/B,EAAA,CAAAG,YAAA,EAAS;;;;;IAEXH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAS,kBAAA,MAAAmW,QAAA,CAAAC,YAAA,CAAAD,QAAA,CAAAE,iBAAA,mBAAAF,QAAA,CAAAC,YAAA,CAAAD,QAAA,CAAAE,iBAAA,EAAAC,OAAA,MACF;;;;;;IA3CJ/W,EAAA,CAAAC,cAAA,eAA2D;IAGvDD,EAAA,CAAAuB,UAAA,mBAAAyV,0DAAA;MAAAhX,EAAA,CAAA0B,aAAA,CAAAuV,KAAA;MAAA,MAAAC,QAAA,GAAAlX,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAqV,QAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAC7BnX,EAAA,CAAAG,YAAA,EAAM;IACPH,EAAA,CAAAC,cAAA,eAA2C;IAG/BD,EAAA,CAAAE,MAAA,GAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtEH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAA6V,6DAAA;MAAApX,EAAA,CAAA0B,aAAA,CAAAuV,KAAA;MAAA,MAAAI,QAAA,GAAArX,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAwV,QAAA,CAAAF,gBAAA,EAAkB;IAAA,EAAC;IAG5BnX,EAAA,CAAA+B,SAAA,YAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAgC,UAAA,KAAAsV,8CAAA,sBAMS;IACTtX,EAAA,CAAA+B,SAAA,gBAIE;IACF/B,EAAA,CAAAgC,UAAA,KAAAuV,8CAAA,sBAMS;IACXvX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAgC,UAAA,KAAAwV,2CAAA,mBAKM;IACRxX,EAAA,CAAAG,YAAA,EAAM;;;;IApCMH,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAyX,kBAAA,KAAAC,OAAA,CAAAZ,iBAAA,aAAAY,OAAA,CAAAb,YAAA,CAAAlN,MAAA,KAAuD;IAW5D3J,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAiB,UAAA,SAAAyW,OAAA,CAAAb,YAAA,CAAAlN,MAAA,KAA6B;IAO9B3J,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAiB,UAAA,QAAAyW,OAAA,CAAAb,YAAA,CAAAa,OAAA,CAAAZ,iBAAA,mBAAAY,OAAA,CAAAb,YAAA,CAAAa,OAAA,CAAAZ,iBAAA,EAAAa,GAAA,EAAA3X,EAAA,CAAAgH,aAAA,CAA4C,QAAA0Q,OAAA,CAAAb,YAAA,CAAAa,OAAA,CAAAZ,iBAAA,mBAAAY,OAAA,CAAAb,YAAA,CAAAa,OAAA,CAAAZ,iBAAA,EAAAC,OAAA;IAK3C/W,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAiB,UAAA,SAAAyW,OAAA,CAAAb,YAAA,CAAAlN,MAAA,KAA6B;IAQ/B3J,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAiB,UAAA,SAAAyW,OAAA,CAAAb,YAAA,CAAAa,OAAA,CAAAZ,iBAAA,mBAAAY,OAAA,CAAAb,YAAA,CAAAa,OAAA,CAAAZ,iBAAA,EAAAC,OAAA,CAA8C;;;;;IASrD/W,EAAA,CAAAC,cAAA,eAAoE;IAEhED,EAAA,CAAA+B,SAAA,QAAgC;IAChC/B,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHWH,EAAA,CAAAiB,UAAA,YAAA2W,OAAA,CAAAC,SAAA,CAAqB;IAE5D7X,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAa,UAAA,CAAA+W,OAAA,CAAAE,YAAA,GAAwB;IACrB9X,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAuX,OAAA,CAAAG,YAAA,CAAkB;;;;;;IAK5B/X,EAAA,CAAAC,cAAA,eAGC;IAEOD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAyW,6DAAA;MAAAhY,EAAA,CAAA0B,aAAA,CAAAuW,KAAA;MAAA,MAAAC,QAAA,GAAAlY,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAAqW,QAAA,CAAA9S,sBAAA,EAAwB;IAAA,EAAC;IAGlCpF,EAAA,CAAA+B,SAAA,YAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAsC;IAElCD,EAAA,CAAA+B,SAAA,eAGE;IACF/B,EAAA,CAAAC,cAAA,eAA0C;IACpCD,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACpCH,EAAA,CAAAC,cAAA,iBAA0C;IAAAD,EAAA,CAAAE,MAAA,IAExC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGbH,EAAA,CAAAC,cAAA,gBAAyC;IAEHD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAAkC;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAExEH,EAAA,CAAAC,cAAA,gBAA2B;IACSD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,iBAAkC;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjEH,EAAA,CAAAC,cAAA,gBAA2B;IACSD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAAkC;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAtB9DH,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAiB,UAAA,SAAAkX,OAAA,CAAA5X,gBAAA,kBAAA4X,OAAA,CAAA5X,gBAAA,CAAAkQ,KAAA,yCAAAzQ,EAAA,CAAAgH,aAAA,CAAqE,QAAAmR,OAAA,CAAA5X,gBAAA,kBAAA4X,OAAA,CAAA5X,gBAAA,CAAAC,QAAA;IAIjER,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAA8X,OAAA,CAAA5X,gBAAA,kBAAA4X,OAAA,CAAA5X,gBAAA,CAAAC,QAAA,CAAgC;IACjCR,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAA8X,OAAA,CAAA5X,gBAAA,kBAAA4X,OAAA,CAAA5X,gBAAA,CAAA6X,KAAA,CAA6B;IACUpY,EAAA,CAAAI,SAAA,GAExC;IAFwCJ,EAAA,CAAAK,iBAAA,EAAA8X,OAAA,CAAA5X,gBAAA,kBAAA4X,OAAA,CAAA5X,gBAAA,CAAAG,QAAA,8BAExC;IAMgCV,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAA8X,OAAA,CAAAE,qBAAA,GAA6B;IAI7BrY,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAA8X,OAAA,CAAAG,cAAA,GAAsB;IAItBtY,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAA8X,OAAA,CAAAI,aAAA,GAAqB;;;;;;IAO/DvY,EAAA,CAAAC,cAAA,eAGC;IAEOD,EAAA,CAAAE,MAAA,sCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAuB,UAAA,mBAAAiX,6DAAA;MAAAxY,EAAA,CAAA0B,aAAA,CAAA+W,KAAA;MAAA,MAAAC,QAAA,GAAA1Y,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA6B,WAAA,CAAA6W,QAAA,CAAAnT,0BAAA,EAA4B;IAAA,EAAC;IAGtCvF,EAAA,CAAA+B,SAAA,YAA4B;IAC9B/B,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAsC;IAE9BD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,gBAAmC;IAI7BD,EAAA,CAAAuB,UAAA,2BAAAoX,qEAAA3L,MAAA;MAAAhN,EAAA,CAAA0B,aAAA,CAAA+W,KAAA;MAAA,MAAAG,QAAA,GAAA5Y,EAAA,CAAA6C,aAAA;MAAA,OAAa7C,EAAA,CAAA6B,WAAA,CAAA+W,QAAA,CAAAC,oBAAA,CAAAC,aAAA,GAAA9L,MAAA,CACrB;IAAA,EADwD,oBAAA+L,8DAAA;MAAA/Y,EAAA,CAAA0B,aAAA,CAAA+W,KAAA;MAAA,MAAAO,QAAA,GAAAhZ,EAAA,CAAA6C,aAAA;MAAA,OACtC7C,EAAA,CAAA6B,WAAA,CAAAmX,QAAA,CAAAC,0BAAA,EAA4B;IAAA,EADU;IAFlDjZ,EAAA,CAAAG,YAAA,EAIE;IACFH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG3CH,EAAA,CAAAC,cAAA,gBAAmC;IAI7BD,EAAA,CAAAuB,UAAA,2BAAA2X,qEAAAlM,MAAA;MAAAhN,EAAA,CAAA0B,aAAA,CAAA+W,KAAA;MAAA,MAAAU,QAAA,GAAAnZ,EAAA,CAAA6C,aAAA;MAAA,OAAa7C,EAAA,CAAA6B,WAAA,CAAAsX,QAAA,CAAAN,oBAAA,CAAAO,kBAAA,GAAApM,MAAA,CACrB;IAAA,EAD6D,oBAAAqM,8DAAA;MAAArZ,EAAA,CAAA0B,aAAA,CAAA+W,KAAA;MAAA,MAAAa,QAAA,GAAAtZ,EAAA,CAAA6C,aAAA;MAAA,OAC3C7C,EAAA,CAAA6B,WAAA,CAAAyX,QAAA,CAAAL,0BAAA,EAA4B;IAAA,EADe;IAFvDjZ,EAAA,CAAAG,YAAA,EAIE;IACFH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIvCH,EAAA,CAAAC,cAAA,gBAAoC;IAC9BD,EAAA,CAAAE,MAAA,4BAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,gBAAmC;IAI7BD,EAAA,CAAAuB,UAAA,2BAAAgY,qEAAAvM,MAAA;MAAAhN,EAAA,CAAA0B,aAAA,CAAA+W,KAAA;MAAA,MAAAe,QAAA,GAAAxZ,EAAA,CAAA6C,aAAA;MAAA,OAAa7C,EAAA,CAAA6B,WAAA,CAAA2X,QAAA,CAAAX,oBAAA,CAAAY,YAAA,GAAAzM,MAAA,CACrB;IAAA,EADuD,oBAAA0M,8DAAA;MAAA1Z,EAAA,CAAA0B,aAAA,CAAA+W,KAAA;MAAA,MAAAkB,QAAA,GAAA3Z,EAAA,CAAA6C,aAAA;MAAA,OACrC7C,EAAA,CAAA6B,WAAA,CAAA8X,QAAA,CAAAV,0BAAA,EAA4B;IAAA,EADS;IAFjDjZ,EAAA,CAAAG,YAAA,EAIE;IACFH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,+BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGnCH,EAAA,CAAAC,cAAA,gBAAmC;IAI7BD,EAAA,CAAAuB,UAAA,2BAAAqY,qEAAA5M,MAAA;MAAAhN,EAAA,CAAA0B,aAAA,CAAA+W,KAAA;MAAA,MAAAoB,QAAA,GAAA7Z,EAAA,CAAA6C,aAAA;MAAA,OAAa7C,EAAA,CAAA6B,WAAA,CAAAgY,QAAA,CAAAhB,oBAAA,CAAAiB,gBAAA,GAAA9M,MAAA,CACrB;IAAA,EAD2D,oBAAA+M,8DAAA;MAAA/Z,EAAA,CAAA0B,aAAA,CAAA+W,KAAA;MAAA,MAAAuB,QAAA,GAAAha,EAAA,CAAA6C,aAAA;MAAA,OACzC7C,EAAA,CAAA6B,WAAA,CAAAmY,QAAA,CAAAf,0BAAA,EAA4B;IAAA,EADa;IAFrDjZ,EAAA,CAAAG,YAAA,EAIE;IACFH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IApChCH,EAAA,CAAAI,SAAA,IAAgD;IAAhDJ,EAAA,CAAAiB,UAAA,YAAAgZ,OAAA,CAAApB,oBAAA,CAAAC,aAAA,CAAgD;IAUhD9Y,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAiB,UAAA,YAAAgZ,OAAA,CAAApB,oBAAA,CAAAO,kBAAA,CAAqD;IAarDpZ,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAiB,UAAA,YAAAgZ,OAAA,CAAApB,oBAAA,CAAAY,YAAA,CAA+C;IAU/CzZ,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAiB,UAAA,YAAAgZ,OAAA,CAAApB,oBAAA,CAAAiB,gBAAA,CAAmD;;;;;;;;;;;;;ADznCjE,OAAM,MAAOI,oBAAoB;EA8K/B;EACA,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACC,CAAC,CAACC,SAAS;EACzB;EA6BA;EACA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACC,cAAc,CAACC,eAAe,EAAE;EAC9C;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAO,CACL;MACEzZ,KAAK,EAAE,gBAAgB;MACvBwB,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE,aAAa;MACpBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC4Y,YAAY,CAAC,OAAO,CAAC;MACzCtY,QAAQ,EAAE;KACX,EACD;MACEpB,KAAK,EAAE,gBAAgB;MACvBwB,IAAI,EAAE,cAAc;MACpBD,KAAK,EAAE,aAAa;MACpBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC4Y,YAAY,CAAC,OAAO,CAAC;MACzCtY,QAAQ,EAAE;KACX,EACD;MACEpB,KAAK,EAAE,YAAY;MACnBwB,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE,YAAY;MACnBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC6N,eAAe,EAAE;MACrCvN,QAAQ,EAAE,IAAI,CAACuY,aAAa;MAC5BxY,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACEnB,KAAK,EAAE,qBAAqB;MAC5BwB,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE,sBAAsB,IAAI,CAACgP,sBAAsB,EAAE,GAAG;MAC7DzP,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACqP,oBAAoB,EAAE;MAC1C/O,QAAQ,EAAE,IAAI,CAACwY,kBAAkB;MACjCzY,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DpB,KAAK,EACH,IAAI,CAACwQ,sBAAsB,EAAE,GAAG,CAAC,GAC7B;QACElQ,KAAK,EAAE,IAAI,CAACkQ,sBAAsB,EAAE;QACpCvQ,KAAK,EAAE,gCAAgC;QACvCI,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEJ,KAAK,EAAE,4BAA4B;MACnCwB,IAAI,EAAE,aAAa;MACnBD,KAAK,EAAE,eAAe;MACtBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC+Y,uBAAuB,EAAE;MAC7CzY,QAAQ,EAAE,IAAI,CAAC0Y,qBAAqB;MACpC3Y,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DpB,KAAK,EACH,IAAI,CAACga,uBAAuB,GAAG,CAAC,GAC5B;QACE1Z,KAAK,EAAE,IAAI,CAAC0Z,uBAAuB;QACnC/Z,KAAK,EAAE,8CAA8C;QACrDI,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEJ,KAAK,EAAE,sBAAsB;MAC7BwB,IAAI,EAAE,gBAAgB;MACtBD,KAAK,EAAE,uBAAuB;MAC9BT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACkZ,sBAAsB,EAAE;MAC5C5Y,QAAQ,EAAE,IAAI,CAAC6Y,oBAAoB;MACnC9Y,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACEnB,KAAK,EAAE,oBAAoB;MAC3BwB,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE,uBAAuB;MAC9BT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACoZ,oBAAoB,EAAE;MAC1C9Y,QAAQ,EAAE,IAAI,CAAC+Y,kBAAkB;MACjChZ,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACEnB,KAAK,EAAE,6BAA6B;MACpCwB,IAAI,EAAE,mBAAmB;MACzBD,KAAK,EAAE,iBAAiB;MACxBT,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACsZ,wBAAwB,EAAE;MAC9ChZ,QAAQ,EAAE,IAAI,CAACiZ,sBAAsB;MACrClZ,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DpB,KAAK,EACH,IAAI,CAACua,aAAa,CAAC3R,MAAM,GAAG,CAAC,GACzB;QACEtI,KAAK,EAAE,IAAI,CAACia,aAAa,CAAC3R,MAAM;QAChC3I,KAAK,EAAE,cAAc;QACrBI,OAAO,EAAE;OACV,GACD;KACP,CACF;EACH;EAEAma,YACUhB,cAA8B,EAC/BiB,KAAqB,EACpBC,WAA4B,EAC5BC,EAAe,EAChBC,aAAgC,EAChCC,MAAc,EACbC,YAA0B,EAE1BC,GAAsB;IARtB,KAAAvB,cAAc,GAAdA,cAAc;IACf,KAAAiB,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,YAAY,GAAZA,YAAY;IAEZ,KAAAC,GAAG,GAAHA,GAAG;IAjTb,KAAAC,QAAQ,GAAc,EAAE;IAExB,KAAAC,YAAY,GAAwB,IAAI;IACxC,KAAAC,OAAO,GAAG,IAAI;IAEd,KAAAnW,aAAa,GAAkB,IAAI;IACnC,KAAAoW,eAAe,GAAW,KAAK;IAC/B,KAAA3b,gBAAgB,GAAgB,IAAI;IACpC,KAAA0G,YAAY,GAAgB,IAAI;IAChC,KAAAF,UAAU,GAAgC,IAAI;IAC9C,KAAAoV,WAAW,GAAG,KAAK;IACnB,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAAhR,gBAAgB,GAAG,KAAK;IACxB,KAAA7C,sBAAsB,GAAG,CAAC;IAET,KAAA8T,qBAAqB,GAAG,CAAC;IACzB,KAAAC,oBAAoB,GAAG,EAAE;IACzB,KAAAC,kBAAkB,GAAG,GAAG;IACjC,KAAAC,WAAW,GAAG,CAAC;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACd,KAAAC,aAAa,GAAG,IAAIjd,YAAY,EAAE;IAE1C;IACA,KAAAkd,aAAa,GAAW,eAAe;IAEvC;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAApC,aAAa,GAAG,KAAK;IACrB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAoC,kBAAkB,GAAG,KAAK;IAC1B,KAAAlC,qBAAqB,GAAG,KAAK;IAC7B,KAAAmC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAjC,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,kBAAkB,GAAG,KAAK;IAC1B,KAAAE,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAArJ,YAAY,GAAQ,IAAI;IACxB,KAAA8B,UAAU,GAAQ,IAAI;IACtB,KAAAqJ,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAA/I,WAAW,GAAG,KAAK;IACnB,KAAA3B,cAAc,GAAG,IAAI;IACrB,KAAAuB,YAAY,GAAG,CAAC;IAChB,KAAAoJ,SAAS,GAAQ,IAAI;IAErB;IACA,KAAAvE,aAAa,GAAU,EAAE;IACzB,KAAAiC,uBAAuB,GAAW,CAAC;IACnC,KAAAuC,qBAAqB,GAAgB,IAAIC,GAAG,EAAE;IAC9C,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAvX,iBAAiB,GAAQ,IAAI;IAE7B;IACA,KAAAqI,WAAW,GAAG,EAAE;IAChB,KAAAuB,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAApB,aAAa,GAAU,EAAE;IAEzB;IACA,KAAAmC,cAAc,GAAU,EAAE;IAC1B,KAAA4M,kBAAkB,GAA+B,EAAE;IACnD,KAAAC,iBAAiB,GAA+B,EAAE;IAClD,KAAAC,cAAc,GAA+B,EAAE;IAC/C,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,qBAAqB,GAAa,EAAE;IACpC,KAAAC,sBAAsB,GAAU,EAAE;IAClC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,sBAAsB,GAAG,KAAK;IAE9B;IACS,KAAAjE,CAAC,GAAG;MACXC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC/CiE,MAAM,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAG,CAAE;MAC9DC,KAAK,EAAE,6BAA6B;MACpCC,SAAS,EAAEA,CAACC,CAAS,EAAEC,IAAS,KAAaA,IAAI,EAAElZ,EAAE,IAAIiZ,CAAC,CAACE,QAAQ,EAAE;MACrEjG,aAAa,EAAE;QACbkG,WAAW,EAAE;UAAExc,IAAI,EAAE,gBAAgB;UAAEa,KAAK,EAAE;QAAe,CAAE;QAC/D4b,WAAW,EAAE;UAAEzc,IAAI,EAAE,oBAAoB;UAAEa,KAAK,EAAE;QAAc,CAAE;QAClE6b,MAAM,EAAE;UAAE1c,IAAI,EAAE,YAAY;UAAEa,KAAK,EAAE;QAAe;OACrD;MACD8b,MAAM,EAAE;QACNC,MAAM,EAAE;UACNC,IAAI,EAAE,UAAU;UAChBhc,KAAK,EAAE,gBAAgB;UACvBb,IAAI,EAAE,eAAe;UACrBc,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAE;SACd;QACD+b,OAAO,EAAE;UACPD,IAAI,EAAE,YAAY;UAClBhc,KAAK,EAAE,eAAe;UACtBb,IAAI,EAAE,eAAe;UACrBc,KAAK,EAAE,YAAY;UACnBC,WAAW,EAAE;SACd;QACDgb,IAAI,EAAE;UACJc,IAAI,EAAE,QAAQ;UACdhc,KAAK,EAAE,iBAAiB;UACxBb,IAAI,EAAE,cAAc;UACpBc,KAAK,EAAE,QAAQ;UACfC,WAAW,EAAE;SACd;QACDgc,IAAI,EAAE;UACJF,IAAI,EAAE,QAAQ;UACdhc,KAAK,EAAE,cAAc;UACrBb,IAAI,EAAE,qBAAqB;UAC3Bc,KAAK,EAAE,QAAQ;UACfC,WAAW,EAAE;;OAEhB;MACDic,MAAM,EAAE,CACN;QACEzc,GAAG,EAAE,eAAe;QACpBO,KAAK,EAAE,QAAQ;QACfD,KAAK,EAAE,SAAS;QAChBkB,UAAU,EAAE;OACb,EACD;QACExB,GAAG,EAAE,gBAAgB;QACrBO,KAAK,EAAE,MAAM;QACbD,KAAK,EAAE,SAAS;QAChBkB,UAAU,EAAE;OACb,EACD;QACExB,GAAG,EAAE,iBAAiB;QACtBO,KAAK,EAAE,MAAM;QACbD,KAAK,EAAE,SAAS;QAChBkB,UAAU,EAAE;OACb,EACD;QACExB,GAAG,EAAE,eAAe;QACpBO,KAAK,EAAE,MAAM;QACbD,KAAK,EAAE,SAAS;QAChBkB,UAAU,EAAE;OACb,CACF;MACDkb,KAAK,EAAE;QACLC,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;OACX;MACD;MACAC,kBAAkB,EAAE;QAClBb,WAAW,EAAE;UAAExc,IAAI,EAAE,gBAAgB;UAAEa,KAAK,EAAE;QAAe,CAAE;QAC/D4b,WAAW,EAAE;UAAEzc,IAAI,EAAE,oBAAoB;UAAEa,KAAK,EAAE;QAAc,CAAE;QAClE6b,MAAM,EAAE;UAAE1c,IAAI,EAAE,YAAY;UAAEa,KAAK,EAAE;QAAe;OACrD;MACDyc,gBAAgB,EAAE;QAChBJ,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;;KAEb;IAOD;IACA,KAAAG,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,uBAAuB,GAAG,KAAK;IAC/B,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,cAAc,GAAG,IAAI;IAErB;IACA,KAAAzM,eAAe,GAAG,KAAK;IACvB,KAAA0M,WAAW,GAAG,YAAY;IAC1B,KAAAnM,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAoM,WAAW,GAAsB,IAAIC,GAAG,EAAE;IAC1C,KAAArd,iBAAiB,GAAW,QAAQ;IACpC,KAAAsd,gBAAgB,GAAS,IAAIC,IAAI,EAAE;IACnC,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAA1d,gBAAgB,GAAG,KAAK;IACxB,KAAA2d,WAAW,GAAU,EAAE;IACvB,KAAAvF,aAAa,GAAU,EAAE;IACzB,KAAAwF,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,gBAAgB,GAAG,KAAK;IAuLxB;IACS,KAAAC,CAAC,GAAG;MACXC,OAAO,EAAGC,CAAU,IAAK,IAAI,CAAC5G,cAAc,CAAC6G,WAAW,CAACD,CAAC,CAAC;MAC3DE,OAAO,EAAGF,CAAU,IAAK,IAAI,CAAC5G,cAAc,CAAC+G,WAAW,CAACH,CAAC;KAC3D;IAED,KAAAC,WAAW,GAAG,IAAI,CAACH,CAAC,CAACC,OAAO;IAC5B,KAAAI,WAAW,GAAG,IAAI,CAACL,CAAC,CAACI,OAAO;IAmLpB,KAAAE,iBAAiB,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,GAAG;IAClB,KAAAC,cAAc,GAAG,IAAI;IAuDtC;IACS,KAAAC,iBAAiB,GAAG;MAC3BC,aAAa,EAAEA,CAAA,KAAM,IAAI,CAACC,WAAW,CAAC,OAAO,CAAC;MAC9CC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACD,WAAW,CAAC,MAAM,CAAC;MACxCE,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACF,WAAW,CAAC,OAAO;KAC5C;IAED,KAAAG,mBAAmB,GAAG,IAAI,CAACL,iBAAiB,CAACC,aAAa;IAC1D,KAAAK,cAAc,GAAG,IAAI,CAACN,iBAAiB,CAACG,QAAQ;IAChD,KAAA/U,iBAAiB,GAAG,IAAI,CAAC4U,iBAAiB,CAACI,WAAW;IAQtD;IACiB,KAAAG,aAAa,GAAG;MAC/BlR,cAAc,EAAEA,CAAA,KAAO,IAAI,CAAC6J,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAmB;MAC1EsH,SAAS,EAAEA,CAAA,KAAK;QACd,IAAI,CAACN,WAAW,CAAC,QAAQ,CAAC;QAC1B,IAAI,CAAC,IAAI,CAACjH,aAAa,EAAE,IAAI,CAAC3M,WAAW,EAAE;MAC7C,CAAC;MACDmU,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACP,WAAW,CAAC,QAAQ,CAAC;MAChDQ,oBAAoB,EAAEA,CAAA,KACnB,IAAI,CAACnF,wBAAwB,GAAG,CAAC,IAAI,CAACA,wBAAyB;MAClEoF,eAAe,EAAEA,CAAA,KACd,IAAI,CAACnF,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAoB;MACxDoF,YAAY,EAAEA,CAAA,KAAO,IAAI,CAACzO,eAAe,GAAG,CAAC,IAAI,CAACA,eAAgB;MAClE0O,gBAAgB,EAAEA,CAAA,KACf,IAAI,CAACtH,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAqB;MAC1DuH,cAAc,EAAEA,CAAA,KAAO,IAAI,CAACrH,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAmB;MAC1EsH,kBAAkB,EAAEA,CAAA,KACjB,IAAI,CAACpH,sBAAsB,GAAG,CAAC,IAAI,CAACA;KACxC;IAED,KAAAlK,oBAAoB,GAAG,IAAI,CAAC8Q,aAAa,CAAClR,cAAc;IACxD,KAAApB,eAAe,GAAG,IAAI,CAACsS,aAAa,CAACC,SAAS;IAC9C,KAAAlf,oBAAoB,GAAG,IAAI,CAACif,aAAa,CAACE,cAAc;IACxD,KAAAO,0BAA0B,GAAG,IAAI,CAACT,aAAa,CAACG,oBAAoB;IACpE,KAAAxe,qBAAqB,GAAG,IAAI,CAACqe,aAAa,CAACI,eAAe;IAC1D,KAAAvP,kBAAkB,GAAG,IAAI,CAACmP,aAAa,CAACK,YAAY;IACpD,KAAAtH,sBAAsB,GAAG,IAAI,CAACiH,aAAa,CAACM,gBAAgB;IAC5D,KAAArH,oBAAoB,GAAG,IAAI,CAAC+G,aAAa,CAACO,cAAc;IACxD,KAAApH,wBAAwB,GAAG,IAAI,CAAC6G,aAAa,CAACQ,kBAAkB;IAQvD,KAAAE,mBAAmB,GAAG;MAC7BC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACC,sBAAsB,CAAC,8BAA8B,CAAC;MAC3EC,YAAY,EAAEA,CAAA,KACZ,IAAI,CAACD,sBAAsB,CAAC,4BAA4B,CAAC;MAC3DE,KAAK,EAAEA,CAAA,KAAW;QAChB,IAAI,CAAC,IAAI,CAAC/G,YAAY,EAAEpW,EAAE,IAAI,IAAI,CAACmW,QAAQ,CAACpS,MAAM,KAAK,CAAC,EAAE;UACxD,IAAI,CAACkS,YAAY,CAACmH,WAAW,CAAC,6BAA6B,CAAC;UAC5D;;QAGF,IACEC,OAAO,CACL,oHAAoH,CACrH,EACD;UACA,IAAI,CAAClH,QAAQ,GAAG,EAAE;UAClB,IAAI,CAACe,YAAY,GAAG,KAAK;UACzB,IAAI,CAACjB,YAAY,CAACqH,WAAW,CAAC,gCAAgC,CAAC;;MAEnE,CAAC;MACDC,MAAM,EAAEA,CAAA,KAAW;QACjB,IAAI,CAAC,IAAI,CAACnH,YAAY,EAAEpW,EAAE,IAAI,IAAI,CAACmW,QAAQ,CAACpS,MAAM,KAAK,CAAC,EAAE;UACxD,IAAI,CAACkS,YAAY,CAACmH,WAAW,CAAC,gCAAgC,CAAC;UAC/D;;QAGF,MAAMI,gBAAgB,GAAG,IAAI,CAACpH,YAAY,CAACqH,OAAO,GAC9C,IAAI,CAACrH,YAAY,CAACsH,SAAS,IAAI,iBAAiB,GAChD,IAAI,CAAC/iB,gBAAgB,EAAEC,QAAQ,IAAI,qBAAqB;QAE5D,MAAM+iB,UAAU,GAAG;UACjBvH,YAAY,EAAE;YACZpW,EAAE,EAAE,IAAI,CAACoW,YAAY,CAACpW,EAAE;YACxBsB,IAAI,EAAEkc,gBAAgB;YACtBC,OAAO,EAAE,IAAI,CAACrH,YAAY,CAACqH,OAAO;YAClCG,YAAY,EAAE,IAAI,CAACxH,YAAY,CAACwH,YAAY;YAC5CC,SAAS,EAAE,IAAI,CAACzH,YAAY,CAACyH;WAC9B;UACD1H,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC2H,GAAG,CAAEC,GAAG,KAAM;YACpC/d,EAAE,EAAE+d,GAAG,CAAC/d,EAAE;YACVG,OAAO,EAAE4d,GAAG,CAAC5d,OAAO;YACpBJ,MAAM,EAAEge,GAAG,CAAChe,MAAM;YAClBM,SAAS,EAAE0d,GAAG,CAAC1d,SAAS;YACxBiM,IAAI,EAAEyR,GAAG,CAACzR;WACX,CAAC,CAAC;UACH0R,UAAU,EAAE,IAAIjD,IAAI,EAAE,CAACkD,WAAW,EAAE;UACpCC,UAAU,EAAE,IAAI,CAAChe;SAClB;QAED,MAAMie,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAACX,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;UAC3DrR,IAAI,EAAE;SACP,CAAC;QACF,MAAMyF,GAAG,GAAGwM,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QAC5C,MAAMO,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAG9M,GAAG;QAEf,MAAM+M,YAAY,GAAGtB,gBAAgB,CAClCuB,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAC3BC,WAAW,EAAE;QAChB,MAAMC,OAAO,GAAG,IAAIlE,IAAI,EAAE,CAACkD,WAAW,EAAE,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtDR,IAAI,CAACS,QAAQ,GAAG,gBAAgBL,YAAY,IAAIG,OAAO,OAAO;QAE9DP,IAAI,CAACU,KAAK,EAAE;QACZb,MAAM,CAACC,GAAG,CAACa,eAAe,CAACtN,GAAG,CAAC;QAE/B,IAAI,CAACmF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACjB,YAAY,CAACqH,WAAW,CAAC,mCAAmC,CAAC;MACpE;KACD;IAED;IACA,KAAA9d,sBAAsB,GAAG,IAAI,CAACud,mBAAmB,CAACC,QAAQ;IAC1D,KAAArd,0BAA0B,GAAG,IAAI,CAACod,mBAAmB,CAACG,YAAY;IAClE,KAAAhe,iBAAiB,GAAG,IAAI,CAAC6d,mBAAmB,CAACI,KAAK;IAClD,KAAA9d,kBAAkB,GAAG,IAAI,CAAC0d,mBAAmB,CAACQ,MAAM;IA6GpD;IACS,KAAA+B,CAAC,GAAG;MACXC,UAAU,EAAGhE,CAA4B,IACvC,IAAI,CAAC5G,cAAc,CAACvU,iBAAiB,CAACmb,CAAC,CAAC;MAC1CiE,YAAY,EAAGjE,CAA4B,IACzC,IAAI,CAAC5G,cAAc,CAAC5Z,gBAAgB,CAACwgB,CAAC,CAAC;MACzCkE,UAAU,EAAGlE,CAA4B,IACvC,IAAI,CAAC5G,cAAc,CAAC3J,iBAAiB,CAACuQ,CAAC,CAAC;MAC1CmE,cAAc,EAAGzG,CAAS,IACxB,IAAI,CAACtE,cAAc,CAACgL,oBAAoB,CAAC,IAAI,CAACxJ,QAAQ,EAAE8C,CAAC,CAAC;MAC5DwC,OAAO,EAAGmE,CAA6B,IACrC,IAAI,CAACjL,cAAc,CAACkL,cAAc,CAACD,CAAC,CAAC;MACvC5e,QAAQ,EAAG4e,CAA6B,IACtC,IAAI,CAACjL,cAAc,CAAC3T,QAAQ,CAAC4e,CAAC,CAAC;MACjCE,OAAO,EAAGF,CAA6B,IACrC,IAAI,CAACjL,cAAc,CAAC1T,cAAc,CAAC2e,CAAC,CAAC;MACvCG,WAAW,EAAGH,CAA6B,IACzC,IAAI,CAACjL,cAAc,CAACqL,kBAAkB,CAACJ,CAAC,CAAC;MAC3CK,gBAAgB,EAAGL,CAA6B,IAC9C,IAAI,CAACjL,cAAc,CAACuL,uBAAuB,CAACN,CAAC,CAAC;MAChDO,cAAc,EAAGlH,CAAS,IAAK,IAAI,CAACtE,cAAc,CAACyL,iBAAiB,CAACnH,CAAC,CAAC;MACvEoH,WAAW,EAAGf,CAAS,IAAK,IAAI,CAAC3K,cAAc,CAAC2L,mBAAmB,CAAChB,CAAC,CAAC;MACtEiB,WAAW,EAAGX,CAA6B,IACzC,IAAI,CAACjL,cAAc,CAAC4L,WAAW,CAACX,CAAC,CAAC;MACpCY,YAAY,EAAGZ,CAA6B,IAC1C,IAAI,CAACjL,cAAc,CAAC8L,mBAAmB,CAACb,CAAC,EAAE,IAAI,CAAC1f,aAAa;KAChE;IAED;IACA,KAAAE,iBAAiB,GAAG,IAAI,CAACkf,CAAC,CAACC,UAAU;IACrC,KAAAxkB,gBAAgB,GAAG,IAAI,CAACukB,CAAC,CAACE,YAAY;IACtC,KAAAxU,iBAAiB,GAAG,IAAI,CAACsU,CAAC,CAACG,UAAU;IACrC,KAAAE,oBAAoB,GAAG,IAAI,CAACL,CAAC,CAACI,cAAc;IAC5C,KAAAG,cAAc,GAAG,IAAI,CAACP,CAAC,CAAC7D,OAAO;IAC/B,KAAAza,QAAQ,GAAG,IAAI,CAACse,CAAC,CAACte,QAAQ;IAC1B,KAAAC,cAAc,GAAG,IAAI,CAACqe,CAAC,CAACQ,OAAO;IAC/B,KAAAE,kBAAkB,GAAG,IAAI,CAACV,CAAC,CAACS,WAAW;IACvC,KAAAG,uBAAuB,GAAG,IAAI,CAACZ,CAAC,CAACW,gBAAgB;IACjD,KAAAG,iBAAiB,GAAG,IAAI,CAACd,CAAC,CAACa,cAAc;IACzC,KAAAG,mBAAmB,GAAG,IAAI,CAAChB,CAAC,CAACe,WAAW;IACxC,KAAAE,WAAW,GAAG,IAAI,CAACjB,CAAC,CAACiB,WAAW;IAChC,KAAAE,mBAAmB,GAAG,IAAI,CAACnB,CAAC,CAACkB,YAAY;IAkFzC;IACS,KAAAE,uBAAuB,GAAG;MACjCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChC,QAAQ,CAACiC,cAAc,CAAC,2BAA2B,CAAC,EAAE;UACzD,MAAMC,SAAS,GAAGlC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC/CiC,SAAS,CAAC7gB,EAAE,GAAG,2BAA2B;UAC1C6gB,SAAS,CAACC,SAAS,GAAG,wCAAwC;UAC9DD,SAAS,CAACE,SAAS,GACjB,uEAAuE;UACzE,IAAI,CAACC,iBAAiB,EAAEC,aAAa,EAAEC,OAAO,CAACL,SAAS,CAAC;;MAE7D,CAAC;MACDM,IAAI,EAAEA,CAAA,KAAK;QACT,MAAMN,SAAS,GAAGlC,QAAQ,CAACiC,cAAc,CAAC,2BAA2B,CAAC;QACtEC,SAAS,EAAEO,UAAU,EAAEC,WAAW,CAACR,SAAS,CAAC;MAC/C;KACD;IAEO,KAAAS,oBAAoB,GAAG,IAAI,CAACZ,uBAAuB,CAACC,IAAI;IACxD,KAAAY,oBAAoB,GAAG,IAAI,CAACb,uBAAuB,CAACS,IAAI;IAuHhE;IACS,KAAAK,qBAAqB,GAAG;MAC/BC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACjc,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;QAC9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE,IAAI,CAAC7C,sBAAsB,GAAG,CAAC;MAC7D,CAAC;MACD+e,QAAQ,EAAGC,SAAe,IAAI;QAC5B,IAAI,CAAC,IAAI,CAACvL,YAAY,EAAEpW,EAAE,IAAI,CAAC,IAAI,CAACrF,gBAAgB,EAAEqF,EAAE,EAAE;UACxD,IAAI,CAACiW,YAAY,CAAC2L,SAAS,CAAC,uCAAuC,CAAC;UACpE,IAAI,CAACpc,gBAAgB,GAAG,KAAK;UAC7B;;QAEF,MAAMqc,UAAU,GAAG,IAAI,CAAClnB,gBAAgB,EAAEqF,EAAE,IAAI,EAAE;QAClD,IAAI,CAAC2U,cAAc,CAACmN,gBAAgB,CAClCD,UAAU,EACVF,SAAS,EACT,IAAI,CAACvL,YAAY,EAAEpW,EAAE,EACrB,IAAI,CAAC2C,sBAAsB,CAC5B,CAACof,SAAS,CAAC;UACVC,IAAI,EAAGC,OAAO,IAAI;YAChB,IAAI,CAACzc,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAAC7C,sBAAsB,GAAG,CAAC;YAC/B,IAAI,CAACuf,cAAc,CAAC,IAAI,CAAC;UAC3B,CAAC;UACDnJ,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC9C,YAAY,CAAC2L,SAAS,CAAC,8BAA8B,CAAC;YAC3D,IAAI,CAACpc,gBAAgB,GAAG,KAAK;UAC/B;SACD,CAAC;MACJ,CAAC;MACD2c,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC3c,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC7C,sBAAsB,GAAG,CAAC;MACjC;KACD;IAED,KAAAyf,oBAAoB,GAAG,IAAI,CAACZ,qBAAqB,CAACC,MAAM;IACxD,KAAAY,wBAAwB,GAAG,IAAI,CAACb,qBAAqB,CAACE,QAAQ;IAC9D,KAAAY,yBAAyB,GAAG,IAAI,CAACd,qBAAqB,CAACW,MAAM;IA8I7D;IACS,KAAAI,WAAW,GAAG;MACrBC,QAAQ,EAAGlW,IAAuB,IAAI;QACpC,IAAI,CAAC,IAAI,CAAC3R,gBAAgB,EAAEqF,EAAE,EAAE;QAChC,IAAI,CAAC2U,cAAc,CAACG,YAAY,CAC9B,IAAI,CAACna,gBAAgB,CAACqF,EAAE,EACxBsM,IAAI,KAAK,OAAO,GAAGtS,QAAQ,CAACyoB,KAAK,GAAGzoB,QAAQ,CAAC0oB,KAAK,EAClD,IAAI,CAACtM,YAAY,EAAEpW,EAAE,CACtB,CAAC+hB,SAAS,CAAC;UACVC,IAAI,EAAGW,IAAI,IAAI,CAAE,CAAC;UAClB5J,KAAK,EAAEA,CAAA,KAAM,IAAI,CAAC9C,YAAY,CAAC2L,SAAS,CAAC,IAAI,CAACpN,CAAC,CAACuE,KAAK;SACtD,CAAC;MACJ,CAAC;MACD6J,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC,IAAI,CAACxW,YAAY,EAAE;QACxB,IAAI,CAACuI,cAAc,CAACzI,UAAU,CAAC,IAAI,CAACE,YAAY,CAACpM,EAAE,CAAC,CAAC+hB,SAAS,CAAC;UAC7DC,IAAI,EAAGW,IAAI,IAAI;YACb,IAAI,CAACpL,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACnL,YAAY,GAAG,IAAI;YACxB,IAAI,CAACU,cAAc,GAAG,IAAI,CAACV,YAAY,EAAEE,IAAI,KAAK,OAAO;YACzD,IAAI,CAACqO,WAAW,GAAG,YAAY;YAC/B,IAAI,CAAC1E,YAAY,CAACqH,WAAW,CAAC,gBAAgB,CAAC;UACjD,CAAC;UACDvE,KAAK,EAAEA,CAAA,KAAK;YACV,IAAI,CAAC9C,YAAY,CAAC2L,SAAS,CAAC,IAAI,CAACpN,CAAC,CAACuE,KAAK,CAAC;YACzC,IAAI,CAACxB,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACnL,YAAY,GAAG,IAAI;UAC1B;SACD,CAAC;MACJ,CAAC;MACDyW,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC,IAAI,CAACzW,YAAY,EAAE;QACxB,IAAI,CAACuI,cAAc,CAACmO,UAAU,CAAC,IAAI,CAAC1W,YAAY,CAACpM,EAAE,CAAC,CAAC+hB,SAAS,CAAC;UAC7DC,IAAI,EAAGW,IAAI,IAAI;YACb,IAAI,CAACpL,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACnL,YAAY,GAAG,IAAI;UAC1B,CAAC;UACD2M,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACxB,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACnL,YAAY,GAAG,IAAI;UAC1B;SACD,CAAC;MACJ,CAAC;MACD2W,GAAG,EAAEA,CAAA,KAAK;QACR,MAAMC,GAAG,GAAG,IAAI,CAACrO,cAAc,CAACsO,WAAW,CAAClB,SAAS,CAAEY,IAAI,IAAI;UAC7D,IAAIA,IAAI,EAAE;YACR,IAAI,CAAChO,cAAc,CAAC7G,OAAO,CAAC6U,IAAI,CAAC3iB,EAAE,CAAC,CAAC+hB,SAAS,CAAC;cAC7CC,IAAI,EAAGW,IAAI,IAAI,CAAE,CAAC;cAClB5J,KAAK,EAAGA,KAAK,IAAI,CAAE;aACpB,CAAC;;QAEN,CAAC,CAAC;QACFiK,GAAG,CAACE,WAAW,EAAE;MACnB;KACD;IAED,KAAApO,YAAY,GAAG,IAAI,CAACyN,WAAW,CAACC,QAAQ;IACxC,KAAAtW,UAAU,GAAG,IAAI,CAACqW,WAAW,CAACK,MAAM;IACpC,KAAAE,UAAU,GAAG,IAAI,CAACP,WAAW,CAACM,MAAM;IACpC,KAAA/U,OAAO,GAAG,IAAI,CAACyU,WAAW,CAACQ,GAAG;IAE9B;IACS,KAAAI,kBAAkB,GAAG;MAC5B3V,UAAU,EAAEA,CAAA,KAAK;QACf,IAAI,CAACiB,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;QACpC,IAAI,CAAC0U,kBAAkB,CAACC,WAAW,EAAE;QACrC,IAAI,CAACnN,YAAY,CAAC+G,QAAQ,CACxB,IAAI,CAACvO,WAAW,GAAG,sBAAsB,GAAG,mBAAmB,CAChE;MACH,CAAC;MACD/B,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAACI,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAACqW,kBAAkB,CAACC,WAAW,EAAE;QACrC,IAAI,CAACnN,YAAY,CAAC+G,QAAQ,CACxB,IAAI,CAAClQ,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDsW,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAACzO,cAAc,CAAC0O,WAAW,CAC7B,IAAI,CAACnV,UAAU,EAAElO,EAAE,EACnB,CAAC,IAAI,CAACyO,WAAW,EACjB,IAAI,CAAC3B,cAAc,CACpB,CAACiV,SAAS,CAAC;UACVC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;UACdjJ,KAAK,EAAGA,KAAK,IACXuK,OAAO,CAACvK,KAAK,CAAC,2CAA2C,EAAEA,KAAK;SACnE,CAAC;MACJ;KACD;IAED,KAAAwK,cAAc,GAAG,IAAI,CAACJ,kBAAkB,CAAC3V,UAAU;IACnD,KAAAgW,eAAe,GAAG,IAAI,CAACL,kBAAkB,CAACzW,WAAW;IAC7C,KAAA+W,eAAe,GAAG,IAAI,CAACN,kBAAkB,CAACC,WAAW;IAE7D;IACS,KAAAM,YAAY,GAAG;MACtBC,cAAc,EAAEA,CAAA,KAAK;QACnB,IAAI,CAACtV,YAAY,GAAG,CAAC;QACrB,IAAI,CAACoJ,SAAS,GAAGmM,WAAW,CAAC,MAAK;UAChC,IAAI,CAACvV,YAAY,EAAE;UACnB,IAAI,IAAI,CAACA,YAAY,KAAK,CAAC,IAAI,IAAI,CAACsM,WAAW,KAAK,YAAY,EAAE;YAChE,IAAI,CAACA,WAAW,GAAG,WAAW;;QAElC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDkJ,aAAa,EAAEA,CAAA,KAAK;QAClB,IAAI,IAAI,CAACpM,SAAS,EAAE;UAClBqM,aAAa,CAAC,IAAI,CAACrM,SAAS,CAAC;UAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;MAEzB,CAAC;MACDsM,cAAc,EAAEA,CAAA,KAAO,IAAI,CAAC1V,YAAY,GAAG;KAC5C;IAEO,KAAA2V,oBAAoB,GAAG,IAAI,CAACN,YAAY,CAACC,cAAc;IACvD,KAAAM,mBAAmB,GAAG,IAAI,CAACP,YAAY,CAACG,aAAa;IACrD,KAAAK,oBAAoB,GAAG,IAAI,CAACR,YAAY,CAACK,cAAc;IAE/D;IAEA;IACS,KAAAI,wBAAwB,GAAG;MAClCnI,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAACA,WAAW,CAAC,cAAc,CAAC;QAChC,IAAI,IAAI,CAAC9G,qBAAqB,EAAE;UAC9B,IAAI,CAACkP,iBAAiB,EAAE;;MAE5B;KACD;IAED,KAAAnP,uBAAuB,GAAG,IAAI,CAACkP,wBAAwB,CAACnI,WAAW;IA8BnE;IACS,KAAAqI,mBAAmB,GAAG;MAC7BC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACF,iBAAiB,EAAE;MACxCG,WAAW,EAAEA,CAAA,KACV,IAAI,CAACpP,uBAAuB,GAAG,IAAI,CAACjC,aAAa,CAAC/Y,MAAM,CACtDqqB,CAAC,IAAK,CAACA,CAAC,CAACC,MAAM,CACjB,CAAC1gB,MAAO;MACX2gB,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACxR,aAAa;MACrCyR,eAAe,EAAGC,cAAsB,IAAI;QAC1C,IAAI,IAAI,CAAClN,qBAAqB,CAACmN,GAAG,CAACD,cAAc,CAAC,EAAE;UAClD,IAAI,CAAClN,qBAAqB,CAACoN,MAAM,CAACF,cAAc,CAAC;SAClD,MAAM;UACL,IAAI,CAAClN,qBAAqB,CAACqN,GAAG,CAACH,cAAc,CAAC;;MAElD,CAAC;MACDI,eAAe,EAAEA,CAAA,KAAK;QACpB,MAAMC,qBAAqB,GAAG,IAAI,CAACZ,mBAAmB,CAACK,WAAW,EAAE;QACpE,MAAMQ,WAAW,GAAGD,qBAAqB,CAACE,KAAK,CAAEX,CAAC,IAChD,IAAI,CAAC9M,qBAAqB,CAACmN,GAAG,CAACL,CAAC,CAACxkB,EAAE,CAAC,CACrC;QACD,IAAIklB,WAAW,EAAE;UACfD,qBAAqB,CAACG,OAAO,CAAEZ,CAAC,IAC9B,IAAI,CAAC9M,qBAAqB,CAACoN,MAAM,CAACN,CAAC,CAACxkB,EAAE,CAAC,CACxC;SACF,MAAM;UACLilB,qBAAqB,CAACG,OAAO,CAAEZ,CAAC,IAC9B,IAAI,CAAC9M,qBAAqB,CAACqN,GAAG,CAACP,CAAC,CAACxkB,EAAE,CAAC,CACrC;;MAEL,CAAC;MACDqlB,cAAc,EAAEA,CAAA,KAAK;QACnB,MAAMJ,qBAAqB,GAAG,IAAI,CAACZ,mBAAmB,CAACK,WAAW,EAAE;QACpE,OACEO,qBAAqB,CAAClhB,MAAM,GAAG,CAAC,IAChCkhB,qBAAqB,CAACE,KAAK,CAAEX,CAAC,IAAK,IAAI,CAAC9M,qBAAqB,CAACmN,GAAG,CAACL,CAAC,CAACxkB,EAAE,CAAC,CAAC;MAE5E;KACD;IAED,KAAAslB,qBAAqB,GAAG,IAAI,CAACjB,mBAAmB,CAACC,QAAQ;IACjD,KAAAiB,uBAAuB,GAAG,IAAI,CAAClB,mBAAmB,CAACE,WAAW;IACtE,KAAAiB,wBAAwB,GAAG,IAAI,CAACnB,mBAAmB,CAACK,WAAW;IAC/D,KAAAe,2BAA2B,GAAG,IAAI,CAACpB,mBAAmB,CAACM,eAAe;IACtE,KAAAe,4BAA4B,GAAG,IAAI,CAACrB,mBAAmB,CAACW,eAAe;IACvE,KAAAW,2BAA2B,GAAG,IAAI,CAACtB,mBAAmB,CAACgB,cAAc;IAErE;IACS,KAAAO,cAAc,GAAG;MACxBC,YAAY,EAAEA,CAAA,KAAK;QACjB,MAAMC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtO,qBAAqB,CAAC;QAC1D,IAAIoO,WAAW,CAAC/hB,MAAM,KAAK,CAAC,EAAE;UAC5B,IAAI,CAACkS,YAAY,CAACmH,WAAW,CAAC,kCAAkC,CAAC;UACjE;;QAEF,IAAI,CAACwI,cAAc,CAACK,UAAU,CAACH,WAAW,EAAE,MAAK;UAC/C,IAAI,CAACpO,qBAAqB,CAACyF,KAAK,EAAE;UAClC,IAAI,CAAClH,YAAY,CAACqH,WAAW,CAC3B,GAAGwI,WAAW,CAAC/hB,MAAM,0CAA0C,CAChE;QACH,CAAC,CAAC;MACJ,CAAC;MACDmiB,OAAO,EAAEA,CAAA,KAAK;QACZ,MAAMC,mBAAmB,GAAG,IAAI,CAACjT,aAAa,CAAC/Y,MAAM,CAAEqqB,CAAC,IAAK,CAACA,CAAC,CAACC,MAAM,CAAC;QACvE,IAAI0B,mBAAmB,CAACpiB,MAAM,KAAK,CAAC,EAAE;UACpC,IAAI,CAACkS,YAAY,CAAC+G,QAAQ,CAAC,6BAA6B,CAAC;UACzD;;QAEF,MAAMoJ,SAAS,GAAGD,mBAAmB,CAACrI,GAAG,CAAE0G,CAAC,IAAKA,CAAC,CAACxkB,EAAE,CAAC;QACtD,IAAI,CAAC4lB,cAAc,CAACK,UAAU,CAACG,SAAS,EAAE,MAAK;UAC7C,IAAI,CAACnQ,YAAY,CAACqH,WAAW,CAC3B,sDAAsD,CACvD;QACH,CAAC,CAAC;MACJ,CAAC;MACD2I,UAAU,EAAEA,CAACI,GAAa,EAAEC,SAAqB,KAAI;QACnD,MAAMC,OAAO,GAAG,IAAI,CAAC5R,cAAc,CAACsR,UAAU,CAACI,GAAG,CAAC,CAACtE,SAAS,CAAC;UAC5DC,IAAI,EAAGwE,MAAM,IAAI;YACf,IAAI,CAACtT,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC4K,GAAG,CAAE0G,CAAC,IAC5C6B,GAAG,CAAC7W,QAAQ,CAACgV,CAAC,CAACxkB,EAAE,CAAC,GAAG;cAAE,GAAGwkB,CAAC;cAAEC,MAAM,EAAE,IAAI;cAAEgC,MAAM,EAAE,IAAI1L,IAAI;YAAE,CAAE,GAAGyJ,CAAC,CACpE;YACD,IAAI,CAACe,uBAAuB,EAAE;YAC9Be,SAAS,EAAE;UACb,CAAC;UACDvN,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC9C,YAAY,CAAC2L,SAAS,CACzB,2CAA2C,CAC5C;UACH;SACD,CAAC;QACF,IAAI,CAAC7K,aAAa,CAACgO,GAAG,CAACwB,OAAO,CAAC;MACjC;KACD;IAED,KAAAG,kBAAkB,GAAG,IAAI,CAACd,cAAc,CAACC,YAAY;IACrD,KAAAc,aAAa,GAAG,IAAI,CAACf,cAAc,CAACM,OAAO;IAE3C;IACS,KAAAU,yBAAyB,GAAG;MACnCC,8BAA8B,EAAEA,CAAA,KAAK;QACnC,IAAI,IAAI,CAACnP,qBAAqB,CAAC5V,IAAI,KAAK,CAAC,EAAE;UACzC,IAAI,CAACmU,YAAY,CAACmH,WAAW,CAAC,kCAAkC,CAAC;UACjE;;QAEF,IAAI,CAACxF,sBAAsB,GAAG,IAAI;MACpC,CAAC;MACDkP,cAAc,EAAEA,CAAA,KAAK;QACnB,MAAMhB,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtO,qBAAqB,CAAC;QAC1D,IAAIoO,WAAW,CAAC/hB,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACuW,uBAAuB,GAAG,IAAI;QACnC,IAAI,CAAC1C,sBAAsB,GAAG,KAAK;QACnC,MAAMmP,SAAS,GAAG,IAAI,CAACpS,cAAc,CAACqS,2BAA2B,CAC/DlB,WAAW,CACZ,CAAC/D,SAAS,CAAC;UACVC,IAAI,EAAGwE,MAAM,IAAI;YACf,IAAI,CAACtT,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC/Y,MAAM,CAC3CqqB,CAAC,IAAK,CAACsB,WAAW,CAACtW,QAAQ,CAACgV,CAAC,CAACxkB,EAAE,CAAC,CACnC;YACD,IAAI,CAAC0X,qBAAqB,CAACyF,KAAK,EAAE;YAClC,IAAI,CAACoI,uBAAuB,EAAE;YAC9B,IAAI,CAACjL,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACrE,YAAY,CAACqH,WAAW,CAC3B,GAAGkJ,MAAM,CAAC/qB,KAAK,+BAA+B,CAC/C;UACH,CAAC;UACDsd,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACuB,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACrE,YAAY,CAAC2L,SAAS,CACzB,iDAAiD,CAClD;UACH;SACD,CAAC;QACF,IAAI,CAAC7K,aAAa,CAACgO,GAAG,CAACgC,SAAS,CAAC;MACnC,CAAC;MACDE,SAAS,EAAGrC,cAAsB,IAAI;QACpC,MAAMmC,SAAS,GAAG,IAAI,CAACpS,cAAc,CAACuS,kBAAkB,CACtDtC,cAAc,CACf,CAAC7C,SAAS,CAAC;UACVC,IAAI,EAAGwE,MAAM,IAAI;YACf,IAAI,CAACtT,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC/Y,MAAM,CAC3CqqB,CAAC,IAAKA,CAAC,CAACxkB,EAAE,KAAK4kB,cAAc,CAC/B;YACD,IAAI,CAAClN,qBAAqB,CAACoN,MAAM,CAACF,cAAc,CAAC;YACjD,IAAI,CAACW,uBAAuB,EAAE;YAC9B,IAAI,CAACtP,YAAY,CAACqH,WAAW,CAAC,wBAAwB,CAAC;UACzD,CAAC;UACDvE,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC9C,YAAY,CAAC2L,SAAS,CACzB,kDAAkD,CACnD;UACH;SACD,CAAC;QACF,IAAI,CAAC7K,aAAa,CAACgO,GAAG,CAACgC,SAAS,CAAC;MACnC,CAAC;MACDI,SAAS,EAAEA,CAAA,KAAK;QACd,IAAI,IAAI,CAACjU,aAAa,CAACnP,MAAM,KAAK,CAAC,EAAE;QACrC,IACE,CAACsZ,OAAO,CACN,8FAA8F,CAC/F,EACD;UACA;;QAEF,IAAI,CAAC/C,uBAAuB,GAAG,IAAI;QACnC,MAAM8M,YAAY,GAChB,IAAI,CAACzS,cAAc,CAAC0S,sBAAsB,EAAE,CAACtF,SAAS,CAAC;UACrDC,IAAI,EAAGwE,MAAM,IAAI;YACf,IAAI,CAACtT,aAAa,GAAG,EAAE;YACvB,IAAI,CAACwE,qBAAqB,CAACyF,KAAK,EAAE;YAClC,IAAI,CAACoI,uBAAuB,EAAE;YAC9B,IAAI,CAACjL,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACrE,YAAY,CAACqH,WAAW,CAC3B,GAAGkJ,MAAM,CAAC/qB,KAAK,uCAAuC,CACvD;UACH,CAAC;UACDsd,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACuB,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAACrE,YAAY,CAAC2L,SAAS,CACzB,2DAA2D,CAC5D;UACH;SACD,CAAC;QACJ,IAAI,CAAC7K,aAAa,CAACgO,GAAG,CAACqC,YAAY,CAAC;MACtC,CAAC;MACDjF,MAAM,EAAEA,CAAA,KAAO,IAAI,CAACvK,sBAAsB,GAAG;KAC9C;IAED,KAAAiP,8BAA8B,GAC5B,IAAI,CAACD,yBAAyB,CAACC,8BAA8B;IAC/D,KAAAS,2BAA2B,GAAG,IAAI,CAACV,yBAAyB,CAACE,cAAc;IAC3E,KAAAI,kBAAkB,GAAG,IAAI,CAACN,yBAAyB,CAACK,SAAS;IAC7D,KAAAI,sBAAsB,GAAG,IAAI,CAACT,yBAAyB,CAACO,SAAS;IACjE,KAAAI,yBAAyB,GAAG,IAAI,CAACX,yBAAyB,CAACzE,MAAM;IAEjE;IACS,KAAAqF,uBAAuB,GAAG;MACjC/H,UAAU,EAAGgI,IAA+B,IAC1C,IAAI,CAAC9S,cAAc,CAAC5Z,gBAAgB,CAAC0sB,IAAI,CAAC;MAC5CnM,OAAO,EAAGhP,IAAY,IACpB,IAAI,CAACkI,CAAC,CAACtB,aAAa,CAAC5G,IAAyC,CAAC,EAAE1P,IAAI,IACrE,aAAa;MACf8qB,QAAQ,EAAGpb,IAAY,IACrB,IAAI,CAACkI,CAAC,CAACtB,aAAa,CAAC5G,IAAyC,CAAC,EAAE7O,KAAK,IACtE,eAAe;MACjBub,SAAS,EAAEA,CAAC2O,KAAa,EAAEC,YAAiB,KAC1C,IAAI,CAACpT,CAAC,CAACwE,SAAS,CAAC,CAAC,EAAE4O,YAAY;KACnC;IAED,KAAAC,sBAAsB,GAAG,IAAI,CAACL,uBAAuB,CAAC/H,UAAU;IAChE,KAAAqI,mBAAmB,GAAG,IAAI,CAACN,uBAAuB,CAAClM,OAAO;IAC1D,KAAAyM,oBAAoB,GAAG,IAAI,CAACP,uBAAuB,CAACE,QAAQ;IAC5D,KAAAM,qBAAqB,GAAG,IAAI,CAACR,uBAAuB,CAACxO,SAAS;IA6K9D;IACS,KAAAiP,YAAY,GAAG;MACtBC,eAAe,EAAEA,CAAA,KAAK;QACpB,MAAMC,MAAM,GAAG,EAAE;QACjB,IAAI,IAAI,CAAC7Q,mBAAmB,EAC1B6Q,MAAM,CAACC,IAAI,CAAC;UACVjrB,GAAG,EAAE,YAAY;UACjBR,KAAK,EAAE,cAAc;UACrBC,IAAI,EAAE,cAAc;UACpByrB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC/Q,mBAAmB,GAAG;SAChD,CAAC;QACJ,IAAI,IAAI,CAACjC,oBAAoB,EAC3B8S,MAAM,CAACC,IAAI,CAAC;UACVjrB,GAAG,EAAE,aAAa;UAClBR,KAAK,EAAE,uBAAuB;UAC9BC,IAAI,EAAE,gBAAgB;UACtByrB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAChT,oBAAoB,GAAG;SACjD,CAAC;QACJ,IAAI,IAAI,CAACE,kBAAkB,EACzB4S,MAAM,CAACC,IAAI,CAAC;UACVjrB,GAAG,EAAE,WAAW;UAChBR,KAAK,EAAE,uBAAuB;UAC9BC,IAAI,EAAE,kBAAkB;UACxByrB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC9S,kBAAkB,GAAG;SAC/C,CAAC;QACJ,IAAI,IAAI,CAACE,sBAAsB,EAC7B0S,MAAM,CAACC,IAAI,CAAC;UACVjrB,GAAG,EAAE,eAAe;UACpBR,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE,mBAAmB;UACzByrB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC5S,sBAAsB,GAAG;SACnD,CAAC;QACJ,OAAO0S,MAAM;MACf,CAAC;MACD/pB,gBAAgB,EAAEA,CAAA,KAChBkqB,MAAM,CAACC,OAAO,CAAC,IAAI,CAAC/T,CAAC,CAAC+E,MAAM,CAAC,CAACuE,GAAG,CAAC,CAAC,CAAC3gB,GAAG,EAAEqrB,MAAM,CAAC,MAAM;QACpDrrB,GAAG;QACH,GAAGqrB;OACJ,CAAC,CAAC;MACL1pB,eAAe,EAAEA,CAAA,KAAM,IAAI,CAAC0V,CAAC,CAACoF;KAC/B;IAED,KAAAsO,eAAe,GAAG,IAAI,CAACD,YAAY,CAACC,eAAe;IACnD,KAAA9pB,gBAAgB,GAAG,IAAI,CAAC6pB,YAAY,CAAC7pB,gBAAgB;IACrD,KAAAU,eAAe,GAAG,IAAI,CAACmpB,YAAY,CAACnpB,eAAe;IAEnD;IACS,KAAA2pB,aAAa,GAAG;MACvBC,OAAO,EAAGnP,MAAc,IACtB,IAAI,CAAC/E,CAAC,CAAC+E,MAAM,CAACA,MAAoC,CAAC,EAAEE,IAAI,IAAI,SAAS;MACxEiO,QAAQ,EAAGnO,MAAc,IACvB,IAAI,CAAC/E,CAAC,CAAC+E,MAAM,CAACA,MAAoC,CAAC,EAAE9b,KAAK,IAC1D,eAAe;MACjB6d,OAAO,EAAG/B,MAAc,IACtB,IAAI,CAAC/E,CAAC,CAAC+E,MAAM,CAACA,MAAoC,CAAC,EAAE3c,IAAI,IACzD;KACH;IAED,KAAAuB,aAAa,GAAG,IAAI,CAACsqB,aAAa,CAACC,OAAO;IAC1C,KAAAxqB,cAAc,GAAG,IAAI,CAACuqB,aAAa,CAACf,QAAQ;IAC5C,KAAAiB,aAAa,GAAG,IAAI,CAACF,aAAa,CAACnN,OAAO;IAE1C;IACS,KAAAsN,cAAc,GAAG;MACxBC,cAAc,EAAG7tB,UAAuB,IACtCA,UAAU,GACN,IAAI,CAAC2Z,cAAc,CAAC5Z,gBAAgB,CAACC,UAAU,CAAC,GAChD,WAAW;MACjBqD,mBAAmB,EAAEA,CAAA,KACnB0nB,KAAK,CAACC,IAAI,CAAC,IAAI,CAACpL,WAAW,CAACkO,MAAM,EAAE,CAAC,CAAC3uB,MAAM,CAAE4uB,IAAI,IAAKA,IAAI,CAACjuB,QAAQ,CAAC,CAClEiJ,MAAM;MACXilB,gBAAgB,EAAEA,CAAA,KAAMjD,KAAK,CAACC,IAAI,CAAC,IAAI,CAACpL,WAAW,CAACkO,MAAM,EAAE,CAAC;MAC7DG,eAAe,EAAG9uB,MAAc,IAAM,IAAI,CAACihB,gBAAgB,GAAGjhB;KAC/D;IAED,KAAA0uB,cAAc,GAAG,IAAI,CAACD,cAAc,CAACC,cAAc;IACnD,KAAAxqB,mBAAmB,GAAG,IAAI,CAACuqB,cAAc,CAACvqB,mBAAmB;IAC7D,KAAA4qB,eAAe,GAAG,IAAI,CAACL,cAAc,CAACK,eAAe;IACrD,KAAAD,gBAAgB,GAAG,IAAI,CAACJ,cAAc,CAACI,gBAAgB;IAEvD;IACS,KAAAE,mBAAmB,GAAG;MAC7BC,UAAU,EAAGlH,OAAY,IAAM,IAAI,CAAC1hB,iBAAiB,GAAG0hB,OAAQ;MAChEthB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACJ,iBAAiB,GAAG,IAAK;MAClD6oB,gBAAgB,EAAGnH,OAAY,IAAI;QACjC,IAAI,CAAC5J,iBAAiB,GAAG4J,OAAO;QAChC,IAAI,CAAC7J,gBAAgB,GAAG,IAAI;MAC9B,CAAC;MACDxI,iBAAiB,EAAEA,CAAA,KAAK;QACtB,IAAI,CAACwI,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,qBAAqB,GAAG,EAAE;MACjC;KACD;IAED,KAAA+Q,mBAAmB,GAAG,IAAI,CAACH,mBAAmB,CAACC,UAAU;IACzD,KAAAxoB,WAAW,GAAG,IAAI,CAACuoB,mBAAmB,CAACvoB,WAAW;IAClD,KAAAyoB,gBAAgB,GAAG,IAAI,CAACF,mBAAmB,CAACE,gBAAgB;IAC5D,KAAAxZ,iBAAiB,GAAG,IAAI,CAACsZ,mBAAmB,CAACtZ,iBAAiB;IAE9D;IACS,KAAA0Z,cAAc,GAAG;MACxBC,UAAU,EAAGtH,OAAY,IACvB,IAAI,CAACuH,eAAe,CAACvH,OAAO,CAAC,GAAG,kBAAkB,GAAG,kBAAkB;MACzEwH,iBAAiB,EAAGxH,OAAY,IAC9B,IAAI,CAACuH,eAAe,CAACvH,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;MAC5DyH,cAAc,EAAGzH,OAAY,IAAKA,OAAO,CAACliB,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACE,aAAa;MAC3EspB,eAAe,EAAGvH,OAAY,IAAKA,OAAO,CAAC0H,QAAQ,IAAI;KACxD;IAED,KAAAJ,UAAU,GAAG,IAAI,CAACD,cAAc,CAACC,UAAU;IAC3C,KAAAE,iBAAiB,GAAG,IAAI,CAACH,cAAc,CAACG,iBAAiB;IACzD,KAAAC,cAAc,GAAG,IAAI,CAACJ,cAAc,CAACI,cAAc;IACnD,KAAAF,eAAe,GAAG,IAAI,CAACF,cAAc,CAACE,eAAe;IAErD;IACS,KAAAI,WAAW,GAAG;MACrBC,gBAAgB,EAAG5H,OAAY,IAAI;QACjC,IAAI,CAACpK,gBAAgB,GAAGoK,OAAO,CAACjiB,EAAE;QAClC,IAAI,CAAC8X,cAAc,GAAGmK,OAAO,CAAC9hB,OAAO;MACvC,CAAC;MACD2pB,iBAAiB,EAAEA,CAAA,KAAK;QACtB,IAAI,CAACjS,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACC,cAAc,GAAG,EAAE;MAC1B,CAAC;MACDiS,eAAe,EAAGC,SAAiB,IACjC,IAAI,CAACJ,WAAW,CAACE,iBAAiB,EAAE;MACtCG,cAAc,EAAEA,CAACC,KAAoB,EAAEF,SAAiB,KAAI;QAC1D,IAAIE,KAAK,CAAC/sB,GAAG,KAAK,OAAO,IAAI,CAAC+sB,KAAK,CAACC,QAAQ,EAAE;UAC5CD,KAAK,CAACE,cAAc,EAAE;UACtB,IAAI,CAACR,WAAW,CAACG,eAAe,CAACC,SAAS,CAAC;SAC5C,MAAM,IAAIE,KAAK,CAAC/sB,GAAG,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACysB,WAAW,CAACE,iBAAiB,EAAE;;MAExC;KACD;IAED,KAAAD,gBAAgB,GAAG,IAAI,CAACD,WAAW,CAACC,gBAAgB;IACpD,KAAAC,iBAAiB,GAAG,IAAI,CAACF,WAAW,CAACE,iBAAiB;IACtD,KAAAC,eAAe,GAAG,IAAI,CAACH,WAAW,CAACG,eAAe;IAClD,KAAAE,cAAc,GAAG,IAAI,CAACL,WAAW,CAACK,cAAc;IAUhD;IACS,KAAAI,aAAa,GAAG;MACvBC,kBAAkB,EAAG/Q,MAAc,IACjC,IAAI,CAAC/E,CAAC,CAAC0F,gBAAgB,CAACX,MAA8C,CAAC,IACvE,eAAe;MACjBgR,eAAe,EAAGje,IAAY,IAC5BA,IAAI,KAAK,OAAO,GAAG,cAAc,GAAG,cAAc;MACpD8B,kBAAkB,EAAGoc,QAAgB,IAAI;QACvC,IAAI,CAACA,QAAQ,EAAE,OAAO,OAAO;QAC7B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAG,EAAE,CAAC;QACzC,MAAMI,OAAO,GAAGJ,QAAQ,GAAG,EAAE;QAC7B,OAAO,GAAGC,OAAO,CAACtR,QAAQ,EAAE,CAAC0R,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CACrDzR,QAAQ,EAAE,CACV0R,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACvB,CAAC;MACDC,cAAc,EAAGzqB,SAAoC,IACnD,IAAI,CAACsU,cAAc,CAAC3J,iBAAiB,CAAC3K,SAAS;KAClD;IAED,KAAAiqB,kBAAkB,GAAG,IAAI,CAACD,aAAa,CAACC,kBAAkB;IAC1D,KAAAC,eAAe,GAAG,IAAI,CAACF,aAAa,CAACE,eAAe;IACpD,KAAAnc,kBAAkB,GAAG,IAAI,CAACic,aAAa,CAACjc,kBAAkB;IAC1D,KAAA0c,cAAc,GAAG,IAAI,CAACT,aAAa,CAACS,cAAc;IAElD;IACS,KAAAC,YAAY,GAAG;MACtBC,eAAe,EAAGd,KAAY,IAAI;QAChC,MAAMe,MAAM,GAAGf,KAAK,CAACe,MAAqB;QAC1C,MAAMC,YAAY,GAAG,CACnB;UACEC,SAAS,EAAE,CAAC,sBAAsB,EAAE,YAAY,CAAC;UACjDC,QAAQ,EAAE;SACX,EACD;UACED,SAAS,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC;UAC1CC,QAAQ,EAAE;SACX,CACF;QAEDF,YAAY,CAAC9F,OAAO,CAAEoD,MAAM,IAAI;UAC9B,MAAM6C,cAAc,GAAG,CAAC7C,MAAM,CAAC2C,SAAS,CAACG,IAAI,CAAEC,QAAQ,IACrDN,MAAM,CAACO,OAAO,CAACD,QAAQ,CAAC,CACzB;UACD,IAAIF,cAAc,EAAE;YACjB,IAAY,CAAC7C,MAAM,CAAC4C,QAAQ,CAAC,GAAG,KAAK;;QAE1C,CAAC,CAAC;MACJ,CAAC;MACDK,oBAAoB,EAAGzB,SAAiB,IAAI;QAC1C,IAAI,CAAChS,iBAAiB,CAACgS,SAAS,CAAC,GAAG,KAAK;MAC3C;KACD;IAED,KAAAgB,eAAe,GAAG,IAAI,CAACD,YAAY,CAACC,eAAe;IACnD,KAAAS,oBAAoB,GAAG,IAAI,CAACV,YAAY,CAACU,oBAAoB;IAE7D;IACS,KAAAC,eAAe,GAAG;MACzBC,kBAAkB,EAAG1J,OAAY,IAAKA,OAAO,CAACxN,SAAS,IAAI,EAAE;MAC7DmX,eAAe,EAAEA,CAAC5B,SAAiB,EAAE6B,KAAa,KAAI;QACpD;MAAA,CACD;MACDC,cAAc,EAAEA,CAAC7J,OAAY,EAAE4J,KAAa,KAAK;KAClD;IAED,KAAAF,kBAAkB,GAAG,IAAI,CAACD,eAAe,CAACC,kBAAkB;IAC5D,KAAAC,eAAe,GAAG,IAAI,CAACF,eAAe,CAACE,eAAe;IACtD,KAAAE,cAAc,GAAG,IAAI,CAACJ,eAAe,CAACI,cAAc;IAEpD;IACS,KAAAC,4BAA4B,GAAG;MACtC1G,cAAc,EAAEA,CAAA,KACd,IAAI,CAAC/M,qBAAqB,CAACvU,MAAM,KAAK,IAAI,CAACwU,sBAAsB,CAACxU,MAAM;MAC1EioB,SAAS,EAAEA,CAAA,KACR,IAAI,CAAC1T,qBAAqB,GAAG,IAAI,CAACC,sBAAsB,CAACuF,GAAG,CAC1DtJ,CAAC,IAAKA,CAAC,CAACxU,EAAE,CACX;MACJisB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC3T,qBAAqB,GAAG,EAAG;MACpDmJ,MAAM,EAAGyK,cAAsB,IAAI;QACjC,MAAMvE,KAAK,GAAG,IAAI,CAACrP,qBAAqB,CAAC6T,OAAO,CAACD,cAAc,CAAC;QAChEvE,KAAK,GAAG,CAAC,CAAC,GACN,IAAI,CAACrP,qBAAqB,CAAC8T,MAAM,CAACzE,KAAK,EAAE,CAAC,CAAC,GAC3C,IAAI,CAACrP,qBAAqB,CAAC8P,IAAI,CAAC8D,cAAc,CAAC;MACrD,CAAC;MACDG,UAAU,EAAGH,cAAsB,IACjC,IAAI,CAAC5T,qBAAqB,CAAC9I,QAAQ,CAAC0c,cAAc,CAAC;MACrDI,eAAe,EAAGlW,YAAiB,IACjCA,YAAY,CAACvL,KAAK,IAAI,kCAAkC;MAC1D0hB,cAAc,EAAGnW,YAAiB,IAAKA,YAAY,CAAC9U,IAAI,IAAI,cAAc;MAC1EkrB,cAAc,EAAEA,CAAA,KAAK;QACnB,IAAI,CAAChU,YAAY,GAAG,IAAI;QACxBiU,UAAU,CAAC,MAAK;UACd,IAAI,CAACjU,YAAY,GAAG,KAAK;UACzB,IAAI,CAAC5I,iBAAiB,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV;KACD;IAED,KAAA8c,2BAA2B,GACzB,IAAI,CAACX,4BAA4B,CAAC1G,cAAc;IAClD,KAAAsH,sBAAsB,GAAG,IAAI,CAACZ,4BAA4B,CAACC,SAAS;IACpE,KAAAY,wBAAwB,GAAG,IAAI,CAACb,4BAA4B,CAACE,WAAW;IACxE,KAAAY,2BAA2B,GAAG,IAAI,CAACd,4BAA4B,CAACtK,MAAM;IACtE,KAAAqL,sBAAsB,GAAG,IAAI,CAACf,4BAA4B,CAACM,UAAU;IACrE,KAAAU,2BAA2B,GACzB,IAAI,CAAChB,4BAA4B,CAACO,eAAe;IACnD,KAAAU,0BAA0B,GAAG,IAAI,CAACjB,4BAA4B,CAACQ,cAAc;IAC7E,KAAAC,cAAc,GAAG,IAAI,CAACT,4BAA4B,CAACS,cAAc;IAEjE;IACS,KAAAS,yBAAyB,GAAG;MACnCC,eAAe,EAAEA,CAAA,KAAO,IAAI,CAAC1e,gBAAgB,GAAG,IAAK;MACrD2e,wBAAwB,EAAEA,CAAA,KAAK,CAAE,CAAC;MAClCC,qBAAqB,EAAGjzB,MAAc,IACnC,IAAI,CAACggB,kBAAkB,GAAGhgB;KAC9B;IAED,KAAA+yB,eAAe,GAAG,IAAI,CAACD,yBAAyB,CAACC,eAAe;IAChE,KAAAC,wBAAwB,GACtB,IAAI,CAACF,yBAAyB,CAACE,wBAAwB;IACzD,KAAAC,qBAAqB,GAAG,IAAI,CAACH,yBAAyB,CAACG,qBAAqB;IAE5E;IACS,KAAAC,aAAa,GAAG;MACvBC,OAAO,EAAGpD,KAAU,IAAI;QACtB,IAAI,CAACthB,WAAW,GAAGshB,KAAK,CAACe,MAAM,CAACnnB,KAAK;QACrC,IAAI,CAAC8E,WAAW,CAAC7E,MAAM,IAAI,CAAC,GACxB,IAAI,CAACspB,aAAa,CAACE,OAAO,EAAE,GAC5B,IAAI,CAACF,aAAa,CAAClQ,KAAK,EAAE;MAChC,CAAC;MACDqQ,UAAU,EAAGtD,KAAoB,IAAI;QACnC,IAAIA,KAAK,CAAC/sB,GAAG,KAAK,OAAO,EAAE;UACzB,IAAI,CAACkwB,aAAa,CAACE,OAAO,EAAE;SAC7B,MAAM,IAAIrD,KAAK,CAAC/sB,GAAG,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACkwB,aAAa,CAAClQ,KAAK,EAAE;;MAE9B,CAAC;MACDoQ,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACpjB,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,UAAU,GAAG,IAAI;QACtBqiB,UAAU,CAAC,MAAK;UACd,IAAI,CAACzjB,aAAa,GAAG,IAAI,CAACmN,QAAQ,CAAChc,MAAM,CAAEylB,CAAC,IAC1CA,CAAC,CAACzf,OAAO,EAAE6e,WAAW,EAAE,CAACxP,QAAQ,CAAC,IAAI,CAAC5G,WAAW,CAACoW,WAAW,EAAE,CAAC,CAClE;UACD,IAAI,CAAC7U,WAAW,GAAG,KAAK;QAC1B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDgT,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACvU,WAAW,GAAG,EAAE;QACrB,IAAI,CAACI,aAAa,GAAG,EAAE;QACvB,IAAI,CAACmB,WAAW,GAAG,KAAK;QACxB,IAAI,CAACC,UAAU,GAAG,KAAK;MACzB;KACD;IAED,KAAAb,aAAa,GAAG,IAAI,CAAC8jB,aAAa,CAACC,OAAO;IAC1C,KAAA5jB,gBAAgB,GAAG,IAAI,CAAC2jB,aAAa,CAACG,UAAU;IAChD,KAAAC,aAAa,GAAG,IAAI,CAACJ,aAAa,CAACE,OAAO;IAC1C,KAAAnlB,WAAW,GAAG,IAAI,CAACilB,aAAa,CAAClQ,KAAK;IAEtC;IACS,KAAAuQ,mBAAmB,GAAG;MAC7BjlB,iBAAiB,EAAGuhB,SAAiB,IAAI;QACvC;MAAA,CACD;MACDvf,qBAAqB,EAAGuf,SAAiB,IAAI;QAC3C;MAAA,CACD;MACDre,sBAAsB,EAAEA,CAAA,KAAM,IAAI,CAACR,cAAc,CAACpH;KACnD;IAED,KAAA0E,iBAAiB,GAAG,IAAI,CAACilB,mBAAmB,CAACjlB,iBAAiB;IAC9D,KAAAgC,qBAAqB,GAAG,IAAI,CAACijB,mBAAmB,CAACjjB,qBAAqB;IACtE,KAAAkB,sBAAsB,GAAG,IAAI,CAAC+hB,mBAAmB,CAAC/hB,sBAAsB;IAExE;IACS,KAAAgiB,wBAAwB,GAAG;MAClChlB,oBAAoB,EAAEA,CAACxI,OAAe,EAAEytB,KAAa,KAAI;QACvD,IAAI,CAACA,KAAK,EAAE,OAAOztB,OAAO;QAC1B,MAAM0tB,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIF,KAAK,GAAG,EAAE,IAAI,CAAC;QAC5C,OAAOztB,OAAO,CAAC4e,OAAO,CAAC8O,KAAK,EAAE,iBAAiB,CAAC;MAClD,CAAC;MACDE,oBAAoB,EAAG/D,SAAiB,IACrC,IAAI,CAACjS,kBAAkB,CAACiS,SAAS,CAAC,GACjC,CAAC,IAAI,CAACjS,kBAAkB,CAACiS,SAAS,CAAE;MACxCgE,cAAc,EAAEA,CAAChE,SAAiB,EAAE6B,KAAa,KAC9C,IAAI,CAAC9T,kBAAkB,CAACiS,SAAS,CAAC,GAAG,KAAM;MAC9CiE,oBAAoB,EAAGjE,SAAiB,IACrC,IAAI,CAAC7R,kBAAkB,CAAC6R,SAAS,CAAC,GACjC,CAAC,IAAI,CAAC7R,kBAAkB,CAAC6R,SAAS;KACvC;IAED,KAAArhB,oBAAoB,GAAG,IAAI,CAACglB,wBAAwB,CAAChlB,oBAAoB;IACzE,KAAAolB,oBAAoB,GAAG,IAAI,CAACJ,wBAAwB,CAACI,oBAAoB;IACzE,KAAAC,cAAc,GAAG,IAAI,CAACL,wBAAwB,CAACK,cAAc;IAC7D,KAAAC,oBAAoB,GAAG,IAAI,CAACN,wBAAwB,CAACM,oBAAoB;IAEzE;IACS,KAAAC,mBAAmB,GAAG;MAC7BC,mBAAmB,EAAGnE,SAAiB,IACpC,IAAI,CAAC/R,cAAc,CAAC+R,SAAS,CAAC,GAAG,IAAK;MACzCoE,qBAAqB,EAAGpE,SAAiB,IACtC,IAAI,CAAC/R,cAAc,CAAC+R,SAAS,CAAC,GAAG,KAAM;MAC1CqE,sBAAsB,EAAGrE,SAAiB,IACvC,IAAI,CAAChS,iBAAiB,CAACgS,SAAS,CAAC,GAAG,IAAK;MAC5CsE,mBAAmB,EAAGtE,SAAiB,IACpC,IAAI,CAAChS,iBAAiB,CAACgS,SAAS,CAAC,GAAG;KACxC;IAED,KAAAmE,mBAAmB,GAAG,IAAI,CAACD,mBAAmB,CAACC,mBAAmB;IAClE,KAAAC,qBAAqB,GAAG,IAAI,CAACF,mBAAmB,CAACE,qBAAqB;IACtE,KAAAC,sBAAsB,GAAG,IAAI,CAACH,mBAAmB,CAACG,sBAAsB;IACxE,KAAAC,mBAAmB,GAAG,IAAI,CAACJ,mBAAmB,CAACI,mBAAmB;IAElE;IACS,KAAAC,OAAO,GAAG;MACjBC,aAAa,EAAEA,CAAA,KACb,CAAC,IAAI,CAACC,aAAa,EAAE,IAAI,CAACzT,eAAe,EAAE,IAAI,CAACvD,SAAS,CAAC,CAAC2N,OAAO,CAC/D7J,CAAC,IAAKA,CAAC,IAAImT,YAAY,CAACnT,CAAC,CAAC,CAC5B;MACHoT,cAAc,EAAEA,CAAA,KACd,IAAI,CAACzuB,aAAa,IAClB,IAAI,CAACyU,cAAc,CAACga,cAAc,CAAC,IAAI,CAACzuB,aAAa,CAAC,CAAC6hB,SAAS,EAAE;MACpE6M,mBAAmB,EAAEA,CAAA,KAAK;QACxB,IAAI,IAAI,CAACjT,iBAAiB,IAAI,IAAI,CAACvF,YAAY,EAAEpW,EAAE,EAAE;UACnD,IAAI,CAAC2b,iBAAiB,GAAG,KAAK;UAC9B+S,YAAY,CAAC,IAAI,CAACG,WAAW,CAAC;UAC9B,IAAI,CAACla,cAAc,CAACma,UAAU,CAAC,IAAI,CAAC1Y,YAAY,CAACpW,EAAE,CAAC,CAAC+hB,SAAS,CAAC;YAC7DC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;YACdjJ,KAAK,EAAGA,KAAK,IAAI,CAAE;WACpB,CAAC;;MAEN;KACD;IA/3DC,IAAI,CAACnV,WAAW,GAAG,IAAI,CAACkS,EAAE,CAACiZ,KAAK,CAAC;MAC/B5uB,OAAO,EAAE,CAAC,EAAE,EAAE,CAACtG,UAAU,CAACm1B,SAAS,CAAC,IAAI,CAAC,CAAC;KAC3C,CAAC;EACJ;EACAC,QAAQA,CAAA;IACN,IAAI,CAAC/uB,aAAa,GAAG,IAAI,CAAC2V,WAAW,CAACqZ,gBAAgB,EAAE;IAExD,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,IAAIF,UAAU,EAAE;MACd,IAAI,CAACnY,aAAa,GAAGmY,UAAU;;IAGjC,IAAI,CAACG,wBAAwB,EAAE;IAC/B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,qBAAqB,EAAE;IAE5B9Q,QAAQ,CAAC+Q,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC1E,eAAe,CAAC2E,IAAI,CAAC,IAAI,CAAC,CAAC;IAEnE,MAAMC,QAAQ,GAAG,IAAI,CAACha,KAAK,CAACia,MAAM,CAC/BC,IAAI,CACH31B,MAAM,CAAE01B,MAAM,IAAKA,MAAM,CAAC,IAAI,CAAC,CAAC,EAChC31B,oBAAoB,EAAE,EACtBD,SAAS,CAAE41B,MAAM,IAAI;MACnB,IAAI,CAACxZ,OAAO,GAAG,IAAI;MACnB,IAAI,CAACF,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACS,WAAW,GAAG,CAAC;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI;MAE3B,OAAO,IAAI,CAACnC,cAAc,CAACob,eAAe,CACxCF,MAAM,CAAC,IAAI,CAAC,EACZ,IAAI,CAACnZ,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB;IACH,CAAC,CAAC,CACH,CACAmL,SAAS,CAAC;MACTC,IAAI,EAAG5L,YAAY,IAAI;QACrB,IAAI,CAAC4Z,wBAAwB,CAAC5Z,YAAY,CAAC;MAC7C,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACkX,WAAW,CAAC,6BAA6B,EAAElX,KAAK,CAAC;MACxD;KACD,CAAC;IACJ,IAAI,CAAChC,aAAa,CAACgO,GAAG,CAAC6K,QAAQ,CAAC;EAClC;EAEA;EACQK,WAAWA,CACjBhO,OAAe,EACflJ,KAAU,EACVmX,YAAA,GAAwB,IAAI;IAE5B5M,OAAO,CAACvK,KAAK,CAAC,aAAa,EAAEkJ,OAAO,EAAElJ,KAAK,CAAC;IAC5C,IAAImX,YAAY,EAAE;MAChB,IAAI,CAAC7Z,OAAO,GAAG,KAAK;MACpB,IAAI,CAACE,WAAW,GAAG,KAAK;MACxB,IAAI,CAACM,aAAa,GAAG,KAAK;;IAE5B,IAAI,CAACkC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC9C,YAAY,CAAC2L,SAAS,CAACK,OAAO,CAAC;EACtC;EAEA;EACQkO,aAAaA,CAAClO,OAAgB,EAAEmO,QAAqB;IAC3D,IAAInO,OAAO,EAAE;MACX,IAAI,CAAChM,YAAY,CAACqH,WAAW,CAAC2E,OAAO,CAAC;;IAExC,IAAImO,QAAQ,EAAE;MACZA,QAAQ,EAAE;;EAEd;EAWQJ,wBAAwBA,CAAC5Z,YAA0B;IACzD,IAAI,CAACA,YAAY,GAAGA,YAAY;IAEhC,IAAI,CAACA,YAAY,EAAED,QAAQ,IAAIC,YAAY,CAACD,QAAQ,CAACpS,MAAM,KAAK,CAAC,EAAE;MACjE,IAAI,CAACpJ,gBAAgB,GACnByb,YAAY,EAAEwH,YAAY,EAAEyS,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAACtwB,EAAE,KAAK,IAAI,CAACE,aAAa,IAAIowB,CAAC,CAACC,GAAG,KAAK,IAAI,CAACrwB,aAAa,CACnE,IAAI,IAAI;MACX,IAAI,CAACiW,QAAQ,GAAG,EAAE;KACnB,MAAM;MACL,MAAMqa,oBAAoB,GAAG,CAAC,IAAIpa,YAAY,EAAED,QAAQ,IAAI,EAAE,CAAC,CAAC;MAEhEqa,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACjC,MAAMC,KAAK,GACTF,CAAC,CAACrwB,SAAS,YAAY0a,IAAI,GACvB2V,CAAC,CAACrwB,SAAS,CAACwwB,OAAO,EAAE,GACrB,IAAI9V,IAAI,CAAC2V,CAAC,CAACrwB,SAAmB,CAAC,CAACwwB,OAAO,EAAE;QAC/C,MAAMC,KAAK,GACTH,CAAC,CAACtwB,SAAS,YAAY0a,IAAI,GACvB4V,CAAC,CAACtwB,SAAS,CAACwwB,OAAO,EAAE,GACrB,IAAI9V,IAAI,CAAC4V,CAAC,CAACtwB,SAAmB,CAAC,CAACwwB,OAAO,EAAE;QAC/C,OAAOD,KAAK,GAAGE,KAAK;MACtB,CAAC,CAAC;MAEF,IAAI,CAAC3a,QAAQ,GAAGqa,oBAAoB;;IAGtC,IAAI,CAAC71B,gBAAgB,GACnByb,YAAY,EAAEwH,YAAY,EAAEyS,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAACtwB,EAAE,KAAK,IAAI,CAACE,aAAa,IAAIowB,CAAC,CAACC,GAAG,KAAK,IAAI,CAACrwB,aAAa,CACnE,IAAI,IAAI;IAEX,IAAI,CAACmW,OAAO,GAAG,KAAK;IACpBoW,UAAU,CAAC,MAAM,IAAI,CAACvK,cAAc,EAAE,EAAE,GAAG,CAAC;IAE5C,IAAI,CAAC6O,kBAAkB,EAAE;IAEzB,IAAI,IAAI,CAAC3a,YAAY,EAAEpW,EAAE,EAAE;MACzB,IAAI,CAACgxB,8BAA8B,CAAC,IAAI,CAAC5a,YAAY,CAACpW,EAAE,CAAC;MACzD,IAAI,CAACixB,sBAAsB,CAAC,IAAI,CAAC7a,YAAY,CAACpW,EAAE,CAAC;MACjD,IAAI,CAACkxB,2BAA2B,CAAC,IAAI,CAAC9a,YAAY,CAACpW,EAAE,CAAC;;EAE1D;EAEQgxB,8BAA8BA,CAAC9E,cAAsB;IAC3D,MAAMlJ,GAAG,GAAG,IAAI,CAACrO,cAAc,CAACqc,8BAA8B,CAC5D9E,cAAc,CACf,CAACnK,SAAS,CAAC;MACVC,IAAI,EAAGmP,mBAAmB,IAAI;QAC5B,IAAI,CAAC/a,YAAY,GAAG+a,mBAAmB;QACvC,IAAI,CAAChb,QAAQ,GAAGgb,mBAAmB,CAAChb,QAAQ,GACxC,CAAC,GAAGgb,mBAAmB,CAAChb,QAAQ,CAAC,GACjC,EAAE;QACN,IAAI,CAAC+L,cAAc,EAAE;MACvB,CAAC;MACDnJ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,YAAY,CAAC2L,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;IACF,IAAI,CAAC7K,aAAa,CAACgO,GAAG,CAAC/B,GAAG,CAAC;EAC7B;EAEQiO,sBAAsBA,CAAC/E,cAAsB;IACnD,MAAMlJ,GAAG,GAAG,IAAI,CAACrO,cAAc,CAACsc,sBAAsB,CACpD/E,cAAc,CACf,CAACnK,SAAS,CAAC;MACVC,IAAI,EAAGoP,UAAU,IAAI;QACnB,IAAIA,UAAU,EAAElF,cAAc,KAAK,IAAI,CAAC9V,YAAY,EAAEpW,EAAE,EAAE;UACxD,IAAI,CAACmW,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAEib,UAAU,CAAC,CAACX,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YAC3D,MAAMC,KAAK,GACTF,CAAC,CAACrwB,SAAS,YAAY0a,IAAI,GACvB2V,CAAC,CAACrwB,SAAS,CAACwwB,OAAO,EAAE,GACrB,IAAI9V,IAAI,CAAC2V,CAAC,CAACrwB,SAAmB,CAAC,CAACwwB,OAAO,EAAE;YAC/C,MAAMC,KAAK,GACTH,CAAC,CAACtwB,SAAS,YAAY0a,IAAI,GACvB4V,CAAC,CAACtwB,SAAS,CAACwwB,OAAO,EAAE,GACrB,IAAI9V,IAAI,CAAC4V,CAAC,CAACtwB,SAAmB,CAAC,CAACwwB,OAAO,EAAE;YAC/C,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEFrE,UAAU,CAAC,MAAM,IAAI,CAACvK,cAAc,EAAE,EAAE,GAAG,CAAC;UAE5C,IACEkP,UAAU,CAACrxB,MAAM,EAAEC,EAAE,KAAK,IAAI,CAACE,aAAa,IAC5CkxB,UAAU,CAACrxB,MAAM,EAAEwwB,GAAG,KAAK,IAAI,CAACrwB,aAAa,EAC7C;YACA,IAAIkxB,UAAU,CAACpxB,EAAE,EAAE;cACjB,IAAI,CAAC2U,cAAc,CAAC0c,iBAAiB,CAACD,UAAU,CAACpxB,EAAE,CAAC,CAAC+hB,SAAS,EAAE;;;;MAIxE,CAAC;MACDhJ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,YAAY,CAAC2L,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;IACF,IAAI,CAAC7K,aAAa,CAACgO,GAAG,CAAC/B,GAAG,CAAC;EAC7B;EAEQkO,2BAA2BA,CAAChF,cAAsB;IACxD,MAAMlJ,GAAG,GAAG,IAAI,CAACrO,cAAc,CAAC2c,0BAA0B,CACxDpF,cAAc,CACf,CAACnK,SAAS,CAAC;MACVC,IAAI,EAAGkI,KAAK,IAAI;QACd,IAAIA,KAAK,CAACqH,MAAM,KAAK,IAAI,CAACrxB,aAAa,EAAE;UACvC,IAAI,CAACsW,QAAQ,GAAG0T,KAAK,CAAC1T,QAAQ;UAC9B,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjBkY,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;YAChC,IAAI,CAACA,aAAa,GAAGhC,UAAU,CAAC,MAAK;cACnC,IAAI,CAACjW,QAAQ,GAAG,KAAK;YACvB,CAAC,EAAE,IAAI,CAAC;;;MAGd;KACD,CAAC;IACF,IAAI,CAACO,aAAa,CAACgO,GAAG,CAAC/B,GAAG,CAAC;EAC7B;EAEQ+N,kBAAkBA,CAAA;IACxB,MAAMS,cAAc,GAAG,IAAI,CAACrb,QAAQ,CAAChc,MAAM,CACxC4jB,GAAG,IACF,CAACA,GAAG,CAAC0G,MAAM,KACV1G,GAAG,CAAC0T,QAAQ,EAAEzxB,EAAE,KAAK,IAAI,CAACE,aAAa,IACtC6d,GAAG,CAAC0T,QAAQ,EAAElB,GAAG,KAAK,IAAI,CAACrwB,aAAa,CAAC,CAC9C;IAEDsxB,cAAc,CAACpM,OAAO,CAAErH,GAAG,IAAI;MAC7B,IAAIA,GAAG,CAAC/d,EAAE,EAAE;QACV,MAAMgjB,GAAG,GAAG,IAAI,CAACrO,cAAc,CAAC0c,iBAAiB,CAACtT,GAAG,CAAC/d,EAAE,CAAC,CAAC+hB,SAAS,CAAC;UAClEhJ,KAAK,EAAGA,KAAK,IAAI,CAAE;SACpB,CAAC;QACF,IAAI,CAAChC,aAAa,CAACgO,GAAG,CAAC/B,GAAG,CAAC;;IAE/B,CAAC,CAAC;EACJ;EAEA0O,cAAcA,CAACxH,KAAU;IACvB,MAAMyH,IAAI,GAAGzH,KAAK,CAACe,MAAM,CAAC2G,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACD,IAAI,EAAE;IAEX,IAAIA,IAAI,CAAC7vB,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/B,IAAI,CAACmU,YAAY,CAAC2L,SAAS,CAAC,mCAAmC,CAAC;MAChE;;IAGF,MAAMiQ,UAAU,GAAG,CACjB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,oBAAoB,EACpB,yEAAyE,CAC1E;IACD,IAAI,CAACA,UAAU,CAACriB,QAAQ,CAACmiB,IAAI,CAACrlB,IAAI,CAAC,EAAE;MACnC,IAAI,CAAC2J,YAAY,CAAC2L,SAAS,CACzB,gEAAgE,CACjE;MACD;;IAGF,IAAI,CAACvgB,YAAY,GAAGswB,IAAI;IACxB,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,IAAI,CAAC7wB,UAAU,GAAG2wB,MAAM,CAACtL,MAAM;IACjC,CAAC;IACDsL,MAAM,CAACG,aAAa,CAACN,IAAI,CAAC;EAC5B;EAEAO,gBAAgBA,CAAA;IACd,IAAI,CAAC7wB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACF,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAACgxB,SAAS,EAAElR,aAAa,EAAE;MACjC,IAAI,CAACkR,SAAS,CAAClR,aAAa,CAACnd,KAAK,GAAG,EAAE;;EAE3C;EAOA;EACAsuB,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAChc,YAAY,EAAEpW,EAAE,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;MACjD;;IAGF,MAAMgsB,cAAc,GAAG,IAAI,CAAC9V,YAAY,CAACpW,EAAE;IAC3C0uB,YAAY,CAAC,IAAI,CAACG,WAAW,CAAC;IAE9B,IAAI,CAAC,IAAI,CAAClT,iBAAiB,EAAE;MAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAAChH,cAAc,CAAC0d,WAAW,CAACnG,cAAc,CAAC,CAACnK,SAAS,CAAC;QACxDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;QACdjJ,KAAK,EAAGA,KAAK,IAAI,CAAE;OACpB,CAAC;;IAGJ,IAAI,CAAC8V,WAAW,GAAGpC,UAAU,CAAC,MAAK;MACjC,IAAI,IAAI,CAAC9Q,iBAAiB,EAAE;QAC1B,IAAI,CAACA,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAChH,cAAc,CAACma,UAAU,CAAC5C,cAAc,CAAC,CAACnK,SAAS,CAAC;UACvDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;UACdjJ,KAAK,EAAGA,KAAK,IAAI,CAAE;SACpB,CAAC;;IAEN,CAAC,EAAE,IAAI,CAAC8C,cAAc,CAAC;EACzB;EAEA;EACQG,WAAWA,CAACsW,SAAiB,EAAEC,WAAA,GAAuB,IAAI;IAChE,MAAMpK,MAAM,GAAG;MACbqK,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,cAAc;MACpB5G,KAAK,EAAE,iBAAiB;MACxBjE,YAAY,EAAE,uBAAuB;MACrC8K,MAAM,EAAE,eAAe;MACvBnZ,MAAM,EAAE;KACT;IAED,MAAMoZ,YAAY,GAAGxK,MAAM,CAACmK,SAAgC,CAAC;IAC7D,IAAIK,YAAY,EAAE;MACf,IAAY,CAACA,YAAY,CAAC,GAAG,CAAE,IAAY,CAACA,YAAY,CAAC;MAE1D,IAAIJ,WAAW,IAAK,IAAY,CAACI,YAAY,CAAC,EAAE;QAC9CrK,MAAM,CAACQ,MAAM,CAACX,MAAM,CAAC,CAAC/C,OAAO,CAAEwN,KAAK,IAAI;UACtC,IAAIA,KAAK,KAAKD,YAAY,EAAE;YACzB,IAAY,CAACC,KAAK,CAAC,GAAG,KAAK;;QAEhC,CAAC,CAAC;;;EAGR;EAaAl0B,WAAWA,CAAC8zB,KAAa;IACvB,IAAI,CAACxb,aAAa,GAAGwb,KAAK;IAC1B,IAAI,CAACvb,iBAAiB,GAAG,KAAK;IAC9BmY,YAAY,CAACyD,OAAO,CAAC,YAAY,EAAEL,KAAK,CAAC;EAC3C;EAgCA;EACQvV,sBAAsBA,CAAC6V,WAAmB;IAChD,IAAI,CAAC5b,YAAY,GAAG,KAAK;IACzB,IAAI,CAACjB,YAAY,CAAC+G,QAAQ,CAAC,GAAG8V,WAAW,4BAA4B,CAAC;EACxE;EA8EA3uB,WAAWA,CAAA;IACT,IACG,IAAI,CAACP,WAAW,CAACmvB,OAAO,IAAI,CAAC,IAAI,CAAC1xB,YAAY,IAC/C,CAAC,IAAI,CAACnB,aAAa,IACnB,CAAC,IAAI,CAACvF,gBAAgB,EAAEqF,EAAE,EAC1B;MACA;;IAGF,IAAI,CAACuuB,OAAO,CAACK,mBAAmB,EAAE;IAElC,MAAMzuB,OAAO,GAAG,IAAI,CAACyD,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK;IAEtD,MAAMkvB,WAAW,GAAY;MAC3BhzB,EAAE,EAAE,OAAO,GAAG,IAAI+a,IAAI,EAAE,CAAC8V,OAAO,EAAE;MAClC1wB,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtBJ,MAAM,EAAE;QACNC,EAAE,EAAE,IAAI,CAACE,aAAa,IAAI,EAAE;QAC5BtF,QAAQ,EAAE,IAAI,CAAC0b;OAChB;MACDmb,QAAQ,EAAE;QACRzxB,EAAE,EAAE,IAAI,CAACrF,gBAAgB,CAACqF,EAAE;QAC5BpF,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC,QAAQ,IAAI;OAC7C;MACDyF,SAAS,EAAE,IAAI0a,IAAI,EAAE;MACrB0J,MAAM,EAAE,KAAK;MACbwO,SAAS,EAAE;KACZ;IAED,IAAI,IAAI,CAAC5xB,YAAY,EAAE;MACrB,IAAI6xB,QAAQ,GAAG,MAAM;MACrB,IAAI,IAAI,CAAC7xB,YAAY,CAACiL,IAAI,CAAC6mB,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC/CD,QAAQ,GAAG,OAAO;QAElB,IAAI,IAAI,CAAC/xB,UAAU,EAAE;UACnB6xB,WAAW,CAACI,WAAW,GAAG,CACxB;YACEpzB,EAAE,EAAE,iBAAiB;YACrB+R,GAAG,EAAE,IAAI,CAAC5Q,UAAU,GAAG,IAAI,CAACA,UAAU,CAACgY,QAAQ,EAAE,GAAG,EAAE;YACtD7M,IAAI,EAAEvS,WAAW,CAACs5B,KAAK;YACvB/xB,IAAI,EAAE,IAAI,CAACD,YAAY,CAACC,IAAI;YAC5BQ,IAAI,EAAE,IAAI,CAACT,YAAY,CAACS;WACzB,CACF;;;MAIL,IAAIoxB,QAAQ,KAAK,OAAO,EAAE;QACxBF,WAAW,CAAC1mB,IAAI,GAAGvS,WAAW,CAACs5B,KAAK;OACrC,MAAM,IAAIH,QAAQ,KAAK,MAAM,EAAE;QAC9BF,WAAW,CAAC1mB,IAAI,GAAGvS,WAAW,CAACu5B,IAAI;;;IAIvC,IAAI,CAACnd,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE6c,WAAW,CAAC;IAE/C,MAAMO,UAAU,GAAG,IAAI,CAAClyB,YAAY;IACpC,IAAI,CAACuC,WAAW,CAAC4vB,KAAK,EAAE;IACxB,IAAI,CAACtB,gBAAgB,EAAE;IAEvBzF,UAAU,CAAC,MAAM,IAAI,CAACvK,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAE/C,IAAI,CAAC3L,WAAW,GAAG,IAAI;IAEvB,MAAMkd,OAAO,GAAG,IAAI,CAAC9e,cAAc,CAACxQ,WAAW,CAC7C,IAAI,CAACxJ,gBAAgB,CAACqF,EAAE,EACxBG,OAAO,EACPozB,UAAU,IAAIG,SAAS,EACvB35B,WAAW,CAAC45B,IAAI,EAChB,IAAI,CAACvd,YAAY,EAAEpW,EAAE,CACtB,CAAC+hB,SAAS,CAAC;MACVC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAAC2R,kBAAkB,CAACZ,WAAW,CAAChzB,EAAG,EAAEiiB,OAAO,CAAC;QACjD,IAAI,CAAC1L,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC6a,kBAAkB,CAACZ,WAAW,CAAChzB,EAAG,EAAE,IAAI,EAAE,IAAI,CAAC;QACpD,IAAI,CAACuW,WAAW,GAAG,KAAK;QACxB,IAAI,CAACN,YAAY,CAAC2L,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;IAEF,IAAI,CAAC7K,aAAa,CAACgO,GAAG,CAAC0O,OAAO,CAAC;EACjC;EAEA;EACQG,kBAAkBA,CACxBC,MAAc,EACdzC,UAA2B,EAC3B0C,OAAA,GAAmB,KAAK;IAExB,IAAI,CAAC3d,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC2H,GAAG,CAAEC,GAAG,IAAI;MACxC,IAAIA,GAAG,CAAC/d,EAAE,KAAK6zB,MAAM,EAAE;QACrB,IAAIzC,UAAU,EAAE;UACd,OAAOA,UAAU;SAClB,MAAM,IAAI0C,OAAO,EAAE;UAClB,OAAO;YACL,GAAG/V,GAAG;YACNkV,SAAS,EAAE,KAAK;YAChBa,OAAO,EAAE;WACV;;;MAGL,OAAO/V,GAAG;IACZ,CAAC,CAAC;EACJ;EA6CA;EACAgW,QAAQA,CAAC7J,KAAU;IACjB,MAAM8J,SAAS,GAAG9J,KAAK,CAACe,MAAM;IAC9B,MAAMgJ,SAAS,GAAGD,SAAS,CAACC,SAAS;IAErC,IACEA,SAAS,GAAG,EAAE,IACd,CAAC,IAAI,CAACpd,aAAa,IACnB,IAAI,CAACT,YAAY,EAAEpW,EAAE,IACrB,IAAI,CAAC8W,eAAe,EACpB;MACA,IAAI,CAACwK,oBAAoB,EAAE;MAE3B,MAAM4S,eAAe,GAAGF,SAAS,CAACG,YAAY;MAC9C,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,EAAE;MAEzD,IAAI,CAACxd,aAAa,GAAG,IAAI;MAEzB,IAAI,CAACyd,gBAAgB,EAAE;MAEvB;MACAC,qBAAqB,CAAC,MAAK;QACzB,MAAMC,sBAAsB,GAAGA,CAAA,KAAK;UAClC,IAAIJ,mBAAmB,EAAE;YACvB,MAAMK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAC5CN,mBAAmB,CAACp0B,EAAE,CACvB;YACD,IAAIy0B,cAAc,EAAE;cAClB;cACAA,cAAc,CAACE,cAAc,CAAC;gBAAEC,KAAK,EAAE;cAAQ,CAAE,CAAC;aACnD,MAAM;cACL;cACA,MAAMC,eAAe,GAAGb,SAAS,CAACG,YAAY;cAC9C,MAAMW,UAAU,GAAGD,eAAe,GAAGX,eAAe;cACpDF,SAAS,CAACC,SAAS,GAAGA,SAAS,GAAGa,UAAU;;;UAIhD;UACA,IAAI,CAACvT,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACAkL,UAAU,CAAC+H,sBAAsB,EAAE,GAAG,CAAC;MACzC,CAAC,CAAC;;EAEN;EAEA;EACQH,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAACrT,iBAAiB,EAAEC,aAAa,IAAI,CAAC,IAAI,CAAC9K,QAAQ,CAACpS,MAAM,EACjE,OAAO,IAAI;IAEb,MAAMiwB,SAAS,GAAG,IAAI,CAAChT,iBAAiB,CAACC,aAAa;IACtD,MAAM8T,eAAe,GAAGf,SAAS,CAACgB,gBAAgB,CAAC,eAAe,CAAC;IAEnE,KAAK,IAAI/b,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8b,eAAe,CAAChxB,MAAM,EAAEkV,CAAC,EAAE,EAAE;MAC/C,MAAMgc,OAAO,GAAGF,eAAe,CAAC9b,CAAC,CAAC;MAClC,MAAMic,IAAI,GAAGD,OAAO,CAACE,qBAAqB,EAAE;MAE5C;MACA,IAAID,IAAI,CAACE,GAAG,IAAI,CAAC,IAAIF,IAAI,CAACG,MAAM,IAAIrB,SAAS,CAACsB,YAAY,EAAE;QAC1D,MAAMtL,SAAS,GAAGiL,OAAO,CAACM,YAAY,CAAC,iBAAiB,CAAC;QACzD,OAAO,IAAI,CAACpf,QAAQ,CAACka,IAAI,CAAEzQ,CAAC,IAAKA,CAAC,CAAC5f,EAAE,KAAKgqB,SAAS,CAAC,IAAI,IAAI;;;IAIhE,OAAO,IAAI;EACb;EAEA;EACQ0K,kBAAkBA,CACxB1K,SAA6B;IAE7B,IAAI,CAAC,IAAI,CAAChJ,iBAAiB,EAAEC,aAAa,IAAI,CAAC+I,SAAS,EAAE,OAAO,IAAI;IACrE,OAAO,IAAI,CAAChJ,iBAAiB,CAACC,aAAa,CAACuU,aAAa,CACvD,qBAAqBxL,SAAS,IAAI,CACnC;EACH;EAuBA;EACAsK,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACzd,aAAa,IAAI,CAAC,IAAI,CAACT,YAAY,EAAEpW,EAAE,IAAI,CAAC,IAAI,CAAC8W,eAAe,EACvE;IAEF;IACA,IAAI,CAACD,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAACD,WAAW,EAAE;IAElB;IACA,IAAI,CAACjC,cAAc,CAACob,eAAe,CACjC,IAAI,CAAC3Z,YAAY,CAACpW,EAAE,EACpB,IAAI,CAAC0W,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB,CAACmL,SAAS,CAAC;MACVC,IAAI,EAAG5L,YAAY,IAAI;QACrB,IACEA,YAAY,IACZA,YAAY,CAACD,QAAQ,IACrBC,YAAY,CAACD,QAAQ,CAACpS,MAAM,GAAG,CAAC,EAChC;UACA;UACA,MAAM0xB,WAAW,GAAG,CAAC,GAAG,IAAI,CAACtf,QAAQ,CAAC;UAEtC;UACA,MAAMuf,WAAW,GAAG,IAAI/d,GAAG,CAAC8d,WAAW,CAAC3X,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAC/d,EAAE,CAAC,CAAC;UAE7D;UACA,MAAM21B,WAAW,GAAGvf,YAAY,CAACD,QAAQ,CACtChc,MAAM,CAAE4jB,GAAG,IAAK,CAAC2X,WAAW,CAAC7Q,GAAG,CAAC9G,GAAG,CAAC/d,EAAE,CAAC,CAAC,CACzCywB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACb,MAAMC,KAAK,GAAG,IAAI7V,IAAI,CAAC2V,CAAC,CAACrwB,SAAmB,CAAC,CAACwwB,OAAO,EAAE;YACvD,MAAMC,KAAK,GAAG,IAAI/V,IAAI,CAAC4V,CAAC,CAACtwB,SAAmB,CAAC,CAACwwB,OAAO,EAAE;YACvD,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEJ,IAAI6E,WAAW,CAAC5xB,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAI,CAACoS,QAAQ,GAAG,CAAC,GAAGwf,WAAW,EAAE,GAAGF,WAAW,CAAC;YAEhD;YACA,IAAI,IAAI,CAACtf,QAAQ,CAACpS,MAAM,GAAG,IAAI,CAAC4S,kBAAkB,EAAE;cAClD,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACyf,KAAK,CAAC,CAAC,EAAE,IAAI,CAACjf,kBAAkB,CAAC;;YAGjE;YACA,IAAI,CAACG,eAAe,GAClB6e,WAAW,CAAC5xB,MAAM,IAAI,IAAI,CAAC2S,oBAAoB;WAClD,MAAM;YACL;YACA,IAAI,CAACI,eAAe,GAAG,KAAK;;SAE/B,MAAM;UACL,IAAI,CAACA,eAAe,GAAG,KAAK;;QAG9B;QACA;QACA2V,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5V,aAAa,GAAG,KAAK;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDkC,KAAK,EAAGA,KAAK,IAAI;QACfuK,OAAO,CAACvK,KAAK,CAAC,aAAa,EAAE,8BAA8B,EAAEA,KAAK,CAAC;QACnE,IAAI,CAAClC,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC0K,oBAAoB,EAAE;QAC3B,IAAI,CAACtL,YAAY,CAAC2L,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACQiU,eAAeA,CACrBC,UAAqC,EACrCC,UAAqC;IAErC,IAAI,CAACD,UAAU,IAAI,CAACC,UAAU,EAAE,OAAO,KAAK;IAE5C,IAAI;MACF,MAAMC,KAAK,GACTF,UAAU,YAAY/a,IAAI,GACtB+a,UAAU,CAACjF,OAAO,EAAE,GACpB,IAAI9V,IAAI,CAAC+a,UAAoB,CAAC,CAACjF,OAAO,EAAE;MAC9C,MAAMoF,KAAK,GACTF,UAAU,YAAYhb,IAAI,GACtBgb,UAAU,CAAClF,OAAO,EAAE,GACpB,IAAI9V,IAAI,CAACgb,UAAoB,CAAC,CAAClF,OAAO,EAAE;MAC9C,OAAOnG,IAAI,CAACwL,GAAG,CAACF,KAAK,GAAGC,KAAK,CAAC,GAAG,IAAI;KACtC,CAAC,OAAOld,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEAmJ,cAAcA,CAACiU,KAAA,GAAiB,KAAK;IACnC,IAAI;MACF,IAAI,CAAC,IAAI,CAACnV,iBAAiB,EAAEC,aAAa,EAAE;MAE5CsT,qBAAqB,CAAC,MAAK;QACzB,MAAMP,SAAS,GAAG,IAAI,CAAChT,iBAAiB,CAACC,aAAa;QACtD,MAAMmV,kBAAkB,GACtBpC,SAAS,CAACG,YAAY,GAAGH,SAAS,CAACsB,YAAY,IAC/CtB,SAAS,CAACC,SAAS,GAAG,GAAG;QAE3B,IAAIkC,KAAK,IAAIC,kBAAkB,EAAE;UAC/BpC,SAAS,CAACqC,QAAQ,CAAC;YACjBjB,GAAG,EAAEpB,SAAS,CAACG,YAAY;YAC3BmC,QAAQ,EAAE;WACX,CAAC;;MAEN,CAAC,CAAC;KACH,CAAC,OAAOC,GAAG,EAAE;MACZjT,OAAO,CAACvK,KAAK,CAAC,aAAa,EAAE,4BAA4B,EAAEwd,GAAG,CAAC;;EAEnE;EA0CA;;;;EAIAC,mBAAmBA,CAACC,QAAgB;IAClClY,MAAM,CAACmY,IAAI,CAACD,QAAQ,EAAE,QAAQ,CAAC;EACjC;EAEA;;;EAGAE,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACzU,cAAc,EAAE;IAErB;IACA;EACF;EAEA;;;EAGA0U,qBAAqBA,CAAA;IACnB,IAAI,CAAC5gB,MAAM,CAAC6gB,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEA;;;;EAIAxwB,WAAWA,CAACwlB,KAAa;IACvB,MAAMiL,OAAO,GAAG,IAAI,CAAClzB,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAIizB,OAAO,EAAE;MACX,MAAMC,YAAY,GAAGD,OAAO,CAAChzB,KAAK,IAAI,EAAE;MACxCgzB,OAAO,CAACE,QAAQ,CAACD,YAAY,GAAGlL,KAAK,CAAC;MACtCiL,OAAO,CAACG,WAAW,EAAE;MACrB;MACAxK,UAAU,CAAC,MAAK;QACd,MAAMyK,YAAY,GAAGvY,QAAQ,CAAC6W,aAAa,CACzC,uBAAuB,CACJ;QACrB,IAAI0B,YAAY,EAAE;UAChBA,YAAY,CAACC,KAAK,EAAE;;MAExB,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQ7H,wBAAwBA,CAAA;IAC9B,MAAM8H,eAAe,GACnB,IAAI,CAACziB,cAAc,CAAC0iB,2BAA2B,EAAE,CAACtV,SAAS,CAAC;MAC1DC,IAAI,EAAG4F,YAAY,IAAI;QACrB,IAAI,CAAC1U,aAAa,CAACokB,OAAO,CAAC1P,YAAY,CAAC;QACxC,IAAI,CAACrC,uBAAuB,EAAE;QAE9B,IAAI,CAAC5Q,cAAc,CAAC4iB,IAAI,CAAC,cAAc,CAAC;QAExC,IACE3P,YAAY,CAACtb,IAAI,KAAK,aAAa,IACnCsb,YAAY,CAACsE,cAAc,KAAK,IAAI,CAAC9V,YAAY,EAAEpW,EAAE,EACrD;UACA,IAAI4nB,YAAY,CAAC5nB,EAAE,EAAE;YACnB,IAAI,CAAC2U,cAAc,CAACsR,UAAU,CAAC,CAAC2B,YAAY,CAAC5nB,EAAE,CAAC,CAAC,CAAC+hB,SAAS,EAAE;;;MAGnE,CAAC;MACDhJ,KAAK,EAAGA,KAAK,IAAI,CAAE;KACpB,CAAC;IACJ,IAAI,CAAChC,aAAa,CAACgO,GAAG,CAACqS,eAAe,CAAC;IAEvC,MAAMI,oBAAoB,GAAG,IAAI,CAAC7iB,cAAc,CAAC8iB,cAAc,CAAC1V,SAAS,CAAC;MACxEC,IAAI,EAAG9O,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACqS,uBAAuB,EAAE;MAChC,CAAC;MACDxM,KAAK,EAAGA,KAAK,IAAI,CAAE;KACpB,CAAC;IACF,IAAI,CAAChC,aAAa,CAACgO,GAAG,CAACyS,oBAAoB,CAAC;IAE5C,MAAME,oBAAoB,GACxB,IAAI,CAAC/iB,cAAc,CAACgjB,kBAAkB,CAAC5V,SAAS,CAAC;MAC/CC,IAAI,EAAGvmB,KAAK,IAAI;QACd,IAAI,CAAC0Z,uBAAuB,GAAG1Z,KAAK;MACtC;KACD,CAAC;IACJ,IAAI,CAACsb,aAAa,CAACgO,GAAG,CAAC2S,oBAAoB,CAAC;IAE5C,MAAME,OAAO,GAAG,IAAI,CAACjjB,cAAc,CAACkjB,aAAa,CAAC9V,SAAS,CAAC;MAC1DC,IAAI,EAAGW,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACvW,YAAY,GAAGuW,IAAI;UACxB,IAAI,CAACpL,aAAa,GAAG,IAAI;UACzB,IAAI,CAAC5C,cAAc,CAAC4iB,IAAI,CAAC,UAAU,CAAC;SACrC,MAAM;UACL,IAAI,CAAChgB,aAAa,GAAG,KAAK;UAC1B,IAAI,CAACnL,YAAY,GAAG,IAAI;;MAE5B;KACD,CAAC;IACF,IAAI,CAAC2K,aAAa,CAACgO,GAAG,CAAC6S,OAAO,CAAC;IAE/B,MAAME,aAAa,GAAG,IAAI,CAACnjB,cAAc,CAACsO,WAAW,CAAClB,SAAS,CAAC;MAC9DC,IAAI,EAAGW,IAAI,IAAI;QACb,IAAI,CAACzU,UAAU,GAAGyU,IAAI;QACtB,IAAIA,IAAI,EAAE;UACR,IAAI,CAACnL,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACwM,oBAAoB,EAAE;SAC5B,MAAM;UACL,IAAI,CAACxM,mBAAmB,GAAG,KAAK;UAChC,IAAI,CAACyM,mBAAmB,EAAE;UAC1B,IAAI,CAACC,oBAAoB,EAAE;;MAE/B;KACD,CAAC;IACF,IAAI,CAACnN,aAAa,CAACgO,GAAG,CAAC+S,aAAa,CAAC;IAErC;IACA,MAAMC,cAAc,GAAG,IAAI,CAACpjB,cAAc,CAACqjB,YAAY,CAACjW,SAAS,CAAC;MAChEC,IAAI,EAAGiW,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAAC/c,iBAAiB,EAAE;UACpC,IAAI,CAACA,iBAAiB,CAACgd,SAAS,GAAGD,MAAM;;MAE7C;KACD,CAAC;IACF,IAAI,CAAClhB,aAAa,CAACgO,GAAG,CAACgT,cAAc,CAAC;IAEtC;IACA,MAAMI,eAAe,GAAG,IAAI,CAACxjB,cAAc,CAACyjB,aAAa,CAACrW,SAAS,CAAC;MAClEC,IAAI,EAAGiW,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAAC9c,kBAAkB,EAAE;UACrC,IAAI,CAACA,kBAAkB,CAAC+c,SAAS,GAAGD,MAAM;;MAE9C;KACD,CAAC;IACF,IAAI,CAAClhB,aAAa,CAACgO,GAAG,CAACoT,eAAe,CAAC;EACzC;EAsIA;;;EAGA/T,iBAAiBA,CAACiU,OAAA,GAAmB,KAAK;IACxC,MAAMC,OAAO,GAAG,IAAI,CAAC3jB,cAAc,CAAC4jB,gBAAgB,CAClDF,OAAO,EACP,CAAC,EACD,EAAE,CACH,CAACtW,SAAS,CAAC;MACVC,IAAI,EAAG9O,aAAa,IAAI;QACtB,IAAImlB,OAAO,EAAE;UACX,IAAI,CAACnlB,aAAa,GAAGA,aAAa;SACnC,MAAM;UACL,IAAI,CAACA,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAGA,aAAa,CAAC;;QAGhE,IAAI,CAACqS,uBAAuB,EAAE;MAChC,CAAC;MACDxM,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,YAAY,CAAC2L,SAAS,CACzB,6CAA6C,CAC9C;MACH;KACD,CAAC;IAEF,IAAI,CAAC7K,aAAa,CAACgO,GAAG,CAACuT,OAAO,CAAC;EACjC;EAsNA;;;EAGQ/I,qBAAqBA,CAAA;IAC3B,MAAMiJ,SAAS,GAAG,IAAI,CAAC7jB,cAAc,CAAC4a,qBAAqB,EAAE,CAACxN,SAAS,CAAC;MACtEC,IAAI,EAAG+G,IAAU,IAAI;QACnB,IAAI,CAAC0P,sBAAsB,CAAC1P,IAAI,CAAC;MACnC,CAAC;MACDhQ,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;IAEF,IAAI,CAAChC,aAAa,CAACgO,GAAG,CAACyT,SAAS,CAAC;EACnC;EAEA;;;EAGQC,sBAAsBA,CAAC1P,IAAU;IACvC,IAAI,CAACA,IAAI,CAAC/oB,EAAE,EAAE;IAEd,IAAI+oB,IAAI,CAACjuB,QAAQ,EAAE;MACjB,IAAI,CAAC8f,WAAW,CAAC8d,GAAG,CAAC3P,IAAI,CAAC/oB,EAAE,EAAE+oB,IAAI,CAAC;KACpC,MAAM;MACL,IAAI,CAACnO,WAAW,CAACkK,MAAM,CAACiE,IAAI,CAAC/oB,EAAE,CAAC;;IAGlC,IAAI,IAAI,CAACrF,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACqF,EAAE,KAAK+oB,IAAI,CAAC/oB,EAAE,EAAE;MACjE,IAAI,CAACrF,gBAAgB,GAAG;QAAE,GAAG,IAAI,CAACA,gBAAgB;QAAE,GAAGouB;MAAI,CAAE;;EAEjE;EAEA;;;EAGQyG,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACtvB,aAAa,EAAE;IAEzB,MAAMy4B,YAAY,GAAG,IAAI,CAAChkB,cAAc,CAACikB,aAAa,CACpD,IAAI,CAAC14B,aAAa,CACnB,CAAC6hB,SAAS,CAAC;MACVC,IAAI,EAAG+G,IAAU,IAAI;QACnB,IAAI,CAACvrB,iBAAiB,GAAG,QAAQ;QACjC,IAAI,CAACsd,gBAAgB,GAAG,IAAIC,IAAI,EAAE;MACpC,CAAC;MACDhC,KAAK,EAAGA,KAAK,IAAI;QACfuK,OAAO,CAACvK,KAAK,CACX,aAAa,EACb,2CAA2C,EAC3CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAChC,aAAa,CAACgO,GAAG,CAAC4T,YAAY,CAAC;EACtC;EAEA;;;EAGQlJ,qBAAqBA,CAAA;IAC3B,MAAMoJ,MAAM,GAAG,CACb,WAAW,EACX,WAAW,EACX,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,OAAO,CACR;IAEDA,MAAM,CAACzT,OAAO,CAAE8E,KAAK,IAAI;MACvBvL,QAAQ,CAAC+Q,gBAAgB,CAACxF,KAAK,EAAE,IAAI,CAAC4O,cAAc,CAACnJ,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IACxE,CAAC,CAAC;EACJ;EAEA;;;EAGQmJ,cAAcA,CAAA;IACpB,IAAI,CAAChe,gBAAgB,GAAG,IAAIC,IAAI,EAAE;IAElC;IACA,IAAI,IAAI,CAACC,eAAe,EAAE;MACxB0T,YAAY,CAAC,IAAI,CAAC1T,eAAe,CAAC;;IAGpC;IACA,IACE,IAAI,CAACxd,iBAAiB,KAAK,MAAM,IACjC,IAAI,CAACA,iBAAiB,KAAK,SAAS,EACpC;MACA,IAAI,CAACN,gBAAgB,CAAC,QAAQ,CAAC;;IAGjC;IACA,IAAI,CAAC8d,eAAe,GAAGyR,UAAU,CAAC,MAAK;MACrC,IAAI,IAAI,CAACjvB,iBAAiB,KAAK,QAAQ,EAAE;QACvC,IAAI,CAACN,gBAAgB,CAAC,MAAM,CAAC;;IAEjC,CAAC,EAAE,IAAI,CAACsX,CAAC,CAACkE,MAAM,CAACC,IAAI,CAAC;EACxB;EAEA;;;EAGAzb,gBAAgBA,CAACqc,MAAc;IAC7B,IAAI,CAAC,IAAI,CAACrZ,aAAa,EAAE;IAEzB,MAAM64B,cAAc,GAAG,IAAI,CAACv7B,iBAAiB;IAE7C,IAAIw7B,gBAAgB;IACpB,IAAIzf,MAAM,KAAK,QAAQ,EAAE;MACvByf,gBAAgB,GAAG,IAAI,CAACrkB,cAAc,CAACikB,aAAa,CAAC,IAAI,CAAC14B,aAAa,CAAC;KACzE,MAAM;MACL84B,gBAAgB,GAAG,IAAI,CAACrkB,cAAc,CAACga,cAAc,CAAC,IAAI,CAACzuB,aAAa,CAAC;;IAG3E,MAAM+4B,SAAS,GAAGD,gBAAgB,CAACjX,SAAS,CAAC;MAC3CC,IAAI,EAAG+G,IAAU,IAAI;QACnB,IAAI,CAACvrB,iBAAiB,GAAG+b,MAAM;QAE/B,IAAIA,MAAM,KAAKwf,cAAc,EAAE;UAC7B,MAAMG,UAAU,GAAG,IAAI,CAAC/6B,aAAa,CAACob,MAAM,CAAC;UAC7C,IAAI,CAACtD,YAAY,CAAC+G,QAAQ,CAAC,YAAYkc,UAAU,EAAE,CAAC;;MAExD,CAAC;MACDngB,KAAK,EAAGA,KAAK,IAAI;QACfuK,OAAO,CAACvK,KAAK,CACX,aAAa,EACb,yCAAyC,EACzCA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAChC,aAAa,CAACgO,GAAG,CAACkU,SAAS,CAAC;EACnC;EAEA;;;EAGAE,eAAeA,CAAA;IACb,MAAMC,QAAQ,GAAG,IAAI,CAACzkB,cAAc,CAAC0kB,WAAW,CAC9C,KAAK,EACL3F,SAAS,EACT,CAAC,EACD,EAAE,EACF,UAAU,EACV,KAAK,EACL,IAAI,CACL,CAAC3R,SAAS,CAAC;MACVC,IAAI,EAAGsX,KAAa,IAAI;QACtBA,KAAK,CAAClU,OAAO,CAAE2D,IAAI,IAAI;UACrB,IAAIA,IAAI,CAACjuB,QAAQ,IAAIiuB,IAAI,CAAC/oB,EAAE,EAAE;YAC5B,IAAI,CAAC4a,WAAW,CAAC8d,GAAG,CAAC3P,IAAI,CAAC/oB,EAAE,EAAE+oB,IAAI,CAAC;;QAEvC,CAAC,CAAC;MACJ,CAAC;MACDhQ,KAAK,EAAGA,KAAK,IAAI;QACfuK,OAAO,CAACvK,KAAK,CACX,aAAa,EACb,qDAAqD,EACrDA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAChC,aAAa,CAACgO,GAAG,CAACqU,QAAQ,CAAC;EAClC;EAgJAG,gBAAgBA,CAACtX,OAAY;IAC3B,IAAI,CAAC/J,SAAS,CAAC+J,OAAO,CAACjiB,EAAE,CAAC,GAAG,IAAI;IACjCysB,UAAU,CAAC,MAAK;MACd,IAAI,CAACvU,SAAS,CAAC+J,OAAO,CAACjiB,EAAE,CAAC,GAAG,KAAK;MAClC,IAAI,CAACiY,cAAc,CAACgK,OAAO,CAACjiB,EAAE,CAAC,GAAG,KAAK;IACzC,CAAC,EAAE,IAAI,CAAC;EACV;EA6OAw5B,WAAWA,CAAA;IACT,IAAI,CAACjL,OAAO,CAACC,aAAa,EAAE;IAC5B,IAAI,CAACD,OAAO,CAACI,cAAc,EAAE;IAC7B,IAAI,CAACJ,OAAO,CAACK,mBAAmB,EAAE;IAClC,IAAI,CAAC7X,aAAa,CAACmM,WAAW,EAAE;IAChCvE,QAAQ,CAAC8a,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACzO,eAAe,CAAC2E,IAAI,CAAC,IAAI,CAAC,CAAC;EACxE;;;uBAjsEWrb,oBAAoB,EAAAla,EAAA,CAAAs/B,iBAAA,CAAAC,EAAA,CAAAhlB,cAAA,GAAAva,EAAA,CAAAs/B,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAAz/B,EAAA,CAAAs/B,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAA3/B,EAAA,CAAAs/B,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA7/B,EAAA,CAAAs/B,iBAAA,CAAAQ,EAAA,CAAAC,iBAAA,GAAA//B,EAAA,CAAAs/B,iBAAA,CAAAE,EAAA,CAAAQ,MAAA,GAAAhgC,EAAA,CAAAs/B,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAlgC,EAAA,CAAAs/B,iBAAA,CAAAt/B,EAAA,CAAAmgC,iBAAA;IAAA;EAAA;;;YAApBjmB,oBAAoB;MAAA6W,SAAA;MAAAqP,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UC9BjCtgC,EAAA,CAAAC,cAAA,aAA4B;UAGhBD,EAAA,CAAAuB,UAAA,mBAAAi/B,sDAAA;YAAA,OAASD,GAAA,CAAA/D,qBAAA,EAAuB;UAAA,EAAC;UACvCx8B,EAAA,CAAA+B,SAAA,WAAiC;UACnC/B,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAA+B,SAAA,aAIE;UAEF/B,EAAA,CAAAgC,UAAA,IAAAy+B,mCAAA,iBASM;UAENzgC,EAAA,CAAAC,cAAA,aAA8B;UAE5BD,EAAA,CAAAgC,UAAA,IAAA0+B,4CAAA,0BAsBe;UAGf1gC,EAAA,CAAAC,cAAA,aAAsB;UAElBD,EAAA,CAAAuB,UAAA,mBAAAo/B,sDAAA;YAAA,OAASJ,GAAA,CAAAv9B,oBAAA,EAAsB;UAAA,EAAC;UAOhChD,EAAA,CAAA+B,SAAA,aAGK;UAEL/B,EAAA,CAAAgC,UAAA,KAAA4+B,qCAAA,mBAGQ;UACV5gC,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAgC,UAAA,KAAA6+B,oCAAA,mBA8DM;UACR7gC,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAsB;UAElBD,EAAA,CAAAuB,UAAA,mBAAAu/B,uDAAA;YAAA,OAASP,GAAA,CAAAxe,mBAAA,EAAqB;UAAA,EAAC;UAG/B/hB,EAAA,CAAA+B,SAAA,aAA8B;UAChC/B,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAgC,UAAA,KAAA++B,oCAAA,kBAgCM;UACR/gC,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAsB;UAElBD,EAAA,CAAAuB,UAAA,mBAAAy/B,uDAAA;YAAA,OAAST,GAAA,CAAAve,cAAA,EAAgB;UAAA,EAAC;UAI1BhiB,EAAA,CAAA+B,SAAA,aAAiC;UACnC/B,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAgC,UAAA,KAAAi/B,oCAAA,mBA+CM;UACRjhC,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,mBAA8C;UAC5CD,EAAA,CAAAgC,UAAA,KAAAk/B,oCAAA,kBAKM;UAENlhC,EAAA,CAAAgC,UAAA,KAAAm/B,oCAAA,kBAUM;UAENnhC,EAAA,CAAAgC,UAAA,KAAAo/B,oCAAA,kBAcM;UACRphC,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAgC,UAAA,KAAAq/B,oCAAA,mBA0BM;UAGNrhC,EAAA,CAAAgC,UAAA,KAAAs/B,oCAAA,mBAoBM;UAGNthC,EAAA,CAAAgC,UAAA,KAAAu/B,oCAAA,mBA+BM;UAGNvhC,EAAA,CAAAC,cAAA,eAAsC;UAM9BD,EAAA,CAAAuB,UAAA,mBAAAigC,uDAAA;YAAA,OAASjB,GAAA,CAAAzzB,iBAAA,EAAmB;UAAA,EAAC;UAK7B9M,EAAA,CAAA+B,SAAA,aAA4B;UAC9B/B,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAsC;UAGlCD,EAAA,CAAAuB,UAAA,mBAAAkgC,uDAAA;YAAA,OAASlB,GAAA,CAAAmB,oBAAA,EAAsB;UAAA,EAAC;UAKhC1hC,EAAA,CAAA+B,SAAA,aAAgC;UAClC/B,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAgC,UAAA,KAAA2/B,oCAAA,mBAiCM;UACR3hC,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAA2C;UAGvCD,EAAA,CAAAuB,UAAA,sBAAAqgC,wDAAA;YAAA,OAAYrB,GAAA,CAAAx2B,WAAA,EAAa;UAAA,EAAC;UAG1B/J,EAAA,CAAAC,cAAA,eAA0C;UAOtCD,EAAA,CAAAuB,UAAA,mBAAAsgC,yDAAA70B,MAAA;YAAA,OAASuzB,GAAA,CAAAuB,aAAA,CAAA90B,MAAA,CAAqB;UAAA,EAAC,qBAAA+0B,2DAAA/0B,MAAA;YAAA,OACpBuzB,GAAA,CAAAyB,cAAA,CAAAh1B,MAAA,CAAsB;UAAA,EADF,mBAAAi1B,yDAAA;YAAA,OAEtB1B,GAAA,CAAA2B,YAAA,EAAc;UAAA,EAFQ,kBAAAC,wDAAA;YAAA,OAGvB5B,GAAA,CAAA6B,WAAA,EAAa;UAAA,EAHU;UAMhCpiC,EAAA,CAAAG,YAAA,EAAW;UAGZH,EAAA,CAAAgC,UAAA,KAAAqgC,oCAAA,kBAKM;UACRriC,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAA0C;UAGtCD,EAAA,CAAAgC,UAAA,KAAAsgC,uCAAA,qBAWS;UAETtiC,EAAA,CAAAgC,UAAA,KAAAugC,uCAAA,qBAaS;UACXviC,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAgC,UAAA,KAAAwgC,oCAAA,mBAoDM;UACRxiC,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,qBAOE;UAHAD,EAAA,CAAAuB,UAAA,oBAAAkhC,uDAAAz1B,MAAA;YAAA,OAAUuzB,GAAA,CAAAjJ,cAAA,CAAAtqB,MAAA,EAAuB,OAAO,CAAC;UAAA,EAAC;UAJ5ChN,EAAA,CAAAG,YAAA,EAOE;UACFH,EAAA,CAAAC,cAAA,qBAME;UAFAD,EAAA,CAAAuB,UAAA,oBAAAmhC,uDAAA11B,MAAA;YAAA,OAAUuzB,GAAA,CAAAjJ,cAAA,CAAAtqB,MAAA,EAAuB,OAAO,CAAC;UAAA,EAAC;UAJ5ChN,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,qBAME;UAFAD,EAAA,CAAAuB,UAAA,oBAAAohC,uDAAA31B,MAAA;YAAA,OAAUuzB,GAAA,CAAAjJ,cAAA,CAAAtqB,MAAA,EAAuB,OAAO,CAAC;UAAA,EAAC;UAJ5ChN,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,qBAME;UAFAD,EAAA,CAAAuB,UAAA,oBAAAqhC,uDAAA51B,MAAA;YAAA,OAAUuzB,GAAA,CAAAjJ,cAAA,CAAAtqB,MAAA,EAAuB,UAAU,CAAC;UAAA,EAAC;UAJ/ChN,EAAA,CAAAG,YAAA,EAME;UAGFH,EAAA,CAAAC,cAAA,eAAoE;UAE5DD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,kBAA8D;UAApCD,EAAA,CAAAuB,UAAA,mBAAAshC,uDAAA;YAAA,OAAStC,GAAA,CAAA1lB,uBAAA,EAAyB;UAAA,EAAC;UAC3D7a,EAAA,CAAA+B,SAAA,aAA4B;UAC9B/B,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAgC,UAAA,KAAA8gC,oCAAA,kBASM;UACN9iC,EAAA,CAAAgC,UAAA,KAAA+gC,oCAAA,kBAQM;UACR/iC,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAgC,UAAA,KAAAghC,oCAAA,mBA0GM;UAGNhjC,EAAA,CAAAgC,UAAA,KAAAihC,oCAAA,mBAqHM;UAKNjjC,EAAA,CAAAgC,UAAA,KAAAkhC,oCAAA,mBAgCM;UAGNljC,EAAA,CAAAgC,UAAA,KAAAmhC,oCAAA,oBA4DM;UAGNnjC,EAAA,CAAAgC,UAAA,KAAAohC,oCAAA,mBAoBM;UAGNpjC,EAAA,CAAAgC,UAAA,KAAAqhC,oCAAA,mBAgDM;UAGNrjC,EAAA,CAAAgC,UAAA,KAAAshC,oCAAA,mBA6CM;UAGNtjC,EAAA,CAAAgC,UAAA,KAAAuhC,oCAAA,kBAKM;UAGNvjC,EAAA,CAAAgC,UAAA,KAAAwhC,oCAAA,mBA0CM;UAGNxjC,EAAA,CAAAgC,UAAA,KAAAyhC,oCAAA,mBA6DM;UACRzjC,EAAA,CAAAG,YAAA,EAAM;;;;UAxpCAH,EAAA,CAAAI,SAAA,GAAqE;UAArEJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAhgC,gBAAA,kBAAAggC,GAAA,CAAAhgC,gBAAA,CAAAkQ,KAAA,yCAAAzQ,EAAA,CAAAgH,aAAA,CAAqE;UAK/ChH,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAhgC,gBAAA,CAAsB;UAaXP,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAiB,UAAA,YAAAs/B,GAAA,CAAA9lB,gBAAA,GAAqB;UA6BlDza,EAAA,CAAAI,SAAA,GAEE;UAFFJ,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAAwiC,IAAA,EAAAnD,GAAA,CAAAvjB,kBAAA,EAEE;UAIAhd,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAa,UAAA,CAAA0/B,GAAA,CAAAhS,aAAA,CAAAgS,GAAA,CAAAn9B,iBAAA,EAA0C;UAC1CpD,EAAA,CAAAiB,UAAA,YAAAs/B,GAAA,CAAAz8B,cAAA,CAAAy8B,GAAA,CAAAn9B,iBAAA,EAA6C;UAI5CpD,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAr9B,gBAAA,CAAsB;UAOxBlD,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAvjB,kBAAA,CAAwB;UA2ExBhd,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAA1jB,iBAAA,CAAuB;UA8CvB7c,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAzjB,YAAA,CAAkB;UAsDtB9c,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAtkB,OAAA,CAAa;UAObjc,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAiB,UAAA,UAAAs/B,GAAA,CAAAtkB,OAAA,IAAAskB,GAAA,CAAAxkB,QAAA,CAAApS,MAAA,OAAuC;UAYpB3J,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAiB,UAAA,YAAAs/B,GAAA,CAAAxkB,QAAA,CAAW;UAiB7B/b,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAp6B,iBAAA,CAAuB;UA6BvBnG,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAt5B,YAAA,CAAkB;UAuBlBjH,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAn1B,gBAAA,CAAsB;UA0CpBpL,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAAwK,GAAA,EAAA60B,GAAA,CAAAxjB,eAAA,EAAuC;UAWrC/c,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAAwK,GAAA,EAAA60B,GAAA,CAAAoD,kBAAA,EAA0C;UAOtC3jC,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAoD,kBAAA,CAAwB;UAwC9B3jC,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAiB,UAAA,cAAAs/B,GAAA,CAAA/2B,WAAA,CAAyB;UAUrBxJ,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAiB,UAAA,cAAAs/B,GAAA,CAAAhgC,gBAAA,IAAAggC,GAAA,CAAAn1B,gBAAA,CAAkD;UAWjDpL,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAiB,UAAA,WAAA2iC,QAAA,GAAArD,GAAA,CAAA/2B,WAAA,CAAAC,GAAA,8BAAAm6B,QAAA,CAAAl6B,KAAA,kBAAAk6B,QAAA,CAAAl6B,KAAA,CAAAC,MAAA,SAAsD;UAcxD3J,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAsD,iBAAA,MAAAtD,GAAA,CAAAt5B,YAAA,CAAyC;UAazCjH,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAiB,UAAA,UAAAs/B,GAAA,CAAAsD,iBAAA,OAAAtD,GAAA,CAAAt5B,YAAA,CAA2C;UAkB9CjH,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAxjB,eAAA,CAAqB;UAuFL/c,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,KAAA4iC,IAAA,EAAAvD,GAAA,CAAAzlB,qBAAA,EAA2C;UAQ9D9a,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAznB,aAAA,CAAAnP,MAAA,OAAgC;UAUR3J,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAiB,UAAA,YAAAs/B,GAAA,CAAAznB,aAAA,CAAgB;UAY1C9Y,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAA5lB,aAAA,CAAmB;UA6GnB3a,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAA3lB,kBAAA,CAAwB;UAyHrB5a,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAvuB,YAAA,CAAkB;UAoCrBhS,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAzsB,UAAA,CAAgB;UA8Db9T,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAA/iB,sBAAA,CAA4B;UAuB5Bxd,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAviB,gBAAA,CAAsB;UAmDtBhe,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAwD,eAAA,CAAqB;UAgDrB/jC,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAAyD,SAAA,CAAe;UASlBhkC,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAA0D,oBAAA,CAA0B;UA6C1BjkC,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAiB,UAAA,SAAAs/B,GAAA,CAAA2D,wBAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}