{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { MessageType, CallType } from 'src/app/models/message.model';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nexport let MessageChatComponent = class MessageChatComponent {\n  // Getter pour compatibilité\n  get availableReactions() {\n    return this.c.reactions;\n  }\n  // Emojis du service\n  get commonEmojis() {\n    return this.MessageService.getCommonEmojis();\n  }\n  // Configuration des boutons d'action de l'en-tête\n  getHeaderActions() {\n    return [{\n      class: 'btn-audio-call',\n      icon: 'fas fa-phone-alt',\n      title: 'Appel audio',\n      onClick: () => this.initiateCall('AUDIO'),\n      isActive: false\n    }, {\n      class: 'btn-video-call',\n      icon: 'fas fa-video',\n      title: 'Appel vidéo',\n      onClick: () => this.initiateCall('VIDEO'),\n      isActive: false\n    }, {\n      class: 'btn-search',\n      icon: 'fas fa-search',\n      title: 'Rechercher',\n      onClick: () => this.toggleSearchBar(),\n      isActive: this.showSearchBar,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-pinned relative',\n      icon: 'fas fa-thumbtack',\n      title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n      onClick: () => this.togglePinnedMessages(),\n      isActive: this.showPinnedMessages,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.getPinnedMessagesCount() > 0 ? {\n        count: this.getPinnedMessagesCount(),\n        class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n        animate: false\n      } : null\n    }, {\n      class: 'btn-notifications relative',\n      icon: 'fas fa-bell',\n      title: 'Notifications',\n      onClick: () => this.toggleNotificationPanel(),\n      isActive: this.showNotificationPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.unreadNotificationCount > 0 ? {\n        count: this.unreadNotificationCount,\n        class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n        animate: true\n      } : null\n    }, {\n      class: 'btn-history relative',\n      icon: 'fas fa-history',\n      title: 'Historique des appels',\n      onClick: () => this.toggleCallHistoryPanel(),\n      isActive: this.showCallHistoryPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-stats relative',\n      icon: 'fas fa-chart-bar',\n      title: \"Statistiques d'appels\",\n      onClick: () => this.toggleCallStatsPanel(),\n      isActive: this.showCallStatsPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      }\n    }, {\n      class: 'btn-voice-messages relative',\n      icon: 'fas fa-microphone',\n      title: 'Messages vocaux',\n      onClick: () => this.toggleVoiceMessagesPanel(),\n      isActive: this.showVoiceMessagesPanel,\n      activeClass: {\n        'text-[#4f5fad] dark:text-[#6d78c9]': true\n      },\n      badge: this.voiceMessages.length > 0 ? {\n        count: this.voiceMessages.length,\n        class: 'bg-[#4f5fad]',\n        animate: false\n      } : null\n    }];\n  }\n  constructor(MessageService, route, authService, fb, statusService, router, toastService, cdr) {\n    this.MessageService = MessageService;\n    this.route = route;\n    this.authService = authService;\n    this.fb = fb;\n    this.statusService = statusService;\n    this.router = router;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    this.messages = [];\n    this.conversation = null;\n    this.loading = true;\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    this.selectedFile = null;\n    this.previewUrl = null;\n    this.isUploading = false;\n    this.isTyping = false;\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.MAX_MESSAGES_PER_SIDE = 5;\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.MAX_TOTAL_MESSAGES = 100;\n    this.currentPage = 1;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.subscriptions = new Subscription();\n    // Interface\n    this.selectedTheme = 'theme-default';\n    // États\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n    this.showEmojiPicker = false;\n    this.showSearchBar = false;\n    this.showPinnedMessages = false;\n    this.showStatusSelector = false;\n    this.showNotificationPanel = false;\n    this.showNotificationSettings = false;\n    this.showUserStatusPanel = false;\n    this.showCallHistoryPanel = false;\n    this.showCallStatsPanel = false;\n    this.showVoiceMessagesPanel = false;\n    // Appels\n    this.incomingCall = null;\n    this.activeCall = null;\n    this.showCallModal = false;\n    this.showActiveCallModal = false;\n    this.isCallMuted = false;\n    this.isVideoEnabled = true;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // Notifications et messages\n    this.notifications = [];\n    this.unreadNotificationCount = 0;\n    this.selectedNotifications = new Set();\n    this.showDeleteConfirmModal = false;\n    this.editingMessageId = null;\n    this.editingContent = '';\n    this.replyingToMessage = null;\n    // Recherche\n    this.searchQuery = '';\n    this.isSearching = false;\n    this.searchMode = false;\n    this.searchResults = [];\n    // Panneaux\n    this.pinnedMessages = [];\n    this.showReactionPicker = {};\n    this.showDeleteConfirm = {};\n    this.showPinConfirm = {};\n    this.isPinning = {};\n    this.showMessageOptions = {};\n    // Variables de transfert\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n    this.availableConversations = [];\n    this.isForwarding = false;\n    this.isLoadingConversations = false;\n    // Constantes optimisées\n    this.c = {\n      reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n      delays: {\n        away: 300000,\n        typing: 3000,\n        scroll: 100,\n        anim: 300\n      },\n      error: 'Erreur. Veuillez réessayer.',\n      trackById: (i, item) => item?.id || i.toString(),\n      notifications: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      status: {\n        online: {\n          text: 'En ligne',\n          color: 'text-green-500',\n          icon: 'fas fa-circle',\n          label: 'En ligne',\n          description: 'Disponible pour discuter'\n        },\n        offline: {\n          text: 'Hors ligne',\n          color: 'text-gray-500',\n          icon: 'far fa-circle',\n          label: 'Hors ligne',\n          description: 'Invisible pour tous'\n        },\n        away: {\n          text: 'Absent',\n          color: 'text-yellow-500',\n          icon: 'fas fa-clock',\n          label: 'Absent',\n          description: 'Absent temporairement'\n        },\n        busy: {\n          text: 'Occupé',\n          color: 'text-red-500',\n          icon: 'fas fa-minus-circle',\n          label: 'Occupé',\n          description: 'Ne pas déranger'\n        }\n      },\n      themes: [{\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad',\n        hoverColor: '#4f5fad'\n      }, {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d',\n        hoverColor: '#ff6b9d'\n      }, {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6',\n        hoverColor: '#3d85c6'\n      }, {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f',\n        hoverColor: '#6aa84f'\n      }],\n      calls: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      },\n      // Alias pour compatibilité\n      notificationConfig: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      callStatusColors: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      }\n    };\n    // Variables de notification\n    this.notificationFilter = 'all';\n    this.isLoadingNotifications = false;\n    this.isMarkingAsRead = false;\n    this.isDeletingNotifications = false;\n    this.hasMoreNotifications = false;\n    this.notificationSounds = true;\n    this.notificationPreview = true;\n    this.autoMarkAsRead = true;\n    // Variables d'appel\n    this.isCallMinimized = false;\n    this.callQuality = 'connecting';\n    this.showCallControls = false;\n    // Variables de statut utilisateur\n    this.onlineUsers = new Map();\n    this.currentUserStatus = 'online';\n    this.lastActivityTime = new Date();\n    this.autoAwayTimeout = null;\n    this.isUpdatingStatus = false;\n    this.callHistory = [];\n    this.voiceMessages = [];\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.statusFilterType = 'all';\n    // Services de fichiers optimisés\n    this.f = {\n      getIcon: t => this.MessageService.getFileIcon(t),\n      getType: t => this.MessageService.getFileType(t)\n    };\n    this.getFileIcon = this.f.getIcon;\n    this.getFileType = this.f.getType;\n    this.isCurrentlyTyping = false;\n    this.TYPING_DELAY = 500;\n    this.TYPING_TIMEOUT = 3000;\n    // Méthodes de basculement principales consolidées\n    this.mainToggleMethods = {\n      themeSelector: () => this.togglePanel('theme'),\n      mainMenu: () => this.togglePanel('menu'),\n      emojiPicker: () => this.togglePanel('emoji')\n    };\n    this.toggleThemeSelector = this.mainToggleMethods.themeSelector;\n    this.toggleMainMenu = this.mainToggleMethods.mainMenu;\n    this.toggleEmojiPicker = this.mainToggleMethods.emojiPicker;\n    // Méthodes toggle consolidées\n    this.toggleMethods = {\n      pinnedMessages: () => this.showPinnedMessages = !this.showPinnedMessages,\n      searchBar: () => {\n        this.togglePanel('search');\n        if (!this.showSearchBar) this.clearSearch();\n      },\n      statusSelector: () => this.togglePanel('status'),\n      notificationSettings: () => this.showNotificationSettings = !this.showNotificationSettings,\n      userStatusPanel: () => this.showUserStatusPanel = !this.showUserStatusPanel,\n      callMinimize: () => this.isCallMinimized = !this.isCallMinimized,\n      callHistoryPanel: () => this.showCallHistoryPanel = !this.showCallHistoryPanel,\n      callStatsPanel: () => this.showCallStatsPanel = !this.showCallStatsPanel,\n      voiceMessagesPanel: () => this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel\n    };\n    this.togglePinnedMessages = this.toggleMethods.pinnedMessages;\n    this.toggleSearchBar = this.toggleMethods.searchBar;\n    this.toggleStatusSelector = this.toggleMethods.statusSelector;\n    this.toggleNotificationSettings = this.toggleMethods.notificationSettings;\n    this.toggleUserStatusPanel = this.toggleMethods.userStatusPanel;\n    this.toggleCallMinimize = this.toggleMethods.callMinimize;\n    this.toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;\n    this.toggleCallStatsPanel = this.toggleMethods.callStatsPanel;\n    this.toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;\n    this.conversationMethods = {\n      showInfo: () => this.showDevelopmentFeature('Informations de conversation'),\n      showSettings: () => this.showDevelopmentFeature('Paramètres de conversation'),\n      clear: () => {\n        if (!this.conversation?.id || this.messages.length === 0) {\n          this.toastService.showWarning('Aucune conversation à vider');\n          return;\n        }\n        if (confirm('Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.')) {\n          this.messages = [];\n          this.showMainMenu = false;\n          this.toastService.showSuccess('Conversation vidée avec succès');\n        }\n      },\n      export: () => {\n        if (!this.conversation?.id || this.messages.length === 0) {\n          this.toastService.showWarning('Aucune conversation à exporter');\n          return;\n        }\n        const conversationName = this.conversation.isGroup ? this.conversation.groupName || 'Groupe sans nom' : this.otherParticipant?.username || 'Conversation privée';\n        const exportData = {\n          conversation: {\n            id: this.conversation.id,\n            name: conversationName,\n            isGroup: this.conversation.isGroup,\n            participants: this.conversation.participants,\n            createdAt: this.conversation.createdAt\n          },\n          messages: this.messages.map(msg => ({\n            id: msg.id,\n            content: msg.content,\n            sender: msg.sender,\n            timestamp: msg.timestamp,\n            type: msg.type\n          })),\n          exportedAt: new Date().toISOString(),\n          exportedBy: this.currentUserId\n        };\n        const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n          type: 'application/json'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        const safeFileName = conversationName.replace(/[^a-z0-9]/gi, '_').toLowerCase();\n        const dateStr = new Date().toISOString().split('T')[0];\n        link.download = `conversation-${safeFileName}-${dateStr}.json`;\n        link.click();\n        window.URL.revokeObjectURL(url);\n        this.showMainMenu = false;\n        this.toastService.showSuccess('Conversation exportée avec succès');\n      }\n    };\n    // Template methods\n    this.toggleConversationInfo = this.conversationMethods.showInfo;\n    this.toggleConversationSettings = this.conversationMethods.showSettings;\n    this.clearConversation = this.conversationMethods.clear;\n    this.exportConversation = this.conversationMethods.export;\n    // Service - méthodes optimisées\n    this.s = {\n      formatTime: t => this.MessageService.formatMessageTime(t),\n      formatActive: t => this.MessageService.formatLastActive(t),\n      formatDate: t => this.MessageService.formatMessageDate(t),\n      showDateHeader: i => this.MessageService.shouldShowDateHeader(this.messages, i),\n      getType: m => this.MessageService.getMessageType(m),\n      hasImage: m => this.MessageService.hasImage(m),\n      isVoice: m => this.MessageService.isVoiceMessage(m),\n      getVoiceUrl: m => this.MessageService.getVoiceMessageUrl(m),\n      getVoiceDuration: m => this.MessageService.getVoiceMessageDuration(m),\n      getVoiceHeight: i => this.MessageService.getVoiceBarHeight(i),\n      formatVoice: s => this.MessageService.formatVoiceDuration(s),\n      getImageUrl: m => this.MessageService.getImageUrl(m),\n      getTypeClass: m => this.MessageService.getMessageTypeClass(m, this.currentUserId)\n    };\n    // Méthodes exposées optimisées\n    this.formatMessageTime = this.s.formatTime;\n    this.formatLastActive = this.s.formatActive;\n    this.formatMessageDate = this.s.formatDate;\n    this.shouldShowDateHeader = this.s.showDateHeader;\n    this.getMessageType = this.s.getType;\n    this.hasImage = this.s.hasImage;\n    this.isVoiceMessage = this.s.isVoice;\n    this.getVoiceMessageUrl = this.s.getVoiceUrl;\n    this.getVoiceMessageDuration = this.s.getVoiceDuration;\n    this.getVoiceBarHeight = this.s.getVoiceHeight;\n    this.formatVoiceDuration = this.s.formatVoice;\n    this.getImageUrl = this.s.getImageUrl;\n    this.getMessageTypeClass = this.s.getTypeClass;\n    // Indicateurs de chargement consolidés\n    this.loadingIndicatorMethods = {\n      show: () => {\n        if (!document.getElementById('message-loading-indicator')) {\n          const indicator = document.createElement('div');\n          indicator.id = 'message-loading-indicator';\n          indicator.className = 'text-center py-2 text-gray-500 text-sm';\n          indicator.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n          this.messagesContainer?.nativeElement?.prepend(indicator);\n        }\n      },\n      hide: () => {\n        const indicator = document.getElementById('message-loading-indicator');\n        indicator?.parentNode?.removeChild(indicator);\n      }\n    };\n    this.showLoadingIndicator = this.loadingIndicatorMethods.show;\n    this.hideLoadingIndicator = this.loadingIndicatorMethods.hide;\n    // Méthodes d'enregistrement vocal consolidées\n    this.voiceRecordingMethods = {\n      toggle: () => {\n        this.isRecordingVoice = !this.isRecordingVoice;\n        if (!this.isRecordingVoice) this.voiceRecordingDuration = 0;\n      },\n      complete: audioBlob => {\n        if (!this.conversation?.id && !this.otherParticipant?.id) {\n          this.toastService.showError('No conversation or recipient selected');\n          this.isRecordingVoice = false;\n          return;\n        }\n        const receiverId = this.otherParticipant?.id || '';\n        this.MessageService.sendVoiceMessage(receiverId, audioBlob, this.conversation?.id, this.voiceRecordingDuration).subscribe({\n          next: message => {\n            this.isRecordingVoice = false;\n            this.voiceRecordingDuration = 0;\n            this.scrollToBottom(true);\n          },\n          error: error => {\n            this.toastService.showError('Failed to send voice message');\n            this.isRecordingVoice = false;\n          }\n        });\n      },\n      cancel: () => {\n        this.isRecordingVoice = false;\n        this.voiceRecordingDuration = 0;\n      }\n    };\n    this.toggleVoiceRecording = this.voiceRecordingMethods.toggle;\n    this.onVoiceRecordingComplete = this.voiceRecordingMethods.complete;\n    this.onVoiceRecordingCancelled = this.voiceRecordingMethods.cancel;\n    // Méthodes d'appel consolidées\n    this.callMethods = {\n      initiate: type => {\n        if (!this.otherParticipant?.id) return;\n        this.MessageService.initiateCall(this.otherParticipant.id, type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO, this.conversation?.id).subscribe({\n          next: call => {},\n          error: () => this.toastService.showError(this.c.error)\n        });\n      },\n      accept: () => {\n        if (!this.incomingCall) return;\n        this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n          next: call => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n            this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n            this.callQuality = 'connecting';\n            this.toastService.showSuccess('Appel connecté');\n          },\n          error: () => {\n            this.toastService.showError(this.c.error);\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        });\n      },\n      reject: () => {\n        if (!this.incomingCall) return;\n        this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n          next: call => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          },\n          error: error => {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        });\n      },\n      end: () => {\n        const sub = this.MessageService.activeCall$.subscribe(call => {\n          if (call) {\n            this.MessageService.endCall(call.id).subscribe({\n              next: call => {},\n              error: error => {}\n            });\n          }\n        });\n        sub.unsubscribe();\n      }\n    };\n    this.initiateCall = this.callMethods.initiate;\n    this.acceptCall = this.callMethods.accept;\n    this.rejectCall = this.callMethods.reject;\n    this.endCall = this.callMethods.end;\n    // Méthodes de contrôle d'appel consolidées\n    this.callControlMethods = {\n      toggleMute: () => {\n        this.isCallMuted = !this.isCallMuted;\n        this.callControlMethods.updateMedia();\n        this.toastService.showInfo(this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé');\n      },\n      toggleVideo: () => {\n        this.isVideoEnabled = !this.isVideoEnabled;\n        this.callControlMethods.updateMedia();\n        this.toastService.showInfo(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n      },\n      updateMedia: () => {\n        this.MessageService.toggleMedia(this.activeCall?.id, !this.isCallMuted, this.isVideoEnabled).subscribe({\n          next: () => {},\n          error: error => console.error('Erreur lors de la mise à jour des médias:', error)\n        });\n      }\n    };\n    this.toggleCallMute = this.callControlMethods.toggleMute;\n    this.toggleCallVideo = this.callControlMethods.toggleVideo;\n    this.updateCallMedia = this.callControlMethods.updateMedia;\n    // Méthodes de timer consolidées\n    this.timerMethods = {\n      startCallTimer: () => {\n        this.callDuration = 0;\n        this.callTimer = setInterval(() => {\n          this.callDuration++;\n          if (this.callDuration === 3 && this.callQuality === 'connecting') {\n            this.callQuality = 'excellent';\n          }\n        }, 1000);\n      },\n      stopCallTimer: () => {\n        if (this.callTimer) {\n          clearInterval(this.callTimer);\n          this.callTimer = null;\n        }\n      },\n      resetCallState: () => this.callDuration = 0\n    };\n    this.startCallTimerMethod = this.timerMethods.startCallTimer;\n    this.stopCallTimerMethod = this.timerMethods.stopCallTimer;\n    this.resetCallStateMethod = this.timerMethods.resetCallState;\n    // Notifications\n    // Méthode de basculement de notification consolidée\n    this.notificationToggleMethod = {\n      togglePanel: () => {\n        this.togglePanel('notification');\n        if (this.showNotificationPanel) {\n          this.loadNotifications();\n        }\n      }\n    };\n    this.toggleNotificationPanel = this.notificationToggleMethod.togglePanel;\n    // Méthodes de notification consolidées\n    this.notificationMethods = {\n      loadMore: () => this.loadNotifications(),\n      updateCount: () => this.unreadNotificationCount = this.notifications.filter(n => !n.isRead).length,\n      getFiltered: () => this.notifications,\n      toggleSelection: notificationId => {\n        if (this.selectedNotifications.has(notificationId)) {\n          this.selectedNotifications.delete(notificationId);\n        } else {\n          this.selectedNotifications.add(notificationId);\n        }\n      },\n      toggleSelectAll: () => {\n        const filteredNotifications = this.notificationMethods.getFiltered();\n        const allSelected = filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n        if (allSelected) {\n          filteredNotifications.forEach(n => this.selectedNotifications.delete(n.id));\n        } else {\n          filteredNotifications.forEach(n => this.selectedNotifications.add(n.id));\n        }\n      },\n      areAllSelected: () => {\n        const filteredNotifications = this.notificationMethods.getFiltered();\n        return filteredNotifications.length > 0 && filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n      }\n    };\n    this.loadMoreNotifications = this.notificationMethods.loadMore;\n    this.updateNotificationCount = this.notificationMethods.updateCount;\n    this.getFilteredNotifications = this.notificationMethods.getFiltered;\n    this.toggleNotificationSelection = this.notificationMethods.toggleSelection;\n    this.toggleSelectAllNotifications = this.notificationMethods.toggleSelectAll;\n    this.areAllNotificationsSelected = this.notificationMethods.areAllSelected;\n    // Méthodes de marquage consolidées\n    this.markingMethods = {\n      markSelected: () => {\n        const selectedIds = Array.from(this.selectedNotifications);\n        if (selectedIds.length === 0) {\n          this.toastService.showWarning('Aucune notification sélectionnée');\n          return;\n        }\n        this.markingMethods.markAsRead(selectedIds, () => {\n          this.selectedNotifications.clear();\n          this.toastService.showSuccess(`${selectedIds.length} notification(s) marquée(s) comme lue(s)`);\n        });\n      },\n      markAll: () => {\n        const unreadNotifications = this.notifications.filter(n => !n.isRead);\n        if (unreadNotifications.length === 0) {\n          this.toastService.showInfo('Aucune notification non lue');\n          return;\n        }\n        const unreadIds = unreadNotifications.map(n => n.id);\n        this.markingMethods.markAsRead(unreadIds, () => {\n          this.toastService.showSuccess('Toutes les notifications ont été marquées comme lues');\n        });\n      },\n      markAsRead: (ids, onSuccess) => {\n        const markSub = this.MessageService.markAsRead(ids).subscribe({\n          next: result => {\n            this.notifications = this.notifications.map(n => ids.includes(n.id) ? {\n              ...n,\n              isRead: true,\n              readAt: new Date()\n            } : n);\n            this.updateNotificationCount();\n            onSuccess();\n          },\n          error: error => {\n            this.toastService.showError('Erreur lors du marquage des notifications');\n          }\n        });\n        this.subscriptions.add(markSub);\n      }\n    };\n    this.markSelectedAsRead = this.markingMethods.markSelected;\n    this.markAllAsRead = this.markingMethods.markAll;\n    // Méthodes de suppression de notifications consolidées\n    this.notificationDeleteMethods = {\n      showDeleteSelectedConfirmation: () => {\n        if (this.selectedNotifications.size === 0) {\n          this.toastService.showWarning('Aucune notification sélectionnée');\n          return;\n        }\n        this.showDeleteConfirmModal = true;\n      },\n      deleteSelected: () => {\n        const selectedIds = Array.from(this.selectedNotifications);\n        if (selectedIds.length === 0) return;\n        this.isDeletingNotifications = true;\n        this.showDeleteConfirmModal = false;\n        const deleteSub = this.MessageService.deleteMultipleNotifications(selectedIds).subscribe({\n          next: result => {\n            this.notifications = this.notifications.filter(n => !selectedIds.includes(n.id));\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(`${result.count} notification(s) supprimée(s)`);\n          },\n          error: error => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError('Erreur lors de la suppression des notifications');\n          }\n        });\n        this.subscriptions.add(deleteSub);\n      },\n      deleteOne: notificationId => {\n        const deleteSub = this.MessageService.deleteNotification(notificationId).subscribe({\n          next: result => {\n            this.notifications = this.notifications.filter(n => n.id !== notificationId);\n            this.selectedNotifications.delete(notificationId);\n            this.updateNotificationCount();\n            this.toastService.showSuccess('Notification supprimée');\n          },\n          error: error => {\n            this.toastService.showError('Erreur lors de la suppression de la notification');\n          }\n        });\n        this.subscriptions.add(deleteSub);\n      },\n      deleteAll: () => {\n        if (this.notifications.length === 0) return;\n        if (!confirm('Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.')) {\n          return;\n        }\n        this.isDeletingNotifications = true;\n        const deleteAllSub = this.MessageService.deleteAllNotifications().subscribe({\n          next: result => {\n            this.notifications = [];\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(`${result.count} notifications supprimées avec succès`);\n          },\n          error: error => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError('Erreur lors de la suppression de toutes les notifications');\n          }\n        });\n        this.subscriptions.add(deleteAllSub);\n      },\n      cancel: () => this.showDeleteConfirmModal = false\n    };\n    this.showDeleteSelectedConfirmation = this.notificationDeleteMethods.showDeleteSelectedConfirmation;\n    this.deleteSelectedNotifications = this.notificationDeleteMethods.deleteSelected;\n    this.deleteNotification = this.notificationDeleteMethods.deleteOne;\n    this.deleteAllNotifications = this.notificationDeleteMethods.deleteAll;\n    this.cancelDeleteNotifications = this.notificationDeleteMethods.cancel;\n    // Méthodes utilitaires de notification consolidées\n    this.notificationUtilMethods = {\n      formatDate: date => this.MessageService.formatLastActive(date),\n      getIcon: type => this.c.notifications[type]?.icon || 'fas fa-bell',\n      getColor: type => this.c.notifications[type]?.color || 'text-cyan-500',\n      trackById: (index, notification) => this.c.trackById(0, notification)\n    };\n    this.formatNotificationDate = this.notificationUtilMethods.formatDate;\n    this.getNotificationIcon = this.notificationUtilMethods.getIcon;\n    this.getNotificationColor = this.notificationUtilMethods.getColor;\n    this.trackByNotificationId = this.notificationUtilMethods.trackById;\n    // Méthodes de gestion des panneaux consolidées\n    this.panelMethods = {\n      getActivePanels: () => {\n        const panels = [];\n        if (this.showUserStatusPanel) panels.push({\n          key: 'userStatus',\n          title: 'Utilisateurs',\n          icon: 'fas fa-users',\n          closeAction: () => this.showUserStatusPanel = false\n        });\n        if (this.showCallHistoryPanel) panels.push({\n          key: 'callHistory',\n          title: 'Historique des appels',\n          icon: 'fas fa-history',\n          closeAction: () => this.showCallHistoryPanel = false\n        });\n        if (this.showCallStatsPanel) panels.push({\n          key: 'callStats',\n          title: \"Statistiques d'appels\",\n          icon: 'fas fa-chart-bar',\n          closeAction: () => this.showCallStatsPanel = false\n        });\n        if (this.showVoiceMessagesPanel) panels.push({\n          key: 'voiceMessages',\n          title: 'Messages vocaux',\n          icon: 'fas fa-microphone',\n          closeAction: () => this.showVoiceMessagesPanel = false\n        });\n        return panels;\n      },\n      getStatusOptions: () => Object.entries(this.c.status).map(([key, config]) => ({\n        key,\n        ...config\n      })),\n      getThemeOptions: () => this.c.themes\n    };\n    this.getActivePanels = this.panelMethods.getActivePanels;\n    this.getStatusOptions = this.panelMethods.getStatusOptions;\n    this.getThemeOptions = this.panelMethods.getThemeOptions;\n    // Méthodes de statut simplifiées\n    this.statusMethods = {\n      getText: status => this.c.status[status]?.text || 'Inconnu',\n      getColor: status => this.c.status[status]?.color || 'text-gray-400',\n      getIcon: status => this.c.status[status]?.icon || 'fas fa-question-circle'\n    };\n    this.getStatusText = this.statusMethods.getText;\n    this.getStatusColor = this.statusMethods.getColor;\n    this.getStatusIcon = this.statusMethods.getIcon;\n    // Méthodes utilitaires consolidées\n    this.utilityMethods = {\n      formatLastSeen: lastActive => lastActive ? this.MessageService.formatLastActive(lastActive) : 'Jamais vu',\n      getOnlineUsersCount: () => Array.from(this.onlineUsers.values()).filter(user => user.isOnline).length,\n      getFilteredUsers: () => Array.from(this.onlineUsers.values()),\n      setStatusFilter: filter => this.statusFilterType = filter\n    };\n    this.formatLastSeen = this.utilityMethods.formatLastSeen;\n    this.getOnlineUsersCount = this.utilityMethods.getOnlineUsersCount;\n    this.setStatusFilter = this.utilityMethods.setStatusFilter;\n    this.getFilteredUsers = this.utilityMethods.getFilteredUsers;\n    // Méthodes de réponse et transfert consolidées\n    this.replyForwardMethods = {\n      startReply: message => this.replyingToMessage = message,\n      cancelReply: () => this.replyingToMessage = null,\n      openForwardModal: message => {\n        this.forwardingMessage = message;\n        this.showForwardModal = true;\n      },\n      closeForwardModal: () => {\n        this.showForwardModal = false;\n        this.forwardingMessage = null;\n        this.selectedConversations = [];\n      }\n    };\n    this.startReplyToMessage = this.replyForwardMethods.startReply;\n    this.cancelReply = this.replyForwardMethods.cancelReply;\n    this.openForwardModal = this.replyForwardMethods.openForwardModal;\n    this.closeForwardModal = this.replyForwardMethods.closeForwardModal;\n    // Messages - méthodes consolidées\n    this.messageMethods = {\n      getPinIcon: message => this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack',\n      getPinDisplayText: message => this.isMessagePinned(message) ? 'Désépingler' : 'Épingler',\n      canEditMessage: message => message.sender?.id === this.currentUserId,\n      isMessagePinned: message => message.isPinned || false\n    };\n    this.getPinIcon = this.messageMethods.getPinIcon;\n    this.getPinDisplayText = this.messageMethods.getPinDisplayText;\n    this.canEditMessage = this.messageMethods.canEditMessage;\n    this.isMessagePinned = this.messageMethods.isMessagePinned;\n    // Méthodes d'édition consolidées\n    this.editMethods = {\n      startEditMessage: message => {\n        this.editingMessageId = message.id;\n        this.editingContent = message.content;\n      },\n      cancelEditMessage: () => {\n        this.editingMessageId = null;\n        this.editingContent = '';\n      },\n      saveEditMessage: messageId => this.editMethods.cancelEditMessage(),\n      onEditKeyPress: (event, messageId) => {\n        if (event.key === 'Enter' && !event.shiftKey) {\n          event.preventDefault();\n          this.editMethods.saveEditMessage(messageId);\n        } else if (event.key === 'Escape') {\n          this.editMethods.cancelEditMessage();\n        }\n      }\n    };\n    this.startEditMessage = this.editMethods.startEditMessage;\n    this.cancelEditMessage = this.editMethods.cancelEditMessage;\n    this.saveEditMessage = this.editMethods.saveEditMessage;\n    this.onEditKeyPress = this.editMethods.onEditKeyPress;\n    // Utilitaires d'appel consolidées\n    this.callUtilities = {\n      getCallStatusColor: status => this.c.callStatusColors[status] || 'text-gray-500',\n      getCallTypeIcon: type => type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',\n      formatCallDuration: duration => {\n        if (!duration) return '00:00';\n        const minutes = Math.floor(duration / 60);\n        const seconds = duration % 60;\n        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      },\n      formatCallDate: timestamp => this.MessageService.formatMessageDate(timestamp)\n    };\n    this.getCallStatusColor = this.callUtilities.getCallStatusColor;\n    this.getCallTypeIcon = this.callUtilities.getCallTypeIcon;\n    this.formatCallDuration = this.callUtilities.formatCallDuration;\n    this.formatCallDate = this.callUtilities.formatCallDate;\n    // Méthodes d'événements consolidées\n    this.eventMethods = {\n      onDocumentClick: event => {\n        const target = event.target;\n        const closeConfigs = [{\n          selectors: ['.theme-selector-menu', '.btn-theme'],\n          property: 'showThemeSelector'\n        }, {\n          selectors: ['.emoji-picker', '.btn-emoji'],\n          property: 'showEmojiPicker'\n        }];\n        closeConfigs.forEach(config => {\n          const isClickOutside = !config.selectors.some(selector => target.closest(selector));\n          if (isClickOutside) {\n            this[config.property] = false;\n          }\n        });\n      },\n      confirmDeleteMessage: messageId => {\n        this.showDeleteConfirm[messageId] = false;\n      }\n    };\n    this.onDocumentClick = this.eventMethods.onDocumentClick;\n    this.confirmDeleteMessage = this.eventMethods.confirmDeleteMessage;\n    // Méthodes de réaction consolidées\n    this.reactionMethods = {\n      getUniqueReactions: message => message.reactions || [],\n      onReactionClick: (messageId, emoji) => {\n        // Implémentation des réactions\n      },\n      hasUserReacted: (message, emoji) => false\n    };\n    this.getUniqueReactions = this.reactionMethods.getUniqueReactions;\n    this.onReactionClick = this.reactionMethods.onReactionClick;\n    this.hasUserReacted = this.reactionMethods.hasUserReacted;\n    // Méthodes de conversation consolidées\n    this.conversationSelectionMethods = {\n      areAllSelected: () => this.selectedConversations.length === this.availableConversations.length,\n      selectAll: () => this.selectedConversations = this.availableConversations.map(c => c.id),\n      deselectAll: () => this.selectedConversations = [],\n      toggle: conversationId => {\n        const index = this.selectedConversations.indexOf(conversationId);\n        index > -1 ? this.selectedConversations.splice(index, 1) : this.selectedConversations.push(conversationId);\n      },\n      isSelected: conversationId => this.selectedConversations.includes(conversationId),\n      getDisplayImage: conversation => conversation.image || 'assets/images/default-avatar.png',\n      getDisplayName: conversation => conversation.name || 'Conversation',\n      forwardMessage: () => {\n        this.isForwarding = true;\n        setTimeout(() => {\n          this.isForwarding = false;\n          this.closeForwardModal();\n        }, 1000);\n      }\n    };\n    this.areAllConversationsSelected = this.conversationSelectionMethods.areAllSelected;\n    this.selectAllConversations = this.conversationSelectionMethods.selectAll;\n    this.deselectAllConversations = this.conversationSelectionMethods.deselectAll;\n    this.toggleConversationSelection = this.conversationSelectionMethods.toggle;\n    this.isConversationSelected = this.conversationSelectionMethods.isSelected;\n    this.getConversationDisplayImage = this.conversationSelectionMethods.getDisplayImage;\n    this.getConversationDisplayName = this.conversationSelectionMethods.getDisplayName;\n    this.forwardMessage = this.conversationSelectionMethods.forwardMessage;\n    // Méthodes de notification simplifiées consolidées\n    this.simpleNotificationMethods = {\n      onCallMouseMove: () => this.showCallControls = true,\n      saveNotificationSettings: () => {},\n      setNotificationFilter: filter => this.notificationFilter = filter\n    };\n    this.onCallMouseMove = this.simpleNotificationMethods.onCallMouseMove;\n    this.saveNotificationSettings = this.simpleNotificationMethods.saveNotificationSettings;\n    this.setNotificationFilter = this.simpleNotificationMethods.setNotificationFilter;\n    // Méthodes de recherche consolidées\n    this.searchMethods = {\n      onInput: event => {\n        this.searchQuery = event.target.value;\n        this.searchQuery.length >= 2 ? this.searchMethods.perform() : this.searchMethods.clear();\n      },\n      onKeyPress: event => {\n        if (event.key === 'Enter') {\n          this.searchMethods.perform();\n        } else if (event.key === 'Escape') {\n          this.searchMethods.clear();\n        }\n      },\n      perform: () => {\n        this.isSearching = true;\n        this.searchMode = true;\n        setTimeout(() => {\n          this.searchResults = this.messages.filter(m => m.content?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n          this.isSearching = false;\n        }, 500);\n      },\n      clear: () => {\n        this.searchQuery = '';\n        this.searchResults = [];\n        this.isSearching = false;\n        this.searchMode = false;\n      }\n    };\n    this.onSearchInput = this.searchMethods.onInput;\n    this.onSearchKeyPress = this.searchMethods.onKeyPress;\n    this.performSearch = this.searchMethods.perform;\n    this.clearSearch = this.searchMethods.clear;\n    // Méthodes utilitaires finales consolidées\n    this.finalUtilityMethods = {\n      navigateToMessage: messageId => {\n        // Navigation vers un message spécifique\n      },\n      scrollToPinnedMessage: messageId => {\n        // Défilement vers un message épinglé\n      },\n      getPinnedMessagesCount: () => this.pinnedMessages.length\n    };\n    this.navigateToMessage = this.finalUtilityMethods.navigateToMessage;\n    this.scrollToPinnedMessage = this.finalUtilityMethods.scrollToPinnedMessage;\n    this.getPinnedMessagesCount = this.finalUtilityMethods.getPinnedMessagesCount;\n    // Méthodes de recherche et réaction consolidées\n    this.searchAndReactionMethods = {\n      highlightSearchTerms: (content, query) => {\n        if (!query) return content;\n        const regex = new RegExp(`(${query})`, 'gi');\n        return content.replace(regex, '<mark>$1</mark>');\n      },\n      toggleReactionPicker: messageId => this.showReactionPicker[messageId] = !this.showReactionPicker[messageId],\n      reactToMessage: (messageId, emoji) => this.showReactionPicker[messageId] = false,\n      toggleMessageOptions: messageId => this.showMessageOptions[messageId] = !this.showMessageOptions[messageId]\n    };\n    this.highlightSearchTerms = this.searchAndReactionMethods.highlightSearchTerms;\n    this.toggleReactionPicker = this.searchAndReactionMethods.toggleReactionPicker;\n    this.reactToMessage = this.searchAndReactionMethods.reactToMessage;\n    this.toggleMessageOptions = this.searchAndReactionMethods.toggleMessageOptions;\n    // Confirmations consolidées\n    this.confirmationMethods = {\n      showPinConfirmation: messageId => this.showPinConfirm[messageId] = true,\n      cancelPinConfirmation: messageId => this.showPinConfirm[messageId] = false,\n      showDeleteConfirmation: messageId => this.showDeleteConfirm[messageId] = true,\n      cancelDeleteMessage: messageId => this.showDeleteConfirm[messageId] = false\n    };\n    this.showPinConfirmation = this.confirmationMethods.showPinConfirmation;\n    this.cancelPinConfirmation = this.confirmationMethods.cancelPinConfirmation;\n    this.showDeleteConfirmation = this.confirmationMethods.showDeleteConfirmation;\n    this.cancelDeleteMessage = this.confirmationMethods.cancelDeleteMessage;\n    // Méthodes de nettoyage optimisées\n    this.cleanup = {\n      clearTimeouts: () => [this.typingTimeout, this.autoAwayTimeout, this.callTimer].forEach(t => t && clearTimeout(t)),\n      setUserOffline: () => this.currentUserId && this.MessageService.setUserOffline(this.currentUserId).subscribe(),\n      stopTypingIndicator: () => {\n        if (this.isCurrentlyTyping && this.conversation?.id) {\n          this.isCurrentlyTyping = false;\n          clearTimeout(this.typingTimer);\n          this.MessageService.stopTyping(this.conversation.id).subscribe({\n            next: () => {},\n            error: error => {}\n          });\n        }\n      }\n    };\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]]\n    });\n  }\n  ngOnInit() {\n    this.currentUserId = this.authService.getCurrentUserId();\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n    }\n    this.subscribeToNotifications();\n    this.subscribeToUserStatus();\n    this.initializeUserStatus();\n    this.startActivityTracking();\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n    const routeSub = this.route.params.pipe(filter(params => params['id']), distinctUntilChanged(), switchMap(params => {\n      this.loading = true;\n      this.messages = [];\n      this.currentPage = 1;\n      this.hasMoreMessages = true;\n      return this.MessageService.getConversation(params['id'], this.MAX_MESSAGES_TO_LOAD, this.currentPage);\n    })).subscribe({\n      next: conversation => {\n        this.handleConversationLoaded(conversation);\n      },\n      error: error => {\n        this.handleError('Failed to load conversation', error);\n      }\n    });\n    this.subscriptions.add(routeSub);\n  }\n  // Gestion centralisée des erreurs\n  handleError(message, error, resetLoading = true) {\n    console.error('MessageChat', message, error);\n    if (resetLoading) {\n      this.loading = false;\n      this.isUploading = false;\n      this.isLoadingMore = false;\n    }\n    this.error = error;\n    this.toastService.showError(message);\n  }\n  // Gestion centralisée des succès\n  handleSuccess(message, callback) {\n    if (message) {\n      this.toastService.showSuccess(message);\n    }\n    if (callback) {\n      callback();\n    }\n  }\n  handleConversationLoaded(conversation) {\n    this.conversation = conversation;\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n      this.messages = [];\n    } else {\n      const conversationMessages = [...(conversation?.messages || [])];\n      conversationMessages.sort((a, b) => {\n        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n        return timeA - timeB;\n      });\n      this.messages = conversationMessages;\n    }\n    this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n    this.markMessagesAsRead();\n    if (this.conversation?.id) {\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub = this.MessageService.subscribeToConversationUpdates(conversationId).subscribe({\n      next: updatedConversation => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages ? [...updatedConversation.messages] : [];\n        this.scrollToBottom();\n      },\n      error: error => {\n        this.toastService.showError('Connection to conversation updates lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToNewMessages(conversationId) {\n    const sub = this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n      next: newMessage => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n            const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          setTimeout(() => this.scrollToBottom(), 100);\n          if (newMessage.sender?.id !== this.currentUserId && newMessage.sender?._id !== this.currentUserId) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: error => {\n        this.toastService.showError('Connection to new messages lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToTypingIndicators(conversationId) {\n    const sub = this.MessageService.subscribeToTypingIndicator(conversationId).subscribe({\n      next: event => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  markMessagesAsRead() {\n    const unreadMessages = this.messages.filter(msg => !msg.isRead && (msg.receiver?.id === this.currentUserId || msg.receiver?._id === this.currentUserId));\n    unreadMessages.forEach(msg => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: error => {}\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (!file) return;\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError('Invalid file type. Only images, PDFs and Word docs are allowed');\n      return;\n    }\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n  removeAttachment() {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n  // Frappe\n  onTyping() {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n    const conversationId = this.conversation.id;\n    clearTimeout(this.typingTimer);\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {},\n        error: error => {}\n      });\n    }\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {},\n          error: error => {}\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n  // Panneaux\n  togglePanel(panelName, closeOthers = true) {\n    const panels = {\n      theme: 'showThemeSelector',\n      menu: 'showMainMenu',\n      emoji: 'showEmojiPicker',\n      notification: 'showNotificationPanel',\n      search: 'showSearchBar',\n      status: 'showStatusSelector'\n    };\n    const currentPanel = panels[panelName];\n    if (currentPanel) {\n      this[currentPanel] = !this[currentPanel];\n      if (closeOthers && this[currentPanel]) {\n        Object.values(panels).forEach(panel => {\n          if (panel !== currentPanel) {\n            this[panel] = false;\n          }\n        });\n      }\n    }\n  }\n  changeTheme(theme) {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    localStorage.setItem('chat-theme', theme);\n  }\n  // Conversation - méthode utilitaire\n  showDevelopmentFeature(featureName) {\n    this.showMainMenu = false;\n    this.toastService.showInfo(`${featureName} en cours de développement`);\n  }\n  sendMessage() {\n    if (this.messageForm.invalid && !this.selectedFile || !this.currentUserId || !this.otherParticipant?.id) {\n      return;\n    }\n    this.cleanup.stopTypingIndicator();\n    const content = this.messageForm.get('content')?.value;\n    const tempMessage = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient'\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true\n    };\n    if (this.selectedFile) {\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n        if (this.previewUrl) {\n          tempMessage.attachments = [{\n            id: 'temp-attachment',\n            url: this.previewUrl ? this.previewUrl.toString() : '',\n            type: MessageType.IMAGE,\n            name: this.selectedFile.name,\n            size: this.selectedFile.size\n          }];\n        }\n      }\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n    this.messages = [...this.messages, tempMessage];\n    const fileToSend = this.selectedFile;\n    this.messageForm.reset();\n    this.removeAttachment();\n    setTimeout(() => this.scrollToBottom(true), 50);\n    this.isUploading = true;\n    const sendSub = this.MessageService.sendMessage(this.otherParticipant.id, content, fileToSend || undefined, MessageType.TEXT, this.conversation?.id).subscribe({\n      next: message => {\n        this.updateMessageState(tempMessage.id, message);\n        this.isUploading = false;\n      },\n      error: error => {\n        this.updateMessageState(tempMessage.id, null, true);\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      }\n    });\n    this.subscriptions.add(sendSub);\n  }\n  // Méthode consolidée pour mettre à jour l'état des messages\n  updateMessageState(tempId, newMessage, isError = false) {\n    this.messages = this.messages.map(msg => {\n      if (msg.id === tempId) {\n        if (newMessage) {\n          return newMessage;\n        } else if (isError) {\n          return {\n            ...msg,\n            isPending: false,\n            isError: true\n          };\n        }\n      }\n      return msg;\n    });\n  }\n  // Défilement\n  onScroll(event) {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n    if (scrollTop < 50 && !this.isLoadingMore && this.conversation?.id && this.hasMoreMessages) {\n      this.showLoadingIndicator();\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n      this.isLoadingMore = true;\n      this.loadMoreMessages();\n      // Maintenir la position de défilement\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(firstVisibleMessage.id);\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({\n                block: 'center'\n              });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n  // Méthode pour trouver le premier message visible dans la vue\n  getFirstVisibleMessage() {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length) return null;\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find(m => m.id === messageId) || null;\n      }\n    }\n    return null;\n  }\n  // Méthode pour trouver un élément de message par ID\n  findMessageElement(messageId) {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(`[data-message-id=\"${messageId}\"]`);\n  }\n  // Charger plus de messages\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages) return;\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n    // Augmenter la page\n    this.currentPage++;\n    // Charger plus de messages\n    this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        if (conversation && conversation.messages && conversation.messages.length > 0) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n          // Créer un Set des IDs existants\n          const existingIds = new Set(oldMessages.map(msg => msg.id));\n          // Filtrer et trier les nouveaux messages\n          const newMessages = conversation.messages.filter(msg => !existingIds.has(msg.id)).sort((a, b) => {\n            const timeA = new Date(a.timestamp).getTime();\n            const timeB = new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages = newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: error => {\n        console.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      }\n    });\n  }\n  // Méthode utilitaire pour comparer les timestamps\n  isSameTimestamp(timestamp1, timestamp2) {\n    if (!timestamp1 || !timestamp2) return false;\n    try {\n      const time1 = timestamp1 instanceof Date ? timestamp1.getTime() : new Date(timestamp1).getTime();\n      const time2 = timestamp2 instanceof Date ? timestamp2.getTime() : new Date(timestamp2).getTime();\n      return Math.abs(time1 - time2) < 1000;\n    } catch (error) {\n      return false;\n    }\n  }\n  scrollToBottom(force = false) {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 150;\n        if (force || isScrolledToBottom) {\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth'\n          });\n        }\n      });\n    } catch (err) {\n      console.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl) {\n    window.open(imageUrl, '_blank');\n  }\n  /**\n   * Détecte les changements après chaque vérification de la vue - Optimisé\n   */\n  ngAfterViewChecked() {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n    // Détection des changements optimisée - Suppression de la vérification inutile\n    // La détection automatique d'Angular gère déjà les messages vocaux\n  }\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations() {\n    this.router.navigate(['/messages/conversations']);\n  }\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji) {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector('.whatsapp-input-field');\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  subscribeToNotifications() {\n    const notificationSub = this.MessageService.subscribeToNewNotifications().subscribe({\n      next: notification => {\n        this.notifications.unshift(notification);\n        this.updateNotificationCount();\n        this.MessageService.play('notification');\n        if (notification.type === 'NEW_MESSAGE' && notification.conversationId === this.conversation?.id) {\n          if (notification.id) {\n            this.MessageService.markAsRead([notification.id]).subscribe();\n          }\n        }\n      },\n      error: error => {}\n    });\n    this.subscriptions.add(notificationSub);\n    const notificationsListSub = this.MessageService.notifications$.subscribe({\n      next: notifications => {\n        this.notifications = notifications;\n        this.updateNotificationCount();\n      },\n      error: error => {}\n    });\n    this.subscriptions.add(notificationsListSub);\n    const notificationCountSub = this.MessageService.notificationCount$.subscribe({\n      next: count => {\n        this.unreadNotificationCount = count;\n      }\n    });\n    this.subscriptions.add(notificationCountSub);\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: call => {\n        if (call) {\n          this.incomingCall = call;\n          this.showCallModal = true;\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      }\n    });\n    this.subscriptions.add(callSub);\n    const activeCallSub = this.MessageService.activeCall$.subscribe({\n      next: call => {\n        this.activeCall = call;\n        if (call) {\n          this.showActiveCallModal = true;\n          this.startCallTimerMethod();\n        } else {\n          this.showActiveCallModal = false;\n          this.stopCallTimerMethod();\n          this.resetCallStateMethod();\n        }\n      }\n    });\n    this.subscriptions.add(activeCallSub);\n    // S'abonner aux flux vidéo locaux\n    const localStreamSub = this.MessageService.localStream$.subscribe({\n      next: stream => {\n        if (stream && this.localVideoElement) {\n          this.localVideoElement.srcObject = stream;\n        }\n      }\n    });\n    this.subscriptions.add(localStreamSub);\n    // S'abonner aux flux vidéo distants\n    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n      next: stream => {\n        if (stream && this.remoteVideoElement) {\n          this.remoteVideoElement.srcObject = stream;\n        }\n      }\n    });\n    this.subscriptions.add(remoteStreamSub);\n  }\n  /**\n   * Charge les notifications\n   */\n  loadNotifications(refresh = false) {\n    const loadSub = this.MessageService.getNotifications(refresh, 1, 20).subscribe({\n      next: notifications => {\n        if (refresh) {\n          this.notifications = notifications;\n        } else {\n          this.notifications = [...this.notifications, ...notifications];\n        }\n        this.updateNotificationCount();\n      },\n      error: error => {\n        this.toastService.showError('Erreur lors du chargement des notifications');\n      }\n    });\n    this.subscriptions.add(loadSub);\n  }\n  /**\n   * S'abonne au statut utilisateur en temps réel\n   */\n  subscribeToUserStatus() {\n    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n      next: user => {\n        this.handleUserStatusUpdate(user);\n      },\n      error: error => {\n        // Error handled silently\n      }\n    });\n    this.subscriptions.add(statusSub);\n  }\n  /**\n   * Gère la mise à jour du statut d'un utilisateur\n   */\n  handleUserStatusUpdate(user) {\n    if (!user.id) return;\n    if (user.isOnline) {\n      this.onlineUsers.set(user.id, user);\n    } else {\n      this.onlineUsers.delete(user.id);\n    }\n    if (this.otherParticipant && this.otherParticipant.id === user.id) {\n      this.otherParticipant = {\n        ...this.otherParticipant,\n        ...user\n      };\n    }\n  }\n  /**\n   * Initialise le statut de l'utilisateur actuel\n   */\n  initializeUserStatus() {\n    if (!this.currentUserId) return;\n    const setOnlineSub = this.MessageService.setUserOnline(this.currentUserId).subscribe({\n      next: user => {\n        this.currentUserStatus = 'online';\n        this.lastActivityTime = new Date();\n      },\n      error: error => {\n        console.error('MessageChat', \"Erreur lors de l'initialisation du statut\", error);\n      }\n    });\n    this.subscriptions.add(setOnlineSub);\n  }\n  /**\n   * Démarre le suivi d'activité automatique\n   */\n  startActivityTracking() {\n    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];\n    events.forEach(event => {\n      document.addEventListener(event, this.onUserActivity.bind(this), true);\n    });\n  }\n  /**\n   * Gère l'activité de l'utilisateur\n   */\n  onUserActivity() {\n    this.lastActivityTime = new Date();\n    // Réinitialiser le timer\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n    // Remettre en ligne si absent\n    if (this.currentUserStatus === 'away' || this.currentUserStatus === 'offline') {\n      this.updateUserStatus('online');\n    }\n    // Programmer la mise en absence\n    this.autoAwayTimeout = setTimeout(() => {\n      if (this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      }\n    }, this.c.delays.away);\n  }\n  /**\n   * Met à jour le statut de l'utilisateur\n   */\n  updateUserStatus(status) {\n    if (!this.currentUserId) return;\n    const previousStatus = this.currentUserStatus;\n    let updateObservable;\n    if (status === 'online') {\n      updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n    } else {\n      updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n    }\n    const updateSub = updateObservable.subscribe({\n      next: user => {\n        this.currentUserStatus = status;\n        if (status !== previousStatus) {\n          const statusText = this.getStatusText(status);\n          this.toastService.showInfo(`Statut : ${statusText}`);\n        }\n      },\n      error: error => {\n        console.error('MessageChat', 'Erreur lors de la mise à jour du statut', error);\n      }\n    });\n    this.subscriptions.add(updateSub);\n  }\n  /**\n   * Charge la liste des utilisateurs en ligne\n   */\n  loadOnlineUsers() {\n    const usersSub = this.MessageService.getAllUsers(false, undefined, 1, 50, 'username', 'asc', true).subscribe({\n      next: users => {\n        users.forEach(user => {\n          if (user.isOnline && user.id) {\n            this.onlineUsers.set(user.id, user);\n          }\n        });\n      },\n      error: error => {\n        console.error('MessageChat', 'Erreur lors du chargement des utilisateurs en ligne', error);\n      }\n    });\n    this.subscriptions.add(usersSub);\n  }\n  togglePinMessage(message) {\n    this.isPinning[message.id] = true;\n    setTimeout(() => {\n      this.isPinning[message.id] = false;\n      this.showPinConfirm[message.id] = false;\n    }, 1000);\n  }\n  ngOnDestroy() {\n    this.cleanup.clearTimeouts();\n    this.cleanup.setUserOffline();\n    this.cleanup.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n};\n__decorate([ViewChild('messagesContainer')], MessageChatComponent.prototype, \"messagesContainer\", void 0);\n__decorate([ViewChild('fileInput', {\n  static: false\n})], MessageChatComponent.prototype, \"fileInput\", void 0);\nMessageChatComponent = __decorate([Component({\n  selector: 'app-message-chat',\n  templateUrl: 'message-chat.component.html',\n  styleUrls: ['./message-chat.component.css']\n})], MessageChatComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "Validators", "Subscription", "MessageType", "CallType", "switchMap", "distinctUntilChanged", "filter", "MessageChatComponent", "availableReactions", "c", "reactions", "commonEmojis", "MessageService", "getCommonEmojis", "getHeaderActions", "class", "icon", "title", "onClick", "initiateCall", "isActive", "toggleSearchBar", "showSearchBar", "activeClass", "getPinnedMessagesCount", "togglePinnedMessages", "showPinnedMessages", "badge", "count", "animate", "toggleNotificationPanel", "showNotificationPanel", "unreadNotificationCount", "toggleCallHistoryPanel", "showCallHistoryPanel", "toggleCallStatsPanel", "showCallStatsPanel", "toggleVoiceMessagesPanel", "showVoiceMessagesPanel", "voiceMessages", "length", "constructor", "route", "authService", "fb", "statusService", "router", "toastService", "cdr", "messages", "conversation", "loading", "currentUserId", "currentUsername", "otherParticipant", "selectedFile", "previewUrl", "isUploading", "isTyping", "isRecordingVoice", "voiceRecordingDuration", "MAX_MESSAGES_PER_SIDE", "MAX_MESSAGES_TO_LOAD", "MAX_TOTAL_MESSAGES", "currentPage", "isLoadingMore", "hasMoreMessages", "subscriptions", "selectedTheme", "showThemeSelector", "showMainMenu", "showEmojiPicker", "showStatusSelector", "showNotificationSettings", "showUserStatusPanel", "incomingCall", "activeCall", "showCallModal", "showActiveCallModal", "isCallMuted", "isVideoEnabled", "callDuration", "callTimer", "notifications", "selectedNotifications", "Set", "showDeleteConfirmModal", "editingMessageId", "<PERSON><PERSON><PERSON><PERSON>", "replyingToMessage", "searchQuery", "isSearching", "searchMode", "searchResults", "pinnedMessages", "showReactionPicker", "showDeleteConfirm", "showPinConfirm", "isPinning", "showMessageOptions", "showForwardModal", "forwardingMessage", "selectedConversations", "availableConversations", "isForwarding", "isLoadingConversations", "delays", "away", "typing", "scroll", "anim", "error", "trackById", "i", "item", "id", "toString", "NEW_MESSAGE", "color", "CALL_MISSED", "SYSTEM", "status", "online", "text", "label", "description", "offline", "busy", "themes", "key", "hoverColor", "calls", "COMPLETED", "MISSED", "REJECTED", "notificationConfig", "callStatusColors", "notificationFilter", "isLoadingNotifications", "isMarkingAsRead", "isDeletingNotifications", "hasMoreNotifications", "notificationSounds", "notificationPreview", "autoMarkAsRead", "isCallMinimized", "callQuality", "showCallControls", "onlineUsers", "Map", "currentUserStatus", "lastActivityTime", "Date", "autoAwayTimeout", "isUpdatingStatus", "callHistory", "localVideoElement", "remoteVideoElement", "statusFilterType", "f", "getIcon", "t", "getFileIcon", "getType", "getFileType", "isCurrentlyTyping", "TYPING_DELAY", "TYPING_TIMEOUT", "mainToggleMethods", "themeSelector", "togglePanel", "mainMenu", "emojiPicker", "toggleThemeSelector", "toggleMainMenu", "toggleEmojiPicker", "toggleMethods", "searchBar", "clearSearch", "statusSelector", "notificationSettings", "userStatusPanel", "callMinimize", "callHistoryPanel", "callStatsPanel", "voiceMessagesPanel", "toggleStatusSelector", "toggleNotificationSettings", "toggleUserStatusPanel", "toggleCallMinimize", "conversationMethods", "showInfo", "showDevelopmentFeature", "showSettings", "clear", "showWarning", "confirm", "showSuccess", "export", "conversation<PERSON>ame", "isGroup", "groupName", "username", "exportData", "name", "participants", "createdAt", "map", "msg", "content", "sender", "timestamp", "type", "exportedAt", "toISOString", "exportedBy", "blob", "Blob", "JSON", "stringify", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "safeFileName", "replace", "toLowerCase", "dateStr", "split", "download", "click", "revokeObjectURL", "toggleConversationInfo", "toggleConversationSettings", "clearConversation", "exportConversation", "s", "formatTime", "formatMessageTime", "formatActive", "formatLastActive", "formatDate", "formatMessageDate", "showDateHeader", "shouldShowDateHeader", "m", "getMessageType", "hasImage", "isVoice", "isVoiceMessage", "getVoiceUrl", "getVoiceMessageUrl", "getVoiceDuration", "getVoiceMessageDuration", "getVoiceHeight", "getVoiceBarHeight", "formatVoice", "formatVoiceDuration", "getImageUrl", "getTypeClass", "getMessageTypeClass", "loadingIndicatorMethods", "show", "getElementById", "indicator", "className", "innerHTML", "messagesContainer", "nativeElement", "prepend", "hide", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showLoadingIndicator", "hideLoadingIndicator", "voiceRecordingMethods", "toggle", "complete", "audioBlob", "showError", "receiverId", "sendVoiceMessage", "subscribe", "next", "message", "scrollToBottom", "cancel", "toggleVoiceRecording", "onVoiceRecordingComplete", "onVoiceRecordingCancelled", "callMethods", "initiate", "AUDIO", "VIDEO", "call", "accept", "acceptCall", "reject", "rejectCall", "end", "sub", "activeCall$", "endCall", "unsubscribe", "callControlMethods", "toggleMute", "updateMedia", "toggleVideo", "toggleMedia", "console", "toggleCallMute", "toggleCallVideo", "updateCallMedia", "timerMethods", "startCallTimer", "setInterval", "stopCallTimer", "clearInterval", "resetCallState", "startCallTimerMethod", "stopCallTimerMethod", "resetCallStateMethod", "notificationToggleMethod", "loadNotifications", "notificationMethods", "loadMore", "updateCount", "n", "isRead", "getFiltered", "toggleSelection", "notificationId", "has", "delete", "add", "toggleSelectAll", "filteredNotifications", "allSelected", "every", "for<PERSON>ach", "areAllSelected", "loadMoreNotifications", "updateNotificationCount", "getFilteredNotifications", "toggleNotificationSelection", "toggleSelectAllNotifications", "areAllNotificationsSelected", "markingMethods", "markSelected", "selectedIds", "Array", "from", "mark<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "unreadNotifications", "unreadIds", "ids", "onSuccess", "mark<PERSON>ub", "result", "includes", "readAt", "markSelectedAsRead", "markAllAsRead", "notificationDeleteMethods", "showDeleteSelectedConfirmation", "size", "deleteSelected", "deleteSub", "deleteMultipleNotifications", "deleteOne", "deleteNotification", "deleteAll", "deleteAllSub", "deleteAllNotifications", "deleteSelectedNotifications", "cancelDeleteNotifications", "notificationUtilMethods", "date", "getColor", "index", "notification", "formatNotificationDate", "getNotificationIcon", "getNotificationColor", "trackByNotificationId", "panelMethods", "getActivePanels", "panels", "push", "closeAction", "getStatusOptions", "Object", "entries", "config", "getThemeOptions", "statusMethods", "getText", "getStatusText", "getStatusColor", "getStatusIcon", "utilityMethods", "formatLastSeen", "lastActive", "getOnlineUsersCount", "values", "user", "isOnline", "getFilteredUsers", "setStatus<PERSON>ilter", "replyForwardMethods", "startReply", "cancelReply", "openForwardModal", "closeForwardModal", "startReplyToMessage", "messageMethods", "getPinIcon", "isMessagePinned", "getPinDisplayText", "canEditMessage", "isPinned", "editMethods", "startEditMessage", "cancelEditMessage", "saveEditMessage", "messageId", "onEditKeyPress", "event", "shift<PERSON>ey", "preventDefault", "callUtilities", "getCallStatusColor", "getCallTypeIcon", "formatCallDuration", "duration", "minutes", "Math", "floor", "seconds", "padStart", "formatCallDate", "eventMethods", "onDocumentClick", "target", "closeConfigs", "selectors", "property", "isClickOutside", "some", "selector", "closest", "confirmDeleteMessage", "reactionMethods", "getUniqueReactions", "onReactionClick", "emoji", "hasUserReacted", "conversationSelectionMethods", "selectAll", "deselectAll", "conversationId", "indexOf", "splice", "isSelected", "getDisplayImage", "image", "getDisplayName", "forwardMessage", "setTimeout", "areAllConversationsSelected", "selectAllConversations", "deselectAllConversations", "toggleConversationSelection", "isConversationSelected", "getConversationDisplayImage", "getConversationDisplayName", "simpleNotificationMethods", "onCallMouseMove", "saveNotificationSettings", "setNotificationFilter", "searchMethods", "onInput", "value", "perform", "onKeyPress", "onSearchInput", "onSearchKeyPress", "performSearch", "finalUtilityMethods", "navigateToMessage", "scrollToPinnedMessage", "searchAndReactionMethods", "highlightSearchTerms", "query", "regex", "RegExp", "toggleReactionPicker", "reactToMessage", "toggleMessageOptions", "confirmationMethods", "showPinConfirmation", "cancelPinConfirmation", "showDeleteConfirmation", "cancelDeleteMessage", "cleanup", "clearTimeouts", "typingTimeout", "clearTimeout", "setUserOffline", "stopTypingIndicator", "typingTimer", "stopTyping", "messageForm", "group", "max<PERSON><PERSON><PERSON>", "ngOnInit", "getCurrentUserId", "savedTheme", "localStorage", "getItem", "subscribeToNotifications", "subscribeToUserStatus", "initializeUserStatus", "startActivityTracking", "addEventListener", "bind", "routeSub", "params", "pipe", "getConversation", "handleConversationLoaded", "handleError", "resetLoading", "handleSuccess", "callback", "find", "p", "_id", "conversationMessages", "sort", "a", "b", "timeA", "getTime", "timeB", "markMessagesAsRead", "subscribeToConversationUpdates", "subscribeToNewMessages", "subscribeToTypingIndicators", "updatedConversation", "newMessage", "markMessageAsRead", "subscribeToTypingIndicator", "userId", "unreadMessages", "receiver", "onFileSelected", "file", "files", "validTypes", "reader", "FileReader", "onload", "readAsDataURL", "removeAttachment", "fileInput", "onTyping", "startTyping", "panelName", "closeOthers", "theme", "menu", "search", "currentPanel", "panel", "changeTheme", "setItem", "featureName", "sendMessage", "invalid", "get", "tempMessage", "isPending", "fileType", "startsWith", "attachments", "IMAGE", "FILE", "fileToSend", "reset", "sendSub", "undefined", "TEXT", "updateMessageState", "tempId", "isError", "onScroll", "container", "scrollTop", "oldScrollHeight", "scrollHeight", "firstVisibleMessage", "getFirstVisibleMessage", "loadMoreMessages", "requestAnimationFrame", "preserveScrollPosition", "messageElement", "findMessageElement", "scrollIntoView", "block", "newScrollHeight", "scrollDiff", "messageElements", "querySelectorAll", "element", "rect", "getBoundingClientRect", "top", "bottom", "clientHeight", "getAttribute", "querySelector", "oldMessages", "existingIds", "newMessages", "slice", "isSameTimestamp", "timestamp1", "timestamp2", "time1", "time2", "abs", "force", "isScrolledToBottom", "scrollTo", "behavior", "err", "openImageFullscreen", "imageUrl", "open", "ngAfterViewChecked", "goBackToConversations", "navigate", "insert<PERSON><PERSON><PERSON>", "control", "currentValue", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputElement", "focus", "notificationSub", "subscribeToNewNotifications", "unshift", "play", "notificationsListSub", "notifications$", "notificationCountSub", "notificationCount$", "callSub", "incomingCall$", "activeCallSub", "localStreamSub", "localStream$", "stream", "srcObject", "remoteStreamSub", "remoteStream$", "refresh", "loadSub", "getNotifications", "statusSub", "handleUserStatusUpdate", "set", "setOnlineSub", "setUserOnline", "events", "onUserActivity", "updateUserStatus", "previousStatus", "updateObservable", "updateSub", "statusText", "loadOnlineUsers", "usersSub", "getAllUsers", "users", "togglePinMessage", "ngOnDestroy", "removeEventListener", "__decorate", "static", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ElementRef,\n  AfterViewChecked,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { User } from '@app/models/user.model';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport {\n  Message,\n  Conversation,\n  MessageType,\n  CallType,\n} from 'src/app/models/message.model';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { MessageService } from '@app/services/message.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: 'message-chat.component.html',\n  styleUrls: ['./message-chat.component.css'],\n})\nexport class MessageChatComponent\n  implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, AfterViewChecked\n{\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  messages: Message[] = [];\n  messageForm: FormGroup;\n  conversation: Conversation | null = null;\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  currentUsername: string = 'You';\n  otherParticipant: User | null = null;\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n  isUploading = false;\n  isTyping = false;\n  typingTimeout: any;\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n\n  private readonly MAX_MESSAGES_PER_SIDE = 5;\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private readonly MAX_TOTAL_MESSAGES = 100;\n  private currentPage = 1;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  private subscriptions = new Subscription();\n\n  // Interface\n  selectedTheme: string = 'theme-default';\n\n  // États\n  showThemeSelector = false;\n  showMainMenu = false;\n  showEmojiPicker = false;\n  showSearchBar = false;\n  showPinnedMessages = false;\n  showStatusSelector = false;\n  showNotificationPanel = false;\n  showNotificationSettings = false;\n  showUserStatusPanel = false;\n  showCallHistoryPanel = false;\n  showCallStatsPanel = false;\n  showVoiceMessagesPanel = false;\n\n  // Appels\n  incomingCall: any = null;\n  activeCall: any = null;\n  showCallModal = false;\n  showActiveCallModal = false;\n  isCallMuted = false;\n  isVideoEnabled = true;\n  callDuration = 0;\n  callTimer: any = null;\n\n  // Notifications et messages\n  notifications: any[] = [];\n  unreadNotificationCount: number = 0;\n  selectedNotifications: Set<string> = new Set();\n  showDeleteConfirmModal: boolean = false;\n  editingMessageId: string | null = null;\n  editingContent = '';\n  replyingToMessage: any = null;\n\n  // Recherche\n  searchQuery = '';\n  isSearching = false;\n  searchMode = false;\n  searchResults: any[] = [];\n\n  // Panneaux\n  pinnedMessages: any[] = [];\n  showReactionPicker: { [key: string]: boolean } = {};\n  showDeleteConfirm: { [key: string]: boolean } = {};\n  showPinConfirm: { [key: string]: boolean } = {};\n  isPinning: { [key: string]: boolean } = {};\n  showMessageOptions: { [key: string]: boolean } = {};\n\n  // Variables de transfert\n  showForwardModal = false;\n  forwardingMessage: any = null;\n  selectedConversations: string[] = [];\n  availableConversations: any[] = [];\n  isForwarding = false;\n  isLoadingConversations = false;\n\n  // Constantes optimisées\n  readonly c = {\n    reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n    delays: { away: 300000, typing: 3000, scroll: 100, anim: 300 },\n    error: 'Erreur. Veuillez réessayer.',\n    trackById: (i: number, item: any): string => item?.id || i.toString(),\n    notifications: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    status: {\n      online: {\n        text: 'En ligne',\n        color: 'text-green-500',\n        icon: 'fas fa-circle',\n        label: 'En ligne',\n        description: 'Disponible pour discuter',\n      },\n      offline: {\n        text: 'Hors ligne',\n        color: 'text-gray-500',\n        icon: 'far fa-circle',\n        label: 'Hors ligne',\n        description: 'Invisible pour tous',\n      },\n      away: {\n        text: 'Absent',\n        color: 'text-yellow-500',\n        icon: 'fas fa-clock',\n        label: 'Absent',\n        description: 'Absent temporairement',\n      },\n      busy: {\n        text: 'Occupé',\n        color: 'text-red-500',\n        icon: 'fas fa-minus-circle',\n        label: 'Occupé',\n        description: 'Ne pas déranger',\n      },\n    },\n    themes: [\n      {\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad',\n        hoverColor: '#4f5fad',\n      },\n      {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d',\n        hoverColor: '#ff6b9d',\n      },\n      {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6',\n        hoverColor: '#3d85c6',\n      },\n      {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f',\n        hoverColor: '#6aa84f',\n      },\n    ],\n    calls: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n    // Alias pour compatibilité\n    notificationConfig: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    callStatusColors: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n  };\n\n  // Getter pour compatibilité\n  get availableReactions(): string[] {\n    return this.c.reactions;\n  }\n\n  // Variables de notification\n  notificationFilter = 'all';\n  isLoadingNotifications = false;\n  isMarkingAsRead = false;\n  isDeletingNotifications = false;\n  hasMoreNotifications = false;\n  notificationSounds = true;\n  notificationPreview = true;\n  autoMarkAsRead = true;\n\n  // Variables d'appel\n  isCallMinimized = false;\n  callQuality = 'connecting';\n  showCallControls = false;\n\n  // Variables de statut utilisateur\n  onlineUsers: Map<string, User> = new Map();\n  currentUserStatus: string = 'online';\n  lastActivityTime: Date = new Date();\n  autoAwayTimeout: any = null;\n  isUpdatingStatus = false;\n  callHistory: any[] = [];\n  voiceMessages: any[] = [];\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n  statusFilterType = 'all';\n\n  // Emojis du service\n  get commonEmojis(): string[] {\n    return this.MessageService.getCommonEmojis();\n  }\n\n  // Configuration des boutons d'action de l'en-tête\n  getHeaderActions() {\n    return [\n      {\n        class: 'btn-audio-call',\n        icon: 'fas fa-phone-alt',\n        title: 'Appel audio',\n        onClick: () => this.initiateCall('AUDIO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-video-call',\n        icon: 'fas fa-video',\n        title: 'Appel vidéo',\n        onClick: () => this.initiateCall('VIDEO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-search',\n        icon: 'fas fa-search',\n        title: 'Rechercher',\n        onClick: () => this.toggleSearchBar(),\n        isActive: this.showSearchBar,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-pinned relative',\n        icon: 'fas fa-thumbtack',\n        title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n        onClick: () => this.togglePinnedMessages(),\n        isActive: this.showPinnedMessages,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.getPinnedMessagesCount() > 0\n            ? {\n                count: this.getPinnedMessagesCount(),\n                class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n                animate: false,\n              }\n            : null,\n      },\n      {\n        class: 'btn-notifications relative',\n        icon: 'fas fa-bell',\n        title: 'Notifications',\n        onClick: () => this.toggleNotificationPanel(),\n        isActive: this.showNotificationPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.unreadNotificationCount > 0\n            ? {\n                count: this.unreadNotificationCount,\n                class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n                animate: true,\n              }\n            : null,\n      },\n      {\n        class: 'btn-history relative',\n        icon: 'fas fa-history',\n        title: 'Historique des appels',\n        onClick: () => this.toggleCallHistoryPanel(),\n        isActive: this.showCallHistoryPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-stats relative',\n        icon: 'fas fa-chart-bar',\n        title: \"Statistiques d'appels\",\n        onClick: () => this.toggleCallStatsPanel(),\n        isActive: this.showCallStatsPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-voice-messages relative',\n        icon: 'fas fa-microphone',\n        title: 'Messages vocaux',\n        onClick: () => this.toggleVoiceMessagesPanel(),\n        isActive: this.showVoiceMessagesPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.voiceMessages.length > 0\n            ? {\n                count: this.voiceMessages.length,\n                class: 'bg-[#4f5fad]',\n                animate: false,\n              }\n            : null,\n      },\n    ];\n  }\n\n  constructor(\n    private MessageService: MessageService,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private fb: FormBuilder,\n    public statusService: UserStatusService,\n    public router: Router,\n    private toastService: ToastService,\n\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]],\n    });\n  }\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n    }\n\n    this.subscribeToNotifications();\n    this.subscribeToUserStatus();\n    this.initializeUserStatus();\n    this.startActivityTracking();\n\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n\n    const routeSub = this.route.params\n      .pipe(\n        filter((params) => params['id']),\n        distinctUntilChanged(),\n        switchMap((params) => {\n          this.loading = true;\n          this.messages = [];\n          this.currentPage = 1;\n          this.hasMoreMessages = true;\n\n          return this.MessageService.getConversation(\n            params['id'],\n            this.MAX_MESSAGES_TO_LOAD,\n            this.currentPage\n          );\n        })\n      )\n      .subscribe({\n        next: (conversation) => {\n          this.handleConversationLoaded(conversation);\n        },\n        error: (error) => {\n          this.handleError('Failed to load conversation', error);\n        },\n      });\n    this.subscriptions.add(routeSub);\n  }\n\n  // Gestion centralisée des erreurs\n  private handleError(\n    message: string,\n    error: any,\n    resetLoading: boolean = true\n  ): void {\n    console.error('MessageChat', message, error);\n    if (resetLoading) {\n      this.loading = false;\n      this.isUploading = false;\n      this.isLoadingMore = false;\n    }\n    this.error = error;\n    this.toastService.showError(message);\n  }\n\n  // Gestion centralisée des succès\n  private handleSuccess(message?: string, callback?: () => void): void {\n    if (message) {\n      this.toastService.showSuccess(message);\n    }\n    if (callback) {\n      callback();\n    }\n  }\n\n  // Services de fichiers optimisés\n  readonly f = {\n    getIcon: (t?: string) => this.MessageService.getFileIcon(t),\n    getType: (t?: string) => this.MessageService.getFileType(t),\n  };\n\n  getFileIcon = this.f.getIcon;\n  getFileType = this.f.getType;\n\n  private handleConversationLoaded(conversation: Conversation): void {\n    this.conversation = conversation;\n\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.otherParticipant =\n        conversation?.participants?.find(\n          (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n        ) || null;\n      this.messages = [];\n    } else {\n      const conversationMessages = [...(conversation?.messages || [])];\n\n      conversationMessages.sort((a, b) => {\n        const timeA =\n          a.timestamp instanceof Date\n            ? a.timestamp.getTime()\n            : new Date(a.timestamp as string).getTime();\n        const timeB =\n          b.timestamp instanceof Date\n            ? b.timestamp.getTime()\n            : new Date(b.timestamp as string).getTime();\n        return timeA - timeB;\n      });\n\n      this.messages = conversationMessages;\n    }\n\n    this.otherParticipant =\n      conversation?.participants?.find(\n        (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n      ) || null;\n\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n\n    this.markMessagesAsRead();\n\n    if (this.conversation?.id) {\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n  }\n\n  private subscribeToConversationUpdates(conversationId: string): void {\n    const sub = this.MessageService.subscribeToConversationUpdates(\n      conversationId\n    ).subscribe({\n      next: (updatedConversation) => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages\n          ? [...updatedConversation.messages]\n          : [];\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to conversation updates lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToNewMessages(conversationId: string): void {\n    const sub = this.MessageService.subscribeToNewMessages(\n      conversationId\n    ).subscribe({\n      next: (newMessage) => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA =\n              a.timestamp instanceof Date\n                ? a.timestamp.getTime()\n                : new Date(a.timestamp as string).getTime();\n            const timeB =\n              b.timestamp instanceof Date\n                ? b.timestamp.getTime()\n                : new Date(b.timestamp as string).getTime();\n            return timeA - timeB;\n          });\n\n          setTimeout(() => this.scrollToBottom(), 100);\n\n          if (\n            newMessage.sender?.id !== this.currentUserId &&\n            newMessage.sender?._id !== this.currentUserId\n          ) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to new messages lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToTypingIndicators(conversationId: string): void {\n    const sub = this.MessageService.subscribeToTypingIndicator(\n      conversationId\n    ).subscribe({\n      next: (event) => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private markMessagesAsRead(): void {\n    const unreadMessages = this.messages.filter(\n      (msg) =>\n        !msg.isRead &&\n        (msg.receiver?.id === this.currentUserId ||\n          msg.receiver?._id === this.currentUserId)\n    );\n\n    unreadMessages.forEach((msg) => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: (error) => {},\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n\n    const validTypes = [\n      'image/jpeg',\n      'image/png',\n      'image/gif',\n      'application/pdf',\n      'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    ];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError(\n        'Invalid file type. Only images, PDFs and Word docs are allowed'\n      );\n      return;\n    }\n\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n\n  removeAttachment(): void {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n\n  private typingTimer: any;\n  private isCurrentlyTyping = false;\n  private readonly TYPING_DELAY = 500;\n  private readonly TYPING_TIMEOUT = 3000;\n\n  // Frappe\n  onTyping(): void {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n\n    const conversationId = this.conversation.id;\n    clearTimeout(this.typingTimer);\n\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {},\n        error: (error) => {},\n      });\n    }\n\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {},\n          error: (error) => {},\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n\n  // Panneaux\n  private togglePanel(panelName: string, closeOthers: boolean = true): void {\n    const panels = {\n      theme: 'showThemeSelector',\n      menu: 'showMainMenu',\n      emoji: 'showEmojiPicker',\n      notification: 'showNotificationPanel',\n      search: 'showSearchBar',\n      status: 'showStatusSelector',\n    };\n\n    const currentPanel = panels[panelName as keyof typeof panels];\n    if (currentPanel) {\n      (this as any)[currentPanel] = !(this as any)[currentPanel];\n\n      if (closeOthers && (this as any)[currentPanel]) {\n        Object.values(panels).forEach((panel) => {\n          if (panel !== currentPanel) {\n            (this as any)[panel] = false;\n          }\n        });\n      }\n    }\n  }\n\n  // Méthodes de basculement principales consolidées\n  readonly mainToggleMethods = {\n    themeSelector: () => this.togglePanel('theme'),\n    mainMenu: () => this.togglePanel('menu'),\n    emojiPicker: () => this.togglePanel('emoji'),\n  };\n\n  toggleThemeSelector = this.mainToggleMethods.themeSelector;\n  toggleMainMenu = this.mainToggleMethods.mainMenu;\n  toggleEmojiPicker = this.mainToggleMethods.emojiPicker;\n\n  changeTheme(theme: string): void {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    localStorage.setItem('chat-theme', theme);\n  }\n\n  // Méthodes toggle consolidées\n  private readonly toggleMethods = {\n    pinnedMessages: () => (this.showPinnedMessages = !this.showPinnedMessages),\n    searchBar: () => {\n      this.togglePanel('search');\n      if (!this.showSearchBar) this.clearSearch();\n    },\n    statusSelector: () => this.togglePanel('status'),\n    notificationSettings: () =>\n      (this.showNotificationSettings = !this.showNotificationSettings),\n    userStatusPanel: () =>\n      (this.showUserStatusPanel = !this.showUserStatusPanel),\n    callMinimize: () => (this.isCallMinimized = !this.isCallMinimized),\n    callHistoryPanel: () =>\n      (this.showCallHistoryPanel = !this.showCallHistoryPanel),\n    callStatsPanel: () => (this.showCallStatsPanel = !this.showCallStatsPanel),\n    voiceMessagesPanel: () =>\n      (this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel),\n  };\n\n  togglePinnedMessages = this.toggleMethods.pinnedMessages;\n  toggleSearchBar = this.toggleMethods.searchBar;\n  toggleStatusSelector = this.toggleMethods.statusSelector;\n  toggleNotificationSettings = this.toggleMethods.notificationSettings;\n  toggleUserStatusPanel = this.toggleMethods.userStatusPanel;\n  toggleCallMinimize = this.toggleMethods.callMinimize;\n  toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;\n  toggleCallStatsPanel = this.toggleMethods.callStatsPanel;\n  toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;\n\n  // Conversation - méthode utilitaire\n  private showDevelopmentFeature(featureName: string): void {\n    this.showMainMenu = false;\n    this.toastService.showInfo(`${featureName} en cours de développement`);\n  }\n\n  readonly conversationMethods = {\n    showInfo: () => this.showDevelopmentFeature('Informations de conversation'),\n    showSettings: () =>\n      this.showDevelopmentFeature('Paramètres de conversation'),\n    clear: (): void => {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à vider');\n        return;\n      }\n\n      if (\n        confirm(\n          'Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.'\n        )\n      ) {\n        this.messages = [];\n        this.showMainMenu = false;\n        this.toastService.showSuccess('Conversation vidée avec succès');\n      }\n    },\n    export: (): void => {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à exporter');\n        return;\n      }\n\n      const conversationName = this.conversation.isGroup\n        ? this.conversation.groupName || 'Groupe sans nom'\n        : this.otherParticipant?.username || 'Conversation privée';\n\n      const exportData = {\n        conversation: {\n          id: this.conversation.id,\n          name: conversationName,\n          isGroup: this.conversation.isGroup,\n          participants: this.conversation.participants,\n          createdAt: this.conversation.createdAt,\n        },\n        messages: this.messages.map((msg) => ({\n          id: msg.id,\n          content: msg.content,\n          sender: msg.sender,\n          timestamp: msg.timestamp,\n          type: msg.type,\n        })),\n        exportedAt: new Date().toISOString(),\n        exportedBy: this.currentUserId,\n      };\n\n      const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n        type: 'application/json',\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n\n      const safeFileName = conversationName\n        .replace(/[^a-z0-9]/gi, '_')\n        .toLowerCase();\n      const dateStr = new Date().toISOString().split('T')[0];\n      link.download = `conversation-${safeFileName}-${dateStr}.json`;\n\n      link.click();\n      window.URL.revokeObjectURL(url);\n\n      this.showMainMenu = false;\n      this.toastService.showSuccess('Conversation exportée avec succès');\n    },\n  };\n\n  // Template methods\n  toggleConversationInfo = this.conversationMethods.showInfo;\n  toggleConversationSettings = this.conversationMethods.showSettings;\n  clearConversation = this.conversationMethods.clear;\n  exportConversation = this.conversationMethods.export;\n\n  sendMessage(): void {\n    if (\n      (this.messageForm.invalid && !this.selectedFile) ||\n      !this.currentUserId ||\n      !this.otherParticipant?.id\n    ) {\n      return;\n    }\n\n    this.cleanup.stopTypingIndicator();\n\n    const content = this.messageForm.get('content')?.value;\n\n    const tempMessage: Message = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername,\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient',\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true,\n    };\n\n    if (this.selectedFile) {\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n\n        if (this.previewUrl) {\n          tempMessage.attachments = [\n            {\n              id: 'temp-attachment',\n              url: this.previewUrl ? this.previewUrl.toString() : '',\n              type: MessageType.IMAGE,\n              name: this.selectedFile.name,\n              size: this.selectedFile.size,\n            },\n          ];\n        }\n      }\n\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n\n    this.messages = [...this.messages, tempMessage];\n\n    const fileToSend = this.selectedFile;\n    this.messageForm.reset();\n    this.removeAttachment();\n\n    setTimeout(() => this.scrollToBottom(true), 50);\n\n    this.isUploading = true;\n\n    const sendSub = this.MessageService.sendMessage(\n      this.otherParticipant.id,\n      content,\n      fileToSend || undefined,\n      MessageType.TEXT,\n      this.conversation?.id\n    ).subscribe({\n      next: (message) => {\n        this.updateMessageState(tempMessage.id!, message);\n        this.isUploading = false;\n      },\n      error: (error) => {\n        this.updateMessageState(tempMessage.id!, null, true);\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      },\n    });\n\n    this.subscriptions.add(sendSub);\n  }\n\n  // Méthode consolidée pour mettre à jour l'état des messages\n  private updateMessageState(\n    tempId: string,\n    newMessage?: Message | null,\n    isError: boolean = false\n  ): void {\n    this.messages = this.messages.map((msg) => {\n      if (msg.id === tempId) {\n        if (newMessage) {\n          return newMessage;\n        } else if (isError) {\n          return {\n            ...msg,\n            isPending: false,\n            isError: true,\n          };\n        }\n      }\n      return msg;\n    });\n  }\n\n  // Service - méthodes optimisées\n  readonly s = {\n    formatTime: (t: string | Date | undefined) =>\n      this.MessageService.formatMessageTime(t),\n    formatActive: (t: string | Date | undefined) =>\n      this.MessageService.formatLastActive(t),\n    formatDate: (t: string | Date | undefined) =>\n      this.MessageService.formatMessageDate(t),\n    showDateHeader: (i: number) =>\n      this.MessageService.shouldShowDateHeader(this.messages, i),\n    getType: (m: Message | null | undefined) =>\n      this.MessageService.getMessageType(m),\n    hasImage: (m: Message | null | undefined) =>\n      this.MessageService.hasImage(m),\n    isVoice: (m: Message | null | undefined) =>\n      this.MessageService.isVoiceMessage(m),\n    getVoiceUrl: (m: Message | null | undefined) =>\n      this.MessageService.getVoiceMessageUrl(m),\n    getVoiceDuration: (m: Message | null | undefined) =>\n      this.MessageService.getVoiceMessageDuration(m),\n    getVoiceHeight: (i: number) => this.MessageService.getVoiceBarHeight(i),\n    formatVoice: (s: number) => this.MessageService.formatVoiceDuration(s),\n    getImageUrl: (m: Message | null | undefined) =>\n      this.MessageService.getImageUrl(m),\n    getTypeClass: (m: Message | null | undefined) =>\n      this.MessageService.getMessageTypeClass(m, this.currentUserId),\n  };\n\n  // Méthodes exposées optimisées\n  formatMessageTime = this.s.formatTime;\n  formatLastActive = this.s.formatActive;\n  formatMessageDate = this.s.formatDate;\n  shouldShowDateHeader = this.s.showDateHeader;\n  getMessageType = this.s.getType;\n  hasImage = this.s.hasImage;\n  isVoiceMessage = this.s.isVoice;\n  getVoiceMessageUrl = this.s.getVoiceUrl;\n  getVoiceMessageDuration = this.s.getVoiceDuration;\n  getVoiceBarHeight = this.s.getVoiceHeight;\n  formatVoiceDuration = this.s.formatVoice;\n  getImageUrl = this.s.getImageUrl;\n  getMessageTypeClass = this.s.getTypeClass;\n\n  // Défilement\n  onScroll(event: any): void {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n\n    if (\n      scrollTop < 50 &&\n      !this.isLoadingMore &&\n      this.conversation?.id &&\n      this.hasMoreMessages\n    ) {\n      this.showLoadingIndicator();\n\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n\n      this.isLoadingMore = true;\n\n      this.loadMoreMessages();\n\n      // Maintenir la position de défilement\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(\n              firstVisibleMessage.id\n            );\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({ block: 'center' });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n\n  // Méthode pour trouver le premier message visible dans la vue\n  private getFirstVisibleMessage(): Message | null {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length)\n      return null;\n\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find((m) => m.id === messageId) || null;\n      }\n    }\n\n    return null;\n  }\n\n  // Méthode pour trouver un élément de message par ID\n  private findMessageElement(\n    messageId: string | undefined\n  ): HTMLElement | null {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(\n      `[data-message-id=\"${messageId}\"]`\n    );\n  }\n\n  // Indicateurs de chargement consolidés\n  readonly loadingIndicatorMethods = {\n    show: () => {\n      if (!document.getElementById('message-loading-indicator')) {\n        const indicator = document.createElement('div');\n        indicator.id = 'message-loading-indicator';\n        indicator.className = 'text-center py-2 text-gray-500 text-sm';\n        indicator.innerHTML =\n          '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n        this.messagesContainer?.nativeElement?.prepend(indicator);\n      }\n    },\n    hide: () => {\n      const indicator = document.getElementById('message-loading-indicator');\n      indicator?.parentNode?.removeChild(indicator);\n    },\n  };\n\n  private showLoadingIndicator = this.loadingIndicatorMethods.show;\n  private hideLoadingIndicator = this.loadingIndicatorMethods.hide;\n\n  // Charger plus de messages\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages)\n      return;\n\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n\n    // Augmenter la page\n    this.currentPage++;\n\n    // Charger plus de messages\n    this.MessageService.getConversation(\n      this.conversation.id,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        if (\n          conversation &&\n          conversation.messages &&\n          conversation.messages.length > 0\n        ) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n\n          // Créer un Set des IDs existants\n          const existingIds = new Set(oldMessages.map((msg) => msg.id));\n\n          // Filtrer et trier les nouveaux messages\n          const newMessages = conversation.messages\n            .filter((msg) => !existingIds.has(msg.id))\n            .sort((a, b) => {\n              const timeA = new Date(a.timestamp as string).getTime();\n              const timeB = new Date(b.timestamp as string).getTime();\n              return timeA - timeB;\n            });\n\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages =\n              newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: (error) => {\n        console.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      },\n    });\n  }\n\n  // Méthode utilitaire pour comparer les timestamps\n  private isSameTimestamp(\n    timestamp1: string | Date | undefined,\n    timestamp2: string | Date | undefined\n  ): boolean {\n    if (!timestamp1 || !timestamp2) return false;\n\n    try {\n      const time1 =\n        timestamp1 instanceof Date\n          ? timestamp1.getTime()\n          : new Date(timestamp1 as string).getTime();\n      const time2 =\n        timestamp2 instanceof Date\n          ? timestamp2.getTime()\n          : new Date(timestamp2 as string).getTime();\n      return Math.abs(time1 - time2) < 1000;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  scrollToBottom(force: boolean = false): void {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom =\n          container.scrollHeight - container.clientHeight <=\n          container.scrollTop + 150;\n\n        if (force || isScrolledToBottom) {\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth',\n          });\n        }\n      });\n    } catch (err) {\n      console.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n\n  // Méthodes d'enregistrement vocal consolidées\n  readonly voiceRecordingMethods = {\n    toggle: () => {\n      this.isRecordingVoice = !this.isRecordingVoice;\n      if (!this.isRecordingVoice) this.voiceRecordingDuration = 0;\n    },\n    complete: (audioBlob: Blob) => {\n      if (!this.conversation?.id && !this.otherParticipant?.id) {\n        this.toastService.showError('No conversation or recipient selected');\n        this.isRecordingVoice = false;\n        return;\n      }\n      const receiverId = this.otherParticipant?.id || '';\n      this.MessageService.sendVoiceMessage(\n        receiverId,\n        audioBlob,\n        this.conversation?.id,\n        this.voiceRecordingDuration\n      ).subscribe({\n        next: (message) => {\n          this.isRecordingVoice = false;\n          this.voiceRecordingDuration = 0;\n          this.scrollToBottom(true);\n        },\n        error: (error) => {\n          this.toastService.showError('Failed to send voice message');\n          this.isRecordingVoice = false;\n        },\n      });\n    },\n    cancel: () => {\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n    },\n  };\n\n  toggleVoiceRecording = this.voiceRecordingMethods.toggle;\n  onVoiceRecordingComplete = this.voiceRecordingMethods.complete;\n  onVoiceRecordingCancelled = this.voiceRecordingMethods.cancel;\n\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl: string): void {\n    window.open(imageUrl, '_blank');\n  }\n\n  /**\n   * Détecte les changements après chaque vérification de la vue - Optimisé\n   */\n  ngAfterViewChecked(): void {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n\n    // Détection des changements optimisée - Suppression de la vérification inutile\n    // La détection automatique d'Angular gère déjà les messages vocaux\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji: string): void {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector(\n          '.whatsapp-input-field'\n        ) as HTMLInputElement;\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  private subscribeToNotifications(): void {\n    const notificationSub =\n      this.MessageService.subscribeToNewNotifications().subscribe({\n        next: (notification) => {\n          this.notifications.unshift(notification);\n          this.updateNotificationCount();\n\n          this.MessageService.play('notification');\n\n          if (\n            notification.type === 'NEW_MESSAGE' &&\n            notification.conversationId === this.conversation?.id\n          ) {\n            if (notification.id) {\n              this.MessageService.markAsRead([notification.id]).subscribe();\n            }\n          }\n        },\n        error: (error) => {},\n      });\n    this.subscriptions.add(notificationSub);\n\n    const notificationsListSub = this.MessageService.notifications$.subscribe({\n      next: (notifications) => {\n        this.notifications = notifications;\n        this.updateNotificationCount();\n      },\n      error: (error) => {},\n    });\n    this.subscriptions.add(notificationsListSub);\n\n    const notificationCountSub =\n      this.MessageService.notificationCount$.subscribe({\n        next: (count) => {\n          this.unreadNotificationCount = count;\n        },\n      });\n    this.subscriptions.add(notificationCountSub);\n\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: (call) => {\n        if (call) {\n          this.incomingCall = call;\n          this.showCallModal = true;\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      },\n    });\n    this.subscriptions.add(callSub);\n\n    const activeCallSub = this.MessageService.activeCall$.subscribe({\n      next: (call) => {\n        this.activeCall = call;\n        if (call) {\n          this.showActiveCallModal = true;\n          this.startCallTimerMethod();\n        } else {\n          this.showActiveCallModal = false;\n          this.stopCallTimerMethod();\n          this.resetCallStateMethod();\n        }\n      },\n    });\n    this.subscriptions.add(activeCallSub);\n\n    // S'abonner aux flux vidéo locaux\n    const localStreamSub = this.MessageService.localStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.localVideoElement) {\n          this.localVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(localStreamSub);\n\n    // S'abonner aux flux vidéo distants\n    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.remoteVideoElement) {\n          this.remoteVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(remoteStreamSub);\n  }\n\n  // Méthodes d'appel consolidées\n  readonly callMethods = {\n    initiate: (type: 'AUDIO' | 'VIDEO') => {\n      if (!this.otherParticipant?.id) return;\n      this.MessageService.initiateCall(\n        this.otherParticipant.id,\n        type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO,\n        this.conversation?.id\n      ).subscribe({\n        next: (call) => {},\n        error: () => this.toastService.showError(this.c.error),\n      });\n    },\n    accept: () => {\n      if (!this.incomingCall) return;\n      this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n        next: (call) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n          this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n          this.callQuality = 'connecting';\n          this.toastService.showSuccess('Appel connecté');\n        },\n        error: () => {\n          this.toastService.showError(this.c.error);\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n      });\n    },\n    reject: () => {\n      if (!this.incomingCall) return;\n      this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n        next: (call) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n        error: (error) => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n      });\n    },\n    end: () => {\n      const sub = this.MessageService.activeCall$.subscribe((call) => {\n        if (call) {\n          this.MessageService.endCall(call.id).subscribe({\n            next: (call) => {},\n            error: (error) => {},\n          });\n        }\n      });\n      sub.unsubscribe();\n    },\n  };\n\n  initiateCall = this.callMethods.initiate;\n  acceptCall = this.callMethods.accept;\n  rejectCall = this.callMethods.reject;\n  endCall = this.callMethods.end;\n\n  // Méthodes de contrôle d'appel consolidées\n  readonly callControlMethods = {\n    toggleMute: () => {\n      this.isCallMuted = !this.isCallMuted;\n      this.callControlMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé'\n      );\n    },\n    toggleVideo: () => {\n      this.isVideoEnabled = !this.isVideoEnabled;\n      this.callControlMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n      );\n    },\n    updateMedia: () => {\n      this.MessageService.toggleMedia(\n        this.activeCall?.id,\n        !this.isCallMuted,\n        this.isVideoEnabled\n      ).subscribe({\n        next: () => {},\n        error: (error) =>\n          console.error('Erreur lors de la mise à jour des médias:', error),\n      });\n    },\n  };\n\n  toggleCallMute = this.callControlMethods.toggleMute;\n  toggleCallVideo = this.callControlMethods.toggleVideo;\n  private updateCallMedia = this.callControlMethods.updateMedia;\n\n  // Méthodes de timer consolidées\n  readonly timerMethods = {\n    startCallTimer: () => {\n      this.callDuration = 0;\n      this.callTimer = setInterval(() => {\n        this.callDuration++;\n        if (this.callDuration === 3 && this.callQuality === 'connecting') {\n          this.callQuality = 'excellent';\n        }\n      }, 1000);\n    },\n    stopCallTimer: () => {\n      if (this.callTimer) {\n        clearInterval(this.callTimer);\n        this.callTimer = null;\n      }\n    },\n    resetCallState: () => (this.callDuration = 0),\n  };\n\n  private startCallTimerMethod = this.timerMethods.startCallTimer;\n  private stopCallTimerMethod = this.timerMethods.stopCallTimer;\n  private resetCallStateMethod = this.timerMethods.resetCallState;\n\n  // Notifications\n\n  // Méthode de basculement de notification consolidée\n  readonly notificationToggleMethod = {\n    togglePanel: () => {\n      this.togglePanel('notification');\n      if (this.showNotificationPanel) {\n        this.loadNotifications();\n      }\n    },\n  };\n\n  toggleNotificationPanel = this.notificationToggleMethod.togglePanel;\n\n  /**\n   * Charge les notifications\n   */\n  loadNotifications(refresh: boolean = false): void {\n    const loadSub = this.MessageService.getNotifications(\n      refresh,\n      1,\n      20\n    ).subscribe({\n      next: (notifications) => {\n        if (refresh) {\n          this.notifications = notifications;\n        } else {\n          this.notifications = [...this.notifications, ...notifications];\n        }\n\n        this.updateNotificationCount();\n      },\n      error: (error) => {\n        this.toastService.showError(\n          'Erreur lors du chargement des notifications'\n        );\n      },\n    });\n\n    this.subscriptions.add(loadSub);\n  }\n\n  // Méthodes de notification consolidées\n  readonly notificationMethods = {\n    loadMore: () => this.loadNotifications(),\n    updateCount: () =>\n      (this.unreadNotificationCount = this.notifications.filter(\n        (n) => !n.isRead\n      ).length),\n    getFiltered: () => this.notifications,\n    toggleSelection: (notificationId: string) => {\n      if (this.selectedNotifications.has(notificationId)) {\n        this.selectedNotifications.delete(notificationId);\n      } else {\n        this.selectedNotifications.add(notificationId);\n      }\n    },\n    toggleSelectAll: () => {\n      const filteredNotifications = this.notificationMethods.getFiltered();\n      const allSelected = filteredNotifications.every((n) =>\n        this.selectedNotifications.has(n.id)\n      );\n      if (allSelected) {\n        filteredNotifications.forEach((n) =>\n          this.selectedNotifications.delete(n.id)\n        );\n      } else {\n        filteredNotifications.forEach((n) =>\n          this.selectedNotifications.add(n.id)\n        );\n      }\n    },\n    areAllSelected: () => {\n      const filteredNotifications = this.notificationMethods.getFiltered();\n      return (\n        filteredNotifications.length > 0 &&\n        filteredNotifications.every((n) => this.selectedNotifications.has(n.id))\n      );\n    },\n  };\n\n  loadMoreNotifications = this.notificationMethods.loadMore;\n  private updateNotificationCount = this.notificationMethods.updateCount;\n  getFilteredNotifications = this.notificationMethods.getFiltered;\n  toggleNotificationSelection = this.notificationMethods.toggleSelection;\n  toggleSelectAllNotifications = this.notificationMethods.toggleSelectAll;\n  areAllNotificationsSelected = this.notificationMethods.areAllSelected;\n\n  // Méthodes de marquage consolidées\n  readonly markingMethods = {\n    markSelected: () => {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      this.markingMethods.markAsRead(selectedIds, () => {\n        this.selectedNotifications.clear();\n        this.toastService.showSuccess(\n          `${selectedIds.length} notification(s) marquée(s) comme lue(s)`\n        );\n      });\n    },\n    markAll: () => {\n      const unreadNotifications = this.notifications.filter((n) => !n.isRead);\n      if (unreadNotifications.length === 0) {\n        this.toastService.showInfo('Aucune notification non lue');\n        return;\n      }\n      const unreadIds = unreadNotifications.map((n) => n.id);\n      this.markingMethods.markAsRead(unreadIds, () => {\n        this.toastService.showSuccess(\n          'Toutes les notifications ont été marquées comme lues'\n        );\n      });\n    },\n    markAsRead: (ids: string[], onSuccess: () => void) => {\n      const markSub = this.MessageService.markAsRead(ids).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.map((n) =>\n            ids.includes(n.id) ? { ...n, isRead: true, readAt: new Date() } : n\n          );\n          this.updateNotificationCount();\n          onSuccess();\n        },\n        error: (error) => {\n          this.toastService.showError(\n            'Erreur lors du marquage des notifications'\n          );\n        },\n      });\n      this.subscriptions.add(markSub);\n    },\n  };\n\n  markSelectedAsRead = this.markingMethods.markSelected;\n  markAllAsRead = this.markingMethods.markAll;\n\n  // Méthodes de suppression de notifications consolidées\n  readonly notificationDeleteMethods = {\n    showDeleteSelectedConfirmation: () => {\n      if (this.selectedNotifications.size === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      this.showDeleteConfirmModal = true;\n    },\n    deleteSelected: () => {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) return;\n      this.isDeletingNotifications = true;\n      this.showDeleteConfirmModal = false;\n      const deleteSub = this.MessageService.deleteMultipleNotifications(\n        selectedIds\n      ).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.filter(\n            (n) => !selectedIds.includes(n.id)\n          );\n          this.selectedNotifications.clear();\n          this.updateNotificationCount();\n          this.isDeletingNotifications = false;\n          this.toastService.showSuccess(\n            `${result.count} notification(s) supprimée(s)`\n          );\n        },\n        error: (error) => {\n          this.isDeletingNotifications = false;\n          this.toastService.showError(\n            'Erreur lors de la suppression des notifications'\n          );\n        },\n      });\n      this.subscriptions.add(deleteSub);\n    },\n    deleteOne: (notificationId: string) => {\n      const deleteSub = this.MessageService.deleteNotification(\n        notificationId\n      ).subscribe({\n        next: (result) => {\n          this.notifications = this.notifications.filter(\n            (n) => n.id !== notificationId\n          );\n          this.selectedNotifications.delete(notificationId);\n          this.updateNotificationCount();\n          this.toastService.showSuccess('Notification supprimée');\n        },\n        error: (error) => {\n          this.toastService.showError(\n            'Erreur lors de la suppression de la notification'\n          );\n        },\n      });\n      this.subscriptions.add(deleteSub);\n    },\n    deleteAll: () => {\n      if (this.notifications.length === 0) return;\n      if (\n        !confirm(\n          'Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.'\n        )\n      ) {\n        return;\n      }\n      this.isDeletingNotifications = true;\n      const deleteAllSub =\n        this.MessageService.deleteAllNotifications().subscribe({\n          next: (result) => {\n            this.notifications = [];\n            this.selectedNotifications.clear();\n            this.updateNotificationCount();\n            this.isDeletingNotifications = false;\n            this.toastService.showSuccess(\n              `${result.count} notifications supprimées avec succès`\n            );\n          },\n          error: (error) => {\n            this.isDeletingNotifications = false;\n            this.toastService.showError(\n              'Erreur lors de la suppression de toutes les notifications'\n            );\n          },\n        });\n      this.subscriptions.add(deleteAllSub);\n    },\n    cancel: () => (this.showDeleteConfirmModal = false),\n  };\n\n  showDeleteSelectedConfirmation =\n    this.notificationDeleteMethods.showDeleteSelectedConfirmation;\n  deleteSelectedNotifications = this.notificationDeleteMethods.deleteSelected;\n  deleteNotification = this.notificationDeleteMethods.deleteOne;\n  deleteAllNotifications = this.notificationDeleteMethods.deleteAll;\n  cancelDeleteNotifications = this.notificationDeleteMethods.cancel;\n\n  // Méthodes utilitaires de notification consolidées\n  readonly notificationUtilMethods = {\n    formatDate: (date: string | Date | undefined) =>\n      this.MessageService.formatLastActive(date),\n    getIcon: (type: string) =>\n      this.c.notifications[type as keyof typeof this.c.notifications]?.icon ||\n      'fas fa-bell',\n    getColor: (type: string) =>\n      this.c.notifications[type as keyof typeof this.c.notifications]?.color ||\n      'text-cyan-500',\n    trackById: (index: number, notification: any) =>\n      this.c.trackById(0, notification),\n  };\n\n  formatNotificationDate = this.notificationUtilMethods.formatDate;\n  getNotificationIcon = this.notificationUtilMethods.getIcon;\n  getNotificationColor = this.notificationUtilMethods.getColor;\n  trackByNotificationId = this.notificationUtilMethods.trackById;\n\n  /**\n   * S'abonne au statut utilisateur en temps réel\n   */\n  private subscribeToUserStatus(): void {\n    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n      next: (user: User) => {\n        this.handleUserStatusUpdate(user);\n      },\n      error: (error) => {\n        // Error handled silently\n      },\n    });\n\n    this.subscriptions.add(statusSub);\n  }\n\n  /**\n   * Gère la mise à jour du statut d'un utilisateur\n   */\n  private handleUserStatusUpdate(user: User): void {\n    if (!user.id) return;\n\n    if (user.isOnline) {\n      this.onlineUsers.set(user.id, user);\n    } else {\n      this.onlineUsers.delete(user.id);\n    }\n\n    if (this.otherParticipant && this.otherParticipant.id === user.id) {\n      this.otherParticipant = { ...this.otherParticipant, ...user };\n    }\n  }\n\n  /**\n   * Initialise le statut de l'utilisateur actuel\n   */\n  private initializeUserStatus(): void {\n    if (!this.currentUserId) return;\n\n    const setOnlineSub = this.MessageService.setUserOnline(\n      this.currentUserId\n    ).subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = 'online';\n        this.lastActivityTime = new Date();\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          \"Erreur lors de l'initialisation du statut\",\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(setOnlineSub);\n  }\n\n  /**\n   * Démarre le suivi d'activité automatique\n   */\n  private startActivityTracking(): void {\n    const events = [\n      'mousedown',\n      'mousemove',\n      'keypress',\n      'scroll',\n      'touchstart',\n      'click',\n    ];\n\n    events.forEach((event) => {\n      document.addEventListener(event, this.onUserActivity.bind(this), true);\n    });\n  }\n\n  /**\n   * Gère l'activité de l'utilisateur\n   */\n  private onUserActivity(): void {\n    this.lastActivityTime = new Date();\n\n    // Réinitialiser le timer\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n\n    // Remettre en ligne si absent\n    if (\n      this.currentUserStatus === 'away' ||\n      this.currentUserStatus === 'offline'\n    ) {\n      this.updateUserStatus('online');\n    }\n\n    // Programmer la mise en absence\n    this.autoAwayTimeout = setTimeout(() => {\n      if (this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      }\n    }, this.c.delays.away);\n  }\n\n  /**\n   * Met à jour le statut de l'utilisateur\n   */\n  updateUserStatus(status: string): void {\n    if (!this.currentUserId) return;\n\n    const previousStatus = this.currentUserStatus;\n\n    let updateObservable;\n    if (status === 'online') {\n      updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n    } else {\n      updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n    }\n\n    const updateSub = updateObservable.subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = status;\n\n        if (status !== previousStatus) {\n          const statusText = this.getStatusText(status);\n          this.toastService.showInfo(`Statut : ${statusText}`);\n        }\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          'Erreur lors de la mise à jour du statut',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(updateSub);\n  }\n\n  /**\n   * Charge la liste des utilisateurs en ligne\n   */\n  loadOnlineUsers(): void {\n    const usersSub = this.MessageService.getAllUsers(\n      false,\n      undefined,\n      1,\n      50,\n      'username',\n      'asc',\n      true\n    ).subscribe({\n      next: (users: User[]) => {\n        users.forEach((user) => {\n          if (user.isOnline && user.id) {\n            this.onlineUsers.set(user.id, user);\n          }\n        });\n      },\n      error: (error) => {\n        console.error(\n          'MessageChat',\n          'Erreur lors du chargement des utilisateurs en ligne',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(usersSub);\n  }\n\n  // Méthodes de gestion des panneaux consolidées\n  readonly panelMethods = {\n    getActivePanels: () => {\n      const panels = [];\n      if (this.showUserStatusPanel)\n        panels.push({\n          key: 'userStatus',\n          title: 'Utilisateurs',\n          icon: 'fas fa-users',\n          closeAction: () => (this.showUserStatusPanel = false),\n        });\n      if (this.showCallHistoryPanel)\n        panels.push({\n          key: 'callHistory',\n          title: 'Historique des appels',\n          icon: 'fas fa-history',\n          closeAction: () => (this.showCallHistoryPanel = false),\n        });\n      if (this.showCallStatsPanel)\n        panels.push({\n          key: 'callStats',\n          title: \"Statistiques d'appels\",\n          icon: 'fas fa-chart-bar',\n          closeAction: () => (this.showCallStatsPanel = false),\n        });\n      if (this.showVoiceMessagesPanel)\n        panels.push({\n          key: 'voiceMessages',\n          title: 'Messages vocaux',\n          icon: 'fas fa-microphone',\n          closeAction: () => (this.showVoiceMessagesPanel = false),\n        });\n      return panels;\n    },\n    getStatusOptions: () =>\n      Object.entries(this.c.status).map(([key, config]) => ({\n        key,\n        ...config,\n      })),\n    getThemeOptions: () => this.c.themes,\n  };\n\n  getActivePanels = this.panelMethods.getActivePanels;\n  getStatusOptions = this.panelMethods.getStatusOptions;\n  getThemeOptions = this.panelMethods.getThemeOptions;\n\n  // Méthodes de statut simplifiées\n  readonly statusMethods = {\n    getText: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.text || 'Inconnu',\n    getColor: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.color ||\n      'text-gray-400',\n    getIcon: (status: string) =>\n      this.c.status[status as keyof typeof this.c.status]?.icon ||\n      'fas fa-question-circle',\n  };\n\n  getStatusText = this.statusMethods.getText;\n  getStatusColor = this.statusMethods.getColor;\n  getStatusIcon = this.statusMethods.getIcon;\n\n  // Méthodes utilitaires consolidées\n  readonly utilityMethods = {\n    formatLastSeen: (lastActive: Date | null) =>\n      lastActive\n        ? this.MessageService.formatLastActive(lastActive)\n        : 'Jamais vu',\n    getOnlineUsersCount: () =>\n      Array.from(this.onlineUsers.values()).filter((user) => user.isOnline)\n        .length,\n    getFilteredUsers: () => Array.from(this.onlineUsers.values()),\n    setStatusFilter: (filter: string) => (this.statusFilterType = filter),\n  };\n\n  formatLastSeen = this.utilityMethods.formatLastSeen;\n  getOnlineUsersCount = this.utilityMethods.getOnlineUsersCount;\n  setStatusFilter = this.utilityMethods.setStatusFilter;\n  getFilteredUsers = this.utilityMethods.getFilteredUsers;\n\n  // Méthodes de réponse et transfert consolidées\n  readonly replyForwardMethods = {\n    startReply: (message: any) => (this.replyingToMessage = message),\n    cancelReply: () => (this.replyingToMessage = null),\n    openForwardModal: (message: any) => {\n      this.forwardingMessage = message;\n      this.showForwardModal = true;\n    },\n    closeForwardModal: () => {\n      this.showForwardModal = false;\n      this.forwardingMessage = null;\n      this.selectedConversations = [];\n    },\n  };\n\n  startReplyToMessage = this.replyForwardMethods.startReply;\n  cancelReply = this.replyForwardMethods.cancelReply;\n  openForwardModal = this.replyForwardMethods.openForwardModal;\n  closeForwardModal = this.replyForwardMethods.closeForwardModal;\n\n  // Messages - méthodes consolidées\n  readonly messageMethods = {\n    getPinIcon: (message: any) =>\n      this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack',\n    getPinDisplayText: (message: any) =>\n      this.isMessagePinned(message) ? 'Désépingler' : 'Épingler',\n    canEditMessage: (message: any) => message.sender?.id === this.currentUserId,\n    isMessagePinned: (message: any) => message.isPinned || false,\n  };\n\n  getPinIcon = this.messageMethods.getPinIcon;\n  getPinDisplayText = this.messageMethods.getPinDisplayText;\n  canEditMessage = this.messageMethods.canEditMessage;\n  isMessagePinned = this.messageMethods.isMessagePinned;\n\n  // Méthodes d'édition consolidées\n  readonly editMethods = {\n    startEditMessage: (message: any) => {\n      this.editingMessageId = message.id;\n      this.editingContent = message.content;\n    },\n    cancelEditMessage: () => {\n      this.editingMessageId = null;\n      this.editingContent = '';\n    },\n    saveEditMessage: (messageId: string) =>\n      this.editMethods.cancelEditMessage(),\n    onEditKeyPress: (event: KeyboardEvent, messageId: string) => {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.editMethods.saveEditMessage(messageId);\n      } else if (event.key === 'Escape') {\n        this.editMethods.cancelEditMessage();\n      }\n    },\n  };\n\n  startEditMessage = this.editMethods.startEditMessage;\n  cancelEditMessage = this.editMethods.cancelEditMessage;\n  saveEditMessage = this.editMethods.saveEditMessage;\n  onEditKeyPress = this.editMethods.onEditKeyPress;\n\n  togglePinMessage(message: any): void {\n    this.isPinning[message.id] = true;\n    setTimeout(() => {\n      this.isPinning[message.id] = false;\n      this.showPinConfirm[message.id] = false;\n    }, 1000);\n  }\n\n  // Utilitaires d'appel consolidées\n  readonly callUtilities = {\n    getCallStatusColor: (status: string) =>\n      this.c.callStatusColors[status as keyof typeof this.c.callStatusColors] ||\n      'text-gray-500',\n    getCallTypeIcon: (type: string) =>\n      type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',\n    formatCallDuration: (duration: number) => {\n      if (!duration) return '00:00';\n      const minutes = Math.floor(duration / 60);\n      const seconds = duration % 60;\n      return `${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    },\n    formatCallDate: (timestamp: string | Date | undefined) =>\n      this.MessageService.formatMessageDate(timestamp),\n  };\n\n  getCallStatusColor = this.callUtilities.getCallStatusColor;\n  getCallTypeIcon = this.callUtilities.getCallTypeIcon;\n  formatCallDuration = this.callUtilities.formatCallDuration;\n  formatCallDate = this.callUtilities.formatCallDate;\n\n  // Méthodes d'événements consolidées\n  readonly eventMethods = {\n    onDocumentClick: (event: Event) => {\n      const target = event.target as HTMLElement;\n      const closeConfigs = [\n        {\n          selectors: ['.theme-selector-menu', '.btn-theme'],\n          property: 'showThemeSelector',\n        },\n        {\n          selectors: ['.emoji-picker', '.btn-emoji'],\n          property: 'showEmojiPicker',\n        },\n      ];\n\n      closeConfigs.forEach((config) => {\n        const isClickOutside = !config.selectors.some((selector) =>\n          target.closest(selector)\n        );\n        if (isClickOutside) {\n          (this as any)[config.property] = false;\n        }\n      });\n    },\n    confirmDeleteMessage: (messageId: string) => {\n      this.showDeleteConfirm[messageId] = false;\n    },\n  };\n\n  onDocumentClick = this.eventMethods.onDocumentClick;\n  confirmDeleteMessage = this.eventMethods.confirmDeleteMessage;\n\n  // Méthodes de réaction consolidées\n  readonly reactionMethods = {\n    getUniqueReactions: (message: any) => message.reactions || [],\n    onReactionClick: (messageId: string, emoji: string) => {\n      // Implémentation des réactions\n    },\n    hasUserReacted: (message: any, emoji: string) => false,\n  };\n\n  getUniqueReactions = this.reactionMethods.getUniqueReactions;\n  onReactionClick = this.reactionMethods.onReactionClick;\n  hasUserReacted = this.reactionMethods.hasUserReacted;\n\n  // Méthodes de conversation consolidées\n  readonly conversationSelectionMethods = {\n    areAllSelected: () =>\n      this.selectedConversations.length === this.availableConversations.length,\n    selectAll: () =>\n      (this.selectedConversations = this.availableConversations.map(\n        (c) => c.id\n      )),\n    deselectAll: () => (this.selectedConversations = []),\n    toggle: (conversationId: string) => {\n      const index = this.selectedConversations.indexOf(conversationId);\n      index > -1\n        ? this.selectedConversations.splice(index, 1)\n        : this.selectedConversations.push(conversationId);\n    },\n    isSelected: (conversationId: string) =>\n      this.selectedConversations.includes(conversationId),\n    getDisplayImage: (conversation: any) =>\n      conversation.image || 'assets/images/default-avatar.png',\n    getDisplayName: (conversation: any) => conversation.name || 'Conversation',\n    forwardMessage: () => {\n      this.isForwarding = true;\n      setTimeout(() => {\n        this.isForwarding = false;\n        this.closeForwardModal();\n      }, 1000);\n    },\n  };\n\n  areAllConversationsSelected =\n    this.conversationSelectionMethods.areAllSelected;\n  selectAllConversations = this.conversationSelectionMethods.selectAll;\n  deselectAllConversations = this.conversationSelectionMethods.deselectAll;\n  toggleConversationSelection = this.conversationSelectionMethods.toggle;\n  isConversationSelected = this.conversationSelectionMethods.isSelected;\n  getConversationDisplayImage =\n    this.conversationSelectionMethods.getDisplayImage;\n  getConversationDisplayName = this.conversationSelectionMethods.getDisplayName;\n  forwardMessage = this.conversationSelectionMethods.forwardMessage;\n\n  // Méthodes de notification simplifiées consolidées\n  readonly simpleNotificationMethods = {\n    onCallMouseMove: () => (this.showCallControls = true),\n    saveNotificationSettings: () => {},\n    setNotificationFilter: (filter: string) =>\n      (this.notificationFilter = filter),\n  };\n\n  onCallMouseMove = this.simpleNotificationMethods.onCallMouseMove;\n  saveNotificationSettings =\n    this.simpleNotificationMethods.saveNotificationSettings;\n  setNotificationFilter = this.simpleNotificationMethods.setNotificationFilter;\n\n  // Méthodes de recherche consolidées\n  readonly searchMethods = {\n    onInput: (event: any) => {\n      this.searchQuery = event.target.value;\n      this.searchQuery.length >= 2\n        ? this.searchMethods.perform()\n        : this.searchMethods.clear();\n    },\n    onKeyPress: (event: KeyboardEvent) => {\n      if (event.key === 'Enter') {\n        this.searchMethods.perform();\n      } else if (event.key === 'Escape') {\n        this.searchMethods.clear();\n      }\n    },\n    perform: () => {\n      this.isSearching = true;\n      this.searchMode = true;\n      setTimeout(() => {\n        this.searchResults = this.messages.filter((m) =>\n          m.content?.toLowerCase().includes(this.searchQuery.toLowerCase())\n        );\n        this.isSearching = false;\n      }, 500);\n    },\n    clear: () => {\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.isSearching = false;\n      this.searchMode = false;\n    },\n  };\n\n  onSearchInput = this.searchMethods.onInput;\n  onSearchKeyPress = this.searchMethods.onKeyPress;\n  performSearch = this.searchMethods.perform;\n  clearSearch = this.searchMethods.clear;\n\n  // Méthodes utilitaires finales consolidées\n  readonly finalUtilityMethods = {\n    navigateToMessage: (messageId: string) => {\n      // Navigation vers un message spécifique\n    },\n    scrollToPinnedMessage: (messageId: string) => {\n      // Défilement vers un message épinglé\n    },\n    getPinnedMessagesCount: () => this.pinnedMessages.length,\n  };\n\n  navigateToMessage = this.finalUtilityMethods.navigateToMessage;\n  scrollToPinnedMessage = this.finalUtilityMethods.scrollToPinnedMessage;\n  getPinnedMessagesCount = this.finalUtilityMethods.getPinnedMessagesCount;\n\n  // Méthodes de recherche et réaction consolidées\n  readonly searchAndReactionMethods = {\n    highlightSearchTerms: (content: string, query: string) => {\n      if (!query) return content;\n      const regex = new RegExp(`(${query})`, 'gi');\n      return content.replace(regex, '<mark>$1</mark>');\n    },\n    toggleReactionPicker: (messageId: string) =>\n      (this.showReactionPicker[messageId] =\n        !this.showReactionPicker[messageId]),\n    reactToMessage: (messageId: string, emoji: string) =>\n      (this.showReactionPicker[messageId] = false),\n    toggleMessageOptions: (messageId: string) =>\n      (this.showMessageOptions[messageId] =\n        !this.showMessageOptions[messageId]),\n  };\n\n  highlightSearchTerms = this.searchAndReactionMethods.highlightSearchTerms;\n  toggleReactionPicker = this.searchAndReactionMethods.toggleReactionPicker;\n  reactToMessage = this.searchAndReactionMethods.reactToMessage;\n  toggleMessageOptions = this.searchAndReactionMethods.toggleMessageOptions;\n\n  // Confirmations consolidées\n  readonly confirmationMethods = {\n    showPinConfirmation: (messageId: string) =>\n      (this.showPinConfirm[messageId] = true),\n    cancelPinConfirmation: (messageId: string) =>\n      (this.showPinConfirm[messageId] = false),\n    showDeleteConfirmation: (messageId: string) =>\n      (this.showDeleteConfirm[messageId] = true),\n    cancelDeleteMessage: (messageId: string) =>\n      (this.showDeleteConfirm[messageId] = false),\n  };\n\n  showPinConfirmation = this.confirmationMethods.showPinConfirmation;\n  cancelPinConfirmation = this.confirmationMethods.cancelPinConfirmation;\n  showDeleteConfirmation = this.confirmationMethods.showDeleteConfirmation;\n  cancelDeleteMessage = this.confirmationMethods.cancelDeleteMessage;\n\n  // Méthodes de nettoyage optimisées\n  readonly cleanup = {\n    clearTimeouts: () =>\n      [this.typingTimeout, this.autoAwayTimeout, this.callTimer].forEach(\n        (t) => t && clearTimeout(t)\n      ),\n    setUserOffline: () =>\n      this.currentUserId &&\n      this.MessageService.setUserOffline(this.currentUserId).subscribe(),\n    stopTypingIndicator: () => {\n      if (this.isCurrentlyTyping && this.conversation?.id) {\n        this.isCurrentlyTyping = false;\n        clearTimeout(this.typingTimer);\n        this.MessageService.stopTyping(this.conversation.id).subscribe({\n          next: () => {},\n          error: (error) => {},\n        });\n      }\n    },\n  };\n\n  ngOnDestroy(): void {\n    this.cleanup.clearTimeouts();\n    this.cleanup.setUserOffline();\n    this.cleanup.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n}\n"], "mappings": ";AAAA,SACEA,SAAS,EAGTC,SAAS,QAIJ,eAAe;AAGtB,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAGEC,WAAW,EACXC,QAAQ,QACH,8BAA8B;AAErC,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,MAAM,QAAQ,gBAAgB;AAQjE,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EA8K/B;EACA,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACC,CAAC,CAACC,SAAS;EACzB;EA6BA;EACA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACC,cAAc,CAACC,eAAe,EAAE;EAC9C;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAO,CACL;MACEC,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC;MACzCC,QAAQ,EAAE;KACX,EACD;MACEL,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC;MACzCC,QAAQ,EAAE;KACX,EACD;MACEL,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,YAAY;MACnBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACG,eAAe,EAAE;MACrCD,QAAQ,EAAE,IAAI,CAACE,aAAa;MAC5BC,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACER,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,sBAAsB,IAAI,CAACO,sBAAsB,EAAE,GAAG;MAC7DN,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACO,oBAAoB,EAAE;MAC1CL,QAAQ,EAAE,IAAI,CAACM,kBAAkB;MACjCH,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DI,KAAK,EACH,IAAI,CAACH,sBAAsB,EAAE,GAAG,CAAC,GAC7B;QACEI,KAAK,EAAE,IAAI,CAACJ,sBAAsB,EAAE;QACpCT,KAAK,EAAE,gCAAgC;QACvCc,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEd,KAAK,EAAE,4BAA4B;MACnCC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,eAAe;MACtBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACY,uBAAuB,EAAE;MAC7CV,QAAQ,EAAE,IAAI,CAACW,qBAAqB;MACpCR,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DI,KAAK,EACH,IAAI,CAACK,uBAAuB,GAAG,CAAC,GAC5B;QACEJ,KAAK,EAAE,IAAI,CAACI,uBAAuB;QACnCjB,KAAK,EAAE,8CAA8C;QACrDc,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEd,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,uBAAuB;MAC9BC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACe,sBAAsB,EAAE;MAC5Cb,QAAQ,EAAE,IAAI,CAACc,oBAAoB;MACnCX,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACER,KAAK,EAAE,oBAAoB;MAC3BC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,uBAAuB;MAC9BC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACiB,oBAAoB,EAAE;MAC1Cf,QAAQ,EAAE,IAAI,CAACgB,kBAAkB;MACjCb,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACER,KAAK,EAAE,6BAA6B;MACpCC,IAAI,EAAE,mBAAmB;MACzBC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACmB,wBAAwB,EAAE;MAC9CjB,QAAQ,EAAE,IAAI,CAACkB,sBAAsB;MACrCf,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DI,KAAK,EACH,IAAI,CAACY,aAAa,CAACC,MAAM,GAAG,CAAC,GACzB;QACEZ,KAAK,EAAE,IAAI,CAACW,aAAa,CAACC,MAAM;QAChCzB,KAAK,EAAE,cAAc;QACrBc,OAAO,EAAE;OACV,GACD;KACP,CACF;EACH;EAEAY,YACU7B,cAA8B,EAC/B8B,KAAqB,EACpBC,WAA4B,EAC5BC,EAAe,EAChBC,aAAgC,EAChCC,MAAc,EACbC,YAA0B,EAE1BC,GAAsB;IARtB,KAAApC,cAAc,GAAdA,cAAc;IACf,KAAA8B,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,YAAY,GAAZA,YAAY;IAEZ,KAAAC,GAAG,GAAHA,GAAG;IAjTb,KAAAC,QAAQ,GAAc,EAAE;IAExB,KAAAC,YAAY,GAAwB,IAAI;IACxC,KAAAC,OAAO,GAAG,IAAI;IAEd,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,eAAe,GAAW,KAAK;IAC/B,KAAAC,gBAAgB,GAAgB,IAAI;IACpC,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,UAAU,GAAgC,IAAI;IAC9C,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAET,KAAAC,qBAAqB,GAAG,CAAC;IACzB,KAAAC,oBAAoB,GAAG,EAAE;IACzB,KAAAC,kBAAkB,GAAG,GAAG;IACjC,KAAAC,WAAW,GAAG,CAAC;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACd,KAAAC,aAAa,GAAG,IAAIlE,YAAY,EAAE;IAE1C;IACA,KAAAmE,aAAa,GAAW,eAAe;IAEvC;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAjD,aAAa,GAAG,KAAK;IACrB,KAAAI,kBAAkB,GAAG,KAAK;IAC1B,KAAA8C,kBAAkB,GAAG,KAAK;IAC1B,KAAAzC,qBAAqB,GAAG,KAAK;IAC7B,KAAA0C,wBAAwB,GAAG,KAAK;IAChC,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAxC,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,kBAAkB,GAAG,KAAK;IAC1B,KAAAE,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAqC,YAAY,GAAQ,IAAI;IACxB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,SAAS,GAAQ,IAAI;IAErB;IACA,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAnD,uBAAuB,GAAW,CAAC;IACnC,KAAAoD,qBAAqB,GAAgB,IAAIC,GAAG,EAAE;IAC9C,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,iBAAiB,GAAQ,IAAI;IAE7B;IACA,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,aAAa,GAAU,EAAE;IAEzB;IACA,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,kBAAkB,GAA+B,EAAE;IACnD,KAAAC,iBAAiB,GAA+B,EAAE;IAClD,KAAAC,cAAc,GAA+B,EAAE;IAC/C,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,qBAAqB,GAAa,EAAE;IACpC,KAAAC,sBAAsB,GAAU,EAAE;IAClC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,sBAAsB,GAAG,KAAK;IAE9B;IACS,KAAAhG,CAAC,GAAG;MACXC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC/CgG,MAAM,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAG,CAAE;MAC9DC,KAAK,EAAE,6BAA6B;MACpCC,SAAS,EAAEA,CAACC,CAAS,EAAEC,IAAS,KAAaA,IAAI,EAAEC,EAAE,IAAIF,CAAC,CAACG,QAAQ,EAAE;MACrEjC,aAAa,EAAE;QACbkC,WAAW,EAAE;UAAErG,IAAI,EAAE,gBAAgB;UAAEsG,KAAK,EAAE;QAAe,CAAE;QAC/DC,WAAW,EAAE;UAAEvG,IAAI,EAAE,oBAAoB;UAAEsG,KAAK,EAAE;QAAc,CAAE;QAClEE,MAAM,EAAE;UAAExG,IAAI,EAAE,YAAY;UAAEsG,KAAK,EAAE;QAAe;OACrD;MACDG,MAAM,EAAE;QACNC,MAAM,EAAE;UACNC,IAAI,EAAE,UAAU;UAChBL,KAAK,EAAE,gBAAgB;UACvBtG,IAAI,EAAE,eAAe;UACrB4G,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAE;SACd;QACDC,OAAO,EAAE;UACPH,IAAI,EAAE,YAAY;UAClBL,KAAK,EAAE,eAAe;UACtBtG,IAAI,EAAE,eAAe;UACrB4G,KAAK,EAAE,YAAY;UACnBC,WAAW,EAAE;SACd;QACDlB,IAAI,EAAE;UACJgB,IAAI,EAAE,QAAQ;UACdL,KAAK,EAAE,iBAAiB;UACxBtG,IAAI,EAAE,cAAc;UACpB4G,KAAK,EAAE,QAAQ;UACfC,WAAW,EAAE;SACd;QACDE,IAAI,EAAE;UACJJ,IAAI,EAAE,QAAQ;UACdL,KAAK,EAAE,cAAc;UACrBtG,IAAI,EAAE,qBAAqB;UAC3B4G,KAAK,EAAE,QAAQ;UACfC,WAAW,EAAE;;OAEhB;MACDG,MAAM,EAAE,CACN;QACEC,GAAG,EAAE,eAAe;QACpBL,KAAK,EAAE,QAAQ;QACfN,KAAK,EAAE,SAAS;QAChBY,UAAU,EAAE;OACb,EACD;QACED,GAAG,EAAE,gBAAgB;QACrBL,KAAK,EAAE,MAAM;QACbN,KAAK,EAAE,SAAS;QAChBY,UAAU,EAAE;OACb,EACD;QACED,GAAG,EAAE,iBAAiB;QACtBL,KAAK,EAAE,MAAM;QACbN,KAAK,EAAE,SAAS;QAChBY,UAAU,EAAE;OACb,EACD;QACED,GAAG,EAAE,eAAe;QACpBL,KAAK,EAAE,MAAM;QACbN,KAAK,EAAE,SAAS;QAChBY,UAAU,EAAE;OACb,CACF;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;OACX;MACD;MACAC,kBAAkB,EAAE;QAClBlB,WAAW,EAAE;UAAErG,IAAI,EAAE,gBAAgB;UAAEsG,KAAK,EAAE;QAAe,CAAE;QAC/DC,WAAW,EAAE;UAAEvG,IAAI,EAAE,oBAAoB;UAAEsG,KAAK,EAAE;QAAc,CAAE;QAClEE,MAAM,EAAE;UAAExG,IAAI,EAAE,YAAY;UAAEsG,KAAK,EAAE;QAAe;OACrD;MACDkB,gBAAgB,EAAE;QAChBJ,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;;KAEb;IAOD;IACA,KAAAG,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,uBAAuB,GAAG,KAAK;IAC/B,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,cAAc,GAAG,IAAI;IAErB;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,WAAW,GAAG,YAAY;IAC1B,KAAAC,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAC,WAAW,GAAsB,IAAIC,GAAG,EAAE;IAC1C,KAAAC,iBAAiB,GAAW,QAAQ;IACpC,KAAAC,gBAAgB,GAAS,IAAIC,IAAI,EAAE;IACnC,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAApH,aAAa,GAAU,EAAE;IACzB,KAAAqH,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,gBAAgB,GAAG,KAAK;IAuLxB;IACS,KAAAC,CAAC,GAAG;MACXC,OAAO,EAAGC,CAAU,IAAK,IAAI,CAACrJ,cAAc,CAACsJ,WAAW,CAACD,CAAC,CAAC;MAC3DE,OAAO,EAAGF,CAAU,IAAK,IAAI,CAACrJ,cAAc,CAACwJ,WAAW,CAACH,CAAC;KAC3D;IAED,KAAAC,WAAW,GAAG,IAAI,CAACH,CAAC,CAACC,OAAO;IAC5B,KAAAI,WAAW,GAAG,IAAI,CAACL,CAAC,CAACI,OAAO;IAmLpB,KAAAE,iBAAiB,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,GAAG;IAClB,KAAAC,cAAc,GAAG,IAAI;IAuDtC;IACS,KAAAC,iBAAiB,GAAG;MAC3BC,aAAa,EAAEA,CAAA,KAAM,IAAI,CAACC,WAAW,CAAC,OAAO,CAAC;MAC9CC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACD,WAAW,CAAC,MAAM,CAAC;MACxCE,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACF,WAAW,CAAC,OAAO;KAC5C;IAED,KAAAG,mBAAmB,GAAG,IAAI,CAACL,iBAAiB,CAACC,aAAa;IAC1D,KAAAK,cAAc,GAAG,IAAI,CAACN,iBAAiB,CAACG,QAAQ;IAChD,KAAAI,iBAAiB,GAAG,IAAI,CAACP,iBAAiB,CAACI,WAAW;IAQtD;IACiB,KAAAI,aAAa,GAAG;MAC/BlF,cAAc,EAAEA,CAAA,KAAO,IAAI,CAACpE,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAmB;MAC1EuJ,SAAS,EAAEA,CAAA,KAAK;QACd,IAAI,CAACP,WAAW,CAAC,QAAQ,CAAC;QAC1B,IAAI,CAAC,IAAI,CAACpJ,aAAa,EAAE,IAAI,CAAC4J,WAAW,EAAE;MAC7C,CAAC;MACDC,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACT,WAAW,CAAC,QAAQ,CAAC;MAChDU,oBAAoB,EAAEA,CAAA,KACnB,IAAI,CAAC3G,wBAAwB,GAAG,CAAC,IAAI,CAACA,wBAAyB;MAClE4G,eAAe,EAAEA,CAAA,KACd,IAAI,CAAC3G,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAoB;MACxD4G,YAAY,EAAEA,CAAA,KAAO,IAAI,CAACrC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAgB;MAClEsC,gBAAgB,EAAEA,CAAA,KACf,IAAI,CAACrJ,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAqB;MAC1DsJ,cAAc,EAAEA,CAAA,KAAO,IAAI,CAACpJ,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAmB;MAC1EqJ,kBAAkB,EAAEA,CAAA,KACjB,IAAI,CAACnJ,sBAAsB,GAAG,CAAC,IAAI,CAACA;KACxC;IAED,KAAAb,oBAAoB,GAAG,IAAI,CAACuJ,aAAa,CAAClF,cAAc;IACxD,KAAAzE,eAAe,GAAG,IAAI,CAAC2J,aAAa,CAACC,SAAS;IAC9C,KAAAS,oBAAoB,GAAG,IAAI,CAACV,aAAa,CAACG,cAAc;IACxD,KAAAQ,0BAA0B,GAAG,IAAI,CAACX,aAAa,CAACI,oBAAoB;IACpE,KAAAQ,qBAAqB,GAAG,IAAI,CAACZ,aAAa,CAACK,eAAe;IAC1D,KAAAQ,kBAAkB,GAAG,IAAI,CAACb,aAAa,CAACM,YAAY;IACpD,KAAArJ,sBAAsB,GAAG,IAAI,CAAC+I,aAAa,CAACO,gBAAgB;IAC5D,KAAApJ,oBAAoB,GAAG,IAAI,CAAC6I,aAAa,CAACQ,cAAc;IACxD,KAAAnJ,wBAAwB,GAAG,IAAI,CAAC2I,aAAa,CAACS,kBAAkB;IAQvD,KAAAK,mBAAmB,GAAG;MAC7BC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACC,sBAAsB,CAAC,8BAA8B,CAAC;MAC3EC,YAAY,EAAEA,CAAA,KACZ,IAAI,CAACD,sBAAsB,CAAC,4BAA4B,CAAC;MAC3DE,KAAK,EAAEA,CAAA,KAAW;QAChB,IAAI,CAAC,IAAI,CAAChJ,YAAY,EAAEiE,EAAE,IAAI,IAAI,CAAClE,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;UACxD,IAAI,CAACO,YAAY,CAACoJ,WAAW,CAAC,6BAA6B,CAAC;UAC5D;;QAGF,IACEC,OAAO,CACL,oHAAoH,CACrH,EACD;UACA,IAAI,CAACnJ,QAAQ,GAAG,EAAE;UAClB,IAAI,CAACqB,YAAY,GAAG,KAAK;UACzB,IAAI,CAACvB,YAAY,CAACsJ,WAAW,CAAC,gCAAgC,CAAC;;MAEnE,CAAC;MACDC,MAAM,EAAEA,CAAA,KAAW;QACjB,IAAI,CAAC,IAAI,CAACpJ,YAAY,EAAEiE,EAAE,IAAI,IAAI,CAAClE,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;UACxD,IAAI,CAACO,YAAY,CAACoJ,WAAW,CAAC,gCAAgC,CAAC;UAC/D;;QAGF,MAAMI,gBAAgB,GAAG,IAAI,CAACrJ,YAAY,CAACsJ,OAAO,GAC9C,IAAI,CAACtJ,YAAY,CAACuJ,SAAS,IAAI,iBAAiB,GAChD,IAAI,CAACnJ,gBAAgB,EAAEoJ,QAAQ,IAAI,qBAAqB;QAE5D,MAAMC,UAAU,GAAG;UACjBzJ,YAAY,EAAE;YACZiE,EAAE,EAAE,IAAI,CAACjE,YAAY,CAACiE,EAAE;YACxByF,IAAI,EAAEL,gBAAgB;YACtBC,OAAO,EAAE,IAAI,CAACtJ,YAAY,CAACsJ,OAAO;YAClCK,YAAY,EAAE,IAAI,CAAC3J,YAAY,CAAC2J,YAAY;YAC5CC,SAAS,EAAE,IAAI,CAAC5J,YAAY,CAAC4J;WAC9B;UACD7J,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC8J,GAAG,CAAEC,GAAG,KAAM;YACpC7F,EAAE,EAAE6F,GAAG,CAAC7F,EAAE;YACV8F,OAAO,EAAED,GAAG,CAACC,OAAO;YACpBC,MAAM,EAAEF,GAAG,CAACE,MAAM;YAClBC,SAAS,EAAEH,GAAG,CAACG,SAAS;YACxBC,IAAI,EAAEJ,GAAG,CAACI;WACX,CAAC,CAAC;UACHC,UAAU,EAAE,IAAI7D,IAAI,EAAE,CAAC8D,WAAW,EAAE;UACpCC,UAAU,EAAE,IAAI,CAACnK;SAClB;QAED,MAAMoK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAAChB,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;UAC3DS,IAAI,EAAE;SACP,CAAC;QACF,MAAMQ,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;QAC5C,MAAMQ,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;QAEf,MAAMQ,YAAY,GAAG7B,gBAAgB,CAClC8B,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAC3BC,WAAW,EAAE;QAChB,MAAMC,OAAO,GAAG,IAAI/E,IAAI,EAAE,CAAC8D,WAAW,EAAE,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtDR,IAAI,CAACS,QAAQ,GAAG,gBAAgBL,YAAY,IAAIG,OAAO,OAAO;QAE9DP,IAAI,CAACU,KAAK,EAAE;QACZb,MAAM,CAACC,GAAG,CAACa,eAAe,CAACf,GAAG,CAAC;QAE/B,IAAI,CAACtJ,YAAY,GAAG,KAAK;QACzB,IAAI,CAACvB,YAAY,CAACsJ,WAAW,CAAC,mCAAmC,CAAC;MACpE;KACD;IAED;IACA,KAAAuC,sBAAsB,GAAG,IAAI,CAAC9C,mBAAmB,CAACC,QAAQ;IAC1D,KAAA8C,0BAA0B,GAAG,IAAI,CAAC/C,mBAAmB,CAACG,YAAY;IAClE,KAAA6C,iBAAiB,GAAG,IAAI,CAAChD,mBAAmB,CAACI,KAAK;IAClD,KAAA6C,kBAAkB,GAAG,IAAI,CAACjD,mBAAmB,CAACQ,MAAM;IA6GpD;IACS,KAAA0C,CAAC,GAAG;MACXC,UAAU,EAAGhF,CAA4B,IACvC,IAAI,CAACrJ,cAAc,CAACsO,iBAAiB,CAACjF,CAAC,CAAC;MAC1CkF,YAAY,EAAGlF,CAA4B,IACzC,IAAI,CAACrJ,cAAc,CAACwO,gBAAgB,CAACnF,CAAC,CAAC;MACzCoF,UAAU,EAAGpF,CAA4B,IACvC,IAAI,CAACrJ,cAAc,CAAC0O,iBAAiB,CAACrF,CAAC,CAAC;MAC1CsF,cAAc,EAAGtI,CAAS,IACxB,IAAI,CAACrG,cAAc,CAAC4O,oBAAoB,CAAC,IAAI,CAACvM,QAAQ,EAAEgE,CAAC,CAAC;MAC5DkD,OAAO,EAAGsF,CAA6B,IACrC,IAAI,CAAC7O,cAAc,CAAC8O,cAAc,CAACD,CAAC,CAAC;MACvCE,QAAQ,EAAGF,CAA6B,IACtC,IAAI,CAAC7O,cAAc,CAAC+O,QAAQ,CAACF,CAAC,CAAC;MACjCG,OAAO,EAAGH,CAA6B,IACrC,IAAI,CAAC7O,cAAc,CAACiP,cAAc,CAACJ,CAAC,CAAC;MACvCK,WAAW,EAAGL,CAA6B,IACzC,IAAI,CAAC7O,cAAc,CAACmP,kBAAkB,CAACN,CAAC,CAAC;MAC3CO,gBAAgB,EAAGP,CAA6B,IAC9C,IAAI,CAAC7O,cAAc,CAACqP,uBAAuB,CAACR,CAAC,CAAC;MAChDS,cAAc,EAAGjJ,CAAS,IAAK,IAAI,CAACrG,cAAc,CAACuP,iBAAiB,CAAClJ,CAAC,CAAC;MACvEmJ,WAAW,EAAGpB,CAAS,IAAK,IAAI,CAACpO,cAAc,CAACyP,mBAAmB,CAACrB,CAAC,CAAC;MACtEsB,WAAW,EAAGb,CAA6B,IACzC,IAAI,CAAC7O,cAAc,CAAC0P,WAAW,CAACb,CAAC,CAAC;MACpCc,YAAY,EAAGd,CAA6B,IAC1C,IAAI,CAAC7O,cAAc,CAAC4P,mBAAmB,CAACf,CAAC,EAAE,IAAI,CAACrM,aAAa;KAChE;IAED;IACA,KAAA8L,iBAAiB,GAAG,IAAI,CAACF,CAAC,CAACC,UAAU;IACrC,KAAAG,gBAAgB,GAAG,IAAI,CAACJ,CAAC,CAACG,YAAY;IACtC,KAAAG,iBAAiB,GAAG,IAAI,CAACN,CAAC,CAACK,UAAU;IACrC,KAAAG,oBAAoB,GAAG,IAAI,CAACR,CAAC,CAACO,cAAc;IAC5C,KAAAG,cAAc,GAAG,IAAI,CAACV,CAAC,CAAC7E,OAAO;IAC/B,KAAAwF,QAAQ,GAAG,IAAI,CAACX,CAAC,CAACW,QAAQ;IAC1B,KAAAE,cAAc,GAAG,IAAI,CAACb,CAAC,CAACY,OAAO;IAC/B,KAAAG,kBAAkB,GAAG,IAAI,CAACf,CAAC,CAACc,WAAW;IACvC,KAAAG,uBAAuB,GAAG,IAAI,CAACjB,CAAC,CAACgB,gBAAgB;IACjD,KAAAG,iBAAiB,GAAG,IAAI,CAACnB,CAAC,CAACkB,cAAc;IACzC,KAAAG,mBAAmB,GAAG,IAAI,CAACrB,CAAC,CAACoB,WAAW;IACxC,KAAAE,WAAW,GAAG,IAAI,CAACtB,CAAC,CAACsB,WAAW;IAChC,KAAAE,mBAAmB,GAAG,IAAI,CAACxB,CAAC,CAACuB,YAAY;IAkFzC;IACS,KAAAE,uBAAuB,GAAG;MACjCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACzC,QAAQ,CAAC0C,cAAc,CAAC,2BAA2B,CAAC,EAAE;UACzD,MAAMC,SAAS,GAAG3C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC/C0C,SAAS,CAACzJ,EAAE,GAAG,2BAA2B;UAC1CyJ,SAAS,CAACC,SAAS,GAAG,wCAAwC;UAC9DD,SAAS,CAACE,SAAS,GACjB,uEAAuE;UACzE,IAAI,CAACC,iBAAiB,EAAEC,aAAa,EAAEC,OAAO,CAACL,SAAS,CAAC;;MAE7D,CAAC;MACDM,IAAI,EAAEA,CAAA,KAAK;QACT,MAAMN,SAAS,GAAG3C,QAAQ,CAAC0C,cAAc,CAAC,2BAA2B,CAAC;QACtEC,SAAS,EAAEO,UAAU,EAAEC,WAAW,CAACR,SAAS,CAAC;MAC/C;KACD;IAEO,KAAAS,oBAAoB,GAAG,IAAI,CAACZ,uBAAuB,CAACC,IAAI;IACxD,KAAAY,oBAAoB,GAAG,IAAI,CAACb,uBAAuB,CAACS,IAAI;IAuHhE;IACS,KAAAK,qBAAqB,GAAG;MAC/BC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC7N,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;QAC9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE,IAAI,CAACC,sBAAsB,GAAG,CAAC;MAC7D,CAAC;MACD6N,QAAQ,EAAGC,SAAe,IAAI;QAC5B,IAAI,CAAC,IAAI,CAACxO,YAAY,EAAEiE,EAAE,IAAI,CAAC,IAAI,CAAC7D,gBAAgB,EAAE6D,EAAE,EAAE;UACxD,IAAI,CAACpE,YAAY,CAAC4O,SAAS,CAAC,uCAAuC,CAAC;UACpE,IAAI,CAAChO,gBAAgB,GAAG,KAAK;UAC7B;;QAEF,MAAMiO,UAAU,GAAG,IAAI,CAACtO,gBAAgB,EAAE6D,EAAE,IAAI,EAAE;QAClD,IAAI,CAACvG,cAAc,CAACiR,gBAAgB,CAClCD,UAAU,EACVF,SAAS,EACT,IAAI,CAACxO,YAAY,EAAEiE,EAAE,EACrB,IAAI,CAACvD,sBAAsB,CAC5B,CAACkO,SAAS,CAAC;UACVC,IAAI,EAAGC,OAAO,IAAI;YAChB,IAAI,CAACrO,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;YAC/B,IAAI,CAACqO,cAAc,CAAC,IAAI,CAAC;UAC3B,CAAC;UACDlL,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAChE,YAAY,CAAC4O,SAAS,CAAC,8BAA8B,CAAC;YAC3D,IAAI,CAAChO,gBAAgB,GAAG,KAAK;UAC/B;SACD,CAAC;MACJ,CAAC;MACDuO,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACvO,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;MACjC;KACD;IAED,KAAAuO,oBAAoB,GAAG,IAAI,CAACZ,qBAAqB,CAACC,MAAM;IACxD,KAAAY,wBAAwB,GAAG,IAAI,CAACb,qBAAqB,CAACE,QAAQ;IAC9D,KAAAY,yBAAyB,GAAG,IAAI,CAACd,qBAAqB,CAACW,MAAM;IA8I7D;IACS,KAAAI,WAAW,GAAG;MACrBC,QAAQ,EAAGnF,IAAuB,IAAI;QACpC,IAAI,CAAC,IAAI,CAAC9J,gBAAgB,EAAE6D,EAAE,EAAE;QAChC,IAAI,CAACvG,cAAc,CAACO,YAAY,CAC9B,IAAI,CAACmC,gBAAgB,CAAC6D,EAAE,EACxBiG,IAAI,KAAK,OAAO,GAAGjN,QAAQ,CAACqS,KAAK,GAAGrS,QAAQ,CAACsS,KAAK,EAClD,IAAI,CAACvP,YAAY,EAAEiE,EAAE,CACtB,CAAC2K,SAAS,CAAC;UACVC,IAAI,EAAGW,IAAI,IAAI,CAAE,CAAC;UAClB3L,KAAK,EAAEA,CAAA,KAAM,IAAI,CAAChE,YAAY,CAAC4O,SAAS,CAAC,IAAI,CAAClR,CAAC,CAACsG,KAAK;SACtD,CAAC;MACJ,CAAC;MACD4L,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC,IAAI,CAAChO,YAAY,EAAE;QACxB,IAAI,CAAC/D,cAAc,CAACgS,UAAU,CAAC,IAAI,CAACjO,YAAY,CAACwC,EAAE,CAAC,CAAC2K,SAAS,CAAC;UAC7DC,IAAI,EAAGW,IAAI,IAAI;YACb,IAAI,CAAC7N,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;YACxB,IAAI,CAACK,cAAc,GAAG,IAAI,CAACL,YAAY,EAAEyI,IAAI,KAAK,OAAO;YACzD,IAAI,CAAClE,WAAW,GAAG,YAAY;YAC/B,IAAI,CAACnG,YAAY,CAACsJ,WAAW,CAAC,gBAAgB,CAAC;UACjD,CAAC;UACDtF,KAAK,EAAEA,CAAA,KAAK;YACV,IAAI,CAAChE,YAAY,CAAC4O,SAAS,CAAC,IAAI,CAAClR,CAAC,CAACsG,KAAK,CAAC;YACzC,IAAI,CAAClC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;UAC1B;SACD,CAAC;MACJ,CAAC;MACDkO,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC,IAAI,CAAClO,YAAY,EAAE;QACxB,IAAI,CAAC/D,cAAc,CAACkS,UAAU,CAAC,IAAI,CAACnO,YAAY,CAACwC,EAAE,CAAC,CAAC2K,SAAS,CAAC;UAC7DC,IAAI,EAAGW,IAAI,IAAI;YACb,IAAI,CAAC7N,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;UAC1B,CAAC;UACDoC,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAClC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;UAC1B;SACD,CAAC;MACJ,CAAC;MACDoO,GAAG,EAAEA,CAAA,KAAK;QACR,MAAMC,GAAG,GAAG,IAAI,CAACpS,cAAc,CAACqS,WAAW,CAACnB,SAAS,CAAEY,IAAI,IAAI;UAC7D,IAAIA,IAAI,EAAE;YACR,IAAI,CAAC9R,cAAc,CAACsS,OAAO,CAACR,IAAI,CAACvL,EAAE,CAAC,CAAC2K,SAAS,CAAC;cAC7CC,IAAI,EAAGW,IAAI,IAAI,CAAE,CAAC;cAClB3L,KAAK,EAAGA,KAAK,IAAI,CAAE;aACpB,CAAC;;QAEN,CAAC,CAAC;QACFiM,GAAG,CAACG,WAAW,EAAE;MACnB;KACD;IAED,KAAAhS,YAAY,GAAG,IAAI,CAACmR,WAAW,CAACC,QAAQ;IACxC,KAAAK,UAAU,GAAG,IAAI,CAACN,WAAW,CAACK,MAAM;IACpC,KAAAG,UAAU,GAAG,IAAI,CAACR,WAAW,CAACO,MAAM;IACpC,KAAAK,OAAO,GAAG,IAAI,CAACZ,WAAW,CAACS,GAAG;IAE9B;IACS,KAAAK,kBAAkB,GAAG;MAC5BC,UAAU,EAAEA,CAAA,KAAK;QACf,IAAI,CAACtO,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;QACpC,IAAI,CAACqO,kBAAkB,CAACE,WAAW,EAAE;QACrC,IAAI,CAACvQ,YAAY,CAACgJ,QAAQ,CACxB,IAAI,CAAChH,WAAW,GAAG,sBAAsB,GAAG,mBAAmB,CAChE;MACH,CAAC;MACDwO,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAACvO,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAACoO,kBAAkB,CAACE,WAAW,EAAE;QACrC,IAAI,CAACvQ,YAAY,CAACgJ,QAAQ,CACxB,IAAI,CAAC/G,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACDsO,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAAC1S,cAAc,CAAC4S,WAAW,CAC7B,IAAI,CAAC5O,UAAU,EAAEuC,EAAE,EACnB,CAAC,IAAI,CAACpC,WAAW,EACjB,IAAI,CAACC,cAAc,CACpB,CAAC8M,SAAS,CAAC;UACVC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;UACdhL,KAAK,EAAGA,KAAK,IACX0M,OAAO,CAAC1M,KAAK,CAAC,2CAA2C,EAAEA,KAAK;SACnE,CAAC;MACJ;KACD;IAED,KAAA2M,cAAc,GAAG,IAAI,CAACN,kBAAkB,CAACC,UAAU;IACnD,KAAAM,eAAe,GAAG,IAAI,CAACP,kBAAkB,CAACG,WAAW;IAC7C,KAAAK,eAAe,GAAG,IAAI,CAACR,kBAAkB,CAACE,WAAW;IAE7D;IACS,KAAAO,YAAY,GAAG;MACtBC,cAAc,EAAEA,CAAA,KAAK;QACnB,IAAI,CAAC7O,YAAY,GAAG,CAAC;QACrB,IAAI,CAACC,SAAS,GAAG6O,WAAW,CAAC,MAAK;UAChC,IAAI,CAAC9O,YAAY,EAAE;UACnB,IAAI,IAAI,CAACA,YAAY,KAAK,CAAC,IAAI,IAAI,CAACiE,WAAW,KAAK,YAAY,EAAE;YAChE,IAAI,CAACA,WAAW,GAAG,WAAW;;QAElC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACD8K,aAAa,EAAEA,CAAA,KAAK;QAClB,IAAI,IAAI,CAAC9O,SAAS,EAAE;UAClB+O,aAAa,CAAC,IAAI,CAAC/O,SAAS,CAAC;UAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;MAEzB,CAAC;MACDgP,cAAc,EAAEA,CAAA,KAAO,IAAI,CAACjP,YAAY,GAAG;KAC5C;IAEO,KAAAkP,oBAAoB,GAAG,IAAI,CAACN,YAAY,CAACC,cAAc;IACvD,KAAAM,mBAAmB,GAAG,IAAI,CAACP,YAAY,CAACG,aAAa;IACrD,KAAAK,oBAAoB,GAAG,IAAI,CAACR,YAAY,CAACK,cAAc;IAE/D;IAEA;IACS,KAAAI,wBAAwB,GAAG;MAClC5J,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAACA,WAAW,CAAC,cAAc,CAAC;QAChC,IAAI,IAAI,CAAC3I,qBAAqB,EAAE;UAC9B,IAAI,CAACwS,iBAAiB,EAAE;;MAE5B;KACD;IAED,KAAAzS,uBAAuB,GAAG,IAAI,CAACwS,wBAAwB,CAAC5J,WAAW;IA8BnE;IACS,KAAA8J,mBAAmB,GAAG;MAC7BC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACF,iBAAiB,EAAE;MACxCG,WAAW,EAAEA,CAAA,KACV,IAAI,CAAC1S,uBAAuB,GAAG,IAAI,CAACmD,aAAa,CAAC7E,MAAM,CACtDqU,CAAC,IAAK,CAACA,CAAC,CAACC,MAAM,CACjB,CAACpS,MAAO;MACXqS,WAAW,EAAEA,CAAA,KAAM,IAAI,CAAC1P,aAAa;MACrC2P,eAAe,EAAGC,cAAsB,IAAI;QAC1C,IAAI,IAAI,CAAC3P,qBAAqB,CAAC4P,GAAG,CAACD,cAAc,CAAC,EAAE;UAClD,IAAI,CAAC3P,qBAAqB,CAAC6P,MAAM,CAACF,cAAc,CAAC;SAClD,MAAM;UACL,IAAI,CAAC3P,qBAAqB,CAAC8P,GAAG,CAACH,cAAc,CAAC;;MAElD,CAAC;MACDI,eAAe,EAAEA,CAAA,KAAK;QACpB,MAAMC,qBAAqB,GAAG,IAAI,CAACZ,mBAAmB,CAACK,WAAW,EAAE;QACpE,MAAMQ,WAAW,GAAGD,qBAAqB,CAACE,KAAK,CAAEX,CAAC,IAChD,IAAI,CAACvP,qBAAqB,CAAC4P,GAAG,CAACL,CAAC,CAACxN,EAAE,CAAC,CACrC;QACD,IAAIkO,WAAW,EAAE;UACfD,qBAAqB,CAACG,OAAO,CAAEZ,CAAC,IAC9B,IAAI,CAACvP,qBAAqB,CAAC6P,MAAM,CAACN,CAAC,CAACxN,EAAE,CAAC,CACxC;SACF,MAAM;UACLiO,qBAAqB,CAACG,OAAO,CAAEZ,CAAC,IAC9B,IAAI,CAACvP,qBAAqB,CAAC8P,GAAG,CAACP,CAAC,CAACxN,EAAE,CAAC,CACrC;;MAEL,CAAC;MACDqO,cAAc,EAAEA,CAAA,KAAK;QACnB,MAAMJ,qBAAqB,GAAG,IAAI,CAACZ,mBAAmB,CAACK,WAAW,EAAE;QACpE,OACEO,qBAAqB,CAAC5S,MAAM,GAAG,CAAC,IAChC4S,qBAAqB,CAACE,KAAK,CAAEX,CAAC,IAAK,IAAI,CAACvP,qBAAqB,CAAC4P,GAAG,CAACL,CAAC,CAACxN,EAAE,CAAC,CAAC;MAE5E;KACD;IAED,KAAAsO,qBAAqB,GAAG,IAAI,CAACjB,mBAAmB,CAACC,QAAQ;IACjD,KAAAiB,uBAAuB,GAAG,IAAI,CAAClB,mBAAmB,CAACE,WAAW;IACtE,KAAAiB,wBAAwB,GAAG,IAAI,CAACnB,mBAAmB,CAACK,WAAW;IAC/D,KAAAe,2BAA2B,GAAG,IAAI,CAACpB,mBAAmB,CAACM,eAAe;IACtE,KAAAe,4BAA4B,GAAG,IAAI,CAACrB,mBAAmB,CAACW,eAAe;IACvE,KAAAW,2BAA2B,GAAG,IAAI,CAACtB,mBAAmB,CAACgB,cAAc;IAErE;IACS,KAAAO,cAAc,GAAG;MACxBC,YAAY,EAAEA,CAAA,KAAK;QACjB,MAAMC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/Q,qBAAqB,CAAC;QAC1D,IAAI6Q,WAAW,CAACzT,MAAM,KAAK,CAAC,EAAE;UAC5B,IAAI,CAACO,YAAY,CAACoJ,WAAW,CAAC,kCAAkC,CAAC;UACjE;;QAEF,IAAI,CAAC4J,cAAc,CAACK,UAAU,CAACH,WAAW,EAAE,MAAK;UAC/C,IAAI,CAAC7Q,qBAAqB,CAAC8G,KAAK,EAAE;UAClC,IAAI,CAACnJ,YAAY,CAACsJ,WAAW,CAC3B,GAAG4J,WAAW,CAACzT,MAAM,0CAA0C,CAChE;QACH,CAAC,CAAC;MACJ,CAAC;MACD6T,OAAO,EAAEA,CAAA,KAAK;QACZ,MAAMC,mBAAmB,GAAG,IAAI,CAACnR,aAAa,CAAC7E,MAAM,CAAEqU,CAAC,IAAK,CAACA,CAAC,CAACC,MAAM,CAAC;QACvE,IAAI0B,mBAAmB,CAAC9T,MAAM,KAAK,CAAC,EAAE;UACpC,IAAI,CAACO,YAAY,CAACgJ,QAAQ,CAAC,6BAA6B,CAAC;UACzD;;QAEF,MAAMwK,SAAS,GAAGD,mBAAmB,CAACvJ,GAAG,CAAE4H,CAAC,IAAKA,CAAC,CAACxN,EAAE,CAAC;QACtD,IAAI,CAAC4O,cAAc,CAACK,UAAU,CAACG,SAAS,EAAE,MAAK;UAC7C,IAAI,CAACxT,YAAY,CAACsJ,WAAW,CAC3B,sDAAsD,CACvD;QACH,CAAC,CAAC;MACJ,CAAC;MACD+J,UAAU,EAAEA,CAACI,GAAa,EAAEC,SAAqB,KAAI;QACnD,MAAMC,OAAO,GAAG,IAAI,CAAC9V,cAAc,CAACwV,UAAU,CAACI,GAAG,CAAC,CAAC1E,SAAS,CAAC;UAC5DC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAACxR,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC4H,GAAG,CAAE4H,CAAC,IAC5C6B,GAAG,CAACI,QAAQ,CAACjC,CAAC,CAACxN,EAAE,CAAC,GAAG;cAAE,GAAGwN,CAAC;cAAEC,MAAM,EAAE,IAAI;cAAEiC,MAAM,EAAE,IAAIrN,IAAI;YAAE,CAAE,GAAGmL,CAAC,CACpE;YACD,IAAI,CAACe,uBAAuB,EAAE;YAC9Be,SAAS,EAAE;UACb,CAAC;UACD1P,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAChE,YAAY,CAAC4O,SAAS,CACzB,2CAA2C,CAC5C;UACH;SACD,CAAC;QACF,IAAI,CAACxN,aAAa,CAAC+Q,GAAG,CAACwB,OAAO,CAAC;MACjC;KACD;IAED,KAAAI,kBAAkB,GAAG,IAAI,CAACf,cAAc,CAACC,YAAY;IACrD,KAAAe,aAAa,GAAG,IAAI,CAAChB,cAAc,CAACM,OAAO;IAE3C;IACS,KAAAW,yBAAyB,GAAG;MACnCC,8BAA8B,EAAEA,CAAA,KAAK;QACnC,IAAI,IAAI,CAAC7R,qBAAqB,CAAC8R,IAAI,KAAK,CAAC,EAAE;UACzC,IAAI,CAACnU,YAAY,CAACoJ,WAAW,CAAC,kCAAkC,CAAC;UACjE;;QAEF,IAAI,CAAC7G,sBAAsB,GAAG,IAAI;MACpC,CAAC;MACD6R,cAAc,EAAEA,CAAA,KAAK;QACnB,MAAMlB,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/Q,qBAAqB,CAAC;QAC1D,IAAI6Q,WAAW,CAACzT,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACoG,uBAAuB,GAAG,IAAI;QACnC,IAAI,CAACtD,sBAAsB,GAAG,KAAK;QACnC,MAAM8R,SAAS,GAAG,IAAI,CAACxW,cAAc,CAACyW,2BAA2B,CAC/DpB,WAAW,CACZ,CAACnE,SAAS,CAAC;UACVC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAACxR,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC7E,MAAM,CAC3CqU,CAAC,IAAK,CAACsB,WAAW,CAACW,QAAQ,CAACjC,CAAC,CAACxN,EAAE,CAAC,CACnC;YACD,IAAI,CAAC/B,qBAAqB,CAAC8G,KAAK,EAAE;YAClC,IAAI,CAACwJ,uBAAuB,EAAE;YAC9B,IAAI,CAAC9M,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAAC7F,YAAY,CAACsJ,WAAW,CAC3B,GAAGsK,MAAM,CAAC/U,KAAK,+BAA+B,CAC/C;UACH,CAAC;UACDmF,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC6B,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAAC7F,YAAY,CAAC4O,SAAS,CACzB,iDAAiD,CAClD;UACH;SACD,CAAC;QACF,IAAI,CAACxN,aAAa,CAAC+Q,GAAG,CAACkC,SAAS,CAAC;MACnC,CAAC;MACDE,SAAS,EAAGvC,cAAsB,IAAI;QACpC,MAAMqC,SAAS,GAAG,IAAI,CAACxW,cAAc,CAAC2W,kBAAkB,CACtDxC,cAAc,CACf,CAACjD,SAAS,CAAC;UACVC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAACxR,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC7E,MAAM,CAC3CqU,CAAC,IAAKA,CAAC,CAACxN,EAAE,KAAK4N,cAAc,CAC/B;YACD,IAAI,CAAC3P,qBAAqB,CAAC6P,MAAM,CAACF,cAAc,CAAC;YACjD,IAAI,CAACW,uBAAuB,EAAE;YAC9B,IAAI,CAAC3S,YAAY,CAACsJ,WAAW,CAAC,wBAAwB,CAAC;UACzD,CAAC;UACDtF,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAChE,YAAY,CAAC4O,SAAS,CACzB,kDAAkD,CACnD;UACH;SACD,CAAC;QACF,IAAI,CAACxN,aAAa,CAAC+Q,GAAG,CAACkC,SAAS,CAAC;MACnC,CAAC;MACDI,SAAS,EAAEA,CAAA,KAAK;QACd,IAAI,IAAI,CAACrS,aAAa,CAAC3C,MAAM,KAAK,CAAC,EAAE;QACrC,IACE,CAAC4J,OAAO,CACN,8FAA8F,CAC/F,EACD;UACA;;QAEF,IAAI,CAACxD,uBAAuB,GAAG,IAAI;QACnC,MAAM6O,YAAY,GAChB,IAAI,CAAC7W,cAAc,CAAC8W,sBAAsB,EAAE,CAAC5F,SAAS,CAAC;UACrDC,IAAI,EAAG4E,MAAM,IAAI;YACf,IAAI,CAACxR,aAAa,GAAG,EAAE;YACvB,IAAI,CAACC,qBAAqB,CAAC8G,KAAK,EAAE;YAClC,IAAI,CAACwJ,uBAAuB,EAAE;YAC9B,IAAI,CAAC9M,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAAC7F,YAAY,CAACsJ,WAAW,CAC3B,GAAGsK,MAAM,CAAC/U,KAAK,uCAAuC,CACvD;UACH,CAAC;UACDmF,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC6B,uBAAuB,GAAG,KAAK;YACpC,IAAI,CAAC7F,YAAY,CAAC4O,SAAS,CACzB,2DAA2D,CAC5D;UACH;SACD,CAAC;QACJ,IAAI,CAACxN,aAAa,CAAC+Q,GAAG,CAACuC,YAAY,CAAC;MACtC,CAAC;MACDvF,MAAM,EAAEA,CAAA,KAAO,IAAI,CAAC5M,sBAAsB,GAAG;KAC9C;IAED,KAAA2R,8BAA8B,GAC5B,IAAI,CAACD,yBAAyB,CAACC,8BAA8B;IAC/D,KAAAU,2BAA2B,GAAG,IAAI,CAACX,yBAAyB,CAACG,cAAc;IAC3E,KAAAI,kBAAkB,GAAG,IAAI,CAACP,yBAAyB,CAACM,SAAS;IAC7D,KAAAI,sBAAsB,GAAG,IAAI,CAACV,yBAAyB,CAACQ,SAAS;IACjE,KAAAI,yBAAyB,GAAG,IAAI,CAACZ,yBAAyB,CAAC9E,MAAM;IAEjE;IACS,KAAA2F,uBAAuB,GAAG;MACjCxI,UAAU,EAAGyI,IAA+B,IAC1C,IAAI,CAAClX,cAAc,CAACwO,gBAAgB,CAAC0I,IAAI,CAAC;MAC5C9N,OAAO,EAAGoD,IAAY,IACpB,IAAI,CAAC3M,CAAC,CAAC0E,aAAa,CAACiI,IAAyC,CAAC,EAAEpM,IAAI,IACrE,aAAa;MACf+W,QAAQ,EAAG3K,IAAY,IACrB,IAAI,CAAC3M,CAAC,CAAC0E,aAAa,CAACiI,IAAyC,CAAC,EAAE9F,KAAK,IACtE,eAAe;MACjBN,SAAS,EAAEA,CAACgR,KAAa,EAAEC,YAAiB,KAC1C,IAAI,CAACxX,CAAC,CAACuG,SAAS,CAAC,CAAC,EAAEiR,YAAY;KACnC;IAED,KAAAC,sBAAsB,GAAG,IAAI,CAACL,uBAAuB,CAACxI,UAAU;IAChE,KAAA8I,mBAAmB,GAAG,IAAI,CAACN,uBAAuB,CAAC7N,OAAO;IAC1D,KAAAoO,oBAAoB,GAAG,IAAI,CAACP,uBAAuB,CAACE,QAAQ;IAC5D,KAAAM,qBAAqB,GAAG,IAAI,CAACR,uBAAuB,CAAC7Q,SAAS;IA6K9D;IACS,KAAAsR,YAAY,GAAG;MACtBC,eAAe,EAAEA,CAAA,KAAK;QACpB,MAAMC,MAAM,GAAG,EAAE;QACjB,IAAI,IAAI,CAAC9T,mBAAmB,EAC1B8T,MAAM,CAACC,IAAI,CAAC;UACVxQ,GAAG,EAAE,YAAY;UACjBhH,KAAK,EAAE,cAAc;UACrBD,IAAI,EAAE,cAAc;UACpB0X,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAChU,mBAAmB,GAAG;SAChD,CAAC;QACJ,IAAI,IAAI,CAACxC,oBAAoB,EAC3BsW,MAAM,CAACC,IAAI,CAAC;UACVxQ,GAAG,EAAE,aAAa;UAClBhH,KAAK,EAAE,uBAAuB;UAC9BD,IAAI,EAAE,gBAAgB;UACtB0X,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACxW,oBAAoB,GAAG;SACjD,CAAC;QACJ,IAAI,IAAI,CAACE,kBAAkB,EACzBoW,MAAM,CAACC,IAAI,CAAC;UACVxQ,GAAG,EAAE,WAAW;UAChBhH,KAAK,EAAE,uBAAuB;UAC9BD,IAAI,EAAE,kBAAkB;UACxB0X,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACtW,kBAAkB,GAAG;SAC/C,CAAC;QACJ,IAAI,IAAI,CAACE,sBAAsB,EAC7BkW,MAAM,CAACC,IAAI,CAAC;UACVxQ,GAAG,EAAE,eAAe;UACpBhH,KAAK,EAAE,iBAAiB;UACxBD,IAAI,EAAE,mBAAmB;UACzB0X,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACpW,sBAAsB,GAAG;SACnD,CAAC;QACJ,OAAOkW,MAAM;MACf,CAAC;MACDG,gBAAgB,EAAEA,CAAA,KAChBC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACpY,CAAC,CAACgH,MAAM,CAAC,CAACsF,GAAG,CAAC,CAAC,CAAC9E,GAAG,EAAE6Q,MAAM,CAAC,MAAM;QACpD7Q,GAAG;QACH,GAAG6Q;OACJ,CAAC,CAAC;MACLC,eAAe,EAAEA,CAAA,KAAM,IAAI,CAACtY,CAAC,CAACuH;KAC/B;IAED,KAAAuQ,eAAe,GAAG,IAAI,CAACD,YAAY,CAACC,eAAe;IACnD,KAAAI,gBAAgB,GAAG,IAAI,CAACL,YAAY,CAACK,gBAAgB;IACrD,KAAAI,eAAe,GAAG,IAAI,CAACT,YAAY,CAACS,eAAe;IAEnD;IACS,KAAAC,aAAa,GAAG;MACvBC,OAAO,EAAGxR,MAAc,IACtB,IAAI,CAAChH,CAAC,CAACgH,MAAM,CAACA,MAAoC,CAAC,EAAEE,IAAI,IAAI,SAAS;MACxEoQ,QAAQ,EAAGtQ,MAAc,IACvB,IAAI,CAAChH,CAAC,CAACgH,MAAM,CAACA,MAAoC,CAAC,EAAEH,KAAK,IAC1D,eAAe;MACjB0C,OAAO,EAAGvC,MAAc,IACtB,IAAI,CAAChH,CAAC,CAACgH,MAAM,CAACA,MAAoC,CAAC,EAAEzG,IAAI,IACzD;KACH;IAED,KAAAkY,aAAa,GAAG,IAAI,CAACF,aAAa,CAACC,OAAO;IAC1C,KAAAE,cAAc,GAAG,IAAI,CAACH,aAAa,CAACjB,QAAQ;IAC5C,KAAAqB,aAAa,GAAG,IAAI,CAACJ,aAAa,CAAChP,OAAO;IAE1C;IACS,KAAAqP,cAAc,GAAG;MACxBC,cAAc,EAAGC,UAAuB,IACtCA,UAAU,GACN,IAAI,CAAC3Y,cAAc,CAACwO,gBAAgB,CAACmK,UAAU,CAAC,GAChD,WAAW;MACjBC,mBAAmB,EAAEA,CAAA,KACnBtD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/M,WAAW,CAACqQ,MAAM,EAAE,CAAC,CAACnZ,MAAM,CAAEoZ,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC,CAClEnX,MAAM;MACXoX,gBAAgB,EAAEA,CAAA,KAAM1D,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/M,WAAW,CAACqQ,MAAM,EAAE,CAAC;MAC7DI,eAAe,EAAGvZ,MAAc,IAAM,IAAI,CAACwJ,gBAAgB,GAAGxJ;KAC/D;IAED,KAAAgZ,cAAc,GAAG,IAAI,CAACD,cAAc,CAACC,cAAc;IACnD,KAAAE,mBAAmB,GAAG,IAAI,CAACH,cAAc,CAACG,mBAAmB;IAC7D,KAAAK,eAAe,GAAG,IAAI,CAACR,cAAc,CAACQ,eAAe;IACrD,KAAAD,gBAAgB,GAAG,IAAI,CAACP,cAAc,CAACO,gBAAgB;IAEvD;IACS,KAAAE,mBAAmB,GAAG;MAC7BC,UAAU,EAAG/H,OAAY,IAAM,IAAI,CAACvM,iBAAiB,GAAGuM,OAAQ;MAChEgI,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACvU,iBAAiB,GAAG,IAAK;MAClDwU,gBAAgB,EAAGjI,OAAY,IAAI;QACjC,IAAI,CAAC3L,iBAAiB,GAAG2L,OAAO;QAChC,IAAI,CAAC5L,gBAAgB,GAAG,IAAI;MAC9B,CAAC;MACD8T,iBAAiB,EAAEA,CAAA,KAAK;QACtB,IAAI,CAAC9T,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,qBAAqB,GAAG,EAAE;MACjC;KACD;IAED,KAAA6T,mBAAmB,GAAG,IAAI,CAACL,mBAAmB,CAACC,UAAU;IACzD,KAAAC,WAAW,GAAG,IAAI,CAACF,mBAAmB,CAACE,WAAW;IAClD,KAAAC,gBAAgB,GAAG,IAAI,CAACH,mBAAmB,CAACG,gBAAgB;IAC5D,KAAAC,iBAAiB,GAAG,IAAI,CAACJ,mBAAmB,CAACI,iBAAiB;IAE9D;IACS,KAAAE,cAAc,GAAG;MACxBC,UAAU,EAAGrI,OAAY,IACvB,IAAI,CAACsI,eAAe,CAACtI,OAAO,CAAC,GAAG,kBAAkB,GAAG,kBAAkB;MACzEuI,iBAAiB,EAAGvI,OAAY,IAC9B,IAAI,CAACsI,eAAe,CAACtI,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;MAC5DwI,cAAc,EAAGxI,OAAY,IAAKA,OAAO,CAAC9E,MAAM,EAAE/F,EAAE,KAAK,IAAI,CAAC/D,aAAa;MAC3EkX,eAAe,EAAGtI,OAAY,IAAKA,OAAO,CAACyI,QAAQ,IAAI;KACxD;IAED,KAAAJ,UAAU,GAAG,IAAI,CAACD,cAAc,CAACC,UAAU;IAC3C,KAAAE,iBAAiB,GAAG,IAAI,CAACH,cAAc,CAACG,iBAAiB;IACzD,KAAAC,cAAc,GAAG,IAAI,CAACJ,cAAc,CAACI,cAAc;IACnD,KAAAF,eAAe,GAAG,IAAI,CAACF,cAAc,CAACE,eAAe;IAErD;IACS,KAAAI,WAAW,GAAG;MACrBC,gBAAgB,EAAG3I,OAAY,IAAI;QACjC,IAAI,CAACzM,gBAAgB,GAAGyM,OAAO,CAAC7K,EAAE;QAClC,IAAI,CAAC3B,cAAc,GAAGwM,OAAO,CAAC/E,OAAO;MACvC,CAAC;MACD2N,iBAAiB,EAAEA,CAAA,KAAK;QACtB,IAAI,CAACrV,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACC,cAAc,GAAG,EAAE;MAC1B,CAAC;MACDqV,eAAe,EAAGC,SAAiB,IACjC,IAAI,CAACJ,WAAW,CAACE,iBAAiB,EAAE;MACtCG,cAAc,EAAEA,CAACC,KAAoB,EAAEF,SAAiB,KAAI;QAC1D,IAAIE,KAAK,CAAC/S,GAAG,KAAK,OAAO,IAAI,CAAC+S,KAAK,CAACC,QAAQ,EAAE;UAC5CD,KAAK,CAACE,cAAc,EAAE;UACtB,IAAI,CAACR,WAAW,CAACG,eAAe,CAACC,SAAS,CAAC;SAC5C,MAAM,IAAIE,KAAK,CAAC/S,GAAG,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACyS,WAAW,CAACE,iBAAiB,EAAE;;MAExC;KACD;IAED,KAAAD,gBAAgB,GAAG,IAAI,CAACD,WAAW,CAACC,gBAAgB;IACpD,KAAAC,iBAAiB,GAAG,IAAI,CAACF,WAAW,CAACE,iBAAiB;IACtD,KAAAC,eAAe,GAAG,IAAI,CAACH,WAAW,CAACG,eAAe;IAClD,KAAAE,cAAc,GAAG,IAAI,CAACL,WAAW,CAACK,cAAc;IAUhD;IACS,KAAAI,aAAa,GAAG;MACvBC,kBAAkB,EAAG3T,MAAc,IACjC,IAAI,CAAChH,CAAC,CAAC+H,gBAAgB,CAACf,MAA8C,CAAC,IACvE,eAAe;MACjB4T,eAAe,EAAGjO,IAAY,IAC5BA,IAAI,KAAK,OAAO,GAAG,cAAc,GAAG,cAAc;MACpDkO,kBAAkB,EAAGC,QAAgB,IAAI;QACvC,IAAI,CAACA,QAAQ,EAAE,OAAO,OAAO;QAC7B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAG,EAAE,CAAC;QACzC,MAAMI,OAAO,GAAGJ,QAAQ,GAAG,EAAE;QAC7B,OAAO,GAAGC,OAAO,CAACpU,QAAQ,EAAE,CAACwU,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CACrDvU,QAAQ,EAAE,CACVwU,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACvB,CAAC;MACDC,cAAc,EAAG1O,SAAoC,IACnD,IAAI,CAACvM,cAAc,CAAC0O,iBAAiB,CAACnC,SAAS;KAClD;IAED,KAAAiO,kBAAkB,GAAG,IAAI,CAACD,aAAa,CAACC,kBAAkB;IAC1D,KAAAC,eAAe,GAAG,IAAI,CAACF,aAAa,CAACE,eAAe;IACpD,KAAAC,kBAAkB,GAAG,IAAI,CAACH,aAAa,CAACG,kBAAkB;IAC1D,KAAAO,cAAc,GAAG,IAAI,CAACV,aAAa,CAACU,cAAc;IAElD;IACS,KAAAC,YAAY,GAAG;MACtBC,eAAe,EAAGf,KAAY,IAAI;QAChC,MAAMgB,MAAM,GAAGhB,KAAK,CAACgB,MAAqB;QAC1C,MAAMC,YAAY,GAAG,CACnB;UACEC,SAAS,EAAE,CAAC,sBAAsB,EAAE,YAAY,CAAC;UACjDC,QAAQ,EAAE;SACX,EACD;UACED,SAAS,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC;UAC1CC,QAAQ,EAAE;SACX,CACF;QAEDF,YAAY,CAAC1G,OAAO,CAAEuD,MAAM,IAAI;UAC9B,MAAMsD,cAAc,GAAG,CAACtD,MAAM,CAACoD,SAAS,CAACG,IAAI,CAAEC,QAAQ,IACrDN,MAAM,CAACO,OAAO,CAACD,QAAQ,CAAC,CACzB;UACD,IAAIF,cAAc,EAAE;YACjB,IAAY,CAACtD,MAAM,CAACqD,QAAQ,CAAC,GAAG,KAAK;;QAE1C,CAAC,CAAC;MACJ,CAAC;MACDK,oBAAoB,EAAG1B,SAAiB,IAAI;QAC1C,IAAI,CAAC9U,iBAAiB,CAAC8U,SAAS,CAAC,GAAG,KAAK;MAC3C;KACD;IAED,KAAAiB,eAAe,GAAG,IAAI,CAACD,YAAY,CAACC,eAAe;IACnD,KAAAS,oBAAoB,GAAG,IAAI,CAACV,YAAY,CAACU,oBAAoB;IAE7D;IACS,KAAAC,eAAe,GAAG;MACzBC,kBAAkB,EAAG1K,OAAY,IAAKA,OAAO,CAACtR,SAAS,IAAI,EAAE;MAC7Dic,eAAe,EAAEA,CAAC7B,SAAiB,EAAE8B,KAAa,KAAI;QACpD;MAAA,CACD;MACDC,cAAc,EAAEA,CAAC7K,OAAY,EAAE4K,KAAa,KAAK;KAClD;IAED,KAAAF,kBAAkB,GAAG,IAAI,CAACD,eAAe,CAACC,kBAAkB;IAC5D,KAAAC,eAAe,GAAG,IAAI,CAACF,eAAe,CAACE,eAAe;IACtD,KAAAE,cAAc,GAAG,IAAI,CAACJ,eAAe,CAACI,cAAc;IAEpD;IACS,KAAAC,4BAA4B,GAAG;MACtCtH,cAAc,EAAEA,CAAA,KACd,IAAI,CAAClP,qBAAqB,CAAC9D,MAAM,KAAK,IAAI,CAAC+D,sBAAsB,CAAC/D,MAAM;MAC1Eua,SAAS,EAAEA,CAAA,KACR,IAAI,CAACzW,qBAAqB,GAAG,IAAI,CAACC,sBAAsB,CAACwG,GAAG,CAC1DtM,CAAC,IAAKA,CAAC,CAAC0G,EAAE,CACX;MACJ6V,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC1W,qBAAqB,GAAG,EAAG;MACpDkL,MAAM,EAAGyL,cAAsB,IAAI;QACjC,MAAMjF,KAAK,GAAG,IAAI,CAAC1R,qBAAqB,CAAC4W,OAAO,CAACD,cAAc,CAAC;QAChEjF,KAAK,GAAG,CAAC,CAAC,GACN,IAAI,CAAC1R,qBAAqB,CAAC6W,MAAM,CAACnF,KAAK,EAAE,CAAC,CAAC,GAC3C,IAAI,CAAC1R,qBAAqB,CAACmS,IAAI,CAACwE,cAAc,CAAC;MACrD,CAAC;MACDG,UAAU,EAAGH,cAAsB,IACjC,IAAI,CAAC3W,qBAAqB,CAACsQ,QAAQ,CAACqG,cAAc,CAAC;MACrDI,eAAe,EAAGna,YAAiB,IACjCA,YAAY,CAACoa,KAAK,IAAI,kCAAkC;MAC1DC,cAAc,EAAGra,YAAiB,IAAKA,YAAY,CAAC0J,IAAI,IAAI,cAAc;MAC1E4Q,cAAc,EAAEA,CAAA,KAAK;QACnB,IAAI,CAAChX,YAAY,GAAG,IAAI;QACxBiX,UAAU,CAAC,MAAK;UACd,IAAI,CAACjX,YAAY,GAAG,KAAK;UACzB,IAAI,CAAC0T,iBAAiB,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV;KACD;IAED,KAAAwD,2BAA2B,GACzB,IAAI,CAACZ,4BAA4B,CAACtH,cAAc;IAClD,KAAAmI,sBAAsB,GAAG,IAAI,CAACb,4BAA4B,CAACC,SAAS;IACpE,KAAAa,wBAAwB,GAAG,IAAI,CAACd,4BAA4B,CAACE,WAAW;IACxE,KAAAa,2BAA2B,GAAG,IAAI,CAACf,4BAA4B,CAACtL,MAAM;IACtE,KAAAsM,sBAAsB,GAAG,IAAI,CAAChB,4BAA4B,CAACM,UAAU;IACrE,KAAAW,2BAA2B,GACzB,IAAI,CAACjB,4BAA4B,CAACO,eAAe;IACnD,KAAAW,0BAA0B,GAAG,IAAI,CAAClB,4BAA4B,CAACS,cAAc;IAC7E,KAAAC,cAAc,GAAG,IAAI,CAACV,4BAA4B,CAACU,cAAc;IAEjE;IACS,KAAAS,yBAAyB,GAAG;MACnCC,eAAe,EAAEA,CAAA,KAAO,IAAI,CAAC/U,gBAAgB,GAAG,IAAK;MACrDgV,wBAAwB,EAAEA,CAAA,KAAK,CAAE,CAAC;MAClCC,qBAAqB,EAAG9d,MAAc,IACnC,IAAI,CAACmI,kBAAkB,GAAGnI;KAC9B;IAED,KAAA4d,eAAe,GAAG,IAAI,CAACD,yBAAyB,CAACC,eAAe;IAChE,KAAAC,wBAAwB,GACtB,IAAI,CAACF,yBAAyB,CAACE,wBAAwB;IACzD,KAAAC,qBAAqB,GAAG,IAAI,CAACH,yBAAyB,CAACG,qBAAqB;IAE5E;IACS,KAAAC,aAAa,GAAG;MACvBC,OAAO,EAAGtD,KAAU,IAAI;QACtB,IAAI,CAACtV,WAAW,GAAGsV,KAAK,CAACgB,MAAM,CAACuC,KAAK;QACrC,IAAI,CAAC7Y,WAAW,CAAClD,MAAM,IAAI,CAAC,GACxB,IAAI,CAAC6b,aAAa,CAACG,OAAO,EAAE,GAC5B,IAAI,CAACH,aAAa,CAACnS,KAAK,EAAE;MAChC,CAAC;MACDuS,UAAU,EAAGzD,KAAoB,IAAI;QACnC,IAAIA,KAAK,CAAC/S,GAAG,KAAK,OAAO,EAAE;UACzB,IAAI,CAACoW,aAAa,CAACG,OAAO,EAAE;SAC7B,MAAM,IAAIxD,KAAK,CAAC/S,GAAG,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACoW,aAAa,CAACnS,KAAK,EAAE;;MAE9B,CAAC;MACDsS,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAC7Y,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,UAAU,GAAG,IAAI;QACtB6X,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5X,aAAa,GAAG,IAAI,CAAC5C,QAAQ,CAAC3C,MAAM,CAAEmP,CAAC,IAC1CA,CAAC,CAACxC,OAAO,EAAEqB,WAAW,EAAE,CAACsI,QAAQ,CAAC,IAAI,CAAClR,WAAW,CAAC4I,WAAW,EAAE,CAAC,CAClE;UACD,IAAI,CAAC3I,WAAW,GAAG,KAAK;QAC1B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDuG,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACxG,WAAW,GAAG,EAAE;QACrB,IAAI,CAACG,aAAa,GAAG,EAAE;QACvB,IAAI,CAACF,WAAW,GAAG,KAAK;QACxB,IAAI,CAACC,UAAU,GAAG,KAAK;MACzB;KACD;IAED,KAAA8Y,aAAa,GAAG,IAAI,CAACL,aAAa,CAACC,OAAO;IAC1C,KAAAK,gBAAgB,GAAG,IAAI,CAACN,aAAa,CAACI,UAAU;IAChD,KAAAG,aAAa,GAAG,IAAI,CAACP,aAAa,CAACG,OAAO;IAC1C,KAAAtT,WAAW,GAAG,IAAI,CAACmT,aAAa,CAACnS,KAAK;IAEtC;IACS,KAAA2S,mBAAmB,GAAG;MAC7BC,iBAAiB,EAAGhE,SAAiB,IAAI;QACvC;MAAA,CACD;MACDiE,qBAAqB,EAAGjE,SAAiB,IAAI;QAC3C;MAAA,CACD;MACDtZ,sBAAsB,EAAEA,CAAA,KAAM,IAAI,CAACsE,cAAc,CAACtD;KACnD;IAED,KAAAsc,iBAAiB,GAAG,IAAI,CAACD,mBAAmB,CAACC,iBAAiB;IAC9D,KAAAC,qBAAqB,GAAG,IAAI,CAACF,mBAAmB,CAACE,qBAAqB;IACtE,KAAAvd,sBAAsB,GAAG,IAAI,CAACqd,mBAAmB,CAACrd,sBAAsB;IAExE;IACS,KAAAwd,wBAAwB,GAAG;MAClCC,oBAAoB,EAAEA,CAAChS,OAAe,EAAEiS,KAAa,KAAI;QACvD,IAAI,CAACA,KAAK,EAAE,OAAOjS,OAAO;QAC1B,MAAMkS,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIF,KAAK,GAAG,EAAE,IAAI,CAAC;QAC5C,OAAOjS,OAAO,CAACoB,OAAO,CAAC8Q,KAAK,EAAE,iBAAiB,CAAC;MAClD,CAAC;MACDE,oBAAoB,EAAGvE,SAAiB,IACrC,IAAI,CAAC/U,kBAAkB,CAAC+U,SAAS,CAAC,GACjC,CAAC,IAAI,CAAC/U,kBAAkB,CAAC+U,SAAS,CAAE;MACxCwE,cAAc,EAAEA,CAACxE,SAAiB,EAAE8B,KAAa,KAC9C,IAAI,CAAC7W,kBAAkB,CAAC+U,SAAS,CAAC,GAAG,KAAM;MAC9CyE,oBAAoB,EAAGzE,SAAiB,IACrC,IAAI,CAAC3U,kBAAkB,CAAC2U,SAAS,CAAC,GACjC,CAAC,IAAI,CAAC3U,kBAAkB,CAAC2U,SAAS;KACvC;IAED,KAAAmE,oBAAoB,GAAG,IAAI,CAACD,wBAAwB,CAACC,oBAAoB;IACzE,KAAAI,oBAAoB,GAAG,IAAI,CAACL,wBAAwB,CAACK,oBAAoB;IACzE,KAAAC,cAAc,GAAG,IAAI,CAACN,wBAAwB,CAACM,cAAc;IAC7D,KAAAC,oBAAoB,GAAG,IAAI,CAACP,wBAAwB,CAACO,oBAAoB;IAEzE;IACS,KAAAC,mBAAmB,GAAG;MAC7BC,mBAAmB,EAAG3E,SAAiB,IACpC,IAAI,CAAC7U,cAAc,CAAC6U,SAAS,CAAC,GAAG,IAAK;MACzC4E,qBAAqB,EAAG5E,SAAiB,IACtC,IAAI,CAAC7U,cAAc,CAAC6U,SAAS,CAAC,GAAG,KAAM;MAC1C6E,sBAAsB,EAAG7E,SAAiB,IACvC,IAAI,CAAC9U,iBAAiB,CAAC8U,SAAS,CAAC,GAAG,IAAK;MAC5C8E,mBAAmB,EAAG9E,SAAiB,IACpC,IAAI,CAAC9U,iBAAiB,CAAC8U,SAAS,CAAC,GAAG;KACxC;IAED,KAAA2E,mBAAmB,GAAG,IAAI,CAACD,mBAAmB,CAACC,mBAAmB;IAClE,KAAAC,qBAAqB,GAAG,IAAI,CAACF,mBAAmB,CAACE,qBAAqB;IACtE,KAAAC,sBAAsB,GAAG,IAAI,CAACH,mBAAmB,CAACG,sBAAsB;IACxE,KAAAC,mBAAmB,GAAG,IAAI,CAACJ,mBAAmB,CAACI,mBAAmB;IAElE;IACS,KAAAC,OAAO,GAAG;MACjBC,aAAa,EAAEA,CAAA,KACb,CAAC,IAAI,CAACC,aAAa,EAAE,IAAI,CAACtW,eAAe,EAAE,IAAI,CAACvE,SAAS,CAAC,CAACqQ,OAAO,CAC/DtL,CAAC,IAAKA,CAAC,IAAI+V,YAAY,CAAC/V,CAAC,CAAC,CAC5B;MACHgW,cAAc,EAAEA,CAAA,KACd,IAAI,CAAC7c,aAAa,IAClB,IAAI,CAACxC,cAAc,CAACqf,cAAc,CAAC,IAAI,CAAC7c,aAAa,CAAC,CAAC0O,SAAS,EAAE;MACpEoO,mBAAmB,EAAEA,CAAA,KAAK;QACxB,IAAI,IAAI,CAAC7V,iBAAiB,IAAI,IAAI,CAACnH,YAAY,EAAEiE,EAAE,EAAE;UACnD,IAAI,CAACkD,iBAAiB,GAAG,KAAK;UAC9B2V,YAAY,CAAC,IAAI,CAACG,WAAW,CAAC;UAC9B,IAAI,CAACvf,cAAc,CAACwf,UAAU,CAAC,IAAI,CAACld,YAAY,CAACiE,EAAE,CAAC,CAAC2K,SAAS,CAAC;YAC7DC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;YACdhL,KAAK,EAAGA,KAAK,IAAI,CAAE;WACpB,CAAC;;MAEN;KACD;IA/3DC,IAAI,CAACsZ,WAAW,GAAG,IAAI,CAACzd,EAAE,CAAC0d,KAAK,CAAC;MAC/BrT,OAAO,EAAE,CAAC,EAAE,EAAE,CAACjN,UAAU,CAACugB,SAAS,CAAC,IAAI,CAAC,CAAC;KAC3C,CAAC;EACJ;EACAC,QAAQA,CAAA;IACN,IAAI,CAACpd,aAAa,GAAG,IAAI,CAACT,WAAW,CAAC8d,gBAAgB,EAAE;IAExD,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,IAAIF,UAAU,EAAE;MACd,IAAI,CAACtc,aAAa,GAAGsc,UAAU;;IAGjC,IAAI,CAACG,wBAAwB,EAAE;IAC/B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,qBAAqB,EAAE;IAE5B/S,QAAQ,CAACgT,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAClF,eAAe,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;IAEnE,MAAMC,QAAQ,GAAG,IAAI,CAACze,KAAK,CAAC0e,MAAM,CAC/BC,IAAI,CACH/gB,MAAM,CAAE8gB,MAAM,IAAKA,MAAM,CAAC,IAAI,CAAC,CAAC,EAChC/gB,oBAAoB,EAAE,EACtBD,SAAS,CAAEghB,MAAM,IAAI;MACnB,IAAI,CAACje,OAAO,GAAG,IAAI;MACnB,IAAI,CAACF,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACe,WAAW,GAAG,CAAC;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI;MAE3B,OAAO,IAAI,CAACtD,cAAc,CAAC0gB,eAAe,CACxCF,MAAM,CAAC,IAAI,CAAC,EACZ,IAAI,CAACtd,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB;IACH,CAAC,CAAC,CACH,CACA8N,SAAS,CAAC;MACTC,IAAI,EAAG7O,YAAY,IAAI;QACrB,IAAI,CAACqe,wBAAwB,CAACre,YAAY,CAAC;MAC7C,CAAC;MACD6D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACya,WAAW,CAAC,6BAA6B,EAAEza,KAAK,CAAC;MACxD;KACD,CAAC;IACJ,IAAI,CAAC5C,aAAa,CAAC+Q,GAAG,CAACiM,QAAQ,CAAC;EAClC;EAEA;EACQK,WAAWA,CACjBxP,OAAe,EACfjL,KAAU,EACV0a,YAAA,GAAwB,IAAI;IAE5BhO,OAAO,CAAC1M,KAAK,CAAC,aAAa,EAAEiL,OAAO,EAAEjL,KAAK,CAAC;IAC5C,IAAI0a,YAAY,EAAE;MAChB,IAAI,CAACte,OAAO,GAAG,KAAK;MACpB,IAAI,CAACM,WAAW,GAAG,KAAK;MACxB,IAAI,CAACQ,aAAa,GAAG,KAAK;;IAE5B,IAAI,CAAC8C,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAChE,YAAY,CAAC4O,SAAS,CAACK,OAAO,CAAC;EACtC;EAEA;EACQ0P,aAAaA,CAAC1P,OAAgB,EAAE2P,QAAqB;IAC3D,IAAI3P,OAAO,EAAE;MACX,IAAI,CAACjP,YAAY,CAACsJ,WAAW,CAAC2F,OAAO,CAAC;;IAExC,IAAI2P,QAAQ,EAAE;MACZA,QAAQ,EAAE;;EAEd;EAWQJ,wBAAwBA,CAACre,YAA0B;IACzD,IAAI,CAACA,YAAY,GAAGA,YAAY;IAEhC,IAAI,CAACA,YAAY,EAAED,QAAQ,IAAIC,YAAY,CAACD,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;MACjE,IAAI,CAACc,gBAAgB,GACnBJ,YAAY,EAAE2J,YAAY,EAAE+U,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAAC1a,EAAE,KAAK,IAAI,CAAC/D,aAAa,IAAIye,CAAC,CAACC,GAAG,KAAK,IAAI,CAAC1e,aAAa,CACnE,IAAI,IAAI;MACX,IAAI,CAACH,QAAQ,GAAG,EAAE;KACnB,MAAM;MACL,MAAM8e,oBAAoB,GAAG,CAAC,IAAI7e,YAAY,EAAED,QAAQ,IAAI,EAAE,CAAC,CAAC;MAEhE8e,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACjC,MAAMC,KAAK,GACTF,CAAC,CAAC9U,SAAS,YAAY3D,IAAI,GACvByY,CAAC,CAAC9U,SAAS,CAACiV,OAAO,EAAE,GACrB,IAAI5Y,IAAI,CAACyY,CAAC,CAAC9U,SAAmB,CAAC,CAACiV,OAAO,EAAE;QAC/C,MAAMC,KAAK,GACTH,CAAC,CAAC/U,SAAS,YAAY3D,IAAI,GACvB0Y,CAAC,CAAC/U,SAAS,CAACiV,OAAO,EAAE,GACrB,IAAI5Y,IAAI,CAAC0Y,CAAC,CAAC/U,SAAmB,CAAC,CAACiV,OAAO,EAAE;QAC/C,OAAOD,KAAK,GAAGE,KAAK;MACtB,CAAC,CAAC;MAEF,IAAI,CAACpf,QAAQ,GAAG8e,oBAAoB;;IAGtC,IAAI,CAACze,gBAAgB,GACnBJ,YAAY,EAAE2J,YAAY,EAAE+U,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAAC1a,EAAE,KAAK,IAAI,CAAC/D,aAAa,IAAIye,CAAC,CAACC,GAAG,KAAK,IAAI,CAAC1e,aAAa,CACnE,IAAI,IAAI;IAEX,IAAI,CAACD,OAAO,GAAG,KAAK;IACpBsa,UAAU,CAAC,MAAM,IAAI,CAACxL,cAAc,EAAE,EAAE,GAAG,CAAC;IAE5C,IAAI,CAACqQ,kBAAkB,EAAE;IAEzB,IAAI,IAAI,CAACpf,YAAY,EAAEiE,EAAE,EAAE;MACzB,IAAI,CAACob,8BAA8B,CAAC,IAAI,CAACrf,YAAY,CAACiE,EAAE,CAAC;MACzD,IAAI,CAACqb,sBAAsB,CAAC,IAAI,CAACtf,YAAY,CAACiE,EAAE,CAAC;MACjD,IAAI,CAACsb,2BAA2B,CAAC,IAAI,CAACvf,YAAY,CAACiE,EAAE,CAAC;;EAE1D;EAEQob,8BAA8BA,CAACtF,cAAsB;IAC3D,MAAMjK,GAAG,GAAG,IAAI,CAACpS,cAAc,CAAC2hB,8BAA8B,CAC5DtF,cAAc,CACf,CAACnL,SAAS,CAAC;MACVC,IAAI,EAAG2Q,mBAAmB,IAAI;QAC5B,IAAI,CAACxf,YAAY,GAAGwf,mBAAmB;QACvC,IAAI,CAACzf,QAAQ,GAAGyf,mBAAmB,CAACzf,QAAQ,GACxC,CAAC,GAAGyf,mBAAmB,CAACzf,QAAQ,CAAC,GACjC,EAAE;QACN,IAAI,CAACgP,cAAc,EAAE;MACvB,CAAC;MACDlL,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChE,YAAY,CAAC4O,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;IACF,IAAI,CAACxN,aAAa,CAAC+Q,GAAG,CAAClC,GAAG,CAAC;EAC7B;EAEQwP,sBAAsBA,CAACvF,cAAsB;IACnD,MAAMjK,GAAG,GAAG,IAAI,CAACpS,cAAc,CAAC4hB,sBAAsB,CACpDvF,cAAc,CACf,CAACnL,SAAS,CAAC;MACVC,IAAI,EAAG4Q,UAAU,IAAI;QACnB,IAAIA,UAAU,EAAE1F,cAAc,KAAK,IAAI,CAAC/Z,YAAY,EAAEiE,EAAE,EAAE;UACxD,IAAI,CAAClE,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE0f,UAAU,CAAC,CAACX,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YAC3D,MAAMC,KAAK,GACTF,CAAC,CAAC9U,SAAS,YAAY3D,IAAI,GACvByY,CAAC,CAAC9U,SAAS,CAACiV,OAAO,EAAE,GACrB,IAAI5Y,IAAI,CAACyY,CAAC,CAAC9U,SAAmB,CAAC,CAACiV,OAAO,EAAE;YAC/C,MAAMC,KAAK,GACTH,CAAC,CAAC/U,SAAS,YAAY3D,IAAI,GACvB0Y,CAAC,CAAC/U,SAAS,CAACiV,OAAO,EAAE,GACrB,IAAI5Y,IAAI,CAAC0Y,CAAC,CAAC/U,SAAmB,CAAC,CAACiV,OAAO,EAAE;YAC/C,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEF5E,UAAU,CAAC,MAAM,IAAI,CAACxL,cAAc,EAAE,EAAE,GAAG,CAAC;UAE5C,IACE0Q,UAAU,CAACzV,MAAM,EAAE/F,EAAE,KAAK,IAAI,CAAC/D,aAAa,IAC5Cuf,UAAU,CAACzV,MAAM,EAAE4U,GAAG,KAAK,IAAI,CAAC1e,aAAa,EAC7C;YACA,IAAIuf,UAAU,CAACxb,EAAE,EAAE;cACjB,IAAI,CAACvG,cAAc,CAACgiB,iBAAiB,CAACD,UAAU,CAACxb,EAAE,CAAC,CAAC2K,SAAS,EAAE;;;;MAIxE,CAAC;MACD/K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChE,YAAY,CAAC4O,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;IACF,IAAI,CAACxN,aAAa,CAAC+Q,GAAG,CAAClC,GAAG,CAAC;EAC7B;EAEQyP,2BAA2BA,CAACxF,cAAsB;IACxD,MAAMjK,GAAG,GAAG,IAAI,CAACpS,cAAc,CAACiiB,0BAA0B,CACxD5F,cAAc,CACf,CAACnL,SAAS,CAAC;MACVC,IAAI,EAAGiJ,KAAK,IAAI;QACd,IAAIA,KAAK,CAAC8H,MAAM,KAAK,IAAI,CAAC1f,aAAa,EAAE;UACvC,IAAI,CAACM,QAAQ,GAAGsX,KAAK,CAACtX,QAAQ;UAC9B,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjBsc,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;YAChC,IAAI,CAACA,aAAa,GAAGtC,UAAU,CAAC,MAAK;cACnC,IAAI,CAAC/Z,QAAQ,GAAG,KAAK;YACvB,CAAC,EAAE,IAAI,CAAC;;;MAGd;KACD,CAAC;IACF,IAAI,CAACS,aAAa,CAAC+Q,GAAG,CAAClC,GAAG,CAAC;EAC7B;EAEQsP,kBAAkBA,CAAA;IACxB,MAAMS,cAAc,GAAG,IAAI,CAAC9f,QAAQ,CAAC3C,MAAM,CACxC0M,GAAG,IACF,CAACA,GAAG,CAAC4H,MAAM,KACV5H,GAAG,CAACgW,QAAQ,EAAE7b,EAAE,KAAK,IAAI,CAAC/D,aAAa,IACtC4J,GAAG,CAACgW,QAAQ,EAAElB,GAAG,KAAK,IAAI,CAAC1e,aAAa,CAAC,CAC9C;IAED2f,cAAc,CAACxN,OAAO,CAAEvI,GAAG,IAAI;MAC7B,IAAIA,GAAG,CAAC7F,EAAE,EAAE;QACV,MAAM6L,GAAG,GAAG,IAAI,CAACpS,cAAc,CAACgiB,iBAAiB,CAAC5V,GAAG,CAAC7F,EAAE,CAAC,CAAC2K,SAAS,CAAC;UAClE/K,KAAK,EAAGA,KAAK,IAAI,CAAE;SACpB,CAAC;QACF,IAAI,CAAC5C,aAAa,CAAC+Q,GAAG,CAAClC,GAAG,CAAC;;IAE/B,CAAC,CAAC;EACJ;EAEAiQ,cAAcA,CAACjI,KAAU;IACvB,MAAMkI,IAAI,GAAGlI,KAAK,CAACgB,MAAM,CAACmH,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACD,IAAI,EAAE;IAEX,IAAIA,IAAI,CAAChM,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/B,IAAI,CAACnU,YAAY,CAAC4O,SAAS,CAAC,mCAAmC,CAAC;MAChE;;IAGF,MAAMyR,UAAU,GAAG,CACjB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,oBAAoB,EACpB,yEAAyE,CAC1E;IACD,IAAI,CAACA,UAAU,CAACxM,QAAQ,CAACsM,IAAI,CAAC9V,IAAI,CAAC,EAAE;MACnC,IAAI,CAACrK,YAAY,CAAC4O,SAAS,CACzB,gEAAgE,CACjE;MACD;;IAGF,IAAI,CAACpO,YAAY,GAAG2f,IAAI;IACxB,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,IAAI,CAAC/f,UAAU,GAAG6f,MAAM,CAAC1M,MAAM;IACjC,CAAC;IACD0M,MAAM,CAACG,aAAa,CAACN,IAAI,CAAC;EAC5B;EAEAO,gBAAgBA,CAAA;IACd,IAAI,CAAClgB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAACkgB,SAAS,EAAE1S,aAAa,EAAE;MACjC,IAAI,CAAC0S,SAAS,CAAC1S,aAAa,CAACuN,KAAK,GAAG,EAAE;;EAE3C;EAOA;EACAoF,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACzgB,YAAY,EAAEiE,EAAE,IAAI,CAAC,IAAI,CAAC/D,aAAa,EAAE;MACjD;;IAGF,MAAM6Z,cAAc,GAAG,IAAI,CAAC/Z,YAAY,CAACiE,EAAE;IAC3C6Y,YAAY,CAAC,IAAI,CAACG,WAAW,CAAC;IAE9B,IAAI,CAAC,IAAI,CAAC9V,iBAAiB,EAAE;MAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACzJ,cAAc,CAACgjB,WAAW,CAAC3G,cAAc,CAAC,CAACnL,SAAS,CAAC;QACxDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;QACdhL,KAAK,EAAGA,KAAK,IAAI,CAAE;OACpB,CAAC;;IAGJ,IAAI,CAACoZ,WAAW,GAAG1C,UAAU,CAAC,MAAK;MACjC,IAAI,IAAI,CAACpT,iBAAiB,EAAE;QAC1B,IAAI,CAACA,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACzJ,cAAc,CAACwf,UAAU,CAACnD,cAAc,CAAC,CAACnL,SAAS,CAAC;UACvDC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;UACdhL,KAAK,EAAGA,KAAK,IAAI,CAAE;SACpB,CAAC;;IAEN,CAAC,EAAE,IAAI,CAACwD,cAAc,CAAC;EACzB;EAEA;EACQG,WAAWA,CAACmZ,SAAiB,EAAEC,WAAA,GAAuB,IAAI;IAChE,MAAMtL,MAAM,GAAG;MACbuL,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,cAAc;MACpBpH,KAAK,EAAE,iBAAiB;MACxB3E,YAAY,EAAE,uBAAuB;MACrCgM,MAAM,EAAE,eAAe;MACvBxc,MAAM,EAAE;KACT;IAED,MAAMyc,YAAY,GAAG1L,MAAM,CAACqL,SAAgC,CAAC;IAC7D,IAAIK,YAAY,EAAE;MACf,IAAY,CAACA,YAAY,CAAC,GAAG,CAAE,IAAY,CAACA,YAAY,CAAC;MAE1D,IAAIJ,WAAW,IAAK,IAAY,CAACI,YAAY,CAAC,EAAE;QAC9CtL,MAAM,CAACa,MAAM,CAACjB,MAAM,CAAC,CAACjD,OAAO,CAAE4O,KAAK,IAAI;UACtC,IAAIA,KAAK,KAAKD,YAAY,EAAE;YACzB,IAAY,CAACC,KAAK,CAAC,GAAG,KAAK;;QAEhC,CAAC,CAAC;;;EAGR;EAaAC,WAAWA,CAACL,KAAa;IACvB,IAAI,CAAC3f,aAAa,GAAG2f,KAAK;IAC1B,IAAI,CAAC1f,iBAAiB,GAAG,KAAK;IAC9Bsc,YAAY,CAAC0D,OAAO,CAAC,YAAY,EAAEN,KAAK,CAAC;EAC3C;EAgCA;EACQ/X,sBAAsBA,CAACsY,WAAmB;IAChD,IAAI,CAAChgB,YAAY,GAAG,KAAK;IACzB,IAAI,CAACvB,YAAY,CAACgJ,QAAQ,CAAC,GAAGuY,WAAW,4BAA4B,CAAC;EACxE;EA8EAC,WAAWA,CAAA;IACT,IACG,IAAI,CAAClE,WAAW,CAACmE,OAAO,IAAI,CAAC,IAAI,CAACjhB,YAAY,IAC/C,CAAC,IAAI,CAACH,aAAa,IACnB,CAAC,IAAI,CAACE,gBAAgB,EAAE6D,EAAE,EAC1B;MACA;;IAGF,IAAI,CAAC0Y,OAAO,CAACK,mBAAmB,EAAE;IAElC,MAAMjT,OAAO,GAAG,IAAI,CAACoT,WAAW,CAACoE,GAAG,CAAC,SAAS,CAAC,EAAElG,KAAK;IAEtD,MAAMmG,WAAW,GAAY;MAC3Bvd,EAAE,EAAE,OAAO,GAAG,IAAIqC,IAAI,EAAE,CAAC4Y,OAAO,EAAE;MAClCnV,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtBC,MAAM,EAAE;QACN/F,EAAE,EAAE,IAAI,CAAC/D,aAAa,IAAI,EAAE;QAC5BsJ,QAAQ,EAAE,IAAI,CAACrJ;OAChB;MACD2f,QAAQ,EAAE;QACR7b,EAAE,EAAE,IAAI,CAAC7D,gBAAgB,CAAC6D,EAAE;QAC5BuF,QAAQ,EAAE,IAAI,CAACpJ,gBAAgB,CAACoJ,QAAQ,IAAI;OAC7C;MACDS,SAAS,EAAE,IAAI3D,IAAI,EAAE;MACrBoL,MAAM,EAAE,KAAK;MACb+P,SAAS,EAAE;KACZ;IAED,IAAI,IAAI,CAACphB,YAAY,EAAE;MACrB,IAAIqhB,QAAQ,GAAG,MAAM;MACrB,IAAI,IAAI,CAACrhB,YAAY,CAAC6J,IAAI,CAACyX,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC/CD,QAAQ,GAAG,OAAO;QAElB,IAAI,IAAI,CAACphB,UAAU,EAAE;UACnBkhB,WAAW,CAACI,WAAW,GAAG,CACxB;YACE3d,EAAE,EAAE,iBAAiB;YACrByG,GAAG,EAAE,IAAI,CAACpK,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC4D,QAAQ,EAAE,GAAG,EAAE;YACtDgG,IAAI,EAAElN,WAAW,CAAC6kB,KAAK;YACvBnY,IAAI,EAAE,IAAI,CAACrJ,YAAY,CAACqJ,IAAI;YAC5BsK,IAAI,EAAE,IAAI,CAAC3T,YAAY,CAAC2T;WACzB,CACF;;;MAIL,IAAI0N,QAAQ,KAAK,OAAO,EAAE;QACxBF,WAAW,CAACtX,IAAI,GAAGlN,WAAW,CAAC6kB,KAAK;OACrC,MAAM,IAAIH,QAAQ,KAAK,MAAM,EAAE;QAC9BF,WAAW,CAACtX,IAAI,GAAGlN,WAAW,CAAC8kB,IAAI;;;IAIvC,IAAI,CAAC/hB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAEyhB,WAAW,CAAC;IAE/C,MAAMO,UAAU,GAAG,IAAI,CAAC1hB,YAAY;IACpC,IAAI,CAAC8c,WAAW,CAAC6E,KAAK,EAAE;IACxB,IAAI,CAACzB,gBAAgB,EAAE;IAEvBhG,UAAU,CAAC,MAAM,IAAI,CAACxL,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAE/C,IAAI,CAACxO,WAAW,GAAG,IAAI;IAEvB,MAAM0hB,OAAO,GAAG,IAAI,CAACvkB,cAAc,CAAC2jB,WAAW,CAC7C,IAAI,CAACjhB,gBAAgB,CAAC6D,EAAE,EACxB8F,OAAO,EACPgY,UAAU,IAAIG,SAAS,EACvBllB,WAAW,CAACmlB,IAAI,EAChB,IAAI,CAACniB,YAAY,EAAEiE,EAAE,CACtB,CAAC2K,SAAS,CAAC;MACVC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAI,CAACsT,kBAAkB,CAACZ,WAAW,CAACvd,EAAG,EAAE6K,OAAO,CAAC;QACjD,IAAI,CAACvO,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDsD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACue,kBAAkB,CAACZ,WAAW,CAACvd,EAAG,EAAE,IAAI,EAAE,IAAI,CAAC;QACpD,IAAI,CAAC1D,WAAW,GAAG,KAAK;QACxB,IAAI,CAACV,YAAY,CAAC4O,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;IAEF,IAAI,CAACxN,aAAa,CAAC+Q,GAAG,CAACiQ,OAAO,CAAC;EACjC;EAEA;EACQG,kBAAkBA,CACxBC,MAAc,EACd5C,UAA2B,EAC3B6C,OAAA,GAAmB,KAAK;IAExB,IAAI,CAACviB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC8J,GAAG,CAAEC,GAAG,IAAI;MACxC,IAAIA,GAAG,CAAC7F,EAAE,KAAKoe,MAAM,EAAE;QACrB,IAAI5C,UAAU,EAAE;UACd,OAAOA,UAAU;SAClB,MAAM,IAAI6C,OAAO,EAAE;UAClB,OAAO;YACL,GAAGxY,GAAG;YACN2X,SAAS,EAAE,KAAK;YAChBa,OAAO,EAAE;WACV;;;MAGL,OAAOxY,GAAG;IACZ,CAAC,CAAC;EACJ;EA6CA;EACAyY,QAAQA,CAACzK,KAAU;IACjB,MAAM0K,SAAS,GAAG1K,KAAK,CAACgB,MAAM;IAC9B,MAAM2J,SAAS,GAAGD,SAAS,CAACC,SAAS;IAErC,IACEA,SAAS,GAAG,EAAE,IACd,CAAC,IAAI,CAAC1hB,aAAa,IACnB,IAAI,CAACf,YAAY,EAAEiE,EAAE,IACrB,IAAI,CAACjD,eAAe,EACpB;MACA,IAAI,CAACmN,oBAAoB,EAAE;MAE3B,MAAMuU,eAAe,GAAGF,SAAS,CAACG,YAAY;MAC9C,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,EAAE;MAEzD,IAAI,CAAC9hB,aAAa,GAAG,IAAI;MAEzB,IAAI,CAAC+hB,gBAAgB,EAAE;MAEvB;MACAC,qBAAqB,CAAC,MAAK;QACzB,MAAMC,sBAAsB,GAAGA,CAAA,KAAK;UAClC,IAAIJ,mBAAmB,EAAE;YACvB,MAAMK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAC5CN,mBAAmB,CAAC3e,EAAE,CACvB;YACD,IAAIgf,cAAc,EAAE;cAClB;cACAA,cAAc,CAACE,cAAc,CAAC;gBAAEC,KAAK,EAAE;cAAQ,CAAE,CAAC;aACnD,MAAM;cACL;cACA,MAAMC,eAAe,GAAGb,SAAS,CAACG,YAAY;cAC9C,MAAMW,UAAU,GAAGD,eAAe,GAAGX,eAAe;cACpDF,SAAS,CAACC,SAAS,GAAGA,SAAS,GAAGa,UAAU;;;UAIhD;UACA,IAAI,CAAClV,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACAmM,UAAU,CAACyI,sBAAsB,EAAE,GAAG,CAAC;MACzC,CAAC,CAAC;;EAEN;EAEA;EACQH,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAAChV,iBAAiB,EAAEC,aAAa,IAAI,CAAC,IAAI,CAAC/N,QAAQ,CAACT,MAAM,EACjE,OAAO,IAAI;IAEb,MAAMkjB,SAAS,GAAG,IAAI,CAAC3U,iBAAiB,CAACC,aAAa;IACtD,MAAMyV,eAAe,GAAGf,SAAS,CAACgB,gBAAgB,CAAC,eAAe,CAAC;IAEnE,KAAK,IAAIzf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwf,eAAe,CAACjkB,MAAM,EAAEyE,CAAC,EAAE,EAAE;MAC/C,MAAM0f,OAAO,GAAGF,eAAe,CAACxf,CAAC,CAAC;MAClC,MAAM2f,IAAI,GAAGD,OAAO,CAACE,qBAAqB,EAAE;MAE5C;MACA,IAAID,IAAI,CAACE,GAAG,IAAI,CAAC,IAAIF,IAAI,CAACG,MAAM,IAAIrB,SAAS,CAACsB,YAAY,EAAE;QAC1D,MAAMlM,SAAS,GAAG6L,OAAO,CAACM,YAAY,CAAC,iBAAiB,CAAC;QACzD,OAAO,IAAI,CAAChkB,QAAQ,CAAC2e,IAAI,CAAEnS,CAAC,IAAKA,CAAC,CAACtI,EAAE,KAAK2T,SAAS,CAAC,IAAI,IAAI;;;IAIhE,OAAO,IAAI;EACb;EAEA;EACQsL,kBAAkBA,CACxBtL,SAA6B;IAE7B,IAAI,CAAC,IAAI,CAAC/J,iBAAiB,EAAEC,aAAa,IAAI,CAAC8J,SAAS,EAAE,OAAO,IAAI;IACrE,OAAO,IAAI,CAAC/J,iBAAiB,CAACC,aAAa,CAACkW,aAAa,CACvD,qBAAqBpM,SAAS,IAAI,CACnC;EACH;EAuBA;EACAkL,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC/hB,aAAa,IAAI,CAAC,IAAI,CAACf,YAAY,EAAEiE,EAAE,IAAI,CAAC,IAAI,CAACjD,eAAe,EACvE;IAEF;IACA,IAAI,CAACD,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAACD,WAAW,EAAE;IAElB;IACA,IAAI,CAACpD,cAAc,CAAC0gB,eAAe,CACjC,IAAI,CAACpe,YAAY,CAACiE,EAAE,EACpB,IAAI,CAACrD,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB,CAAC8N,SAAS,CAAC;MACVC,IAAI,EAAG7O,YAAY,IAAI;QACrB,IACEA,YAAY,IACZA,YAAY,CAACD,QAAQ,IACrBC,YAAY,CAACD,QAAQ,CAACT,MAAM,GAAG,CAAC,EAChC;UACA;UACA,MAAM2kB,WAAW,GAAG,CAAC,GAAG,IAAI,CAAClkB,QAAQ,CAAC;UAEtC;UACA,MAAMmkB,WAAW,GAAG,IAAI/hB,GAAG,CAAC8hB,WAAW,CAACpa,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAC7F,EAAE,CAAC,CAAC;UAE7D;UACA,MAAMkgB,WAAW,GAAGnkB,YAAY,CAACD,QAAQ,CACtC3C,MAAM,CAAE0M,GAAG,IAAK,CAACoa,WAAW,CAACpS,GAAG,CAAChI,GAAG,CAAC7F,EAAE,CAAC,CAAC,CACzC6a,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACb,MAAMC,KAAK,GAAG,IAAI3Y,IAAI,CAACyY,CAAC,CAAC9U,SAAmB,CAAC,CAACiV,OAAO,EAAE;YACvD,MAAMC,KAAK,GAAG,IAAI7Y,IAAI,CAAC0Y,CAAC,CAAC/U,SAAmB,CAAC,CAACiV,OAAO,EAAE;YACvD,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEJ,IAAIgF,WAAW,CAAC7kB,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAI,CAACS,QAAQ,GAAG,CAAC,GAAGokB,WAAW,EAAE,GAAGF,WAAW,CAAC;YAEhD;YACA,IAAI,IAAI,CAAClkB,QAAQ,CAACT,MAAM,GAAG,IAAI,CAACuB,kBAAkB,EAAE;cAClD,IAAI,CAACd,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACqkB,KAAK,CAAC,CAAC,EAAE,IAAI,CAACvjB,kBAAkB,CAAC;;YAGjE;YACA,IAAI,CAACG,eAAe,GAClBmjB,WAAW,CAAC7kB,MAAM,IAAI,IAAI,CAACsB,oBAAoB;WAClD,MAAM;YACL;YACA,IAAI,CAACI,eAAe,GAAG,KAAK;;SAE/B,MAAM;UACL,IAAI,CAACA,eAAe,GAAG,KAAK;;QAG9B;QACA;QACAuZ,UAAU,CAAC,MAAK;UACd,IAAI,CAACxZ,aAAa,GAAG,KAAK;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD8C,KAAK,EAAGA,KAAK,IAAI;QACf0M,OAAO,CAAC1M,KAAK,CAAC,aAAa,EAAE,8BAA8B,EAAEA,KAAK,CAAC;QACnE,IAAI,CAAC9C,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACqN,oBAAoB,EAAE;QAC3B,IAAI,CAACvO,YAAY,CAAC4O,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACQ4V,eAAeA,CACrBC,UAAqC,EACrCC,UAAqC;IAErC,IAAI,CAACD,UAAU,IAAI,CAACC,UAAU,EAAE,OAAO,KAAK;IAE5C,IAAI;MACF,MAAMC,KAAK,GACTF,UAAU,YAAYhe,IAAI,GACtBge,UAAU,CAACpF,OAAO,EAAE,GACpB,IAAI5Y,IAAI,CAACge,UAAoB,CAAC,CAACpF,OAAO,EAAE;MAC9C,MAAMuF,KAAK,GACTF,UAAU,YAAYje,IAAI,GACtBie,UAAU,CAACrF,OAAO,EAAE,GACpB,IAAI5Y,IAAI,CAACie,UAAoB,CAAC,CAACrF,OAAO,EAAE;MAC9C,OAAO3G,IAAI,CAACmM,GAAG,CAACF,KAAK,GAAGC,KAAK,CAAC,GAAG,IAAI;KACtC,CAAC,OAAO5gB,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEAkL,cAAcA,CAAC4V,KAAA,GAAiB,KAAK;IACnC,IAAI;MACF,IAAI,CAAC,IAAI,CAAC9W,iBAAiB,EAAEC,aAAa,EAAE;MAE5CiV,qBAAqB,CAAC,MAAK;QACzB,MAAMP,SAAS,GAAG,IAAI,CAAC3U,iBAAiB,CAACC,aAAa;QACtD,MAAM8W,kBAAkB,GACtBpC,SAAS,CAACG,YAAY,GAAGH,SAAS,CAACsB,YAAY,IAC/CtB,SAAS,CAACC,SAAS,GAAG,GAAG;QAE3B,IAAIkC,KAAK,IAAIC,kBAAkB,EAAE;UAC/BpC,SAAS,CAACqC,QAAQ,CAAC;YACjBjB,GAAG,EAAEpB,SAAS,CAACG,YAAY;YAC3BmC,QAAQ,EAAE;WACX,CAAC;;MAEN,CAAC,CAAC;KACH,CAAC,OAAOC,GAAG,EAAE;MACZxU,OAAO,CAAC1M,KAAK,CAAC,aAAa,EAAE,4BAA4B,EAAEkhB,GAAG,CAAC;;EAEnE;EA0CA;;;;EAIAC,mBAAmBA,CAACC,QAAgB;IAClCta,MAAM,CAACua,IAAI,CAACD,QAAQ,EAAE,QAAQ,CAAC;EACjC;EAEA;;;EAGAE,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACpW,cAAc,EAAE;IAErB;IACA;EACF;EAEA;;;EAGAqW,qBAAqBA,CAAA;IACnB,IAAI,CAACxlB,MAAM,CAACylB,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEA;;;;EAIAC,WAAWA,CAAC5L,KAAa;IACvB,MAAM6L,OAAO,GAAG,IAAI,CAACpI,WAAW,CAACoE,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAIgE,OAAO,EAAE;MACX,MAAMC,YAAY,GAAGD,OAAO,CAAClK,KAAK,IAAI,EAAE;MACxCkK,OAAO,CAACE,QAAQ,CAACD,YAAY,GAAG9L,KAAK,CAAC;MACtC6L,OAAO,CAACG,WAAW,EAAE;MACrB;MACAnL,UAAU,CAAC,MAAK;QACd,MAAMoL,YAAY,GAAG5a,QAAQ,CAACiZ,aAAa,CACzC,uBAAuB,CACJ;QACrB,IAAI2B,YAAY,EAAE;UAChBA,YAAY,CAACC,KAAK,EAAE;;MAExB,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQjI,wBAAwBA,CAAA;IAC9B,MAAMkI,eAAe,GACnB,IAAI,CAACnoB,cAAc,CAACooB,2BAA2B,EAAE,CAAClX,SAAS,CAAC;MAC1DC,IAAI,EAAGkG,YAAY,IAAI;QACrB,IAAI,CAAC9S,aAAa,CAAC8jB,OAAO,CAAChR,YAAY,CAAC;QACxC,IAAI,CAACvC,uBAAuB,EAAE;QAE9B,IAAI,CAAC9U,cAAc,CAACsoB,IAAI,CAAC,cAAc,CAAC;QAExC,IACEjR,YAAY,CAAC7K,IAAI,KAAK,aAAa,IACnC6K,YAAY,CAACgF,cAAc,KAAK,IAAI,CAAC/Z,YAAY,EAAEiE,EAAE,EACrD;UACA,IAAI8Q,YAAY,CAAC9Q,EAAE,EAAE;YACnB,IAAI,CAACvG,cAAc,CAACwV,UAAU,CAAC,CAAC6B,YAAY,CAAC9Q,EAAE,CAAC,CAAC,CAAC2K,SAAS,EAAE;;;MAGnE,CAAC;MACD/K,KAAK,EAAGA,KAAK,IAAI,CAAE;KACpB,CAAC;IACJ,IAAI,CAAC5C,aAAa,CAAC+Q,GAAG,CAAC6T,eAAe,CAAC;IAEvC,MAAMI,oBAAoB,GAAG,IAAI,CAACvoB,cAAc,CAACwoB,cAAc,CAACtX,SAAS,CAAC;MACxEC,IAAI,EAAG5M,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACuQ,uBAAuB,EAAE;MAChC,CAAC;MACD3O,KAAK,EAAGA,KAAK,IAAI,CAAE;KACpB,CAAC;IACF,IAAI,CAAC5C,aAAa,CAAC+Q,GAAG,CAACiU,oBAAoB,CAAC;IAE5C,MAAME,oBAAoB,GACxB,IAAI,CAACzoB,cAAc,CAAC0oB,kBAAkB,CAACxX,SAAS,CAAC;MAC/CC,IAAI,EAAGnQ,KAAK,IAAI;QACd,IAAI,CAACI,uBAAuB,GAAGJ,KAAK;MACtC;KACD,CAAC;IACJ,IAAI,CAACuC,aAAa,CAAC+Q,GAAG,CAACmU,oBAAoB,CAAC;IAE5C,MAAME,OAAO,GAAG,IAAI,CAAC3oB,cAAc,CAAC4oB,aAAa,CAAC1X,SAAS,CAAC;MAC1DC,IAAI,EAAGW,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC/N,YAAY,GAAG+N,IAAI;UACxB,IAAI,CAAC7N,aAAa,GAAG,IAAI;UACzB,IAAI,CAACjE,cAAc,CAACsoB,IAAI,CAAC,UAAU,CAAC;SACrC,MAAM;UACL,IAAI,CAACrkB,aAAa,GAAG,KAAK;UAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;;MAE5B;KACD,CAAC;IACF,IAAI,CAACR,aAAa,CAAC+Q,GAAG,CAACqU,OAAO,CAAC;IAE/B,MAAME,aAAa,GAAG,IAAI,CAAC7oB,cAAc,CAACqS,WAAW,CAACnB,SAAS,CAAC;MAC9DC,IAAI,EAAGW,IAAI,IAAI;QACb,IAAI,CAAC9N,UAAU,GAAG8N,IAAI;QACtB,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC5N,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACqP,oBAAoB,EAAE;SAC5B,MAAM;UACL,IAAI,CAACrP,mBAAmB,GAAG,KAAK;UAChC,IAAI,CAACsP,mBAAmB,EAAE;UAC1B,IAAI,CAACC,oBAAoB,EAAE;;MAE/B;KACD,CAAC;IACF,IAAI,CAAClQ,aAAa,CAAC+Q,GAAG,CAACuU,aAAa,CAAC;IAErC;IACA,MAAMC,cAAc,GAAG,IAAI,CAAC9oB,cAAc,CAAC+oB,YAAY,CAAC7X,SAAS,CAAC;MAChEC,IAAI,EAAG6X,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAAChgB,iBAAiB,EAAE;UACpC,IAAI,CAACA,iBAAiB,CAACigB,SAAS,GAAGD,MAAM;;MAE7C;KACD,CAAC;IACF,IAAI,CAACzlB,aAAa,CAAC+Q,GAAG,CAACwU,cAAc,CAAC;IAEtC;IACA,MAAMI,eAAe,GAAG,IAAI,CAAClpB,cAAc,CAACmpB,aAAa,CAACjY,SAAS,CAAC;MAClEC,IAAI,EAAG6X,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAAC/f,kBAAkB,EAAE;UACrC,IAAI,CAACA,kBAAkB,CAACggB,SAAS,GAAGD,MAAM;;MAE9C;KACD,CAAC;IACF,IAAI,CAACzlB,aAAa,CAAC+Q,GAAG,CAAC4U,eAAe,CAAC;EACzC;EAsIA;;;EAGAvV,iBAAiBA,CAACyV,OAAA,GAAmB,KAAK;IACxC,MAAMC,OAAO,GAAG,IAAI,CAACrpB,cAAc,CAACspB,gBAAgB,CAClDF,OAAO,EACP,CAAC,EACD,EAAE,CACH,CAAClY,SAAS,CAAC;MACVC,IAAI,EAAG5M,aAAa,IAAI;QACtB,IAAI6kB,OAAO,EAAE;UACX,IAAI,CAAC7kB,aAAa,GAAGA,aAAa;SACnC,MAAM;UACL,IAAI,CAACA,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAGA,aAAa,CAAC;;QAGhE,IAAI,CAACuQ,uBAAuB,EAAE;MAChC,CAAC;MACD3O,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChE,YAAY,CAAC4O,SAAS,CACzB,6CAA6C,CAC9C;MACH;KACD,CAAC;IAEF,IAAI,CAACxN,aAAa,CAAC+Q,GAAG,CAAC+U,OAAO,CAAC;EACjC;EAsNA;;;EAGQnJ,qBAAqBA,CAAA;IAC3B,MAAMqJ,SAAS,GAAG,IAAI,CAACvpB,cAAc,CAACkgB,qBAAqB,EAAE,CAAChP,SAAS,CAAC;MACtEC,IAAI,EAAG2H,IAAU,IAAI;QACnB,IAAI,CAAC0Q,sBAAsB,CAAC1Q,IAAI,CAAC;MACnC,CAAC;MACD3S,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;IAEF,IAAI,CAAC5C,aAAa,CAAC+Q,GAAG,CAACiV,SAAS,CAAC;EACnC;EAEA;;;EAGQC,sBAAsBA,CAAC1Q,IAAU;IACvC,IAAI,CAACA,IAAI,CAACvS,EAAE,EAAE;IAEd,IAAIuS,IAAI,CAACC,QAAQ,EAAE;MACjB,IAAI,CAACvQ,WAAW,CAACihB,GAAG,CAAC3Q,IAAI,CAACvS,EAAE,EAAEuS,IAAI,CAAC;KACpC,MAAM;MACL,IAAI,CAACtQ,WAAW,CAAC6L,MAAM,CAACyE,IAAI,CAACvS,EAAE,CAAC;;IAGlC,IAAI,IAAI,CAAC7D,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC6D,EAAE,KAAKuS,IAAI,CAACvS,EAAE,EAAE;MACjE,IAAI,CAAC7D,gBAAgB,GAAG;QAAE,GAAG,IAAI,CAACA,gBAAgB;QAAE,GAAGoW;MAAI,CAAE;;EAEjE;EAEA;;;EAGQqH,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAAC3d,aAAa,EAAE;IAEzB,MAAMknB,YAAY,GAAG,IAAI,CAAC1pB,cAAc,CAAC2pB,aAAa,CACpD,IAAI,CAACnnB,aAAa,CACnB,CAAC0O,SAAS,CAAC;MACVC,IAAI,EAAG2H,IAAU,IAAI;QACnB,IAAI,CAACpQ,iBAAiB,GAAG,QAAQ;QACjC,IAAI,CAACC,gBAAgB,GAAG,IAAIC,IAAI,EAAE;MACpC,CAAC;MACDzC,KAAK,EAAGA,KAAK,IAAI;QACf0M,OAAO,CAAC1M,KAAK,CACX,aAAa,EACb,2CAA2C,EAC3CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC5C,aAAa,CAAC+Q,GAAG,CAACoV,YAAY,CAAC;EACtC;EAEA;;;EAGQtJ,qBAAqBA,CAAA;IAC3B,MAAMwJ,MAAM,GAAG,CACb,WAAW,EACX,WAAW,EACX,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,OAAO,CACR;IAEDA,MAAM,CAACjV,OAAO,CAAEyF,KAAK,IAAI;MACvB/M,QAAQ,CAACgT,gBAAgB,CAACjG,KAAK,EAAE,IAAI,CAACyP,cAAc,CAACvJ,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IACxE,CAAC,CAAC;EACJ;EAEA;;;EAGQuJ,cAAcA,CAAA;IACpB,IAAI,CAAClhB,gBAAgB,GAAG,IAAIC,IAAI,EAAE;IAElC;IACA,IAAI,IAAI,CAACC,eAAe,EAAE;MACxBuW,YAAY,CAAC,IAAI,CAACvW,eAAe,CAAC;;IAGpC;IACA,IACE,IAAI,CAACH,iBAAiB,KAAK,MAAM,IACjC,IAAI,CAACA,iBAAiB,KAAK,SAAS,EACpC;MACA,IAAI,CAACohB,gBAAgB,CAAC,QAAQ,CAAC;;IAGjC;IACA,IAAI,CAACjhB,eAAe,GAAGgU,UAAU,CAAC,MAAK;MACrC,IAAI,IAAI,CAACnU,iBAAiB,KAAK,QAAQ,EAAE;QACvC,IAAI,CAACohB,gBAAgB,CAAC,MAAM,CAAC;;IAEjC,CAAC,EAAE,IAAI,CAACjqB,CAAC,CAACiG,MAAM,CAACC,IAAI,CAAC;EACxB;EAEA;;;EAGA+jB,gBAAgBA,CAACjjB,MAAc;IAC7B,IAAI,CAAC,IAAI,CAACrE,aAAa,EAAE;IAEzB,MAAMunB,cAAc,GAAG,IAAI,CAACrhB,iBAAiB;IAE7C,IAAIshB,gBAAgB;IACpB,IAAInjB,MAAM,KAAK,QAAQ,EAAE;MACvBmjB,gBAAgB,GAAG,IAAI,CAAChqB,cAAc,CAAC2pB,aAAa,CAAC,IAAI,CAACnnB,aAAa,CAAC;KACzE,MAAM;MACLwnB,gBAAgB,GAAG,IAAI,CAAChqB,cAAc,CAACqf,cAAc,CAAC,IAAI,CAAC7c,aAAa,CAAC;;IAG3E,MAAMynB,SAAS,GAAGD,gBAAgB,CAAC9Y,SAAS,CAAC;MAC3CC,IAAI,EAAG2H,IAAU,IAAI;QACnB,IAAI,CAACpQ,iBAAiB,GAAG7B,MAAM;QAE/B,IAAIA,MAAM,KAAKkjB,cAAc,EAAE;UAC7B,MAAMG,UAAU,GAAG,IAAI,CAAC5R,aAAa,CAACzR,MAAM,CAAC;UAC7C,IAAI,CAAC1E,YAAY,CAACgJ,QAAQ,CAAC,YAAY+e,UAAU,EAAE,CAAC;;MAExD,CAAC;MACD/jB,KAAK,EAAGA,KAAK,IAAI;QACf0M,OAAO,CAAC1M,KAAK,CACX,aAAa,EACb,yCAAyC,EACzCA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC5C,aAAa,CAAC+Q,GAAG,CAAC2V,SAAS,CAAC;EACnC;EAEA;;;EAGAE,eAAeA,CAAA;IACb,MAAMC,QAAQ,GAAG,IAAI,CAACpqB,cAAc,CAACqqB,WAAW,CAC9C,KAAK,EACL7F,SAAS,EACT,CAAC,EACD,EAAE,EACF,UAAU,EACV,KAAK,EACL,IAAI,CACL,CAACtT,SAAS,CAAC;MACVC,IAAI,EAAGmZ,KAAa,IAAI;QACtBA,KAAK,CAAC3V,OAAO,CAAEmE,IAAI,IAAI;UACrB,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACvS,EAAE,EAAE;YAC5B,IAAI,CAACiC,WAAW,CAACihB,GAAG,CAAC3Q,IAAI,CAACvS,EAAE,EAAEuS,IAAI,CAAC;;QAEvC,CAAC,CAAC;MACJ,CAAC;MACD3S,KAAK,EAAGA,KAAK,IAAI;QACf0M,OAAO,CAAC1M,KAAK,CACX,aAAa,EACb,qDAAqD,EACrDA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC5C,aAAa,CAAC+Q,GAAG,CAAC8V,QAAQ,CAAC;EAClC;EAgJAG,gBAAgBA,CAACnZ,OAAY;IAC3B,IAAI,CAAC9L,SAAS,CAAC8L,OAAO,CAAC7K,EAAE,CAAC,GAAG,IAAI;IACjCsW,UAAU,CAAC,MAAK;MACd,IAAI,CAACvX,SAAS,CAAC8L,OAAO,CAAC7K,EAAE,CAAC,GAAG,KAAK;MAClC,IAAI,CAAClB,cAAc,CAAC+L,OAAO,CAAC7K,EAAE,CAAC,GAAG,KAAK;IACzC,CAAC,EAAE,IAAI,CAAC;EACV;EA6OAikB,WAAWA,CAAA;IACT,IAAI,CAACvL,OAAO,CAACC,aAAa,EAAE;IAC5B,IAAI,CAACD,OAAO,CAACI,cAAc,EAAE;IAC7B,IAAI,CAACJ,OAAO,CAACK,mBAAmB,EAAE;IAClC,IAAI,CAAC/b,aAAa,CAACgP,WAAW,EAAE;IAChClF,QAAQ,CAACod,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACtP,eAAe,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;EACxE;CACD;AA/rEyCoK,UAAA,EAAvCvrB,SAAS,CAAC,mBAAmB,CAAC,C,8DAAwC;AAEvEurB,UAAA,EADCvrB,SAAS,CAAC,WAAW,EAAE;EAAEwrB,MAAM,EAAE;AAAK,CAAE,CAAC,C,sDACD;AAL9BhrB,oBAAoB,GAAA+qB,UAAA,EALhCxrB,SAAS,CAAC;EACTwc,QAAQ,EAAE,kBAAkB;EAC5BkP,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,8BAA8B;CAC3C,CAAC,C,EACWlrB,oBAAoB,CAksEhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}