/* CSS Simple et Futuriste - Fonctionnalités Pures */

/* Container principal */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
  color: #e0e0e0;
}

/* En-tête */
.chat-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: rgba(0, 247, 255, 0.1);
  border-bottom: 1px solid rgba(0, 247, 255, 0.3);
  backdrop-filter: blur(10px);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 1rem;
  border: 2px solid #00f7ff;
}

.user-info h3 {
  margin: 0;
  color: #00f7ff;
  font-size: 1.1rem;
}

.user-status {
  font-size: 0.8rem;
  color: #888;
}

/* Boutons d'action */
.action-buttons {
  margin-left: auto;
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: transparent;
  border: 1px solid #00f7ff;
  color: #00f7ff;
  padding: 0.5rem;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #00f7ff;
  color: #000;
  box-shadow: 0 0 10px #00f7ff;
}

.action-btn {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #ff6b69, #ff4757);
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Zone des messages */
.messages-area {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
}

/* Message */
.message {
  display: flex;
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease;
}

.message.current-user {
  justify-content: flex-end;
}

.message-bubble {
  max-width: 70%;
  padding: 0.8rem 1rem;
  border-radius: 18px;
  position: relative;
  backdrop-filter: blur(5px);
}

.message.current-user .message-bubble {
  background: linear-gradient(135deg, #00f7ff, #0066ff);
  color: #fff;
  border-radius: 18px 18px 4px 18px;
}

.message.other-user .message-bubble {
  background: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
  border: 1px solid rgba(0, 247, 255, 0.3);
  border-radius: 18px 18px 18px 4px;
}

/* Zone de saisie */
.input-area {
  padding: 1rem;
  background: rgba(0, 247, 255, 0.05);
  border-top: 1px solid rgba(0, 247, 255, 0.3);
}

.input-form {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.message-input {
  flex: 1;
  padding: 0.8rem 1rem;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 247, 255, 0.3);
  border-radius: 25px;
  color: #e0e0e0;
  outline: none;
  transition: all 0.3s ease;
}

.message-input:focus {
  border-color: #00f7ff;
  box-shadow: 0 0 10px rgba(0, 247, 255, 0.3);
}

.send-btn {
  background: linear-gradient(135deg, #00f7ff, #0066ff);
  border: none;
  color: #fff;
  padding: 0.8rem;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.send-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);
}

/* Panneaux latéraux */
.side-panel {
  position: fixed;
  top: 0;
  right: -300px;
  width: 300px;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(0, 247, 255, 0.3);
  transition: right 0.3s ease;
  z-index: 1000;
  padding: 1rem;
}

.side-panel.open {
  right: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 247, 255, 0.3);
}

.close-btn {
  background: transparent;
  border: none;
  color: #00f7ff;
  font-size: 1.2rem;
  cursor: pointer;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .chat-header {
    padding: 0.5rem;
  }

  .message-bubble {
    max-width: 85%;
  }

  .side-panel {
    width: 100%;
    right: -100%;
  }
}
