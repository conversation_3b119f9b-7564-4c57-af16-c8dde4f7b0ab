/* Style ultra-minimal et performant */
:host {
  --c1: #4facfe;
  --c2: #00f7ff;
  --bg: #f0f2f5;
  --txt: #333;
}

/* Base commune */
.whatsapp-chat-header,
.whatsapp-user-info,
.whatsapp-user-details,
.whatsapp-actions {
  display: flex;
  align-items: center;
}

/* En-tête simplifié */
.whatsapp-chat-header {
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--c1), var(--c2));
  height: 60px;
  position: relative;
}

/* Utilisateur */
.whatsapp-user-info {
  flex: 1;
  margin-left: 12px;
}

.whatsapp-user-details {
  flex-direction: column;
  align-items: flex-start;
  margin-left: 8px;
}

/* Avatar minimal */
.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}

.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #0f0;
  border: 2px solid var(--bg);
}

/* Texte optimisé */
.whatsapp-username {
  font-size: 15px;
  font-weight: 600;
  color: var(--txt);
}

.whatsapp-status {
  font-size: 12px;
  color: #666;
}

/* Actions simplifiées */
.whatsapp-actions {
  gap: 16px;
}

/* Bouton unifié ultra-simple */
.whatsapp-action-button {
  background: 0;
  border: 0;
  color: var(--c2);
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
}

.whatsapp-action-button.btn-audio-call,
.whatsapp-action-button.btn-video-call {
  border-radius: 50%;
  background: rgba(0, 247, 255, 0.1);
}

.whatsapp-action-button:hover {
  transform: scale(1.1);
  color: #fff;
}

/* Mode sombre minimal */
:host-context(.dark) {
  --txt: #e0e0e0;
  --bg: #1a1a2e;
}

:host-context(.dark) .whatsapp-chat-header {
  background: var(--bg);
  background-image: linear-gradient(135deg, var(--c1), var(--c2));
}

:host-context(.dark) .whatsapp-action-button:hover {
  color: #fff;
}
