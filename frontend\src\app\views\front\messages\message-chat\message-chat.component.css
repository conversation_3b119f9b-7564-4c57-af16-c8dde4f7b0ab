/* Style minimal - Une règle par élément */
.whatsapp-chat-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  height: 60px;
  background: linear-gradient(135deg, #4facfe, #00f7ff);
}
.whatsapp-user-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
}
.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}
.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #0f0;
  border: 2px solid #f0f2f5;
}
.whatsapp-user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 8px;
}
.whatsapp-username {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}
.whatsapp-status {
  font-size: 12px;
  color: #666;
}
.whatsapp-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}
.whatsapp-action-button {
  background: 0;
  border: 0;
  color: #00f7ff;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
}
.whatsapp-action-button.btn-audio-call {
  border-radius: 50%;
  background: rgba(0, 247, 255, 0.1);
}
.whatsapp-action-button.btn-video-call {
  border-radius: 50%;
  background: rgba(0, 247, 255, 0.1);
}
.whatsapp-action-button:hover {
  transform: scale(1.1);
  color: #fff;
}
:host-context(.dark) .whatsapp-chat-header {
  background: #1a1a2e;
  background-image: linear-gradient(135deg, #4facfe, #00f7ff);
}
:host-context(.dark) .whatsapp-username {
  color: #e0e0e0;
}
:host-context(.dark) .whatsapp-online-indicator {
  border-color: #1a1a2e;
}
:host-context(.dark) .whatsapp-action-button:hover {
  color: #fff;
}
