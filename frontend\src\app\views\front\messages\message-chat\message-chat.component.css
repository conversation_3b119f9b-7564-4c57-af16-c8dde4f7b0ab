/* Style unifié et minimal */
:host {
  --primary: #4facfe;
  --accent: #00f7ff;
  --dark: #1a1a2e;
  --light: #f0f2f5;
  --text: #333;
  --transition: all 0.3s ease;
}

/* Classes communes */
.whatsapp-chat-header,
.whatsapp-user-info,
.whatsapp-user-details,
.whatsapp-actions {
  display: flex;
  align-items: center;
  transition: var(--transition);
}

/* En-tête */
.whatsapp-chat-header {
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
  height: 60px;
  position: relative;
}

/* Informations utilisateur */
.whatsapp-user-info {
  flex: 1;
  margin-left: 12px;
}

/* Avatar */
.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}

.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Indicateur en ligne */
.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00ff00;
  border: 2px solid var(--light);
}

/* Détails utilisateur */
.whatsapp-user-details {
  flex-direction: column;
  align-items: flex-start;
  margin-left: 8px;
}

/* Texte */
.whatsapp-username {
  font-size: 0.9375rem;
  font-weight: 600;
  color: var(--text);
}

.whatsapp-status {
  font-size: 0.75rem;
  color: #666;
}

/* Actions */
.whatsapp-actions {
  gap: 16px;
}

/* Boutons */
.whatsapp-action-button {
  background: transparent;
  border: none;
  color: var(--accent);
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  font-size: 1.1rem;
}

.whatsapp-action-button.btn-audio-call,
.whatsapp-action-button.btn-video-call {
  border-radius: 50%;
  background: rgba(0, 247, 255, 0.1);
  border: 1px solid rgba(0, 247, 255, 0.3);
}

.whatsapp-action-button:hover {
  transform: scale(1.1) translateY(-2px);
  color: #ffffff;
  text-shadow: 0 0 10px var(--accent);
}

/* Mode sombre */
:host-context(.dark) {
  --text: #e0e0e0;
  --light: var(--dark);
}

:host-context(.dark) .whatsapp-chat-header {
  background: var(--dark);
  background-image: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--accent) 100%
  );
}

:host-context(.dark) .whatsapp-action-button {
  text-shadow: 0 0 10px var(--accent);
}

:host-context(.dark) .whatsapp-action-button:hover {
  color: white;
  text-shadow: none;
}
