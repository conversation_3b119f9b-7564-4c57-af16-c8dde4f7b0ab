/* CSS Minimal - Objectif <50 lignes */
.whatsapp-chat-header,
.whatsapp-user-info,
.whatsapp-actions,
.whatsapp-user-details,
.whatsapp-action-button {
  display: flex;
  align-items: center;
}
.whatsapp-avatar,
.whatsapp-online-indicator,
.whatsapp-action-button.btn-audio-call,
.whatsapp-action-button.btn-video-call {
  border-radius: 50%;
}
.whatsapp-avatar,
.whatsapp-action-button,
.whatsapp-online-indicator {
  position: relative;
}
.whatsapp-avatar img,
.whatsapp-avatar {
  width: 100%;
  height: 100%;
}
.whatsapp-chat-header {
  padding: 8px 16px;
  height: 60px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}
.whatsapp-user-info {
  flex: 1;
  margin-left: 12px;
}
.whatsapp-avatar {
  width: 36px;
  height: 36px;
  overflow: hidden;
  margin-right: 8px;
}
.whatsapp-avatar img {
  object-fit: cover;
}
.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 10px;
  height: 10px;
  background: #28a745;
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}
.whatsapp-user-details {
  flex-direction: column;
  align-items: flex-start;
  margin-left: 8px;
}
.whatsapp-username {
  font: 600 15px/1 sans-serif;
  color: #333;
}
.whatsapp-status {
  font: 12px/1 sans-serif;
  color: #666;
}
.whatsapp-actions {
  gap: 16px;
}
.whatsapp-action-button {
  background: 0;
  border: 0;
  width: 42px;
  height: 42px;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.whatsapp-action-button.btn-audio-call {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}
.whatsapp-action-button.btn-video-call {
  color: #007bff;
  background: rgba(0, 123, 255, 0.1);
}
.whatsapp-action-button:hover {
  transform: scale(1.1);
}
.whatsapp-action-button.btn-audio-call:hover {
  background: #28a745;
  color: #fff;
}
.whatsapp-action-button.btn-video-call:hover {
  background: #007bff;
  color: #fff;
}
:host-context(.dark) .whatsapp-chat-header {
  background: #2c3e50;
  border-bottom: 1px solid #34495e;
}
:host-context(.dark) .whatsapp-username {
  color: #e0e0e0;
}
:host-context(.dark) .whatsapp-status {
  color: #95a5a6;
}
:host-context(.dark) .whatsapp-online-indicator {
  border-color: #2c3e50;
}
