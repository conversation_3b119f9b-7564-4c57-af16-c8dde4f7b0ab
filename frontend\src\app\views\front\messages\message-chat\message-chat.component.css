/* Style minimal - Une règle par élément */
.whatsapp-chat-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  height: 60px;
  background: linear-gradient(135deg, #4facfe, #00f7ff);
}
.whatsapp-user-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
}
.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}
.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #0f0;
  border: 2px solid #f0f2f5;
}
.whatsapp-user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 8px;
}
.whatsapp-username {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}
.whatsapp-status {
  font-size: 12px;
  color: #666;
}
.whatsapp-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}
.whatsapp-action-button {
  background: 0;
  border: 0;
  color: #00f7ff;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
}
.whatsapp-action-button.btn-audio-call {
  border-radius: 50%;
  background: rgba(0, 247, 255, 0.1);
}
.whatsapp-action-button.btn-video-call {
  border-radius: 50%;
  background: rgba(0, 247, 255, 0.1);
}
.whatsapp-action-button:hover {
  transform: scale(1.1);
  color: #fff;
}
:host-context(.dark) .whatsapp-chat-header {
  background: #1a1a2e;
  background-image: linear-gradient(135deg, #4facfe, #00f7ff);
}
:host-context(.dark) .whatsapp-username {
  color: #e0e0e0;
}
:host-context(.dark) .whatsapp-online-indicator {
  border-color: #1a1a2e;
}
:host-context(.dark) .whatsapp-action-button:hover {
  color: #fff;
}

/* Input de saisie WhatsApp */
.whatsapp-input-container {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #f0f2f5;
  border-top: 1px solid #e0e0e0;
}
.whatsapp-input-field {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 24px;
  background: #fff;
  font-size: 14px;
  outline: none;
}
.whatsapp-input-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}
.whatsapp-input-button {
  background: 0;
  border: 0;
  color: #00f7ff;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  font-size: 16px;
}
.whatsapp-input-button:hover {
  background: rgba(0, 247, 255, 0.1);
}
.whatsapp-send-button {
  background: #00f7ff;
  color: #fff;
}
.whatsapp-send-button:hover {
  background: #00d4e6;
}

/* Messages vocaux WhatsApp */
.whatsapp-voice-message {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #dcf8c6;
  border-radius: 18px;
  max-width: 280px;
  margin: 4px 0;
}
.whatsapp-voice-play-button {
  background: #00f7ff;
  border: 0;
  color: #fff;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 8px;
  font-size: 12px;
}
.whatsapp-voice-waveform {
  flex: 1;
  height: 20px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><rect x="2" y="8" width="2" height="4" fill="%23666"/><rect x="6" y="6" width="2" height="8" fill="%23666"/><rect x="10" y="4" width="2" height="12" fill="%23666"/><rect x="14" y="7" width="2" height="6" fill="%23666"/><rect x="18" y="5" width="2" height="10" fill="%23666"/></svg>')
    repeat-x;
  margin: 0 8px;
}
.whatsapp-voice-duration {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}
.whatsapp-voice-message.sent {
  background: #dcf8c6;
  margin-left: auto;
}
.whatsapp-voice-message.received {
  background: #fff;
  margin-right: auto;
}

/* Enregistrement vocal */
.whatsapp-recording-container {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #ff6b6b;
  border-radius: 24px;
}
.whatsapp-recording-dot {
  width: 8px;
  height: 8px;
  background: #fff;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 1s infinite;
}
.whatsapp-recording-text {
  color: #fff;
  font-size: 14px;
  margin-right: 12px;
}
.whatsapp-recording-time {
  color: #fff;
  font-size: 12px;
  min-width: 40px;
}
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Mode sombre pour input et messages vocaux */
:host-context(.dark) .whatsapp-input-container {
  background: #1a1a2e;
  border-top-color: #333;
}
:host-context(.dark) .whatsapp-input-field {
  background: #2d2d44;
  color: #e0e0e0;
}
:host-context(.dark) .whatsapp-voice-message.received {
  background: #2d2d44;
}
:host-context(.dark) .whatsapp-voice-duration {
  color: #aaa;
}
