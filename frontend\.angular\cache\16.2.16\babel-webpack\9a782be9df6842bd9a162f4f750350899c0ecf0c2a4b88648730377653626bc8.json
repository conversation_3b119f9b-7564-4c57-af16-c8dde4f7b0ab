{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"@app/services/message.service\";\nimport * as i4 from \"src/app/services/toast.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"src/app/services/user-status.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 44);\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\");\n    i0.ɵɵtext(2, \"En train d'\\u00E9crire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46);\n    i0.ɵɵelement(4, \"div\", 47)(5, \"div\", 48)(6, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.isOnline) ? \"En ligne\" : ctx_r2.formatLastActive(ctx_r2.otherParticipant == null ? null : ctx_r2.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"div\", 51);\n    i0.ɵɵelementStart(2, \"span\", 52);\n    i0.ɵɵtext(3, \"Chargement des messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵelement(2, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 56);\n    i0.ɵɵtext(4, \" Aucun message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Commencez votre conversation avec \", ctx_r5.otherParticipant == null ? null : ctx_r5.otherParticipant.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"span\", 73);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.formatDateSeparator(message_r16.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"img\", 75);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const message_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.openUserProfile(message_r16.sender == null ? null : message_r16.sender.id));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r16.sender == null ? null : message_r16.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r16.sender == null ? null : message_r16.sender.username);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r20.getUserColor(message_r16.sender == null ? null : message_r16.sender.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r16.sender == null ? null : message_r16.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"div\", 78);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r21.formatMessageContent(message_r16.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 84);\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r33.formatMessageContent(message_r16.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 79);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_7_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const message_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.openImageViewer(message_r16));\n    });\n    i0.ɵɵelement(2, \"img\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵelement(4, \"i\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, MessageChatComponent_div_27_ng_container_1_div_7_div_5_Template, 1, 1, \"div\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r22.getImageUrl(message_r16), i0.ɵɵsanitizeUrl)(\"alt\", message_r16.content || \"Image\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", message_r16.content);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const message_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.downloadFile(message_r16));\n    });\n    i0.ɵɵelementStart(1, \"div\", 86);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8)(4, \"div\", 87);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 73);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 88);\n    i0.ɵɵelement(9, \"i\", 89);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r23.getFileIcon(message_r16));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getFileName(message_r16), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getFileSize(message_r16), \" \");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 95);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 96);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 97);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 98);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_div_12_i_1_Template, 1, 0, \"i\", 91);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_27_ng_container_1_div_12_i_2_Template, 1, 0, \"i\", 92);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_27_ng_container_1_div_12_i_3_Template, 1, 0, \"i\", 93);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_27_ng_container_1_div_12_i_4_Template, 1, 0, \"i\", 94);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"SENDING\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"SENT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"DELIVERED\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.status === \"READ\");\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const reaction_r49 = restoredCtx.$implicit;\n      const message_r16 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.toggleReaction(message_r16.id, reaction_r49.emoji));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"1\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reaction_r49 = ctx.$implicit;\n    const ctx_r48 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r48.hasUserReacted(reaction_r49, ctx_r48.currentUserId || \"\"))(\"text-green-600\", ctx_r48.hasUserReacted(reaction_r49, ctx_r48.currentUserId || \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r49.emoji);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template, 5, 5, \"button\", 100);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", message_r16.reactions);\n  }\n}\nfunction MessageChatComponent_div_27_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_div_1_Template, 4, 1, \"div\", 61);\n    i0.ɵɵelementStart(2, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_ng_container_1_Template_div_click_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const message_r16 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.onMessageClick(message_r16, $event));\n    })(\"contextmenu\", function MessageChatComponent_div_27_ng_container_1_Template_div_contextmenu_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const message_r16 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.onMessageContextMenu(message_r16, $event));\n    });\n    i0.ɵɵtemplate(3, MessageChatComponent_div_27_ng_container_1_div_3_Template, 2, 2, \"div\", 63);\n    i0.ɵɵelementStart(4, \"div\", 64);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_27_ng_container_1_div_5_Template, 2, 3, \"div\", 65);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_27_ng_container_1_div_6_Template, 2, 1, \"div\", 66);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_27_ng_container_1_div_7_Template, 6, 3, \"div\", 26);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_27_ng_container_1_div_8_Template, 10, 4, \"div\", 67);\n    i0.ɵɵelementStart(9, \"div\", 68)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, MessageChatComponent_div_27_ng_container_1_div_12_Template, 5, 4, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, MessageChatComponent_div_27_ng_container_1_div_13_Template, 2, 1, \"div\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r16 = ctx.$implicit;\n    const i_r17 = ctx.index;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.shouldShowDateSeparator(i_r17));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"justify-end\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId)(\"justify-start\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId);\n    i0.ɵɵproperty(\"id\", \"message-\" + message_r16.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId && ctx_r14.shouldShowAvatar(i_r17));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"bg-green-500\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId)(\"text-white\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId)(\"bg-white\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId)(\"text-gray-900\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId)(\"dark:bg-gray-700\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId)(\"dark:text-white\", (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isGroupConversation() && (message_r16.sender == null ? null : message_r16.sender.id) !== ctx_r14.currentUserId && ctx_r14.shouldShowSenderName(i_r17));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.getMessageType(message_r16) === \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.hasImage(message_r16));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.hasFile(message_r16));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r14.formatMessageTime(message_r16.timestamp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r16.sender == null ? null : message_r16.sender.id) === ctx_r14.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r16.reactions && message_r16.reactions.length > 0);\n  }\n}\nfunction MessageChatComponent_div_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"img\", 103);\n    i0.ɵɵelementStart(2, \"div\", 104)(3, \"div\", 46);\n    i0.ɵɵelement(4, \"div\", 105)(5, \"div\", 106)(6, \"div\", 107);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (ctx_r15.otherParticipant == null ? null : ctx_r15.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r15.otherParticipant == null ? null : ctx_r15.otherParticipant.username);\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_27_ng_container_1_Template, 14, 26, \"ng-container\", 59);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_27_div_2_Template, 7, 2, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.messages)(\"ngForTrackBy\", ctx_r6.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isTyping);\n  }\n}\nfunction MessageChatComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_button_40_Template_button_mousedown_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.startVoiceRecording());\n    })(\"mouseup\", function MessageChatComponent_button_40_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.stopVoiceRecording());\n    })(\"mouseleave\", function MessageChatComponent_button_40_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.cancelVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"bg-red-500\", ctx_r8.isRecordingVoice)(\"hover:bg-red-600\", ctx_r8.isRecordingVoice)(\"animate-pulse\", ctx_r8.isRecordingVoice);\n  }\n}\nfunction MessageChatComponent_button_41_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 113);\n  }\n}\nfunction MessageChatComponent_button_41_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 114);\n  }\n}\nfunction MessageChatComponent_button_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_41_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.sendMessage());\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_button_41_i_1_Template, 1, 0, \"i\", 111);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_41_i_2_Template, 1, 0, \"i\", 112);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r9.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isSendingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isSendingMessage);\n  }\n}\nfunction MessageChatComponent_div_42_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_42_button_3_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const category_r67 = restoredCtx.$implicit;\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.selectEmojiCategory(category_r67));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r67 = ctx.$implicit;\n    const ctx_r65 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-green-100\", ctx_r65.selectedEmojiCategory === category_r67)(\"text-green-600\", ctx_r65.selectedEmojiCategory === category_r67)(\"hover:bg-gray-100\", ctx_r65.selectedEmojiCategory !== category_r67)(\"dark:hover:bg-gray-700\", ctx_r65.selectedEmojiCategory !== category_r67);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r67.icon, \" \");\n  }\n}\nfunction MessageChatComponent_div_42_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_42_button_5_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r72);\n      const emoji_r70 = restoredCtx.$implicit;\n      const ctx_r71 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r71.insertEmoji(emoji_r70));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r70 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r70.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r70.emoji, \" \");\n  }\n}\nfunction MessageChatComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"div\", 116)(2, \"div\", 117);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_42_button_3_Template, 2, 9, \"button\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 119);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_42_button_5_Template, 2, 2, \"button\", 120);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.emojiCategories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.getEmojisForCategory(ctx_r10.selectedEmojiCategory));\n  }\n}\nfunction MessageChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 116)(2, \"div\", 124)(3, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.triggerFileInput(\"image\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 126);\n    i0.ɵɵelement(5, \"i\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 128);\n    i0.ɵɵtext(7, \"Photos\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.triggerFileInput(\"video\"));\n    });\n    i0.ɵɵelementStart(9, \"div\", 129);\n    i0.ɵɵelement(10, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 128);\n    i0.ɵɵtext(12, \"Vid\\u00E9os\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.triggerFileInput(\"document\"));\n    });\n    i0.ɵɵelementStart(14, \"div\", 131);\n    i0.ɵɵelement(15, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 128);\n    i0.ɵɵtext(17, \"Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.openCamera());\n    });\n    i0.ɵɵelementStart(19, \"div\", 133);\n    i0.ɵɵelement(20, \"i\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 128);\n    i0.ɵɵtext(22, \"Cam\\u00E9ra\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 135);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_46_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.closeAllMenus());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class MessageChatComponent {\n  constructor(route, router, authUserService, MessageService, toastService, fb, cdr, userStatusService) {\n    this.route = route;\n    this.router = router;\n    this.authUserService = authUserService;\n    this.MessageService = MessageService;\n    this.toastService = toastService;\n    this.fb = fb;\n    this.cdr = cdr;\n    this.userStatusService = userStatusService;\n    // === PROPRIÉTÉS PRINCIPALES ===\n    this.messages = [];\n    this.conversation = null;\n    this.loading = true;\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    this.isAdmin = false;\n    this.selectedFile = null;\n    this.previewUrl = null;\n    this.isUploading = false;\n    this.isTyping = false;\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    // === PAGINATION ET CHARGEMENT ===\n    this.MAX_MESSAGES_TO_LOAD = 10;\n    this.currentPage = 1;\n    this.isLoadingMore = false;\n    this.hasMoreMessages = true;\n    this.subscriptions = new Subscription();\n    // === INTERFACE ET THÈMES ===\n    this.selectedTheme = 'theme-default';\n    this.showThemeSelector = false;\n    this.showMainMenu = false;\n    this.showEmojiPicker = false;\n    this.showSearchBar = false;\n    this.showPinnedMessages = false;\n    this.showStatusSelector = false;\n    this.showNotificationPanel = false;\n    this.showNotificationSettings = false;\n    this.showUserStatusPanel = false;\n    this.showCallHistoryPanel = false;\n    this.showCallStatsPanel = false;\n    this.showVoiceMessagesPanel = false;\n    // === APPELS ===\n    this.incomingCall = null;\n    this.activeCall = null;\n    this.showCallModal = false;\n    this.showActiveCallModal = false;\n    this.isCallMuted = false;\n    this.isVideoEnabled = true;\n    this.callDuration = 0;\n    this.callTimer = null;\n    // === NOTIFICATIONS ET MESSAGES ===\n    this.notifications = [];\n    this.unreadNotificationCount = 0;\n    this.selectedNotifications = new Set();\n    this.showDeleteConfirmModal = false;\n    this.editingMessageId = null;\n    this.editingContent = '';\n    this.replyingToMessage = null;\n    // === RECHERCHE ===\n    this.searchQuery = '';\n    this.isSearching = false;\n    this.searchMode = false;\n    this.searchResults = [];\n    this.showSearch = false;\n    this.showAdvancedSearch = false;\n    // === PANNEAUX ===\n    this.pinnedMessages = [];\n    this.showReactionPicker = {};\n    this.showDeleteConfirm = {};\n    this.showPinConfirm = {};\n    this.isPinning = {};\n    this.showMessageOptions = {};\n    // === TRANSFERT ===\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n    this.availableConversations = [];\n    this.isForwarding = false;\n    this.isLoadingConversations = false;\n    // === ÉMOJIS ET AUTOCOLLANTS ===\n    this.emojiCategories = [{\n      id: 'recent',\n      name: 'Récents',\n      icon: 'fas fa-clock'\n    }, {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: 'fas fa-smile'\n    }, {\n      id: 'people',\n      name: 'Personnes',\n      icon: 'fas fa-user'\n    }, {\n      id: 'animals',\n      name: 'Animaux',\n      icon: 'fas fa-paw'\n    }, {\n      id: 'food',\n      name: 'Nourriture',\n      icon: 'fas fa-apple-alt'\n    }, {\n      id: 'travel',\n      name: 'Voyage',\n      icon: 'fas fa-plane'\n    }, {\n      id: 'activities',\n      name: 'Activités',\n      icon: 'fas fa-football-ball'\n    }, {\n      id: 'objects',\n      name: 'Objets',\n      icon: 'fas fa-lightbulb'\n    }, {\n      id: 'symbols',\n      name: 'Symboles',\n      icon: 'fas fa-heart'\n    }, {\n      id: 'flags',\n      name: 'Drapeaux',\n      icon: 'fas fa-flag'\n    }];\n    this.selectedEmojiCategory = 'recent';\n    this.emojiSearchQuery = '';\n    this.recentEmojis = [];\n    this.previewedEmoji = null;\n    this.showStickerPicker = false;\n    this.stickerPacks = [];\n    this.selectedStickerPack = '';\n    // === GIFS ===\n    this.showGifPicker = false;\n    this.gifSearchQuery = '';\n    this.gifCategories = ['Trending', 'Reactions', 'Animals', 'Sports', 'TV & Movies'];\n    this.selectedGifCategory = 'Trending';\n    // === OUTIL DE DESSIN ===\n    this.showDrawingTool = false;\n    this.selectedDrawingTool = 'pen';\n    this.drawingColors = ['#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#FFFFFF'];\n    this.selectedDrawingColor = '#000000';\n    this.customDrawingColor = '#000000';\n    this.drawingSize = 5;\n    // === CAMÉRA ===\n    this.showCamera = false;\n    this.cameraMode = 'photo';\n    this.flashEnabled = false;\n    this.showCameraGrid = false;\n    this.showFocusIndicator = false;\n    this.focusX = 0;\n    this.focusY = 0;\n    this.lastCapturedImage = '';\n    this.isRecordingVideo = false;\n    this.videoRecordingDuration = 0;\n    // === ÉDITEUR D'IMAGES ===\n    this.showImageEditor = false;\n    this.imageEditorMode = 'crop';\n    this.cropArea = {\n      x: 0,\n      y: 0,\n      width: 100,\n      height: 100\n    };\n    this.cropRatio = 'free';\n    this.imageFilters = [{\n      name: 'none',\n      label: 'Aucun',\n      css: 'none'\n    }, {\n      name: 'grayscale',\n      label: 'Noir & Blanc',\n      css: 'grayscale(100%)'\n    }, {\n      name: 'sepia',\n      label: 'Sépia',\n      css: 'sepia(100%)'\n    }, {\n      name: 'vintage',\n      label: 'Vintage',\n      css: 'sepia(50%) contrast(1.2)'\n    }, {\n      name: 'bright',\n      label: 'Lumineux',\n      css: 'brightness(1.3)'\n    }, {\n      name: 'contrast',\n      label: 'Contraste',\n      css: 'contrast(1.5)'\n    }];\n    this.selectedImageFilter = 'none';\n    this.imageAdjustments = {\n      brightness: 0,\n      contrast: 0,\n      saturation: 0,\n      hue: 0\n    };\n    this.imageTextElements = [];\n    this.newTextContent = '';\n    this.textFontFamily = 'Arial';\n    this.textFontSize = 24;\n    this.textColor = '#000000';\n    this.availableFonts = [{\n      value: 'Arial',\n      label: 'Arial'\n    }, {\n      value: 'Helvetica',\n      label: 'Helvetica'\n    }, {\n      value: 'Times New Roman',\n      label: 'Times New Roman'\n    }, {\n      value: 'Courier New',\n      label: 'Courier New'\n    }];\n    // === GESTIONNAIRE DE FICHIERS ===\n    this.showFileManager = false;\n    this.fileViewMode = 'grid';\n    this.fileFolders = [];\n    this.selectedFolder = null;\n    this.fileBreadcrumbs = [];\n    this.fileSearchQuery = '';\n    this.fileTypeFilter = '';\n    this.fileSortBy = 'name';\n    this.selectedFiles = [];\n    // === ANALYTICS ===\n    this.showAnalyticsDashboard = false;\n    this.analyticsTimeRange = '7d';\n    this.analyticsData = {\n      totalMessages: 0,\n      activeUsers: 0,\n      avgResponseTime: '0s',\n      filesShared: 0,\n      messagesChange: 0,\n      usersChange: 0,\n      responseTimeChange: 0,\n      filesChange: 0,\n      topUsers: [],\n      messageTypes: [],\n      longestConversation: 0,\n      avgConversationDuration: '0m',\n      topEmojis: []\n    };\n    // === INTÉGRATIONS ===\n    this.showIntegrations = false;\n    this.integrationCategories = ['CRM', 'Productivité', 'Communication', 'Analytics', 'Sécurité'];\n    this.selectedIntegrationCategory = 'CRM';\n    this.webhooks = [];\n    // === ZONE DE SAISIE AVANCÉE ===\n    this.showQuickReplies = false;\n    this.quickReplies = ['Merci !', \"D'accord\", 'Parfait', 'À bientôt'];\n    this.showMentionSuggestions = false;\n    this.mentionSuggestions = [];\n    this.selectedMentionIndex = 0;\n    this.activeMentions = [];\n    this.showHashtagSuggestions = false;\n    this.hashtagSuggestions = [];\n    this.selectedHashtagIndex = 0;\n    this.activeHashtags = [];\n    this.showCommandSuggestions = false;\n    this.commandSuggestions = [];\n    this.selectedCommandIndex = 0;\n    this.activeLinks = [];\n    this.inputHeight = 40;\n    this.showFormattingToolbar = false;\n    this.detectedLanguage = 'fr';\n    this.autoCorrections = [];\n    this.showAutoCorrectSuggestions = false;\n    this.autoCorrectSuggestions = [];\n    // === TRADUCTION ===\n    this.showTranslationPanel = false;\n    this.translationFrom = 'auto';\n    this.translationTo = 'fr';\n    this.originalTextForTranslation = '';\n    this.translatedText = '';\n    this.isTranslating = false;\n    this.supportedLanguages = [{\n      code: 'fr',\n      name: 'Français'\n    }, {\n      code: 'en',\n      name: 'English'\n    }, {\n      code: 'es',\n      name: 'Español'\n    }, {\n      code: 'de',\n      name: 'Deutsch'\n    }, {\n      code: 'it',\n      name: 'Italiano'\n    }];\n    // === SONDAGES ===\n    this.showPollCreator = false;\n    this.pollQuestion = '';\n    this.pollOptions = [{\n      text: ''\n    }, {\n      text: ''\n    }];\n    this.pollSettings = {\n      allowMultiple: false,\n      anonymous: false,\n      showResults: true,\n      showVoters: true\n    };\n    this.pollExpiry = '';\n    this.customPollExpiry = '';\n    // === LOCALISATION ===\n    this.showLocationPicker = false;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n    this.locationSearchResults = [];\n    this.selectedLocation = null;\n    this.showLocationMessage = false;\n    this.locationMessage = '';\n    // === CONTACTS ===\n    this.showContactPicker = false;\n    this.contactSearchQuery = '';\n    this.selectedContactForSharing = null;\n    // === PROGRAMMATION D'ENVOI ===\n    this.showScheduleMessage = false;\n    this.customScheduleDate = '';\n    this.customScheduleTime = '';\n    this.scheduleTimezone = 'Europe/Paris';\n    this.availableTimezones = [{\n      value: 'Europe/Paris',\n      label: 'Paris (CET)'\n    }, {\n      value: 'America/New_York',\n      label: 'New York (EST)'\n    }, {\n      value: 'Asia/Tokyo',\n      label: 'Tokyo (JST)'\n    }];\n    // === ÉTATS DES MESSAGES ===\n    this.highlightedMessageId = '';\n    this.searchResultIds = [];\n    this.selectionMode = false;\n    this.selectedMessages = new Set();\n    this.hoveredMessageId = '';\n    this.playingVoiceId = '';\n    this.showScrollToBottom = false;\n    this.unreadMessagesCount = 0;\n    // === VISUALISEUR D'IMAGES ===\n    this.showImageViewer = false;\n    this.currentImageIndex = 0;\n    this.imageGallery = [];\n    // === TOAST ===\n    this.showToast = false;\n    this.toastMessage = '';\n    this.toastType = 'info';\n    // === PANNEAUX D'INFORMATIONS ===\n    this.showConversationInfo = false;\n    this.showConversationSettings = false;\n    this.conversationSettings = {\n      notifications: true,\n      soundNotifications: true,\n      readReceipts: true,\n      typingIndicators: true\n    };\n    // === VARIABLES D'ÉTAT POUR LES NOUVELLES FONCTIONNALITÉS ===\n    this.isSendingMessage = false;\n    this.isUserTyping = false;\n    this.voiceRecordingState = 'idle';\n    this.voiceRecordingSize = 0;\n    this.recordingWaveform = [];\n    this.voiceRecordingQuality = 'medium';\n    this.voiceEffects = [];\n    this.selectedContacts = [];\n    this.isUpdatingStatus = false;\n    // === VARIABLES POUR LES PIÈCES JOINTES ===\n    this.showAttachmentMenu = false;\n    this.attachmentFiles = [];\n    this.fileCaptions = [];\n    this.fileUploadProgress = [];\n    // === PROPRIÉTÉS POUR LE TEMPLATE ===\n    this.messageContent = '';\n    // === CONSTANTES OPTIMISÉES ===\n    this.c = {\n      reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n      delays: {\n        away: 300000,\n        typing: 3000,\n        scroll: 100,\n        anim: 300\n      },\n      error: 'Erreur. Veuillez réessayer.',\n      trackById: (i, item) => item?.id || i.toString(),\n      notifications: {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      },\n      status: {\n        online: {\n          text: 'En ligne',\n          color: 'text-green-500',\n          icon: 'fas fa-circle'\n        },\n        offline: {\n          text: 'Hors ligne',\n          color: 'text-gray-500',\n          icon: 'far fa-circle'\n        },\n        away: {\n          text: 'Absent',\n          color: 'text-yellow-500',\n          icon: 'fas fa-clock'\n        },\n        busy: {\n          text: 'Occupé',\n          color: 'text-red-500',\n          icon: 'fas fa-minus-circle'\n        }\n      },\n      themes: [{\n        key: 'theme-default',\n        label: 'Défaut',\n        color: '#4f5fad'\n      }, {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '#ff6b9d'\n      }, {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '#3d85c6'\n      }, {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '#6aa84f'\n      }],\n      calls: {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500'\n      }\n    };\n    this._selectedMessages = [];\n    // === ENREGISTREMENT VOCAL COMPLET ===\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.recordingTimer = null;\n    // === MÉTHODES POUR LES MODALS ===\n    this.isCallMinimized = false;\n    this.isInCall = false;\n    this.inputFocused = false;\n    this.forwardSearchQuery = '';\n    this.showCallControls = true;\n    // === SYSTÈME D'ÉMOJIS COMPLET ===\n    this.emojiCategories = [{\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [{\n        emoji: '😀',\n        name: 'grinning face'\n      }, {\n        emoji: '😃',\n        name: 'grinning face with big eyes'\n      }, {\n        emoji: '😄',\n        name: 'grinning face with smiling eyes'\n      }, {\n        emoji: '😁',\n        name: 'beaming face with smiling eyes'\n      }, {\n        emoji: '😆',\n        name: 'grinning squinting face'\n      }, {\n        emoji: '😅',\n        name: 'grinning face with sweat'\n      }, {\n        emoji: '😂',\n        name: 'face with tears of joy'\n      }, {\n        emoji: '🤣',\n        name: 'rolling on the floor laughing'\n      }, {\n        emoji: '😊',\n        name: 'smiling face with smiling eyes'\n      }, {\n        emoji: '😇',\n        name: 'smiling face with halo'\n      }, {\n        emoji: '🙂',\n        name: 'slightly smiling face'\n      }, {\n        emoji: '🙃',\n        name: 'upside-down face'\n      }, {\n        emoji: '😉',\n        name: 'winking face'\n      }, {\n        emoji: '😌',\n        name: 'relieved face'\n      }, {\n        emoji: '😍',\n        name: 'smiling face with heart-eyes'\n      }, {\n        emoji: '🥰',\n        name: 'smiling face with hearts'\n      }, {\n        emoji: '😘',\n        name: 'face blowing a kiss'\n      }, {\n        emoji: '😗',\n        name: 'kissing face'\n      }, {\n        emoji: '😙',\n        name: 'kissing face with smiling eyes'\n      }, {\n        emoji: '😚',\n        name: 'kissing face with closed eyes'\n      }, {\n        emoji: '😋',\n        name: 'face savoring food'\n      }, {\n        emoji: '😛',\n        name: 'face with tongue'\n      }, {\n        emoji: '😝',\n        name: 'squinting face with tongue'\n      }, {\n        emoji: '😜',\n        name: 'winking face with tongue'\n      }]\n    }, {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [{\n        emoji: '👶',\n        name: 'baby'\n      }, {\n        emoji: '🧒',\n        name: 'child'\n      }, {\n        emoji: '👦',\n        name: 'boy'\n      }, {\n        emoji: '👧',\n        name: 'girl'\n      }, {\n        emoji: '🧑',\n        name: 'person'\n      }, {\n        emoji: '👱',\n        name: 'person: blond hair'\n      }, {\n        emoji: '👨',\n        name: 'man'\n      }, {\n        emoji: '🧔',\n        name: 'man: beard'\n      }, {\n        emoji: '👩',\n        name: 'woman'\n      }, {\n        emoji: '🧓',\n        name: 'older person'\n      }, {\n        emoji: '👴',\n        name: 'old man'\n      }, {\n        emoji: '👵',\n        name: 'old woman'\n      }, {\n        emoji: '🙍',\n        name: 'person frowning'\n      }, {\n        emoji: '🙎',\n        name: 'person pouting'\n      }, {\n        emoji: '🙅',\n        name: 'person gesturing NO'\n      }, {\n        emoji: '🙆',\n        name: 'person gesturing OK'\n      }, {\n        emoji: '💁',\n        name: 'person tipping hand'\n      }, {\n        emoji: '🙋',\n        name: 'person raising hand'\n      }, {\n        emoji: '🧏',\n        name: 'deaf person'\n      }, {\n        emoji: '🙇',\n        name: 'person bowing'\n      }, {\n        emoji: '🤦',\n        name: 'person facepalming'\n      }, {\n        emoji: '🤷',\n        name: 'person shrugging'\n      }, {\n        emoji: '👮',\n        name: 'police officer'\n      }, {\n        emoji: '🕵️',\n        name: 'detective'\n      }]\n    }, {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [{\n        emoji: '🐶',\n        name: 'dog face'\n      }, {\n        emoji: '🐱',\n        name: 'cat face'\n      }, {\n        emoji: '🐭',\n        name: 'mouse face'\n      }, {\n        emoji: '🐹',\n        name: 'hamster'\n      }, {\n        emoji: '🐰',\n        name: 'rabbit face'\n      }, {\n        emoji: '🦊',\n        name: 'fox'\n      }, {\n        emoji: '🐻',\n        name: 'bear'\n      }, {\n        emoji: '🐼',\n        name: 'panda'\n      }, {\n        emoji: '🐨',\n        name: 'koala'\n      }, {\n        emoji: '🐯',\n        name: 'tiger face'\n      }, {\n        emoji: '🦁',\n        name: 'lion'\n      }, {\n        emoji: '🐮',\n        name: 'cow face'\n      }, {\n        emoji: '🐷',\n        name: 'pig face'\n      }, {\n        emoji: '🐸',\n        name: 'frog'\n      }, {\n        emoji: '🐵',\n        name: 'monkey face'\n      }, {\n        emoji: '🙈',\n        name: 'see-no-evil monkey'\n      }, {\n        emoji: '🙉',\n        name: 'hear-no-evil monkey'\n      }, {\n        emoji: '🙊',\n        name: 'speak-no-evil monkey'\n      }, {\n        emoji: '🐒',\n        name: 'monkey'\n      }, {\n        emoji: '🐔',\n        name: 'chicken'\n      }, {\n        emoji: '🐧',\n        name: 'penguin'\n      }, {\n        emoji: '🐦',\n        name: 'bird'\n      }, {\n        emoji: '🐤',\n        name: 'baby chick'\n      }, {\n        emoji: '🐣',\n        name: 'hatching chick'\n      }]\n    }, {\n      id: 'food',\n      name: 'Food',\n      icon: '🍎',\n      emojis: [{\n        emoji: '🍎',\n        name: 'red apple'\n      }, {\n        emoji: '🍊',\n        name: 'tangerine'\n      }, {\n        emoji: '🍋',\n        name: 'lemon'\n      }, {\n        emoji: '🍌',\n        name: 'banana'\n      }, {\n        emoji: '🍉',\n        name: 'watermelon'\n      }, {\n        emoji: '🍇',\n        name: 'grapes'\n      }, {\n        emoji: '🍓',\n        name: 'strawberry'\n      }, {\n        emoji: '🫐',\n        name: 'blueberries'\n      }, {\n        emoji: '🍈',\n        name: 'melon'\n      }, {\n        emoji: '🍒',\n        name: 'cherries'\n      }, {\n        emoji: '🍑',\n        name: 'peach'\n      }, {\n        emoji: '🥭',\n        name: 'mango'\n      }, {\n        emoji: '🍍',\n        name: 'pineapple'\n      }, {\n        emoji: '🥥',\n        name: 'coconut'\n      }, {\n        emoji: '🥝',\n        name: 'kiwi fruit'\n      }, {\n        emoji: '🍅',\n        name: 'tomato'\n      }, {\n        emoji: '🍆',\n        name: 'eggplant'\n      }, {\n        emoji: '🥑',\n        name: 'avocado'\n      }, {\n        emoji: '🥦',\n        name: 'broccoli'\n      }, {\n        emoji: '🥬',\n        name: 'leafy greens'\n      }, {\n        emoji: '🥒',\n        name: 'cucumber'\n      }, {\n        emoji: '🌶️',\n        name: 'hot pepper'\n      }, {\n        emoji: '🫑',\n        name: 'bell pepper'\n      }, {\n        emoji: '🌽',\n        name: 'ear of corn'\n      }]\n    }, {\n      id: 'activities',\n      name: 'Activities',\n      icon: '⚽',\n      emojis: [{\n        emoji: '⚽',\n        name: 'soccer ball'\n      }, {\n        emoji: '🏀',\n        name: 'basketball'\n      }, {\n        emoji: '🏈',\n        name: 'american football'\n      }, {\n        emoji: '⚾',\n        name: 'baseball'\n      }, {\n        emoji: '🥎',\n        name: 'softball'\n      }, {\n        emoji: '🎾',\n        name: 'tennis'\n      }, {\n        emoji: '🏐',\n        name: 'volleyball'\n      }, {\n        emoji: '🏉',\n        name: 'rugby football'\n      }, {\n        emoji: '🥏',\n        name: 'flying disc'\n      }, {\n        emoji: '🎱',\n        name: 'pool 8 ball'\n      }, {\n        emoji: '🪀',\n        name: 'yo-yo'\n      }, {\n        emoji: '🏓',\n        name: 'ping pong'\n      }, {\n        emoji: '🏸',\n        name: 'badminton'\n      }, {\n        emoji: '🥅',\n        name: 'goal net'\n      }, {\n        emoji: '⛳',\n        name: 'flag in hole'\n      }, {\n        emoji: '🪁',\n        name: 'kite'\n      }, {\n        emoji: '🏹',\n        name: 'bow and arrow'\n      }, {\n        emoji: '🎣',\n        name: 'fishing pole'\n      }, {\n        emoji: '🤿',\n        name: 'diving mask'\n      }, {\n        emoji: '🥊',\n        name: 'boxing glove'\n      }, {\n        emoji: '🥋',\n        name: 'martial arts uniform'\n      }, {\n        emoji: '🎽',\n        name: 'running shirt'\n      }, {\n        emoji: '🛹',\n        name: 'skateboard'\n      }, {\n        emoji: '🛼',\n        name: 'roller skate'\n      }]\n    }, {\n      id: 'objects',\n      name: 'Objects',\n      icon: '💡',\n      emojis: [{\n        emoji: '⌚',\n        name: 'watch'\n      }, {\n        emoji: '📱',\n        name: 'mobile phone'\n      }, {\n        emoji: '📲',\n        name: 'mobile phone with arrow'\n      }, {\n        emoji: '💻',\n        name: 'laptop'\n      }, {\n        emoji: '⌨️',\n        name: 'keyboard'\n      }, {\n        emoji: '🖥️',\n        name: 'desktop computer'\n      }, {\n        emoji: '🖨️',\n        name: 'printer'\n      }, {\n        emoji: '🖱️',\n        name: 'computer mouse'\n      }, {\n        emoji: '🖲️',\n        name: 'trackball'\n      }, {\n        emoji: '🕹️',\n        name: 'joystick'\n      }, {\n        emoji: '🗜️',\n        name: 'clamp'\n      }, {\n        emoji: '💽',\n        name: 'computer disk'\n      }, {\n        emoji: '💾',\n        name: 'floppy disk'\n      }, {\n        emoji: '💿',\n        name: 'optical disk'\n      }, {\n        emoji: '📀',\n        name: 'dvd'\n      }, {\n        emoji: '🧮',\n        name: 'abacus'\n      }, {\n        emoji: '🎥',\n        name: 'movie camera'\n      }, {\n        emoji: '🎞️',\n        name: 'film frames'\n      }, {\n        emoji: '📽️',\n        name: 'film projector'\n      }, {\n        emoji: '🎬',\n        name: 'clapper board'\n      }, {\n        emoji: '📺',\n        name: 'television'\n      }, {\n        emoji: '📷',\n        name: 'camera'\n      }, {\n        emoji: '📸',\n        name: 'camera with flash'\n      }, {\n        emoji: '📹',\n        name: 'video camera'\n      }]\n    }];\n    this.selectedEmojiCategory = this.emojiCategories[0];\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]]\n    });\n  }\n  // === GETTERS ===\n  get availableReactions() {\n    return this.c.reactions;\n  }\n  get commonEmojis() {\n    return this.MessageService.getCommonEmojis();\n  }\n  get unreadNotificationsCount() {\n    return this.unreadNotificationCount;\n  }\n  get currentUserStatus() {\n    return 'online';\n  }\n  get onlineUsersCount() {\n    return 5;\n  }\n  // === LIFECYCLE HOOKS ===\n  ngOnInit() {\n    this.initializeComponent();\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    if (this.callTimer) clearInterval(this.callTimer);\n    if (this.typingTimeout) clearTimeout(this.typingTimeout);\n  }\n  ngAfterViewChecked() {\n    this.cdr.detectChanges();\n  }\n  // === INITIALISATION ===\n  initializeComponent() {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n    // Initialiser des données de test pour la démo\n    this.initializeTestData();\n  }\n  loadConversation() {\n    this.route.params.subscribe(params => {\n      const conversationId = params['id'];\n      if (conversationId) {\n        this.getConversation(conversationId);\n      }\n    });\n  }\n  setupSubscriptions() {\n    // Configuration des abonnements WebSocket et autres\n  }\n  // === GESTION DES CONVERSATIONS ===\n  getConversation(conversationId) {\n    this.loading = true;\n    this.MessageService.getConversation(conversationId, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        this.conversation = conversation;\n        this.otherParticipant = conversation.participants?.find(p => p.id !== this.currentUserId) || null;\n        // Utiliser les messages de la conversation\n        if (conversation.messages) {\n          this.messages = conversation.messages;\n          this.hasMoreMessages = conversation.messages.length === this.MAX_MESSAGES_TO_LOAD;\n        }\n        this.loading = false;\n        this.scrollToBottom();\n      },\n      error: error => {\n        this.error = error;\n        this.loading = false;\n        console.error('Erreur lors du chargement de la conversation:', error);\n      }\n    });\n  }\n  // === GESTION DES MESSAGES ===\n  sendMessage() {\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content || !this.conversation?.id) return;\n    this.isSendingMessage = true;\n    // Créer un message temporaire pour l'affichage immédiat\n    const tempMessage = {\n      id: Date.now().toString(),\n      content,\n      sender: {\n        id: this.currentUserId,\n        username: this.currentUsername\n      },\n      timestamp: new Date(),\n      conversationId: this.conversation.id,\n      type: 'text'\n    };\n    this.messages.push(tempMessage);\n    this.messageForm.reset();\n    this.scrollToBottom();\n    this.isSendingMessage = false;\n    // Simuler l'envoi réel (à remplacer par l'appel API réel)\n    /*\n    this.MessageService.sendMessage(\n      this.conversation.id,\n      content,\n      'text' as MessageType,\n      this.currentUserId || ''\n    ).subscribe({\n      next: (message) => {\n        // Remplacer le message temporaire par le vrai message\n        const index = this.messages.findIndex(m => m.id === tempMessage.id);\n        if (index > -1) {\n          this.messages[index] = message;\n        }\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        // Supprimer le message temporaire en cas d'erreur\n        this.messages = this.messages.filter(m => m.id !== tempMessage.id);\n      },\n    });\n    */\n  }\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.attachmentFiles = Array.from(files);\n      this.generatePreviews();\n    }\n  }\n  removeAttachment(index) {\n    this.attachmentFiles.splice(index, 1);\n    if (this.attachmentFiles.length === 0) {\n      this.previewUrl = null;\n    }\n  }\n  // === GESTION DES RÉACTIONS ===\n  toggleReaction(messageId, emoji) {\n    // Simulation de la réaction - à implémenter avec le vrai service\n    const message = this.messages.find(m => m.id === messageId);\n    if (message) {\n      if (!message.reactions) message.reactions = [];\n      const existingReaction = message.reactions.find(r => r.emoji === emoji);\n      if (existingReaction) {\n        existingReaction.count = (existingReaction.count || 0) + 1;\n      } else {\n        message.reactions.push({\n          emoji,\n          count: 1,\n          users: [this.currentUserId]\n        });\n      }\n    }\n  }\n  addReaction(messageId, emoji) {\n    this.toggleReaction(messageId, emoji);\n    this.showReactionPicker = {};\n  }\n  // === GESTION DE L'ÉDITION ===\n  startEditMessage(message) {\n    this.editingMessageId = message.id;\n    this.editingContent = message.content || '';\n    this.showMessageOptions = {};\n  }\n  cancelEdit() {\n    this.editingMessageId = null;\n    this.editingContent = '';\n  }\n  saveEdit(messageId) {\n    if (this.editingContent?.trim()) {\n      this.MessageService.editMessage(messageId, this.editingContent.trim()).subscribe({\n        next: () => {\n          this.cancelEdit();\n          // Recharger la conversation pour obtenir les messages mis à jour\n          if (this.conversation?.id) {\n            this.getConversation(this.conversation.id);\n          }\n        },\n        error: error => {\n          console.error('Erreur lors de la modification:', error);\n        }\n      });\n    }\n  }\n  onEditKeyDown(event, messageId) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.saveEdit(messageId);\n    } else if (event.key === 'Escape') {\n      this.cancelEdit();\n    }\n  }\n  // === GESTION DE LA SUPPRESSION ===\n  deleteMessage(messageId) {\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n      this.MessageService.deleteMessage(messageId).subscribe({\n        next: () => {\n          // Recharger la conversation pour obtenir les messages mis à jour\n          if (this.conversation?.id) {\n            this.getConversation(this.conversation.id);\n          }\n          this.showMessageOptions = {};\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression:', error);\n        }\n      });\n    }\n  }\n  // === GESTION DES APPELS ===\n  startCall(type) {\n    if (!this.otherParticipant?.id) return;\n    this.MessageService.initiateCall(this.otherParticipant.id, type).subscribe({\n      next: call => {\n        this.activeCall = call;\n        this.showActiveCallModal = true;\n        this.startCallTimer();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'initiation de l'appel:\", error);\n        this.toastService.showError(\"Impossible d'initier l'appel\");\n      }\n    });\n  }\n  startCallTimer() {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n    }, 1000);\n  }\n  endCall() {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.activeCall = null;\n    this.showActiveCallModal = false;\n    this.callDuration = 0;\n  }\n  toggleMute() {\n    this.isCallMuted = !this.isCallMuted;\n  }\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n  }\n  // === MÉTHODES UTILITAIRES ===\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  trackByMessageId(index, message) {\n    return message?.id || index.toString();\n  }\n  // === GESTION DE L'INTERFACE ===\n  toggleSearch() {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.clearSearch();\n    }\n  }\n  toggleNotifications() {\n    this.showNotificationPanel = !this.showNotificationPanel;\n  }\n  toggleConversationInfo() {\n    this.showConversationInfo = !this.showConversationInfo;\n  }\n  toggleThemeSelector() {\n    this.showThemeSelector = !this.showThemeSelector;\n  }\n  toggleMainMenu() {\n    this.showMainMenu = !this.showMainMenu;\n  }\n  toggleAdvancedSearch() {\n    this.showAdvancedSearch = !this.showAdvancedSearch;\n  }\n  toggleStatusSelector() {\n    this.showStatusSelector = !this.showStatusSelector;\n  }\n  toggleUserStatusPanel() {\n    this.showUserStatusPanel = !this.showUserStatusPanel;\n  }\n  // === GESTION DE LA RECHERCHE ===\n  performSearch() {\n    this.isSearching = true;\n    // Simulation de recherche\n    setTimeout(() => {\n      this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n      this.isSearching = false;\n    }, 500);\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchMode = false;\n    this.searchResults = [];\n    this.searchResultIds = [];\n  }\n  highlightSearchTerm(content) {\n    if (!this.searchQuery || !content) return content;\n    const regex = new RegExp(`(${this.searchQuery})`, 'gi');\n    return content.replace(regex, '<mark class=\"whatsapp-search-highlight\">$1</mark>');\n  }\n  scrollToMessage(messageId) {\n    this.highlightedMessageId = messageId;\n    // Logique pour faire défiler vers le message\n    setTimeout(() => {\n      this.highlightedMessageId = '';\n    }, 3000);\n  }\n  // === GESTION DES MESSAGES ÉPINGLÉS ===\n  showAllPinnedMessages() {\n    // Afficher tous les messages épinglés\n  }\n  closePinnedMessages() {\n    this.pinnedMessages = [];\n  }\n  togglePinMessage(messageId) {\n    const message = this.messages.find(m => m.id === messageId);\n    if (message) {\n      message.pinned = !message.pinned;\n      if (message.pinned) {\n        this.pinnedMessages.push(message);\n      } else {\n        this.pinnedMessages = this.pinnedMessages.filter(m => m.id !== messageId);\n      }\n    }\n    this.showMessageOptions = {};\n  }\n  // === GESTION DES OPTIONS DE MESSAGE ===\n  toggleMessageOptions(messageId) {\n    if (this.showMessageOptions[messageId]) {\n      this.showMessageOptions = {};\n    } else {\n      this.showMessageOptions = {\n        [messageId]: true\n      };\n    }\n  }\n  toggleReactionPicker(messageId) {\n    if (this.showReactionPicker[messageId]) {\n      this.showReactionPicker = {};\n    } else {\n      this.showReactionPicker = {\n        [messageId]: true\n      };\n    }\n  }\n  // === GESTION DES ACTIONS DE MESSAGE ===\n  replyToMessage(message) {\n    this.replyingToMessage = message;\n    this.showMessageOptions = {};\n  }\n  forwardMessage(message) {\n    this.forwardingMessage = message;\n    this.showForwardModal = true;\n    this.showMessageOptions = {};\n  }\n  copyMessage(message) {\n    if (navigator.clipboard && message.content) {\n      navigator.clipboard.writeText(message.content);\n      this.toastService.showSuccess('Message copié');\n    }\n    this.showMessageOptions = {};\n  }\n  selectMessage(messageId) {\n    this.selectionMode = true;\n    this.toggleMessageSelection(messageId);\n    this.showMessageOptions = {};\n  }\n  toggleMessageSelection(messageId) {\n    if (this.selectedMessages.has(messageId)) {\n      this.selectedMessages.delete(messageId);\n    } else {\n      this.selectedMessages.add(messageId);\n    }\n    if (this.selectedMessages.size === 0) {\n      this.selectionMode = false;\n    }\n  }\n  // === GESTION DES ÉMOJIS ===\n  openEmojiPicker(messageId) {\n    this.showEmojiPicker = true;\n    if (messageId) {\n      this.showReactionPicker = {\n        [messageId]: true\n      };\n    }\n  }\n  closeEmojiPicker() {\n    this.showEmojiPicker = false;\n    this.showReactionPicker = {};\n  }\n  selectEmojiCategory(categoryId) {\n    this.selectedEmojiCategory = categoryId;\n  }\n  addEmojiToMessage(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    this.messageForm.get('content')?.setValue(currentContent + emoji);\n  }\n  // === GESTION DES PIÈCES JOINTES ===\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  openFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  // === GESTION DES MÉDIAS ===\n  openImageViewer(message) {\n    this.showImageViewer = true;\n    this.currentImageIndex = 0;\n    this.imageGallery = [message];\n  }\n  closeImageViewer() {\n    this.showImageViewer = false;\n    this.imageGallery = [];\n  }\n  // === GESTION DES MESSAGES VOCAUX ===\n  toggleVoicePlayback(messageId) {\n    if (this.playingVoiceId === messageId) {\n      this.playingVoiceId = '';\n    } else {\n      this.playingVoiceId = messageId;\n    }\n  }\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Demander permission pour le microphone\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: {\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true\n          }\n        });\n        _this.mediaRecorder = new MediaRecorder(stream, {\n          mimeType: 'audio/webm;codecs=opus'\n        });\n        _this.audioChunks = [];\n        _this.isRecordingVoice = true;\n        _this.voiceRecordingDuration = 0;\n        _this.voiceRecordingState = 'recording';\n        // Démarrer le timer\n        _this.recordingTimer = setInterval(() => {\n          _this.voiceRecordingDuration++;\n          _this.cdr.detectChanges();\n        }, 1000);\n        // Gérer les données audio\n        _this.mediaRecorder.ondataavailable = event => {\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        // Gérer la fin d'enregistrement\n        _this.mediaRecorder.onstop = () => {\n          _this.processRecordedAudio();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n        _this.toastService.showSuccess('Enregistrement vocal démarré');\n      } catch (error) {\n        console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n        _this.toastService.showError(\"Impossible d'accéder au microphone\");\n        _this.cancelVoiceRecording();\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n      // Arrêter le stream\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n  cancelVoiceRecording() {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\n      this.mediaRecorder = null;\n    }\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n  processRecordedAudio() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this2.audioChunks.length === 0) {\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Créer le blob audio\n        const audioBlob = new Blob(_this2.audioChunks, {\n          type: 'audio/webm;codecs=opus'\n        });\n        // Vérifier la durée minimale (1 seconde)\n        if (_this2.voiceRecordingDuration < 1) {\n          _this2.toastService.showWarning('Enregistrement trop court (minimum 1 seconde)');\n          _this2.cancelVoiceRecording();\n          return;\n        }\n        // Créer un fichier\n        const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n          type: 'audio/webm;codecs=opus'\n        });\n        // Envoyer le message vocal\n        yield _this2.sendVoiceMessage(audioFile);\n        _this2.toastService.showSuccess('Message vocal envoyé');\n      } catch (error) {\n        console.error(\"Erreur lors du traitement de l'audio:\", error);\n        _this2.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n      } finally {\n        _this2.voiceRecordingState = 'idle';\n        _this2.voiceRecordingDuration = 0;\n        _this2.audioChunks = [];\n      }\n    })();\n  }\n  sendVoiceMessage(audioFile) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.conversation?.id) {\n          throw new Error('Aucune conversation sélectionnée');\n        }\n        // Upload du fichier audio\n        const formData = new FormData();\n        formData.append('file', audioFile);\n        formData.append('type', 'voice');\n        formData.append('duration', _this3.voiceRecordingDuration.toString());\n        // Envoyer via le service de messages\n        _this3.MessageService.sendVoiceMessage(_this3.conversation.id, audioFile, _this3.voiceRecordingDuration.toString()).subscribe({\n          next: message => {\n            // Ajouter le message à la liste\n            _this3.messages.push(message);\n            _this3.scrollToBottom();\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n            throw error;\n          }\n        });\n      } catch (error) {\n        throw error;\n      }\n    })();\n  }\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // === GESTION DES THÈMES ===\n  changeTheme(theme) {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n  }\n  getThemeOptions() {\n    return this.c.themes;\n  }\n  // === GESTION DU STATUT ===\n  updateUserStatus(status) {\n    this.isUpdatingStatus = true;\n    // Simuler une mise à jour\n    setTimeout(() => {\n      this.isUpdatingStatus = false;\n    }, 1000);\n  }\n  getStatusOptions() {\n    return Object.entries(this.c.status).map(([key, value]) => ({\n      key,\n      ...value\n    }));\n  }\n  getStatusIcon(status) {\n    return this.c.status[status]?.icon || 'fas fa-circle';\n  }\n  getStatusColor(status) {\n    return this.c.status[status]?.color || 'text-gray-500';\n  }\n  getStatusText(status) {\n    return this.c.status[status]?.text || 'Inconnu';\n  }\n  // === MÉTHODES POUR LE TEMPLATE ===\n  formatMessageContent(content) {\n    if (!content) return '';\n    return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/`(.*?)`/g, '<code>$1</code>').replace(/\\n/g, '<br>');\n  }\n  onImageLoad(event) {\n    // Gérer le chargement d'image\n  }\n  onImageError(event) {\n    event.target.src = 'assets/images/image-error.png';\n  }\n  onVideoLoaded(event, message) {\n    // Gérer le chargement vidéo\n  }\n  // === MÉTHODES POUR LES ANALYTICS ===\n  openAnalyticsDashboard() {\n    this.showAnalyticsDashboard = true;\n  }\n  closeAnalyticsDashboard() {\n    this.showAnalyticsDashboard = false;\n  }\n  // === MÉTHODES POUR LES INTÉGRATIONS ===\n  openIntegrations() {\n    this.showIntegrations = true;\n  }\n  closeIntegrations() {\n    this.showIntegrations = false;\n  }\n  // === MÉTHODES POUR LES RÉPONSES RAPIDES ===\n  sendQuickMessage(content) {\n    const control = this.messageForm.get('content');\n    if (control) {\n      control.setValue(content);\n      this.sendMessage();\n    }\n  }\n  // === MÉTHODES POUR LA COMPATIBILITÉ ===\n  initiateCall(type) {\n    this.startCall(type);\n  }\n  // === CORRECTION DES MÉTHODES MANQUANTES ===\n  generatePreviews() {\n    this.attachmentFiles.forEach(file => {\n      if (file.type.startsWith('image/')) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.previewUrl = e.target?.result || null;\n        };\n        reader.readAsDataURL(file);\n      }\n    });\n  }\n  loadCurrentUser() {\n    try {\n      // Utiliser getCurrentUser de manière synchrone\n      const user = this.authUserService.getCurrentUser();\n      if (user) {\n        this.currentUserId = user.id || null;\n        this.currentUsername = user.username || 'You';\n      } else {\n        // Fallback si pas d'utilisateur connecté\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement de l'utilisateur:\", error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n  // === MÉTHODES SUPPLÉMENTAIRES POUR LE TEMPLATE ===\n  // Méthodes pour les types de messages\n  getMessageType(message) {\n    if (message.content && !message.attachments?.length) return 'text';\n    if (message.attachments?.some(att => att.type === 'image')) return 'image';\n    if (message.attachments?.some(att => att.type === 'voice')) return 'voice';\n    if (message.attachments?.some(att => att.type === 'file')) return 'file';\n    if (message.attachments?.some(att => att.type === 'video')) return 'video';\n    if (message.type === 'location') return 'location';\n    if (message.type === 'contact') return 'contact';\n    return 'text';\n  }\n  // Méthodes pour les médias\n  getImageUrl(message) {\n    return message.imageUrl || message.attachments?.find(att => att.type === 'image')?.url || '';\n  }\n  getVideoUrl(message) {\n    return message.videoUrl || message.attachments?.find(att => att.type === 'video')?.url || '';\n  }\n  getVideoThumbnail(message) {\n    return message.thumbnailUrl || '';\n  }\n  getVideoDuration(message) {\n    return message.duration || '00:00';\n  }\n  // Méthodes pour les fichiers\n  getFileSize(message) {\n    return this.formatFileSize(message.size || 0);\n  }\n  getFileIcon(message) {\n    const fileType = message.fileType || message.attachments?.[0]?.type || '';\n    const icons = {\n      'application/pdf': 'fas fa-file-pdf',\n      'application/msword': 'fas fa-file-word',\n      'application/vnd.ms-excel': 'fas fa-file-excel',\n      'application/vnd.ms-powerpoint': 'fas fa-file-powerpoint',\n      'text/': 'fas fa-file-alt',\n      'image/': 'fas fa-file-image',\n      'video/': 'fas fa-file-video',\n      'audio/': 'fas fa-file-audio'\n    };\n    for (const [type, icon] of Object.entries(icons)) {\n      if (fileType.startsWith(type)) return icon;\n    }\n    return 'fas fa-file';\n  }\n  getFileType(message) {\n    const fileType = message.fileType || message.attachments?.[0]?.type || '';\n    return fileType.split('/')[1]?.toUpperCase() || 'FICHIER';\n  }\n  // Méthodes pour les messages vocaux\n  formatVoiceDuration(current, total) {\n    const formatTime = seconds => {\n      const mins = Math.floor(seconds / 60);\n      const secs = seconds % 60;\n      return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return `${formatTime(current)} / ${formatTime(total)}`;\n  }\n  getVoiceWaveform(message) {\n    return Array.from({\n      length: 20\n    }, (_, i) => ({\n      height: Math.random() * 20 + 5,\n      active: false,\n      played: false\n    }));\n  }\n  getVoiceProgress(messageId) {\n    return 0;\n  }\n  getVoiceCurrentTime(messageId) {\n    return 0;\n  }\n  getVoiceTotalDuration(messageId) {\n    return 0;\n  }\n  getVoiceSpeed(messageId) {\n    return '1x';\n  }\n  // Méthodes pour les états\n  isImageLoading(message) {\n    return false;\n  }\n  isVoiceLoading(messageId) {\n    return false;\n  }\n  isFileDownloading(message) {\n    return false;\n  }\n  getImageLoadingProgress(message) {\n    return 0;\n  }\n  getFileDownloadProgress(message) {\n    return 0;\n  }\n  getImageDimensions(message) {\n    return '';\n  }\n  // Méthodes pour les actions\n  downloadFile(message) {\n    // Télécharger le fichier\n  }\n  previewFile(message) {\n    // Prévisualiser le fichier\n  }\n  canPreviewFile(message) {\n    const previewableTypes = ['image/', 'video/', 'audio/', 'text/', 'application/pdf'];\n    return previewableTypes.some(type => message.type?.startsWith(type));\n  }\n  hasVideo(message) {\n    return message.type === 'video' || message.attachments && message.attachments.some(att => att.type === 'video');\n  }\n  getEmojiName(emoji) {\n    const emojiNames = {\n      '👍': 'Pouce levé',\n      '❤️': 'Cœur',\n      '😂': 'Rire',\n      '😮': 'Surprise',\n      '😢': 'Triste',\n      '😡': 'Colère'\n    };\n    return emojiNames[emoji] || emoji;\n  }\n  // Méthodes pour la localisation\n  openLocationViewer(message) {\n    // Ouvrir le visualiseur de localisation\n  }\n  getLocationMapUrl(message) {\n    return message.mapUrl || '';\n  }\n  getLocationName(message) {\n    return message.locationName || 'Localisation';\n  }\n  getLocationAddress(message) {\n    return message.address || '';\n  }\n  // Méthodes pour les contacts\n  openContactViewer(message) {\n    // Ouvrir le visualiseur de contact\n  }\n  getContactAvatar(message) {\n    return message.contactAvatar || 'assets/images/default-avatar.png';\n  }\n  getContactName(message) {\n    return message.contactName || 'Contact';\n  }\n  getContactPhone(message) {\n    return message.contactPhone || '';\n  }\n  // Méthodes pour les lecteurs\n  openVideoPlayer(message) {\n    // Ouvrir le lecteur vidéo\n  }\n  seekVoiceMessage(messageId, event) {\n    // Implémentation de la recherche dans le message vocal\n  }\n  changeVoiceSpeed(messageId) {\n    // Changer la vitesse de lecture\n  }\n  // Méthodes pour les événements\n  onScroll(event) {\n    // Gérer le défilement\n    const element = event.target;\n    this.showScrollToBottom = element.scrollTop < element.scrollHeight - element.clientHeight - 100;\n  }\n  loadMoreMessages() {\n    if (!this.conversation?.id || !this.hasMoreMessages) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // Charger plus de messages en utilisant getConversation avec pagination\n    this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        if (conversation.messages && conversation.messages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste (messages plus anciens)\n          this.messages = [...conversation.messages, ...this.messages];\n          this.hasMoreMessages = conversation.messages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement de plus de messages:', error);\n        this.isLoadingMore = false;\n      }\n    });\n  }\n  clearConversation() {\n    if (confirm('Êtes-vous sûr de vouloir vider cette conversation ?')) {\n      this.messages = [];\n    }\n  }\n  exportConversation() {\n    // Exporter la conversation\n  }\n  // Méthodes pour les en-têtes\n  getHeaderActions() {\n    return [{\n      icon: 'fas fa-search',\n      title: 'Rechercher',\n      onClick: () => this.toggleSearch(),\n      class: 'search-btn',\n      isActive: this.showSearch\n    }, {\n      icon: 'fas fa-bell',\n      title: 'Notifications',\n      onClick: () => this.toggleNotifications(),\n      class: 'notification-btn',\n      badge: this.unreadNotificationsCount > 0 ? {\n        count: this.unreadNotificationsCount,\n        class: 'bg-red-500',\n        animate: true\n      } : null\n    }, {\n      icon: 'fas fa-phone',\n      title: 'Appel audio',\n      onClick: () => this.startCall('AUDIO'),\n      class: 'call-btn'\n    }, {\n      icon: 'fas fa-video',\n      title: 'Appel vidéo',\n      onClick: () => this.startCall('VIDEO'),\n      class: 'video-btn'\n    }];\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n  goBackToConversations() {\n    this.router.navigate(['/front/messages']);\n  }\n  openUserProfile(userId) {\n    // Ouvrir le profil utilisateur\n  }\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Hors ligne';\n    const date = new Date(lastActive);\n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const minutes = Math.floor(diff / 60000);\n    if (minutes < 1) return 'En ligne';\n    if (minutes < 60) return `Il y a ${minutes} min`;\n    if (minutes < 1440) return `Il y a ${Math.floor(minutes / 60)} h`;\n    return `Il y a ${Math.floor(minutes / 1440)} j`;\n  }\n  toggleConversationSettings() {\n    this.showConversationSettings = !this.showConversationSettings;\n  }\n  shouldShowDateSeparator(index) {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n    return currentDate !== previousDate;\n  }\n  formatDateSeparator(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) return \"Aujourd'hui\";\n    if (date.toDateString() === yesterday.toDateString()) return 'Hier';\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  getSystemMessageIcon(message) {\n    const icons = {\n      user_joined: 'fas fa-user-plus',\n      user_left: 'fas fa-user-minus',\n      call_started: 'fas fa-phone',\n      call_ended: 'fas fa-phone-slash',\n      message_deleted: 'fas fa-trash'\n    };\n    return icons[message.systemType] || 'fas fa-info-circle';\n  }\n  onMessageClick(message, event) {\n    if (this.selectionMode) {\n      this.toggleMessageSelection(message.id);\n    }\n  }\n  onMessageContextMenu(message, event) {\n    event.preventDefault();\n    this.toggleMessageOptions(message.id);\n  }\n  onMessageHover(messageId, isHovering) {\n    this.hoveredMessageId = isHovering ? messageId : '';\n  }\n  shouldShowAvatar(index) {\n    if (index === this.messages.length - 1) return true;\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    return currentMessage?.sender?.id !== nextMessage?.sender?.id;\n  }\n  onAvatarError(event) {\n    event.target.src = 'assets/images/default-avatar.png';\n  }\n  isGroupConversation() {\n    return this.conversation?.type === 'group';\n  }\n  shouldShowSenderName(index) {\n    if (!this.isGroupConversation()) return false;\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    return currentMessage?.sender?.id !== previousMessage?.sender?.id;\n  }\n  getUserColor(userId) {\n    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n  // Propriété Math pour le template\n  get Math() {\n    return Math;\n  }\n  // Méthodes pour les fichiers\n  downloadSelectedFiles() {\n    // Télécharger les fichiers sélectionnés\n  }\n  shareSelectedFiles() {\n    // Partager les fichiers sélectionnés\n  }\n  deleteSelectedFiles() {\n    // Supprimer les fichiers sélectionnés\n  }\n  // Méthodes pour les analytics\n  updateAnalytics() {\n    // Mettre à jour les analytics\n  }\n  exportAnalytics() {\n    // Exporter les analytics\n  }\n  getMessageTypeIcon(type) {\n    const icons = {\n      text: 'fas fa-comment',\n      image: 'fas fa-image',\n      video: 'fas fa-video',\n      voice: 'fas fa-microphone',\n      file: 'fas fa-file',\n      location: 'fas fa-map-marker-alt',\n      contact: 'fas fa-user'\n    };\n    return icons[type] || 'fas fa-comment';\n  }\n  // Méthodes pour les intégrations\n  createNewIntegration() {\n    // Créer une nouvelle intégration\n  }\n  selectIntegrationCategory(category) {\n    this.selectedIntegrationCategory = category;\n  }\n  getIntegrationCategoryIcon(category) {\n    const icons = {\n      CRM: 'fas fa-users',\n      Productivité: 'fas fa-tasks',\n      Communication: 'fas fa-comments',\n      Analytics: 'fas fa-chart-bar',\n      Sécurité: 'fas fa-shield-alt'\n    };\n    return icons[category] || 'fas fa-puzzle-piece';\n  }\n  getAvailableIntegrations() {\n    return [{\n      id: 1,\n      name: 'Slack',\n      category: 'Communication',\n      icon: 'fab fa-slack'\n    }, {\n      id: 2,\n      name: 'Trello',\n      category: 'Productivité',\n      icon: 'fab fa-trello'\n    }, {\n      id: 3,\n      name: 'Google Analytics',\n      category: 'Analytics',\n      icon: 'fab fa-google'\n    }];\n  }\n  configureIntegration(integration) {\n    // Configurer l'intégration\n  }\n  toggleIntegration(integration) {\n    // Basculer l'intégration\n  }\n  getActiveIntegrations() {\n    return this.getAvailableIntegrations().filter(i => i.active);\n  }\n  getIntegrationStatusText(status) {\n    const texts = {\n      active: 'Actif',\n      inactive: 'Inactif',\n      error: 'Erreur',\n      pending: 'En attente'\n    };\n    return texts[status] || 'Inconnu';\n  }\n  formatLastActivity(lastActivity) {\n    return this.formatLastActive(lastActivity);\n  }\n  viewIntegrationLogs(integration) {\n    // Voir les logs d'intégration\n  }\n  editIntegration(integration) {\n    // Modifier l'intégration\n  }\n  testIntegration(integration) {\n    // Tester l'intégration\n  }\n  removeIntegration(integration) {\n    // Supprimer l'intégration\n  }\n  // Méthodes pour les webhooks\n  editWebhook(webhook) {\n    // Modifier le webhook\n  }\n  testWebhook(webhook) {\n    // Tester le webhook\n  }\n  deleteWebhook(webhook) {\n    // Supprimer le webhook\n  }\n  createWebhook() {\n    // Créer un webhook\n  }\n  // Méthodes pour les modales\n  hasActiveModal() {\n    return this.showImageViewer || this.showForwardModal || this.showAnalyticsDashboard || this.showIntegrations || this.showEmojiPicker || this.showCamera || this.showDrawingTool || this.showFileManager;\n  }\n  closeActiveModal() {\n    this.showImageViewer = false;\n    this.showForwardModal = false;\n    this.showAnalyticsDashboard = false;\n    this.showIntegrations = false;\n    this.showEmojiPicker = false;\n    this.showCamera = false;\n    this.showDrawingTool = false;\n    this.showFileManager = false;\n  }\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE HTML ===\n  // Méthodes pour l'éditeur d'images\n  flipImage(direction) {\n    console.log('Flip image:', direction);\n  }\n  applyImageFilter(filterName) {\n    this.selectedImageFilter = filterName;\n  }\n  updateImageAdjustments() {\n    // Mettre à jour les ajustements d'image\n  }\n  resetImageAdjustments() {\n    this.imageAdjustments = {\n      brightness: 0,\n      contrast: 0,\n      saturation: 0,\n      hue: 0\n    };\n  }\n  addTextToImage() {\n    if (this.newTextContent.trim()) {\n      this.imageTextElements.push({\n        id: Date.now().toString(),\n        content: this.newTextContent,\n        x: 50,\n        y: 50,\n        fontFamily: this.textFontFamily,\n        fontSize: this.textFontSize,\n        color: this.textColor\n      });\n      this.newTextContent = '';\n    }\n  }\n  // Méthodes pour le gestionnaire de fichiers\n  createNewFolder() {\n    const folderName = prompt('Nom du nouveau dossier:');\n    if (folderName) {\n      this.fileFolders.push({\n        id: Date.now().toString(),\n        name: folderName,\n        type: 'folder',\n        size: 0,\n        modifiedAt: new Date()\n      });\n    }\n  }\n  uploadFiles() {\n    this.openFileInput();\n  }\n  toggleFileView() {\n    this.fileViewMode = this.fileViewMode === 'grid' ? 'list' : 'grid';\n  }\n  closeFileManager() {\n    this.showFileManager = false;\n  }\n  selectFolder(folder) {\n    this.selectedFolder = folder;\n  }\n  renameFolder(folder) {\n    const newName = prompt('Nouveau nom:', folder.name);\n    if (newName) {\n      folder.name = newName;\n    }\n  }\n  deleteFolder(folder) {\n    if (confirm('Supprimer ce dossier ?')) {\n      this.fileFolders = this.fileFolders.filter(f => f.id !== folder.id);\n    }\n  }\n  getStorageUsagePercentage() {\n    return 65; // Exemple\n  }\n\n  getStorageUsed() {\n    return '6.5 GB';\n  }\n  getStorageTotal() {\n    return '10 GB';\n  }\n  getTotalFilesCount() {\n    return 1247;\n  }\n  navigateToFolder(crumb) {\n    this.selectedFolder = crumb;\n  }\n  onFileSearch() {\n    // Rechercher dans les fichiers\n  }\n  applyFileFilters() {\n    // Appliquer les filtres de fichiers\n  }\n  sortFiles() {\n    // Trier les fichiers\n  }\n  getFilteredFiles() {\n    return [{\n      id: '1',\n      name: 'Document.pdf',\n      type: 'file',\n      size: 1024000,\n      modifiedAt: new Date()\n    }, {\n      id: '2',\n      name: 'Image.jpg',\n      type: 'image',\n      size: 2048000,\n      modifiedAt: new Date()\n    }];\n  }\n  toggleFileSelection(file) {\n    const index = this.selectedFiles.indexOf(file.id);\n    if (index > -1) {\n      this.selectedFiles.splice(index, 1);\n    } else {\n      this.selectedFiles.push(file.id);\n    }\n  }\n  openFile(file) {\n    console.log('Ouvrir fichier:', file.name);\n  }\n  formatFileDate(date) {\n    return new Date(date).toLocaleDateString('fr-FR');\n  }\n  shareFile(file) {\n    console.log('Partager fichier:', file.name);\n  }\n  deleteFile(file) {\n    if (confirm('Supprimer ce fichier ?')) {\n      console.log('Fichier supprimé:', file.name);\n    }\n  }\n  // Méthodes pour les propriétés manquantes dans le template\n  get forwarded() {\n    return false; // Propriété pour les messages transférés\n  }\n  // Méthodes pour corriger les erreurs du template\n  getOnlineUsersCount() {\n    return this.onlineUsersCount;\n  }\n  // Méthodes manquantes pour le template\n  truncateText(text, maxLength) {\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  }\n  hasImage(message) {\n    return message.type === 'image' || message.attachments && message.attachments.some(a => a.type.startsWith('image/'));\n  }\n  isVoiceMessage(message) {\n    return message.type === 'voice' || message.type === 'audio';\n  }\n  hasFile(message) {\n    return message.type === 'file' || message.attachments && message.attachments.length > 0;\n  }\n  getFileName(message) {\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments[0].name || 'Fichier';\n    }\n    return 'Fichier';\n  }\n  getMessageBubbleClass(message) {\n    const isOwn = message.sender?.id === this.currentUserId;\n    return `whatsapp-message-bubble ${isOwn ? 'own' : 'other'}`;\n  }\n  isLocationMessage(message) {\n    return message.type === 'location';\n  }\n  isContactMessage(message) {\n    return message.type === 'contact';\n  }\n  // Méthodes pour la caméra\n  closeCamera() {\n    this.showCamera = false;\n  }\n  setCameraMode(mode) {\n    this.cameraMode = mode;\n  }\n  toggleCameraFlash() {\n    this.flashEnabled = !this.flashEnabled;\n  }\n  switchCamera() {\n    // Basculer entre caméra avant et arrière\n  }\n  openGallery() {\n    this.openFileInput();\n  }\n  capturePhoto() {\n    // Capturer une photo\n  }\n  startVideoRecording() {\n    this.isRecordingVideo = true;\n    this.videoRecordingDuration = 0;\n  }\n  stopVideoRecording() {\n    this.isRecordingVideo = false;\n  }\n  toggleCameraGrid() {\n    this.showCameraGrid = !this.showCameraGrid;\n  }\n  formatRecordingTime(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }\n  // Méthodes pour l'éditeur d'images\n  closeImageEditor() {\n    this.showImageEditor = false;\n  }\n  saveEditedImage() {\n    // Sauvegarder l'image éditée\n  }\n  startCropResize(corner, event) {\n    // Commencer le redimensionnement de crop\n  }\n  editTextElement(index) {\n    // Éditer un élément de texte\n  }\n  setImageEditorMode(mode) {\n    this.imageEditorMode = mode;\n  }\n  setCropRatio(ratio) {\n    this.cropRatio = ratio;\n  }\n  rotateImage(angle) {\n    // Faire tourner l'image\n  }\n  // Initialiser des données de test\n  initializeTestData() {\n    // Créer une conversation de test\n    this.conversation = {\n      id: 'test-conversation',\n      participants: [{\n        id: 'user1',\n        username: 'Alice'\n      }, {\n        id: 'user2',\n        username: 'Bob'\n      }]\n    };\n    // Créer des messages de test\n    this.messages = [{\n      id: '1',\n      content: 'Salut ! Comment ça va ?',\n      sender: {\n        id: 'user2',\n        username: 'Bob'\n      },\n      timestamp: new Date(Date.now() - 3600000),\n      conversationId: 'test-conversation',\n      type: 'text'\n    }, {\n      id: '2',\n      content: 'Ça va bien, merci ! Et toi ?',\n      sender: {\n        id: this.currentUserId,\n        username: this.currentUsername\n      },\n      timestamp: new Date(Date.now() - 3000000),\n      conversationId: 'test-conversation',\n      type: 'text'\n    }, {\n      id: '3',\n      content: \"Super ! Tu veux qu'on se voit ce soir ?\",\n      sender: {\n        id: 'user2',\n        username: 'Bob'\n      },\n      timestamp: new Date(Date.now() - 1800000),\n      conversationId: 'test-conversation',\n      type: 'text'\n    }];\n    this.otherParticipant = {\n      id: 'user2',\n      username: 'Bob'\n    };\n    this.loading = false;\n  }\n  // === MÉTHODES MANQUANTES POUR CORRIGER LES ERREURS DU TEMPLATE ===\n  // Méthodes pour les emojis et stickers\n  insertGif(gif) {\n    // Insérer un GIF dans le message\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + `[GIF:${gif.id}]`\n    });\n  }\n  manageStickerPacks() {\n    // Gérer les packs de stickers\n  }\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  // Méthodes pour l'outil de dessin\n  openDrawingTool() {\n    this.showDrawingTool = true;\n  }\n  closeDrawingTool() {\n    this.showDrawingTool = false;\n  }\n  selectDrawingTool(tool) {\n    this.selectedDrawingTool = tool;\n  }\n  selectDrawingColor(color) {\n    this.selectedDrawingColor = color;\n  }\n  undoDrawing() {\n    // Annuler le dernier trait\n  }\n  redoDrawing() {\n    // Refaire le dernier trait annulé\n  }\n  clearDrawing() {\n    // Effacer tout le dessin\n  }\n  startDrawing(event) {\n    // Commencer à dessiner\n  }\n  draw(event) {\n    // Dessiner\n  }\n  stopDrawing() {\n    // Arrêter de dessiner\n  }\n  saveDrawing() {\n    // Sauvegarder le dessin\n  }\n  // === MÉTHODES POUR LES RÉACTIONS ===\n  hasUserReacted(reaction, userId) {\n    if (!userId) return false;\n    return reaction.userId === userId;\n  }\n  getReactionTooltip(reaction) {\n    return `${reaction.user?.username || 'Utilisateur'} a réagi avec ${reaction.emoji}`;\n  }\n  // === MÉTHODES POUR LES PERMISSIONS ===\n  canDeleteMessage(message) {\n    // L'utilisateur peut supprimer ses propres messages ou s'il est admin\n    return message.sender?.id === this.currentUserId || this.isAdmin;\n  }\n  canEditMessage(message) {\n    // L'utilisateur peut modifier ses propres messages\n    return message.sender?.id === this.currentUserId;\n  }\n  // === MÉTHODES POUR LA SÉLECTION DE MESSAGES ===\n  exitSelectionMode() {\n    this.selectionMode = false;\n    this.selectedMessages.clear();\n  }\n  deleteSelectedMessages() {\n    if (this.selectedMessages.size === 0) return;\n    if (confirm(`Supprimer ${this.selectedMessages.size} message(s) ?`)) {\n      // Supprimer les messages sélectionnés\n      this.messages = this.messages.filter(m => !m.id || !this.selectedMessages.has(m.id));\n      this.exitSelectionMode();\n      this.toastService.showSuccess('Messages supprimés');\n    }\n  }\n  forwardSelectedMessages() {\n    if (this.selectedMessages.size === 0) return;\n    // Ouvrir le modal de transfert\n    this.showForwardModal = true;\n    this.forwardingMessage = Array.from(this.selectedMessages);\n  }\n  copySelectedMessages() {\n    if (this.selectedMessages.size === 0) return;\n    const selectedMessagesArray = this.messages.filter(m => m.id && this.selectedMessages.has(m.id));\n    const textToCopy = selectedMessagesArray.map(m => `${m.sender?.username}: ${m.content}`).join('\\n');\n    navigator.clipboard.writeText(textToCopy).then(() => {\n      this.toastService.showSuccess('Messages copiés');\n      this.exitSelectionMode();\n    }).catch(() => {\n      this.toastService.showError('Erreur lors de la copie');\n    });\n  }\n  // === MÉTHODES POUR LES RÉPONSES ===\n  cancelReply() {\n    this.replyingToMessage = null;\n  }\n  // === MÉTHODES POUR LES FICHIERS ===\n  removeSelectedFile() {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n  // === MÉTHODES POUR L'ENREGISTREMENT VOCAL ===\n  formatRecordingDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  cancelVoiceRecording() {\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n  }\n  // === MÉTHODES POUR LES SONDAGES ===\n  canCreatePoll() {\n    return this.pollQuestion.trim().length > 0 && this.pollOptions.filter(opt => opt.text.trim().length > 0).length >= 2;\n  }\n  // === MÉTHODES POUR LA LOCALISATION ===\n  closeLocationPicker() {\n    this.showLocationPicker = false;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n    this.locationSearchResults = [];\n    this.selectedLocation = null;\n  }\n  getCurrentLocation() {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        this.selectedLocation = {\n          latitude: position.coords.latitude,\n          longitude: position.coords.longitude,\n          name: 'Ma position actuelle'\n        };\n      }, error => {\n        this.toastService.showError(\"Impossible d'obtenir votre position\");\n      });\n    } else {\n      this.toastService.showError('Géolocalisation non supportée');\n    }\n  }\n  searchLocation() {\n    this.showLocationSearch = true;\n    // Simuler une recherche de lieux\n    this.locationSearchResults = [{\n      name: 'Paris, France',\n      latitude: 48.8566,\n      longitude: 2.3522\n    }, {\n      name: 'Lyon, France',\n      latitude: 45.764,\n      longitude: 4.8357\n    }, {\n      name: 'Marseille, France',\n      latitude: 43.2965,\n      longitude: 5.3698\n    }];\n  }\n  onLocationSearch() {\n    // Filtrer les résultats selon la recherche\n    if (this.locationSearchQuery.trim()) {\n      this.locationSearchResults = this.locationSearchResults.filter(location => location.name.toLowerCase().includes(this.locationSearchQuery.toLowerCase()));\n    }\n  }\n  selectLocationResult(result) {\n    this.selectedLocation = result;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n  }\n  editLocationMessage() {\n    this.showLocationMessage = true;\n  }\n  shareLocation() {\n    if (this.selectedLocation) {\n      // Créer un message de localisation\n      const locationMessage = {\n        id: Date.now().toString(),\n        type: 'location',\n        location: this.selectedLocation,\n        locationMessage: this.locationMessage,\n        sender: {\n          id: this.currentUserId,\n          username: this.currentUsername\n        },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id\n      };\n      this.messages.push(locationMessage);\n      this.closeLocationPicker();\n      this.locationMessage = '';\n      this.toastService.showSuccess('Localisation partagée');\n    }\n  }\n  // === MÉTHODES POUR LES CONTACTS ===\n  closeContactPicker() {\n    this.showContactPicker = false;\n    this.contactSearchQuery = '';\n    this.selectedContactForSharing = null;\n  }\n  onContactSearch() {\n    // Filtrer les contacts selon la recherche\n    // Implémentation de la recherche de contacts\n  }\n  getFilteredContactsForSharing() {\n    // Simuler une liste de contacts\n    const contacts = [{\n      id: '1',\n      name: 'Alice Martin',\n      phone: '+33123456789',\n      email: '<EMAIL>'\n    }, {\n      id: '2',\n      name: 'Bob Dupont',\n      phone: '+33987654321',\n      email: '<EMAIL>'\n    }, {\n      id: '3',\n      name: 'Claire Durand',\n      phone: '+33555666777',\n      email: '<EMAIL>'\n    }];\n    if (this.contactSearchQuery.trim()) {\n      return contacts.filter(contact => contact.name.toLowerCase().includes(this.contactSearchQuery.toLowerCase()) || contact.phone.includes(this.contactSearchQuery) || contact.email.toLowerCase().includes(this.contactSearchQuery.toLowerCase()));\n    }\n    return contacts;\n  }\n  selectContactForSharing(contact) {\n    this.selectedContactForSharing = contact;\n  }\n  viewContactDetails(contact) {\n    // Ouvrir les détails du contact\n    this.toastService.showInfo(`Détails de ${contact.name}`);\n  }\n  createNewContact() {\n    // Ouvrir le formulaire de création de contact\n    this.toastService.showInfo(\"Création d'un nouveau contact\");\n  }\n  shareContact() {\n    if (this.selectedContactForSharing) {\n      // Créer un message de contact\n      const contactMessage = {\n        id: Date.now().toString(),\n        type: 'contact',\n        contact: this.selectedContactForSharing,\n        sender: {\n          id: this.currentUserId,\n          username: this.currentUsername\n        },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id\n      };\n      this.messages.push(contactMessage);\n      this.closeContactPicker();\n      this.toastService.showSuccess('Contact partagé');\n    }\n  }\n  // === MÉTHODES POUR LES EMOJIS ET STICKERS ===\n  openStickerPicker() {\n    this.showStickerPicker = true;\n    this.showEmojiPicker = false;\n  }\n  openGifPicker() {\n    this.showGifPicker = true;\n    this.showEmojiPicker = false;\n  }\n  onEmojiSearch() {\n    // Filtrer les emojis selon la recherche\n    // Implémentation de la recherche d'emojis\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: currentContent + emoji\n    });\n  }\n  getFilteredEmojiCategories() {\n    return this.emojiCategories.filter(category => {\n      if (!this.emojiSearchQuery.trim()) return true;\n      return category.name.toLowerCase().includes(this.emojiSearchQuery.toLowerCase());\n    });\n  }\n  previewEmoji(emoji) {\n    this.previewedEmoji = emoji;\n  }\n  selectStickerPack(packId) {\n    this.selectedStickerPack = packId;\n  }\n  getSelectedStickerPack() {\n    return this.stickerPacks.find(pack => pack.id === this.selectedStickerPack);\n  }\n  insertSticker(sticker) {\n    // Créer un message sticker\n    const stickerMessage = {\n      id: Date.now().toString(),\n      type: 'sticker',\n      sticker: sticker,\n      sender: {\n        id: this.currentUserId,\n        username: this.currentUsername\n      },\n      timestamp: new Date(),\n      conversationId: this.conversation?.id\n    };\n    this.messages.push(stickerMessage);\n    this.showStickerPicker = false;\n    this.toastService.showSuccess('Sticker envoyé');\n  }\n  onGifSearch() {\n    // Filtrer les GIFs selon la recherche\n    // Implémentation de la recherche de GIFs\n  }\n  selectGifCategory(category) {\n    this.selectedGifCategory = category;\n  }\n  getFilteredGifs() {\n    // Simuler une liste de GIFs\n    return [{\n      id: '1',\n      url: 'gif1.gif',\n      title: 'Happy'\n    }, {\n      id: '2',\n      url: 'gif2.gif',\n      title: 'Funny'\n    }, {\n      id: '3',\n      url: 'gif3.gif',\n      title: 'Love'\n    }];\n  }\n  cancelDelete() {\n    this.showDeleteConfirmModal = false;\n  }\n  confirmDelete() {\n    // Logique de suppression\n    this.showDeleteConfirmModal = false;\n    this.toastService.showSuccess('Message supprimé');\n  }\n  closeForwardModal() {\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n  }\n  getFilteredContacts() {\n    const contacts = [{\n      id: '1',\n      name: 'Alice Martin',\n      avatar: 'avatar1.jpg'\n    }, {\n      id: '2',\n      name: 'Bob Dupont',\n      avatar: 'avatar2.jpg'\n    }, {\n      id: '3',\n      name: 'Claire Durand',\n      avatar: 'avatar3.jpg'\n    }];\n    if (this.forwardSearchQuery.trim()) {\n      return contacts.filter(contact => contact.name.toLowerCase().includes(this.forwardSearchQuery.toLowerCase()));\n    }\n    return contacts;\n  }\n  toggleContactSelection(contactId) {\n    const index = this.selectedConversations.indexOf(contactId);\n    if (index > -1) {\n      this.selectedConversations.splice(index, 1);\n    } else {\n      this.selectedConversations.push(contactId);\n    }\n  }\n  confirmForward() {\n    if (this.selectedConversations.length > 0) {\n      this.toastService.showSuccess(`Message transféré à ${this.selectedConversations.length} contact(s)`);\n      this.closeForwardModal();\n    }\n  }\n  // === MÉTHODES POUR LE VISUALISEUR D'IMAGES ===\n  previousImage() {\n    if (this.currentImageIndex > 0) {\n      this.currentImageIndex--;\n    }\n  }\n  nextImage() {\n    if (this.currentImageIndex < this.imageGallery.length - 1) {\n      this.currentImageIndex++;\n    }\n  }\n  // === MÉTHODES POUR LES TOASTS ===\n  getToastIcon() {\n    const icons = {\n      success: 'fas fa-check-circle',\n      error: 'fas fa-exclamation-circle',\n      warning: 'fas fa-exclamation-triangle',\n      info: 'fas fa-info-circle'\n    };\n    return icons[this.toastType] || 'fas fa-info-circle';\n  }\n  // === MÉTHODES POUR LES STATISTIQUES ===\n  getTotalMessagesCount() {\n    return this.messages.length;\n  }\n  getPhotosCount() {\n    return this.messages.filter(m => m.attachments?.some(att => att.type === 'image')).length;\n  }\n  getFilesCount() {\n    return this.messages.filter(m => m.attachments?.some(att => att.type === 'file')).length;\n  }\n  // === MÉTHODES POUR LES PARAMÈTRES DE CONVERSATION ===\n  updateConversationSettings() {\n    // Sauvegarder les paramètres de conversation\n    this.toastService.showSuccess('Paramètres mis à jour');\n  }\n  // === MÉTHODES POUR LA TRADUCTION ===\n  closeTranslationPanel() {\n    this.showTranslationPanel = false;\n    this.originalTextForTranslation = '';\n    this.translatedText = '';\n  }\n  swapLanguages() {\n    const temp = this.translationFrom;\n    this.translationFrom = this.translationTo;\n    this.translationTo = temp;\n  }\n  copyTranslation() {\n    if (this.translatedText) {\n      navigator.clipboard.writeText(this.translatedText).then(() => {\n        this.toastService.showSuccess('Traduction copiée');\n      });\n    }\n  }\n  insertTranslation() {\n    if (this.translatedText) {\n      const currentContent = this.messageForm.get('content')?.value || '';\n      this.messageForm.patchValue({\n        content: currentContent + this.translatedText\n      });\n      this.closeTranslationPanel();\n    }\n  }\n  shareTranslation() {\n    if (this.translatedText) {\n      // Créer un message avec la traduction\n      const translationMessage = {\n        id: Date.now().toString(),\n        content: this.translatedText,\n        sender: {\n          id: this.currentUserId,\n          username: this.currentUsername\n        },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id\n      };\n      this.messages.push(translationMessage);\n      this.closeTranslationPanel();\n      this.toastService.showSuccess('Traduction partagée');\n    }\n  }\n  // === MÉTHODES POUR LES SONDAGES ===\n  closePollCreator() {\n    this.showPollCreator = false;\n    this.pollQuestion = '';\n    this.pollOptions = [{\n      text: ''\n    }, {\n      text: ''\n    }];\n    this.pollSettings = {\n      allowMultiple: false,\n      anonymous: false,\n      showResults: true,\n      showVoters: true\n    };\n  }\n  addPollOption() {\n    if (this.pollOptions.length < 10) {\n      this.pollOptions.push({\n        text: ''\n      });\n    }\n  }\n  removePollOption(index) {\n    if (this.pollOptions.length > 2) {\n      this.pollOptions.splice(index, 1);\n    }\n  }\n  createPoll() {\n    if (this.canCreatePoll()) {\n      // Créer un message sondage\n      const pollMessage = {\n        id: Date.now().toString(),\n        type: 'poll',\n        poll: {\n          question: this.pollQuestion,\n          options: this.pollOptions.filter(opt => opt.text.trim()),\n          settings: this.pollSettings,\n          votes: [],\n          totalVotes: 0\n        },\n        sender: {\n          id: this.currentUserId,\n          username: this.currentUsername\n        },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id\n      };\n      this.messages.push(pollMessage);\n      this.closePollCreator();\n      this.toastService.showSuccess('Sondage créé');\n    }\n  }\n  // === MÉTHODES POUR LES FONCTIONNALITÉS DE BASE ===\n  hasMessageContent() {\n    const content = this.messageForm.get('content')?.value;\n    return content && content.trim().length > 0;\n  }\n  canSendMessage() {\n    return this.hasMessageContent() || this.selectedFile !== null;\n  }\n  searchEmojis() {\n    // Rechercher des emojis selon emojiSearchQuery\n  }\n  getFilteredEmojis() {\n    // Simuler une liste d'emojis\n    const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'];\n    if (this.emojiSearchQuery.trim()) {\n      return emojis.filter(emoji => emoji.includes(this.emojiSearchQuery));\n    }\n    return emojis;\n  }\n  // === MÉTHODES POUR LES NOTIFICATIONS ===\n  toggleNotificationPanel() {\n    this.showNotificationPanel = !this.showNotificationPanel;\n  }\n  onSearchKeyPress(event) {\n    if (event.key === 'Enter') {\n      this.performSearch();\n    }\n  }\n  toggleSearchBar() {\n    this.showSearchBar = !this.showSearchBar;\n    if (!this.showSearchBar) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n  navigateToMessage(messageId) {\n    // Naviguer vers un message spécifique\n    this.highlightedMessageId = messageId;\n  }\n  highlightSearchTerms(content, query) {\n    if (!query) return content;\n    const regex = new RegExp(`(${query})`, 'gi');\n    return content.replace(regex, '<mark>$1</mark>');\n  }\n  // === MÉTHODES POUR LES MESSAGES ÉPINGLÉS ===\n  getPinnedMessagesCount() {\n    return this.messages.filter(m => m.pinned).length;\n  }\n  togglePinnedMessages() {\n    this.showPinnedMessages = !this.showPinnedMessages;\n  }\n  scrollToPinnedMessage(messageId) {\n    this.scrollToMessage(messageId);\n  }\n  formatMessageDate(date) {\n    return this.formatMessageTime(date);\n  }\n  // === MÉTHODES POUR LES APPELS ===\n  declineCall() {\n    this.incomingCall = null;\n    this.toastService.showInfo('Appel refusé');\n  }\n  acceptCall() {\n    this.incomingCall = null;\n    this.isInCall = true;\n    this.toastService.showSuccess('Appel accepté');\n  }\n  formatCallDuration(duration) {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  getCallStatusText() {\n    if (this.isInCall) return 'En cours...';\n    return 'Connexion...';\n  }\n  toggleCallMinimize() {\n    this.isCallMinimized = !this.isCallMinimized;\n  }\n  // === MÉTHODES POUR LE FORMATAGE DE TEXTE ===\n  applyFormatting(type) {\n    const content = this.messageForm.get('content')?.value || '';\n    let formattedContent = content;\n    switch (type) {\n      case 'bold':\n        formattedContent = `*${content}*`;\n        break;\n      case 'italic':\n        formattedContent = `_${content}_`;\n        break;\n      case 'strikethrough':\n        formattedContent = `~${content}~`;\n        break;\n      case 'underline':\n        formattedContent = `__${content}__`;\n        break;\n      case 'code':\n        formattedContent = `\\`${content}\\``;\n        break;\n      case 'quote':\n        formattedContent = `> ${content}`;\n        break;\n      case 'spoiler':\n        formattedContent = `||${content}||`;\n        break;\n    }\n    this.messageForm.patchValue({\n      content: formattedContent\n    });\n  }\n  hasFormatting(type) {\n    const content = this.messageForm.get('content')?.value || '';\n    switch (type) {\n      case 'bold':\n        return content.includes('*');\n      case 'italic':\n        return content.includes('_');\n      case 'strikethrough':\n        return content.includes('~');\n      case 'underline':\n        return content.includes('__');\n      case 'code':\n        return content.includes('`');\n      case 'quote':\n        return content.includes('>');\n      case 'spoiler':\n        return content.includes('||');\n      default:\n        return false;\n    }\n  }\n  insertLink() {\n    const url = prompt(\"Entrez l'URL du lien :\");\n    if (url) {\n      const text = prompt('Entrez le texte du lien :') || url;\n      const content = this.messageForm.get('content')?.value || '';\n      this.messageForm.patchValue({\n        content: content + `[${text}](${url})`\n      });\n    }\n  }\n  insertTable() {\n    const table = '\\n| Colonne 1 | Colonne 2 |\\n|-----------|----------|\\n| Cellule 1 | Cellule 2 |\\n';\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + table\n    });\n  }\n  insertList(type) {\n    const content = this.messageForm.get('content')?.value || '';\n    const listItem = type === 'ul' ? '• Élément de liste\\n' : '1. Élément de liste\\n';\n    this.messageForm.patchValue({\n      content: content + '\\n' + listItem\n    });\n  }\n  insertMentionSymbol() {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + '@'\n    });\n  }\n  insertHashtagSymbol() {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + '#'\n    });\n  }\n  clearFormatting() {\n    const content = this.messageForm.get('content')?.value || '';\n    const cleanContent = content.replace(/\\*([^*]+)\\*/g, '$1') // Bold\n    .replace(/_([^_]+)_/g, '$1') // Italic\n    .replace(/~([^~]+)~/g, '$1') // Strikethrough\n    .replace(/__([^_]+)__/g, '$1') // Underline\n    .replace(/`([^`]+)`/g, '$1') // Code\n    .replace(/> /g, '') // Quote\n    .replace(/\\|\\|([^|]+)\\|\\|/g, '$1'); // Spoiler\n    this.messageForm.patchValue({\n      content: cleanContent\n    });\n  }\n  toggleFormattingToolbar() {\n    this.showFormattingToolbar = !this.showFormattingToolbar;\n  }\n  // === MÉTHODES POUR LES CORRECTIONS AUTOMATIQUES ===\n  hideAutoCorrectSuggestions() {\n    this.autoCorrectSuggestions = [];\n  }\n  applyCorrection(original, correction) {\n    const content = this.messageForm.get('content')?.value || '';\n    const correctedContent = content.replace(original, correction);\n    this.messageForm.patchValue({\n      content: correctedContent\n    });\n    this.hideAutoCorrectSuggestions();\n  }\n  ignoreCorrection(original) {\n    this.autoCorrectSuggestions = this.autoCorrectSuggestions.filter(s => s.original !== original);\n  }\n  // === MÉTHODES POUR LES FICHIERS ===\n  triggerFileInput(type) {\n    const input = document.createElement('input');\n    input.type = 'file';\n    switch (type) {\n      case 'image':\n        input.accept = 'image/*';\n        break;\n      case 'video':\n        input.accept = 'video/*';\n        break;\n      case 'audio':\n        input.accept = 'audio/*';\n        break;\n      case 'document':\n        input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx';\n        break;\n    }\n    input.onchange = event => {\n      this.onFileSelected(event);\n    };\n    input.click();\n  }\n  // === MÉTHODES POUR L'INPUT DE MESSAGE ===\n  getInputPlaceholder() {\n    if (this.replyingToMessage) {\n      return `Répondre à ${this.replyingToMessage.sender?.username}...`;\n    }\n    if (this.isRecordingVoice) {\n      return 'Enregistrement en cours...';\n    }\n    return 'Tapez votre message...';\n  }\n  onInputChange(event) {\n    const content = event.target.value;\n    this.messageForm.patchValue({\n      content\n    });\n    // Détecter les mentions, hashtags, etc.\n    this.detectMentions(content);\n    this.detectHashtags(content);\n    this.detectCommands(content);\n    // Gestion de la frappe\n    this.handleTyping();\n  }\n  onInputKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    } else if (event.key === 'ArrowUp' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.selectedMentionIndex = Math.max(0, this.selectedMentionIndex - 1);\n    } else if (event.key === 'ArrowDown' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.selectedMentionIndex = Math.min(this.mentionSuggestions.length - 1, this.selectedMentionIndex + 1);\n    } else if (event.key === 'Tab' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.insertMention(this.mentionSuggestions[this.selectedMentionIndex]);\n    }\n  }\n  onInputKeyUp(event) {\n    // Gestion des suggestions en temps réel\n    this.updateSuggestions();\n  }\n  onInputFocus() {\n    this.inputFocused = true;\n  }\n  onInputBlur() {\n    this.inputFocused = false;\n    // Masquer les suggestions après un délai\n    setTimeout(() => {\n      this.mentionSuggestions = [];\n      this.hashtagSuggestions = [];\n      this.commandSuggestions = [];\n    }, 200);\n  }\n  onInputPaste(event) {\n    const clipboardData = event.clipboardData || window.clipboardData;\n    const pastedData = clipboardData.getData('text');\n    // Traiter le contenu collé\n    if (pastedData.includes('http')) {\n      // Détecter les liens\n      this.detectLinks(pastedData);\n    }\n  }\n  onInputScroll(event) {\n    // Gérer le défilement de l'input si nécessaire\n  }\n  // === MÉTHODES POUR LES MENTIONS ET SUGGESTIONS ===\n  detectMentions(content) {\n    const mentionMatch = content.match(/@(\\w*)$/);\n    if (mentionMatch) {\n      const query = mentionMatch[1];\n      this.mentionSuggestions = this.getFilteredUsers(query);\n      this.selectedMentionIndex = 0;\n    } else {\n      this.mentionSuggestions = [];\n    }\n  }\n  detectHashtags(content) {\n    const hashtagMatch = content.match(/#(\\w*)$/);\n    if (hashtagMatch) {\n      const query = hashtagMatch[1];\n      this.hashtagSuggestions = this.getFilteredHashtags(query);\n    } else {\n      this.hashtagSuggestions = [];\n    }\n  }\n  detectCommands(content) {\n    const commandMatch = content.match(/\\/(\\w*)$/);\n    if (commandMatch) {\n      const query = commandMatch[1];\n      this.commandSuggestions = this.getFilteredCommands(query);\n    } else {\n      this.commandSuggestions = [];\n    }\n  }\n  detectLinks(content) {\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    const links = content.match(urlRegex);\n    if (links) {\n      // Traiter les liens détectés\n      console.log('Liens détectés:', links);\n    }\n  }\n  insertMention(user) {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/@\\w*$/, `@${user.username} `);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.mentionSuggestions = [];\n  }\n  insertHashtag(hashtag) {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/#\\w*$/, `#${hashtag.name} `);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.hashtagSuggestions = [];\n  }\n  insertCommand(command) {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/\\/\\w*$/, `/${command.name} `);\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    this.commandSuggestions = [];\n  }\n  setSelectedMentionIndex(index) {\n    this.selectedMentionIndex = index;\n  }\n  updateSuggestions() {\n    const content = this.messageForm.get('content')?.value || '';\n    this.detectMentions(content);\n    this.detectHashtags(content);\n    this.detectCommands(content);\n  }\n  // === MÉTHODES POUR LE COMPTAGE DE CARACTÈRES ===\n  getCharacterCount() {\n    const content = this.messageForm.get('content')?.value || '';\n    return content.length;\n  }\n  hasFormattedText() {\n    const content = this.messageForm.get('content')?.value || '';\n    return content.includes('*') || content.includes('_') || content.includes('`') || content.includes('~');\n  }\n  // === MÉTHODES POUR LA TRADUCTION ===\n  getLanguageName(languageCode) {\n    const languages = {\n      fr: 'Français',\n      en: 'Anglais',\n      es: 'Espagnol',\n      de: 'Allemand',\n      it: 'Italien'\n    };\n    return languages[languageCode] || languageCode;\n  }\n  translateMessage() {\n    this.showTranslationPanel = true;\n    const content = this.messageForm.get('content')?.value || '';\n    this.originalTextForTranslation = content;\n    // Simuler une traduction\n    this.translatedText = `[Traduction] ${content}`;\n  }\n  showAutoCorrections() {\n    // Afficher les corrections automatiques\n    this.autoCorrectSuggestions = [{\n      original: 'salut',\n      corrections: ['Salut']\n    }, {\n      original: 'ca va',\n      corrections: ['Ça va']\n    }];\n  }\n  // === MÉTHODES UTILITAIRES MANQUANTES ===\n  handleTyping() {\n    // Gérer l'indicateur de frappe\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'événement de début de frappe\n    }\n    // Réinitialiser le timer\n    clearTimeout(this.typingTimeout);\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Envoyer l'événement de fin de frappe\n    }, 1000);\n  }\n  getFilteredUsers(query) {\n    const users = [{\n      id: '1',\n      username: 'alice',\n      name: 'Alice Martin'\n    }, {\n      id: '2',\n      username: 'bob',\n      name: 'Bob Dupont'\n    }, {\n      id: '3',\n      username: 'claire',\n      name: 'Claire Durand'\n    }];\n    if (!query) return users;\n    return users.filter(user => user.username.toLowerCase().includes(query.toLowerCase()) || user.name.toLowerCase().includes(query.toLowerCase()));\n  }\n  getFilteredHashtags(query) {\n    const hashtags = [{\n      id: '1',\n      name: 'important',\n      count: 15\n    }, {\n      id: '2',\n      name: 'urgent',\n      count: 8\n    }, {\n      id: '3',\n      name: 'meeting',\n      count: 12\n    }];\n    if (!query) return hashtags;\n    return hashtags.filter(hashtag => hashtag.name.toLowerCase().includes(query.toLowerCase()));\n  }\n  getFilteredCommands(query) {\n    const commands = [{\n      id: '1',\n      name: 'help',\n      description: \"Afficher l'aide\"\n    }, {\n      id: '2',\n      name: 'clear',\n      description: 'Effacer la conversation'\n    }, {\n      id: '3',\n      name: 'status',\n      description: 'Changer le statut'\n    }];\n    if (!query) return commands;\n    return commands.filter(command => command.name.toLowerCase().includes(query.toLowerCase()));\n  }\n  // === MÉTHODES MANQUANTES POUR LES MODALS ET FONCTIONNALITÉS ===\n  openCamera() {\n    // Ouvrir la caméra\n    this.toastService.showInfo('Ouverture de la caméra...');\n  }\n  openLocationPicker() {\n    this.showLocationPicker = true;\n  }\n  openContactPicker() {\n    this.showContactPicker = true;\n  }\n  openPollCreator() {\n    this.showPollCreator = true;\n  }\n  hideQuickReplies() {\n    this.showQuickReplies = false;\n  }\n  insertQuickReply(reply) {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + reply\n    });\n    this.hideQuickReplies();\n  }\n  // === CORRECTION DES MÉTHODES AVEC PARAMÈTRES ===\n  onSearchInput(event) {\n    // Rechercher dans les messages\n    if (event) {\n      this.searchQuery = event.target.value;\n    }\n  }\n  getEmojisForCategory(category) {\n    return category?.emojis || [];\n  }\n  selectEmojiCategory(category) {\n    this.selectedEmojiCategory = category;\n  }\n  insertEmoji(emoji) {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({\n      content: newContent\n    });\n    // Fermer le picker après insertion\n    this.showEmojiPicker = false;\n    // Focus sur le textarea\n    setTimeout(() => {\n      if (this.messageTextarea) {\n        this.messageTextarea.nativeElement.focus();\n      }\n    }, 100);\n  }\n  getFileAcceptTypes() {\n    return '*/*';\n  }\n  closeAllMenus() {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n  }\n  startVideoCall() {\n    this.startCall('video');\n  }\n  startVoiceCall() {\n    this.startCall('voice');\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i6.UserStatusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 47,\n      vars: 29,\n      consts: [[1, \"flex\", \"flex-col\", \"h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-green-50\", \"dark:from-gray-900\", \"dark:to-gray-800\"], [1, \"flex\", \"items-center\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-gray-800\", \"border-b\", \"border-gray-200\", \"dark:border-gray-700\", \"shadow-sm\"], [1, \"p-2\", \"mr-3\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"text-gray-600\", \"dark:text-gray-300\"], [1, \"flex\", \"items-center\", \"flex-1\", \"min-w-0\"], [1, \"relative\", \"mr-3\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-green-500\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [\"class\", \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\", 4, \"ngIf\"], [1, \"flex-1\", \"min-w-0\"], [1, \"font-semibold\", \"text-gray-900\", \"dark:text-white\", \"truncate\"], [1, \"text-sm\", \"text-gray-500\", \"dark:text-gray-400\"], [\"class\", \"flex items-center gap-1 text-green-600\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"items-center\", \"gap-2\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [\"title\", \"Appel vocal\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Rechercher\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"title\", \"Menu\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"flex-1\", \"overflow-y-auto\", \"p-4\", \"space-y-4\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"flex flex-col items-center justify-center py-8\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [1, \"bg-white\", \"dark:bg-gray-800\", \"border-t\", \"border-gray-200\", \"dark:border-gray-700\", \"p-4\"], [1, \"flex\", \"items-end\", \"gap-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"gap-2\"], [\"type\", \"button\", \"title\", \"\\u00C9mojis\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"type\", \"button\", \"title\", \"Joindre un fichier\", 1, \"p-2\", \"rounded-full\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"text-gray-600\", \"dark:text-gray-300\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [1, \"flex-1\"], [\"formControlName\", \"content\", \"placeholder\", \"Tapez votre message...\", \"rows\", \"1\", \"maxlength\", \"4096\", \"autocomplete\", \"off\", \"spellcheck\", \"true\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-gray-50\", \"dark:bg-gray-700\", \"border\", \"border-gray-200\", \"dark:border-gray-600\", \"rounded-2xl\", \"resize-none\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-green-500\", \"focus:border-transparent\", \"text-gray-900\", \"dark:text-white\", \"placeholder-gray-500\", \"dark:placeholder-gray-400\", 3, \"disabled\", \"input\", \"keydown\", \"focus\", \"blur\"], [\"messageTextarea\", \"\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\", \"title\", \"Maintenir pour enregistrer\", 3, \"bg-red-500\", \"hover:bg-red-600\", \"animate-pulse\", \"mousedown\", \"mouseup\", \"mouseleave\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\", \"title\", \"Envoyer\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"class\", \"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 1, \"hidden\", 3, \"accept\", \"change\"], [\"fileInput\", \"\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-25 z-40\", 3, \"click\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"right-0\", \"w-3\", \"h-3\", \"bg-green-500\", \"border-2\", \"border-white\", \"dark:border-gray-800\", \"rounded-full\", \"animate-pulse\"], [1, \"flex\", \"items-center\", \"gap-1\", \"text-green-600\"], [1, \"flex\", \"gap-1\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-1\", \"h-1\", \"bg-green-600\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-green-500\", \"mb-4\"], [1, \"text-gray-500\", \"dark:text-gray-400\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"text-6xl\", \"text-gray-300\", \"dark:text-gray-600\", \"mb-4\"], [1, \"fas\", \"fa-comments\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-700\", \"dark:text-gray-300\", \"mb-2\"], [1, \"text-gray-500\", \"dark:text-gray-400\", \"text-center\"], [1, \"space-y-2\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"flex items-start gap-2\", 4, \"ngIf\"], [\"class\", \"flex justify-center my-4\", 4, \"ngIf\"], [1, \"flex\", 3, \"id\", \"click\", \"contextmenu\"], [\"class\", \"mr-2 flex-shrink-0\", 4, \"ngIf\"], [1, \"max-w-xs\", \"lg:max-w-md\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\", \"relative\", \"group\"], [\"class\", \"text-xs font-semibold mb-1 opacity-75\", 3, \"color\", 4, \"ngIf\"], [\"class\", \"break-words\", 4, \"ngIf\"], [\"class\", \"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-end\", \"gap-1\", \"mt-1\", \"text-xs\", \"opacity-75\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex gap-1 mt-2\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-4\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-3\", \"py-1\", \"rounded-full\", \"shadow-sm\"], [1, \"text-xs\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"mr-2\", \"flex-shrink-0\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", \"cursor-pointer\", \"hover:scale-105\", \"transition-transform\", 3, \"src\", \"alt\", \"click\"], [1, \"text-xs\", \"font-semibold\", \"mb-1\", \"opacity-75\"], [1, \"break-words\"], [3, \"innerHTML\"], [1, \"relative\", \"cursor-pointer\", \"rounded-lg\", \"overflow-hidden\", 3, \"click\"], [1, \"max-w-full\", \"h-auto\", \"rounded-lg\", \"hover:opacity-90\", \"transition-opacity\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"hover:bg-opacity-10\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-expand\", \"text-white\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [\"class\", \"text-sm\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"text-sm\", 3, \"innerHTML\"], [1, \"flex\", \"items-center\", \"gap-3\", \"p-2\", \"bg-gray-50\", \"dark:bg-gray-600\", \"rounded-lg\", \"cursor-pointer\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"text-2xl\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"font-medium\", \"text-sm\", \"truncate\"], [1, \"p-1\", \"rounded-full\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-400\", \"transition-colors\"], [1, \"fas\", \"fa-download\", \"text-sm\"], [1, \"flex\", \"items-center\"], [\"class\", \"fas fa-clock\", \"title\", \"Envoi en cours\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", \"title\", \"Envoy\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", \"title\", \"Livr\\u00E9\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double text-blue-400\", \"title\", \"Lu\", 4, \"ngIf\"], [\"title\", \"Envoi en cours\", 1, \"fas\", \"fa-clock\"], [\"title\", \"Envoy\\u00E9\", 1, \"fas\", \"fa-check\"], [\"title\", \"Livr\\u00E9\", 1, \"fas\", \"fa-check-double\"], [\"title\", \"Lu\", 1, \"fas\", \"fa-check-double\", \"text-blue-400\"], [1, \"flex\", \"gap-1\", \"mt-2\"], [\"class\", \"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\", 3, \"bg-green-100\", \"text-green-600\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"gap-1\", \"px-2\", \"py-1\", \"bg-gray-100\", \"dark:bg-gray-600\", \"rounded-full\", \"text-xs\", \"hover:bg-gray-200\", \"dark:hover:bg-gray-500\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"bg-white\", \"dark:bg-gray-700\", \"px-4\", \"py-2\", \"rounded-2xl\", \"shadow-sm\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-gray-400\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [\"type\", \"button\", \"title\", \"Maintenir pour enregistrer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", 3, \"mousedown\", \"mouseup\", \"mouseleave\"], [1, \"fas\", \"fa-microphone\"], [\"type\", \"button\", \"title\", \"Envoyer\", 1, \"p-3\", \"rounded-full\", \"bg-green-500\", \"hover:bg-green-600\", \"text-white\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"right-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"p-4\"], [1, \"flex\", \"gap-2\", \"mb-4\", \"overflow-x-auto\"], [\"class\", \"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\", 3, \"bg-green-100\", \"text-green-600\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-8\", \"gap-2\", \"max-h-48\", \"overflow-y-auto\"], [\"class\", \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-3\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex-shrink-0\", 3, \"click\"], [1, \"p-2\", \"rounded-lg\", \"hover:bg-gray-100\", \"dark:hover:bg-gray-700\", \"transition-colors\", \"text-xl\", 3, \"title\", \"click\"], [1, \"absolute\", \"bottom-20\", \"left-4\", \"bg-white\", \"dark:bg-gray-800\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-gray-200\", \"dark:border-gray-700\", \"z-50\"], [1, \"grid\", \"grid-cols-2\", \"gap-3\"], [1, \"flex\", \"flex-col\", \"items-center\", \"gap-2\", \"p-4\", \"rounded-xl\", \"hover:bg-gray-50\", \"dark:hover:bg-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"w-12\", \"h-12\", \"bg-blue-100\", \"dark:bg-blue-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-image\", \"text-blue-600\", \"dark:text-blue-400\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"dark:text-gray-300\"], [1, \"w-12\", \"h-12\", \"bg-purple-100\", \"dark:bg-purple-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-video\", \"text-purple-600\", \"dark:text-purple-400\"], [1, \"w-12\", \"h-12\", \"bg-orange-100\", \"dark:bg-orange-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-file\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"w-12\", \"h-12\", \"bg-green-100\", \"dark:bg-green-900\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-camera\", \"text-green-600\", \"dark:text-green-400\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-25\", \"z-40\", 3, \"click\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_2_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"img\", 6);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_img_click_6_listener() {\n            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MessageChatComponent_div_7_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"h3\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 7, 0, \"div\", 11);\n          i0.ɵɵtemplate(13, MessageChatComponent_span_13_Template, 2, 1, \"span\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_15_listener() {\n            return ctx.startVideoCall();\n          });\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_17_listener() {\n            return ctx.startVoiceCall();\n          });\n          i0.ɵɵelement(18, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_19_listener() {\n            return ctx.toggleSearch();\n          });\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n            return ctx.toggleMainMenu();\n          });\n          i0.ɵɵelement(22, \"i\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"main\", 22, 23);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_main_scroll_23_listener($event) {\n            return ctx.onScroll($event);\n          });\n          i0.ɵɵtemplate(25, MessageChatComponent_div_25_Template, 4, 0, \"div\", 24);\n          i0.ɵɵtemplate(26, MessageChatComponent_div_26_Template, 7, 1, \"div\", 25);\n          i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 3, 3, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"footer\", 27)(29, \"form\", 28);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_29_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(30, \"div\", 29)(31, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_31_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(32, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_33_listener() {\n            return ctx.toggleAttachmentMenu();\n          });\n          i0.ɵɵelement(34, \"i\", 33);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 34)(36, \"textarea\", 35, 36);\n          i0.ɵɵlistener(\"input\", function MessageChatComponent_Template_textarea_input_36_listener($event) {\n            return ctx.onInputChange($event);\n          })(\"keydown\", function MessageChatComponent_Template_textarea_keydown_36_listener($event) {\n            return ctx.onInputKeyDown($event);\n          })(\"focus\", function MessageChatComponent_Template_textarea_focus_36_listener() {\n            return ctx.onInputFocus();\n          })(\"blur\", function MessageChatComponent_Template_textarea_blur_36_listener() {\n            return ctx.onInputBlur();\n          });\n          i0.ɵɵtext(38, \"        \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 29);\n          i0.ɵɵtemplate(40, MessageChatComponent_button_40_Template, 2, 6, \"button\", 37);\n          i0.ɵɵtemplate(41, MessageChatComponent_button_41_Template, 3, 3, \"button\", 38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(42, MessageChatComponent_div_42_Template, 6, 2, \"div\", 39);\n          i0.ɵɵtemplate(43, MessageChatComponent_div_43_Template, 23, 0, \"div\", 40);\n          i0.ɵɵelementStart(44, \"input\", 41, 42);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_44_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, MessageChatComponent_div_46_Template, 1, 0, \"div\", 43);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_17_0;\n          let tmp_18_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || \"Utilisateur\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUserTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserTyping);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.searchMode)(\"text-green-600\", ctx.searchMode);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.messages.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showEmojiPicker)(\"text-green-600\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"bg-green-100\", ctx.showAttachmentMenu)(\"text-green-600\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.otherParticipant || ctx.isRecordingVoice || ctx.isSendingMessage);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_17_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_17_0.value == null ? null : tmp_17_0.value.trim()));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (tmp_18_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_18_0.value == null ? null : tmp_18_0.value.trim());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAttachmentMenu);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"accept\", ctx.getFileAcceptTypes());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker || ctx.showAttachmentMenu);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.MaxLengthValidator, i5.FormGroupDirective, i5.FormControlName],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ctx_r5", "username", "ctx_r18", "formatDateSeparator", "message_r16", "timestamp", "ɵɵlistener", "MessageChatComponent_div_27_ng_container_1_div_3_Template_img_click_1_listener", "ɵɵrestoreView", "_r29", "ɵɵnextContext", "$implicit", "ctx_r27", "ɵɵresetView", "openUserProfile", "sender", "id", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r20", "getUserColor", "ctx_r21", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r33", "MessageChatComponent_div_27_ng_container_1_div_7_Template_div_click_1_listener", "_r37", "ctx_r35", "openImageViewer", "ɵɵtemplate", "MessageChatComponent_div_27_ng_container_1_div_7_div_5_Template", "ctx_r22", "getImageUrl", "MessageChatComponent_div_27_ng_container_1_div_8_Template_div_click_0_listener", "_r41", "ctx_r39", "downloadFile", "ɵɵclassMap", "ctx_r23", "getFileIcon", "getFileName", "getFileSize", "MessageChatComponent_div_27_ng_container_1_div_12_i_1_Template", "MessageChatComponent_div_27_ng_container_1_div_12_i_2_Template", "MessageChatComponent_div_27_ng_container_1_div_12_i_3_Template", "MessageChatComponent_div_27_ng_container_1_div_12_i_4_Template", "status", "MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template_button_click_0_listener", "restoredCtx", "_r52", "reaction_r49", "ctx_r50", "toggleReaction", "emoji", "ɵɵclassProp", "ctx_r48", "hasUserReacted", "currentUserId", "ɵɵtextInterpolate", "MessageChatComponent_div_27_ng_container_1_div_13_button_1_Template", "reactions", "ɵɵelementContainerStart", "MessageChatComponent_div_27_ng_container_1_div_1_Template", "MessageChatComponent_div_27_ng_container_1_Template_div_click_2_listener", "$event", "_r55", "ctx_r54", "onMessageClick", "MessageChatComponent_div_27_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r56", "onMessageContextMenu", "MessageChatComponent_div_27_ng_container_1_div_3_Template", "MessageChatComponent_div_27_ng_container_1_div_5_Template", "MessageChatComponent_div_27_ng_container_1_div_6_Template", "MessageChatComponent_div_27_ng_container_1_div_7_Template", "MessageChatComponent_div_27_ng_container_1_div_8_Template", "MessageChatComponent_div_27_ng_container_1_div_12_Template", "MessageChatComponent_div_27_ng_container_1_div_13_Template", "ɵɵelementContainerEnd", "ctx_r14", "shouldShowDateSeparator", "i_r17", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "hasFile", "formatMessageTime", "length", "ctx_r15", "MessageChatComponent_div_27_ng_container_1_Template", "MessageChatComponent_div_27_div_2_Template", "ctx_r6", "messages", "trackByMessageId", "isTyping", "MessageChatComponent_button_40_Template_button_mousedown_0_listener", "_r58", "ctx_r57", "startVoiceRecording", "MessageChatComponent_button_40_Template_button_mouseup_0_listener", "ctx_r59", "stopVoiceRecording", "MessageChatComponent_button_40_Template_button_mouseleave_0_listener", "ctx_r60", "cancelVoiceRecording", "ctx_r8", "isRecordingVoice", "MessageChatComponent_button_41_Template_button_click_0_listener", "_r64", "ctx_r63", "sendMessage", "MessageChatComponent_button_41_i_1_Template", "MessageChatComponent_button_41_i_2_Template", "ctx_r9", "isSendingMessage", "MessageChatComponent_div_42_button_3_Template_button_click_0_listener", "_r69", "category_r67", "ctx_r68", "selectEmojiCategory", "ctx_r65", "selectedEmojiCategory", "icon", "MessageChatComponent_div_42_button_5_Template_button_click_0_listener", "_r72", "emoji_r70", "ctx_r71", "insert<PERSON><PERSON><PERSON>", "name", "MessageChatComponent_div_42_button_3_Template", "MessageChatComponent_div_42_button_5_Template", "ctx_r10", "emojiCategories", "getEmojisForCategory", "MessageChatComponent_div_43_Template_button_click_3_listener", "_r74", "ctx_r73", "triggerFileInput", "MessageChatComponent_div_43_Template_button_click_8_listener", "ctx_r75", "MessageChatComponent_div_43_Template_button_click_13_listener", "ctx_r76", "MessageChatComponent_div_43_Template_button_click_18_listener", "ctx_r77", "openCamera", "MessageChatComponent_div_46_Template_div_click_0_listener", "_r79", "ctx_r78", "closeAllMenus", "MessageChatComponent", "constructor", "route", "router", "authUserService", "MessageService", "toastService", "fb", "cdr", "userStatusService", "conversation", "loading", "currentUsername", "isAdmin", "selectedFile", "previewUrl", "isUploading", "voiceRecordingDuration", "MAX_MESSAGES_TO_LOAD", "currentPage", "isLoadingMore", "hasMoreMessages", "subscriptions", "selectedTheme", "showThemeSelector", "showMainMenu", "showEmojiPicker", "showSearchBar", "showPinnedMessages", "showStatusSelector", "showNotificationPanel", "showNotificationSettings", "showUserStatusPanel", "showCallHistoryPanel", "showCallStatsPanel", "showVoiceMessagesPanel", "incomingCall", "activeCall", "showCallModal", "showActiveCallModal", "isCallMuted", "isVideoEnabled", "callDuration", "callTimer", "notifications", "unreadNotificationCount", "selectedNotifications", "Set", "showDeleteConfirmModal", "editingMessageId", "<PERSON><PERSON><PERSON><PERSON>", "replyingToMessage", "searchQuery", "isSearching", "searchMode", "searchResults", "showSearch", "showAdvancedSearch", "pinnedMessages", "showReactionPicker", "showDeleteConfirm", "showPinConfirm", "isPinning", "showMessageOptions", "showForwardModal", "forwardingMessage", "selectedConversations", "availableConversations", "isForwarding", "isLoadingConversations", "emojiSearchQuery", "recentEmojis", "previewedEmoji", "showStickerPicker", "stickerPacks", "selected<PERSON><PERSON>er<PERSON>ack", "showGifPicker", "gifSearch<PERSON>uery", "gifCategories", "selectedGifCategory", "showDrawingTool", "selectedDrawingTool", "drawingColors", "selectedDrawingColor", "customDrawingColor", "drawingSize", "showCamera", "cameraMode", "flashEnabled", "showCameraGrid", "showFocusIndicator", "focusX", "focusY", "lastCapturedImage", "isRecordingVideo", "videoRecordingDuration", "showImageEditor", "imageEditorMode", "cropArea", "x", "y", "width", "height", "cropRatio", "imageFilters", "label", "css", "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "imageAdjustments", "brightness", "contrast", "saturation", "hue", "imageTextElements", "newTextContent", "textFontFamily", "textFontSize", "textColor", "availableFonts", "value", "showFileManager", "fileViewMode", "fileFolders", "selectedFolder", "fileBreadcrumbs", "fileSearchQuery", "fileTypeFilter", "fileSortBy", "selectedFiles", "showAnalyticsDashboard", "analyticsTimeRange", "analyticsData", "totalMessages", "activeUsers", "avgResponseTime", "filesShared", "messagesChange", "usersChange", "responseTimeChange", "filesChange", "topUsers", "messageTypes", "longestConversation", "avgConversationDuration", "topEmojis", "showIntegrations", "integrationCategories", "selectedIntegrationCategory", "webhooks", "showQuickReplies", "quickReplies", "showMentionSuggestions", "mentionSuggestions", "selectedMentionIndex", "activeMentions", "showHashtagSuggestions", "hashtagSuggestions", "selectedHashtagIndex", "activeHashtags", "showCommandSuggestions", "commandSuggestions", "selectedCommandIndex", "activeLinks", "inputHeight", "showFormattingToolbar", "detectedLanguage", "autoCorrections", "showAutoCorrectSuggestions", "autoCorrectSuggestions", "showTranslationPanel", "translationFrom", "translationTo", "originalTextForTranslation", "translatedText", "isTranslating", "supportedLanguages", "code", "showPollCreator", "pollQuestion", "pollOptions", "text", "pollSettings", "allowMultiple", "anonymous", "showResults", "showVoters", "pollExpiry", "customPollExpiry", "showLocationPicker", "showLocationSearch", "locationSearchQuery", "locationSearchResults", "selectedLocation", "showLocationMessage", "locationMessage", "showContactPicker", "contactSearchQuery", "selectedContactForSharing", "showScheduleMessage", "customScheduleDate", "customScheduleTime", "scheduleTimezone", "availableTimezones", "highlightedMessageId", "searchResultIds", "selectionMode", "selectedMessages", "hoveredMessageId", "playingVoiceId", "showScrollToBottom", "unreadMessagesCount", "showImageViewer", "currentImageIndex", "imageGallery", "showToast", "toastMessage", "toastType", "showConversationInfo", "showConversationSettings", "conversationSettings", "soundNotifications", "readReceipts", "typingIndicators", "isUserTyping", "voiceRecordingState", "voiceRecordingSize", "recordingWaveform", "voiceRecordingQuality", "voiceEffects", "selectedContacts", "isUpdatingStatus", "showAttachmentMenu", "attachmentFiles", "fileCaptions", "fileUploadProgress", "messageContent", "c", "delays", "away", "typing", "scroll", "anim", "error", "trackById", "i", "item", "toString", "NEW_MESSAGE", "color", "CALL_MISSED", "SYSTEM", "online", "offline", "busy", "themes", "key", "calls", "COMPLETED", "MISSED", "REJECTED", "_selectedMessages", "mediaRecorder", "audioChunks", "recordingTimer", "isCallMinimized", "isInCall", "inputFocused", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showCallControls", "emojis", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "availableReactions", "commonEmojis", "getCommonEmojis", "unreadNotificationsCount", "currentUserStatus", "onlineUsersCount", "ngOnInit", "initializeComponent", "ngOnDestroy", "unsubscribe", "clearInterval", "typingTimeout", "clearTimeout", "ngAfterViewChecked", "detectChanges", "loadCurrentUser", "loadConversation", "setupSubscriptions", "initializeTestData", "params", "subscribe", "conversationId", "getConversation", "next", "participants", "find", "p", "scrollToBottom", "console", "get", "trim", "tempMessage", "Date", "now", "type", "push", "reset", "onFileSelected", "event", "files", "target", "Array", "from", "generatePreviews", "removeAttachment", "index", "splice", "messageId", "message", "m", "existingReaction", "r", "count", "users", "addReaction", "startEditMessage", "cancelEdit", "saveEdit", "editMessage", "onEditKeyDown", "shift<PERSON>ey", "preventDefault", "deleteMessage", "confirm", "startCall", "initiateCall", "call", "startCallTimer", "showError", "setInterval", "endCall", "toggleMute", "toggleVideo", "setTimeout", "messagesContainer", "element", "nativeElement", "scrollTop", "scrollHeight", "date", "toLocaleTimeString", "hour", "minute", "formatFileSize", "bytes", "k", "sizes", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "toggleSearch", "clearSearch", "toggleNotifications", "toggleConversationInfo", "toggleThemeSelector", "toggleMainMenu", "toggleAdvancedSearch", "toggleStatusSelector", "toggleUserStatusPanel", "performSearch", "filter", "toLowerCase", "includes", "highlightSearchTerm", "regex", "RegExp", "replace", "scrollToMessage", "showAllPinnedMessages", "closePinnedMessages", "togglePinMessage", "pinned", "toggleMessageOptions", "toggleReactionPicker", "replyToMessage", "forwardMessage", "copyMessage", "navigator", "clipboard", "writeText", "showSuccess", "selectMessage", "toggleMessageSelection", "has", "delete", "add", "size", "openEmojiPicker", "closeEmojiPicker", "categoryId", "addEmojiToMessage", "currentC<PERSON>nt", "setValue", "toggleAttachmentMenu", "openFileInput", "fileInput", "click", "closeImageViewer", "toggleVoicePlayback", "_this", "_asyncToGenerator", "stream", "mediaDevices", "getUserMedia", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "MediaRecorder", "mimeType", "ondataavailable", "data", "onstop", "processRecordedAudio", "start", "state", "stop", "getTracks", "for<PERSON>ach", "track", "_this2", "audioBlob", "Blob", "showWarning", "audioFile", "File", "sendVoiceMessage", "_this3", "Error", "formData", "FormData", "append", "formatRecordingDuration", "duration", "minutes", "seconds", "padStart", "changeTheme", "theme", "getThemeOptions", "updateUserStatus", "getStatusOptions", "Object", "entries", "map", "getStatusIcon", "getStatusColor", "getStatusText", "onImageLoad", "onImageError", "src", "onVideoLoaded", "openAnalyticsDashboard", "closeAnalyticsDashboard", "openIntegrations", "closeIntegrations", "sendQuickMessage", "control", "file", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "user", "getCurrentUser", "attachments", "some", "att", "imageUrl", "url", "getVideoUrl", "videoUrl", "getVideoThumbnail", "thumbnailUrl", "getVideoDuration", "fileType", "icons", "getFileType", "split", "toUpperCase", "formatVoiceDuration", "current", "total", "formatTime", "mins", "secs", "getVoiceWaveform", "_", "random", "active", "played", "getVoiceProgress", "getVoiceCurrentTime", "getVoiceTotalDuration", "getVoiceSpeed", "isImageLoading", "isVoiceLoading", "isFileDownloading", "getImageLoadingProgress", "getFileDownloadProgress", "getImageDimensions", "previewFile", "canPreviewFile", "previewableTypes", "hasVideo", "getEmojiName", "emojiNames", "openLocationViewer", "getLocationMapUrl", "mapUrl", "getLocationName", "locationName", "getLocationAddress", "address", "openContactViewer", "getContactAvatar", "contactAvatar", "getContactName", "contactName", "getContactPhone", "contactPhone", "openVideoPlayer", "seekVoiceMessage", "changeVoiceSpeed", "onScroll", "clientHeight", "loadMoreMessages", "clearConversation", "exportConversation", "getHeaderActions", "title", "onClick", "class", "isActive", "badge", "animate", "goBackToConversations", "navigate", "userId", "diff", "getTime", "toggleConversationSettings", "currentMessage", "previousMessage", "currentDate", "toDateString", "previousDate", "today", "yesterday", "setDate", "getDate", "toLocaleDateString", "weekday", "year", "month", "day", "getSystemMessageIcon", "user_joined", "user_left", "call_started", "call_ended", "message_deleted", "systemType", "onMessageHover", "isHovering", "nextMessage", "onAvatarError", "colors", "charCodeAt", "downloadSelectedFiles", "shareSelectedFiles", "deleteSelectedFiles", "updateAnalytics", "exportAnalytics", "getMessageTypeIcon", "video", "voice", "location", "contact", "createNewIntegration", "selectIntegrationCategory", "category", "getIntegrationCategoryIcon", "CRM", "Productivité", "Communication", "Analytics", "Sécurité", "getAvailableIntegrations", "configureIntegration", "integration", "toggleIntegration", "getActiveIntegrations", "getIntegrationStatusText", "texts", "inactive", "pending", "formatLastActivity", "lastActivity", "viewIntegrationLogs", "editIntegration", "testIntegration", "removeIntegration", "editWebhook", "webhook", "testWebhook", "deleteWebhook", "createWebhook", "hasActiveModal", "closeActiveModal", "flipImage", "direction", "applyImageFilter", "filterName", "updateImageAdjustments", "resetImageAdjustments", "addTextToImage", "fontFamily", "fontSize", "createNewFolder", "folderName", "prompt", "modifiedAt", "uploadFiles", "toggleFile<PERSON>iew", "closeFileManager", "selectFolder", "folder", "renameFolder", "newName", "deleteFolder", "f", "getStorageUsagePercentage", "getStorageUsed", "getStorageTotal", "getTotalFilesCount", "navigateToFolder", "crumb", "onFileSearch", "applyFileFilters", "sortFiles", "getFilteredFiles", "toggleFileSelection", "indexOf", "openFile", "formatFileDate", "shareFile", "deleteFile", "forwarded", "getOnlineUsersCount", "truncateText", "max<PERSON><PERSON><PERSON>", "substring", "a", "isVoiceMessage", "getMessageBubbleClass", "isOwn", "isLocationMessage", "isContactMessage", "closeCamera", "setCameraMode", "mode", "toggleCameraFlash", "switchCamera", "openGallery", "capturePhoto", "startVideoRecording", "stopVideoRecording", "toggleCameraGrid", "formatRecordingTime", "closeImageEditor", "saveEditedImage", "startCropResize", "corner", "editTextElement", "setImageEditorMode", "setCropRatio", "ratio", "rotateImage", "angle", "insertGif", "gif", "patchValue", "manageStickerPacks", "toggleEmojiPicker", "openDrawingTool", "closeDrawingTool", "selectDrawingTool", "tool", "selectDrawingColor", "undoDrawing", "redoDrawing", "clearDrawing", "startDrawing", "draw", "stopDrawing", "saveDrawing", "reaction", "getReactionTooltip", "canDeleteMessage", "canEditMessage", "exitSelectionMode", "clear", "deleteSelectedMessages", "forwardSelectedMessages", "copySelectedMessages", "selectedMessagesArray", "textToCopy", "join", "then", "catch", "cancelReply", "removeSelectedFile", "canCreatePoll", "opt", "closeLocationPicker", "getCurrentLocation", "geolocation", "getCurrentPosition", "position", "latitude", "coords", "longitude", "searchLocation", "onLocationSearch", "selectLocationResult", "editLocationMessage", "shareLocation", "closeContactPicker", "onContactSearch", "getFilteredContactsForSharing", "contacts", "phone", "email", "selectContactForSharing", "viewContactDetails", "showInfo", "createNewContact", "shareContact", "contactMessage", "openStickerPicker", "openGifPicker", "onEmojiSearch", "getFilteredEmojiCategories", "previewEmoji", "selectStickerPack", "packId", "getSelectedStickerPack", "pack", "insertSticker", "sticker", "stickerMessage", "onGifSearch", "selectGifCategory", "getFilteredGifs", "cancelDelete", "confirmDelete", "closeForwardModal", "getFilteredContacts", "avatar", "toggleContactSelection", "contactId", "confirmForward", "previousImage", "nextImage", "getToastIcon", "success", "warning", "info", "getTotalMessagesCount", "getPhotosCount", "getFilesCount", "updateConversationSettings", "closeTranslationPanel", "swapLanguages", "temp", "copyTranslation", "insertTranslation", "shareTranslation", "translationMessage", "closePollCreator", "addPollOption", "removePollOption", "createPoll", "pollMessage", "poll", "question", "options", "settings", "votes", "totalVotes", "hasMessageContent", "canSendMessage", "searchEmojis", "getFilteredEmojis", "toggleNotificationPanel", "onSearchKeyPress", "toggleSearchBar", "navigateToMessage", "highlightSearchTerms", "query", "getPinnedMessagesCount", "togglePinnedMessages", "scrollToPinnedMessage", "formatMessageDate", "declineCall", "acceptCall", "formatCallDuration", "getCallStatusText", "toggleCallMinimize", "applyFormatting", "formattedContent", "hasFormatting", "insertLink", "insertTable", "table", "insertList", "listItem", "insertMentionSymbol", "insertHashtagSymbol", "clearFormatting", "cleanContent", "toggleFormattingToolbar", "hideAutoCorrectSuggestions", "applyCorrection", "original", "correction", "<PERSON><PERSON><PERSON>nt", "ignoreCorrection", "s", "input", "document", "createElement", "accept", "onchange", "getInputPlaceholder", "onInputChange", "detectMentions", "detectHashtags", "detectCommands", "handleTyping", "onInputKeyDown", "max", "min", "insertMention", "onInputKeyUp", "updateSuggestions", "onInputFocus", "onInputBlur", "onInputPaste", "clipboardData", "window", "pastedData", "getData", "detectLinks", "onInputScroll", "mentionMatch", "match", "getFilteredUsers", "hashtagMatch", "getFilteredHashtags", "commandMatch", "getFilteredCommands", "urlRegex", "links", "newContent", "insertHashtag", "hashtag", "insertCommand", "command", "setSelectedMentionIndex", "getCharacterCount", "hasFormattedText", "getLanguageName", "languageCode", "languages", "fr", "en", "es", "de", "it", "translateMessage", "showAutoCorrections", "corrections", "hashtags", "commands", "description", "openLocationPicker", "openContactPicker", "openPollCreator", "hideQuickReplies", "insertQuickReply", "reply", "onSearchInput", "messageTextarea", "focus", "getFileAcceptTypes", "startVideoCall", "startVoiceCall", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AuthuserService", "i3", "i4", "ToastService", "i5", "FormBuilder", "ChangeDetectorRef", "i6", "UserStatusService", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_2_listener", "MessageChatComponent_Template_img_click_6_listener", "MessageChatComponent_div_7_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_span_13_Template", "MessageChatComponent_Template_button_click_15_listener", "MessageChatComponent_Template_button_click_17_listener", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_Template_main_scroll_23_listener", "MessageChatComponent_div_25_Template", "MessageChatComponent_div_26_Template", "MessageChatComponent_div_27_Template", "MessageChatComponent_Template_form_ngSubmit_29_listener", "MessageChatComponent_Template_button_click_31_listener", "MessageChatComponent_Template_button_click_33_listener", "MessageChatComponent_Template_textarea_input_36_listener", "MessageChatComponent_Template_textarea_keydown_36_listener", "MessageChatComponent_Template_textarea_focus_36_listener", "MessageChatComponent_Template_textarea_blur_36_listener", "MessageChatComponent_button_40_Template", "MessageChatComponent_button_41_Template", "MessageChatComponent_div_42_Template", "MessageChatComponent_div_43_Template", "MessageChatComponent_Template_input_change_44_listener", "MessageChatComponent_div_46_Template", "tmp_17_0", "tmp_18_0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Component,\n  OnIni<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  AfterViewChecked,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { User } from '@app/models/user.model';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport {\n  Message,\n  Conversation,\n  MessageType,\n  CallType,\n} from 'src/app/models/message.model';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { MessageService } from '@app/services/message.service';\n\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: './message-chat.component.html',\n})\nexport class MessageChatComponent\n  implements OnInit, OnDestroy, AfterViewChecked\n{\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  // === PROPRIÉTÉS PRINCIPALES ===\n  messages: Message[] = [];\n  messageForm: FormGroup;\n  conversation: Conversation | null = null;\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  currentUsername: string = 'You';\n  otherParticipant: User | null = null;\n  isAdmin = false;\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n  isUploading = false;\n  isTyping = false;\n  typingTimeout: any;\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n\n  // === PAGINATION ET CHARGEMENT ===\n  private readonly MAX_MESSAGES_TO_LOAD = 10;\n  private currentPage = 1;\n  isLoadingMore = false;\n  hasMoreMessages = true;\n  private subscriptions = new Subscription();\n\n  // === INTERFACE ET THÈMES ===\n  selectedTheme: string = 'theme-default';\n  showThemeSelector = false;\n  showMainMenu = false;\n  showEmojiPicker = false;\n  showSearchBar = false;\n  showPinnedMessages = false;\n  showStatusSelector = false;\n  showNotificationPanel = false;\n  showNotificationSettings = false;\n  showUserStatusPanel = false;\n  showCallHistoryPanel = false;\n  showCallStatsPanel = false;\n  showVoiceMessagesPanel = false;\n\n  // === APPELS ===\n  incomingCall: any = null;\n  activeCall: any = null;\n  showCallModal = false;\n  showActiveCallModal = false;\n  isCallMuted = false;\n  isVideoEnabled = true;\n  callDuration = 0;\n  callTimer: any = null;\n\n  // === NOTIFICATIONS ET MESSAGES ===\n  notifications: any[] = [];\n  unreadNotificationCount: number = 0;\n  selectedNotifications: Set<string> = new Set();\n  showDeleteConfirmModal: boolean = false;\n  editingMessageId: string | null = null;\n  editingContent = '';\n  replyingToMessage: any = null;\n\n  // === RECHERCHE ===\n  searchQuery = '';\n  isSearching = false;\n  searchMode = false;\n  searchResults: any[] = [];\n  showSearch = false;\n  showAdvancedSearch = false;\n\n  // === PANNEAUX ===\n  pinnedMessages: any[] = [];\n  showReactionPicker: any = {};\n  showDeleteConfirm: any = {};\n  showPinConfirm: any = {};\n  isPinning: any = {};\n  showMessageOptions: any = {};\n\n  // === TRANSFERT ===\n  showForwardModal = false;\n  forwardingMessage: any = null;\n  selectedConversations: string[] = [];\n  availableConversations: any[] = [];\n  isForwarding = false;\n  isLoadingConversations = false;\n\n  // === ÉMOJIS ET AUTOCOLLANTS ===\n  emojiCategories: any[] = [\n    { id: 'recent', name: 'Récents', icon: 'fas fa-clock' },\n    { id: 'smileys', name: 'Smileys', icon: 'fas fa-smile' },\n    { id: 'people', name: 'Personnes', icon: 'fas fa-user' },\n    { id: 'animals', name: 'Animaux', icon: 'fas fa-paw' },\n    { id: 'food', name: 'Nourriture', icon: 'fas fa-apple-alt' },\n    { id: 'travel', name: 'Voyage', icon: 'fas fa-plane' },\n    { id: 'activities', name: 'Activités', icon: 'fas fa-football-ball' },\n    { id: 'objects', name: 'Objets', icon: 'fas fa-lightbulb' },\n    { id: 'symbols', name: 'Symboles', icon: 'fas fa-heart' },\n    { id: 'flags', name: 'Drapeaux', icon: 'fas fa-flag' },\n  ];\n  selectedEmojiCategory = 'recent';\n  emojiSearchQuery = '';\n  recentEmojis: any[] = [];\n  previewedEmoji: any = null;\n  showStickerPicker = false;\n  stickerPacks: any[] = [];\n  selectedStickerPack = '';\n\n  // === GIFS ===\n  showGifPicker = false;\n  gifSearchQuery = '';\n  gifCategories = ['Trending', 'Reactions', 'Animals', 'Sports', 'TV & Movies'];\n  selectedGifCategory = 'Trending';\n\n  // === OUTIL DE DESSIN ===\n  showDrawingTool = false;\n  selectedDrawingTool = 'pen';\n  drawingColors = [\n    '#000000',\n    '#FF0000',\n    '#00FF00',\n    '#0000FF',\n    '#FFFF00',\n    '#FF00FF',\n    '#00FFFF',\n    '#FFFFFF',\n  ];\n  selectedDrawingColor = '#000000';\n  customDrawingColor = '#000000';\n  drawingSize = 5;\n\n  // === CAMÉRA ===\n  showCamera = false;\n  cameraMode = 'photo';\n  flashEnabled = false;\n  showCameraGrid = false;\n  showFocusIndicator = false;\n  focusX = 0;\n  focusY = 0;\n  lastCapturedImage = '';\n  isRecordingVideo = false;\n  videoRecordingDuration = 0;\n\n  // === ÉDITEUR D'IMAGES ===\n  showImageEditor = false;\n  imageEditorMode = 'crop';\n  cropArea = { x: 0, y: 0, width: 100, height: 100 };\n  cropRatio = 'free';\n  imageFilters: any[] = [\n    { name: 'none', label: 'Aucun', css: 'none' },\n    { name: 'grayscale', label: 'Noir & Blanc', css: 'grayscale(100%)' },\n    { name: 'sepia', label: 'Sépia', css: 'sepia(100%)' },\n    { name: 'vintage', label: 'Vintage', css: 'sepia(50%) contrast(1.2)' },\n    { name: 'bright', label: 'Lumineux', css: 'brightness(1.3)' },\n    { name: 'contrast', label: 'Contraste', css: 'contrast(1.5)' },\n  ];\n  selectedImageFilter = 'none';\n  imageAdjustments = { brightness: 0, contrast: 0, saturation: 0, hue: 0 };\n  imageTextElements: any[] = [];\n  newTextContent = '';\n  textFontFamily = 'Arial';\n  textFontSize = 24;\n  textColor = '#000000';\n  availableFonts = [\n    { value: 'Arial', label: 'Arial' },\n    { value: 'Helvetica', label: 'Helvetica' },\n    { value: 'Times New Roman', label: 'Times New Roman' },\n    { value: 'Courier New', label: 'Courier New' },\n  ];\n\n  // === GESTIONNAIRE DE FICHIERS ===\n  showFileManager = false;\n  fileViewMode = 'grid';\n  fileFolders: any[] = [];\n  selectedFolder: any = null;\n  fileBreadcrumbs: any[] = [];\n  fileSearchQuery = '';\n  fileTypeFilter = '';\n  fileSortBy = 'name';\n  selectedFiles: string[] = [];\n\n  // === ANALYTICS ===\n  showAnalyticsDashboard = false;\n  analyticsTimeRange = '7d';\n  analyticsData: any = {\n    totalMessages: 0,\n    activeUsers: 0,\n    avgResponseTime: '0s',\n    filesShared: 0,\n    messagesChange: 0,\n    usersChange: 0,\n    responseTimeChange: 0,\n    filesChange: 0,\n    topUsers: [],\n    messageTypes: [],\n    longestConversation: 0,\n    avgConversationDuration: '0m',\n    topEmojis: [],\n  };\n\n  // === INTÉGRATIONS ===\n  showIntegrations = false;\n  integrationCategories = [\n    'CRM',\n    'Productivité',\n    'Communication',\n    'Analytics',\n    'Sécurité',\n  ];\n  selectedIntegrationCategory = 'CRM';\n  webhooks: any[] = [];\n\n  // === ZONE DE SAISIE AVANCÉE ===\n  showQuickReplies = false;\n  quickReplies: string[] = ['Merci !', \"D'accord\", 'Parfait', 'À bientôt'];\n  showMentionSuggestions = false;\n  mentionSuggestions: any[] = [];\n  selectedMentionIndex = 0;\n  activeMentions: any[] = [];\n  showHashtagSuggestions = false;\n  hashtagSuggestions: any[] = [];\n  selectedHashtagIndex = 0;\n  activeHashtags: any[] = [];\n  showCommandSuggestions = false;\n  commandSuggestions: any[] = [];\n  selectedCommandIndex = 0;\n  activeLinks: any[] = [];\n  inputHeight = 40;\n  showFormattingToolbar = false;\n  detectedLanguage = 'fr';\n  autoCorrections: any[] = [];\n  showAutoCorrectSuggestions = false;\n  autoCorrectSuggestions: any[] = [];\n\n  // === TRADUCTION ===\n  showTranslationPanel = false;\n  translationFrom = 'auto';\n  translationTo = 'fr';\n  originalTextForTranslation = '';\n  translatedText = '';\n  isTranslating = false;\n  supportedLanguages = [\n    { code: 'fr', name: 'Français' },\n    { code: 'en', name: 'English' },\n    { code: 'es', name: 'Español' },\n    { code: 'de', name: 'Deutsch' },\n    { code: 'it', name: 'Italiano' },\n  ];\n\n  // === SONDAGES ===\n  showPollCreator = false;\n  pollQuestion = '';\n  pollOptions: any[] = [{ text: '' }, { text: '' }];\n  pollSettings = {\n    allowMultiple: false,\n    anonymous: false,\n    showResults: true,\n    showVoters: true,\n  };\n  pollExpiry = '';\n  customPollExpiry = '';\n\n  // === LOCALISATION ===\n  showLocationPicker = false;\n  showLocationSearch = false;\n  locationSearchQuery = '';\n  locationSearchResults: any[] = [];\n  selectedLocation: any = null;\n  showLocationMessage = false;\n  locationMessage = '';\n\n  // === CONTACTS ===\n  showContactPicker = false;\n  contactSearchQuery = '';\n  selectedContactForSharing: any = null;\n\n  // === PROGRAMMATION D'ENVOI ===\n  showScheduleMessage = false;\n  customScheduleDate = '';\n  customScheduleTime = '';\n  scheduleTimezone = 'Europe/Paris';\n  availableTimezones = [\n    { value: 'Europe/Paris', label: 'Paris (CET)' },\n    { value: 'America/New_York', label: 'New York (EST)' },\n    { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },\n  ];\n\n  // === ÉTATS DES MESSAGES ===\n  highlightedMessageId = '';\n  searchResultIds: string[] = [];\n  selectionMode = false;\n  selectedMessages: Set<string> = new Set();\n  hoveredMessageId = '';\n  playingVoiceId = '';\n  showScrollToBottom = false;\n  unreadMessagesCount = 0;\n\n  // === VISUALISEUR D'IMAGES ===\n  showImageViewer = false;\n  currentImageIndex = 0;\n  imageGallery: any[] = [];\n\n  // === TOAST ===\n  showToast = false;\n  toastMessage = '';\n  toastType = 'info';\n\n  // === PANNEAUX D'INFORMATIONS ===\n  showConversationInfo = false;\n  showConversationSettings = false;\n  conversationSettings = {\n    notifications: true,\n    soundNotifications: true,\n    readReceipts: true,\n    typingIndicators: true,\n  };\n\n  // === VARIABLES D'ÉTAT POUR LES NOUVELLES FONCTIONNALITÉS ===\n  isSendingMessage = false;\n  isUserTyping = false;\n  voiceRecordingState = 'idle';\n  voiceRecordingSize = 0;\n  recordingWaveform: number[] = [];\n  voiceRecordingQuality = 'medium';\n  voiceEffects: string[] = [];\n  selectedContacts: string[] = [];\n  isUpdatingStatus = false;\n\n  // === VARIABLES POUR LES PIÈCES JOINTES ===\n  showAttachmentMenu = false;\n  attachmentFiles: File[] = [];\n  fileCaptions: string[] = [];\n  fileUploadProgress: number[] = [];\n\n  // === PROPRIÉTÉS POUR LE TEMPLATE ===\n  messageContent = '';\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private authUserService: AuthuserService,\n    private MessageService: MessageService,\n    private toastService: ToastService,\n    private fb: FormBuilder,\n    private cdr: ChangeDetectorRef,\n    private userStatusService: UserStatusService\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.required, Validators.minLength(1)]],\n    });\n  }\n\n  // === CONSTANTES OPTIMISÉES ===\n  readonly c = {\n    reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],\n    delays: { away: 300000, typing: 3000, scroll: 100, anim: 300 },\n    error: 'Erreur. Veuillez réessayer.',\n    trackById: (i: number, item: any): string => item?.id || i.toString(),\n    notifications: {\n      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n    },\n    status: {\n      online: {\n        text: 'En ligne',\n        color: 'text-green-500',\n        icon: 'fas fa-circle',\n      },\n      offline: {\n        text: 'Hors ligne',\n        color: 'text-gray-500',\n        icon: 'far fa-circle',\n      },\n      away: { text: 'Absent', color: 'text-yellow-500', icon: 'fas fa-clock' },\n      busy: {\n        text: 'Occupé',\n        color: 'text-red-500',\n        icon: 'fas fa-minus-circle',\n      },\n    },\n    themes: [\n      { key: 'theme-default', label: 'Défaut', color: '#4f5fad' },\n      { key: 'theme-feminine', label: 'Rose', color: '#ff6b9d' },\n      { key: 'theme-masculine', label: 'Bleu', color: '#3d85c6' },\n      { key: 'theme-neutral', label: 'Vert', color: '#6aa84f' },\n    ],\n    calls: {\n      COMPLETED: 'text-green-500',\n      MISSED: 'text-red-500',\n      REJECTED: 'text-orange-500',\n    },\n  };\n\n  // === GETTERS ===\n  get availableReactions(): string[] {\n    return this.c.reactions;\n  }\n  get commonEmojis(): string[] {\n    return this.MessageService.getCommonEmojis();\n  }\n  get unreadNotificationsCount(): number {\n    return this.unreadNotificationCount;\n  }\n  get currentUserStatus(): string {\n    return 'online';\n  }\n  get onlineUsersCount(): number {\n    return 5;\n  }\n\n  // === LIFECYCLE HOOKS ===\n  ngOnInit(): void {\n    this.initializeComponent();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n    if (this.callTimer) clearInterval(this.callTimer);\n    if (this.typingTimeout) clearTimeout(this.typingTimeout);\n  }\n\n  ngAfterViewChecked(): void {\n    this.cdr.detectChanges();\n  }\n\n  // === INITIALISATION ===\n  private initializeComponent(): void {\n    this.loadCurrentUser();\n    this.loadConversation();\n    this.setupSubscriptions();\n    // Initialiser des données de test pour la démo\n    this.initializeTestData();\n  }\n\n  private loadConversation(): void {\n    this.route.params.subscribe((params) => {\n      const conversationId = params['id'];\n      if (conversationId) {\n        this.getConversation(conversationId);\n      }\n    });\n  }\n\n  private setupSubscriptions(): void {\n    // Configuration des abonnements WebSocket et autres\n  }\n\n  // === GESTION DES CONVERSATIONS ===\n  getConversation(conversationId: string): void {\n    this.loading = true;\n    this.MessageService.getConversation(\n      conversationId,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        this.conversation = conversation;\n        this.otherParticipant =\n          conversation.participants?.find((p) => p.id !== this.currentUserId) ||\n          null;\n\n        // Utiliser les messages de la conversation\n        if (conversation.messages) {\n          this.messages = conversation.messages;\n          this.hasMoreMessages =\n            conversation.messages.length === this.MAX_MESSAGES_TO_LOAD;\n        }\n\n        this.loading = false;\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        this.error = error;\n        this.loading = false;\n        console.error('Erreur lors du chargement de la conversation:', error);\n      },\n    });\n  }\n\n  // === GESTION DES MESSAGES ===\n\n  sendMessage(): void {\n    const content = this.messageForm.get('content')?.value?.trim();\n    if (!content || !this.conversation?.id) return;\n\n    this.isSendingMessage = true;\n\n    // Créer un message temporaire pour l'affichage immédiat\n    const tempMessage: Message = {\n      id: Date.now().toString(),\n      content,\n      sender: { id: this.currentUserId!, username: this.currentUsername },\n      timestamp: new Date(),\n      conversationId: this.conversation.id,\n      type: 'text' as MessageType,\n    };\n\n    this.messages.push(tempMessage);\n    this.messageForm.reset();\n    this.scrollToBottom();\n    this.isSendingMessage = false;\n\n    // Simuler l'envoi réel (à remplacer par l'appel API réel)\n    /*\n    this.MessageService.sendMessage(\n      this.conversation.id,\n      content,\n      'text' as MessageType,\n      this.currentUserId || ''\n    ).subscribe({\n      next: (message) => {\n        // Remplacer le message temporaire par le vrai message\n        const index = this.messages.findIndex(m => m.id === tempMessage.id);\n        if (index > -1) {\n          this.messages[index] = message;\n        }\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n        // Supprimer le message temporaire en cas d'erreur\n        this.messages = this.messages.filter(m => m.id !== tempMessage.id);\n      },\n    });\n    */\n  }\n\n  // === GESTION DES FICHIERS ===\n  onFileSelected(event: any): void {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.attachmentFiles = Array.from(files);\n      this.generatePreviews();\n    }\n  }\n\n  removeAttachment(index: number): void {\n    this.attachmentFiles.splice(index, 1);\n    if (this.attachmentFiles.length === 0) {\n      this.previewUrl = null;\n    }\n  }\n\n  // === GESTION DES RÉACTIONS ===\n  toggleReaction(messageId: string, emoji: string): void {\n    // Simulation de la réaction - à implémenter avec le vrai service\n    const message = this.messages.find((m) => m.id === messageId);\n    if (message) {\n      if (!(message as any).reactions) (message as any).reactions = [];\n      const existingReaction = (message as any).reactions.find(\n        (r: any) => r.emoji === emoji\n      );\n      if (existingReaction) {\n        existingReaction.count = (existingReaction.count || 0) + 1;\n      } else {\n        (message as any).reactions.push({\n          emoji,\n          count: 1,\n          users: [this.currentUserId],\n        });\n      }\n    }\n  }\n\n  addReaction(messageId: string, emoji: string): void {\n    this.toggleReaction(messageId, emoji);\n    this.showReactionPicker = {};\n  }\n\n  // === GESTION DE L'ÉDITION ===\n  startEditMessage(message: any): void {\n    this.editingMessageId = message.id;\n    this.editingContent = message.content || '';\n    this.showMessageOptions = {};\n  }\n\n  cancelEdit(): void {\n    this.editingMessageId = null;\n    this.editingContent = '';\n  }\n\n  saveEdit(messageId: string): void {\n    if (this.editingContent?.trim()) {\n      this.MessageService.editMessage(\n        messageId,\n        this.editingContent.trim()\n      ).subscribe({\n        next: () => {\n          this.cancelEdit();\n          // Recharger la conversation pour obtenir les messages mis à jour\n          if (this.conversation?.id) {\n            this.getConversation(this.conversation.id);\n          }\n        },\n        error: (error) => {\n          console.error('Erreur lors de la modification:', error);\n        },\n      });\n    }\n  }\n\n  onEditKeyDown(event: KeyboardEvent, messageId: string): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.saveEdit(messageId);\n    } else if (event.key === 'Escape') {\n      this.cancelEdit();\n    }\n  }\n\n  // === GESTION DE LA SUPPRESSION ===\n  deleteMessage(messageId: string): void {\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n      this.MessageService.deleteMessage(messageId).subscribe({\n        next: () => {\n          // Recharger la conversation pour obtenir les messages mis à jour\n          if (this.conversation?.id) {\n            this.getConversation(this.conversation.id);\n          }\n          this.showMessageOptions = {};\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression:', error);\n        },\n      });\n    }\n  }\n\n  // === GESTION DES APPELS ===\n  startCall(type: CallType): void {\n    if (!this.otherParticipant?.id) return;\n\n    this.MessageService.initiateCall(this.otherParticipant.id, type).subscribe({\n      next: (call) => {\n        this.activeCall = call;\n        this.showActiveCallModal = true;\n        this.startCallTimer();\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'initiation de l'appel:\", error);\n        this.toastService.showError(\"Impossible d'initier l'appel\");\n      },\n    });\n  }\n\n  private startCallTimer(): void {\n    this.callDuration = 0;\n    this.callTimer = setInterval(() => {\n      this.callDuration++;\n    }, 1000);\n  }\n\n  endCall(): void {\n    if (this.callTimer) {\n      clearInterval(this.callTimer);\n      this.callTimer = null;\n    }\n    this.activeCall = null;\n    this.showActiveCallModal = false;\n    this.callDuration = 0;\n  }\n\n  toggleMute(): void {\n    this.isCallMuted = !this.isCallMuted;\n  }\n\n  toggleVideo(): void {\n    this.isVideoEnabled = !this.isVideoEnabled;\n  }\n\n  // === MÉTHODES UTILITAIRES ===\n  scrollToBottom(): void {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 100);\n  }\n\n  formatMessageTime(timestamp: any): string {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  }\n\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  trackByMessageId(index: number, message: any): string {\n    return message?.id || index.toString();\n  }\n\n  // === GESTION DE L'INTERFACE ===\n  toggleSearch(): void {\n    this.showSearch = !this.showSearch;\n    if (!this.showSearch) {\n      this.clearSearch();\n    }\n  }\n\n  toggleNotifications(): void {\n    this.showNotificationPanel = !this.showNotificationPanel;\n  }\n\n  toggleConversationInfo(): void {\n    this.showConversationInfo = !this.showConversationInfo;\n  }\n\n  toggleThemeSelector(): void {\n    this.showThemeSelector = !this.showThemeSelector;\n  }\n\n  toggleMainMenu(): void {\n    this.showMainMenu = !this.showMainMenu;\n  }\n\n  toggleAdvancedSearch(): void {\n    this.showAdvancedSearch = !this.showAdvancedSearch;\n  }\n\n  toggleStatusSelector(): void {\n    this.showStatusSelector = !this.showStatusSelector;\n  }\n\n  toggleUserStatusPanel(): void {\n    this.showUserStatusPanel = !this.showUserStatusPanel;\n  }\n\n  // === GESTION DE LA RECHERCHE ===\n\n  private performSearch(): void {\n    this.isSearching = true;\n    // Simulation de recherche\n    setTimeout(() => {\n      this.searchResults = this.messages.filter((message) =>\n        message.content?.toLowerCase().includes(this.searchQuery.toLowerCase())\n      );\n      this.isSearching = false;\n    }, 500);\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchMode = false;\n    this.searchResults = [];\n    this.searchResultIds = [];\n  }\n\n  highlightSearchTerm(content: string): string {\n    if (!this.searchQuery || !content) return content;\n    const regex = new RegExp(`(${this.searchQuery})`, 'gi');\n    return content.replace(\n      regex,\n      '<mark class=\"whatsapp-search-highlight\">$1</mark>'\n    );\n  }\n\n  scrollToMessage(messageId: string): void {\n    this.highlightedMessageId = messageId;\n    // Logique pour faire défiler vers le message\n    setTimeout(() => {\n      this.highlightedMessageId = '';\n    }, 3000);\n  }\n\n  // === GESTION DES MESSAGES ÉPINGLÉS ===\n  showAllPinnedMessages(): void {\n    // Afficher tous les messages épinglés\n  }\n\n  closePinnedMessages(): void {\n    this.pinnedMessages = [];\n  }\n\n  togglePinMessage(messageId: string): void {\n    const message = this.messages.find((m) => m.id === messageId);\n    if (message) {\n      message.pinned = !message.pinned;\n      if (message.pinned) {\n        this.pinnedMessages.push(message);\n      } else {\n        this.pinnedMessages = this.pinnedMessages.filter(\n          (m) => m.id !== messageId\n        );\n      }\n    }\n    this.showMessageOptions = {};\n  }\n\n  // === GESTION DES OPTIONS DE MESSAGE ===\n  toggleMessageOptions(messageId: string): void {\n    if (this.showMessageOptions[messageId]) {\n      this.showMessageOptions = {};\n    } else {\n      this.showMessageOptions = { [messageId]: true };\n    }\n  }\n\n  toggleReactionPicker(messageId: string): void {\n    if (this.showReactionPicker[messageId]) {\n      this.showReactionPicker = {};\n    } else {\n      this.showReactionPicker = { [messageId]: true };\n    }\n  }\n\n  // === GESTION DES ACTIONS DE MESSAGE ===\n  replyToMessage(message: any): void {\n    this.replyingToMessage = message;\n    this.showMessageOptions = {};\n  }\n\n  forwardMessage(message: any): void {\n    this.forwardingMessage = message;\n    this.showForwardModal = true;\n    this.showMessageOptions = {};\n  }\n\n  copyMessage(message: any): void {\n    if (navigator.clipboard && message.content) {\n      navigator.clipboard.writeText(message.content);\n      this.toastService.showSuccess('Message copié');\n    }\n    this.showMessageOptions = {};\n  }\n\n  selectMessage(messageId: string): void {\n    this.selectionMode = true;\n    this.toggleMessageSelection(messageId);\n    this.showMessageOptions = {};\n  }\n\n  toggleMessageSelection(messageId: string): void {\n    if (this.selectedMessages.has(messageId)) {\n      this.selectedMessages.delete(messageId);\n    } else {\n      this.selectedMessages.add(messageId);\n    }\n\n    if (this.selectedMessages.size === 0) {\n      this.selectionMode = false;\n    }\n  }\n\n  private _selectedMessages: string[] = [];\n\n  // === GESTION DES ÉMOJIS ===\n  openEmojiPicker(messageId?: string): void {\n    this.showEmojiPicker = true;\n    if (messageId) {\n      this.showReactionPicker = { [messageId]: true };\n    }\n  }\n\n  closeEmojiPicker(): void {\n    this.showEmojiPicker = false;\n    this.showReactionPicker = {};\n  }\n\n  selectEmojiCategory(categoryId: string): void {\n    this.selectedEmojiCategory = categoryId;\n  }\n\n  addEmojiToMessage(emoji: string): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    this.messageForm.get('content')?.setValue(currentContent + emoji);\n  }\n\n  // === GESTION DES PIÈCES JOINTES ===\n  toggleAttachmentMenu(): void {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n\n  openFileInput(): void {\n    this.fileInput.nativeElement.click();\n  }\n\n  // === GESTION DES MÉDIAS ===\n  openImageViewer(message: any): void {\n    this.showImageViewer = true;\n    this.currentImageIndex = 0;\n    this.imageGallery = [message];\n  }\n\n  closeImageViewer(): void {\n    this.showImageViewer = false;\n    this.imageGallery = [];\n  }\n\n  // === GESTION DES MESSAGES VOCAUX ===\n  toggleVoicePlayback(messageId: string): void {\n    if (this.playingVoiceId === messageId) {\n      this.playingVoiceId = '';\n    } else {\n      this.playingVoiceId = messageId;\n    }\n  }\n\n  // === ENREGISTREMENT VOCAL COMPLET ===\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioChunks: Blob[] = [];\n  private recordingTimer: any = null;\n\n  async startVoiceRecording(): Promise<void> {\n    try {\n      // Demander permission pour le microphone\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n        },\n      });\n\n      this.mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus',\n      });\n\n      this.audioChunks = [];\n      this.isRecordingVoice = true;\n      this.voiceRecordingDuration = 0;\n      this.voiceRecordingState = 'recording';\n\n      // Démarrer le timer\n      this.recordingTimer = setInterval(() => {\n        this.voiceRecordingDuration++;\n        this.cdr.detectChanges();\n      }, 1000);\n\n      // Gérer les données audio\n      this.mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          this.audioChunks.push(event.data);\n        }\n      };\n\n      // Gérer la fin d'enregistrement\n      this.mediaRecorder.onstop = () => {\n        this.processRecordedAudio();\n      };\n\n      // Démarrer l'enregistrement\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\n\n      this.toastService.showSuccess('Enregistrement vocal démarré');\n    } catch (error) {\n      console.error(\"Erreur lors du démarrage de l'enregistrement:\", error);\n      this.toastService.showError(\"Impossible d'accéder au microphone\");\n      this.cancelVoiceRecording();\n    }\n  }\n\n  stopVoiceRecording(): void {\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n      this.mediaRecorder.stop();\n\n      // Arrêter le stream\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingState = 'processing';\n  }\n\n  cancelVoiceRecording(): void {\n    if (this.mediaRecorder) {\n      if (this.mediaRecorder.state === 'recording') {\n        this.mediaRecorder.stop();\n      }\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\n      this.mediaRecorder = null;\n    }\n\n    if (this.recordingTimer) {\n      clearInterval(this.recordingTimer);\n      this.recordingTimer = null;\n    }\n\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n    this.audioChunks = [];\n  }\n\n  private async processRecordedAudio(): Promise<void> {\n    try {\n      if (this.audioChunks.length === 0) {\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      // Créer le blob audio\n      const audioBlob = new Blob(this.audioChunks, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      // Vérifier la durée minimale (1 seconde)\n      if (this.voiceRecordingDuration < 1) {\n        this.toastService.showWarning(\n          'Enregistrement trop court (minimum 1 seconde)'\n        );\n        this.cancelVoiceRecording();\n        return;\n      }\n\n      // Créer un fichier\n      const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {\n        type: 'audio/webm;codecs=opus',\n      });\n\n      // Envoyer le message vocal\n      await this.sendVoiceMessage(audioFile);\n\n      this.toastService.showSuccess('Message vocal envoyé');\n    } catch (error) {\n      console.error(\"Erreur lors du traitement de l'audio:\", error);\n      this.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\n    } finally {\n      this.voiceRecordingState = 'idle';\n      this.voiceRecordingDuration = 0;\n      this.audioChunks = [];\n    }\n  }\n\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\n    try {\n      if (!this.conversation?.id) {\n        throw new Error('Aucune conversation sélectionnée');\n      }\n\n      // Upload du fichier audio\n      const formData = new FormData();\n      formData.append('file', audioFile);\n      formData.append('type', 'voice');\n      formData.append('duration', this.voiceRecordingDuration.toString());\n\n      // Envoyer via le service de messages\n      this.MessageService.sendVoiceMessage(\n        this.conversation.id,\n        audioFile,\n        this.voiceRecordingDuration.toString()\n      ).subscribe({\n        next: (message) => {\n          // Ajouter le message à la liste\n          this.messages.push(message);\n          this.scrollToBottom();\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\n          throw error;\n        },\n      });\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // === GESTION DES THÈMES ===\n  changeTheme(theme: string): void {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n  }\n\n  getThemeOptions(): any[] {\n    return this.c.themes;\n  }\n\n  // === GESTION DU STATUT ===\n  updateUserStatus(status: string): void {\n    this.isUpdatingStatus = true;\n    // Simuler une mise à jour\n    setTimeout(() => {\n      this.isUpdatingStatus = false;\n    }, 1000);\n  }\n\n  getStatusOptions(): any[] {\n    return Object.entries(this.c.status).map(([key, value]) => ({\n      key,\n      ...value,\n    }));\n  }\n\n  getStatusIcon(status: string): string {\n    return (\n      this.c.status[status as keyof typeof this.c.status]?.icon ||\n      'fas fa-circle'\n    );\n  }\n\n  getStatusColor(status: string): string {\n    return (\n      this.c.status[status as keyof typeof this.c.status]?.color ||\n      'text-gray-500'\n    );\n  }\n\n  getStatusText(status: string): string {\n    return (\n      this.c.status[status as keyof typeof this.c.status]?.text || 'Inconnu'\n    );\n  }\n\n  // === MÉTHODES POUR LE TEMPLATE ===\n  formatMessageContent(content: string | undefined): string {\n    if (!content) return '';\n    return content\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n      .replace(/`(.*?)`/g, '<code>$1</code>')\n      .replace(/\\n/g, '<br>');\n  }\n\n  onImageLoad(event: any): void {\n    // Gérer le chargement d'image\n  }\n\n  onImageError(event: any): void {\n    event.target.src = 'assets/images/image-error.png';\n  }\n\n  onVideoLoaded(event: any, message?: any): void {\n    // Gérer le chargement vidéo\n  }\n\n  // === MÉTHODES POUR LES ANALYTICS ===\n  openAnalyticsDashboard(): void {\n    this.showAnalyticsDashboard = true;\n  }\n\n  closeAnalyticsDashboard(): void {\n    this.showAnalyticsDashboard = false;\n  }\n\n  // === MÉTHODES POUR LES INTÉGRATIONS ===\n  openIntegrations(): void {\n    this.showIntegrations = true;\n  }\n\n  closeIntegrations(): void {\n    this.showIntegrations = false;\n  }\n\n  // === MÉTHODES POUR LES RÉPONSES RAPIDES ===\n  sendQuickMessage(content: string): void {\n    const control = this.messageForm.get('content');\n    if (control) {\n      control.setValue(content);\n      this.sendMessage();\n    }\n  }\n\n  // === MÉTHODES POUR LA COMPATIBILITÉ ===\n  initiateCall(type: CallType): void {\n    this.startCall(type);\n  }\n\n  // === CORRECTION DES MÉTHODES MANQUANTES ===\n  private generatePreviews(): void {\n    this.attachmentFiles.forEach((file) => {\n      if (file.type.startsWith('image/')) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          this.previewUrl = e.target?.result || null;\n        };\n        reader.readAsDataURL(file);\n      }\n    });\n  }\n\n  private loadCurrentUser(): void {\n    try {\n      // Utiliser getCurrentUser de manière synchrone\n      const user = this.authUserService.getCurrentUser();\n      if (user) {\n        this.currentUserId = user.id || null;\n        this.currentUsername = user.username || 'You';\n      } else {\n        // Fallback si pas d'utilisateur connecté\n        this.currentUserId = null;\n        this.currentUsername = 'You';\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement de l'utilisateur:\", error);\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n    }\n  }\n\n  // === MÉTHODES SUPPLÉMENTAIRES POUR LE TEMPLATE ===\n\n  // Méthodes pour les types de messages\n  getMessageType(message: any): string {\n    if (message.content && !message.attachments?.length) return 'text';\n    if (message.attachments?.some((att: any) => att.type === 'image'))\n      return 'image';\n    if (message.attachments?.some((att: any) => att.type === 'voice'))\n      return 'voice';\n    if (message.attachments?.some((att: any) => att.type === 'file'))\n      return 'file';\n    if (message.attachments?.some((att: any) => att.type === 'video'))\n      return 'video';\n    if (message.type === 'location') return 'location';\n    if (message.type === 'contact') return 'contact';\n    return 'text';\n  }\n\n  // Méthodes pour les médias\n  getImageUrl(message: any): string {\n    return (\n      message.imageUrl ||\n      message.attachments?.find((att: any) => att.type === 'image')?.url ||\n      ''\n    );\n  }\n\n  getVideoUrl(message: any): string {\n    return (\n      message.videoUrl ||\n      message.attachments?.find((att: any) => att.type === 'video')?.url ||\n      ''\n    );\n  }\n\n  getVideoThumbnail(message: any): string {\n    return message.thumbnailUrl || '';\n  }\n\n  getVideoDuration(message: any): string {\n    return message.duration || '00:00';\n  }\n\n  // Méthodes pour les fichiers\n  getFileSize(message: any): string {\n    return this.formatFileSize(message.size || 0);\n  }\n\n  getFileIcon(message: any): string {\n    const fileType = message.fileType || message.attachments?.[0]?.type || '';\n    const icons: any = {\n      'application/pdf': 'fas fa-file-pdf',\n      'application/msword': 'fas fa-file-word',\n      'application/vnd.ms-excel': 'fas fa-file-excel',\n      'application/vnd.ms-powerpoint': 'fas fa-file-powerpoint',\n      'text/': 'fas fa-file-alt',\n      'image/': 'fas fa-file-image',\n      'video/': 'fas fa-file-video',\n      'audio/': 'fas fa-file-audio',\n    };\n\n    for (const [type, icon] of Object.entries(icons)) {\n      if (fileType.startsWith(type)) return icon as string;\n    }\n    return 'fas fa-file';\n  }\n\n  getFileType(message: any): string {\n    const fileType = message.fileType || message.attachments?.[0]?.type || '';\n    return fileType.split('/')[1]?.toUpperCase() || 'FICHIER';\n  }\n\n  // Méthodes pour les messages vocaux\n  formatVoiceDuration(current: number, total: number): string {\n    const formatTime = (seconds: number) => {\n      const mins = Math.floor(seconds / 60);\n      const secs = seconds % 60;\n      return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return `${formatTime(current)} / ${formatTime(total)}`;\n  }\n\n  getVoiceWaveform(message: any): any[] {\n    return Array.from({ length: 20 }, (_, i) => ({\n      height: Math.random() * 20 + 5,\n      active: false,\n      played: false,\n    }));\n  }\n\n  getVoiceProgress(messageId: string): number {\n    return 0;\n  }\n\n  getVoiceCurrentTime(messageId: string): number {\n    return 0;\n  }\n\n  getVoiceTotalDuration(messageId: string): number {\n    return 0;\n  }\n\n  getVoiceSpeed(messageId: string): string {\n    return '1x';\n  }\n\n  // Méthodes pour les états\n  isImageLoading(message: any): boolean {\n    return false;\n  }\n\n  isVoiceLoading(messageId: string): boolean {\n    return false;\n  }\n\n  isFileDownloading(message: any): boolean {\n    return false;\n  }\n\n  getImageLoadingProgress(message: any): number {\n    return 0;\n  }\n\n  getFileDownloadProgress(message: any): number {\n    return 0;\n  }\n\n  getImageDimensions(message: any): string {\n    return '';\n  }\n\n  // Méthodes pour les actions\n  downloadFile(message: any): void {\n    // Télécharger le fichier\n  }\n\n  previewFile(message: any): void {\n    // Prévisualiser le fichier\n  }\n\n  canPreviewFile(message: any): boolean {\n    const previewableTypes = [\n      'image/',\n      'video/',\n      'audio/',\n      'text/',\n      'application/pdf',\n    ];\n    return previewableTypes.some((type) => message.type?.startsWith(type));\n  }\n\n  hasVideo(message: any): boolean {\n    return (\n      message.type === 'video' ||\n      (message.attachments &&\n        message.attachments.some((att: any) => att.type === 'video'))\n    );\n  }\n\n  getEmojiName(emoji: string): string {\n    const emojiNames: any = {\n      '👍': 'Pouce levé',\n      '❤️': 'Cœur',\n      '😂': 'Rire',\n      '😮': 'Surprise',\n      '😢': 'Triste',\n      '😡': 'Colère',\n    };\n    return emojiNames[emoji] || emoji;\n  }\n\n  // Méthodes pour la localisation\n  openLocationViewer(message: any): void {\n    // Ouvrir le visualiseur de localisation\n  }\n\n  getLocationMapUrl(message: any): string {\n    return message.mapUrl || '';\n  }\n\n  getLocationName(message: any): string {\n    return message.locationName || 'Localisation';\n  }\n\n  getLocationAddress(message: any): string {\n    return message.address || '';\n  }\n\n  // Méthodes pour les contacts\n  openContactViewer(message: any): void {\n    // Ouvrir le visualiseur de contact\n  }\n\n  getContactAvatar(message: any): string {\n    return message.contactAvatar || 'assets/images/default-avatar.png';\n  }\n\n  getContactName(message: any): string {\n    return message.contactName || 'Contact';\n  }\n\n  getContactPhone(message: any): string {\n    return message.contactPhone || '';\n  }\n\n  // Méthodes pour les lecteurs\n  openVideoPlayer(message: any): void {\n    // Ouvrir le lecteur vidéo\n  }\n\n  seekVoiceMessage(messageId: string, event: any): void {\n    // Implémentation de la recherche dans le message vocal\n  }\n\n  changeVoiceSpeed(messageId: string): void {\n    // Changer la vitesse de lecture\n  }\n\n  // Méthodes pour les événements\n  onScroll(event: any): void {\n    // Gérer le défilement\n    const element = event.target;\n    this.showScrollToBottom =\n      element.scrollTop < element.scrollHeight - element.clientHeight - 100;\n  }\n\n  loadMoreMessages(): void {\n    if (!this.conversation?.id || !this.hasMoreMessages) return;\n\n    this.isLoadingMore = true;\n    this.currentPage++;\n\n    // Charger plus de messages en utilisant getConversation avec pagination\n    this.MessageService.getConversation(\n      this.conversation.id,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        if (conversation.messages && conversation.messages.length > 0) {\n          // Ajouter les nouveaux messages au début de la liste (messages plus anciens)\n          this.messages = [...conversation.messages, ...this.messages];\n          this.hasMoreMessages =\n            conversation.messages.length === this.MAX_MESSAGES_TO_LOAD;\n        } else {\n          this.hasMoreMessages = false;\n        }\n        this.isLoadingMore = false;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement de plus de messages:', error);\n        this.isLoadingMore = false;\n      },\n    });\n  }\n\n  clearConversation(): void {\n    if (confirm('Êtes-vous sûr de vouloir vider cette conversation ?')) {\n      this.messages = [];\n    }\n  }\n\n  exportConversation(): void {\n    // Exporter la conversation\n  }\n\n  // Méthodes pour les en-têtes\n  getHeaderActions(): any[] {\n    return [\n      {\n        icon: 'fas fa-search',\n        title: 'Rechercher',\n        onClick: () => this.toggleSearch(),\n        class: 'search-btn',\n        isActive: this.showSearch,\n      },\n      {\n        icon: 'fas fa-bell',\n        title: 'Notifications',\n        onClick: () => this.toggleNotifications(),\n        class: 'notification-btn',\n        badge:\n          this.unreadNotificationsCount > 0\n            ? {\n                count: this.unreadNotificationsCount,\n                class: 'bg-red-500',\n                animate: true,\n              }\n            : null,\n      },\n      {\n        icon: 'fas fa-phone',\n        title: 'Appel audio',\n        onClick: () => this.startCall('AUDIO' as CallType),\n        class: 'call-btn',\n      },\n      {\n        icon: 'fas fa-video',\n        title: 'Appel vidéo',\n        onClick: () => this.startCall('VIDEO' as CallType),\n        class: 'video-btn',\n      },\n    ];\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\n\n  goBackToConversations(): void {\n    this.router.navigate(['/front/messages']);\n  }\n\n  openUserProfile(userId: string): void {\n    // Ouvrir le profil utilisateur\n  }\n\n  formatLastActive(lastActive: any): string {\n    if (!lastActive) return 'Hors ligne';\n    const date = new Date(lastActive);\n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const minutes = Math.floor(diff / 60000);\n\n    if (minutes < 1) return 'En ligne';\n    if (minutes < 60) return `Il y a ${minutes} min`;\n    if (minutes < 1440) return `Il y a ${Math.floor(minutes / 60)} h`;\n    return `Il y a ${Math.floor(minutes / 1440)} j`;\n  }\n\n  toggleConversationSettings(): void {\n    this.showConversationSettings = !this.showConversationSettings;\n  }\n\n  shouldShowDateSeparator(index: number): boolean {\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\n\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\n\n    return currentDate !== previousDate;\n  }\n\n  formatDateSeparator(timestamp: any): string {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) return \"Aujourd'hui\";\n    if (date.toDateString() === yesterday.toDateString()) return 'Hier';\n\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  }\n\n  getSystemMessageIcon(message: any): string {\n    const icons: any = {\n      user_joined: 'fas fa-user-plus',\n      user_left: 'fas fa-user-minus',\n      call_started: 'fas fa-phone',\n      call_ended: 'fas fa-phone-slash',\n      message_deleted: 'fas fa-trash',\n    };\n    return icons[message.systemType] || 'fas fa-info-circle';\n  }\n\n  onMessageClick(message: any, event: any): void {\n    if (this.selectionMode) {\n      this.toggleMessageSelection(message.id);\n    }\n  }\n\n  onMessageContextMenu(message: any, event: any): void {\n    event.preventDefault();\n    this.toggleMessageOptions(message.id);\n  }\n\n  onMessageHover(messageId: string, isHovering: boolean): void {\n    this.hoveredMessageId = isHovering ? messageId : '';\n  }\n\n  shouldShowAvatar(index: number): boolean {\n    if (index === this.messages.length - 1) return true;\n    const currentMessage = this.messages[index];\n    const nextMessage = this.messages[index + 1];\n    return currentMessage?.sender?.id !== nextMessage?.sender?.id;\n  }\n\n  onAvatarError(event: any): void {\n    event.target.src = 'assets/images/default-avatar.png';\n  }\n\n  isGroupConversation(): boolean {\n    return (this.conversation as any)?.type === 'group';\n  }\n\n  shouldShowSenderName(index: number): boolean {\n    if (!this.isGroupConversation()) return false;\n    if (index === 0) return true;\n    const currentMessage = this.messages[index];\n    const previousMessage = this.messages[index - 1];\n    return currentMessage?.sender?.id !== previousMessage?.sender?.id;\n  }\n\n  getUserColor(userId: string): string {\n    const colors = [\n      '#ff6b6b',\n      '#4ecdc4',\n      '#45b7d1',\n      '#96ceb4',\n      '#feca57',\n      '#ff9ff3',\n    ];\n    const index = userId.charCodeAt(0) % colors.length;\n    return colors[index];\n  }\n\n  // Propriété Math pour le template\n  get Math(): typeof Math {\n    return Math;\n  }\n\n  // Méthodes pour les fichiers\n  downloadSelectedFiles(): void {\n    // Télécharger les fichiers sélectionnés\n  }\n\n  shareSelectedFiles(): void {\n    // Partager les fichiers sélectionnés\n  }\n\n  deleteSelectedFiles(): void {\n    // Supprimer les fichiers sélectionnés\n  }\n\n  // Méthodes pour les analytics\n  updateAnalytics(): void {\n    // Mettre à jour les analytics\n  }\n\n  exportAnalytics(): void {\n    // Exporter les analytics\n  }\n\n  getMessageTypeIcon(type: string): string {\n    const icons: any = {\n      text: 'fas fa-comment',\n      image: 'fas fa-image',\n      video: 'fas fa-video',\n      voice: 'fas fa-microphone',\n      file: 'fas fa-file',\n      location: 'fas fa-map-marker-alt',\n      contact: 'fas fa-user',\n    };\n    return icons[type] || 'fas fa-comment';\n  }\n\n  // Méthodes pour les intégrations\n  createNewIntegration(): void {\n    // Créer une nouvelle intégration\n  }\n\n  selectIntegrationCategory(category: string): void {\n    this.selectedIntegrationCategory = category;\n  }\n\n  getIntegrationCategoryIcon(category: string): string {\n    const icons: any = {\n      CRM: 'fas fa-users',\n      Productivité: 'fas fa-tasks',\n      Communication: 'fas fa-comments',\n      Analytics: 'fas fa-chart-bar',\n      Sécurité: 'fas fa-shield-alt',\n    };\n    return icons[category] || 'fas fa-puzzle-piece';\n  }\n\n  getAvailableIntegrations(): any[] {\n    return [\n      { id: 1, name: 'Slack', category: 'Communication', icon: 'fab fa-slack' },\n      {\n        id: 2,\n        name: 'Trello',\n        category: 'Productivité',\n        icon: 'fab fa-trello',\n      },\n      {\n        id: 3,\n        name: 'Google Analytics',\n        category: 'Analytics',\n        icon: 'fab fa-google',\n      },\n    ];\n  }\n\n  configureIntegration(integration: any): void {\n    // Configurer l'intégration\n  }\n\n  toggleIntegration(integration: any): void {\n    // Basculer l'intégration\n  }\n\n  getActiveIntegrations(): any[] {\n    return this.getAvailableIntegrations().filter((i) => i.active);\n  }\n\n  getIntegrationStatusText(status: string): string {\n    const texts: any = {\n      active: 'Actif',\n      inactive: 'Inactif',\n      error: 'Erreur',\n      pending: 'En attente',\n    };\n    return texts[status] || 'Inconnu';\n  }\n\n  formatLastActivity(lastActivity: any): string {\n    return this.formatLastActive(lastActivity);\n  }\n\n  viewIntegrationLogs(integration: any): void {\n    // Voir les logs d'intégration\n  }\n\n  editIntegration(integration: any): void {\n    // Modifier l'intégration\n  }\n\n  testIntegration(integration: any): void {\n    // Tester l'intégration\n  }\n\n  removeIntegration(integration: any): void {\n    // Supprimer l'intégration\n  }\n\n  // Méthodes pour les webhooks\n  editWebhook(webhook: any): void {\n    // Modifier le webhook\n  }\n\n  testWebhook(webhook: any): void {\n    // Tester le webhook\n  }\n\n  deleteWebhook(webhook: any): void {\n    // Supprimer le webhook\n  }\n\n  createWebhook(): void {\n    // Créer un webhook\n  }\n\n  // Méthodes pour les modales\n  hasActiveModal(): boolean {\n    return (\n      this.showImageViewer ||\n      this.showForwardModal ||\n      this.showAnalyticsDashboard ||\n      this.showIntegrations ||\n      this.showEmojiPicker ||\n      this.showCamera ||\n      this.showDrawingTool ||\n      this.showFileManager\n    );\n  }\n\n  closeActiveModal(): void {\n    this.showImageViewer = false;\n    this.showForwardModal = false;\n    this.showAnalyticsDashboard = false;\n    this.showIntegrations = false;\n    this.showEmojiPicker = false;\n    this.showCamera = false;\n    this.showDrawingTool = false;\n    this.showFileManager = false;\n  }\n\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE HTML ===\n\n  // Méthodes pour l'éditeur d'images\n  flipImage(direction: string): void {\n    console.log('Flip image:', direction);\n  }\n\n  applyImageFilter(filterName: string): void {\n    this.selectedImageFilter = filterName;\n  }\n\n  updateImageAdjustments(): void {\n    // Mettre à jour les ajustements d'image\n  }\n\n  resetImageAdjustments(): void {\n    this.imageAdjustments = {\n      brightness: 0,\n      contrast: 0,\n      saturation: 0,\n      hue: 0,\n    };\n  }\n\n  addTextToImage(): void {\n    if (this.newTextContent.trim()) {\n      this.imageTextElements.push({\n        id: Date.now().toString(),\n        content: this.newTextContent,\n        x: 50,\n        y: 50,\n        fontFamily: this.textFontFamily,\n        fontSize: this.textFontSize,\n        color: this.textColor,\n      });\n      this.newTextContent = '';\n    }\n  }\n\n  // Méthodes pour le gestionnaire de fichiers\n  createNewFolder(): void {\n    const folderName = prompt('Nom du nouveau dossier:');\n    if (folderName) {\n      this.fileFolders.push({\n        id: Date.now().toString(),\n        name: folderName,\n        type: 'folder',\n        size: 0,\n        modifiedAt: new Date(),\n      });\n    }\n  }\n\n  uploadFiles(): void {\n    this.openFileInput();\n  }\n\n  toggleFileView(): void {\n    this.fileViewMode = this.fileViewMode === 'grid' ? 'list' : 'grid';\n  }\n\n  closeFileManager(): void {\n    this.showFileManager = false;\n  }\n\n  selectFolder(folder: any): void {\n    this.selectedFolder = folder;\n  }\n\n  renameFolder(folder: any): void {\n    const newName = prompt('Nouveau nom:', folder.name);\n    if (newName) {\n      folder.name = newName;\n    }\n  }\n\n  deleteFolder(folder: any): void {\n    if (confirm('Supprimer ce dossier ?')) {\n      this.fileFolders = this.fileFolders.filter((f) => f.id !== folder.id);\n    }\n  }\n\n  getStorageUsagePercentage(): number {\n    return 65; // Exemple\n  }\n\n  getStorageUsed(): string {\n    return '6.5 GB';\n  }\n\n  getStorageTotal(): string {\n    return '10 GB';\n  }\n\n  getTotalFilesCount(): number {\n    return 1247;\n  }\n\n  navigateToFolder(crumb: any): void {\n    this.selectedFolder = crumb;\n  }\n\n  onFileSearch(): void {\n    // Rechercher dans les fichiers\n  }\n\n  applyFileFilters(): void {\n    // Appliquer les filtres de fichiers\n  }\n\n  sortFiles(): void {\n    // Trier les fichiers\n  }\n\n  getFilteredFiles(): any[] {\n    return [\n      {\n        id: '1',\n        name: 'Document.pdf',\n        type: 'file',\n        size: 1024000,\n        modifiedAt: new Date(),\n      },\n      {\n        id: '2',\n        name: 'Image.jpg',\n        type: 'image',\n        size: 2048000,\n        modifiedAt: new Date(),\n      },\n    ];\n  }\n\n  toggleFileSelection(file: any): void {\n    const index = this.selectedFiles.indexOf(file.id);\n    if (index > -1) {\n      this.selectedFiles.splice(index, 1);\n    } else {\n      this.selectedFiles.push(file.id);\n    }\n  }\n\n  openFile(file: any): void {\n    console.log('Ouvrir fichier:', file.name);\n  }\n\n  formatFileDate(date: any): string {\n    return new Date(date).toLocaleDateString('fr-FR');\n  }\n\n  shareFile(file: any): void {\n    console.log('Partager fichier:', file.name);\n  }\n\n  deleteFile(file: any): void {\n    if (confirm('Supprimer ce fichier ?')) {\n      console.log('Fichier supprimé:', file.name);\n    }\n  }\n\n  // Méthodes pour les propriétés manquantes dans le template\n  get forwarded(): boolean {\n    return false; // Propriété pour les messages transférés\n  }\n\n  // Méthodes pour corriger les erreurs du template\n  getOnlineUsersCount(): number {\n    return this.onlineUsersCount;\n  }\n\n  // Méthodes manquantes pour le template\n  truncateText(text: string, maxLength: number): string {\n    return text.length > maxLength\n      ? text.substring(0, maxLength) + '...'\n      : text;\n  }\n\n  hasImage(message: any): boolean {\n    return (\n      message.type === 'image' ||\n      (message.attachments &&\n        message.attachments.some((a: any) => a.type.startsWith('image/')))\n    );\n  }\n\n  isVoiceMessage(message: any): boolean {\n    return message.type === 'voice' || message.type === 'audio';\n  }\n\n  hasFile(message: any): boolean {\n    return (\n      message.type === 'file' ||\n      (message.attachments && message.attachments.length > 0)\n    );\n  }\n\n  getFileName(message: any): string {\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments[0].name || 'Fichier';\n    }\n    return 'Fichier';\n  }\n\n  getMessageBubbleClass(message: any): string {\n    const isOwn = message.sender?.id === this.currentUserId;\n    return `whatsapp-message-bubble ${isOwn ? 'own' : 'other'}`;\n  }\n\n  isLocationMessage(message: any): boolean {\n    return message.type === 'location';\n  }\n\n  isContactMessage(message: any): boolean {\n    return message.type === 'contact';\n  }\n\n  // Méthodes pour la caméra\n  closeCamera(): void {\n    this.showCamera = false;\n  }\n\n  setCameraMode(mode: string): void {\n    this.cameraMode = mode;\n  }\n\n  toggleCameraFlash(): void {\n    this.flashEnabled = !this.flashEnabled;\n  }\n\n  switchCamera(): void {\n    // Basculer entre caméra avant et arrière\n  }\n\n  openGallery(): void {\n    this.openFileInput();\n  }\n\n  capturePhoto(): void {\n    // Capturer une photo\n  }\n\n  startVideoRecording(): void {\n    this.isRecordingVideo = true;\n    this.videoRecordingDuration = 0;\n  }\n\n  stopVideoRecording(): void {\n    this.isRecordingVideo = false;\n  }\n\n  toggleCameraGrid(): void {\n    this.showCameraGrid = !this.showCameraGrid;\n  }\n\n  formatRecordingTime(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds\n      .toString()\n      .padStart(2, '0')}`;\n  }\n\n  // Méthodes pour l'éditeur d'images\n  closeImageEditor(): void {\n    this.showImageEditor = false;\n  }\n\n  saveEditedImage(): void {\n    // Sauvegarder l'image éditée\n  }\n\n  startCropResize(corner: string, event: any): void {\n    // Commencer le redimensionnement de crop\n  }\n\n  editTextElement(index: number): void {\n    // Éditer un élément de texte\n  }\n\n  setImageEditorMode(mode: string): void {\n    this.imageEditorMode = mode;\n  }\n\n  setCropRatio(ratio: string): void {\n    this.cropRatio = ratio;\n  }\n\n  rotateImage(angle: number): void {\n    // Faire tourner l'image\n  }\n\n  // Initialiser des données de test\n  private initializeTestData(): void {\n    // Créer une conversation de test\n    this.conversation = {\n      id: 'test-conversation',\n      participants: [\n        { id: 'user1', username: 'Alice' },\n        { id: 'user2', username: 'Bob' },\n      ],\n    } as Conversation;\n\n    // Créer des messages de test\n    this.messages = [\n      {\n        id: '1',\n        content: 'Salut ! Comment ça va ?',\n        sender: { id: 'user2', username: 'Bob' },\n        timestamp: new Date(Date.now() - 3600000),\n        conversationId: 'test-conversation',\n        type: 'text' as MessageType,\n      },\n      {\n        id: '2',\n        content: 'Ça va bien, merci ! Et toi ?',\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(Date.now() - 3000000),\n        conversationId: 'test-conversation',\n        type: 'text' as MessageType,\n      },\n      {\n        id: '3',\n        content: \"Super ! Tu veux qu'on se voit ce soir ?\",\n        sender: { id: 'user2', username: 'Bob' },\n        timestamp: new Date(Date.now() - 1800000),\n        conversationId: 'test-conversation',\n        type: 'text' as MessageType,\n      },\n    ];\n\n    this.otherParticipant = { id: 'user2', username: 'Bob' } as User;\n    this.loading = false;\n  }\n\n  // === MÉTHODES MANQUANTES POUR CORRIGER LES ERREURS DU TEMPLATE ===\n\n  // Méthodes pour les emojis et stickers\n  insertGif(gif: any): void {\n    // Insérer un GIF dans le message\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({ content: content + `[GIF:${gif.id}]` });\n  }\n\n  manageStickerPacks(): void {\n    // Gérer les packs de stickers\n  }\n\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n\n  // Méthodes pour l'outil de dessin\n  openDrawingTool(): void {\n    this.showDrawingTool = true;\n  }\n\n  closeDrawingTool(): void {\n    this.showDrawingTool = false;\n  }\n\n  selectDrawingTool(tool: string): void {\n    this.selectedDrawingTool = tool;\n  }\n\n  selectDrawingColor(color: string): void {\n    this.selectedDrawingColor = color;\n  }\n\n  undoDrawing(): void {\n    // Annuler le dernier trait\n  }\n\n  redoDrawing(): void {\n    // Refaire le dernier trait annulé\n  }\n\n  clearDrawing(): void {\n    // Effacer tout le dessin\n  }\n\n  startDrawing(event: any): void {\n    // Commencer à dessiner\n  }\n\n  draw(event: any): void {\n    // Dessiner\n  }\n\n  stopDrawing(): void {\n    // Arrêter de dessiner\n  }\n\n  saveDrawing(): void {\n    // Sauvegarder le dessin\n  }\n\n  // === MÉTHODES POUR LES RÉACTIONS ===\n  hasUserReacted(reaction: any, userId: string | null): boolean {\n    if (!userId) return false;\n    return reaction.userId === userId;\n  }\n\n  getReactionTooltip(reaction: any): string {\n    return `${reaction.user?.username || 'Utilisateur'} a réagi avec ${\n      reaction.emoji\n    }`;\n  }\n\n  // === MÉTHODES POUR LES PERMISSIONS ===\n  canDeleteMessage(message: any): boolean {\n    // L'utilisateur peut supprimer ses propres messages ou s'il est admin\n    return message.sender?.id === this.currentUserId || this.isAdmin;\n  }\n\n  canEditMessage(message: any): boolean {\n    // L'utilisateur peut modifier ses propres messages\n    return message.sender?.id === this.currentUserId;\n  }\n\n  // === MÉTHODES POUR LA SÉLECTION DE MESSAGES ===\n  exitSelectionMode(): void {\n    this.selectionMode = false;\n    this.selectedMessages.clear();\n  }\n\n  deleteSelectedMessages(): void {\n    if (this.selectedMessages.size === 0) return;\n\n    if (confirm(`Supprimer ${this.selectedMessages.size} message(s) ?`)) {\n      // Supprimer les messages sélectionnés\n      this.messages = this.messages.filter(\n        (m) => !m.id || !this.selectedMessages.has(m.id)\n      );\n      this.exitSelectionMode();\n      this.toastService.showSuccess('Messages supprimés');\n    }\n  }\n\n  forwardSelectedMessages(): void {\n    if (this.selectedMessages.size === 0) return;\n\n    // Ouvrir le modal de transfert\n    this.showForwardModal = true;\n    this.forwardingMessage = Array.from(this.selectedMessages);\n  }\n\n  copySelectedMessages(): void {\n    if (this.selectedMessages.size === 0) return;\n\n    const selectedMessagesArray = this.messages.filter(\n      (m) => m.id && this.selectedMessages.has(m.id)\n    );\n    const textToCopy = selectedMessagesArray\n      .map((m) => `${m.sender?.username}: ${m.content}`)\n      .join('\\n');\n\n    navigator.clipboard\n      .writeText(textToCopy)\n      .then(() => {\n        this.toastService.showSuccess('Messages copiés');\n        this.exitSelectionMode();\n      })\n      .catch(() => {\n        this.toastService.showError('Erreur lors de la copie');\n      });\n  }\n\n  // === MÉTHODES POUR LES RÉPONSES ===\n  cancelReply(): void {\n    this.replyingToMessage = null;\n  }\n\n  // === MÉTHODES POUR LES FICHIERS ===\n  removeSelectedFile(): void {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n\n  // === MÉTHODES POUR L'ENREGISTREMENT VOCAL ===\n  formatRecordingDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  cancelVoiceRecording(): void {\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.voiceRecordingState = 'idle';\n  }\n\n  // === MÉTHODES POUR LES SONDAGES ===\n  canCreatePoll(): boolean {\n    return (\n      this.pollQuestion.trim().length > 0 &&\n      this.pollOptions.filter((opt) => opt.text.trim().length > 0).length >= 2\n    );\n  }\n\n  // === MÉTHODES POUR LA LOCALISATION ===\n  closeLocationPicker(): void {\n    this.showLocationPicker = false;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n    this.locationSearchResults = [];\n    this.selectedLocation = null;\n  }\n\n  getCurrentLocation(): void {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          this.selectedLocation = {\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude,\n            name: 'Ma position actuelle',\n          };\n        },\n        (error) => {\n          this.toastService.showError(\"Impossible d'obtenir votre position\");\n        }\n      );\n    } else {\n      this.toastService.showError('Géolocalisation non supportée');\n    }\n  }\n\n  searchLocation(): void {\n    this.showLocationSearch = true;\n    // Simuler une recherche de lieux\n    this.locationSearchResults = [\n      { name: 'Paris, France', latitude: 48.8566, longitude: 2.3522 },\n      { name: 'Lyon, France', latitude: 45.764, longitude: 4.8357 },\n      { name: 'Marseille, France', latitude: 43.2965, longitude: 5.3698 },\n    ];\n  }\n\n  onLocationSearch(): void {\n    // Filtrer les résultats selon la recherche\n    if (this.locationSearchQuery.trim()) {\n      this.locationSearchResults = this.locationSearchResults.filter(\n        (location) =>\n          location.name\n            .toLowerCase()\n            .includes(this.locationSearchQuery.toLowerCase())\n      );\n    }\n  }\n\n  selectLocationResult(result: any): void {\n    this.selectedLocation = result;\n    this.showLocationSearch = false;\n    this.locationSearchQuery = '';\n  }\n\n  editLocationMessage(): void {\n    this.showLocationMessage = true;\n  }\n\n  shareLocation(): void {\n    if (this.selectedLocation) {\n      // Créer un message de localisation\n      const locationMessage = {\n        id: Date.now().toString(),\n        type: 'location',\n        location: this.selectedLocation,\n        locationMessage: this.locationMessage,\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id,\n      };\n\n      this.messages.push(locationMessage as any);\n      this.closeLocationPicker();\n      this.locationMessage = '';\n      this.toastService.showSuccess('Localisation partagée');\n    }\n  }\n\n  // === MÉTHODES POUR LES CONTACTS ===\n  closeContactPicker(): void {\n    this.showContactPicker = false;\n    this.contactSearchQuery = '';\n    this.selectedContactForSharing = null;\n  }\n\n  onContactSearch(): void {\n    // Filtrer les contacts selon la recherche\n    // Implémentation de la recherche de contacts\n  }\n\n  getFilteredContactsForSharing(): any[] {\n    // Simuler une liste de contacts\n    const contacts = [\n      {\n        id: '1',\n        name: 'Alice Martin',\n        phone: '+33123456789',\n        email: '<EMAIL>',\n      },\n      {\n        id: '2',\n        name: 'Bob Dupont',\n        phone: '+33987654321',\n        email: '<EMAIL>',\n      },\n      {\n        id: '3',\n        name: 'Claire Durand',\n        phone: '+33555666777',\n        email: '<EMAIL>',\n      },\n    ];\n\n    if (this.contactSearchQuery.trim()) {\n      return contacts.filter(\n        (contact) =>\n          contact.name\n            .toLowerCase()\n            .includes(this.contactSearchQuery.toLowerCase()) ||\n          contact.phone.includes(this.contactSearchQuery) ||\n          contact.email\n            .toLowerCase()\n            .includes(this.contactSearchQuery.toLowerCase())\n      );\n    }\n    return contacts;\n  }\n\n  selectContactForSharing(contact: any): void {\n    this.selectedContactForSharing = contact;\n  }\n\n  viewContactDetails(contact: any): void {\n    // Ouvrir les détails du contact\n    this.toastService.showInfo(`Détails de ${contact.name}`);\n  }\n\n  createNewContact(): void {\n    // Ouvrir le formulaire de création de contact\n    this.toastService.showInfo(\"Création d'un nouveau contact\");\n  }\n\n  shareContact(): void {\n    if (this.selectedContactForSharing) {\n      // Créer un message de contact\n      const contactMessage = {\n        id: Date.now().toString(),\n        type: 'contact',\n        contact: this.selectedContactForSharing,\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id,\n      };\n\n      this.messages.push(contactMessage as any);\n      this.closeContactPicker();\n      this.toastService.showSuccess('Contact partagé');\n    }\n  }\n\n  // === MÉTHODES POUR LES EMOJIS ET STICKERS ===\n  openStickerPicker(): void {\n    this.showStickerPicker = true;\n    this.showEmojiPicker = false;\n  }\n\n  openGifPicker(): void {\n    this.showGifPicker = true;\n    this.showEmojiPicker = false;\n  }\n\n  onEmojiSearch(): void {\n    // Filtrer les emojis selon la recherche\n    // Implémentation de la recherche d'emojis\n  }\n\n  insertEmoji(emoji: string): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: currentContent + emoji,\n    });\n  }\n\n  getFilteredEmojiCategories(): any[] {\n    return this.emojiCategories.filter((category) => {\n      if (!this.emojiSearchQuery.trim()) return true;\n      return category.name\n        .toLowerCase()\n        .includes(this.emojiSearchQuery.toLowerCase());\n    });\n  }\n\n  previewEmoji(emoji: any): void {\n    this.previewedEmoji = emoji;\n  }\n\n  selectStickerPack(packId: string): void {\n    this.selectedStickerPack = packId;\n  }\n\n  getSelectedStickerPack(): any {\n    return this.stickerPacks.find(\n      (pack) => pack.id === this.selectedStickerPack\n    );\n  }\n\n  insertSticker(sticker: any): void {\n    // Créer un message sticker\n    const stickerMessage = {\n      id: Date.now().toString(),\n      type: 'sticker',\n      sticker: sticker,\n      sender: { id: this.currentUserId!, username: this.currentUsername },\n      timestamp: new Date(),\n      conversationId: this.conversation?.id,\n    };\n\n    this.messages.push(stickerMessage as any);\n    this.showStickerPicker = false;\n    this.toastService.showSuccess('Sticker envoyé');\n  }\n\n  onGifSearch(): void {\n    // Filtrer les GIFs selon la recherche\n    // Implémentation de la recherche de GIFs\n  }\n\n  selectGifCategory(category: string): void {\n    this.selectedGifCategory = category;\n  }\n\n  getFilteredGifs(): any[] {\n    // Simuler une liste de GIFs\n    return [\n      { id: '1', url: 'gif1.gif', title: 'Happy' },\n      { id: '2', url: 'gif2.gif', title: 'Funny' },\n      { id: '3', url: 'gif3.gif', title: 'Love' },\n    ];\n  }\n\n  // === MÉTHODES POUR LES MODALS ===\n  isCallMinimized = false;\n  isInCall = false;\n  inputFocused = false;\n\n  cancelDelete(): void {\n    this.showDeleteConfirmModal = false;\n  }\n\n  confirmDelete(): void {\n    // Logique de suppression\n    this.showDeleteConfirmModal = false;\n    this.toastService.showSuccess('Message supprimé');\n  }\n\n  closeForwardModal(): void {\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n  }\n\n  forwardSearchQuery = '';\n\n  getFilteredContacts(): any[] {\n    const contacts = [\n      { id: '1', name: 'Alice Martin', avatar: 'avatar1.jpg' },\n      { id: '2', name: 'Bob Dupont', avatar: 'avatar2.jpg' },\n      { id: '3', name: 'Claire Durand', avatar: 'avatar3.jpg' },\n    ];\n\n    if (this.forwardSearchQuery.trim()) {\n      return contacts.filter((contact) =>\n        contact.name\n          .toLowerCase()\n          .includes(this.forwardSearchQuery.toLowerCase())\n      );\n    }\n    return contacts;\n  }\n\n  toggleContactSelection(contactId: string): void {\n    const index = this.selectedConversations.indexOf(contactId);\n    if (index > -1) {\n      this.selectedConversations.splice(index, 1);\n    } else {\n      this.selectedConversations.push(contactId);\n    }\n  }\n\n  confirmForward(): void {\n    if (this.selectedConversations.length > 0) {\n      this.toastService.showSuccess(\n        `Message transféré à ${this.selectedConversations.length} contact(s)`\n      );\n      this.closeForwardModal();\n    }\n  }\n\n  // === MÉTHODES POUR LE VISUALISEUR D'IMAGES ===\n  previousImage(): void {\n    if (this.currentImageIndex > 0) {\n      this.currentImageIndex--;\n    }\n  }\n\n  nextImage(): void {\n    if (this.currentImageIndex < this.imageGallery.length - 1) {\n      this.currentImageIndex++;\n    }\n  }\n\n  // === MÉTHODES POUR LES TOASTS ===\n  getToastIcon(): string {\n    const icons = {\n      success: 'fas fa-check-circle',\n      error: 'fas fa-exclamation-circle',\n      warning: 'fas fa-exclamation-triangle',\n      info: 'fas fa-info-circle',\n    };\n    return icons[this.toastType as keyof typeof icons] || 'fas fa-info-circle';\n  }\n\n  // === MÉTHODES POUR LES STATISTIQUES ===\n  getTotalMessagesCount(): number {\n    return this.messages.length;\n  }\n\n  getPhotosCount(): number {\n    return this.messages.filter((m) =>\n      m.attachments?.some((att) => att.type === 'image')\n    ).length;\n  }\n\n  getFilesCount(): number {\n    return this.messages.filter((m) =>\n      m.attachments?.some((att) => att.type === 'file')\n    ).length;\n  }\n\n  // === MÉTHODES POUR LES PARAMÈTRES DE CONVERSATION ===\n  updateConversationSettings(): void {\n    // Sauvegarder les paramètres de conversation\n    this.toastService.showSuccess('Paramètres mis à jour');\n  }\n\n  // === MÉTHODES POUR LA TRADUCTION ===\n  closeTranslationPanel(): void {\n    this.showTranslationPanel = false;\n    this.originalTextForTranslation = '';\n    this.translatedText = '';\n  }\n\n  swapLanguages(): void {\n    const temp = this.translationFrom;\n    this.translationFrom = this.translationTo;\n    this.translationTo = temp;\n  }\n\n  copyTranslation(): void {\n    if (this.translatedText) {\n      navigator.clipboard.writeText(this.translatedText).then(() => {\n        this.toastService.showSuccess('Traduction copiée');\n      });\n    }\n  }\n\n  insertTranslation(): void {\n    if (this.translatedText) {\n      const currentContent = this.messageForm.get('content')?.value || '';\n      this.messageForm.patchValue({\n        content: currentContent + this.translatedText,\n      });\n      this.closeTranslationPanel();\n    }\n  }\n\n  shareTranslation(): void {\n    if (this.translatedText) {\n      // Créer un message avec la traduction\n      const translationMessage = {\n        id: Date.now().toString(),\n        content: this.translatedText,\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id,\n      };\n\n      this.messages.push(translationMessage as any);\n      this.closeTranslationPanel();\n      this.toastService.showSuccess('Traduction partagée');\n    }\n  }\n\n  // === MÉTHODES POUR LES SONDAGES ===\n  closePollCreator(): void {\n    this.showPollCreator = false;\n    this.pollQuestion = '';\n    this.pollOptions = [{ text: '' }, { text: '' }];\n    this.pollSettings = {\n      allowMultiple: false,\n      anonymous: false,\n      showResults: true,\n      showVoters: true,\n    };\n  }\n\n  addPollOption(): void {\n    if (this.pollOptions.length < 10) {\n      this.pollOptions.push({ text: '' });\n    }\n  }\n\n  removePollOption(index: number): void {\n    if (this.pollOptions.length > 2) {\n      this.pollOptions.splice(index, 1);\n    }\n  }\n\n  createPoll(): void {\n    if (this.canCreatePoll()) {\n      // Créer un message sondage\n      const pollMessage = {\n        id: Date.now().toString(),\n        type: 'poll',\n        poll: {\n          question: this.pollQuestion,\n          options: this.pollOptions.filter((opt) => opt.text.trim()),\n          settings: this.pollSettings,\n          votes: [],\n          totalVotes: 0,\n        },\n        sender: { id: this.currentUserId!, username: this.currentUsername },\n        timestamp: new Date(),\n        conversationId: this.conversation?.id,\n      };\n\n      this.messages.push(pollMessage as any);\n      this.closePollCreator();\n      this.toastService.showSuccess('Sondage créé');\n    }\n  }\n\n  // === MÉTHODES POUR LES FONCTIONNALITÉS DE BASE ===\n  hasMessageContent(): boolean {\n    const content = this.messageForm.get('content')?.value;\n    return content && content.trim().length > 0;\n  }\n\n  canSendMessage(): boolean {\n    return this.hasMessageContent() || this.selectedFile !== null;\n  }\n\n  searchEmojis(): void {\n    // Rechercher des emojis selon emojiSearchQuery\n  }\n\n  getFilteredEmojis(): any[] {\n    // Simuler une liste d'emojis\n    const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'];\n    if (this.emojiSearchQuery.trim()) {\n      return emojis.filter((emoji) => emoji.includes(this.emojiSearchQuery));\n    }\n    return emojis;\n  }\n\n  // === MÉTHODES POUR LES NOTIFICATIONS ===\n  toggleNotificationPanel(): void {\n    this.showNotificationPanel = !this.showNotificationPanel;\n  }\n\n  onSearchKeyPress(event: any): void {\n    if (event.key === 'Enter') {\n      this.performSearch();\n    }\n  }\n\n  toggleSearchBar(): void {\n    this.showSearchBar = !this.showSearchBar;\n    if (!this.showSearchBar) {\n      this.searchQuery = '';\n      this.searchResults = [];\n    }\n  }\n\n  navigateToMessage(messageId: string): void {\n    // Naviguer vers un message spécifique\n    this.highlightedMessageId = messageId;\n  }\n\n  highlightSearchTerms(content: string, query: string): string {\n    if (!query) return content;\n    const regex = new RegExp(`(${query})`, 'gi');\n    return content.replace(regex, '<mark>$1</mark>');\n  }\n\n  // === MÉTHODES POUR LES MESSAGES ÉPINGLÉS ===\n  getPinnedMessagesCount(): number {\n    return this.messages.filter((m) => m.pinned).length;\n  }\n\n  togglePinnedMessages(): void {\n    this.showPinnedMessages = !this.showPinnedMessages;\n  }\n\n  scrollToPinnedMessage(messageId: string): void {\n    this.scrollToMessage(messageId);\n  }\n\n  formatMessageDate(date: Date | string): string {\n    return this.formatMessageTime(date);\n  }\n\n  // === MÉTHODES POUR LES APPELS ===\n  declineCall(): void {\n    this.incomingCall = null;\n    this.toastService.showInfo('Appel refusé');\n  }\n\n  acceptCall(): void {\n    this.incomingCall = null;\n    this.isInCall = true;\n    this.toastService.showSuccess('Appel accepté');\n  }\n\n  formatCallDuration(duration: number): string {\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  getCallStatusText(): string {\n    if (this.isInCall) return 'En cours...';\n    return 'Connexion...';\n  }\n\n  toggleCallMinimize(): void {\n    this.isCallMinimized = !this.isCallMinimized;\n  }\n\n  showCallControls = true;\n\n  // === MÉTHODES POUR LE FORMATAGE DE TEXTE ===\n  applyFormatting(type: string): void {\n    const content = this.messageForm.get('content')?.value || '';\n    let formattedContent = content;\n\n    switch (type) {\n      case 'bold':\n        formattedContent = `*${content}*`;\n        break;\n      case 'italic':\n        formattedContent = `_${content}_`;\n        break;\n      case 'strikethrough':\n        formattedContent = `~${content}~`;\n        break;\n      case 'underline':\n        formattedContent = `__${content}__`;\n        break;\n      case 'code':\n        formattedContent = `\\`${content}\\``;\n        break;\n      case 'quote':\n        formattedContent = `> ${content}`;\n        break;\n      case 'spoiler':\n        formattedContent = `||${content}||`;\n        break;\n    }\n\n    this.messageForm.patchValue({ content: formattedContent });\n  }\n\n  hasFormatting(type: string): boolean {\n    const content = this.messageForm.get('content')?.value || '';\n    switch (type) {\n      case 'bold':\n        return content.includes('*');\n      case 'italic':\n        return content.includes('_');\n      case 'strikethrough':\n        return content.includes('~');\n      case 'underline':\n        return content.includes('__');\n      case 'code':\n        return content.includes('`');\n      case 'quote':\n        return content.includes('>');\n      case 'spoiler':\n        return content.includes('||');\n      default:\n        return false;\n    }\n  }\n\n  insertLink(): void {\n    const url = prompt(\"Entrez l'URL du lien :\");\n    if (url) {\n      const text = prompt('Entrez le texte du lien :') || url;\n      const content = this.messageForm.get('content')?.value || '';\n      this.messageForm.patchValue({\n        content: content + `[${text}](${url})`,\n      });\n    }\n  }\n\n  insertTable(): void {\n    const table =\n      '\\n| Colonne 1 | Colonne 2 |\\n|-----------|----------|\\n| Cellule 1 | Cellule 2 |\\n';\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + table,\n    });\n  }\n\n  insertList(type: string): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const listItem =\n      type === 'ul' ? '• Élément de liste\\n' : '1. Élément de liste\\n';\n    this.messageForm.patchValue({\n      content: content + '\\n' + listItem,\n    });\n  }\n\n  insertMentionSymbol(): void {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + '@',\n    });\n  }\n\n  insertHashtagSymbol(): void {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + '#',\n    });\n  }\n\n  clearFormatting(): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const cleanContent = content\n      .replace(/\\*([^*]+)\\*/g, '$1') // Bold\n      .replace(/_([^_]+)_/g, '$1') // Italic\n      .replace(/~([^~]+)~/g, '$1') // Strikethrough\n      .replace(/__([^_]+)__/g, '$1') // Underline\n      .replace(/`([^`]+)`/g, '$1') // Code\n      .replace(/> /g, '') // Quote\n      .replace(/\\|\\|([^|]+)\\|\\|/g, '$1'); // Spoiler\n\n    this.messageForm.patchValue({ content: cleanContent });\n  }\n\n  toggleFormattingToolbar(): void {\n    this.showFormattingToolbar = !this.showFormattingToolbar;\n  }\n\n  // === MÉTHODES POUR LES CORRECTIONS AUTOMATIQUES ===\n  hideAutoCorrectSuggestions(): void {\n    this.autoCorrectSuggestions = [];\n  }\n\n  applyCorrection(original: string, correction: string): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const correctedContent = content.replace(original, correction);\n    this.messageForm.patchValue({ content: correctedContent });\n    this.hideAutoCorrectSuggestions();\n  }\n\n  ignoreCorrection(original: string): void {\n    this.autoCorrectSuggestions = this.autoCorrectSuggestions.filter(\n      (s) => s.original !== original\n    );\n  }\n\n  // === MÉTHODES POUR LES FICHIERS ===\n  triggerFileInput(type: string): void {\n    const input = document.createElement('input');\n    input.type = 'file';\n\n    switch (type) {\n      case 'image':\n        input.accept = 'image/*';\n        break;\n      case 'video':\n        input.accept = 'video/*';\n        break;\n      case 'audio':\n        input.accept = 'audio/*';\n        break;\n      case 'document':\n        input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx';\n        break;\n    }\n\n    input.onchange = (event: any) => {\n      this.onFileSelected(event);\n    };\n\n    input.click();\n  }\n\n  // === MÉTHODES POUR L'INPUT DE MESSAGE ===\n  getInputPlaceholder(): string {\n    if (this.replyingToMessage) {\n      return `Répondre à ${this.replyingToMessage.sender?.username}...`;\n    }\n    if (this.isRecordingVoice) {\n      return 'Enregistrement en cours...';\n    }\n    return 'Tapez votre message...';\n  }\n\n  onInputChange(event: any): void {\n    const content = event.target.value;\n    this.messageForm.patchValue({ content });\n\n    // Détecter les mentions, hashtags, etc.\n    this.detectMentions(content);\n    this.detectHashtags(content);\n    this.detectCommands(content);\n\n    // Gestion de la frappe\n    this.handleTyping();\n  }\n\n  onInputKeyDown(event: any): void {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    } else if (event.key === 'ArrowUp' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.selectedMentionIndex = Math.max(0, this.selectedMentionIndex - 1);\n    } else if (\n      event.key === 'ArrowDown' &&\n      this.mentionSuggestions.length > 0\n    ) {\n      event.preventDefault();\n      this.selectedMentionIndex = Math.min(\n        this.mentionSuggestions.length - 1,\n        this.selectedMentionIndex + 1\n      );\n    } else if (event.key === 'Tab' && this.mentionSuggestions.length > 0) {\n      event.preventDefault();\n      this.insertMention(this.mentionSuggestions[this.selectedMentionIndex]);\n    }\n  }\n\n  onInputKeyUp(event: any): void {\n    // Gestion des suggestions en temps réel\n    this.updateSuggestions();\n  }\n\n  onInputFocus(): void {\n    this.inputFocused = true;\n  }\n\n  onInputBlur(): void {\n    this.inputFocused = false;\n    // Masquer les suggestions après un délai\n    setTimeout(() => {\n      this.mentionSuggestions = [];\n      this.hashtagSuggestions = [];\n      this.commandSuggestions = [];\n    }, 200);\n  }\n\n  onInputPaste(event: any): void {\n    const clipboardData = event.clipboardData || (window as any).clipboardData;\n    const pastedData = clipboardData.getData('text');\n\n    // Traiter le contenu collé\n    if (pastedData.includes('http')) {\n      // Détecter les liens\n      this.detectLinks(pastedData);\n    }\n  }\n\n  onInputScroll(event: any): void {\n    // Gérer le défilement de l'input si nécessaire\n  }\n\n  // === MÉTHODES POUR LES MENTIONS ET SUGGESTIONS ===\n  detectMentions(content: string): void {\n    const mentionMatch = content.match(/@(\\w*)$/);\n    if (mentionMatch) {\n      const query = mentionMatch[1];\n      this.mentionSuggestions = this.getFilteredUsers(query);\n      this.selectedMentionIndex = 0;\n    } else {\n      this.mentionSuggestions = [];\n    }\n  }\n\n  detectHashtags(content: string): void {\n    const hashtagMatch = content.match(/#(\\w*)$/);\n    if (hashtagMatch) {\n      const query = hashtagMatch[1];\n      this.hashtagSuggestions = this.getFilteredHashtags(query);\n    } else {\n      this.hashtagSuggestions = [];\n    }\n  }\n\n  detectCommands(content: string): void {\n    const commandMatch = content.match(/\\/(\\w*)$/);\n    if (commandMatch) {\n      const query = commandMatch[1];\n      this.commandSuggestions = this.getFilteredCommands(query);\n    } else {\n      this.commandSuggestions = [];\n    }\n  }\n\n  detectLinks(content: string): void {\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\n    const links = content.match(urlRegex);\n    if (links) {\n      // Traiter les liens détectés\n      console.log('Liens détectés:', links);\n    }\n  }\n\n  insertMention(user: any): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/@\\w*$/, `@${user.username} `);\n    this.messageForm.patchValue({ content: newContent });\n    this.mentionSuggestions = [];\n  }\n\n  insertHashtag(hashtag: any): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/#\\w*$/, `#${hashtag.name} `);\n    this.messageForm.patchValue({ content: newContent });\n    this.hashtagSuggestions = [];\n  }\n\n  insertCommand(command: any): void {\n    const content = this.messageForm.get('content')?.value || '';\n    const newContent = content.replace(/\\/\\w*$/, `/${command.name} `);\n    this.messageForm.patchValue({ content: newContent });\n    this.commandSuggestions = [];\n  }\n\n  setSelectedMentionIndex(index: number): void {\n    this.selectedMentionIndex = index;\n  }\n\n  updateSuggestions(): void {\n    const content = this.messageForm.get('content')?.value || '';\n    this.detectMentions(content);\n    this.detectHashtags(content);\n    this.detectCommands(content);\n  }\n\n  // === MÉTHODES POUR LE COMPTAGE DE CARACTÈRES ===\n  getCharacterCount(): number {\n    const content = this.messageForm.get('content')?.value || '';\n    return content.length;\n  }\n\n  hasFormattedText(): boolean {\n    const content = this.messageForm.get('content')?.value || '';\n    return (\n      content.includes('*') ||\n      content.includes('_') ||\n      content.includes('`') ||\n      content.includes('~')\n    );\n  }\n\n  // === MÉTHODES POUR LA TRADUCTION ===\n  getLanguageName(languageCode: string): string {\n    const languages: { [key: string]: string } = {\n      fr: 'Français',\n      en: 'Anglais',\n      es: 'Espagnol',\n      de: 'Allemand',\n      it: 'Italien',\n    };\n    return languages[languageCode] || languageCode;\n  }\n\n  translateMessage(): void {\n    this.showTranslationPanel = true;\n    const content = this.messageForm.get('content')?.value || '';\n    this.originalTextForTranslation = content;\n    // Simuler une traduction\n    this.translatedText = `[Traduction] ${content}`;\n  }\n\n  showAutoCorrections(): void {\n    // Afficher les corrections automatiques\n    this.autoCorrectSuggestions = [\n      { original: 'salut', corrections: ['Salut'] },\n      { original: 'ca va', corrections: ['Ça va'] },\n    ];\n  }\n\n  // === MÉTHODES UTILITAIRES MANQUANTES ===\n  handleTyping(): void {\n    // Gérer l'indicateur de frappe\n    if (!this.isTyping) {\n      this.isTyping = true;\n      // Envoyer l'événement de début de frappe\n    }\n\n    // Réinitialiser le timer\n    clearTimeout(this.typingTimeout);\n    this.typingTimeout = setTimeout(() => {\n      this.isTyping = false;\n      // Envoyer l'événement de fin de frappe\n    }, 1000);\n  }\n\n  getFilteredUsers(query: string): any[] {\n    const users = [\n      { id: '1', username: 'alice', name: 'Alice Martin' },\n      { id: '2', username: 'bob', name: 'Bob Dupont' },\n      { id: '3', username: 'claire', name: 'Claire Durand' },\n    ];\n\n    if (!query) return users;\n    return users.filter(\n      (user) =>\n        user.username.toLowerCase().includes(query.toLowerCase()) ||\n        user.name.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n\n  getFilteredHashtags(query: string): any[] {\n    const hashtags = [\n      { id: '1', name: 'important', count: 15 },\n      { id: '2', name: 'urgent', count: 8 },\n      { id: '3', name: 'meeting', count: 12 },\n    ];\n\n    if (!query) return hashtags;\n    return hashtags.filter((hashtag) =>\n      hashtag.name.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n\n  getFilteredCommands(query: string): any[] {\n    const commands = [\n      { id: '1', name: 'help', description: \"Afficher l'aide\" },\n      { id: '2', name: 'clear', description: 'Effacer la conversation' },\n      { id: '3', name: 'status', description: 'Changer le statut' },\n    ];\n\n    if (!query) return commands;\n    return commands.filter((command) =>\n      command.name.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n\n  // === MÉTHODES MANQUANTES POUR LES MODALS ET FONCTIONNALITÉS ===\n  openCamera(): void {\n    // Ouvrir la caméra\n    this.toastService.showInfo('Ouverture de la caméra...');\n  }\n\n  openLocationPicker(): void {\n    this.showLocationPicker = true;\n  }\n\n  openContactPicker(): void {\n    this.showContactPicker = true;\n  }\n\n  openPollCreator(): void {\n    this.showPollCreator = true;\n  }\n\n  hideQuickReplies(): void {\n    this.showQuickReplies = false;\n  }\n\n  insertQuickReply(reply: string): void {\n    const content = this.messageForm.get('content')?.value || '';\n    this.messageForm.patchValue({\n      content: content + reply,\n    });\n    this.hideQuickReplies();\n  }\n\n  // === CORRECTION DES MÉTHODES AVEC PARAMÈTRES ===\n  onSearchInput(event?: any): void {\n    // Rechercher dans les messages\n    if (event) {\n      this.searchQuery = event.target.value;\n    }\n  }\n\n  // === SYSTÈME D'ÉMOJIS COMPLET ===\n  emojiCategories = [\n    {\n      id: 'smileys',\n      name: 'Smileys',\n      icon: '😀',\n      emojis: [\n        { emoji: '😀', name: 'grinning face' },\n        { emoji: '😃', name: 'grinning face with big eyes' },\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\n        { emoji: '😆', name: 'grinning squinting face' },\n        { emoji: '😅', name: 'grinning face with sweat' },\n        { emoji: '😂', name: 'face with tears of joy' },\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\n        { emoji: '😇', name: 'smiling face with halo' },\n        { emoji: '🙂', name: 'slightly smiling face' },\n        { emoji: '🙃', name: 'upside-down face' },\n        { emoji: '😉', name: 'winking face' },\n        { emoji: '😌', name: 'relieved face' },\n        { emoji: '😍', name: 'smiling face with heart-eyes' },\n        { emoji: '🥰', name: 'smiling face with hearts' },\n        { emoji: '😘', name: 'face blowing a kiss' },\n        { emoji: '😗', name: 'kissing face' },\n        { emoji: '😙', name: 'kissing face with smiling eyes' },\n        { emoji: '😚', name: 'kissing face with closed eyes' },\n        { emoji: '😋', name: 'face savoring food' },\n        { emoji: '😛', name: 'face with tongue' },\n        { emoji: '😝', name: 'squinting face with tongue' },\n        { emoji: '😜', name: 'winking face with tongue' },\n      ],\n    },\n    {\n      id: 'people',\n      name: 'People',\n      icon: '👤',\n      emojis: [\n        { emoji: '👶', name: 'baby' },\n        { emoji: '🧒', name: 'child' },\n        { emoji: '👦', name: 'boy' },\n        { emoji: '👧', name: 'girl' },\n        { emoji: '🧑', name: 'person' },\n        { emoji: '👱', name: 'person: blond hair' },\n        { emoji: '👨', name: 'man' },\n        { emoji: '🧔', name: 'man: beard' },\n        { emoji: '👩', name: 'woman' },\n        { emoji: '🧓', name: 'older person' },\n        { emoji: '👴', name: 'old man' },\n        { emoji: '👵', name: 'old woman' },\n        { emoji: '🙍', name: 'person frowning' },\n        { emoji: '🙎', name: 'person pouting' },\n        { emoji: '🙅', name: 'person gesturing NO' },\n        { emoji: '🙆', name: 'person gesturing OK' },\n        { emoji: '💁', name: 'person tipping hand' },\n        { emoji: '🙋', name: 'person raising hand' },\n        { emoji: '🧏', name: 'deaf person' },\n        { emoji: '🙇', name: 'person bowing' },\n        { emoji: '🤦', name: 'person facepalming' },\n        { emoji: '🤷', name: 'person shrugging' },\n        { emoji: '👮', name: 'police officer' },\n        { emoji: '🕵️', name: 'detective' },\n      ],\n    },\n    {\n      id: 'nature',\n      name: 'Nature',\n      icon: '🌿',\n      emojis: [\n        { emoji: '🐶', name: 'dog face' },\n        { emoji: '🐱', name: 'cat face' },\n        { emoji: '🐭', name: 'mouse face' },\n        { emoji: '🐹', name: 'hamster' },\n        { emoji: '🐰', name: 'rabbit face' },\n        { emoji: '🦊', name: 'fox' },\n        { emoji: '🐻', name: 'bear' },\n        { emoji: '🐼', name: 'panda' },\n        { emoji: '🐨', name: 'koala' },\n        { emoji: '🐯', name: 'tiger face' },\n        { emoji: '🦁', name: 'lion' },\n        { emoji: '🐮', name: 'cow face' },\n        { emoji: '🐷', name: 'pig face' },\n        { emoji: '🐸', name: 'frog' },\n        { emoji: '🐵', name: 'monkey face' },\n        { emoji: '🙈', name: 'see-no-evil monkey' },\n        { emoji: '🙉', name: 'hear-no-evil monkey' },\n        { emoji: '🙊', name: 'speak-no-evil monkey' },\n        { emoji: '🐒', name: 'monkey' },\n        { emoji: '🐔', name: 'chicken' },\n        { emoji: '🐧', name: 'penguin' },\n        { emoji: '🐦', name: 'bird' },\n        { emoji: '🐤', name: 'baby chick' },\n        { emoji: '🐣', name: 'hatching chick' },\n      ],\n    },\n    {\n      id: 'food',\n      name: 'Food',\n      icon: '🍎',\n      emojis: [\n        { emoji: '🍎', name: 'red apple' },\n        { emoji: '🍊', name: 'tangerine' },\n        { emoji: '🍋', name: 'lemon' },\n        { emoji: '🍌', name: 'banana' },\n        { emoji: '🍉', name: 'watermelon' },\n        { emoji: '🍇', name: 'grapes' },\n        { emoji: '🍓', name: 'strawberry' },\n        { emoji: '🫐', name: 'blueberries' },\n        { emoji: '🍈', name: 'melon' },\n        { emoji: '🍒', name: 'cherries' },\n        { emoji: '🍑', name: 'peach' },\n        { emoji: '🥭', name: 'mango' },\n        { emoji: '🍍', name: 'pineapple' },\n        { emoji: '🥥', name: 'coconut' },\n        { emoji: '🥝', name: 'kiwi fruit' },\n        { emoji: '🍅', name: 'tomato' },\n        { emoji: '🍆', name: 'eggplant' },\n        { emoji: '🥑', name: 'avocado' },\n        { emoji: '🥦', name: 'broccoli' },\n        { emoji: '🥬', name: 'leafy greens' },\n        { emoji: '🥒', name: 'cucumber' },\n        { emoji: '🌶️', name: 'hot pepper' },\n        { emoji: '🫑', name: 'bell pepper' },\n        { emoji: '🌽', name: 'ear of corn' },\n      ],\n    },\n    {\n      id: 'activities',\n      name: 'Activities',\n      icon: '⚽',\n      emojis: [\n        { emoji: '⚽', name: 'soccer ball' },\n        { emoji: '🏀', name: 'basketball' },\n        { emoji: '🏈', name: 'american football' },\n        { emoji: '⚾', name: 'baseball' },\n        { emoji: '🥎', name: 'softball' },\n        { emoji: '🎾', name: 'tennis' },\n        { emoji: '🏐', name: 'volleyball' },\n        { emoji: '🏉', name: 'rugby football' },\n        { emoji: '🥏', name: 'flying disc' },\n        { emoji: '🎱', name: 'pool 8 ball' },\n        { emoji: '🪀', name: 'yo-yo' },\n        { emoji: '🏓', name: 'ping pong' },\n        { emoji: '🏸', name: 'badminton' },\n        { emoji: '🥅', name: 'goal net' },\n        { emoji: '⛳', name: 'flag in hole' },\n        { emoji: '🪁', name: 'kite' },\n        { emoji: '🏹', name: 'bow and arrow' },\n        { emoji: '🎣', name: 'fishing pole' },\n        { emoji: '🤿', name: 'diving mask' },\n        { emoji: '🥊', name: 'boxing glove' },\n        { emoji: '🥋', name: 'martial arts uniform' },\n        { emoji: '🎽', name: 'running shirt' },\n        { emoji: '🛹', name: 'skateboard' },\n        { emoji: '🛼', name: 'roller skate' },\n      ],\n    },\n    {\n      id: 'objects',\n      name: 'Objects',\n      icon: '💡',\n      emojis: [\n        { emoji: '⌚', name: 'watch' },\n        { emoji: '📱', name: 'mobile phone' },\n        { emoji: '📲', name: 'mobile phone with arrow' },\n        { emoji: '💻', name: 'laptop' },\n        { emoji: '⌨️', name: 'keyboard' },\n        { emoji: '🖥️', name: 'desktop computer' },\n        { emoji: '🖨️', name: 'printer' },\n        { emoji: '🖱️', name: 'computer mouse' },\n        { emoji: '🖲️', name: 'trackball' },\n        { emoji: '🕹️', name: 'joystick' },\n        { emoji: '🗜️', name: 'clamp' },\n        { emoji: '💽', name: 'computer disk' },\n        { emoji: '💾', name: 'floppy disk' },\n        { emoji: '💿', name: 'optical disk' },\n        { emoji: '📀', name: 'dvd' },\n        { emoji: '🧮', name: 'abacus' },\n        { emoji: '🎥', name: 'movie camera' },\n        { emoji: '🎞️', name: 'film frames' },\n        { emoji: '📽️', name: 'film projector' },\n        { emoji: '🎬', name: 'clapper board' },\n        { emoji: '📺', name: 'television' },\n        { emoji: '📷', name: 'camera' },\n        { emoji: '📸', name: 'camera with flash' },\n        { emoji: '📹', name: 'video camera' },\n      ],\n    },\n  ];\n\n  selectedEmojiCategory = this.emojiCategories[0];\n\n  getEmojisForCategory(category: any): any[] {\n    return category?.emojis || [];\n  }\n\n  selectEmojiCategory(category: any): void {\n    this.selectedEmojiCategory = category;\n  }\n\n  insertEmoji(emoji: any): void {\n    const currentContent = this.messageForm.get('content')?.value || '';\n    const newContent = currentContent + emoji.emoji;\n    this.messageForm.patchValue({ content: newContent });\n\n    // Fermer le picker après insertion\n    this.showEmojiPicker = false;\n\n    // Focus sur le textarea\n    setTimeout(() => {\n      if (this.messageTextarea) {\n        this.messageTextarea.nativeElement.focus();\n      }\n    }, 100);\n  }\n\n  getFileAcceptTypes(): string {\n    return '*/*';\n  }\n\n  closeAllMenus(): void {\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n  }\n\n  startVideoCall(): void {\n    this.startCall('video' as CallType);\n  }\n\n  startVoiceCall(): void {\n    this.startCall('voice' as CallType);\n  }\n}\n", "<!-- Chat WhatsApp Moderne avec Tailwind CSS -->\n<div\n  class=\"flex flex-col h-screen bg-gradient-to-br from-gray-50 to-green-50 dark:from-gray-900 dark:to-gray-800\"\n>\n  <!-- En-tête -->\n  <header\n    class=\"flex items-center px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm\"\n  >\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      class=\"p-2 mr-3 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n    >\n      <i class=\"fas fa-arrow-left text-gray-600 dark:text-gray-300\"></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div class=\"flex items-center flex-1 min-w-0\">\n      <div class=\"relative mr-3\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-10 h-10 rounded-full object-cover border-2 border-green-500 cursor-pointer hover:scale-105 transition-transform\"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n        />\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full animate-pulse\"\n        ></div>\n      </div>\n\n      <div class=\"flex-1 min-w-0\">\n        <h3 class=\"font-semibold text-gray-900 dark:text-white truncate\">\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n          <div\n            *ngIf=\"isUserTyping\"\n            class=\"flex items-center gap-1 text-green-600\"\n          >\n            <span>En train d'écrire</span>\n            <div class=\"flex gap-1\">\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.1s\"\n              ></div>\n              <div\n                class=\"w-1 h-1 bg-green-600 rounded-full animate-bounce\"\n                style=\"animation-delay: 0.2s\"\n              ></div>\n            </div>\n          </div>\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div class=\"flex items-center gap-2\">\n      <button\n        (click)=\"startVideoCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vidéo\"\n      >\n        <i class=\"fas fa-video\"></i>\n      </button>\n      <button\n        (click)=\"startVoiceCall()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Appel vocal\"\n      >\n        <i class=\"fas fa-phone\"></i>\n      </button>\n      <button\n        (click)=\"toggleSearch()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        [class.bg-green-100]=\"searchMode\"\n        [class.text-green-600]=\"searchMode\"\n        title=\"Rechercher\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n      <button\n        (click)=\"toggleMainMenu()\"\n        class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n        title=\"Menu\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n  </header>\n\n  <!-- Zone de messages -->\n  <main\n    class=\"flex-1 overflow-y-auto p-4 space-y-4\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n  >\n    <!-- Chargement -->\n    <div *ngIf=\"loading\" class=\"flex flex-col items-center justify-center py-8\">\n      <div\n        class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mb-4\"\n      ></div>\n      <span class=\"text-gray-500 dark:text-gray-400\"\n        >Chargement des messages...</span\n      >\n    </div>\n\n    <!-- État vide -->\n    <div\n      *ngIf=\"!loading && messages.length === 0\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"text-6xl text-gray-300 dark:text-gray-600 mb-4\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3 class=\"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n        Aucun message\n      </h3>\n      <p class=\"text-gray-500 dark:text-gray-400 text-center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages -->\n    <div *ngIf=\"!loading && messages.length > 0\" class=\"space-y-2\">\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Séparateur de date -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          class=\"flex justify-center my-4\"\n        >\n          <div\n            class=\"bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-sm\"\n          >\n            <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message -->\n        <div\n          class=\"flex\"\n          [class.justify-end]=\"message.sender?.id === currentUserId\"\n          [class.justify-start]=\"message.sender?.id !== currentUserId\"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar pour les autres -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            class=\"mr-2 flex-shrink-0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              class=\"w-8 h-8 rounded-full object-cover cursor-pointer hover:scale-105 transition-transform\"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n            />\n          </div>\n\n          <!-- Bulle de message -->\n          <div\n            class=\"max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm relative group\"\n            [class.bg-green-500]=\"message.sender?.id === currentUserId\"\n            [class.text-white]=\"message.sender?.id === currentUserId\"\n            [class.bg-white]=\"message.sender?.id !== currentUserId\"\n            [class.text-gray-900]=\"message.sender?.id !== currentUserId\"\n            [class.dark:bg-gray-700]=\"message.sender?.id !== currentUserId\"\n            [class.dark:text-white]=\"message.sender?.id !== currentUserId\"\n          >\n            <!-- Nom expéditeur (groupes) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              class=\"text-xs font-semibold mb-1 opacity-75\"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Contenu texte -->\n            <div *ngIf=\"getMessageType(message) === 'text'\" class=\"break-words\">\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image -->\n            <div *ngIf=\"hasImage(message)\" class=\"space-y-2\">\n              <div\n                class=\"relative cursor-pointer rounded-lg overflow-hidden\"\n                (click)=\"openImageViewer(message)\"\n              >\n                <img\n                  [src]=\"getImageUrl(message)\"\n                  [alt]=\"message.content || 'Image'\"\n                  class=\"max-w-full h-auto rounded-lg hover:opacity-90 transition-opacity\"\n                />\n                <div\n                  class=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all flex items-center justify-center\"\n                >\n                  <i\n                    class=\"fas fa-expand text-white opacity-0 group-hover:opacity-100 transition-opacity\"\n                  ></i>\n                </div>\n              </div>\n              <div\n                *ngIf=\"message.content\"\n                class=\"text-sm\"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Fichier -->\n            <div\n              *ngIf=\"hasFile(message)\"\n              class=\"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors\"\n              (click)=\"downloadFile(message)\"\n            >\n              <div class=\"text-2xl text-gray-500 dark:text-gray-400\">\n                <i [class]=\"getFileIcon(message)\"></i>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <div class=\"font-medium text-sm truncate\">\n                  {{ getFileName(message) }}\n                </div>\n                <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ getFileSize(message) }}\n                </div>\n              </div>\n              <button\n                class=\"p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-400 transition-colors\"\n              >\n                <i class=\"fas fa-download text-sm\"></i>\n              </button>\n            </div>\n\n            <!-- Métadonnées -->\n            <div\n              class=\"flex items-center justify-end gap-1 mt-1 text-xs opacity-75\"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                class=\"flex items-center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double text-blue-400\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n\n            <!-- Réactions -->\n            <div\n              *ngIf=\"message.reactions && message.reactions.length > 0\"\n              class=\"flex gap-1 mt-2\"\n            >\n              <button\n                *ngFor=\"let reaction of message.reactions\"\n                (click)=\"toggleReaction(message.id!, reaction.emoji)\"\n                class=\"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors\"\n                [class.bg-green-100]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n                [class.text-green-600]=\"\n                  hasUserReacted(reaction, currentUserId || '')\n                \"\n              >\n                <span>{{ reaction.emoji }}</span>\n                <span>1</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Indicateur de frappe -->\n      <div *ngIf=\"isTyping\" class=\"flex items-start gap-2\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          class=\"w-8 h-8 rounded-full object-cover\"\n        />\n        <div class=\"bg-white dark:bg-gray-700 px-4 py-2 rounded-2xl shadow-sm\">\n          <div class=\"flex gap-1\">\n            <div class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.1s\"\n            ></div>\n            <div\n              class=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n              style=\"animation-delay: 0.2s\"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- Zone d'input -->\n  <footer\n    class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"flex items-end gap-3\"\n    >\n      <!-- Actions gauche -->\n      <div class=\"flex gap-2\">\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showEmojiPicker\"\n          [class.text-green-600]=\"showEmojiPicker\"\n          title=\"Émojis\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          class=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors\"\n          [class.bg-green-100]=\"showAttachmentMenu\"\n          [class.text-green-600]=\"showAttachmentMenu\"\n          title=\"Joindre un fichier\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n      </div>\n\n      <!-- Champ de saisie -->\n      <div class=\"flex-1\">\n        <textarea\n          formControlName=\"content\"\n          #messageTextarea\n          placeholder=\"Tapez votre message...\"\n          class=\"w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400\"\n          [disabled]=\"!otherParticipant || isRecordingVoice || isSendingMessage\"\n          (input)=\"onInputChange($event)\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (focus)=\"onInputFocus()\"\n          (blur)=\"onInputBlur()\"\n          rows=\"1\"\n          maxlength=\"4096\"\n          autocomplete=\"off\"\n          spellcheck=\"true\"\n        >\n        </textarea>\n      </div>\n\n      <!-- Actions droite -->\n      <div class=\"flex gap-2\">\n        <!-- Enregistrement vocal -->\n        <button\n          *ngIf=\"!messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (mousedown)=\"startVoiceRecording()\"\n          (mouseup)=\"stopVoiceRecording()\"\n          (mouseleave)=\"cancelVoiceRecording()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors\"\n          [class.bg-red-500]=\"isRecordingVoice\"\n          [class.hover:bg-red-600]=\"isRecordingVoice\"\n          [class.animate-pulse]=\"isRecordingVoice\"\n          title=\"Maintenir pour enregistrer\"\n        >\n          <i class=\"fas fa-microphone\"></i>\n        </button>\n\n        <!-- Bouton d'envoi -->\n        <button\n          *ngIf=\"messageForm.get('content')?.value?.trim()\"\n          type=\"button\"\n          (click)=\"sendMessage()\"\n          class=\"p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50\"\n          [disabled]=\"isSendingMessage\"\n          title=\"Envoyer\"\n        >\n          <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n          <i class=\"fas fa-spinner fa-spin\" *ngIf=\"isSendingMessage\"></i>\n        </button>\n      </div>\n    </form>\n  </footer>\n\n  <!-- Sélecteur d'émojis -->\n  <div\n    *ngIf=\"showEmojiPicker\"\n    class=\"absolute bottom-20 left-4 right-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"flex gap-2 mb-4 overflow-x-auto\">\n        <button\n          *ngFor=\"let category of emojiCategories\"\n          (click)=\"selectEmojiCategory(category)\"\n          class=\"px-3 py-2 rounded-lg text-sm font-medium transition-colors flex-shrink-0\"\n          [class.bg-green-100]=\"selectedEmojiCategory === category\"\n          [class.text-green-600]=\"selectedEmojiCategory === category\"\n          [class.hover:bg-gray-100]=\"selectedEmojiCategory !== category\"\n          [class.dark:hover:bg-gray-700]=\"selectedEmojiCategory !== category\"\n        >\n          {{ category.icon }}\n        </button>\n      </div>\n      <div class=\"grid grid-cols-8 gap-2 max-h-48 overflow-y-auto\">\n        <button\n          *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n          (click)=\"insertEmoji(emoji)\"\n          class=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-xl\"\n          [title]=\"emoji.name\"\n        >\n          {{ emoji.emoji }}\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Menu des pièces jointes -->\n  <div\n    *ngIf=\"showAttachmentMenu\"\n    class=\"absolute bottom-20 left-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n  >\n    <div class=\"p-4\">\n      <div class=\"grid grid-cols-2 gap-3\">\n        <button\n          (click)=\"triggerFileInput('image')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-image text-blue-600 dark:text-blue-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Photos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('video')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-video text-purple-600 dark:text-purple-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Vidéos</span\n          >\n        </button>\n        <button\n          (click)=\"triggerFileInput('document')\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-file text-orange-600 dark:text-orange-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Documents</span\n          >\n        </button>\n        <button\n          (click)=\"openCamera()\"\n          class=\"flex flex-col items-center gap-2 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n        >\n          <div\n            class=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\"\n          >\n            <i class=\"fas fa-camera text-green-600 dark:text-green-400\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >Caméra</span\n          >\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Input caché pour fichiers -->\n  <input\n    #fileInput\n    type=\"file\"\n    class=\"hidden\"\n    (change)=\"onFileSelected($event)\"\n    [accept]=\"getFileAcceptTypes()\"\n    multiple\n  />\n\n  <!-- Overlay pour fermer les menus -->\n  <div\n    *ngIf=\"showEmojiPicker || showAttachmentMenu\"\n    class=\"fixed inset-0 bg-black bg-opacity-25 z-40\"\n    (click)=\"closeAllMenus()\"\n  ></div>\n</div>\n"], "mappings": ";AAWA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,YAAY,QAAQ,MAAM;;;;;;;;;;;;;ICa3BC,EAAA,CAAAC,SAAA,cAGO;;;;;IAQLD,EAAA,CAAAE,cAAA,cAGC;IACOF,EAAA,CAAAG,MAAA,6BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9BJ,EAAA,CAAAE,cAAA,cAAwB;IACtBF,EAAA,CAAAC,SAAA,cAEO;IASTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAERJ,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IALLJ,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAC,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;IA+CNX,EAAA,CAAAE,cAAA,cAA4E;IAC1EF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,eACG;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAC5B;;;;;IAIHJ,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAC,SAAA,YAA+B;IACjCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAAwE;IACtEF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAAwD;IACtDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,wCAAAM,MAAA,CAAAJ,gBAAA,kBAAAI,MAAA,CAAAJ,gBAAA,CAAAK,QAAA,MACF;;;;;IAaEb,EAAA,CAAAE,cAAA,cAGC;IAKKF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAQ,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAcFjB,EAAA,CAAAE,cAAA,cAGC;IAOGF,EAAA,CAAAkB,UAAA,mBAAAC,+EAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,IAAA;MAAA,MAAAL,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAD,OAAA,CAAAE,eAAA,CAAAV,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,CAAoC;IAAA,EAAC;IANhD5B,EAAA,CAAAI,YAAA,EAOE;;;;IANAJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAG,KAAA,yCAAA9B,EAAA,CAAA+B,aAAA,CAEC,QAAAf,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAd,QAAA;;;;;IAkBHb,EAAA,CAAAE,cAAA,cAQC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAgC,WAAA,UAAAC,OAAA,CAAAC,YAAA,CAAAlB,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,EAAiD;IAEjD5B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAU,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAd,QAAA,MACF;;;;;IAGAb,EAAA,CAAAE,cAAA,cAAoE;IAClEF,EAAA,CAAAC,SAAA,cAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IADCJ,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA6B,UAAA,cAAAM,OAAA,CAAAC,oBAAA,CAAApB,WAAA,CAAAqB,OAAA,GAAArC,EAAA,CAAAsC,cAAA,CAAmD;;;;;IAsBxDtC,EAAA,CAAAC,SAAA,cAIO;;;;;IADLD,EAAA,CAAA6B,UAAA,cAAAU,OAAA,CAAAH,oBAAA,CAAApB,WAAA,CAAAqB,OAAA,GAAArC,EAAA,CAAAsC,cAAA,CAAmD;;;;;;IArBvDtC,EAAA,CAAAE,cAAA,cAAiD;IAG7CF,EAAA,CAAAkB,UAAA,mBAAAsB,+EAAA;MAAAxC,EAAA,CAAAoB,aAAA,CAAAqB,IAAA;MAAA,MAAAzB,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAmB,OAAA,GAAA1C,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAiB,OAAA,CAAAC,eAAA,CAAA3B,WAAA,CAAwB;IAAA,EAAC;IAElChB,EAAA,CAAAC,SAAA,cAIE;IACFD,EAAA,CAAAE,cAAA,cAEC;IACCF,EAAA,CAAAC,SAAA,YAEK;IACPD,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAA4C,UAAA,IAAAC,+DAAA,kBAIO;IACT7C,EAAA,CAAAI,YAAA,EAAM;;;;;IAjBAJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA6B,UAAA,QAAAiB,OAAA,CAAAC,WAAA,CAAA/B,WAAA,GAAAhB,EAAA,CAAA+B,aAAA,CAA4B,QAAAf,WAAA,CAAAqB,OAAA;IAa7BrC,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAAqB,OAAA,CAAqB;;;;;;IAO1BrC,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAkB,UAAA,mBAAA8B,+EAAA;MAAAhD,EAAA,CAAAoB,aAAA,CAAA6B,IAAA;MAAA,MAAAjC,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAA2B,OAAA,GAAAlD,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAyB,OAAA,CAAAC,YAAA,CAAAnC,WAAA,CAAqB;IAAA,EAAC;IAE/BhB,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAAC,SAAA,QAAsC;IACxCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAA4B;IAExBF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAAsD;IACpDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAE,cAAA,iBAEC;IACCF,EAAA,CAAAC,SAAA,YAAuC;IACzCD,EAAA,CAAAI,YAAA,EAAS;;;;;IAdJJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAoD,UAAA,CAAAC,OAAA,CAAAC,WAAA,CAAAtC,WAAA,EAA8B;IAI/BhB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+C,OAAA,CAAAE,WAAA,CAAAvC,WAAA,OACF;IAEEhB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA+C,OAAA,CAAAG,WAAA,CAAAxC,WAAA,OACF;;;;;IAkBAhB,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,YAIK;;;;;IACLD,EAAA,CAAAC,SAAA,YAIK;;;;;IAvBPD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAA4C,UAAA,IAAAa,8DAAA,gBAIK;IACLzD,EAAA,CAAA4C,UAAA,IAAAc,8DAAA,gBAIK;IACL1D,EAAA,CAAA4C,UAAA,IAAAe,8DAAA,gBAIK;IACL3D,EAAA,CAAA4C,UAAA,IAAAgB,8DAAA,gBAIK;IACP5D,EAAA,CAAAI,YAAA,EAAM;;;;IAlBDJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,eAAkC;IAKlC7D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,YAA+B;IAK/B7D,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,iBAAoC;IAKpC7D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA6C,MAAA,YAA+B;;;;;;IAWpC7D,EAAA,CAAAE,cAAA,kBAUC;IARCF,EAAA,CAAAkB,UAAA,mBAAA4C,4FAAA;MAAA,MAAAC,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAA4C,IAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAAxC,SAAA;MAAA,MAAAP,WAAA,GAAAhB,EAAA,CAAAsB,aAAA,IAAAC,SAAA;MAAA,MAAA2C,OAAA,GAAAlE,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAyC,OAAA,CAAAC,cAAA,CAAAnD,WAAA,CAAAY,EAAA,EAAAqC,YAAA,CAAAG,KAAA,CAA2C;IAAA,EAAC;IASrDpE,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,QAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IARdJ,EAAA,CAAAqE,WAAA,iBAAAC,OAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAK,OAAA,CAAAE,aAAA,QAEC,mBAAAF,OAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAK,OAAA,CAAAE,aAAA;IAKKxE,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAyE,iBAAA,CAAAR,YAAA,CAAAG,KAAA,CAAoB;;;;;IAf9BpE,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAA4C,UAAA,IAAA8B,mEAAA,sBAaS;IACX1E,EAAA,CAAAI,YAAA,EAAM;;;;IAbmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA6B,UAAA,YAAAb,WAAA,CAAA2D,SAAA,CAAoB;;;;;;IAjKnD3E,EAAA,CAAA4E,uBAAA,GAMC;IAEC5E,EAAA,CAAA4C,UAAA,IAAAiC,yDAAA,kBAWM;IAGN7E,EAAA,CAAAE,cAAA,cAOC;IAFCF,EAAA,CAAAkB,UAAA,mBAAA4D,yEAAAC,MAAA;MAAA,MAAAhB,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAA4D,IAAA;MAAA,MAAAhE,WAAA,GAAA+C,WAAA,CAAAxC,SAAA;MAAA,MAAA0D,OAAA,GAAAjF,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAwD,OAAA,CAAAC,cAAA,CAAAlE,WAAA,EAAA+D,MAAA,CAA+B;IAAA,EAAC,yBAAAI,+EAAAJ,MAAA;MAAA,MAAAhB,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAA4D,IAAA;MAAA,MAAAhE,WAAA,GAAA+C,WAAA,CAAAxC,SAAA;MAAA,MAAA6D,OAAA,GAAApF,EAAA,CAAAsB,aAAA;MAAA,OAC1BtB,EAAA,CAAAyB,WAAA,CAAA2D,OAAA,CAAAC,oBAAA,CAAArE,WAAA,EAAA+D,MAAA,CAAqC;IAAA,EADX;IAIzC/E,EAAA,CAAA4C,UAAA,IAAA0C,yDAAA,kBAYM;IAGNtF,EAAA,CAAAE,cAAA,cAQC;IAECF,EAAA,CAAA4C,UAAA,IAAA2C,yDAAA,kBAUM;IAGNvF,EAAA,CAAA4C,UAAA,IAAA4C,yDAAA,kBAEM;IAGNxF,EAAA,CAAA4C,UAAA,IAAA6C,yDAAA,kBAuBM;IAGNzF,EAAA,CAAA4C,UAAA,IAAA8C,yDAAA,mBAqBM;IAGN1F,EAAA,CAAAE,cAAA,cAEC;IACOF,EAAA,CAAAG,MAAA,IAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAA4C,UAAA,KAAA+C,0DAAA,kBAwBM;IACR3F,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAA4C,UAAA,KAAAgD,0DAAA,kBAkBM;IACR5F,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAA6F,qBAAA,EAAe;;;;;;IAxKV7F,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAejChG,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAqE,WAAA,iBAAArD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,CAA0D,mBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA;IAE1DxE,EAAA,CAAA6B,UAAA,oBAAAb,WAAA,CAAAY,EAAA,CAA8B;IAM3B5B,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAA6B,UAAA,UAAAb,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,IAAAsB,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAgBlEhG,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAqE,WAAA,kBAAArD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,CAA2D,gBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,eAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,oBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,uBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,sBAAAxD,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA;IASxDxE,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAI,mBAAA,OAAAlF,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,IAAAsB,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAOkBhG,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAM,cAAA,CAAApF,WAAA,aAAwC;IAKxChB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAO,QAAA,CAAArF,WAAA,EAAuB;IA2B1BhB,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,SAAAiE,OAAA,CAAAQ,OAAA,CAAAtF,WAAA,EAAsB;IA0BjBhB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAyE,iBAAA,CAAAqB,OAAA,CAAAS,iBAAA,CAAAvF,WAAA,CAAAC,SAAA,EAA0C;IAE7CjB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA6B,UAAA,UAAAb,WAAA,CAAAW,MAAA,kBAAAX,WAAA,CAAAW,MAAA,CAAAC,EAAA,MAAAkE,OAAA,CAAAtB,aAAA,CAA0C;IA4B5CxE,EAAA,CAAAK,SAAA,GAAuD;IAAvDL,EAAA,CAAA6B,UAAA,SAAAb,WAAA,CAAA2D,SAAA,IAAA3D,WAAA,CAAA2D,SAAA,CAAA6B,MAAA,KAAuD;;;;;IAuBhExG,EAAA,CAAAE,cAAA,eAAqD;IACnDF,EAAA,CAAAC,SAAA,eAIE;IACFD,EAAA,CAAAE,cAAA,eAAuE;IAEnEF,EAAA,CAAAC,SAAA,eAAmE;IASrED,EAAA,CAAAI,YAAA,EAAM;;;;IAfNJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAA6B,UAAA,SAAA4E,OAAA,CAAAjG,gBAAA,kBAAAiG,OAAA,CAAAjG,gBAAA,CAAAsB,KAAA,yCAAA9B,EAAA,CAAA+B,aAAA,CAAqE,QAAA0E,OAAA,CAAAjG,gBAAA,kBAAAiG,OAAA,CAAAjG,gBAAA,CAAAK,QAAA;;;;;IAvL3Eb,EAAA,CAAAE,cAAA,cAA+D;IAC7DF,EAAA,CAAA4C,UAAA,IAAA8D,mDAAA,6BAiLe;IAGf1G,EAAA,CAAA4C,UAAA,IAAA+D,0CAAA,kBAmBM;IACR3G,EAAA,CAAAI,YAAA,EAAM;;;;IAtMuBJ,EAAA,CAAAK,SAAA,GACZ;IADYL,EAAA,CAAA6B,UAAA,YAAA+E,MAAA,CAAAC,QAAA,CACZ,iBAAAD,MAAA,CAAAE,gBAAA;IAiLT9G,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA6B,UAAA,SAAA+E,MAAA,CAAAG,QAAA,CAAc;;;;;;IA+ElB/G,EAAA,CAAAE,cAAA,kBAWC;IARCF,EAAA,CAAAkB,UAAA,uBAAA8F,oEAAA;MAAAhH,EAAA,CAAAoB,aAAA,CAAA6F,IAAA;MAAA,MAAAC,OAAA,GAAAlH,EAAA,CAAAsB,aAAA;MAAA,OAAatB,EAAA,CAAAyB,WAAA,CAAAyF,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC,qBAAAC,kEAAA;MAAApH,EAAA,CAAAoB,aAAA,CAAA6F,IAAA;MAAA,MAAAI,OAAA,GAAArH,EAAA,CAAAsB,aAAA;MAAA,OACxBtB,EAAA,CAAAyB,WAAA,CAAA4F,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EADI,wBAAAC,qEAAA;MAAAvH,EAAA,CAAAoB,aAAA,CAAA6F,IAAA;MAAA,MAAAO,OAAA,GAAAxH,EAAA,CAAAsB,aAAA;MAAA,OAErBtB,EAAA,CAAAyB,WAAA,CAAA+F,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAFD;IASnCzH,EAAA,CAAAC,SAAA,aAAiC;IACnCD,EAAA,CAAAI,YAAA,EAAS;;;;IANPJ,EAAA,CAAAqE,WAAA,eAAAqD,MAAA,CAAAC,gBAAA,CAAqC,qBAAAD,MAAA,CAAAC,gBAAA,mBAAAD,MAAA,CAAAC,gBAAA;;;;;IAiBrC3H,EAAA,CAAAC,SAAA,aAA4D;;;;;IAC5DD,EAAA,CAAAC,SAAA,aAA+D;;;;;;IATjED,EAAA,CAAAE,cAAA,kBAOC;IAJCF,EAAA,CAAAkB,UAAA,mBAAA0G,gEAAA;MAAA5H,EAAA,CAAAoB,aAAA,CAAAyG,IAAA;MAAA,MAAAC,OAAA,GAAA9H,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAqG,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvB/H,EAAA,CAAA4C,UAAA,IAAAoF,2CAAA,iBAA4D;IAC5DhI,EAAA,CAAA4C,UAAA,IAAAqF,2CAAA,iBAA+D;IACjEjI,EAAA,CAAAI,YAAA,EAAS;;;;IALPJ,EAAA,CAAA6B,UAAA,aAAAqG,MAAA,CAAAC,gBAAA,CAA6B;IAGEnI,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,UAAAqG,MAAA,CAAAC,gBAAA,CAAuB;IACnBnI,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,SAAAqG,MAAA,CAAAC,gBAAA,CAAsB;;;;;;IAa3DnI,EAAA,CAAAE,cAAA,kBAQC;IANCF,EAAA,CAAAkB,UAAA,mBAAAkH,sEAAA;MAAA,MAAArE,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAAiH,IAAA;MAAA,MAAAC,YAAA,GAAAvE,WAAA,CAAAxC,SAAA;MAAA,MAAAgH,OAAA,GAAAvI,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAA8G,OAAA,CAAAC,mBAAA,CAAAF,YAAA,CAA6B;IAAA,EAAC;IAOvCtI,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IANPJ,EAAA,CAAAqE,WAAA,iBAAAoE,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,CAAyD,mBAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,uBAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA,4BAAAG,OAAA,CAAAC,qBAAA,KAAAJ,YAAA;IAKzDtI,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAgI,YAAA,CAAAK,IAAA,MACF;;;;;;IAGA3I,EAAA,CAAAE,cAAA,kBAKC;IAHCF,EAAA,CAAAkB,UAAA,mBAAA0H,sEAAA;MAAA,MAAA7E,WAAA,GAAA/D,EAAA,CAAAoB,aAAA,CAAAyH,IAAA;MAAA,MAAAC,SAAA,GAAA/E,WAAA,CAAAxC,SAAA;MAAA,MAAAwH,OAAA,GAAA/I,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAsH,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAI5B9I,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAHPJ,EAAA,CAAA6B,UAAA,UAAAiH,SAAA,CAAAG,IAAA,CAAoB;IAEpBjJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwI,SAAA,CAAA1E,KAAA,MACF;;;;;IA1BNpE,EAAA,CAAAE,cAAA,eAGC;IAGKF,EAAA,CAAA4C,UAAA,IAAAsG,6CAAA,sBAUS;IACXlJ,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA6D;IAC3DF,EAAA,CAAA4C,UAAA,IAAAuG,6CAAA,sBAOS;IACXnJ,EAAA,CAAAI,YAAA,EAAM;;;;IApBmBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA6B,UAAA,YAAAuH,OAAA,CAAAC,eAAA,CAAkB;IAarBrJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAA6B,UAAA,YAAAuH,OAAA,CAAAE,oBAAA,CAAAF,OAAA,CAAAV,qBAAA,EAA8C;;;;;;IAYxE1I,EAAA,CAAAE,cAAA,eAGC;IAIOF,EAAA,CAAAkB,UAAA,mBAAAqI,6DAAA;MAAAvJ,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAC,OAAA,GAAAzJ,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAgI,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC1J,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,aAA6D;IAC/DD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBACG;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,kBAGC;IAFCF,EAAA,CAAAkB,UAAA,mBAAAyI,6DAAA;MAAA3J,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAI,OAAA,GAAA5J,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAmI,OAAA,CAAAF,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAGnC1J,EAAA,CAAAE,cAAA,eAEC;IACCF,EAAA,CAAAC,SAAA,cAAiE;IACnED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAkB,UAAA,mBAAA2I,8DAAA;MAAA7J,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAM,OAAA,GAAA9J,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAqI,OAAA,CAAAJ,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAGtC1J,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EACX;IAEHJ,EAAA,CAAAE,cAAA,mBAGC;IAFCF,EAAA,CAAAkB,UAAA,mBAAA6I,8DAAA;MAAA/J,EAAA,CAAAoB,aAAA,CAAAoI,IAAA;MAAA,MAAAQ,OAAA,GAAAhK,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAuI,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAGtBjK,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAC,SAAA,cAAgE;IAClED,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBACG;IAAAF,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EACR;;;;;;IAiBTJ,EAAA,CAAAE,cAAA,eAIC;IADCF,EAAA,CAAAkB,UAAA,mBAAAgJ,0DAAA;MAAAlK,EAAA,CAAAoB,aAAA,CAAA+I,IAAA;MAAA,MAAAC,OAAA,GAAApK,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAA2I,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1BrK,EAAA,CAAAI,YAAA,EAAM;;;AD3fT,OAAM,MAAOkK,oBAAoB;EAoV/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,cAA8B,EAC9BC,YAA0B,EAC1BC,EAAe,EACfC,GAAsB,EACtBC,iBAAoC;IAPpC,KAAAP,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,iBAAiB,GAAjBA,iBAAiB;IArV3B;IACA,KAAAlE,QAAQ,GAAc,EAAE;IAExB,KAAAmE,YAAY,GAAwB,IAAI;IACxC,KAAAC,OAAO,GAAG,IAAI;IAEd,KAAAzG,aAAa,GAAkB,IAAI;IACnC,KAAA0G,eAAe,GAAW,KAAK;IAC/B,KAAA1K,gBAAgB,GAAgB,IAAI;IACpC,KAAA2K,OAAO,GAAG,KAAK;IACf,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,UAAU,GAAgC,IAAI;IAC9C,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAvE,QAAQ,GAAG,KAAK;IAEhB,KAAAY,gBAAgB,GAAG,KAAK;IACxB,KAAA4D,sBAAsB,GAAG,CAAC;IAE1B;IACiB,KAAAC,oBAAoB,GAAG,EAAE;IAClC,KAAAC,WAAW,GAAG,CAAC;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACd,KAAAC,aAAa,GAAG,IAAI7L,YAAY,EAAE;IAE1C;IACA,KAAA8L,aAAa,GAAW,eAAe;IACvC,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,SAAS,GAAQ,IAAI;IAErB;IACA,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,uBAAuB,GAAW,CAAC;IACnC,KAAAC,qBAAqB,GAAgB,IAAIC,GAAG,EAAE;IAC9C,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,iBAAiB,GAAQ,IAAI;IAE7B;IACA,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,kBAAkB,GAAQ,EAAE;IAC5B,KAAAC,iBAAiB,GAAQ,EAAE;IAC3B,KAAAC,cAAc,GAAQ,EAAE;IACxB,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,kBAAkB,GAAQ,EAAE;IAE5B;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,qBAAqB,GAAa,EAAE;IACpC,KAAAC,sBAAsB,GAAU,EAAE;IAClC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAtF,eAAe,GAAU,CACvB;MAAEzH,EAAE,EAAE,QAAQ;MAAEqH,IAAI,EAAE,SAAS;MAAEN,IAAI,EAAE;IAAc,CAAE,EACvD;MAAE/G,EAAE,EAAE,SAAS;MAAEqH,IAAI,EAAE,SAAS;MAAEN,IAAI,EAAE;IAAc,CAAE,EACxD;MAAE/G,EAAE,EAAE,QAAQ;MAAEqH,IAAI,EAAE,WAAW;MAAEN,IAAI,EAAE;IAAa,CAAE,EACxD;MAAE/G,EAAE,EAAE,SAAS;MAAEqH,IAAI,EAAE,SAAS;MAAEN,IAAI,EAAE;IAAY,CAAE,EACtD;MAAE/G,EAAE,EAAE,MAAM;MAAEqH,IAAI,EAAE,YAAY;MAAEN,IAAI,EAAE;IAAkB,CAAE,EAC5D;MAAE/G,EAAE,EAAE,QAAQ;MAAEqH,IAAI,EAAE,QAAQ;MAAEN,IAAI,EAAE;IAAc,CAAE,EACtD;MAAE/G,EAAE,EAAE,YAAY;MAAEqH,IAAI,EAAE,WAAW;MAAEN,IAAI,EAAE;IAAsB,CAAE,EACrE;MAAE/G,EAAE,EAAE,SAAS;MAAEqH,IAAI,EAAE,QAAQ;MAAEN,IAAI,EAAE;IAAkB,CAAE,EAC3D;MAAE/G,EAAE,EAAE,SAAS;MAAEqH,IAAI,EAAE,UAAU;MAAEN,IAAI,EAAE;IAAc,CAAE,EACzD;MAAE/G,EAAE,EAAE,OAAO;MAAEqH,IAAI,EAAE,UAAU;MAAEN,IAAI,EAAE;IAAa,CAAE,CACvD;IACD,KAAAD,qBAAqB,GAAG,QAAQ;IAChC,KAAAkG,gBAAgB,GAAG,EAAE;IACrB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,mBAAmB,GAAG,EAAE;IAExB;IACA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,aAAa,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,CAAC;IAC7E,KAAAC,mBAAmB,GAAG,UAAU;IAEhC;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,aAAa,GAAG,CACd,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,KAAAC,oBAAoB,GAAG,SAAS;IAChC,KAAAC,kBAAkB,GAAG,SAAS;IAC9B,KAAAC,WAAW,GAAG,CAAC;IAEf;IACA,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,OAAO;IACpB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,MAAM,GAAG,CAAC;IACV,KAAAC,MAAM,GAAG,CAAC;IACV,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAE1B;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,eAAe,GAAG,MAAM;IACxB,KAAAC,QAAQ,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAG,CAAE;IAClD,KAAAC,SAAS,GAAG,MAAM;IAClB,KAAAC,YAAY,GAAU,CACpB;MAAE7H,IAAI,EAAE,MAAM;MAAE8H,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAM,CAAE,EAC7C;MAAE/H,IAAI,EAAE,WAAW;MAAE8H,KAAK,EAAE,cAAc;MAAEC,GAAG,EAAE;IAAiB,CAAE,EACpE;MAAE/H,IAAI,EAAE,OAAO;MAAE8H,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAa,CAAE,EACrD;MAAE/H,IAAI,EAAE,SAAS;MAAE8H,KAAK,EAAE,SAAS;MAAEC,GAAG,EAAE;IAA0B,CAAE,EACtE;MAAE/H,IAAI,EAAE,QAAQ;MAAE8H,KAAK,EAAE,UAAU;MAAEC,GAAG,EAAE;IAAiB,CAAE,EAC7D;MAAE/H,IAAI,EAAE,UAAU;MAAE8H,KAAK,EAAE,WAAW;MAAEC,GAAG,EAAE;IAAe,CAAE,CAC/D;IACD,KAAAC,mBAAmB,GAAG,MAAM;IAC5B,KAAAC,gBAAgB,GAAG;MAAEC,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAC,CAAE;IACxE,KAAAC,iBAAiB,GAAU,EAAE;IAC7B,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,cAAc,GAAG,OAAO;IACxB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,SAAS,GAAG,SAAS;IACrB,KAAAC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE,OAAO;MAAEd,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEc,KAAK,EAAE,WAAW;MAAEd,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEc,KAAK,EAAE,iBAAiB;MAAEd,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAEc,KAAK,EAAE,aAAa;MAAEd,KAAK,EAAE;IAAa,CAAE,CAC/C;IAED;IACA,KAAAe,eAAe,GAAG,KAAK;IACvB,KAAAC,YAAY,GAAG,MAAM;IACrB,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,UAAU,GAAG,MAAM;IACnB,KAAAC,aAAa,GAAa,EAAE;IAE5B;IACA,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,kBAAkB,GAAG,IAAI;IACzB,KAAAC,aAAa,GAAQ;MACnBC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,CAAC;MACdC,eAAe,EAAE,IAAI;MACrBC,WAAW,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC;MACdC,kBAAkB,EAAE,CAAC;MACrBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,mBAAmB,EAAE,CAAC;MACtBC,uBAAuB,EAAE,IAAI;MAC7BC,SAAS,EAAE;KACZ;IAED;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,qBAAqB,GAAG,CACtB,KAAK,EACL,cAAc,EACd,eAAe,EACf,WAAW,EACX,UAAU,CACX;IACD,KAAAC,2BAA2B,GAAG,KAAK;IACnC,KAAAC,QAAQ,GAAU,EAAE;IAEpB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,YAAY,GAAa,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC;IACxE,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,kBAAkB,GAAU,EAAE;IAC9B,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,kBAAkB,GAAU,EAAE;IAC9B,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,kBAAkB,GAAU,EAAE;IAC9B,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,gBAAgB,GAAG,IAAI;IACvB,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,0BAA0B,GAAG,KAAK;IAClC,KAAAC,sBAAsB,GAAU,EAAE;IAElC;IACA,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,eAAe,GAAG,MAAM;IACxB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,0BAA0B,GAAG,EAAE;IAC/B,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,IAAI,EAAE,IAAI;MAAErM,IAAI,EAAE;IAAU,CAAE,EAChC;MAAEqM,IAAI,EAAE,IAAI;MAAErM,IAAI,EAAE;IAAS,CAAE,EAC/B;MAAEqM,IAAI,EAAE,IAAI;MAAErM,IAAI,EAAE;IAAS,CAAE,EAC/B;MAAEqM,IAAI,EAAE,IAAI;MAAErM,IAAI,EAAE;IAAS,CAAE,EAC/B;MAAEqM,IAAI,EAAE,IAAI;MAAErM,IAAI,EAAE;IAAU,CAAE,CACjC;IAED;IACA,KAAAsM,eAAe,GAAG,KAAK;IACvB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,WAAW,GAAU,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,EAAE;MAAEA,IAAI,EAAE;IAAE,CAAE,CAAC;IACjD,KAAAC,YAAY,GAAG;MACbC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE;KACb;IACD,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,gBAAgB,GAAG,EAAE;IAErB;IACA,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,mBAAmB,GAAG,EAAE;IACxB,KAAAC,qBAAqB,GAAU,EAAE;IACjC,KAAAC,gBAAgB,GAAQ,IAAI;IAC5B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,eAAe,GAAG,EAAE;IAEpB;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,yBAAyB,GAAQ,IAAI;IAErC;IACA,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,gBAAgB,GAAG,cAAc;IACjC,KAAAC,kBAAkB,GAAG,CACnB;MAAEnF,KAAK,EAAE,cAAc;MAAEd,KAAK,EAAE;IAAa,CAAE,EAC/C;MAAEc,KAAK,EAAE,kBAAkB;MAAEd,KAAK,EAAE;IAAgB,CAAE,EACtD;MAAEc,KAAK,EAAE,YAAY;MAAEd,KAAK,EAAE;IAAa,CAAE,CAC9C;IAED;IACA,KAAAkG,oBAAoB,GAAG,EAAE;IACzB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,gBAAgB,GAAgB,IAAI/J,GAAG,EAAE;IACzC,KAAAgK,gBAAgB,GAAG,EAAE;IACrB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,mBAAmB,GAAG,CAAC;IAEvB;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,YAAY,GAAU,EAAE;IAExB;IACA,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,SAAS,GAAG,MAAM;IAElB;IACA,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,oBAAoB,GAAG;MACrB/K,aAAa,EAAE,IAAI;MACnBgL,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAE,IAAI;MAClBC,gBAAgB,EAAE;KACnB;IAED;IACA,KAAAjQ,gBAAgB,GAAG,KAAK;IACxB,KAAAkQ,YAAY,GAAG,KAAK;IACpB,KAAAC,mBAAmB,GAAG,MAAM;IAC5B,KAAAC,kBAAkB,GAAG,CAAC;IACtB,KAAAC,iBAAiB,GAAa,EAAE;IAChC,KAAAC,qBAAqB,GAAG,QAAQ;IAChC,KAAAC,YAAY,GAAa,EAAE;IAC3B,KAAAC,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,YAAY,GAAa,EAAE;IAC3B,KAAAC,kBAAkB,GAAa,EAAE;IAEjC;IACA,KAAAC,cAAc,GAAG,EAAE;IAiBnB;IACS,KAAAC,CAAC,GAAG;MACXvU,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC/CwU,MAAM,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAG,CAAE;MAC9DC,KAAK,EAAE,6BAA6B;MACpCC,SAAS,EAAEA,CAACC,CAAS,EAAEC,IAAS,KAAaA,IAAI,EAAE/X,EAAE,IAAI8X,CAAC,CAACE,QAAQ,EAAE;MACrE1M,aAAa,EAAE;QACb2M,WAAW,EAAE;UAAElR,IAAI,EAAE,gBAAgB;UAAEmR,KAAK,EAAE;QAAe,CAAE;QAC/DC,WAAW,EAAE;UAAEpR,IAAI,EAAE,oBAAoB;UAAEmR,KAAK,EAAE;QAAc,CAAE;QAClEE,MAAM,EAAE;UAAErR,IAAI,EAAE,YAAY;UAAEmR,KAAK,EAAE;QAAe;OACrD;MACDjW,MAAM,EAAE;QACNoW,MAAM,EAAE;UACNvE,IAAI,EAAE,UAAU;UAChBoE,KAAK,EAAE,gBAAgB;UACvBnR,IAAI,EAAE;SACP;QACDuR,OAAO,EAAE;UACPxE,IAAI,EAAE,YAAY;UAClBoE,KAAK,EAAE,eAAe;UACtBnR,IAAI,EAAE;SACP;QACDyQ,IAAI,EAAE;UAAE1D,IAAI,EAAE,QAAQ;UAAEoE,KAAK,EAAE,iBAAiB;UAAEnR,IAAI,EAAE;QAAc,CAAE;QACxEwR,IAAI,EAAE;UACJzE,IAAI,EAAE,QAAQ;UACdoE,KAAK,EAAE,cAAc;UACrBnR,IAAI,EAAE;;OAET;MACDyR,MAAM,EAAE,CACN;QAAEC,GAAG,EAAE,eAAe;QAAEtJ,KAAK,EAAE,QAAQ;QAAE+I,KAAK,EAAE;MAAS,CAAE,EAC3D;QAAEO,GAAG,EAAE,gBAAgB;QAAEtJ,KAAK,EAAE,MAAM;QAAE+I,KAAK,EAAE;MAAS,CAAE,EAC1D;QAAEO,GAAG,EAAE,iBAAiB;QAAEtJ,KAAK,EAAE,MAAM;QAAE+I,KAAK,EAAE;MAAS,CAAE,EAC3D;QAAEO,GAAG,EAAE,eAAe;QAAEtJ,KAAK,EAAE,MAAM;QAAE+I,KAAK,EAAE;MAAS,CAAE,CAC1D;MACDQ,KAAK,EAAE;QACLC,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;;KAEb;IA8cO,KAAAC,iBAAiB,GAAa,EAAE;IAsDxC;IACQ,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IA8lDlC;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IAkBpB,KAAAC,kBAAkB,GAAG,EAAE;IAsRvB,KAAAC,gBAAgB,GAAG,IAAI;IAscvB;IACA,KAAA7R,eAAe,GAAG,CAChB;MACEzH,EAAE,EAAE,SAAS;MACbqH,IAAI,EAAE,SAAS;MACfN,IAAI,EAAE,IAAI;MACVwS,MAAM,EAAE,CACN;QAAE/W,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAuB,CAAE,EAC9C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAkB,CAAE,EACzC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACrC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA8B,CAAE,EACrD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAqB,CAAE,EAC5C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACrC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAoB,CAAE,EAC3C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAkB,CAAE,EACzC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA4B,CAAE,EACnD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAA0B,CAAE;KAEpD,EACD;MACErH,EAAE,EAAE,QAAQ;MACZqH,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVwS,MAAM,EAAE,CACN;QAAE/W,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAoB,CAAE,EAC3C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACrC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAW,CAAE,EAClC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAiB,CAAE,EACxC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgB,CAAE,EACvC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAqB,CAAE,EAC5C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAqB,CAAE,EAC5C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAqB,CAAE,EAC5C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAqB,CAAE,EAC5C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAoB,CAAE,EAC3C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAkB,CAAE,EACzC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgB,CAAE,EACvC;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAW,CAAE;KAEtC,EACD;MACErH,EAAE,EAAE,QAAQ;MACZqH,IAAI,EAAE,QAAQ;MACdN,IAAI,EAAE,IAAI;MACVwS,MAAM,EAAE,CACN;QAAE/W,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAoB,CAAE,EAC3C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAqB,CAAE,EAC5C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAsB,CAAE,EAC7C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgB,CAAE;KAE1C,EACD;MACErH,EAAE,EAAE,MAAM;MACVqH,IAAI,EAAE,MAAM;MACZN,IAAI,EAAE,IAAI;MACVwS,MAAM,EAAE,CACN;QAAE/W,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAW,CAAE,EAClC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAW,CAAE,EAClC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAW,CAAE,EAClC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAS,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACrC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE;KAEvC,EACD;MACErH,EAAE,EAAE,YAAY;MAChBqH,IAAI,EAAE,YAAY;MAClBN,IAAI,EAAE,GAAG;MACTwS,MAAM,EAAE,CACN;QAAE/W,KAAK,EAAE,GAAG;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAmB,CAAE,EAC1C;QAAE7E,KAAK,EAAE,GAAG;QAAE6E,IAAI,EAAE;MAAU,CAAE,EAChC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAgB,CAAE,EACvC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAW,CAAE,EAClC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAW,CAAE,EAClC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,GAAG;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACrC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACrC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAsB,CAAE,EAC7C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE;KAExC,EACD;MACErH,EAAE,EAAE,SAAS;MACbqH,IAAI,EAAE,SAAS;MACfN,IAAI,EAAE,IAAI;MACVwS,MAAM,EAAE,CACN;QAAE/W,KAAK,EAAE,GAAG;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC7B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACrC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAU,CAAE,EACjC;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAkB,CAAE,EAC1C;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAS,CAAE,EACjC;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAgB,CAAE,EACxC;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAW,CAAE,EACnC;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAU,CAAE,EAClC;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAO,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACpC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACrC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE,EACrC;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAa,CAAE,EACrC;QAAE7E,KAAK,EAAE,KAAK;QAAE6E,IAAI,EAAE;MAAgB,CAAE,EACxC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAe,CAAE,EACtC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAY,CAAE,EACnC;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAmB,CAAE,EAC1C;QAAE7E,KAAK,EAAE,IAAI;QAAE6E,IAAI,EAAE;MAAc,CAAE;KAExC,CACF;IAED,KAAAP,qBAAqB,GAAG,IAAI,CAACW,eAAe,CAAC,CAAC,CAAC;IAjkG7C,IAAI,CAAC+R,WAAW,GAAG,IAAI,CAACvQ,EAAE,CAACwQ,KAAK,CAAC;MAC/BhZ,OAAO,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAACwb,QAAQ,EAAExb,UAAU,CAACyb,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EA4CA;EACA,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACtC,CAAC,CAACvU,SAAS;EACzB;EACA,IAAI8W,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC9Q,cAAc,CAAC+Q,eAAe,EAAE;EAC9C;EACA,IAAIC,wBAAwBA,CAAA;IAC1B,OAAO,IAAI,CAACxO,uBAAuB;EACrC;EACA,IAAIyO,iBAAiBA,CAAA;IACnB,OAAO,QAAQ;EACjB;EACA,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,CAAC;EACV;EAEA;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACpQ,aAAa,CAACqQ,WAAW,EAAE;IAChC,IAAI,IAAI,CAAChP,SAAS,EAAEiP,aAAa,CAAC,IAAI,CAACjP,SAAS,CAAC;IACjD,IAAI,IAAI,CAACkP,aAAa,EAAEC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;EAC1D;EAEAE,kBAAkBA,CAAA;IAChB,IAAI,CAACvR,GAAG,CAACwR,aAAa,EAAE;EAC1B;EAEA;EACQP,mBAAmBA,CAAA;IACzB,IAAI,CAACQ,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,kBAAkB,EAAE;IACzB;IACA,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQF,gBAAgBA,CAAA;IACtB,IAAI,CAAChS,KAAK,CAACmS,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACrC,MAAME,cAAc,GAAGF,MAAM,CAAC,IAAI,CAAC;MACnC,IAAIE,cAAc,EAAE;QAClB,IAAI,CAACC,eAAe,CAACD,cAAc,CAAC;;IAExC,CAAC,CAAC;EACJ;EAEQJ,kBAAkBA,CAAA;IACxB;EAAA;EAGF;EACAK,eAAeA,CAACD,cAAsB;IACpC,IAAI,CAAC5R,OAAO,GAAG,IAAI;IACnB,IAAI,CAACN,cAAc,CAACmS,eAAe,CACjCD,cAAc,EACd,IAAI,CAACrR,oBAAoB,EACzB,IAAI,CAACC,WAAW,CACjB,CAACmR,SAAS,CAAC;MACVG,IAAI,EAAG/R,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACxK,gBAAgB,GACnBwK,YAAY,CAACgS,YAAY,EAAEC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACtb,EAAE,KAAK,IAAI,CAAC4C,aAAa,CAAC,IACnE,IAAI;QAEN;QACA,IAAIwG,YAAY,CAACnE,QAAQ,EAAE;UACzB,IAAI,CAACA,QAAQ,GAAGmE,YAAY,CAACnE,QAAQ;UACrC,IAAI,CAAC8E,eAAe,GAClBX,YAAY,CAACnE,QAAQ,CAACL,MAAM,KAAK,IAAI,CAACgF,oBAAoB;;QAG9D,IAAI,CAACP,OAAO,GAAG,KAAK;QACpB,IAAI,CAACkS,cAAc,EAAE;MACvB,CAAC;MACD3D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACvO,OAAO,GAAG,KAAK;QACpBmS,OAAO,CAAC5D,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACvE;KACD,CAAC;EACJ;EAEA;EAEAzR,WAAWA,CAAA;IACT,MAAM1F,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,EAAEyL,IAAI,EAAE;IAC9D,IAAI,CAACjb,OAAO,IAAI,CAAC,IAAI,CAAC2I,YAAY,EAAEpJ,EAAE,EAAE;IAExC,IAAI,CAACuG,gBAAgB,GAAG,IAAI;IAE5B;IACA,MAAMoV,WAAW,GAAY;MAC3B3b,EAAE,EAAE4b,IAAI,CAACC,GAAG,EAAE,CAAC7D,QAAQ,EAAE;MACzBvX,OAAO;MACPV,MAAM,EAAE;QAAEC,EAAE,EAAE,IAAI,CAAC4C,aAAc;QAAE3D,QAAQ,EAAE,IAAI,CAACqK;MAAe,CAAE;MACnEjK,SAAS,EAAE,IAAIuc,IAAI,EAAE;MACrBX,cAAc,EAAE,IAAI,CAAC7R,YAAY,CAACpJ,EAAE;MACpC8b,IAAI,EAAE;KACP;IAED,IAAI,CAAC7W,QAAQ,CAAC8W,IAAI,CAACJ,WAAW,CAAC;IAC/B,IAAI,CAACnC,WAAW,CAACwC,KAAK,EAAE;IACxB,IAAI,CAACT,cAAc,EAAE;IACrB,IAAI,CAAChV,gBAAgB,GAAG,KAAK;IAE7B;IACA;;;;;;;;;;;;;;;;;;;;;;EAsBF;EAEA;EACA0V,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAACvX,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACsS,eAAe,GAAGmF,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC;MACxC,IAAI,CAACI,gBAAgB,EAAE;;EAE3B;EAEAC,gBAAgBA,CAACC,KAAa;IAC5B,IAAI,CAACvF,eAAe,CAACwF,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACrC,IAAI,IAAI,CAACvF,eAAe,CAACtS,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC6E,UAAU,GAAG,IAAI;;EAE1B;EAEA;EACAlH,cAAcA,CAACoa,SAAiB,EAAEna,KAAa;IAC7C;IACA,MAAMoa,OAAO,GAAG,IAAI,CAAC3X,QAAQ,CAACoW,IAAI,CAAEwB,CAAC,IAAKA,CAAC,CAAC7c,EAAE,KAAK2c,SAAS,CAAC;IAC7D,IAAIC,OAAO,EAAE;MACX,IAAI,CAAEA,OAAe,CAAC7Z,SAAS,EAAG6Z,OAAe,CAAC7Z,SAAS,GAAG,EAAE;MAChE,MAAM+Z,gBAAgB,GAAIF,OAAe,CAAC7Z,SAAS,CAACsY,IAAI,CACrD0B,CAAM,IAAKA,CAAC,CAACva,KAAK,KAAKA,KAAK,CAC9B;MACD,IAAIsa,gBAAgB,EAAE;QACpBA,gBAAgB,CAACE,KAAK,GAAG,CAACF,gBAAgB,CAACE,KAAK,IAAI,CAAC,IAAI,CAAC;OAC3D,MAAM;QACJJ,OAAe,CAAC7Z,SAAS,CAACgZ,IAAI,CAAC;UAC9BvZ,KAAK;UACLwa,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,CAAC,IAAI,CAACra,aAAa;SAC3B,CAAC;;;EAGR;EAEAsa,WAAWA,CAACP,SAAiB,EAAEna,KAAa;IAC1C,IAAI,CAACD,cAAc,CAACoa,SAAS,EAAEna,KAAK,CAAC;IACrC,IAAI,CAAC6J,kBAAkB,GAAG,EAAE;EAC9B;EAEA;EACA8Q,gBAAgBA,CAACP,OAAY;IAC3B,IAAI,CAACjR,gBAAgB,GAAGiR,OAAO,CAAC5c,EAAE;IAClC,IAAI,CAAC4L,cAAc,GAAGgR,OAAO,CAACnc,OAAO,IAAI,EAAE;IAC3C,IAAI,CAACgM,kBAAkB,GAAG,EAAE;EAC9B;EAEA2Q,UAAUA,CAAA;IACR,IAAI,CAACzR,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B;EAEAyR,QAAQA,CAACV,SAAiB;IACxB,IAAI,IAAI,CAAC/Q,cAAc,EAAE8P,IAAI,EAAE,EAAE;MAC/B,IAAI,CAAC3S,cAAc,CAACuU,WAAW,CAC7BX,SAAS,EACT,IAAI,CAAC/Q,cAAc,CAAC8P,IAAI,EAAE,CAC3B,CAACV,SAAS,CAAC;QACVG,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACiC,UAAU,EAAE;UACjB;UACA,IAAI,IAAI,CAAChU,YAAY,EAAEpJ,EAAE,EAAE;YACzB,IAAI,CAACkb,eAAe,CAAC,IAAI,CAAC9R,YAAY,CAACpJ,EAAE,CAAC;;QAE9C,CAAC;QACD4X,KAAK,EAAGA,KAAK,IAAI;UACf4D,OAAO,CAAC5D,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACzD;OACD,CAAC;;EAEN;EAEA2F,aAAaA,CAACrB,KAAoB,EAAES,SAAiB;IACnD,IAAIT,KAAK,CAACzD,GAAG,KAAK,OAAO,IAAI,CAACyD,KAAK,CAACsB,QAAQ,EAAE;MAC5CtB,KAAK,CAACuB,cAAc,EAAE;MACtB,IAAI,CAACJ,QAAQ,CAACV,SAAS,CAAC;KACzB,MAAM,IAAIT,KAAK,CAACzD,GAAG,KAAK,QAAQ,EAAE;MACjC,IAAI,CAAC2E,UAAU,EAAE;;EAErB;EAEA;EACAM,aAAaA,CAACf,SAAiB;IAC7B,IAAIgB,OAAO,CAAC,iDAAiD,CAAC,EAAE;MAC9D,IAAI,CAAC5U,cAAc,CAAC2U,aAAa,CAACf,SAAS,CAAC,CAAC3B,SAAS,CAAC;QACrDG,IAAI,EAAEA,CAAA,KAAK;UACT;UACA,IAAI,IAAI,CAAC/R,YAAY,EAAEpJ,EAAE,EAAE;YACzB,IAAI,CAACkb,eAAe,CAAC,IAAI,CAAC9R,YAAY,CAACpJ,EAAE,CAAC;;UAE5C,IAAI,CAACyM,kBAAkB,GAAG,EAAE;QAC9B,CAAC;QACDmL,KAAK,EAAGA,KAAK,IAAI;UACf4D,OAAO,CAAC5D,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;OACD,CAAC;;EAEN;EAEA;EACAgG,SAASA,CAAC9B,IAAc;IACtB,IAAI,CAAC,IAAI,CAACld,gBAAgB,EAAEoB,EAAE,EAAE;IAEhC,IAAI,CAAC+I,cAAc,CAAC8U,YAAY,CAAC,IAAI,CAACjf,gBAAgB,CAACoB,EAAE,EAAE8b,IAAI,CAAC,CAACd,SAAS,CAAC;MACzEG,IAAI,EAAG2C,IAAI,IAAI;QACb,IAAI,CAAC/S,UAAU,GAAG+S,IAAI;QACtB,IAAI,CAAC7S,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAAC8S,cAAc,EAAE;MACvB,CAAC;MACDnG,KAAK,EAAGA,KAAK,IAAI;QACf4D,OAAO,CAAC5D,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC5O,YAAY,CAACgV,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEQD,cAAcA,CAAA;IACpB,IAAI,CAAC3S,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAG4S,WAAW,CAAC,MAAK;MAChC,IAAI,CAAC7S,YAAY,EAAE;IACrB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA8S,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC7S,SAAS,EAAE;MAClBiP,aAAa,CAAC,IAAI,CAACjP,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAEvB,IAAI,CAACN,UAAU,GAAG,IAAI;IACtB,IAAI,CAACE,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACG,YAAY,GAAG,CAAC;EACvB;EAEA+S,UAAUA,CAAA;IACR,IAAI,CAACjT,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;EACtC;EAEAkT,WAAWA,CAAA;IACT,IAAI,CAACjT,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA;EACAoQ,cAAcA,CAAA;IACZ8C,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa;QACpDD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA/Z,iBAAiBA,CAACtF,SAAc;IAC9B,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,MAAMsf,IAAI,GAAG,IAAI/C,IAAI,CAACvc,SAAS,CAAC;IAChC,OAAOsf,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAC,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAC7B,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,MAAMpH,CAAC,GAAGqH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,KAAK,CAAC,GAAGG,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAGG,IAAI,CAACI,GAAG,CAACN,CAAC,EAAEnH,CAAC,CAAC,EAAE0H,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACpH,CAAC,CAAC;EACzE;EAEA5S,gBAAgBA,CAACuX,KAAa,EAAEG,OAAY;IAC1C,OAAOA,OAAO,EAAE5c,EAAE,IAAIyc,KAAK,CAACzE,QAAQ,EAAE;EACxC;EAEA;EACAyH,YAAYA,CAAA;IACV,IAAI,CAACvT,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB,IAAI,CAACwT,WAAW,EAAE;;EAEtB;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACnV,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEAoV,sBAAsBA,CAAA;IACpB,IAAI,CAACzJ,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;EACxD;EAEA0J,mBAAmBA,CAAA;IACjB,IAAI,CAAC3V,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAEA4V,cAAcA,CAAA;IACZ,IAAI,CAAC3V,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA4V,oBAAoBA,CAAA;IAClB,IAAI,CAAC5T,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA6T,oBAAoBA,CAAA;IAClB,IAAI,CAACzV,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA0V,qBAAqBA,CAAA;IACnB,IAAI,CAACvV,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA;EAEQwV,aAAaA,CAAA;IACnB,IAAI,CAACnU,WAAW,GAAG,IAAI;IACvB;IACAsS,UAAU,CAAC,MAAK;MACd,IAAI,CAACpS,aAAa,GAAG,IAAI,CAAChH,QAAQ,CAACkb,MAAM,CAAEvD,OAAO,IAChDA,OAAO,CAACnc,OAAO,EAAE2f,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvU,WAAW,CAACsU,WAAW,EAAE,CAAC,CACxE;MACD,IAAI,CAACrU,WAAW,GAAG,KAAK;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA2T,WAAWA,CAAA;IACT,IAAI,CAAC5T,WAAW,GAAG,EAAE;IACrB,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACqJ,eAAe,GAAG,EAAE;EAC3B;EAEAgL,mBAAmBA,CAAC7f,OAAe;IACjC,IAAI,CAAC,IAAI,CAACqL,WAAW,IAAI,CAACrL,OAAO,EAAE,OAAOA,OAAO;IACjD,MAAM8f,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAI,IAAI,CAAC1U,WAAW,GAAG,EAAE,IAAI,CAAC;IACvD,OAAOrL,OAAO,CAACggB,OAAO,CACpBF,KAAK,EACL,mDAAmD,CACpD;EACH;EAEAG,eAAeA,CAAC/D,SAAiB;IAC/B,IAAI,CAACtH,oBAAoB,GAAGsH,SAAS;IACrC;IACA0B,UAAU,CAAC,MAAK;MACd,IAAI,CAAChJ,oBAAoB,GAAG,EAAE;IAChC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACAsL,qBAAqBA,CAAA;IACnB;EAAA;EAGFC,mBAAmBA,CAAA;IACjB,IAAI,CAACxU,cAAc,GAAG,EAAE;EAC1B;EAEAyU,gBAAgBA,CAAClE,SAAiB;IAChC,MAAMC,OAAO,GAAG,IAAI,CAAC3X,QAAQ,CAACoW,IAAI,CAAEwB,CAAC,IAAKA,CAAC,CAAC7c,EAAE,KAAK2c,SAAS,CAAC;IAC7D,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACkE,MAAM,GAAG,CAAClE,OAAO,CAACkE,MAAM;MAChC,IAAIlE,OAAO,CAACkE,MAAM,EAAE;QAClB,IAAI,CAAC1U,cAAc,CAAC2P,IAAI,CAACa,OAAO,CAAC;OAClC,MAAM;QACL,IAAI,CAACxQ,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC+T,MAAM,CAC7CtD,CAAC,IAAKA,CAAC,CAAC7c,EAAE,KAAK2c,SAAS,CAC1B;;;IAGL,IAAI,CAAClQ,kBAAkB,GAAG,EAAE;EAC9B;EAEA;EACAsU,oBAAoBA,CAACpE,SAAiB;IACpC,IAAI,IAAI,CAAClQ,kBAAkB,CAACkQ,SAAS,CAAC,EAAE;MACtC,IAAI,CAAClQ,kBAAkB,GAAG,EAAE;KAC7B,MAAM;MACL,IAAI,CAACA,kBAAkB,GAAG;QAAE,CAACkQ,SAAS,GAAG;MAAI,CAAE;;EAEnD;EAEAqE,oBAAoBA,CAACrE,SAAiB;IACpC,IAAI,IAAI,CAACtQ,kBAAkB,CAACsQ,SAAS,CAAC,EAAE;MACtC,IAAI,CAACtQ,kBAAkB,GAAG,EAAE;KAC7B,MAAM;MACL,IAAI,CAACA,kBAAkB,GAAG;QAAE,CAACsQ,SAAS,GAAG;MAAI,CAAE;;EAEnD;EAEA;EACAsE,cAAcA,CAACrE,OAAY;IACzB,IAAI,CAAC/Q,iBAAiB,GAAG+Q,OAAO;IAChC,IAAI,CAACnQ,kBAAkB,GAAG,EAAE;EAC9B;EAEAyU,cAAcA,CAACtE,OAAY;IACzB,IAAI,CAACjQ,iBAAiB,GAAGiQ,OAAO;IAChC,IAAI,CAAClQ,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACD,kBAAkB,GAAG,EAAE;EAC9B;EAEA0U,WAAWA,CAACvE,OAAY;IACtB,IAAIwE,SAAS,CAACC,SAAS,IAAIzE,OAAO,CAACnc,OAAO,EAAE;MAC1C2gB,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1E,OAAO,CAACnc,OAAO,CAAC;MAC9C,IAAI,CAACuI,YAAY,CAACuY,WAAW,CAAC,eAAe,CAAC;;IAEhD,IAAI,CAAC9U,kBAAkB,GAAG,EAAE;EAC9B;EAEA+U,aAAaA,CAAC7E,SAAiB;IAC7B,IAAI,CAACpH,aAAa,GAAG,IAAI;IACzB,IAAI,CAACkM,sBAAsB,CAAC9E,SAAS,CAAC;IACtC,IAAI,CAAClQ,kBAAkB,GAAG,EAAE;EAC9B;EAEAgV,sBAAsBA,CAAC9E,SAAiB;IACtC,IAAI,IAAI,CAACnH,gBAAgB,CAACkM,GAAG,CAAC/E,SAAS,CAAC,EAAE;MACxC,IAAI,CAACnH,gBAAgB,CAACmM,MAAM,CAAChF,SAAS,CAAC;KACxC,MAAM;MACL,IAAI,CAACnH,gBAAgB,CAACoM,GAAG,CAACjF,SAAS,CAAC;;IAGtC,IAAI,IAAI,CAACnH,gBAAgB,CAACqM,IAAI,KAAK,CAAC,EAAE;MACpC,IAAI,CAACtM,aAAa,GAAG,KAAK;;EAE9B;EAIA;EACAuM,eAAeA,CAACnF,SAAkB;IAChC,IAAI,CAACvS,eAAe,GAAG,IAAI;IAC3B,IAAIuS,SAAS,EAAE;MACb,IAAI,CAACtQ,kBAAkB,GAAG;QAAE,CAACsQ,SAAS,GAAG;MAAI,CAAE;;EAEnD;EAEAoF,gBAAgBA,CAAA;IACd,IAAI,CAAC3X,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACiC,kBAAkB,GAAG,EAAE;EAC9B;EAEAzF,mBAAmBA,CAACob,UAAkB;IACpC,IAAI,CAAClb,qBAAqB,GAAGkb,UAAU;EACzC;EAEAC,iBAAiBA,CAACzf,KAAa;IAC7B,MAAM0f,cAAc,GAAG,IAAI,CAAC1I,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IACnE,IAAI,CAACuJ,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAE0G,QAAQ,CAACD,cAAc,GAAG1f,KAAK,CAAC;EACnE;EAEA;EACA4f,oBAAoBA,CAAA;IAClB,IAAI,CAACnL,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEAoL,aAAaA,CAAA;IACX,IAAI,CAACC,SAAS,CAAC9D,aAAa,CAAC+D,KAAK,EAAE;EACtC;EAEA;EACAxhB,eAAeA,CAAC6b,OAAY;IAC1B,IAAI,CAAC/G,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,YAAY,GAAG,CAAC6G,OAAO,CAAC;EAC/B;EAEA4F,gBAAgBA,CAAA;IACd,IAAI,CAAC3M,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACE,YAAY,GAAG,EAAE;EACxB;EAEA;EACA0M,mBAAmBA,CAAC9F,SAAiB;IACnC,IAAI,IAAI,CAACjH,cAAc,KAAKiH,SAAS,EAAE;MACrC,IAAI,CAACjH,cAAc,GAAG,EAAE;KACzB,MAAM;MACL,IAAI,CAACA,cAAc,GAAGiH,SAAS;;EAEnC;EAOMpX,mBAAmBA,CAAA;IAAA,IAAAmd,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI;QACF;QACA,MAAMC,MAAM,SAASxB,SAAS,CAACyB,YAAY,CAACC,YAAY,CAAC;UACvDC,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE;;SAEpB,CAAC;QAEFR,KAAI,CAAC3J,aAAa,GAAG,IAAIoK,aAAa,CAACP,MAAM,EAAE;UAC7CQ,QAAQ,EAAE;SACX,CAAC;QAEFV,KAAI,CAAC1J,WAAW,GAAG,EAAE;QACrB0J,KAAI,CAAC3c,gBAAgB,GAAG,IAAI;QAC5B2c,KAAI,CAAC/Y,sBAAsB,GAAG,CAAC;QAC/B+Y,KAAI,CAAChM,mBAAmB,GAAG,WAAW;QAEtC;QACAgM,KAAI,CAACzJ,cAAc,GAAGgF,WAAW,CAAC,MAAK;UACrCyE,KAAI,CAAC/Y,sBAAsB,EAAE;UAC7B+Y,KAAI,CAACxZ,GAAG,CAACwR,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACAgI,KAAI,CAAC3J,aAAa,CAACsK,eAAe,GAAInH,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAACoH,IAAI,CAACzB,IAAI,GAAG,CAAC,EAAE;YACvBa,KAAI,CAAC1J,WAAW,CAAC+C,IAAI,CAACG,KAAK,CAACoH,IAAI,CAAC;;QAErC,CAAC;QAED;QACAZ,KAAI,CAAC3J,aAAa,CAACwK,MAAM,GAAG,MAAK;UAC/Bb,KAAI,CAACc,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACAd,KAAI,CAAC3J,aAAa,CAAC0K,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAE/Bf,KAAI,CAAC1Z,YAAY,CAACuY,WAAW,CAAC,8BAA8B,CAAC;OAC9D,CAAC,OAAO3J,KAAK,EAAE;QACd4D,OAAO,CAAC5D,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE8K,KAAI,CAAC1Z,YAAY,CAACgV,SAAS,CAAC,oCAAoC,CAAC;QACjE0E,KAAI,CAAC7c,oBAAoB,EAAE;;IAC5B;EACH;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACqT,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2K,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAAC3K,aAAa,CAAC4K,IAAI,EAAE;MAEzB;MACA,IAAI,CAAC5K,aAAa,CAAC6J,MAAM,CAACgB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAAC1K,cAAc,EAAE;MACvBqB,aAAa,CAAC,IAAI,CAACrB,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAAClT,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC2Q,mBAAmB,GAAG,YAAY;EACzC;EAEA7Q,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACkT,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAAC2K,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAC3K,aAAa,CAAC4K,IAAI,EAAE;;MAE3B,IAAI,CAAC5K,aAAa,CAAC6J,MAAM,CAACgB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;MACtE,IAAI,CAAC5K,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvBqB,aAAa,CAAC,IAAI,CAACrB,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAAClT,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC4D,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAAC+M,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACsC,WAAW,GAAG,EAAE;EACvB;EAEcwK,oBAAoBA,CAAA;IAAA,IAAAO,MAAA;IAAA,OAAApB,iBAAA;MAChC,IAAI;QACF,IAAIoB,MAAI,CAAC/K,WAAW,CAACpU,MAAM,KAAK,CAAC,EAAE;UACjCmf,MAAI,CAACle,oBAAoB,EAAE;UAC3B;;QAGF;QACA,MAAMme,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAAC/K,WAAW,EAAE;UAC3C8C,IAAI,EAAE;SACP,CAAC;QAEF;QACA,IAAIiI,MAAI,CAACpa,sBAAsB,GAAG,CAAC,EAAE;UACnCoa,MAAI,CAAC/a,YAAY,CAACkb,WAAW,CAC3B,+CAA+C,CAChD;UACDH,MAAI,CAACle,oBAAoB,EAAE;UAC3B;;QAGF;QACA,MAAMse,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACJ,SAAS,CAAC,EAAE,SAASpI,IAAI,CAACC,GAAG,EAAE,OAAO,EAAE;UAClEC,IAAI,EAAE;SACP,CAAC;QAEF;QACA,MAAMiI,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtCJ,MAAI,CAAC/a,YAAY,CAACuY,WAAW,CAAC,sBAAsB,CAAC;OACtD,CAAC,OAAO3J,KAAK,EAAE;QACd4D,OAAO,CAAC5D,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7DmM,MAAI,CAAC/a,YAAY,CAACgV,SAAS,CAAC,yCAAyC,CAAC;OACvE,SAAS;QACR+F,MAAI,CAACrN,mBAAmB,GAAG,MAAM;QACjCqN,MAAI,CAACpa,sBAAsB,GAAG,CAAC;QAC/Boa,MAAI,CAAC/K,WAAW,GAAG,EAAE;;IACtB;EACH;EAEcqL,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAA3B,iBAAA;MAC5C,IAAI;QACF,IAAI,CAAC2B,MAAI,CAAClb,YAAY,EAAEpJ,EAAE,EAAE;UAC1B,MAAM,IAAIukB,KAAK,CAAC,kCAAkC,CAAC;;QAGrD;QACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEP,SAAS,CAAC;QAClCK,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;QAChCF,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,MAAI,CAAC3a,sBAAsB,CAACqO,QAAQ,EAAE,CAAC;QAEnE;QACAsM,MAAI,CAACvb,cAAc,CAACsb,gBAAgB,CAClCC,MAAI,CAAClb,YAAY,CAACpJ,EAAE,EACpBmkB,SAAS,EACTG,MAAI,CAAC3a,sBAAsB,CAACqO,QAAQ,EAAE,CACvC,CAACgD,SAAS,CAAC;UACVG,IAAI,EAAGyB,OAAO,IAAI;YAChB;YACA0H,MAAI,CAACrf,QAAQ,CAAC8W,IAAI,CAACa,OAAO,CAAC;YAC3B0H,MAAI,CAAC/I,cAAc,EAAE;UACvB,CAAC;UACD3D,KAAK,EAAGA,KAAK,IAAI;YACf4D,OAAO,CAAC5D,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChE,MAAMA,KAAK;UACb;SACD,CAAC;OACH,CAAC,OAAOA,KAAK,EAAE;QACd,MAAMA,KAAK;;IACZ;EACH;EAEA+M,uBAAuBA,CAACC,QAAgB;IACtC,MAAMC,OAAO,GAAG1F,IAAI,CAACC,KAAK,CAACwF,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAME,OAAO,GAAGF,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGC,OAAO,IAAIC,OAAO,CAAC9M,QAAQ,EAAE,CAAC+M,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACAC,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAChb,aAAa,GAAGgb,KAAK;IAC1B,IAAI,CAAC/a,iBAAiB,GAAG,KAAK;EAChC;EAEAgb,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC5N,CAAC,CAACkB,MAAM;EACtB;EAEA;EACA2M,gBAAgBA,CAACljB,MAAc;IAC7B,IAAI,CAAC+U,gBAAgB,GAAG,IAAI;IAC5B;IACAqH,UAAU,CAAC,MAAK;MACd,IAAI,CAACrH,gBAAgB,GAAG,KAAK;IAC/B,CAAC,EAAE,IAAI,CAAC;EACV;EAEAoO,gBAAgBA,CAAA;IACd,OAAOC,MAAM,CAACC,OAAO,CAAC,IAAI,CAAChO,CAAC,CAACrV,MAAM,CAAC,CAACsjB,GAAG,CAAC,CAAC,CAAC9M,GAAG,EAAExI,KAAK,CAAC,MAAM;MAC1DwI,GAAG;MACH,GAAGxI;KACJ,CAAC,CAAC;EACL;EAEAuV,aAAaA,CAACvjB,MAAc;IAC1B,OACE,IAAI,CAACqV,CAAC,CAACrV,MAAM,CAACA,MAAoC,CAAC,EAAE8E,IAAI,IACzD,eAAe;EAEnB;EAEA0e,cAAcA,CAACxjB,MAAc;IAC3B,OACE,IAAI,CAACqV,CAAC,CAACrV,MAAM,CAACA,MAAoC,CAAC,EAAEiW,KAAK,IAC1D,eAAe;EAEnB;EAEAwN,aAAaA,CAACzjB,MAAc;IAC1B,OACE,IAAI,CAACqV,CAAC,CAACrV,MAAM,CAACA,MAAoC,CAAC,EAAE6R,IAAI,IAAI,SAAS;EAE1E;EAEA;EACAtT,oBAAoBA,CAACC,OAA2B;IAC9C,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IACvB,OAAOA,OAAO,CACXggB,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAChDA,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC,CACpCA,OAAO,CAAC,UAAU,EAAE,iBAAiB,CAAC,CACtCA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;EAC3B;EAEAkF,WAAWA,CAACzJ,KAAU;IACpB;EAAA;EAGF0J,YAAYA,CAAC1J,KAAU;IACrBA,KAAK,CAACE,MAAM,CAACyJ,GAAG,GAAG,+BAA+B;EACpD;EAEAC,aAAaA,CAAC5J,KAAU,EAAEU,OAAa;IACrC;EAAA;EAGF;EACAmJ,sBAAsBA,CAAA;IACpB,IAAI,CAACpV,sBAAsB,GAAG,IAAI;EACpC;EAEAqV,uBAAuBA,CAAA;IACrB,IAAI,CAACrV,sBAAsB,GAAG,KAAK;EACrC;EAEA;EACAsV,gBAAgBA,CAAA;IACd,IAAI,CAACtU,gBAAgB,GAAG,IAAI;EAC9B;EAEAuU,iBAAiBA,CAAA;IACf,IAAI,CAACvU,gBAAgB,GAAG,KAAK;EAC/B;EAEA;EACAwU,gBAAgBA,CAAC1lB,OAAe;IAC9B,MAAM2lB,OAAO,GAAG,IAAI,CAAC5M,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAI2K,OAAO,EAAE;MACXA,OAAO,CAACjE,QAAQ,CAAC1hB,OAAO,CAAC;MACzB,IAAI,CAAC0F,WAAW,EAAE;;EAEtB;EAEA;EACA0X,YAAYA,CAAC/B,IAAc;IACzB,IAAI,CAAC8B,SAAS,CAAC9B,IAAI,CAAC;EACtB;EAEA;EACQS,gBAAgBA,CAAA;IACtB,IAAI,CAACrF,eAAe,CAAC2M,OAAO,CAAEwC,IAAI,IAAI;MACpC,IAAIA,IAAI,CAACvK,IAAI,CAACwK,UAAU,CAAC,QAAQ,CAAC,EAAE;QAClC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;UACpB,IAAI,CAACjd,UAAU,GAAGid,CAAC,CAACtK,MAAM,EAAEuK,MAAM,IAAI,IAAI;QAC5C,CAAC;QACDJ,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;;IAE9B,CAAC,CAAC;EACJ;EAEQ1L,eAAeA,CAAA;IACrB,IAAI;MACF;MACA,MAAMkM,IAAI,GAAG,IAAI,CAAC/d,eAAe,CAACge,cAAc,EAAE;MAClD,IAAID,IAAI,EAAE;QACR,IAAI,CAACjkB,aAAa,GAAGikB,IAAI,CAAC7mB,EAAE,IAAI,IAAI;QACpC,IAAI,CAACsJ,eAAe,GAAGud,IAAI,CAAC5nB,QAAQ,IAAI,KAAK;OAC9C,MAAM;QACL;QACA,IAAI,CAAC2D,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC0G,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAOsO,KAAK,EAAE;MACd4D,OAAO,CAAC5D,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,IAAI,CAAChV,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC0G,eAAe,GAAG,KAAK;;EAEhC;EAEA;EAEA;EACA9E,cAAcA,CAACoY,OAAY;IACzB,IAAIA,OAAO,CAACnc,OAAO,IAAI,CAACmc,OAAO,CAACmK,WAAW,EAAEniB,MAAM,EAAE,OAAO,MAAM;IAClE,IAAIgY,OAAO,CAACmK,WAAW,EAAEC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACnL,IAAI,KAAK,OAAO,CAAC,EAC/D,OAAO,OAAO;IAChB,IAAIc,OAAO,CAACmK,WAAW,EAAEC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACnL,IAAI,KAAK,OAAO,CAAC,EAC/D,OAAO,OAAO;IAChB,IAAIc,OAAO,CAACmK,WAAW,EAAEC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACnL,IAAI,KAAK,MAAM,CAAC,EAC9D,OAAO,MAAM;IACf,IAAIc,OAAO,CAACmK,WAAW,EAAEC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACnL,IAAI,KAAK,OAAO,CAAC,EAC/D,OAAO,OAAO;IAChB,IAAIc,OAAO,CAACd,IAAI,KAAK,UAAU,EAAE,OAAO,UAAU;IAClD,IAAIc,OAAO,CAACd,IAAI,KAAK,SAAS,EAAE,OAAO,SAAS;IAChD,OAAO,MAAM;EACf;EAEA;EACA3a,WAAWA,CAACyb,OAAY;IACtB,OACEA,OAAO,CAACsK,QAAQ,IAChBtK,OAAO,CAACmK,WAAW,EAAE1L,IAAI,CAAE4L,GAAQ,IAAKA,GAAG,CAACnL,IAAI,KAAK,OAAO,CAAC,EAAEqL,GAAG,IAClE,EAAE;EAEN;EAEAC,WAAWA,CAACxK,OAAY;IACtB,OACEA,OAAO,CAACyK,QAAQ,IAChBzK,OAAO,CAACmK,WAAW,EAAE1L,IAAI,CAAE4L,GAAQ,IAAKA,GAAG,CAACnL,IAAI,KAAK,OAAO,CAAC,EAAEqL,GAAG,IAClE,EAAE;EAEN;EAEAG,iBAAiBA,CAAC1K,OAAY;IAC5B,OAAOA,OAAO,CAAC2K,YAAY,IAAI,EAAE;EACnC;EAEAC,gBAAgBA,CAAC5K,OAAY;IAC3B,OAAOA,OAAO,CAACgI,QAAQ,IAAI,OAAO;EACpC;EAEA;EACAhjB,WAAWA,CAACgb,OAAY;IACtB,OAAO,IAAI,CAACmC,cAAc,CAACnC,OAAO,CAACiF,IAAI,IAAI,CAAC,CAAC;EAC/C;EAEAngB,WAAWA,CAACkb,OAAY;IACtB,MAAM6K,QAAQ,GAAG7K,OAAO,CAAC6K,QAAQ,IAAI7K,OAAO,CAACmK,WAAW,GAAG,CAAC,CAAC,EAAEjL,IAAI,IAAI,EAAE;IACzE,MAAM4L,KAAK,GAAQ;MACjB,iBAAiB,EAAE,iBAAiB;MACpC,oBAAoB,EAAE,kBAAkB;MACxC,0BAA0B,EAAE,mBAAmB;MAC/C,+BAA+B,EAAE,wBAAwB;MACzD,OAAO,EAAE,iBAAiB;MAC1B,QAAQ,EAAE,mBAAmB;MAC7B,QAAQ,EAAE,mBAAmB;MAC7B,QAAQ,EAAE;KACX;IAED,KAAK,MAAM,CAAC5L,IAAI,EAAE/U,IAAI,CAAC,IAAIse,MAAM,CAACC,OAAO,CAACoC,KAAK,CAAC,EAAE;MAChD,IAAID,QAAQ,CAACnB,UAAU,CAACxK,IAAI,CAAC,EAAE,OAAO/U,IAAc;;IAEtD,OAAO,aAAa;EACtB;EAEA4gB,WAAWA,CAAC/K,OAAY;IACtB,MAAM6K,QAAQ,GAAG7K,OAAO,CAAC6K,QAAQ,IAAI7K,OAAO,CAACmK,WAAW,GAAG,CAAC,CAAC,EAAEjL,IAAI,IAAI,EAAE;IACzE,OAAO2L,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE,IAAI,SAAS;EAC3D;EAEA;EACAC,mBAAmBA,CAACC,OAAe,EAAEC,KAAa;IAChD,MAAMC,UAAU,GAAInD,OAAe,IAAI;MACrC,MAAMoD,IAAI,GAAG/I,IAAI,CAACC,KAAK,CAAC0F,OAAO,GAAG,EAAE,CAAC;MACrC,MAAMqD,IAAI,GAAGrD,OAAO,GAAG,EAAE;MACzB,OAAO,GAAGoD,IAAI,IAAIC,IAAI,CAACnQ,QAAQ,EAAE,CAAC+M,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IACtD,CAAC;IACD,OAAO,GAAGkD,UAAU,CAACF,OAAO,CAAC,MAAME,UAAU,CAACD,KAAK,CAAC,EAAE;EACxD;EAEAI,gBAAgBA,CAACxL,OAAY;IAC3B,OAAOP,KAAK,CAACC,IAAI,CAAC;MAAE1X,MAAM,EAAE;IAAE,CAAE,EAAE,CAACyjB,CAAC,EAAEvQ,CAAC,MAAM;MAC3C9I,MAAM,EAAEmQ,IAAI,CAACmJ,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;MAC9BC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;KACT,CAAC,CAAC;EACL;EAEAC,gBAAgBA,CAAC9L,SAAiB;IAChC,OAAO,CAAC;EACV;EAEA+L,mBAAmBA,CAAC/L,SAAiB;IACnC,OAAO,CAAC;EACV;EAEAgM,qBAAqBA,CAAChM,SAAiB;IACrC,OAAO,CAAC;EACV;EAEAiM,aAAaA,CAACjM,SAAiB;IAC7B,OAAO,IAAI;EACb;EAEA;EACAkM,cAAcA,CAACjM,OAAY;IACzB,OAAO,KAAK;EACd;EAEAkM,cAAcA,CAACnM,SAAiB;IAC9B,OAAO,KAAK;EACd;EAEAoM,iBAAiBA,CAACnM,OAAY;IAC5B,OAAO,KAAK;EACd;EAEAoM,uBAAuBA,CAACpM,OAAY;IAClC,OAAO,CAAC;EACV;EAEAqM,uBAAuBA,CAACrM,OAAY;IAClC,OAAO,CAAC;EACV;EAEAsM,kBAAkBA,CAACtM,OAAY;IAC7B,OAAO,EAAE;EACX;EAEA;EACArb,YAAYA,CAACqb,OAAY;IACvB;EAAA;EAGFuM,WAAWA,CAACvM,OAAY;IACtB;EAAA;EAGFwM,cAAcA,CAACxM,OAAY;IACzB,MAAMyM,gBAAgB,GAAG,CACvB,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,iBAAiB,CAClB;IACD,OAAOA,gBAAgB,CAACrC,IAAI,CAAElL,IAAI,IAAKc,OAAO,CAACd,IAAI,EAAEwK,UAAU,CAACxK,IAAI,CAAC,CAAC;EACxE;EAEAwN,QAAQA,CAAC1M,OAAY;IACnB,OACEA,OAAO,CAACd,IAAI,KAAK,OAAO,IACvBc,OAAO,CAACmK,WAAW,IAClBnK,OAAO,CAACmK,WAAW,CAACC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACnL,IAAI,KAAK,OAAO,CAAE;EAEnE;EAEAyN,YAAYA,CAAC/mB,KAAa;IACxB,MAAMgnB,UAAU,GAAQ;MACtB,IAAI,EAAE,YAAY;MAClB,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,UAAU;MAChB,IAAI,EAAE,QAAQ;MACd,IAAI,EAAE;KACP;IACD,OAAOA,UAAU,CAAChnB,KAAK,CAAC,IAAIA,KAAK;EACnC;EAEA;EACAinB,kBAAkBA,CAAC7M,OAAY;IAC7B;EAAA;EAGF8M,iBAAiBA,CAAC9M,OAAY;IAC5B,OAAOA,OAAO,CAAC+M,MAAM,IAAI,EAAE;EAC7B;EAEAC,eAAeA,CAAChN,OAAY;IAC1B,OAAOA,OAAO,CAACiN,YAAY,IAAI,cAAc;EAC/C;EAEAC,kBAAkBA,CAAClN,OAAY;IAC7B,OAAOA,OAAO,CAACmN,OAAO,IAAI,EAAE;EAC9B;EAEA;EACAC,iBAAiBA,CAACpN,OAAY;IAC5B;EAAA;EAGFqN,gBAAgBA,CAACrN,OAAY;IAC3B,OAAOA,OAAO,CAACsN,aAAa,IAAI,kCAAkC;EACpE;EAEAC,cAAcA,CAACvN,OAAY;IACzB,OAAOA,OAAO,CAACwN,WAAW,IAAI,SAAS;EACzC;EAEAC,eAAeA,CAACzN,OAAY;IAC1B,OAAOA,OAAO,CAAC0N,YAAY,IAAI,EAAE;EACnC;EAEA;EACAC,eAAeA,CAAC3N,OAAY;IAC1B;EAAA;EAGF4N,gBAAgBA,CAAC7N,SAAiB,EAAET,KAAU;IAC5C;EAAA;EAGFuO,gBAAgBA,CAAC9N,SAAiB;IAChC;EAAA;EAGF;EACA+N,QAAQA,CAACxO,KAAU;IACjB;IACA,MAAMqC,OAAO,GAAGrC,KAAK,CAACE,MAAM;IAC5B,IAAI,CAACzG,kBAAkB,GACrB4I,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACoM,YAAY,GAAG,GAAG;EACzE;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACxhB,YAAY,EAAEpJ,EAAE,IAAI,CAAC,IAAI,CAAC+J,eAAe,EAAE;IAErD,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACD,WAAW,EAAE;IAElB;IACA,IAAI,CAACd,cAAc,CAACmS,eAAe,CACjC,IAAI,CAAC9R,YAAY,CAACpJ,EAAE,EACpB,IAAI,CAAC4J,oBAAoB,EACzB,IAAI,CAACC,WAAW,CACjB,CAACmR,SAAS,CAAC;MACVG,IAAI,EAAG/R,YAAY,IAAI;QACrB,IAAIA,YAAY,CAACnE,QAAQ,IAAImE,YAAY,CAACnE,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;UAC7D;UACA,IAAI,CAACK,QAAQ,GAAG,CAAC,GAAGmE,YAAY,CAACnE,QAAQ,EAAE,GAAG,IAAI,CAACA,QAAQ,CAAC;UAC5D,IAAI,CAAC8E,eAAe,GAClBX,YAAY,CAACnE,QAAQ,CAACL,MAAM,KAAK,IAAI,CAACgF,oBAAoB;SAC7D,MAAM;UACL,IAAI,CAACG,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD8N,KAAK,EAAGA,KAAK,IAAI;QACf4D,OAAO,CAAC5D,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,IAAI,CAAC9N,aAAa,GAAG,KAAK;MAC5B;KACD,CAAC;EACJ;EAEA+gB,iBAAiBA,CAAA;IACf,IAAIlN,OAAO,CAAC,qDAAqD,CAAC,EAAE;MAClE,IAAI,CAAC1Y,QAAQ,GAAG,EAAE;;EAEtB;EAEA6lB,kBAAkBA,CAAA;IAChB;EAAA;EAGF;EACAC,gBAAgBA,CAAA;IACd,OAAO,CACL;MACEhkB,IAAI,EAAE,eAAe;MACrBikB,KAAK,EAAE,YAAY;MACnBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACxL,YAAY,EAAE;MAClCyL,KAAK,EAAE,YAAY;MACnBC,QAAQ,EAAE,IAAI,CAACjf;KAChB,EACD;MACEnF,IAAI,EAAE,aAAa;MACnBikB,KAAK,EAAE,eAAe;MACtBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACtL,mBAAmB,EAAE;MACzCuL,KAAK,EAAE,kBAAkB;MACzBE,KAAK,EACH,IAAI,CAACrR,wBAAwB,GAAG,CAAC,GAC7B;QACEiD,KAAK,EAAE,IAAI,CAACjD,wBAAwB;QACpCmR,KAAK,EAAE,YAAY;QACnBG,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEtkB,IAAI,EAAE,cAAc;MACpBikB,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACrN,SAAS,CAAC,OAAmB,CAAC;MAClDsN,KAAK,EAAE;KACR,EACD;MACEnkB,IAAI,EAAE,cAAc;MACpBikB,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACrN,SAAS,CAAC,OAAmB,CAAC;MAClDsN,KAAK,EAAE;KACR,CACF;EACH;EAEA;EAEAI,qBAAqBA,CAAA;IACnB,IAAI,CAACziB,MAAM,CAAC0iB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAzrB,eAAeA,CAAC0rB,MAAc;IAC5B;EAAA;EAGF1sB,gBAAgBA,CAACC,UAAe;IAC9B,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IACpC,MAAM4f,IAAI,GAAG,IAAI/C,IAAI,CAAC7c,UAAU,CAAC;IACjC,MAAM8c,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM6P,IAAI,GAAG5P,GAAG,CAAC6P,OAAO,EAAE,GAAG/M,IAAI,CAAC+M,OAAO,EAAE;IAC3C,MAAM7G,OAAO,GAAG1F,IAAI,CAACC,KAAK,CAACqM,IAAI,GAAG,KAAK,CAAC;IAExC,IAAI5G,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;IAClC,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,UAAUA,OAAO,MAAM;IAChD,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,UAAU1F,IAAI,CAACC,KAAK,CAACyF,OAAO,GAAG,EAAE,CAAC,IAAI;IACjE,OAAO,UAAU1F,IAAI,CAACC,KAAK,CAACyF,OAAO,GAAG,IAAI,CAAC,IAAI;EACjD;EAEA8G,0BAA0BA,CAAA;IACxB,IAAI,CAACvV,wBAAwB,GAAG,CAAC,IAAI,CAACA,wBAAwB;EAChE;EAEAjS,uBAAuBA,CAACsY,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5B,MAAMmP,cAAc,GAAG,IAAI,CAAC3mB,QAAQ,CAACwX,KAAK,CAAC;IAC3C,MAAMoP,eAAe,GAAG,IAAI,CAAC5mB,QAAQ,CAACwX,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACmP,cAAc,EAAEvsB,SAAS,IAAI,CAACwsB,eAAe,EAAExsB,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAMysB,WAAW,GAAG,IAAIlQ,IAAI,CAACgQ,cAAc,CAACvsB,SAAS,CAAC,CAAC0sB,YAAY,EAAE;IACrE,MAAMC,YAAY,GAAG,IAAIpQ,IAAI,CAACiQ,eAAe,CAACxsB,SAAS,CAAC,CAAC0sB,YAAY,EAAE;IAEvE,OAAOD,WAAW,KAAKE,YAAY;EACrC;EAEA7sB,mBAAmBA,CAACE,SAAc;IAChC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,MAAMsf,IAAI,GAAG,IAAI/C,IAAI,CAACvc,SAAS,CAAC;IAChC,MAAM4sB,KAAK,GAAG,IAAIrQ,IAAI,EAAE;IACxB,MAAMsQ,SAAS,GAAG,IAAItQ,IAAI,CAACqQ,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIzN,IAAI,CAACoN,YAAY,EAAE,KAAKE,KAAK,CAACF,YAAY,EAAE,EAAE,OAAO,aAAa;IACtE,IAAIpN,IAAI,CAACoN,YAAY,EAAE,KAAKG,SAAS,CAACH,YAAY,EAAE,EAAE,OAAO,MAAM;IAEnE,OAAOpN,IAAI,CAAC0N,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAC,oBAAoBA,CAAC9P,OAAY;IAC/B,MAAM8K,KAAK,GAAQ;MACjBiF,WAAW,EAAE,kBAAkB;MAC/BC,SAAS,EAAE,mBAAmB;MAC9BC,YAAY,EAAE,cAAc;MAC5BC,UAAU,EAAE,oBAAoB;MAChCC,eAAe,EAAE;KAClB;IACD,OAAOrF,KAAK,CAAC9K,OAAO,CAACoQ,UAAU,CAAC,IAAI,oBAAoB;EAC1D;EAEA1pB,cAAcA,CAACsZ,OAAY,EAAEV,KAAU;IACrC,IAAI,IAAI,CAAC3G,aAAa,EAAE;MACtB,IAAI,CAACkM,sBAAsB,CAAC7E,OAAO,CAAC5c,EAAE,CAAC;;EAE3C;EAEAyD,oBAAoBA,CAACmZ,OAAY,EAAEV,KAAU;IAC3CA,KAAK,CAACuB,cAAc,EAAE;IACtB,IAAI,CAACsD,oBAAoB,CAACnE,OAAO,CAAC5c,EAAE,CAAC;EACvC;EAEAitB,cAAcA,CAACtQ,SAAiB,EAAEuQ,UAAmB;IACnD,IAAI,CAACzX,gBAAgB,GAAGyX,UAAU,GAAGvQ,SAAS,GAAG,EAAE;EACrD;EAEAtY,gBAAgBA,CAACoY,KAAa;IAC5B,IAAIA,KAAK,KAAK,IAAI,CAACxX,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IACnD,MAAMgnB,cAAc,GAAG,IAAI,CAAC3mB,QAAQ,CAACwX,KAAK,CAAC;IAC3C,MAAM0Q,WAAW,GAAG,IAAI,CAACloB,QAAQ,CAACwX,KAAK,GAAG,CAAC,CAAC;IAC5C,OAAOmP,cAAc,EAAE7rB,MAAM,EAAEC,EAAE,KAAKmtB,WAAW,EAAEptB,MAAM,EAAEC,EAAE;EAC/D;EAEAotB,aAAaA,CAAClR,KAAU;IACtBA,KAAK,CAACE,MAAM,CAACyJ,GAAG,GAAG,kCAAkC;EACvD;EAEAvhB,mBAAmBA,CAAA;IACjB,OAAQ,IAAI,CAAC8E,YAAoB,EAAE0S,IAAI,KAAK,OAAO;EACrD;EAEAvX,oBAAoBA,CAACkY,KAAa;IAChC,IAAI,CAAC,IAAI,CAACnY,mBAAmB,EAAE,EAAE,OAAO,KAAK;IAC7C,IAAImY,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5B,MAAMmP,cAAc,GAAG,IAAI,CAAC3mB,QAAQ,CAACwX,KAAK,CAAC;IAC3C,MAAMoP,eAAe,GAAG,IAAI,CAAC5mB,QAAQ,CAACwX,KAAK,GAAG,CAAC,CAAC;IAChD,OAAOmP,cAAc,EAAE7rB,MAAM,EAAEC,EAAE,KAAK6rB,eAAe,EAAE9rB,MAAM,EAAEC,EAAE;EACnE;EAEAM,YAAYA,CAACkrB,MAAc;IACzB,MAAM6B,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAM5Q,KAAK,GAAG+O,MAAM,CAAC8B,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAACzoB,MAAM;IAClD,OAAOyoB,MAAM,CAAC5Q,KAAK,CAAC;EACtB;EAEA;EACA,IAAI0C,IAAIA,CAAA;IACN,OAAOA,IAAI;EACb;EAEA;EACAoO,qBAAqBA,CAAA;IACnB;EAAA;EAGFC,kBAAkBA,CAAA;IAChB;EAAA;EAGFC,mBAAmBA,CAAA;IACjB;EAAA;EAGF;EACAC,eAAeA,CAAA;IACb;EAAA;EAGFC,eAAeA,CAAA;IACb;EAAA;EAGFC,kBAAkBA,CAAC9R,IAAY;IAC7B,MAAM4L,KAAK,GAAQ;MACjB5T,IAAI,EAAE,gBAAgB;MACtB5T,KAAK,EAAE,cAAc;MACrB2tB,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,mBAAmB;MAC1BzH,IAAI,EAAE,aAAa;MACnB0H,QAAQ,EAAE,uBAAuB;MACjCC,OAAO,EAAE;KACV;IACD,OAAOtG,KAAK,CAAC5L,IAAI,CAAC,IAAI,gBAAgB;EACxC;EAEA;EACAmS,oBAAoBA,CAAA;IAClB;EAAA;EAGFC,yBAAyBA,CAACC,QAAgB;IACxC,IAAI,CAACtc,2BAA2B,GAAGsc,QAAQ;EAC7C;EAEAC,0BAA0BA,CAACD,QAAgB;IACzC,MAAMzG,KAAK,GAAQ;MACjB2G,GAAG,EAAE,cAAc;MACnBC,YAAY,EAAE,cAAc;MAC5BC,aAAa,EAAE,iBAAiB;MAChCC,SAAS,EAAE,kBAAkB;MAC7BC,QAAQ,EAAE;KACX;IACD,OAAO/G,KAAK,CAACyG,QAAQ,CAAC,IAAI,qBAAqB;EACjD;EAEAO,wBAAwBA,CAAA;IACtB,OAAO,CACL;MAAE1uB,EAAE,EAAE,CAAC;MAAEqH,IAAI,EAAE,OAAO;MAAE8mB,QAAQ,EAAE,eAAe;MAAEpnB,IAAI,EAAE;IAAc,CAAE,EACzE;MACE/G,EAAE,EAAE,CAAC;MACLqH,IAAI,EAAE,QAAQ;MACd8mB,QAAQ,EAAE,cAAc;MACxBpnB,IAAI,EAAE;KACP,EACD;MACE/G,EAAE,EAAE,CAAC;MACLqH,IAAI,EAAE,kBAAkB;MACxB8mB,QAAQ,EAAE,WAAW;MACrBpnB,IAAI,EAAE;KACP,CACF;EACH;EAEA4nB,oBAAoBA,CAACC,WAAgB;IACnC;EAAA;EAGFC,iBAAiBA,CAACD,WAAgB;IAChC;EAAA;EAGFE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACJ,wBAAwB,EAAE,CAACvO,MAAM,CAAErI,CAAC,IAAKA,CAAC,CAACyQ,MAAM,CAAC;EAChE;EAEAwG,wBAAwBA,CAAC9sB,MAAc;IACrC,MAAM+sB,KAAK,GAAQ;MACjBzG,MAAM,EAAE,OAAO;MACf0G,QAAQ,EAAE,SAAS;MACnBrX,KAAK,EAAE,QAAQ;MACfsX,OAAO,EAAE;KACV;IACD,OAAOF,KAAK,CAAC/sB,MAAM,CAAC,IAAI,SAAS;EACnC;EAEAktB,kBAAkBA,CAACC,YAAiB;IAClC,OAAO,IAAI,CAACtwB,gBAAgB,CAACswB,YAAY,CAAC;EAC5C;EAEAC,mBAAmBA,CAACT,WAAgB;IAClC;EAAA;EAGFU,eAAeA,CAACV,WAAgB;IAC9B;EAAA;EAGFW,eAAeA,CAACX,WAAgB;IAC9B;EAAA;EAGFY,iBAAiBA,CAACZ,WAAgB;IAChC;EAAA;EAGF;EACAa,WAAWA,CAACC,OAAY;IACtB;EAAA;EAGFC,WAAWA,CAACD,OAAY;IACtB;EAAA;EAGFE,aAAaA,CAACF,OAAY;IACxB;EAAA;EAGFG,aAAaA,CAAA;IACX;EAAA;EAGF;EACAC,cAAcA,CAAA;IACZ,OACE,IAAI,CAACja,eAAe,IACpB,IAAI,CAACnJ,gBAAgB,IACrB,IAAI,CAACiE,sBAAsB,IAC3B,IAAI,CAACgB,gBAAgB,IACrB,IAAI,CAACvH,eAAe,IACpB,IAAI,CAAC4D,UAAU,IACf,IAAI,CAACN,eAAe,IACpB,IAAI,CAACwC,eAAe;EAExB;EAEA6f,gBAAgBA,CAAA;IACd,IAAI,CAACla,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACnJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACiE,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACgB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACvH,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC4D,UAAU,GAAG,KAAK;IACvB,IAAI,CAACN,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACwC,eAAe,GAAG,KAAK;EAC9B;EAEA;EAEA;EACA8f,SAASA,CAACC,SAAiB;IACzBzU,OAAO,CAAC6D,GAAG,CAAC,aAAa,EAAE4Q,SAAS,CAAC;EACvC;EAEAC,gBAAgBA,CAACC,UAAkB;IACjC,IAAI,CAAC9gB,mBAAmB,GAAG8gB,UAAU;EACvC;EAEAC,sBAAsBA,CAAA;IACpB;EAAA;EAGFC,qBAAqBA,CAAA;IACnB,IAAI,CAAC/gB,gBAAgB,GAAG;MACtBC,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC;MACbC,GAAG,EAAE;KACN;EACH;EAEA4gB,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC1gB,cAAc,CAAC8L,IAAI,EAAE,EAAE;MAC9B,IAAI,CAAC/L,iBAAiB,CAACoM,IAAI,CAAC;QAC1B/b,EAAE,EAAE4b,IAAI,CAACC,GAAG,EAAE,CAAC7D,QAAQ,EAAE;QACzBvX,OAAO,EAAE,IAAI,CAACmP,cAAc;QAC5Bf,CAAC,EAAE,EAAE;QACLC,CAAC,EAAE,EAAE;QACLyhB,UAAU,EAAE,IAAI,CAAC1gB,cAAc;QAC/B2gB,QAAQ,EAAE,IAAI,CAAC1gB,YAAY;QAC3BoI,KAAK,EAAE,IAAI,CAACnI;OACb,CAAC;MACF,IAAI,CAACH,cAAc,GAAG,EAAE;;EAE5B;EAEA;EACA6gB,eAAeA,CAAA;IACb,MAAMC,UAAU,GAAGC,MAAM,CAAC,yBAAyB,CAAC;IACpD,IAAID,UAAU,EAAE;MACd,IAAI,CAACtgB,WAAW,CAAC2L,IAAI,CAAC;QACpB/b,EAAE,EAAE4b,IAAI,CAACC,GAAG,EAAE,CAAC7D,QAAQ,EAAE;QACzB3Q,IAAI,EAAEqpB,UAAU;QAChB5U,IAAI,EAAE,QAAQ;QACd+F,IAAI,EAAE,CAAC;QACP+O,UAAU,EAAE,IAAIhV,IAAI;OACrB,CAAC;;EAEN;EAEAiV,WAAWA,CAAA;IACT,IAAI,CAACxO,aAAa,EAAE;EACtB;EAEAyO,cAAcA,CAAA;IACZ,IAAI,CAAC3gB,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;EACpE;EAEA4gB,gBAAgBA,CAAA;IACd,IAAI,CAAC7gB,eAAe,GAAG,KAAK;EAC9B;EAEA8gB,YAAYA,CAACC,MAAW;IACtB,IAAI,CAAC5gB,cAAc,GAAG4gB,MAAM;EAC9B;EAEAC,YAAYA,CAACD,MAAW;IACtB,MAAME,OAAO,GAAGR,MAAM,CAAC,cAAc,EAAEM,MAAM,CAAC5pB,IAAI,CAAC;IACnD,IAAI8pB,OAAO,EAAE;MACXF,MAAM,CAAC5pB,IAAI,GAAG8pB,OAAO;;EAEzB;EAEAC,YAAYA,CAACH,MAAW;IACtB,IAAItT,OAAO,CAAC,wBAAwB,CAAC,EAAE;MACrC,IAAI,CAACvN,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC+P,MAAM,CAAEkR,CAAC,IAAKA,CAAC,CAACrxB,EAAE,KAAKixB,MAAM,CAACjxB,EAAE,CAAC;;EAEzE;EAEAsxB,yBAAyBA,CAAA;IACvB,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAC,cAAcA,CAAA;IACZ,OAAO,QAAQ;EACjB;EAEAC,eAAeA,CAAA;IACb,OAAO,OAAO;EAChB;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI;EACb;EAEAC,gBAAgBA,CAACC,KAAU;IACzB,IAAI,CAACthB,cAAc,GAAGshB,KAAK;EAC7B;EAEAC,YAAYA,CAAA;IACV;EAAA;EAGFC,gBAAgBA,CAAA;IACd;EAAA;EAGFC,SAASA,CAAA;IACP;EAAA;EAGFC,gBAAgBA,CAAA;IACd,OAAO,CACL;MACE/xB,EAAE,EAAE,GAAG;MACPqH,IAAI,EAAE,cAAc;MACpByU,IAAI,EAAE,MAAM;MACZ+F,IAAI,EAAE,OAAO;MACb+O,UAAU,EAAE,IAAIhV,IAAI;KACrB,EACD;MACE5b,EAAE,EAAE,GAAG;MACPqH,IAAI,EAAE,WAAW;MACjByU,IAAI,EAAE,OAAO;MACb+F,IAAI,EAAE,OAAO;MACb+O,UAAU,EAAE,IAAIhV,IAAI;KACrB,CACF;EACH;EAEAoW,mBAAmBA,CAAC3L,IAAS;IAC3B,MAAM5J,KAAK,GAAG,IAAI,CAAC/L,aAAa,CAACuhB,OAAO,CAAC5L,IAAI,CAACrmB,EAAE,CAAC;IACjD,IAAIyc,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC/L,aAAa,CAACgM,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;KACpC,MAAM;MACL,IAAI,CAAC/L,aAAa,CAACqL,IAAI,CAACsK,IAAI,CAACrmB,EAAE,CAAC;;EAEpC;EAEAkyB,QAAQA,CAAC7L,IAAS;IAChB7K,OAAO,CAAC6D,GAAG,CAAC,iBAAiB,EAAEgH,IAAI,CAAChf,IAAI,CAAC;EAC3C;EAEA8qB,cAAcA,CAACxT,IAAS;IACtB,OAAO,IAAI/C,IAAI,CAAC+C,IAAI,CAAC,CAAC0N,kBAAkB,CAAC,OAAO,CAAC;EACnD;EAEA+F,SAASA,CAAC/L,IAAS;IACjB7K,OAAO,CAAC6D,GAAG,CAAC,mBAAmB,EAAEgH,IAAI,CAAChf,IAAI,CAAC;EAC7C;EAEAgrB,UAAUA,CAAChM,IAAS;IAClB,IAAI1I,OAAO,CAAC,wBAAwB,CAAC,EAAE;MACrCnC,OAAO,CAAC6D,GAAG,CAAC,mBAAmB,EAAEgH,IAAI,CAAChf,IAAI,CAAC;;EAE/C;EAEA;EACA,IAAIirB,SAASA,CAAA;IACX,OAAO,KAAK,CAAC,CAAC;EAChB;EAEA;EACAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACtY,gBAAgB;EAC9B;EAEA;EACAuY,YAAYA,CAAC1e,IAAY,EAAE2e,SAAiB;IAC1C,OAAO3e,IAAI,CAAClP,MAAM,GAAG6tB,SAAS,GAC1B3e,IAAI,CAAC4e,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK,GACpC3e,IAAI;EACV;EAEArP,QAAQA,CAACmY,OAAY;IACnB,OACEA,OAAO,CAACd,IAAI,KAAK,OAAO,IACvBc,OAAO,CAACmK,WAAW,IAClBnK,OAAO,CAACmK,WAAW,CAACC,IAAI,CAAE2L,CAAM,IAAKA,CAAC,CAAC7W,IAAI,CAACwK,UAAU,CAAC,QAAQ,CAAC,CAAE;EAExE;EAEAsM,cAAcA,CAAChW,OAAY;IACzB,OAAOA,OAAO,CAACd,IAAI,KAAK,OAAO,IAAIc,OAAO,CAACd,IAAI,KAAK,OAAO;EAC7D;EAEApX,OAAOA,CAACkY,OAAY;IAClB,OACEA,OAAO,CAACd,IAAI,KAAK,MAAM,IACtBc,OAAO,CAACmK,WAAW,IAAInK,OAAO,CAACmK,WAAW,CAACniB,MAAM,GAAG,CAAE;EAE3D;EAEAjD,WAAWA,CAACib,OAAY;IACtB,IAAIA,OAAO,CAACmK,WAAW,IAAInK,OAAO,CAACmK,WAAW,CAACniB,MAAM,GAAG,CAAC,EAAE;MACzD,OAAOgY,OAAO,CAACmK,WAAW,CAAC,CAAC,CAAC,CAAC1f,IAAI,IAAI,SAAS;;IAEjD,OAAO,SAAS;EAClB;EAEAwrB,qBAAqBA,CAACjW,OAAY;IAChC,MAAMkW,KAAK,GAAGlW,OAAO,CAAC7c,MAAM,EAAEC,EAAE,KAAK,IAAI,CAAC4C,aAAa;IACvD,OAAO,2BAA2BkwB,KAAK,GAAG,KAAK,GAAG,OAAO,EAAE;EAC7D;EAEAC,iBAAiBA,CAACnW,OAAY;IAC5B,OAAOA,OAAO,CAACd,IAAI,KAAK,UAAU;EACpC;EAEAkX,gBAAgBA,CAACpW,OAAY;IAC3B,OAAOA,OAAO,CAACd,IAAI,KAAK,SAAS;EACnC;EAEA;EACAmX,WAAWA,CAAA;IACT,IAAI,CAACjlB,UAAU,GAAG,KAAK;EACzB;EAEAklB,aAAaA,CAACC,IAAY;IACxB,IAAI,CAACllB,UAAU,GAAGklB,IAAI;EACxB;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACllB,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAmlB,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT,IAAI,CAACjR,aAAa,EAAE;EACtB;EAEAkR,YAAYA,CAAA;IACV;EAAA;EAGFC,mBAAmBA,CAAA;IACjB,IAAI,CAAChlB,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,sBAAsB,GAAG,CAAC;EACjC;EAEAglB,kBAAkBA,CAAA;IAChB,IAAI,CAACjlB,gBAAgB,GAAG,KAAK;EAC/B;EAEAklB,gBAAgBA,CAAA;IACd,IAAI,CAACvlB,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEAwlB,mBAAmBA,CAAC/O,QAAgB;IAClC,MAAMC,OAAO,GAAG1F,IAAI,CAACC,KAAK,CAACwF,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAME,OAAO,GAAGF,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGC,OAAO,CAAC7M,QAAQ,EAAE,CAAC+M,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CACrD9M,QAAQ,EAAE,CACV+M,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACvB;EAEA;EACA6O,gBAAgBA,CAAA;IACd,IAAI,CAACllB,eAAe,GAAG,KAAK;EAC9B;EAEAmlB,eAAeA,CAAA;IACb;EAAA;EAGFC,eAAeA,CAACC,MAAc,EAAE7X,KAAU;IACxC;EAAA;EAGF8X,eAAeA,CAACvX,KAAa;IAC3B;EAAA;EAGFwX,kBAAkBA,CAACd,IAAY;IAC7B,IAAI,CAACxkB,eAAe,GAAGwkB,IAAI;EAC7B;EAEAe,YAAYA,CAACC,KAAa;IACxB,IAAI,CAACllB,SAAS,GAAGklB,KAAK;EACxB;EAEAC,WAAWA,CAACC,KAAa;IACvB;EAAA;EAGF;EACQvZ,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAAC1R,YAAY,GAAG;MAClBpJ,EAAE,EAAE,mBAAmB;MACvBob,YAAY,EAAE,CACZ;QAAEpb,EAAE,EAAE,OAAO;QAAEf,QAAQ,EAAE;MAAO,CAAE,EAClC;QAAEe,EAAE,EAAE,OAAO;QAAEf,QAAQ,EAAE;MAAK,CAAE;KAEnB;IAEjB;IACA,IAAI,CAACgG,QAAQ,GAAG,CACd;MACEjF,EAAE,EAAE,GAAG;MACPS,OAAO,EAAE,yBAAyB;MAClCV,MAAM,EAAE;QAAEC,EAAE,EAAE,OAAO;QAAEf,QAAQ,EAAE;MAAK,CAAE;MACxCI,SAAS,EAAE,IAAIuc,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCZ,cAAc,EAAE,mBAAmB;MACnCa,IAAI,EAAE;KACP,EACD;MACE9b,EAAE,EAAE,GAAG;MACPS,OAAO,EAAE,8BAA8B;MACvCV,MAAM,EAAE;QAAEC,EAAE,EAAE,IAAI,CAAC4C,aAAc;QAAE3D,QAAQ,EAAE,IAAI,CAACqK;MAAe,CAAE;MACnEjK,SAAS,EAAE,IAAIuc,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCZ,cAAc,EAAE,mBAAmB;MACnCa,IAAI,EAAE;KACP,EACD;MACE9b,EAAE,EAAE,GAAG;MACPS,OAAO,EAAE,yCAAyC;MAClDV,MAAM,EAAE;QAAEC,EAAE,EAAE,OAAO;QAAEf,QAAQ,EAAE;MAAK,CAAE;MACxCI,SAAS,EAAE,IAAIuc,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCZ,cAAc,EAAE,mBAAmB;MACnCa,IAAI,EAAE;KACP,CACF;IAED,IAAI,CAACld,gBAAgB,GAAG;MAAEoB,EAAE,EAAE,OAAO;MAAEf,QAAQ,EAAE;IAAK,CAAU;IAChE,IAAI,CAACoK,OAAO,GAAG,KAAK;EACtB;EAEA;EAEA;EACAirB,SAASA,CAACC,GAAQ;IAChB;IACA,MAAM9zB,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACuJ,WAAW,CAACgb,UAAU,CAAC;MAAE/zB,OAAO,EAAEA,OAAO,GAAG,QAAQ8zB,GAAG,CAACv0B,EAAE;IAAG,CAAE,CAAC;EACvE;EAEAy0B,kBAAkBA,CAAA;IAChB;EAAA;EAGFC,iBAAiBA,CAAA;IACf,IAAI,CAACtqB,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA;EACAuqB,eAAeA,CAAA;IACb,IAAI,CAACjnB,eAAe,GAAG,IAAI;EAC7B;EAEAknB,gBAAgBA,CAAA;IACd,IAAI,CAAClnB,eAAe,GAAG,KAAK;EAC9B;EAEAmnB,iBAAiBA,CAACC,IAAY;IAC5B,IAAI,CAACnnB,mBAAmB,GAAGmnB,IAAI;EACjC;EAEAC,kBAAkBA,CAAC7c,KAAa;IAC9B,IAAI,CAACrK,oBAAoB,GAAGqK,KAAK;EACnC;EAEA8c,WAAWA,CAAA;IACT;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,YAAYA,CAAA;IACV;EAAA;EAGFC,YAAYA,CAACjZ,KAAU;IACrB;EAAA;EAGFkZ,IAAIA,CAAClZ,KAAU;IACb;EAAA;EAGFmZ,WAAWA,CAAA;IACT;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGF;EACA3yB,cAAcA,CAAC4yB,QAAa,EAAE/J,MAAqB;IACjD,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK;IACzB,OAAO+J,QAAQ,CAAC/J,MAAM,KAAKA,MAAM;EACnC;EAEAgK,kBAAkBA,CAACD,QAAa;IAC9B,OAAO,GAAGA,QAAQ,CAAC1O,IAAI,EAAE5nB,QAAQ,IAAI,aAAa,iBAChDs2B,QAAQ,CAAC/yB,KACX,EAAE;EACJ;EAEA;EACAizB,gBAAgBA,CAAC7Y,OAAY;IAC3B;IACA,OAAOA,OAAO,CAAC7c,MAAM,EAAEC,EAAE,KAAK,IAAI,CAAC4C,aAAa,IAAI,IAAI,CAAC2G,OAAO;EAClE;EAEAmsB,cAAcA,CAAC9Y,OAAY;IACzB;IACA,OAAOA,OAAO,CAAC7c,MAAM,EAAEC,EAAE,KAAK,IAAI,CAAC4C,aAAa;EAClD;EAEA;EACA+yB,iBAAiBA,CAAA;IACf,IAAI,CAACpgB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,CAACogB,KAAK,EAAE;EAC/B;EAEAC,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACrgB,gBAAgB,CAACqM,IAAI,KAAK,CAAC,EAAE;IAEtC,IAAIlE,OAAO,CAAC,aAAa,IAAI,CAACnI,gBAAgB,CAACqM,IAAI,eAAe,CAAC,EAAE;MACnE;MACA,IAAI,CAAC5c,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACkb,MAAM,CACjCtD,CAAC,IAAK,CAACA,CAAC,CAAC7c,EAAE,IAAI,CAAC,IAAI,CAACwV,gBAAgB,CAACkM,GAAG,CAAC7E,CAAC,CAAC7c,EAAE,CAAC,CACjD;MACD,IAAI,CAAC21B,iBAAiB,EAAE;MACxB,IAAI,CAAC3sB,YAAY,CAACuY,WAAW,CAAC,oBAAoB,CAAC;;EAEvD;EAEAuU,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAACtgB,gBAAgB,CAACqM,IAAI,KAAK,CAAC,EAAE;IAEtC;IACA,IAAI,CAACnV,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,iBAAiB,GAAG0P,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9G,gBAAgB,CAAC;EAC5D;EAEAugB,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACvgB,gBAAgB,CAACqM,IAAI,KAAK,CAAC,EAAE;IAEtC,MAAMmU,qBAAqB,GAAG,IAAI,CAAC/wB,QAAQ,CAACkb,MAAM,CAC/CtD,CAAC,IAAKA,CAAC,CAAC7c,EAAE,IAAI,IAAI,CAACwV,gBAAgB,CAACkM,GAAG,CAAC7E,CAAC,CAAC7c,EAAE,CAAC,CAC/C;IACD,MAAMi2B,UAAU,GAAGD,qBAAqB,CACrCzQ,GAAG,CAAE1I,CAAC,IAAK,GAAGA,CAAC,CAAC9c,MAAM,EAAEd,QAAQ,KAAK4d,CAAC,CAACpc,OAAO,EAAE,CAAC,CACjDy1B,IAAI,CAAC,IAAI,CAAC;IAEb9U,SAAS,CAACC,SAAS,CAChBC,SAAS,CAAC2U,UAAU,CAAC,CACrBE,IAAI,CAAC,MAAK;MACT,IAAI,CAACntB,YAAY,CAACuY,WAAW,CAAC,iBAAiB,CAAC;MAChD,IAAI,CAACoU,iBAAiB,EAAE;IAC1B,CAAC,CAAC,CACDS,KAAK,CAAC,MAAK;MACV,IAAI,CAACptB,YAAY,CAACgV,SAAS,CAAC,yBAAyB,CAAC;IACxD,CAAC,CAAC;EACN;EAEA;EACAqY,WAAWA,CAAA;IACT,IAAI,CAACxqB,iBAAiB,GAAG,IAAI;EAC/B;EAEA;EACAyqB,kBAAkBA,CAAA;IAChB,IAAI,CAAC9sB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAAC6Y,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAAC9D,aAAa,CAACvO,KAAK,GAAG,EAAE;;EAE3C;EAEA;EACA0U,uBAAuBA,CAACC,QAAgB;IACtC,MAAMC,OAAO,GAAG1F,IAAI,CAACC,KAAK,CAACwF,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAME,OAAO,GAAGF,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGC,OAAO,IAAIC,OAAO,CAAC9M,QAAQ,EAAE,CAAC+M,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEAlf,oBAAoBA,CAAA;IAClB,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC4D,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAAC+M,mBAAmB,GAAG,MAAM;EACnC;EAEA;EACA6f,aAAaA,CAAA;IACX,OACE,IAAI,CAAC3iB,YAAY,CAAC8H,IAAI,EAAE,CAAC9W,MAAM,GAAG,CAAC,IACnC,IAAI,CAACiP,WAAW,CAACsM,MAAM,CAAEqW,GAAG,IAAKA,GAAG,CAAC1iB,IAAI,CAAC4H,IAAI,EAAE,CAAC9W,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,IAAI,CAAC;EAE5E;EAEA;EACA6xB,mBAAmBA,CAAA;IACjB,IAAI,CAACniB,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC9B;EAEAgiB,kBAAkBA,CAAA;IAChB,IAAItV,SAAS,CAACuV,WAAW,EAAE;MACzBvV,SAAS,CAACuV,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAI;QACX,IAAI,CAACniB,gBAAgB,GAAG;UACtBoiB,QAAQ,EAAED,QAAQ,CAACE,MAAM,CAACD,QAAQ;UAClCE,SAAS,EAAEH,QAAQ,CAACE,MAAM,CAACC,SAAS;UACpC3vB,IAAI,EAAE;SACP;MACH,CAAC,EACAuQ,KAAK,IAAI;QACR,IAAI,CAAC5O,YAAY,CAACgV,SAAS,CAAC,qCAAqC,CAAC;MACpE,CAAC,CACF;KACF,MAAM;MACL,IAAI,CAAChV,YAAY,CAACgV,SAAS,CAAC,+BAA+B,CAAC;;EAEhE;EAEAiZ,cAAcA,CAAA;IACZ,IAAI,CAAC1iB,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,CAACE,qBAAqB,GAAG,CAC3B;MAAEpN,IAAI,EAAE,eAAe;MAAEyvB,QAAQ,EAAE,OAAO;MAAEE,SAAS,EAAE;IAAM,CAAE,EAC/D;MAAE3vB,IAAI,EAAE,cAAc;MAAEyvB,QAAQ,EAAE,MAAM;MAAEE,SAAS,EAAE;IAAM,CAAE,EAC7D;MAAE3vB,IAAI,EAAE,mBAAmB;MAAEyvB,QAAQ,EAAE,OAAO;MAAEE,SAAS,EAAE;IAAM,CAAE,CACpE;EACH;EAEAE,gBAAgBA,CAAA;IACd;IACA,IAAI,IAAI,CAAC1iB,mBAAmB,CAACkH,IAAI,EAAE,EAAE;MACnC,IAAI,CAACjH,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAAC0L,MAAM,CAC3D4N,QAAQ,IACPA,QAAQ,CAAC1mB,IAAI,CACV+Y,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAAC7L,mBAAmB,CAAC4L,WAAW,EAAE,CAAC,CACtD;;EAEL;EAEA+W,oBAAoBA,CAACxQ,MAAW;IAC9B,IAAI,CAACjS,gBAAgB,GAAGiS,MAAM;IAC9B,IAAI,CAACpS,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,mBAAmB,GAAG,EAAE;EAC/B;EAEA4iB,mBAAmBA,CAAA;IACjB,IAAI,CAACziB,mBAAmB,GAAG,IAAI;EACjC;EAEA0iB,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC3iB,gBAAgB,EAAE;MACzB;MACA,MAAME,eAAe,GAAG;QACtB5U,EAAE,EAAE4b,IAAI,CAACC,GAAG,EAAE,CAAC7D,QAAQ,EAAE;QACzB8D,IAAI,EAAE,UAAU;QAChBiS,QAAQ,EAAE,IAAI,CAACrZ,gBAAgB;QAC/BE,eAAe,EAAE,IAAI,CAACA,eAAe;QACrC7U,MAAM,EAAE;UAAEC,EAAE,EAAE,IAAI,CAAC4C,aAAc;UAAE3D,QAAQ,EAAE,IAAI,CAACqK;QAAe,CAAE;QACnEjK,SAAS,EAAE,IAAIuc,IAAI,EAAE;QACrBX,cAAc,EAAE,IAAI,CAAC7R,YAAY,EAAEpJ;OACpC;MAED,IAAI,CAACiF,QAAQ,CAAC8W,IAAI,CAACnH,eAAsB,CAAC;MAC1C,IAAI,CAAC6hB,mBAAmB,EAAE;MAC1B,IAAI,CAAC7hB,eAAe,GAAG,EAAE;MACzB,IAAI,CAAC5L,YAAY,CAACuY,WAAW,CAAC,uBAAuB,CAAC;;EAE1D;EAEA;EACA+V,kBAAkBA,CAAA;IAChB,IAAI,CAACziB,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,yBAAyB,GAAG,IAAI;EACvC;EAEAwiB,eAAeA,CAAA;IACb;IACA;EAAA;EAGFC,6BAA6BA,CAAA;IAC3B;IACA,MAAMC,QAAQ,GAAG,CACf;MACEz3B,EAAE,EAAE,GAAG;MACPqH,IAAI,EAAE,cAAc;MACpBqwB,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE;KACR,EACD;MACE33B,EAAE,EAAE,GAAG;MACPqH,IAAI,EAAE,YAAY;MAClBqwB,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE;KACR,EACD;MACE33B,EAAE,EAAE,GAAG;MACPqH,IAAI,EAAE,eAAe;MACrBqwB,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE;KACR,CACF;IAED,IAAI,IAAI,CAAC7iB,kBAAkB,CAAC4G,IAAI,EAAE,EAAE;MAClC,OAAO+b,QAAQ,CAACtX,MAAM,CACnB6N,OAAO,IACNA,OAAO,CAAC3mB,IAAI,CACT+Y,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAACvL,kBAAkB,CAACsL,WAAW,EAAE,CAAC,IAClD4N,OAAO,CAAC0J,KAAK,CAACrX,QAAQ,CAAC,IAAI,CAACvL,kBAAkB,CAAC,IAC/CkZ,OAAO,CAAC2J,KAAK,CACVvX,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAACvL,kBAAkB,CAACsL,WAAW,EAAE,CAAC,CACrD;;IAEH,OAAOqX,QAAQ;EACjB;EAEAG,uBAAuBA,CAAC5J,OAAY;IAClC,IAAI,CAACjZ,yBAAyB,GAAGiZ,OAAO;EAC1C;EAEA6J,kBAAkBA,CAAC7J,OAAY;IAC7B;IACA,IAAI,CAAChlB,YAAY,CAAC8uB,QAAQ,CAAC,cAAc9J,OAAO,CAAC3mB,IAAI,EAAE,CAAC;EAC1D;EAEA0wB,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAC/uB,YAAY,CAAC8uB,QAAQ,CAAC,+BAA+B,CAAC;EAC7D;EAEAE,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjjB,yBAAyB,EAAE;MAClC;MACA,MAAMkjB,cAAc,GAAG;QACrBj4B,EAAE,EAAE4b,IAAI,CAACC,GAAG,EAAE,CAAC7D,QAAQ,EAAE;QACzB8D,IAAI,EAAE,SAAS;QACfkS,OAAO,EAAE,IAAI,CAACjZ,yBAAyB;QACvChV,MAAM,EAAE;UAAEC,EAAE,EAAE,IAAI,CAAC4C,aAAc;UAAE3D,QAAQ,EAAE,IAAI,CAACqK;QAAe,CAAE;QACnEjK,SAAS,EAAE,IAAIuc,IAAI,EAAE;QACrBX,cAAc,EAAE,IAAI,CAAC7R,YAAY,EAAEpJ;OACpC;MAED,IAAI,CAACiF,QAAQ,CAAC8W,IAAI,CAACkc,cAAqB,CAAC;MACzC,IAAI,CAACX,kBAAkB,EAAE;MACzB,IAAI,CAACtuB,YAAY,CAACuY,WAAW,CAAC,iBAAiB,CAAC;;EAEpD;EAEA;EACA2W,iBAAiBA,CAAA;IACf,IAAI,CAAC/qB,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC/C,eAAe,GAAG,KAAK;EAC9B;EAEA+tB,aAAaA,CAAA;IACX,IAAI,CAAC7qB,aAAa,GAAG,IAAI;IACzB,IAAI,CAAClD,eAAe,GAAG,KAAK;EAC9B;EAEAguB,aAAaA,CAAA;IACX;IACA;EAAA;EAGFhxB,WAAWA,CAAC5E,KAAa;IACvB,MAAM0f,cAAc,GAAG,IAAI,CAAC1I,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IACnE,IAAI,CAACuJ,WAAW,CAACgb,UAAU,CAAC;MAC1B/zB,OAAO,EAAEyhB,cAAc,GAAG1f;KAC3B,CAAC;EACJ;EAEA61B,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAAC5wB,eAAe,CAAC0Y,MAAM,CAAEgO,QAAQ,IAAI;MAC9C,IAAI,CAAC,IAAI,CAACnhB,gBAAgB,CAAC0O,IAAI,EAAE,EAAE,OAAO,IAAI;MAC9C,OAAOyS,QAAQ,CAAC9mB,IAAI,CACjB+Y,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAACrT,gBAAgB,CAACoT,WAAW,EAAE,CAAC;IAClD,CAAC,CAAC;EACJ;EAEAkY,YAAYA,CAAC91B,KAAU;IACrB,IAAI,CAAC0K,cAAc,GAAG1K,KAAK;EAC7B;EAEA+1B,iBAAiBA,CAACC,MAAc;IAC9B,IAAI,CAACnrB,mBAAmB,GAAGmrB,MAAM;EACnC;EAEAC,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACrrB,YAAY,CAACiO,IAAI,CAC1Bqd,IAAI,IAAKA,IAAI,CAAC14B,EAAE,KAAK,IAAI,CAACqN,mBAAmB,CAC/C;EACH;EAEAsrB,aAAaA,CAACC,OAAY;IACxB;IACA,MAAMC,cAAc,GAAG;MACrB74B,EAAE,EAAE4b,IAAI,CAACC,GAAG,EAAE,CAAC7D,QAAQ,EAAE;MACzB8D,IAAI,EAAE,SAAS;MACf8c,OAAO,EAAEA,OAAO;MAChB74B,MAAM,EAAE;QAAEC,EAAE,EAAE,IAAI,CAAC4C,aAAc;QAAE3D,QAAQ,EAAE,IAAI,CAACqK;MAAe,CAAE;MACnEjK,SAAS,EAAE,IAAIuc,IAAI,EAAE;MACrBX,cAAc,EAAE,IAAI,CAAC7R,YAAY,EAAEpJ;KACpC;IAED,IAAI,CAACiF,QAAQ,CAAC8W,IAAI,CAAC8c,cAAqB,CAAC;IACzC,IAAI,CAAC1rB,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACnE,YAAY,CAACuY,WAAW,CAAC,gBAAgB,CAAC;EACjD;EAEAuX,WAAWA,CAAA;IACT;IACA;EAAA;EAGFC,iBAAiBA,CAAC5K,QAAgB;IAChC,IAAI,CAAC1gB,mBAAmB,GAAG0gB,QAAQ;EACrC;EAEA6K,eAAeA,CAAA;IACb;IACA,OAAO,CACL;MAAEh5B,EAAE,EAAE,GAAG;MAAEmnB,GAAG,EAAE,UAAU;MAAE6D,KAAK,EAAE;IAAO,CAAE,EAC5C;MAAEhrB,EAAE,EAAE,GAAG;MAAEmnB,GAAG,EAAE,UAAU;MAAE6D,KAAK,EAAE;IAAO,CAAE,EAC5C;MAAEhrB,EAAE,EAAE,GAAG;MAAEmnB,GAAG,EAAE,UAAU;MAAE6D,KAAK,EAAE;IAAM,CAAE,CAC5C;EACH;EAOAiO,YAAYA,CAAA;IACV,IAAI,CAACvtB,sBAAsB,GAAG,KAAK;EACrC;EAEAwtB,aAAaA,CAAA;IACX;IACA,IAAI,CAACxtB,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAAC1C,YAAY,CAACuY,WAAW,CAAC,kBAAkB,CAAC;EACnD;EAEA4X,iBAAiBA,CAAA;IACf,IAAI,CAACzsB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,qBAAqB,GAAG,EAAE;EACjC;EAIAwsB,mBAAmBA,CAAA;IACjB,MAAM3B,QAAQ,GAAG,CACf;MAAEz3B,EAAE,EAAE,GAAG;MAAEqH,IAAI,EAAE,cAAc;MAAEgyB,MAAM,EAAE;IAAa,CAAE,EACxD;MAAEr5B,EAAE,EAAE,GAAG;MAAEqH,IAAI,EAAE,YAAY;MAAEgyB,MAAM,EAAE;IAAa,CAAE,EACtD;MAAEr5B,EAAE,EAAE,GAAG;MAAEqH,IAAI,EAAE,eAAe;MAAEgyB,MAAM,EAAE;IAAa,CAAE,CAC1D;IAED,IAAI,IAAI,CAAChgB,kBAAkB,CAACqC,IAAI,EAAE,EAAE;MAClC,OAAO+b,QAAQ,CAACtX,MAAM,CAAE6N,OAAO,IAC7BA,OAAO,CAAC3mB,IAAI,CACT+Y,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAAChH,kBAAkB,CAAC+G,WAAW,EAAE,CAAC,CACnD;;IAEH,OAAOqX,QAAQ;EACjB;EAEA6B,sBAAsBA,CAACC,SAAiB;IACtC,MAAM9c,KAAK,GAAG,IAAI,CAAC7P,qBAAqB,CAACqlB,OAAO,CAACsH,SAAS,CAAC;IAC3D,IAAI9c,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC7P,qBAAqB,CAAC8P,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;KAC5C,MAAM;MACL,IAAI,CAAC7P,qBAAqB,CAACmP,IAAI,CAACwd,SAAS,CAAC;;EAE9C;EAEAC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC5sB,qBAAqB,CAAChI,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACoE,YAAY,CAACuY,WAAW,CAC3B,uBAAuB,IAAI,CAAC3U,qBAAqB,CAAChI,MAAM,aAAa,CACtE;MACD,IAAI,CAACu0B,iBAAiB,EAAE;;EAE5B;EAEA;EACAM,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC3jB,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;;EAE5B;EAEA4jB,SAASA,CAAA;IACP,IAAI,IAAI,CAAC5jB,iBAAiB,GAAG,IAAI,CAACC,YAAY,CAACnR,MAAM,GAAG,CAAC,EAAE;MACzD,IAAI,CAACkR,iBAAiB,EAAE;;EAE5B;EAEA;EACA6jB,YAAYA,CAAA;IACV,MAAMjS,KAAK,GAAG;MACZkS,OAAO,EAAE,qBAAqB;MAC9BhiB,KAAK,EAAE,2BAA2B;MAClCiiB,OAAO,EAAE,6BAA6B;MACtCC,IAAI,EAAE;KACP;IACD,OAAOpS,KAAK,CAAC,IAAI,CAACxR,SAA+B,CAAC,IAAI,oBAAoB;EAC5E;EAEA;EACA6jB,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC90B,QAAQ,CAACL,MAAM;EAC7B;EAEAo1B,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC/0B,QAAQ,CAACkb,MAAM,CAAEtD,CAAC,IAC5BA,CAAC,CAACkK,WAAW,EAAEC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACnL,IAAI,KAAK,OAAO,CAAC,CACnD,CAAClX,MAAM;EACV;EAEAq1B,aAAaA,CAAA;IACX,OAAO,IAAI,CAACh1B,QAAQ,CAACkb,MAAM,CAAEtD,CAAC,IAC5BA,CAAC,CAACkK,WAAW,EAAEC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACnL,IAAI,KAAK,MAAM,CAAC,CAClD,CAAClX,MAAM;EACV;EAEA;EACAs1B,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAAClxB,YAAY,CAACuY,WAAW,CAAC,uBAAuB,CAAC;EACxD;EAEA;EACA4Y,qBAAqBA,CAAA;IACnB,IAAI,CAAChnB,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACG,0BAA0B,GAAG,EAAE;IACpC,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B;EAEA6mB,aAAaA,CAAA;IACX,MAAMC,IAAI,GAAG,IAAI,CAACjnB,eAAe;IACjC,IAAI,CAACA,eAAe,GAAG,IAAI,CAACC,aAAa;IACzC,IAAI,CAACA,aAAa,GAAGgnB,IAAI;EAC3B;EAEAC,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC/mB,cAAc,EAAE;MACvB6N,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC/N,cAAc,CAAC,CAAC4iB,IAAI,CAAC,MAAK;QAC3D,IAAI,CAACntB,YAAY,CAACuY,WAAW,CAAC,mBAAmB,CAAC;MACpD,CAAC,CAAC;;EAEN;EAEAgZ,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAChnB,cAAc,EAAE;MACvB,MAAM2O,cAAc,GAAG,IAAI,CAAC1I,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;MACnE,IAAI,CAACuJ,WAAW,CAACgb,UAAU,CAAC;QAC1B/zB,OAAO,EAAEyhB,cAAc,GAAG,IAAI,CAAC3O;OAChC,CAAC;MACF,IAAI,CAAC4mB,qBAAqB,EAAE;;EAEhC;EAEAK,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACjnB,cAAc,EAAE;MACvB;MACA,MAAMknB,kBAAkB,GAAG;QACzBz6B,EAAE,EAAE4b,IAAI,CAACC,GAAG,EAAE,CAAC7D,QAAQ,EAAE;QACzBvX,OAAO,EAAE,IAAI,CAAC8S,cAAc;QAC5BxT,MAAM,EAAE;UAAEC,EAAE,EAAE,IAAI,CAAC4C,aAAc;UAAE3D,QAAQ,EAAE,IAAI,CAACqK;QAAe,CAAE;QACnEjK,SAAS,EAAE,IAAIuc,IAAI,EAAE;QACrBX,cAAc,EAAE,IAAI,CAAC7R,YAAY,EAAEpJ;OACpC;MAED,IAAI,CAACiF,QAAQ,CAAC8W,IAAI,CAAC0e,kBAAyB,CAAC;MAC7C,IAAI,CAACN,qBAAqB,EAAE;MAC5B,IAAI,CAACnxB,YAAY,CAACuY,WAAW,CAAC,qBAAqB,CAAC;;EAExD;EAEA;EACAmZ,gBAAgBA,CAAA;IACd,IAAI,CAAC/mB,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,EAAE;MAAEA,IAAI,EAAE;IAAE,CAAE,CAAC;IAC/C,IAAI,CAACC,YAAY,GAAG;MAClBC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE;KACb;EACH;EAEAwmB,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC9mB,WAAW,CAACjP,MAAM,GAAG,EAAE,EAAE;MAChC,IAAI,CAACiP,WAAW,CAACkI,IAAI,CAAC;QAAEjI,IAAI,EAAE;MAAE,CAAE,CAAC;;EAEvC;EAEA8mB,gBAAgBA,CAACne,KAAa;IAC5B,IAAI,IAAI,CAAC5I,WAAW,CAACjP,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACiP,WAAW,CAAC6I,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;;EAErC;EAEAoe,UAAUA,CAAA;IACR,IAAI,IAAI,CAACtE,aAAa,EAAE,EAAE;MACxB;MACA,MAAMuE,WAAW,GAAG;QAClB96B,EAAE,EAAE4b,IAAI,CAACC,GAAG,EAAE,CAAC7D,QAAQ,EAAE;QACzB8D,IAAI,EAAE,MAAM;QACZif,IAAI,EAAE;UACJC,QAAQ,EAAE,IAAI,CAACpnB,YAAY;UAC3BqnB,OAAO,EAAE,IAAI,CAACpnB,WAAW,CAACsM,MAAM,CAAEqW,GAAG,IAAKA,GAAG,CAAC1iB,IAAI,CAAC4H,IAAI,EAAE,CAAC;UAC1Dwf,QAAQ,EAAE,IAAI,CAACnnB,YAAY;UAC3BonB,KAAK,EAAE,EAAE;UACTC,UAAU,EAAE;SACb;QACDr7B,MAAM,EAAE;UAAEC,EAAE,EAAE,IAAI,CAAC4C,aAAc;UAAE3D,QAAQ,EAAE,IAAI,CAACqK;QAAe,CAAE;QACnEjK,SAAS,EAAE,IAAIuc,IAAI,EAAE;QACrBX,cAAc,EAAE,IAAI,CAAC7R,YAAY,EAAEpJ;OACpC;MAED,IAAI,CAACiF,QAAQ,CAAC8W,IAAI,CAAC+e,WAAkB,CAAC;MACtC,IAAI,CAACJ,gBAAgB,EAAE;MACvB,IAAI,CAAC1xB,YAAY,CAACuY,WAAW,CAAC,cAAc,CAAC;;EAEjD;EAEA;EACA8Z,iBAAiBA,CAAA;IACf,MAAM56B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK;IACtD,OAAOxP,OAAO,IAAIA,OAAO,CAACib,IAAI,EAAE,CAAC9W,MAAM,GAAG,CAAC;EAC7C;EAEA02B,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACD,iBAAiB,EAAE,IAAI,IAAI,CAAC7xB,YAAY,KAAK,IAAI;EAC/D;EAEA+xB,YAAYA,CAAA;IACV;EAAA;EAGFC,iBAAiBA,CAAA;IACf;IACA,MAAMjiB,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3E,IAAI,IAAI,CAACvM,gBAAgB,CAAC0O,IAAI,EAAE,EAAE;MAChC,OAAOnC,MAAM,CAAC4G,MAAM,CAAE3d,KAAK,IAAKA,KAAK,CAAC6d,QAAQ,CAAC,IAAI,CAACrT,gBAAgB,CAAC,CAAC;;IAExE,OAAOuM,MAAM;EACf;EAEA;EACAkiB,uBAAuBA,CAAA;IACrB,IAAI,CAACjxB,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEAkxB,gBAAgBA,CAACxf,KAAU;IACzB,IAAIA,KAAK,CAACzD,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACyH,aAAa,EAAE;;EAExB;EAEAyb,eAAeA,CAAA;IACb,IAAI,CAACtxB,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;MACvB,IAAI,CAACyB,WAAW,GAAG,EAAE;MACrB,IAAI,CAACG,aAAa,GAAG,EAAE;;EAE3B;EAEA2vB,iBAAiBA,CAACjf,SAAiB;IACjC;IACA,IAAI,CAACtH,oBAAoB,GAAGsH,SAAS;EACvC;EAEAkf,oBAAoBA,CAACp7B,OAAe,EAAEq7B,KAAa;IACjD,IAAI,CAACA,KAAK,EAAE,OAAOr7B,OAAO;IAC1B,MAAM8f,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIsb,KAAK,GAAG,EAAE,IAAI,CAAC;IAC5C,OAAOr7B,OAAO,CAACggB,OAAO,CAACF,KAAK,EAAE,iBAAiB,CAAC;EAClD;EAEA;EACAwb,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAC92B,QAAQ,CAACkb,MAAM,CAAEtD,CAAC,IAAKA,CAAC,CAACiE,MAAM,CAAC,CAAClc,MAAM;EACrD;EAEAo3B,oBAAoBA,CAAA;IAClB,IAAI,CAAC1xB,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA2xB,qBAAqBA,CAACtf,SAAiB;IACrC,IAAI,CAAC+D,eAAe,CAAC/D,SAAS,CAAC;EACjC;EAEAuf,iBAAiBA,CAACvd,IAAmB;IACnC,OAAO,IAAI,CAACha,iBAAiB,CAACga,IAAI,CAAC;EACrC;EAEA;EACAwd,WAAWA,CAAA;IACT,IAAI,CAACrxB,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC9B,YAAY,CAAC8uB,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEAsE,UAAUA,CAAA;IACR,IAAI,CAACtxB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACqO,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACnQ,YAAY,CAACuY,WAAW,CAAC,eAAe,CAAC;EAChD;EAEA8a,kBAAkBA,CAACzX,QAAgB;IACjC,MAAMC,OAAO,GAAG1F,IAAI,CAACC,KAAK,CAACwF,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAME,OAAO,GAAGF,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGC,OAAO,IAAIC,OAAO,CAAC9M,QAAQ,EAAE,CAAC+M,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEAuX,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACnjB,QAAQ,EAAE,OAAO,aAAa;IACvC,OAAO,cAAc;EACvB;EAEAojB,kBAAkBA,CAAA;IAChB,IAAI,CAACrjB,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAIA;EACAsjB,eAAeA,CAAC1gB,IAAY;IAC1B,MAAMrb,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,IAAIwsB,gBAAgB,GAAGh8B,OAAO;IAE9B,QAAQqb,IAAI;MACV,KAAK,MAAM;QACT2gB,gBAAgB,GAAG,IAAIh8B,OAAO,GAAG;QACjC;MACF,KAAK,QAAQ;QACXg8B,gBAAgB,GAAG,IAAIh8B,OAAO,GAAG;QACjC;MACF,KAAK,eAAe;QAClBg8B,gBAAgB,GAAG,IAAIh8B,OAAO,GAAG;QACjC;MACF,KAAK,WAAW;QACdg8B,gBAAgB,GAAG,KAAKh8B,OAAO,IAAI;QACnC;MACF,KAAK,MAAM;QACTg8B,gBAAgB,GAAG,KAAKh8B,OAAO,IAAI;QACnC;MACF,KAAK,OAAO;QACVg8B,gBAAgB,GAAG,KAAKh8B,OAAO,EAAE;QACjC;MACF,KAAK,SAAS;QACZg8B,gBAAgB,GAAG,KAAKh8B,OAAO,IAAI;QACnC;;IAGJ,IAAI,CAAC+Y,WAAW,CAACgb,UAAU,CAAC;MAAE/zB,OAAO,EAAEg8B;IAAgB,CAAE,CAAC;EAC5D;EAEAC,aAAaA,CAAC5gB,IAAY;IACxB,MAAMrb,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,QAAQ6L,IAAI;MACV,KAAK,MAAM;QACT,OAAOrb,OAAO,CAAC4f,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,QAAQ;QACX,OAAO5f,OAAO,CAAC4f,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,eAAe;QAClB,OAAO5f,OAAO,CAAC4f,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,WAAW;QACd,OAAO5f,OAAO,CAAC4f,QAAQ,CAAC,IAAI,CAAC;MAC/B,KAAK,MAAM;QACT,OAAO5f,OAAO,CAAC4f,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,OAAO;QACV,OAAO5f,OAAO,CAAC4f,QAAQ,CAAC,GAAG,CAAC;MAC9B,KAAK,SAAS;QACZ,OAAO5f,OAAO,CAAC4f,QAAQ,CAAC,IAAI,CAAC;MAC/B;QACE,OAAO,KAAK;;EAElB;EAEAsc,UAAUA,CAAA;IACR,MAAMxV,GAAG,GAAGwJ,MAAM,CAAC,wBAAwB,CAAC;IAC5C,IAAIxJ,GAAG,EAAE;MACP,MAAMrT,IAAI,GAAG6c,MAAM,CAAC,2BAA2B,CAAC,IAAIxJ,GAAG;MACvD,MAAM1mB,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;MAC5D,IAAI,CAACuJ,WAAW,CAACgb,UAAU,CAAC;QAC1B/zB,OAAO,EAAEA,OAAO,GAAG,IAAIqT,IAAI,KAAKqT,GAAG;OACpC,CAAC;;EAEN;EAEAyV,WAAWA,CAAA;IACT,MAAMC,KAAK,GACT,oFAAoF;IACtF,MAAMp8B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACuJ,WAAW,CAACgb,UAAU,CAAC;MAC1B/zB,OAAO,EAAEA,OAAO,GAAGo8B;KACpB,CAAC;EACJ;EAEAC,UAAUA,CAAChhB,IAAY;IACrB,MAAMrb,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,MAAM8sB,QAAQ,GACZjhB,IAAI,KAAK,IAAI,GAAG,sBAAsB,GAAG,uBAAuB;IAClE,IAAI,CAACtC,WAAW,CAACgb,UAAU,CAAC;MAC1B/zB,OAAO,EAAEA,OAAO,GAAG,IAAI,GAAGs8B;KAC3B,CAAC;EACJ;EAEAC,mBAAmBA,CAAA;IACjB,MAAMv8B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACuJ,WAAW,CAACgb,UAAU,CAAC;MAC1B/zB,OAAO,EAAEA,OAAO,GAAG;KACpB,CAAC;EACJ;EAEAw8B,mBAAmBA,CAAA;IACjB,MAAMx8B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACuJ,WAAW,CAACgb,UAAU,CAAC;MAC1B/zB,OAAO,EAAEA,OAAO,GAAG;KACpB,CAAC;EACJ;EAEAy8B,eAAeA,CAAA;IACb,MAAMz8B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,MAAMktB,YAAY,GAAG18B,OAAO,CACzBggB,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAAA,CAC9BA,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5BA,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5BA,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAAA,CAC9BA,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5BA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAAA,CACnBA,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC;IAEtC,IAAI,CAACjH,WAAW,CAACgb,UAAU,CAAC;MAAE/zB,OAAO,EAAE08B;IAAY,CAAE,CAAC;EACxD;EAEAC,uBAAuBA,CAAA;IACrB,IAAI,CAACtqB,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEA;EACAuqB,0BAA0BA,CAAA;IACxB,IAAI,CAACnqB,sBAAsB,GAAG,EAAE;EAClC;EAEAoqB,eAAeA,CAACC,QAAgB,EAAEC,UAAkB;IAClD,MAAM/8B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,MAAMwtB,gBAAgB,GAAGh9B,OAAO,CAACggB,OAAO,CAAC8c,QAAQ,EAAEC,UAAU,CAAC;IAC9D,IAAI,CAAChkB,WAAW,CAACgb,UAAU,CAAC;MAAE/zB,OAAO,EAAEg9B;IAAgB,CAAE,CAAC;IAC1D,IAAI,CAACJ,0BAA0B,EAAE;EACnC;EAEAK,gBAAgBA,CAACH,QAAgB;IAC/B,IAAI,CAACrqB,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACiN,MAAM,CAC7Dwd,CAAC,IAAKA,CAAC,CAACJ,QAAQ,KAAKA,QAAQ,CAC/B;EACH;EAEA;EACAz1B,gBAAgBA,CAACgU,IAAY;IAC3B,MAAM8hB,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAAC9hB,IAAI,GAAG,MAAM;IAEnB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV8hB,KAAK,CAACG,MAAM,GAAG,SAAS;QACxB;MACF,KAAK,OAAO;QACVH,KAAK,CAACG,MAAM,GAAG,SAAS;QACxB;MACF,KAAK,OAAO;QACVH,KAAK,CAACG,MAAM,GAAG,SAAS;QACxB;MACF,KAAK,UAAU;QACbH,KAAK,CAACG,MAAM,GAAG,4CAA4C;QAC3D;;IAGJH,KAAK,CAACI,QAAQ,GAAI9hB,KAAU,IAAI;MAC9B,IAAI,CAACD,cAAc,CAACC,KAAK,CAAC;IAC5B,CAAC;IAED0hB,KAAK,CAACrb,KAAK,EAAE;EACf;EAEA;EACA0b,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACpyB,iBAAiB,EAAE;MAC1B,OAAO,cAAc,IAAI,CAACA,iBAAiB,CAAC9L,MAAM,EAAEd,QAAQ,KAAK;;IAEnE,IAAI,IAAI,CAAC8G,gBAAgB,EAAE;MACzB,OAAO,4BAA4B;;IAErC,OAAO,wBAAwB;EACjC;EAEAm4B,aAAaA,CAAChiB,KAAU;IACtB,MAAMzb,OAAO,GAAGyb,KAAK,CAACE,MAAM,CAACnM,KAAK;IAClC,IAAI,CAACuJ,WAAW,CAACgb,UAAU,CAAC;MAAE/zB;IAAO,CAAE,CAAC;IAExC;IACA,IAAI,CAAC09B,cAAc,CAAC19B,OAAO,CAAC;IAC5B,IAAI,CAAC29B,cAAc,CAAC39B,OAAO,CAAC;IAC5B,IAAI,CAAC49B,cAAc,CAAC59B,OAAO,CAAC;IAE5B;IACA,IAAI,CAAC69B,YAAY,EAAE;EACrB;EAEAC,cAAcA,CAACriB,KAAU;IACvB,IAAIA,KAAK,CAACzD,GAAG,KAAK,OAAO,IAAI,CAACyD,KAAK,CAACsB,QAAQ,EAAE;MAC5CtB,KAAK,CAACuB,cAAc,EAAE;MACtB,IAAI,CAACtX,WAAW,EAAE;KACnB,MAAM,IAAI+V,KAAK,CAACzD,GAAG,KAAK,SAAS,IAAI,IAAI,CAACvG,kBAAkB,CAACtN,MAAM,GAAG,CAAC,EAAE;MACxEsX,KAAK,CAACuB,cAAc,EAAE;MACtB,IAAI,CAACtL,oBAAoB,GAAGgN,IAAI,CAACqf,GAAG,CAAC,CAAC,EAAE,IAAI,CAACrsB,oBAAoB,GAAG,CAAC,CAAC;KACvE,MAAM,IACL+J,KAAK,CAACzD,GAAG,KAAK,WAAW,IACzB,IAAI,CAACvG,kBAAkB,CAACtN,MAAM,GAAG,CAAC,EAClC;MACAsX,KAAK,CAACuB,cAAc,EAAE;MACtB,IAAI,CAACtL,oBAAoB,GAAGgN,IAAI,CAACsf,GAAG,CAClC,IAAI,CAACvsB,kBAAkB,CAACtN,MAAM,GAAG,CAAC,EAClC,IAAI,CAACuN,oBAAoB,GAAG,CAAC,CAC9B;KACF,MAAM,IAAI+J,KAAK,CAACzD,GAAG,KAAK,KAAK,IAAI,IAAI,CAACvG,kBAAkB,CAACtN,MAAM,GAAG,CAAC,EAAE;MACpEsX,KAAK,CAACuB,cAAc,EAAE;MACtB,IAAI,CAACihB,aAAa,CAAC,IAAI,CAACxsB,kBAAkB,CAAC,IAAI,CAACC,oBAAoB,CAAC,CAAC;;EAE1E;EAEAwsB,YAAYA,CAACziB,KAAU;IACrB;IACA,IAAI,CAAC0iB,iBAAiB,EAAE;EAC1B;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACzlB,YAAY,GAAG,IAAI;EAC1B;EAEA0lB,WAAWA,CAAA;IACT,IAAI,CAAC1lB,YAAY,GAAG,KAAK;IACzB;IACAiF,UAAU,CAAC,MAAK;MACd,IAAI,CAACnM,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACI,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACI,kBAAkB,GAAG,EAAE;IAC9B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAqsB,YAAYA,CAAC7iB,KAAU;IACrB,MAAM8iB,aAAa,GAAG9iB,KAAK,CAAC8iB,aAAa,IAAKC,MAAc,CAACD,aAAa;IAC1E,MAAME,UAAU,GAAGF,aAAa,CAACG,OAAO,CAAC,MAAM,CAAC;IAEhD;IACA,IAAID,UAAU,CAAC7e,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC/B;MACA,IAAI,CAAC+e,WAAW,CAACF,UAAU,CAAC;;EAEhC;EAEAG,aAAaA,CAACnjB,KAAU;IACtB;EAAA;EAGF;EACAiiB,cAAcA,CAAC19B,OAAe;IAC5B,MAAM6+B,YAAY,GAAG7+B,OAAO,CAAC8+B,KAAK,CAAC,SAAS,CAAC;IAC7C,IAAID,YAAY,EAAE;MAChB,MAAMxD,KAAK,GAAGwD,YAAY,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACptB,kBAAkB,GAAG,IAAI,CAACstB,gBAAgB,CAAC1D,KAAK,CAAC;MACtD,IAAI,CAAC3pB,oBAAoB,GAAG,CAAC;KAC9B,MAAM;MACL,IAAI,CAACD,kBAAkB,GAAG,EAAE;;EAEhC;EAEAksB,cAAcA,CAAC39B,OAAe;IAC5B,MAAMg/B,YAAY,GAAGh/B,OAAO,CAAC8+B,KAAK,CAAC,SAAS,CAAC;IAC7C,IAAIE,YAAY,EAAE;MAChB,MAAM3D,KAAK,GAAG2D,YAAY,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACntB,kBAAkB,GAAG,IAAI,CAACotB,mBAAmB,CAAC5D,KAAK,CAAC;KAC1D,MAAM;MACL,IAAI,CAACxpB,kBAAkB,GAAG,EAAE;;EAEhC;EAEA+rB,cAAcA,CAAC59B,OAAe;IAC5B,MAAMk/B,YAAY,GAAGl/B,OAAO,CAAC8+B,KAAK,CAAC,UAAU,CAAC;IAC9C,IAAII,YAAY,EAAE;MAChB,MAAM7D,KAAK,GAAG6D,YAAY,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACjtB,kBAAkB,GAAG,IAAI,CAACktB,mBAAmB,CAAC9D,KAAK,CAAC;KAC1D,MAAM;MACL,IAAI,CAACppB,kBAAkB,GAAG,EAAE;;EAEhC;EAEA0sB,WAAWA,CAAC3+B,OAAe;IACzB,MAAMo/B,QAAQ,GAAG,sBAAsB;IACvC,MAAMC,KAAK,GAAGr/B,OAAO,CAAC8+B,KAAK,CAACM,QAAQ,CAAC;IACrC,IAAIC,KAAK,EAAE;MACT;MACAtkB,OAAO,CAAC6D,GAAG,CAAC,iBAAiB,EAAEygB,KAAK,CAAC;;EAEzC;EAEApB,aAAaA,CAAC7X,IAAS;IACrB,MAAMpmB,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,MAAM8vB,UAAU,GAAGt/B,OAAO,CAACggB,OAAO,CAAC,OAAO,EAAE,IAAIoG,IAAI,CAAC5nB,QAAQ,GAAG,CAAC;IACjE,IAAI,CAACua,WAAW,CAACgb,UAAU,CAAC;MAAE/zB,OAAO,EAAEs/B;IAAU,CAAE,CAAC;IACpD,IAAI,CAAC7tB,kBAAkB,GAAG,EAAE;EAC9B;EAEA8tB,aAAaA,CAACC,OAAY;IACxB,MAAMx/B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,MAAM8vB,UAAU,GAAGt/B,OAAO,CAACggB,OAAO,CAAC,OAAO,EAAE,IAAIwf,OAAO,CAAC54B,IAAI,GAAG,CAAC;IAChE,IAAI,CAACmS,WAAW,CAACgb,UAAU,CAAC;MAAE/zB,OAAO,EAAEs/B;IAAU,CAAE,CAAC;IACpD,IAAI,CAACztB,kBAAkB,GAAG,EAAE;EAC9B;EAEA4tB,aAAaA,CAACC,OAAY;IACxB,MAAM1/B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,MAAM8vB,UAAU,GAAGt/B,OAAO,CAACggB,OAAO,CAAC,QAAQ,EAAE,IAAI0f,OAAO,CAAC94B,IAAI,GAAG,CAAC;IACjE,IAAI,CAACmS,WAAW,CAACgb,UAAU,CAAC;MAAE/zB,OAAO,EAAEs/B;IAAU,CAAE,CAAC;IACpD,IAAI,CAACrtB,kBAAkB,GAAG,EAAE;EAC9B;EAEA0tB,uBAAuBA,CAAC3jB,KAAa;IACnC,IAAI,CAACtK,oBAAoB,GAAGsK,KAAK;EACnC;EAEAmiB,iBAAiBA,CAAA;IACf,MAAMn+B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACkuB,cAAc,CAAC19B,OAAO,CAAC;IAC5B,IAAI,CAAC29B,cAAc,CAAC39B,OAAO,CAAC;IAC5B,IAAI,CAAC49B,cAAc,CAAC59B,OAAO,CAAC;EAC9B;EAEA;EACA4/B,iBAAiBA,CAAA;IACf,MAAM5/B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,OAAOxP,OAAO,CAACmE,MAAM;EACvB;EAEA07B,gBAAgBA,CAAA;IACd,MAAM7/B,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,OACExP,OAAO,CAAC4f,QAAQ,CAAC,GAAG,CAAC,IACrB5f,OAAO,CAAC4f,QAAQ,CAAC,GAAG,CAAC,IACrB5f,OAAO,CAAC4f,QAAQ,CAAC,GAAG,CAAC,IACrB5f,OAAO,CAAC4f,QAAQ,CAAC,GAAG,CAAC;EAEzB;EAEA;EACAkgB,eAAeA,CAACC,YAAoB;IAClC,MAAMC,SAAS,GAA8B;MAC3CC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE,SAAS;MACbC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE,UAAU;MACdC,EAAE,EAAE;KACL;IACD,OAAOL,SAAS,CAACD,YAAY,CAAC,IAAIA,YAAY;EAChD;EAEAO,gBAAgBA,CAAA;IACd,IAAI,CAAC5tB,oBAAoB,GAAG,IAAI;IAChC,MAAM1S,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACqD,0BAA0B,GAAG7S,OAAO;IACzC;IACA,IAAI,CAAC8S,cAAc,GAAG,gBAAgB9S,OAAO,EAAE;EACjD;EAEAugC,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC9tB,sBAAsB,GAAG,CAC5B;MAAEqqB,QAAQ,EAAE,OAAO;MAAE0D,WAAW,EAAE,CAAC,OAAO;IAAC,CAAE,EAC7C;MAAE1D,QAAQ,EAAE,OAAO;MAAE0D,WAAW,EAAE,CAAC,OAAO;IAAC,CAAE,CAC9C;EACH;EAEA;EACA3C,YAAYA,CAAA;IACV;IACA,IAAI,CAAC,IAAI,CAACn5B,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;;IAGF;IACAqV,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;IAChC,IAAI,CAACA,aAAa,GAAG8D,UAAU,CAAC,MAAK;MACnC,IAAI,CAAClZ,QAAQ,GAAG,KAAK;MACrB;IACF,CAAC,EAAE,IAAI,CAAC;EACV;EAEAq6B,gBAAgBA,CAAC1D,KAAa;IAC5B,MAAM7e,KAAK,GAAG,CACZ;MAAEjd,EAAE,EAAE,GAAG;MAAEf,QAAQ,EAAE,OAAO;MAAEoI,IAAI,EAAE;IAAc,CAAE,EACpD;MAAErH,EAAE,EAAE,GAAG;MAAEf,QAAQ,EAAE,KAAK;MAAEoI,IAAI,EAAE;IAAY,CAAE,EAChD;MAAErH,EAAE,EAAE,GAAG;MAAEf,QAAQ,EAAE,QAAQ;MAAEoI,IAAI,EAAE;IAAe,CAAE,CACvD;IAED,IAAI,CAACy0B,KAAK,EAAE,OAAO7e,KAAK;IACxB,OAAOA,KAAK,CAACkD,MAAM,CAChB0G,IAAI,IACHA,IAAI,CAAC5nB,QAAQ,CAACmhB,WAAW,EAAE,CAACC,QAAQ,CAACyb,KAAK,CAAC1b,WAAW,EAAE,CAAC,IACzDyG,IAAI,CAACxf,IAAI,CAAC+Y,WAAW,EAAE,CAACC,QAAQ,CAACyb,KAAK,CAAC1b,WAAW,EAAE,CAAC,CACxD;EACH;EAEAsf,mBAAmBA,CAAC5D,KAAa;IAC/B,MAAMoF,QAAQ,GAAG,CACf;MAAElhC,EAAE,EAAE,GAAG;MAAEqH,IAAI,EAAE,WAAW;MAAE2V,KAAK,EAAE;IAAE,CAAE,EACzC;MAAEhd,EAAE,EAAE,GAAG;MAAEqH,IAAI,EAAE,QAAQ;MAAE2V,KAAK,EAAE;IAAC,CAAE,EACrC;MAAEhd,EAAE,EAAE,GAAG;MAAEqH,IAAI,EAAE,SAAS;MAAE2V,KAAK,EAAE;IAAE,CAAE,CACxC;IAED,IAAI,CAAC8e,KAAK,EAAE,OAAOoF,QAAQ;IAC3B,OAAOA,QAAQ,CAAC/gB,MAAM,CAAE8f,OAAO,IAC7BA,OAAO,CAAC54B,IAAI,CAAC+Y,WAAW,EAAE,CAACC,QAAQ,CAACyb,KAAK,CAAC1b,WAAW,EAAE,CAAC,CACzD;EACH;EAEAwf,mBAAmBA,CAAC9D,KAAa;IAC/B,MAAMqF,QAAQ,GAAG,CACf;MAAEnhC,EAAE,EAAE,GAAG;MAAEqH,IAAI,EAAE,MAAM;MAAE+5B,WAAW,EAAE;IAAiB,CAAE,EACzD;MAAEphC,EAAE,EAAE,GAAG;MAAEqH,IAAI,EAAE,OAAO;MAAE+5B,WAAW,EAAE;IAAyB,CAAE,EAClE;MAAEphC,EAAE,EAAE,GAAG;MAAEqH,IAAI,EAAE,QAAQ;MAAE+5B,WAAW,EAAE;IAAmB,CAAE,CAC9D;IAED,IAAI,CAACtF,KAAK,EAAE,OAAOqF,QAAQ;IAC3B,OAAOA,QAAQ,CAAChhB,MAAM,CAAEggB,OAAO,IAC7BA,OAAO,CAAC94B,IAAI,CAAC+Y,WAAW,EAAE,CAACC,QAAQ,CAACyb,KAAK,CAAC1b,WAAW,EAAE,CAAC,CACzD;EACH;EAEA;EACA/X,UAAUA,CAAA;IACR;IACA,IAAI,CAACW,YAAY,CAAC8uB,QAAQ,CAAC,2BAA2B,CAAC;EACzD;EAEAuJ,kBAAkBA,CAAA;IAChB,IAAI,CAAC/sB,kBAAkB,GAAG,IAAI;EAChC;EAEAgtB,iBAAiBA,CAAA;IACf,IAAI,CAACzsB,iBAAiB,GAAG,IAAI;EAC/B;EAEA0sB,eAAeA,CAAA;IACb,IAAI,CAAC5tB,eAAe,GAAG,IAAI;EAC7B;EAEA6tB,gBAAgBA,CAAA;IACd,IAAI,CAACzvB,gBAAgB,GAAG,KAAK;EAC/B;EAEA0vB,gBAAgBA,CAACC,KAAa;IAC5B,MAAMjhC,OAAO,GAAG,IAAI,CAAC+Y,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IAC5D,IAAI,CAACuJ,WAAW,CAACgb,UAAU,CAAC;MAC1B/zB,OAAO,EAAEA,OAAO,GAAGihC;KACpB,CAAC;IACF,IAAI,CAACF,gBAAgB,EAAE;EACzB;EAEA;EACAG,aAAaA,CAACzlB,KAAW;IACvB;IACA,IAAIA,KAAK,EAAE;MACT,IAAI,CAACpQ,WAAW,GAAGoQ,KAAK,CAACE,MAAM,CAACnM,KAAK;;EAEzC;EAkMAvI,oBAAoBA,CAACymB,QAAa;IAChC,OAAOA,QAAQ,EAAE5U,MAAM,IAAI,EAAE;EAC/B;EAEA3S,mBAAmBA,CAACunB,QAAa;IAC/B,IAAI,CAACrnB,qBAAqB,GAAGqnB,QAAQ;EACvC;EAEA/mB,WAAWA,CAAC5E,KAAU;IACpB,MAAM0f,cAAc,GAAG,IAAI,CAAC1I,WAAW,CAACiC,GAAG,CAAC,SAAS,CAAC,EAAExL,KAAK,IAAI,EAAE;IACnE,MAAM8vB,UAAU,GAAG7d,cAAc,GAAG1f,KAAK,CAACA,KAAK;IAC/C,IAAI,CAACgX,WAAW,CAACgb,UAAU,CAAC;MAAE/zB,OAAO,EAAEs/B;IAAU,CAAE,CAAC;IAEpD;IACA,IAAI,CAAC31B,eAAe,GAAG,KAAK;IAE5B;IACAiU,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACujB,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAACpjB,aAAa,CAACqjB,KAAK,EAAE;;IAE9C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAr5B,aAAaA,CAAA;IACX,IAAI,CAAC2B,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC6M,kBAAkB,GAAG,KAAK;EACjC;EAEA8qB,cAAcA,CAAA;IACZ,IAAI,CAACnkB,SAAS,CAAC,OAAmB,CAAC;EACrC;EAEAokB,cAAcA,CAAA;IACZ,IAAI,CAACpkB,SAAS,CAAC,OAAmB,CAAC;EACrC;;;uBAx8GWlV,oBAAoB,EAAAtK,EAAA,CAAA6jC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/jC,EAAA,CAAA6jC,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAhkC,EAAA,CAAA6jC,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAlkC,EAAA,CAAA6jC,iBAAA,CAAAM,EAAA,CAAAx5B,cAAA,GAAA3K,EAAA,CAAA6jC,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAArkC,EAAA,CAAA6jC,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAvkC,EAAA,CAAA6jC,iBAAA,CAAA7jC,EAAA,CAAAwkC,iBAAA,GAAAxkC,EAAA,CAAA6jC,iBAAA,CAAAY,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAApBp6B,oBAAoB;MAAAq6B,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UC3BjC9kC,EAAA,CAAAE,cAAA,aAEC;UAOKF,EAAA,CAAAkB,UAAA,mBAAA8jC,sDAAA;YAAA,OAASD,GAAA,CAAA7X,qBAAA,EAAuB;UAAA,EAAC;UAGjCltB,EAAA,CAAAC,SAAA,WAAkE;UACpED,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,aAA8C;UAMxCF,EAAA,CAAAkB,UAAA,mBAAA+jC,mDAAA;YAAA,OAASF,GAAA,CAAArjC,eAAA,CAAAqjC,GAAA,CAAAvkC,gBAAA,kBAAAukC,GAAA,CAAAvkC,gBAAA,CAAAoB,EAAA,CAAsC;UAAA,EAAC;UAJlD5B,EAAA,CAAAI,YAAA,EAKE;UACFJ,EAAA,CAAA4C,UAAA,IAAAsiC,mCAAA,iBAGO;UACTllC,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,aAA4B;UAExBF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,eAAsD;UACpDF,EAAA,CAAA4C,UAAA,KAAAuiC,oCAAA,kBAkBM;UACNnlC,EAAA,CAAA4C,UAAA,KAAAwiC,qCAAA,mBAMO;UACTplC,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,eAAqC;UAEjCF,EAAA,CAAAkB,UAAA,mBAAAmkC,uDAAA;YAAA,OAASN,GAAA,CAAApB,cAAA,EAAgB;UAAA,EAAC;UAI1B3jC,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAkB,UAAA,mBAAAokC,uDAAA;YAAA,OAASP,GAAA,CAAAnB,cAAA,EAAgB;UAAA,EAAC;UAI1B5jC,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAMC;UALCF,EAAA,CAAAkB,UAAA,mBAAAqkC,uDAAA;YAAA,OAASR,GAAA,CAAA1jB,YAAA,EAAc;UAAA,EAAC;UAMxBrhB,EAAA,CAAAC,SAAA,aAA6B;UAC/BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAHCF,EAAA,CAAAkB,UAAA,mBAAAskC,uDAAA;YAAA,OAAST,GAAA,CAAArjB,cAAA,EAAgB;UAAA,EAAC;UAI1B1hB,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAE,cAAA,oBAIC;UADCF,EAAA,CAAAkB,UAAA,oBAAAukC,sDAAA1gC,MAAA;YAAA,OAAUggC,GAAA,CAAAzY,QAAA,CAAAvnB,MAAA,CAAgB;UAAA,EAAC;UAG3B/E,EAAA,CAAA4C,UAAA,KAAA8iC,oCAAA,kBAOM;UAGN1lC,EAAA,CAAA4C,UAAA,KAAA+iC,oCAAA,kBAaM;UAGN3lC,EAAA,CAAA4C,UAAA,KAAAgjC,oCAAA,kBAyMM;UACR5lC,EAAA,CAAAI,YAAA,EAAO;UAGPJ,EAAA,CAAAE,cAAA,kBAEC;UAGGF,EAAA,CAAAkB,UAAA,sBAAA2kC,wDAAA;YAAA,OAAYd,GAAA,CAAAh9B,WAAA,EAAa;UAAA,EAAC;UAI1B/H,EAAA,CAAAE,cAAA,eAAwB;UAGpBF,EAAA,CAAAkB,UAAA,mBAAA4kC,uDAAA;YAAA,OAASf,GAAA,CAAAzO,iBAAA,EAAmB;UAAA,EAAC;UAM7Bt2B,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAOC;UALCF,EAAA,CAAAkB,UAAA,mBAAA6kC,uDAAA;YAAA,OAAShB,GAAA,CAAA/gB,oBAAA,EAAsB;UAAA,EAAC;UAMhChkB,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAE,cAAA,eAAoB;UAOhBF,EAAA,CAAAkB,UAAA,mBAAA8kC,yDAAAjhC,MAAA;YAAA,OAASggC,GAAA,CAAAjF,aAAA,CAAA/6B,MAAA,CAAqB;UAAA,EAAC,qBAAAkhC,2DAAAlhC,MAAA;YAAA,OACpBggC,GAAA,CAAA5E,cAAA,CAAAp7B,MAAA,CAAsB;UAAA,EADF,mBAAAmhC,yDAAA;YAAA,OAEtBnB,GAAA,CAAAtE,YAAA,EAAc;UAAA,EAFQ,kBAAA0F,wDAAA;YAAA,OAGvBpB,GAAA,CAAArE,WAAA,EAAa;UAAA,EAHU;UASjC1gC,EAAA,CAAAG,MAAA;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAIbJ,EAAA,CAAAE,cAAA,eAAwB;UAEtBF,EAAA,CAAA4C,UAAA,KAAAwjC,uCAAA,qBAaS;UAGTpmC,EAAA,CAAA4C,UAAA,KAAAyjC,uCAAA,qBAUS;UACXrmC,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAA4C,UAAA,KAAA0jC,oCAAA,kBA6BM;UAGNtmC,EAAA,CAAA4C,UAAA,KAAA2jC,oCAAA,mBA4DM;UAGNvmC,EAAA,CAAAE,cAAA,qBAOE;UAHAF,EAAA,CAAAkB,UAAA,oBAAAslC,uDAAAzhC,MAAA;YAAA,OAAUggC,GAAA,CAAAlnB,cAAA,CAAA9Y,MAAA,CAAsB;UAAA,EAAC;UAJnC/E,EAAA,CAAAI,YAAA,EAOE;UAGFJ,EAAA,CAAA4C,UAAA,KAAA6jC,oCAAA,kBAIO;UACTzmC,EAAA,CAAAI,YAAA,EAAM;;;;;UApgBIJ,EAAA,CAAAK,SAAA,GAAqE;UAArEL,EAAA,CAAA6B,UAAA,SAAAkjC,GAAA,CAAAvkC,gBAAA,kBAAAukC,GAAA,CAAAvkC,gBAAA,CAAAsB,KAAA,yCAAA9B,EAAA,CAAA+B,aAAA,CAAqE,QAAAgjC,GAAA,CAAAvkC,gBAAA,kBAAAukC,GAAA,CAAAvkC,gBAAA,CAAAK,QAAA;UAMpEb,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAA6B,UAAA,SAAAkjC,GAAA,CAAAvkC,gBAAA,kBAAAukC,GAAA,CAAAvkC,gBAAA,CAAAC,QAAA,CAAgC;UAOjCT,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,OAAAykC,GAAA,CAAAvkC,gBAAA,kBAAAukC,GAAA,CAAAvkC,gBAAA,CAAAK,QAAA,wBACF;UAGKb,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAA6B,UAAA,SAAAkjC,GAAA,CAAA1sB,YAAA,CAAkB;UAkBdrY,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAA6B,UAAA,UAAAkjC,GAAA,CAAA1sB,YAAA,CAAmB;UA8B5BrY,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAAqE,WAAA,iBAAA0gC,GAAA,CAAAn3B,UAAA,CAAiC,mBAAAm3B,GAAA,CAAAn3B,UAAA;UAuB/B5N,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA6B,UAAA,SAAAkjC,GAAA,CAAA95B,OAAA,CAAa;UAWhBjL,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAA6B,UAAA,UAAAkjC,GAAA,CAAA95B,OAAA,IAAA85B,GAAA,CAAAl+B,QAAA,CAAAL,MAAA,OAAuC;UAepCxG,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAA6B,UAAA,UAAAkjC,GAAA,CAAA95B,OAAA,IAAA85B,GAAA,CAAAl+B,QAAA,CAAAL,MAAA,KAAqC;UAiNzCxG,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAA6B,UAAA,cAAAkjC,GAAA,CAAA3pB,WAAA,CAAyB;UAUrBpb,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAqE,WAAA,iBAAA0gC,GAAA,CAAA/4B,eAAA,CAAsC,mBAAA+4B,GAAA,CAAA/4B,eAAA;UAUtChM,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAqE,WAAA,iBAAA0gC,GAAA,CAAAlsB,kBAAA,CAAyC,mBAAAksB,GAAA,CAAAlsB,kBAAA;UAezC7Y,EAAA,CAAAK,SAAA,GAAsE;UAAtEL,EAAA,CAAA6B,UAAA,cAAAkjC,GAAA,CAAAvkC,gBAAA,IAAAukC,GAAA,CAAAp9B,gBAAA,IAAAo9B,GAAA,CAAA58B,gBAAA,CAAsE;UAiBrEnI,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAA6B,UAAA,YAAA6kC,QAAA,GAAA3B,GAAA,CAAA3pB,WAAA,CAAAiC,GAAA,8BAAAqpB,QAAA,CAAA70B,KAAA,kBAAA60B,QAAA,CAAA70B,KAAA,CAAAyL,IAAA,IAAgD;UAgBhDtd,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAA6B,UAAA,UAAA8kC,QAAA,GAAA5B,GAAA,CAAA3pB,WAAA,CAAAiC,GAAA,8BAAAspB,QAAA,CAAA90B,KAAA,kBAAA80B,QAAA,CAAA90B,KAAA,CAAAyL,IAAA,GAA+C;UAgBrDtd,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAA6B,UAAA,SAAAkjC,GAAA,CAAA/4B,eAAA,CAAqB;UAgCrBhM,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAA6B,UAAA,SAAAkjC,GAAA,CAAAlsB,kBAAA,CAAwB;UAmEzB7Y,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAA6B,UAAA,WAAAkjC,GAAA,CAAArB,kBAAA,GAA+B;UAM9B1jC,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAA6B,UAAA,SAAAkjC,GAAA,CAAA/4B,eAAA,IAAA+4B,GAAA,CAAAlsB,kBAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}