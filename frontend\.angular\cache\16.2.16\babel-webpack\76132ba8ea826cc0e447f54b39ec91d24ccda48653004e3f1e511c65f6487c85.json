{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/logger.service\";\nimport * as i4 from \"@angular/common\";\nexport class MessageLayoutComponent {\n  constructor(MessageService, route, logger) {\n    this.MessageService = MessageService;\n    this.route = route;\n    this.logger = logger;\n    this.subscriptions = [];\n    this.context = 'messages';\n  }\n  ngOnInit() {\n    // Détermine le contexte (messages ou notifications)\n    this.context = this.route.snapshot.data['context'] || 'messages';\n    if (this.context === 'messages') {\n      // S'abonner aux changements de conversation active\n      this.subscriptions.push(this.MessageService.activeConversation$.subscribe(conversationId => {\n        // Ne s'abonner aux messages que si une conversation est sélectionnée\n        if (conversationId) {\n          this.conversationId = conversationId;\n          // Désabonner de l'ancienne souscription si elle existe\n          this.subscriptions.forEach(sub => sub.unsubscribe());\n          this.subscriptions = [];\n          // S'abonner aux nouveaux messages pour cette conversation\n          this.subscriptions.push(this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n            next: message => {\n              // Gestion des nouveaux messages\n            },\n            error: err => this.logger.error('MessageLayout', 'Error in message subscription', err)\n          }));\n        }\n      }));\n    }\n    // Ajoutez ici la logique spécifique aux notifications si nécessaire\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  static {\n    this.ɵfac = function MessageLayoutComponent_Factory(t) {\n      return new (t || MessageLayoutComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageLayoutComponent,\n      selectors: [[\"app-message-layout\"]],\n      decls: 4,\n      vars: 4,\n      consts: [[1, \"layout-container\", \"futuristic-layout\"], [1, \"main-content\", \"futuristic-main-content\"]],\n      template: function MessageLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵelement(3, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"sidebar-hidden\", !i0.ɵɵpipeBind1(2, 2, ctx.sidebarVisible$));\n        }\n      },\n      dependencies: [i2.RouterOutlet, i4.AsyncPipe],\n      styles: [\".layout-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  width: 100%;\\n  overflow: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  transition: margin-left 0.3s ease;\\n  overflow: hidden;\\n}\\n\\n.sidebar-hidden[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n}\\n\\n\\n\\n.futuristic-layout[_ngcontent-%COMP%] {\\n  background-color: var(--dark-bg);\\n  color: var(--text-light);\\n}\\n\\n.futuristic-main-content[_ngcontent-%COMP%] {\\n  background-color: var(--dark-bg);\\n  position: relative;\\n}\\n\\n\\n\\n.futuristic-layout[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: linear-gradient(\\n      rgba(0, 247, 255, 0.03) 1px,\\n      transparent 1px\\n    ),\\n    linear-gradient(90deg, rgba(0, 247, 255, 0.03) 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm1lc3NhZ2UtbGF5b3V0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFhO0VBQ2IsYUFBYTtFQUNiLFdBQVc7RUFDWCxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxZQUFZO0VBQ1osaUNBQWlDO0VBQ2pDLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUEsMkNBQTJDO0FBQzNDO0VBQ0UsZ0NBQWdDO0VBQ2hDLHdCQUF3QjtBQUMxQjs7QUFFQTtFQUNFLGdDQUFnQztFQUNoQyxrQkFBa0I7QUFDcEI7O0FBRUEsb0NBQW9DO0FBQ3BDO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixNQUFNO0VBQ04sT0FBTztFQUNQLFFBQVE7RUFDUixTQUFTO0VBQ1Q7Ozs7d0VBSXNFO0VBQ3RFLDBCQUEwQjtFQUMxQixvQkFBb0I7RUFDcEIsVUFBVTtBQUNaIiwiZmlsZSI6Im1lc3NhZ2UtbGF5b3V0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIubGF5b3V0LWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBoZWlnaHQ6IDEwMHZoO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbn1cclxuXHJcbi5tYWluLWNvbnRlbnQge1xyXG4gIGZsZXgtZ3JvdzogMTtcclxuICB0cmFuc2l0aW9uOiBtYXJnaW4tbGVmdCAwLjNzIGVhc2U7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG5cclxuLnNpZGViYXItaGlkZGVuIHtcclxuICBtYXJnaW4tbGVmdDogMDtcclxufVxyXG5cclxuLyogU3R5bGVzIGZ1dHVyaXN0ZXMgcG91ciBsYSBtaXNlIGVuIHBhZ2UgKi9cclxuLmZ1dHVyaXN0aWMtbGF5b3V0IHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1kYXJrLWJnKTtcclxuICBjb2xvcjogdmFyKC0tdGV4dC1saWdodCk7XHJcbn1cclxuXHJcbi5mdXR1cmlzdGljLW1haW4tY29udGVudCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tZGFyay1iZyk7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG59XHJcblxyXG4vKiBFZmZldCBkZSBncmlsbGUgZW4gYXJyacOocmUtcGxhbiAqL1xyXG4uZnV0dXJpc3RpYy1sYXlvdXQ6OmJlZm9yZSB7XHJcbiAgY29udGVudDogXCJcIjtcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgdG9wOiAwO1xyXG4gIGxlZnQ6IDA7XHJcbiAgcmlnaHQ6IDA7XHJcbiAgYm90dG9tOiAwO1xyXG4gIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudChcclxuICAgICAgcmdiYSgwLCAyNDcsIDI1NSwgMC4wMykgMXB4LFxyXG4gICAgICB0cmFuc3BhcmVudCAxcHhcclxuICAgICksXHJcbiAgICBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHJnYmEoMCwgMjQ3LCAyNTUsIDAuMDMpIDFweCwgdHJhbnNwYXJlbnQgMXB4KTtcclxuICBiYWNrZ3JvdW5kLXNpemU6IDIwcHggMjBweDtcclxuICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxuICB6LWluZGV4OiAwO1xyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvbWVzc2FnZS1sYXlvdXQvbWVzc2FnZS1sYXlvdXQuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQWE7RUFDYixhQUFhO0VBQ2IsV0FBVztFQUNYLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFlBQVk7RUFDWixpQ0FBaUM7RUFDakMsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsY0FBYztBQUNoQjs7QUFFQSwyQ0FBMkM7QUFDM0M7RUFDRSxnQ0FBZ0M7RUFDaEMsd0JBQXdCO0FBQzFCOztBQUVBO0VBQ0UsZ0NBQWdDO0VBQ2hDLGtCQUFrQjtBQUNwQjs7QUFFQSxvQ0FBb0M7QUFDcEM7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLE1BQU07RUFDTixPQUFPO0VBQ1AsUUFBUTtFQUNSLFNBQVM7RUFDVDs7Ozt3RUFJc0U7RUFDdEUsMEJBQTBCO0VBQzFCLG9CQUFvQjtFQUNwQixVQUFVO0FBQ1o7O0FBRUEsdzlEQUF3OUQiLCJzb3VyY2VzQ29udGVudCI6WyIubGF5b3V0LWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBoZWlnaHQ6IDEwMHZoO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbn1cclxuXHJcbi5tYWluLWNvbnRlbnQge1xyXG4gIGZsZXgtZ3JvdzogMTtcclxuICB0cmFuc2l0aW9uOiBtYXJnaW4tbGVmdCAwLjNzIGVhc2U7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG5cclxuLnNpZGViYXItaGlkZGVuIHtcclxuICBtYXJnaW4tbGVmdDogMDtcclxufVxyXG5cclxuLyogU3R5bGVzIGZ1dHVyaXN0ZXMgcG91ciBsYSBtaXNlIGVuIHBhZ2UgKi9cclxuLmZ1dHVyaXN0aWMtbGF5b3V0IHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1kYXJrLWJnKTtcclxuICBjb2xvcjogdmFyKC0tdGV4dC1saWdodCk7XHJcbn1cclxuXHJcbi5mdXR1cmlzdGljLW1haW4tY29udGVudCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tZGFyay1iZyk7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG59XHJcblxyXG4vKiBFZmZldCBkZSBncmlsbGUgZW4gYXJyacODwqhyZS1wbGFuICovXHJcbi5mdXR1cmlzdGljLWxheW91dDo6YmVmb3JlIHtcclxuICBjb250ZW50OiBcIlwiO1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZC1pbWFnZTogbGluZWFyLWdyYWRpZW50KFxyXG4gICAgICByZ2JhKDAsIDI0NywgMjU1LCAwLjAzKSAxcHgsXHJcbiAgICAgIHRyYW5zcGFyZW50IDFweFxyXG4gICAgKSxcclxuICAgIGxpbmVhci1ncmFkaWVudCg5MGRlZywgcmdiYSgwLCAyNDcsIDI1NSwgMC4wMykgMXB4LCB0cmFuc3BhcmVudCAxcHgpO1xyXG4gIGJhY2tncm91bmQtc2l6ZTogMjBweCAyMHB4O1xyXG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xyXG4gIHotaW5kZXg6IDA7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MessageLayoutComponent", "constructor", "MessageService", "route", "logger", "subscriptions", "context", "ngOnInit", "snapshot", "data", "push", "activeConversation$", "subscribe", "conversationId", "for<PERSON>ach", "sub", "unsubscribe", "subscribeToNewMessages", "next", "message", "error", "err", "ngOnDestroy", "i0", "ɵɵdirectiveInject", "i1", "i2", "ActivatedRoute", "i3", "LoggerService", "selectors", "decls", "vars", "consts", "template", "MessageLayoutComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵclassProp", "ɵɵpipeBind1", "sidebarVisible$"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-layout\\message-layout.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-layout\\message-layout.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { BehaviorSubject, Subscription } from 'rxjs';\nimport { ActivatedRoute } from '@angular/router';\nimport { MessageService } from '@app/services/message.service';\nimport { LoggerService } from '@app/services/logger.service';\n@Component({\n  selector: 'app-message-layout',\n  templateUrl: './message-layout.component.html',\n  styleUrls: ['./message-layout.component.css'],\n  // schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})\nexport class MessageLayoutComponent implements OnInit, OnDestroy {\n  private subscriptions: Subscription[] = [];\n  context: string = 'messages';\n  conversationId: any;\n  constructor(\n    private MessageService: MessageService,\n    private route: ActivatedRoute,\n    private logger: LoggerService\n  ) {}\n\n  ngOnInit() {\n    // Détermine le contexte (messages ou notifications)\n    this.context = this.route.snapshot.data['context'] || 'messages';\n\n    if (this.context === 'messages') {\n      // S'abonner aux changements de conversation active\n      this.subscriptions.push(\n        this.MessageService.activeConversation$.subscribe((conversationId) => {\n          // Ne s'abonner aux messages que si une conversation est sélectionnée\n          if (conversationId) {\n            this.conversationId = conversationId;\n\n            // Désabonner de l'ancienne souscription si elle existe\n            this.subscriptions.forEach((sub) => sub.unsubscribe());\n            this.subscriptions = [];\n\n            // S'abonner aux nouveaux messages pour cette conversation\n            this.subscriptions.push(\n              this.MessageService.subscribeToNewMessages(\n                conversationId\n              ).subscribe({\n                next: (message) => {\n                  // Gestion des nouveaux messages\n                },\n                error: (err) =>\n                  this.logger.error(\n                    'MessageLayout',\n                    'Error in message subscription',\n                    err\n                  ),\n              })\n            );\n          }\n        })\n      );\n    }\n    // Ajoutez ici la logique spécifique aux notifications si nécessaire\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n}\n", "<div class=\"layout-container futuristic-layout\">\n  <!-- <app-messages-sidebar *ngIf=\"context === 'messages'\"></app-messages-sidebar> -->\n  <div\n    class=\"main-content futuristic-main-content\"\n    [class.sidebar-hidden]=\"!(sidebarVisible$ | async)\"\n  >\n    <router-outlet></router-outlet>\n  </div>\n</div>\n"], "mappings": ";;;;;AAWA,OAAM,MAAOA,sBAAsB;EAIjCC,YACUC,cAA8B,EAC9BC,KAAqB,EACrBC,MAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IANR,KAAAC,aAAa,GAAmB,EAAE;IAC1C,KAAAC,OAAO,GAAW,UAAU;EAMzB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,OAAO,GAAG,IAAI,CAACH,KAAK,CAACK,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC,IAAI,UAAU;IAEhE,IAAI,IAAI,CAACH,OAAO,KAAK,UAAU,EAAE;MAC/B;MACA,IAAI,CAACD,aAAa,CAACK,IAAI,CACrB,IAAI,CAACR,cAAc,CAACS,mBAAmB,CAACC,SAAS,CAAEC,cAAc,IAAI;QACnE;QACA,IAAIA,cAAc,EAAE;UAClB,IAAI,CAACA,cAAc,GAAGA,cAAc;UAEpC;UACA,IAAI,CAACR,aAAa,CAACS,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;UACtD,IAAI,CAACX,aAAa,GAAG,EAAE;UAEvB;UACA,IAAI,CAACA,aAAa,CAACK,IAAI,CACrB,IAAI,CAACR,cAAc,CAACe,sBAAsB,CACxCJ,cAAc,CACf,CAACD,SAAS,CAAC;YACVM,IAAI,EAAGC,OAAO,IAAI;cAChB;YAAA,CACD;YACDC,KAAK,EAAGC,GAAG,IACT,IAAI,CAACjB,MAAM,CAACgB,KAAK,CACf,eAAe,EACf,+BAA+B,EAC/BC,GAAG;WAER,CAAC,CACH;;MAEL,CAAC,CAAC,CACH;;IAEH;EACF;;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjB,aAAa,CAACS,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;;;uBAnDWhB,sBAAsB,EAAAuB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAvB,cAAA,GAAAqB,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAtB7B,sBAAsB;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXnCb,EAAA,CAAAe,cAAA,aAAgD;;UAM5Cf,EAAA,CAAAgB,SAAA,oBAA+B;UACjChB,EAAA,CAAAiB,YAAA,EAAM;;;UAHJjB,EAAA,CAAAkB,SAAA,GAAmD;UAAnDlB,EAAA,CAAAmB,WAAA,oBAAAnB,EAAA,CAAAoB,WAAA,OAAAN,GAAA,CAAAO,eAAA,EAAmD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}