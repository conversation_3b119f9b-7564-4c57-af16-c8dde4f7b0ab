const { AuthenticationError, ApolloError } = require('apollo-server-express');
const webrtcService = require('../services/webrtc.service');
const { pubsub } = require('./pubsub');
const logger = require('../utils/logger');

const webrtcResolvers = {
  Query: {
    // ✅ Obtenir l'état d'un appel
    getCallState: async (_, { callId }, context) => {
      logger.info(`[GraphQL] getCallState called for call: ${callId}`);

      if (!context.userId) {
        throw new AuthenticationError('Unauthorized');
      }

      try {
        const callState = webrtcService.getCallState(callId);
        if (!callState) {
          return null;
        }

        // Vérifier que l'utilisateur est participant à l'appel
        const isParticipant = callState.callerId === context.userId || callState.recipientId === context.userId;
        if (!isParticipant) {
          throw new AuthenticationError('Unauthorized to access this call');
        }

        return {
          id: callState.id,
          status: callState.status,
          type: callState.type,
          startTime: callState.startTime.toISOString()
        };

      } catch (error) {
        logger.error(`[GraphQL] Error getting call state: ${error.message}`);
        throw new ApolloError('Failed to get call state', 'CALL_STATE_ERROR');
      }
    }
  },

  Mutation: {
    // ✅ Initier un appel
    initiateCall: async (_, { recipientId, type = 'video' }, context) => {
      logger.info(`[GraphQL] initiateCall called: caller=${context.userId}, recipient=${recipientId}, type=${type}`);

      if (!context.userId) {
        throw new AuthenticationError('Unauthorized');
      }

      if (context.userId === recipientId) {
        throw new ApolloError('Cannot call yourself', 'INVALID_CALL');
      }

      try {
        const call = await webrtcService.initiateCall(context.userId, recipientId, type);
        
        logger.info(`[GraphQL] Call initiated successfully: ${call.id}`);
        return call;

      } catch (error) {
        logger.error(`[GraphQL] Error initiating call: ${error.message}`);
        throw new ApolloError(`Failed to initiate call: ${error.message}`, 'CALL_INITIATION_FAILED');
      }
    },

    // ✅ Accepter un appel
    acceptCall: async (_, { callId }, context) => {
      logger.info(`[GraphQL] acceptCall called: callId=${callId}, userId=${context.userId}`);

      if (!context.userId) {
        throw new AuthenticationError('Unauthorized');
      }

      try {
        const result = await webrtcService.acceptCall(callId, context.userId);
        
        logger.info(`[GraphQL] Call accepted successfully: ${callId}`);
        return result;

      } catch (error) {
        logger.error(`[GraphQL] Error accepting call: ${error.message}`);
        throw new ApolloError(`Failed to accept call: ${error.message}`, 'CALL_ACCEPT_FAILED');
      }
    },

    // ✅ Rejeter un appel
    rejectCall: async (_, { callId }, context) => {
      logger.info(`[GraphQL] rejectCall called: callId=${callId}, userId=${context.userId}`);

      if (!context.userId) {
        throw new AuthenticationError('Unauthorized');
      }

      try {
        const result = await webrtcService.rejectCall(callId, context.userId);
        
        logger.info(`[GraphQL] Call rejected successfully: ${callId}`);
        return result;

      } catch (error) {
        logger.error(`[GraphQL] Error rejecting call: ${error.message}`);
        throw new ApolloError(`Failed to reject call: ${error.message}`, 'CALL_REJECT_FAILED');
      }
    },

    // ✅ Terminer un appel
    endCall: async (_, { callId }, context) => {
      logger.info(`[GraphQL] endCall called: callId=${callId}, userId=${context.userId}`);

      if (!context.userId) {
        throw new AuthenticationError('Unauthorized');
      }

      try {
        const result = await webrtcService.endCall(callId, context.userId);
        
        logger.info(`[GraphQL] Call ended successfully: ${callId}`);
        return result;

      } catch (error) {
        logger.error(`[GraphQL] Error ending call: ${error.message}`);
        throw new ApolloError(`Failed to end call: ${error.message}`, 'CALL_END_FAILED');
      }
    },

    // ✅ Envoyer une offre SDP
    sendWebRTCOffer: async (_, { callId, offer }, context) => {
      logger.info(`[GraphQL] sendWebRTCOffer called: callId=${callId}, userId=${context.userId}`);

      if (!context.userId) {
        throw new AuthenticationError('Unauthorized');
      }

      try {
        const result = await webrtcService.handleOffer(callId, context.userId, offer);
        
        logger.info(`[GraphQL] WebRTC offer sent successfully: ${callId}`);
        return result;

      } catch (error) {
        logger.error(`[GraphQL] Error sending WebRTC offer: ${error.message}`);
        throw new ApolloError(`Failed to send offer: ${error.message}`, 'WEBRTC_OFFER_FAILED');
      }
    },

    // ✅ Envoyer une réponse SDP
    sendWebRTCAnswer: async (_, { callId, answer }, context) => {
      logger.info(`[GraphQL] sendWebRTCAnswer called: callId=${callId}, userId=${context.userId}`);

      if (!context.userId) {
        throw new AuthenticationError('Unauthorized');
      }

      try {
        const result = await webrtcService.handleAnswer(callId, context.userId, answer);
        
        logger.info(`[GraphQL] WebRTC answer sent successfully: ${callId}`);
        return result;

      } catch (error) {
        logger.error(`[GraphQL] Error sending WebRTC answer: ${error.message}`);
        throw new ApolloError(`Failed to send answer: ${error.message}`, 'WEBRTC_ANSWER_FAILED');
      }
    },

    // ✅ Envoyer un candidat ICE
    sendICECandidate: async (_, { callId, candidate }, context) => {
      logger.debug(`[GraphQL] sendICECandidate called: callId=${callId}, userId=${context.userId}`);

      if (!context.userId) {
        throw new AuthenticationError('Unauthorized');
      }

      try {
        const result = await webrtcService.handleIceCandidate(callId, context.userId, candidate);
        
        logger.debug(`[GraphQL] ICE candidate sent successfully: ${callId}`);
        return result;

      } catch (error) {
        logger.error(`[GraphQL] Error sending ICE candidate: ${error.message}`);
        throw new ApolloError(`Failed to send ICE candidate: ${error.message}`, 'ICE_CANDIDATE_FAILED');
      }
    }
  },

  Subscription: {
    // ✅ Appel entrant
    incomingCall: {
      subscribe: (_, __, context) => {
        if (!context.userId) {
          throw new AuthenticationError('Unauthorized');
        }

        logger.info(`[GraphQL] Setting up incomingCall subscription for user: ${context.userId}`);
        return pubsub.asyncIterator(['INCOMING_CALL']);
      },
      resolve: (payload, _, context) => {
        // Filtrer pour l'utilisateur connecté
        if (payload.userId === context.userId) {
          return payload.incomingCall;
        }
        return null;
      }
    },

    // ✅ Appel accepté
    callAccepted: {
      subscribe: (_, __, context) => {
        if (!context.userId) {
          throw new AuthenticationError('Unauthorized');
        }

        logger.info(`[GraphQL] Setting up callAccepted subscription for user: ${context.userId}`);
        return pubsub.asyncIterator(['CALL_ACCEPTED']);
      },
      resolve: (payload, _, context) => {
        if (payload.userId === context.userId) {
          return payload.callAccepted;
        }
        return null;
      }
    },

    // ✅ Appel rejeté
    callRejected: {
      subscribe: (_, __, context) => {
        if (!context.userId) {
          throw new AuthenticationError('Unauthorized');
        }

        logger.info(`[GraphQL] Setting up callRejected subscription for user: ${context.userId}`);
        return pubsub.asyncIterator(['CALL_REJECTED']);
      },
      resolve: (payload, _, context) => {
        if (payload.userId === context.userId) {
          return payload.callRejected;
        }
        return null;
      }
    },

    // ✅ Appel terminé
    callEnded: {
      subscribe: (_, __, context) => {
        if (!context.userId) {
          throw new AuthenticationError('Unauthorized');
        }

        logger.info(`[GraphQL] Setting up callEnded subscription for user: ${context.userId}`);
        return pubsub.asyncIterator(['CALL_ENDED']);
      },
      resolve: (payload, _, context) => {
        if (payload.userId === context.userId) {
          return payload.callEnded;
        }
        return null;
      }
    },

    // ✅ Appel connecté
    callConnected: {
      subscribe: (_, __, context) => {
        if (!context.userId) {
          throw new AuthenticationError('Unauthorized');
        }

        logger.info(`[GraphQL] Setting up callConnected subscription for user: ${context.userId}`);
        return pubsub.asyncIterator(['CALL_CONNECTED']);
      },
      resolve: (payload, _, context) => {
        if (payload.userId === context.userId) {
          return payload.callConnected;
        }
        return null;
      }
    },

    // ✅ Offre WebRTC
    webrtcOffer: {
      subscribe: (_, __, context) => {
        if (!context.userId) {
          throw new AuthenticationError('Unauthorized');
        }

        logger.info(`[GraphQL] Setting up webrtcOffer subscription for user: ${context.userId}`);
        return pubsub.asyncIterator(['WEBRTC_OFFER']);
      },
      resolve: (payload, _, context) => {
        if (payload.userId === context.userId) {
          return payload.webrtcOffer;
        }
        return null;
      }
    },

    // ✅ Réponse WebRTC
    webrtcAnswer: {
      subscribe: (_, __, context) => {
        if (!context.userId) {
          throw new AuthenticationError('Unauthorized');
        }

        logger.info(`[GraphQL] Setting up webrtcAnswer subscription for user: ${context.userId}`);
        return pubsub.asyncIterator(['WEBRTC_ANSWER']);
      },
      resolve: (payload, _, context) => {
        if (payload.userId === context.userId) {
          return payload.webrtcAnswer;
        }
        return null;
      }
    },

    // ✅ Candidat ICE
    webrtcIceCandidate: {
      subscribe: (_, __, context) => {
        if (!context.userId) {
          throw new AuthenticationError('Unauthorized');
        }

        logger.debug(`[GraphQL] Setting up webrtcIceCandidate subscription for user: ${context.userId}`);
        return pubsub.asyncIterator(['WEBRTC_ICE_CANDIDATE']);
      },
      resolve: (payload, _, context) => {
        if (payload.userId === context.userId) {
          return payload.webrtcIceCandidate;
        }
        return null;
      }
    }
  }
};

module.exports = webrtcResolvers;
