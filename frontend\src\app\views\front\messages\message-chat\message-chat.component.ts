import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  On<PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  AfterViewChecked,
  ChangeDetectorRef,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthuserService } from 'src/app/services/authuser.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { User } from '@app/models/user.model';
import { UserStatusService } from 'src/app/services/user-status.service';
import {
  Message,
  Conversation,
  MessageType,
  CallType,
} from 'src/app/models/message.model';
import { ToastService } from 'src/app/services/toast.service';
import { MessageService } from '@app/services/message.service';

@Component({
  selector: 'app-message-chat',
  templateUrl: 'message-chat.component.html',
  styleUrls: ['./message-chat.component.css'],
})
export class MessageChatComponent
  implements OnInit, On<PERSON><PERSON>roy, AfterViewChecked
{
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  @ViewChild('fileInput', { static: false })
  fileInput!: ElementRef<HTMLInputElement>;

  // === PROPRIÉTÉS PRINCIPALES ===
  messages: Message[] = [];
  messageForm: FormGroup;
  conversation: Conversation | null = null;
  loading = true;
  error: any;
  currentUserId: string | null = null;
  currentUsername: string = 'You';
  otherParticipant: User | null = null;
  selectedFile: File | null = null;
  previewUrl: string | ArrayBuffer | null = null;
  isUploading = false;
  isTyping = false;
  typingTimeout: any;
  isRecordingVoice = false;
  voiceRecordingDuration = 0;

  // === PAGINATION ET CHARGEMENT ===
  private readonly MAX_MESSAGES_PER_SIDE = 5;
  private readonly MAX_MESSAGES_TO_LOAD = 10;
  private readonly MAX_TOTAL_MESSAGES = 100;
  private currentPage = 1;
  isLoadingMore = false;
  hasMoreMessages = true;
  private subscriptions = new Subscription();

  // === INTERFACE ET THÈMES ===
  selectedTheme: string = 'theme-default';
  showThemeSelector = false;
  showMainMenu = false;
  showEmojiPicker = false;
  showSearchBar = false;
  showPinnedMessages = false;
  showStatusSelector = false;
  showNotificationPanel = false;
  showNotificationSettings = false;
  showUserStatusPanel = false;
  showCallHistoryPanel = false;
  showCallStatsPanel = false;
  showVoiceMessagesPanel = false;

  // === APPELS ===
  incomingCall: any = null;
  activeCall: any = null;
  showCallModal = false;
  showActiveCallModal = false;
  isCallMuted = false;
  isVideoEnabled = true;
  callDuration = 0;
  callTimer: any = null;

  // === NOTIFICATIONS ET MESSAGES ===
  notifications: any[] = [];
  unreadNotificationCount: number = 0;
  selectedNotifications: Set<string> = new Set();
  showDeleteConfirmModal: boolean = false;
  editingMessageId: string | null = null;
  editingContent = '';
  replyingToMessage: any = null;

  // === RECHERCHE ===
  searchQuery = '';
  isSearching = false;
  searchMode = false;
  searchResults: any[] = [];
  showSearch = false;
  showAdvancedSearch = false;

  // === PANNEAUX ===
  pinnedMessages: any[] = [];
  showReactionPicker: any = {};
  showDeleteConfirm: any = {};
  showPinConfirm: any = {};
  isPinning: any = {};
  showMessageOptions: any = {};

  // === TRANSFERT ===
  showForwardModal = false;
  forwardingMessage: any = null;
  selectedConversations: string[] = [];
  availableConversations: any[] = [];
  isForwarding = false;
  isLoadingConversations = false;

  // === ÉMOJIS ET AUTOCOLLANTS ===
  emojiCategories: any[] = [
    { id: 'recent', name: 'Récents', icon: 'fas fa-clock' },
    { id: 'smileys', name: 'Smileys', icon: 'fas fa-smile' },
    { id: 'people', name: 'Personnes', icon: 'fas fa-user' },
    { id: 'animals', name: 'Animaux', icon: 'fas fa-paw' },
    { id: 'food', name: 'Nourriture', icon: 'fas fa-apple-alt' },
    { id: 'travel', name: 'Voyage', icon: 'fas fa-plane' },
    { id: 'activities', name: 'Activités', icon: 'fas fa-football-ball' },
    { id: 'objects', name: 'Objets', icon: 'fas fa-lightbulb' },
    { id: 'symbols', name: 'Symboles', icon: 'fas fa-heart' },
    { id: 'flags', name: 'Drapeaux', icon: 'fas fa-flag' },
  ];
  selectedEmojiCategory = 'recent';
  emojiSearchQuery = '';
  recentEmojis: any[] = [];
  previewedEmoji: any = null;
  showStickerPicker = false;
  stickerPacks: any[] = [];
  selectedStickerPack = '';

  // === GIFS ===
  showGifPicker = false;
  gifSearchQuery = '';
  gifCategories = ['Trending', 'Reactions', 'Animals', 'Sports', 'TV & Movies'];
  selectedGifCategory = 'Trending';

  // === OUTIL DE DESSIN ===
  showDrawingTool = false;
  selectedDrawingTool = 'pen';
  drawingColors = [
    '#000000',
    '#FF0000',
    '#00FF00',
    '#0000FF',
    '#FFFF00',
    '#FF00FF',
    '#00FFFF',
    '#FFFFFF',
  ];
  selectedDrawingColor = '#000000';
  customDrawingColor = '#000000';
  drawingSize = 5;

  // === CAMÉRA ===
  showCamera = false;
  cameraMode = 'photo';
  flashEnabled = false;
  showCameraGrid = false;
  showFocusIndicator = false;
  focusX = 0;
  focusY = 0;
  lastCapturedImage = '';
  isRecordingVideo = false;
  videoRecordingDuration = 0;

  // === ÉDITEUR D'IMAGES ===
  showImageEditor = false;
  imageEditorMode = 'crop';
  cropArea = { x: 0, y: 0, width: 100, height: 100 };
  cropRatio = 'free';
  imageFilters: any[] = [
    { name: 'none', label: 'Aucun', css: 'none' },
    { name: 'grayscale', label: 'Noir & Blanc', css: 'grayscale(100%)' },
    { name: 'sepia', label: 'Sépia', css: 'sepia(100%)' },
    { name: 'vintage', label: 'Vintage', css: 'sepia(50%) contrast(1.2)' },
    { name: 'bright', label: 'Lumineux', css: 'brightness(1.3)' },
    { name: 'contrast', label: 'Contraste', css: 'contrast(1.5)' },
  ];
  selectedImageFilter = 'none';
  imageAdjustments = { brightness: 0, contrast: 0, saturation: 0, hue: 0 };
  imageTextElements: any[] = [];
  newTextContent = '';
  textFontFamily = 'Arial';
  textFontSize = 24;
  textColor = '#000000';
  availableFonts = [
    { value: 'Arial', label: 'Arial' },
    { value: 'Helvetica', label: 'Helvetica' },
    { value: 'Times New Roman', label: 'Times New Roman' },
    { value: 'Courier New', label: 'Courier New' },
  ];

  // === GESTIONNAIRE DE FICHIERS ===
  showFileManager = false;
  fileViewMode = 'grid';
  fileFolders: any[] = [];
  selectedFolder: any = null;
  fileBreadcrumbs: any[] = [];
  fileSearchQuery = '';
  fileTypeFilter = '';
  fileSortBy = 'name';
  selectedFiles: string[] = [];

  // === ANALYTICS ===
  showAnalyticsDashboard = false;
  analyticsTimeRange = '7d';
  analyticsData: any = {
    totalMessages: 0,
    activeUsers: 0,
    avgResponseTime: '0s',
    filesShared: 0,
    messagesChange: 0,
    usersChange: 0,
    responseTimeChange: 0,
    filesChange: 0,
    topUsers: [],
    messageTypes: [],
    longestConversation: 0,
    avgConversationDuration: '0m',
    topEmojis: [],
  };

  // === INTÉGRATIONS ===
  showIntegrations = false;
  integrationCategories = [
    'CRM',
    'Productivité',
    'Communication',
    'Analytics',
    'Sécurité',
  ];
  selectedIntegrationCategory = 'CRM';
  webhooks: any[] = [];

  // === ZONE DE SAISIE AVANCÉE ===
  showQuickReplies = false;
  quickReplies: string[] = ['Merci !', "D'accord", 'Parfait', 'À bientôt'];
  showMentionSuggestions = false;
  mentionSuggestions: any[] = [];
  selectedMentionIndex = 0;
  activeMentions: any[] = [];
  showHashtagSuggestions = false;
  hashtagSuggestions: any[] = [];
  selectedHashtagIndex = 0;
  activeHashtags: any[] = [];
  showCommandSuggestions = false;
  commandSuggestions: any[] = [];
  selectedCommandIndex = 0;
  activeLinks: any[] = [];
  inputHeight = 40;
  showFormattingToolbar = false;
  detectedLanguage = 'fr';
  autoCorrections: any[] = [];
  showAutoCorrectSuggestions = false;
  autoCorrectSuggestions: any[] = [];

  // === TRADUCTION ===
  showTranslationPanel = false;
  translationFrom = 'auto';
  translationTo = 'fr';
  originalTextForTranslation = '';
  translatedText = '';
  isTranslating = false;
  supportedLanguages = [
    { code: 'fr', name: 'Français' },
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'de', name: 'Deutsch' },
    { code: 'it', name: 'Italiano' },
  ];

  // === SONDAGES ===
  showPollCreator = false;
  pollQuestion = '';
  pollOptions: any[] = [{ text: '' }, { text: '' }];
  pollSettings = {
    allowMultiple: false,
    anonymous: false,
    showResults: true,
    showVoters: true,
  };
  pollExpiry = '';
  customPollExpiry = '';

  // === LOCALISATION ===
  showLocationPicker = false;
  showLocationSearch = false;
  locationSearchQuery = '';
  locationSearchResults: any[] = [];
  selectedLocation: any = null;
  showLocationMessage = false;
  locationMessage = '';

  // === CONTACTS ===
  showContactPicker = false;
  contactSearchQuery = '';
  selectedContactForSharing: any = null;

  // === PROGRAMMATION D'ENVOI ===
  showScheduleMessage = false;
  customScheduleDate = '';
  customScheduleTime = '';
  scheduleTimezone = 'Europe/Paris';
  availableTimezones = [
    { value: 'Europe/Paris', label: 'Paris (CET)' },
    { value: 'America/New_York', label: 'New York (EST)' },
    { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
  ];

  // === ÉTATS DES MESSAGES ===
  highlightedMessageId = '';
  searchResultIds: string[] = [];
  selectionMode = false;
  hoveredMessageId = '';
  playingVoiceId = '';
  showScrollToBottom = false;
  unreadMessagesCount = 0;

  // === VISUALISEUR D'IMAGES ===
  showImageViewer = false;
  currentImageIndex = 0;
  imageGallery: any[] = [];

  // === TOAST ===
  showToast = false;
  toastMessage = '';
  toastType = 'info';

  // === PANNEAUX D'INFORMATIONS ===
  showConversationInfo = false;
  showConversationSettings = false;
  conversationSettings = {
    notifications: true,
    soundNotifications: true,
    readReceipts: true,
    typingIndicators: true,
  };

  // === VARIABLES D'ÉTAT POUR LES NOUVELLES FONCTIONNALITÉS ===
  isSendingMessage = false;
  isUserTyping = false;
  voiceRecordingState = 'idle';
  voiceRecordingSize = 0;
  recordingWaveform: number[] = [];
  voiceRecordingQuality = 'medium';
  voiceEffects: string[] = [];
  selectedContacts: string[] = [];
  isUpdatingStatus = false;

  // === VARIABLES POUR LES PIÈCES JOINTES ===
  showAttachmentMenu = false;
  attachmentFiles: File[] = [];
  fileCaptions: string[] = [];
  fileUploadProgress: number[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authUserService: AuthuserService,
    private MessageService: MessageService,
    private toastService: ToastService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private userStatusService: UserStatusService
  ) {
    this.messageForm = this.fb.group({
      content: ['', [Validators.required, Validators.minLength(1)]],
    });
  }

  // === CONSTANTES OPTIMISÉES ===
  readonly c = {
    reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],
    delays: { away: 300000, typing: 3000, scroll: 100, anim: 300 },
    error: 'Erreur. Veuillez réessayer.',
    trackById: (i: number, item: any): string => item?.id || i.toString(),
    notifications: {
      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },
      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },
      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },
    },
    status: {
      online: {
        text: 'En ligne',
        color: 'text-green-500',
        icon: 'fas fa-circle',
      },
      offline: {
        text: 'Hors ligne',
        color: 'text-gray-500',
        icon: 'far fa-circle',
      },
      away: { text: 'Absent', color: 'text-yellow-500', icon: 'fas fa-clock' },
      busy: {
        text: 'Occupé',
        color: 'text-red-500',
        icon: 'fas fa-minus-circle',
      },
    },
    themes: [
      { key: 'theme-default', label: 'Défaut', color: '#4f5fad' },
      { key: 'theme-feminine', label: 'Rose', color: '#ff6b9d' },
      { key: 'theme-masculine', label: 'Bleu', color: '#3d85c6' },
      { key: 'theme-neutral', label: 'Vert', color: '#6aa84f' },
    ],
    calls: {
      COMPLETED: 'text-green-500',
      MISSED: 'text-red-500',
      REJECTED: 'text-orange-500',
    },
  };

  // === GETTERS ===
  get availableReactions(): string[] {
    return this.c.reactions;
  }
  get commonEmojis(): string[] {
    return this.MessageService.getCommonEmojis();
  }
  get unreadNotificationsCount(): number {
    return this.unreadNotificationCount;
  }
  get currentUserStatus(): string {
    return 'online';
  }
  get onlineUsersCount(): number {
    return 5;
  }

  // === LIFECYCLE HOOKS ===
  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    if (this.callTimer) clearInterval(this.callTimer);
    if (this.typingTimeout) clearTimeout(this.typingTimeout);
  }

  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  // === INITIALISATION ===
  private initializeComponent(): void {
    this.loadCurrentUser();
    this.loadConversation();
    this.setupSubscriptions();
    // Initialiser des données de test pour la démo
    this.initializeTestData();
  }

  private loadConversation(): void {
    this.route.params.subscribe((params) => {
      const conversationId = params['id'];
      if (conversationId) {
        this.getConversation(conversationId);
        this.getMessages();
      }
    });
  }

  private setupSubscriptions(): void {
    // Configuration des abonnements WebSocket et autres
  }

  // === GESTION DES CONVERSATIONS ===
  getConversation(conversationId: string): void {
    this.loading = true;
    this.MessageService.getConversation(conversationId).subscribe({
      next: (conversation) => {
        this.conversation = conversation;
        this.otherParticipant =
          conversation.participants?.find((p) => p.id !== this.currentUserId) ||
          null;
        this.loading = false;
      },
      error: (error) => {
        this.error = error;
        this.loading = false;
        console.error('Erreur lors du chargement de la conversation:', error);
      },
    });
  }

  // === GESTION DES MESSAGES ===
  getMessages(): void {
    if (!this.conversation?.id) return;

    this.MessageService.getMessages(
      this.conversation.id,
      this.currentPage.toString(),
      this.MAX_MESSAGES_TO_LOAD.toString()
    ).subscribe({
      next: (messages) => {
        if (this.currentPage === 1) {
          this.messages = messages;
        } else {
          this.messages = [...messages, ...this.messages];
        }
        this.hasMoreMessages = messages.length === this.MAX_MESSAGES_TO_LOAD;
        this.scrollToBottom();
      },
      error: (error) => {
        console.error('Erreur lors du chargement des messages:', error);
        this.toastService.showError('Erreur lors du chargement des messages');
      },
    });
  }

  sendMessage(): void {
    const content = this.messageForm.get('content')?.value?.trim();
    if (!content || !this.conversation?.id) return;

    this.isSendingMessage = true;

    // Créer un message temporaire pour l'affichage immédiat
    const tempMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: { id: this.currentUserId!, username: this.currentUsername },
      timestamp: new Date(),
      conversationId: this.conversation.id,
      type: 'text' as MessageType,
    };

    this.messages.push(tempMessage);
    this.messageForm.reset();
    this.scrollToBottom();
    this.isSendingMessage = false;

    // Simuler l'envoi réel (à remplacer par l'appel API réel)
    /*
    this.MessageService.sendMessage(
      this.conversation.id,
      content,
      'text' as MessageType,
      this.currentUserId || ''
    ).subscribe({
      next: (message) => {
        // Remplacer le message temporaire par le vrai message
        const index = this.messages.findIndex(m => m.id === tempMessage.id);
        if (index > -1) {
          this.messages[index] = message;
        }
      },
      error: (error) => {
        console.error("Erreur lors de l'envoi du message:", error);
        this.toastService.showError("Erreur lors de l'envoi du message");
        // Supprimer le message temporaire en cas d'erreur
        this.messages = this.messages.filter(m => m.id !== tempMessage.id);
      },
    });
    */
  }

  // === GESTION DES FICHIERS ===
  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.attachmentFiles = Array.from(files);
      this.generatePreviews();
    }
  }

  removeAttachment(index: number): void {
    this.attachmentFiles.splice(index, 1);
    if (this.attachmentFiles.length === 0) {
      this.previewUrl = null;
    }
  }

  // === GESTION DES RÉACTIONS ===
  toggleReaction(messageId: string, emoji: string): void {
    // Simulation de la réaction - à implémenter avec le vrai service
    const message = this.messages.find((m) => m.id === messageId);
    if (message) {
      if (!(message as any).reactions) (message as any).reactions = [];
      const existingReaction = (message as any).reactions.find(
        (r: any) => r.emoji === emoji
      );
      if (existingReaction) {
        existingReaction.count = (existingReaction.count || 0) + 1;
      } else {
        (message as any).reactions.push({
          emoji,
          count: 1,
          users: [this.currentUserId],
        });
      }
    }
  }

  addReaction(messageId: string, emoji: string): void {
    this.toggleReaction(messageId, emoji);
    this.showReactionPicker = {};
  }

  // === GESTION DE L'ÉDITION ===
  startEditMessage(message: any): void {
    this.editingMessageId = message.id;
    this.editingContent = message.content || '';
    this.showMessageOptions = {};
  }

  cancelEdit(): void {
    this.editingMessageId = null;
    this.editingContent = '';
  }

  saveEdit(messageId: string): void {
    if (this.editingContent?.trim()) {
      this.MessageService.editMessage(
        messageId,
        this.editingContent.trim()
      ).subscribe({
        next: () => {
          this.cancelEdit();
          this.getMessages();
        },
        error: (error) => {
          console.error('Erreur lors de la modification:', error);
        },
      });
    }
  }

  onEditKeyDown(event: KeyboardEvent, messageId: string): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.saveEdit(messageId);
    } else if (event.key === 'Escape') {
      this.cancelEdit();
    }
  }

  // === GESTION DE LA SUPPRESSION ===
  deleteMessage(messageId: string): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {
      this.MessageService.deleteMessage(messageId).subscribe({
        next: () => {
          this.getMessages();
          this.showMessageOptions = {};
        },
        error: (error) => {
          console.error('Erreur lors de la suppression:', error);
        },
      });
    }
  }

  // === GESTION DES APPELS ===
  startCall(type: CallType): void {
    if (!this.otherParticipant?.id) return;

    this.MessageService.initiateCall(this.otherParticipant.id, type).subscribe({
      next: (call) => {
        this.activeCall = call;
        this.showActiveCallModal = true;
        this.startCallTimer();
      },
      error: (error) => {
        console.error("Erreur lors de l'initiation de l'appel:", error);
        this.toastService.showError("Impossible d'initier l'appel");
      },
    });
  }

  private startCallTimer(): void {
    this.callDuration = 0;
    this.callTimer = setInterval(() => {
      this.callDuration++;
    }, 1000);
  }

  endCall(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
    this.activeCall = null;
    this.showActiveCallModal = false;
    this.callDuration = 0;
  }

  toggleMute(): void {
    this.isCallMuted = !this.isCallMuted;
  }

  toggleVideo(): void {
    this.isVideoEnabled = !this.isVideoEnabled;
  }

  // === MÉTHODES UTILITAIRES ===
  scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  formatMessageTime(timestamp: any): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  trackByMessageId(index: number, message: any): string {
    return message?.id || index.toString();
  }

  // === GESTION DE L'INTERFACE ===
  toggleSearch(): void {
    this.showSearch = !this.showSearch;
    if (!this.showSearch) {
      this.clearSearch();
    }
  }

  toggleNotifications(): void {
    this.showNotificationPanel = !this.showNotificationPanel;
  }

  toggleConversationInfo(): void {
    this.showConversationInfo = !this.showConversationInfo;
  }

  toggleThemeSelector(): void {
    this.showThemeSelector = !this.showThemeSelector;
  }

  toggleMainMenu(): void {
    this.showMainMenu = !this.showMainMenu;
  }

  toggleAdvancedSearch(): void {
    this.showAdvancedSearch = !this.showAdvancedSearch;
  }

  toggleStatusSelector(): void {
    this.showStatusSelector = !this.showStatusSelector;
  }

  toggleUserStatusPanel(): void {
    this.showUserStatusPanel = !this.showUserStatusPanel;
  }

  // === GESTION DE LA RECHERCHE ===
  onSearchInput(): void {
    if (this.searchQuery.trim()) {
      this.searchMode = true;
      this.performSearch();
    } else {
      this.searchMode = false;
      this.searchResults = [];
    }
  }

  private performSearch(): void {
    this.isSearching = true;
    // Simulation de recherche
    setTimeout(() => {
      this.searchResults = this.messages.filter((message) =>
        message.content?.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
      this.isSearching = false;
    }, 500);
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.searchMode = false;
    this.searchResults = [];
    this.searchResultIds = [];
  }

  highlightSearchTerm(content: string): string {
    if (!this.searchQuery || !content) return content;
    const regex = new RegExp(`(${this.searchQuery})`, 'gi');
    return content.replace(
      regex,
      '<mark class="whatsapp-search-highlight">$1</mark>'
    );
  }

  scrollToMessage(messageId: string): void {
    this.highlightedMessageId = messageId;
    // Logique pour faire défiler vers le message
    setTimeout(() => {
      this.highlightedMessageId = '';
    }, 3000);
  }

  // === GESTION DES MESSAGES ÉPINGLÉS ===
  showAllPinnedMessages(): void {
    // Afficher tous les messages épinglés
  }

  closePinnedMessages(): void {
    this.pinnedMessages = [];
  }

  togglePinMessage(messageId: string): void {
    const message = this.messages.find((m) => m.id === messageId);
    if (message) {
      message.pinned = !message.pinned;
      if (message.pinned) {
        this.pinnedMessages.push(message);
      } else {
        this.pinnedMessages = this.pinnedMessages.filter(
          (m) => m.id !== messageId
        );
      }
    }
    this.showMessageOptions = {};
  }

  // === GESTION DES OPTIONS DE MESSAGE ===
  toggleMessageOptions(messageId: string): void {
    if (this.showMessageOptions[messageId]) {
      this.showMessageOptions = {};
    } else {
      this.showMessageOptions = { [messageId]: true };
    }
  }

  toggleReactionPicker(messageId: string): void {
    if (this.showReactionPicker[messageId]) {
      this.showReactionPicker = {};
    } else {
      this.showReactionPicker = { [messageId]: true };
    }
  }

  // === GESTION DES ACTIONS DE MESSAGE ===
  replyToMessage(message: any): void {
    this.replyingToMessage = message;
    this.showMessageOptions = {};
  }

  forwardMessage(message: any): void {
    this.forwardingMessage = message;
    this.showForwardModal = true;
    this.showMessageOptions = {};
  }

  copyMessage(message: any): void {
    if (navigator.clipboard && message.content) {
      navigator.clipboard.writeText(message.content);
      this.toastService.showSuccess('Message copié');
    }
    this.showMessageOptions = {};
  }

  selectMessage(messageId: string): void {
    this.selectionMode = true;
    this.toggleMessageSelection(messageId);
    this.showMessageOptions = {};
  }

  toggleMessageSelection(messageId: string): void {
    const index = this.selectedMessages.indexOf(messageId);
    if (index > -1) {
      this.selectedMessages.splice(index, 1);
    } else {
      this.selectedMessages.push(messageId);
    }

    if (this.selectedMessages.length === 0) {
      this.selectionMode = false;
    }
  }

  get selectedMessages(): string[] {
    return this._selectedMessages;
  }

  set selectedMessages(value: string[]) {
    this._selectedMessages = value;
  }

  private _selectedMessages: string[] = [];

  // === GESTION DES ÉMOJIS ===
  openEmojiPicker(messageId?: string): void {
    this.showEmojiPicker = true;
    if (messageId) {
      this.showReactionPicker = { [messageId]: true };
    }
  }

  closeEmojiPicker(): void {
    this.showEmojiPicker = false;
    this.showReactionPicker = {};
  }

  selectEmojiCategory(categoryId: string): void {
    this.selectedEmojiCategory = categoryId;
  }

  addEmojiToMessage(emoji: string): void {
    const currentContent = this.messageForm.get('content')?.value || '';
    this.messageForm.get('content')?.setValue(currentContent + emoji);
  }

  // === GESTION DES PIÈCES JOINTES ===
  toggleAttachmentMenu(): void {
    this.showAttachmentMenu = !this.showAttachmentMenu;
  }

  openFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  // === GESTION DES MÉDIAS ===
  openImageViewer(message: any): void {
    this.showImageViewer = true;
    this.currentImageIndex = 0;
    this.imageGallery = [message];
  }

  closeImageViewer(): void {
    this.showImageViewer = false;
    this.imageGallery = [];
  }

  // === GESTION DES MESSAGES VOCAUX ===
  toggleVoicePlayback(messageId: string): void {
    if (this.playingVoiceId === messageId) {
      this.playingVoiceId = '';
    } else {
      this.playingVoiceId = messageId;
    }
  }

  startVoiceRecording(): void {
    this.isRecordingVoice = true;
    this.voiceRecordingDuration = 0;
    // Logique d'enregistrement vocal
  }

  stopVoiceRecording(): void {
    this.isRecordingVoice = false;
    this.voiceRecordingDuration = 0;
  }

  // === GESTION DES THÈMES ===
  changeTheme(theme: string): void {
    this.selectedTheme = theme;
    this.showThemeSelector = false;
  }

  getThemeOptions(): any[] {
    return this.c.themes;
  }

  // === GESTION DU STATUT ===
  updateUserStatus(status: string): void {
    this.isUpdatingStatus = true;
    // Simuler une mise à jour
    setTimeout(() => {
      this.isUpdatingStatus = false;
    }, 1000);
  }

  getStatusOptions(): any[] {
    return Object.entries(this.c.status).map(([key, value]) => ({
      key,
      ...value,
    }));
  }

  getStatusIcon(status: string): string {
    return (
      this.c.status[status as keyof typeof this.c.status]?.icon ||
      'fas fa-circle'
    );
  }

  getStatusColor(status: string): string {
    return (
      this.c.status[status as keyof typeof this.c.status]?.color ||
      'text-gray-500'
    );
  }

  getStatusText(status: string): string {
    return (
      this.c.status[status as keyof typeof this.c.status]?.text || 'Inconnu'
    );
  }

  // === MÉTHODES POUR LE TEMPLATE ===
  formatMessageContent(content: string | undefined): string {
    if (!content) return '';
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>');
  }

  onImageLoad(event: any): void {
    // Gérer le chargement d'image
  }

  onImageError(event: any): void {
    event.target.src = 'assets/images/image-error.png';
  }

  onVideoLoaded(event: any): void {
    // Gérer le chargement vidéo
  }

  // === MÉTHODES POUR LES ANALYTICS ===
  openAnalyticsDashboard(): void {
    this.showAnalyticsDashboard = true;
  }

  closeAnalyticsDashboard(): void {
    this.showAnalyticsDashboard = false;
  }

  // === MÉTHODES POUR LES INTÉGRATIONS ===
  openIntegrations(): void {
    this.showIntegrations = true;
  }

  closeIntegrations(): void {
    this.showIntegrations = false;
  }

  // === MÉTHODES POUR LES RÉPONSES RAPIDES ===
  sendQuickMessage(content: string): void {
    const control = this.messageForm.get('content');
    if (control) {
      control.setValue(content);
      this.sendMessage();
    }
  }

  // === MÉTHODES POUR LA COMPATIBILITÉ ===
  initiateCall(type: CallType): void {
    this.startCall(type);
  }

  // === CORRECTION DES MÉTHODES MANQUANTES ===
  private generatePreviews(): void {
    this.attachmentFiles.forEach((file) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.previewUrl = e.target?.result || null;
        };
        reader.readAsDataURL(file);
      }
    });
  }

  private loadCurrentUser(): void {
    // Utiliser getCurrentUser au lieu de getUser
    this.authUserService.getCurrentUser().subscribe({
      next: (user: any) => {
        this.currentUserId = user?.id || null;
        this.currentUsername = user?.username || 'You';
      },
      error: (error: any) =>
        console.error("Erreur lors du chargement de l'utilisateur:", error),
    });
  }

  // === MÉTHODES SUPPLÉMENTAIRES POUR LE TEMPLATE ===

  // Méthodes pour les types de messages
  getMessageType(message: any): string {
    if (message.content && !message.attachments?.length) return 'text';
    if (message.attachments?.some((att: any) => att.type === 'image'))
      return 'image';
    if (message.attachments?.some((att: any) => att.type === 'voice'))
      return 'voice';
    if (message.attachments?.some((att: any) => att.type === 'file'))
      return 'file';
    if (message.attachments?.some((att: any) => att.type === 'video'))
      return 'video';
    if (message.type === 'location') return 'location';
    if (message.type === 'contact') return 'contact';
    return 'text';
  }

  // Méthodes pour les médias
  getImageUrl(message: any): string {
    return (
      message.imageUrl ||
      message.attachments?.find((att: any) => att.type === 'image')?.url ||
      ''
    );
  }

  getVideoUrl(message: any): string {
    return (
      message.videoUrl ||
      message.attachments?.find((att: any) => att.type === 'video')?.url ||
      ''
    );
  }

  getVideoThumbnail(message: any): string {
    return message.thumbnailUrl || '';
  }

  getVideoDuration(message: any): string {
    return message.duration || '00:00';
  }

  // Méthodes pour les fichiers
  getFileSize(message: any): string {
    return this.formatFileSize(message.size || 0);
  }

  getFileIcon(message: any): string {
    const fileType = message.fileType || message.attachments?.[0]?.type || '';
    const icons: any = {
      'application/pdf': 'fas fa-file-pdf',
      'application/msword': 'fas fa-file-word',
      'application/vnd.ms-excel': 'fas fa-file-excel',
      'application/vnd.ms-powerpoint': 'fas fa-file-powerpoint',
      'text/': 'fas fa-file-alt',
      'image/': 'fas fa-file-image',
      'video/': 'fas fa-file-video',
      'audio/': 'fas fa-file-audio',
    };

    for (const [type, icon] of Object.entries(icons)) {
      if (fileType.startsWith(type)) return icon as string;
    }
    return 'fas fa-file';
  }

  getFileType(message: any): string {
    const fileType = message.fileType || message.attachments?.[0]?.type || '';
    return fileType.split('/')[1]?.toUpperCase() || 'FICHIER';
  }

  // Méthodes pour les messages vocaux
  formatVoiceDuration(current: number, total: number): string {
    const formatTime = (seconds: number) => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    };
    return `${formatTime(current)} / ${formatTime(total)}`;
  }

  getVoiceWaveform(message: any): any[] {
    return Array.from({ length: 20 }, (_, i) => ({
      height: Math.random() * 20 + 5,
      active: false,
      played: false,
    }));
  }

  getVoiceProgress(messageId: string): number {
    return 0;
  }

  getVoiceCurrentTime(messageId: string): number {
    return 0;
  }

  getVoiceTotalDuration(messageId: string): number {
    return 0;
  }

  getVoiceSpeed(messageId: string): string {
    return '1x';
  }

  // Méthodes pour les états
  isImageLoading(message: any): boolean {
    return false;
  }

  isVoiceLoading(messageId: string): boolean {
    return false;
  }

  isFileDownloading(message: any): boolean {
    return false;
  }

  getImageLoadingProgress(message: any): number {
    return 0;
  }

  getFileDownloadProgress(message: any): number {
    return 0;
  }

  getImageDimensions(message: any): string {
    return '';
  }

  // Méthodes pour les actions
  downloadFile(message: any): void {
    // Télécharger le fichier
  }

  previewFile(message: any): void {
    // Prévisualiser le fichier
  }

  canPreviewFile(message: any): boolean {
    const previewableTypes = [
      'image/',
      'video/',
      'audio/',
      'text/',
      'application/pdf',
    ];
    return previewableTypes.some((type) => message.type?.startsWith(type));
  }

  hasVideo(message: any): boolean {
    return (
      message.type === 'video' ||
      (message.attachments &&
        message.attachments.some((att: any) => att.type === 'video'))
    );
  }

  // Méthodes pour les réactions
  hasUserReacted(reaction: any, userId: string): boolean {
    return reaction.users && reaction.users.includes(userId);
  }

  getReactionTooltip(reaction: any): string {
    return `${reaction.emoji} ${reaction.count}`;
  }

  getEmojiName(emoji: string): string {
    const emojiNames: any = {
      '👍': 'Pouce levé',
      '❤️': 'Cœur',
      '😂': 'Rire',
      '😮': 'Surprise',
      '😢': 'Triste',
      '😡': 'Colère',
    };
    return emojiNames[emoji] || emoji;
  }

  // Méthodes pour la localisation
  openLocationViewer(message: any): void {
    // Ouvrir le visualiseur de localisation
  }

  getLocationMapUrl(message: any): string {
    return message.mapUrl || '';
  }

  getLocationName(message: any): string {
    return message.locationName || 'Localisation';
  }

  getLocationAddress(message: any): string {
    return message.address || '';
  }

  // Méthodes pour les contacts
  openContactViewer(message: any): void {
    // Ouvrir le visualiseur de contact
  }

  getContactAvatar(message: any): string {
    return message.contactAvatar || 'assets/images/default-avatar.png';
  }

  getContactName(message: any): string {
    return message.contactName || 'Contact';
  }

  getContactPhone(message: any): string {
    return message.contactPhone || '';
  }

  // Méthodes pour les lecteurs
  openVideoPlayer(message: any): void {
    // Ouvrir le lecteur vidéo
  }

  seekVoiceMessage(messageId: string, event: any): void {
    // Implémentation de la recherche dans le message vocal
  }

  changeVoiceSpeed(messageId: string): void {
    // Changer la vitesse de lecture
  }

  // Méthodes pour les événements
  onScroll(event: any): void {
    // Gérer le défilement
    const element = event.target;
    this.showScrollToBottom =
      element.scrollTop < element.scrollHeight - element.clientHeight - 100;
  }

  loadMoreMessages(): void {
    this.isLoadingMore = true;
    this.currentPage++;
    this.getMessages();
    setTimeout(() => {
      this.isLoadingMore = false;
    }, 1000);
  }

  clearConversation(): void {
    if (confirm('Êtes-vous sûr de vouloir vider cette conversation ?')) {
      this.messages = [];
    }
  }

  exportConversation(): void {
    // Exporter la conversation
  }

  // Méthodes pour les en-têtes
  getHeaderActions(): any[] {
    return [
      {
        icon: 'fas fa-search',
        title: 'Rechercher',
        onClick: () => this.toggleSearch(),
        class: 'search-btn',
        isActive: this.showSearch,
      },
      {
        icon: 'fas fa-bell',
        title: 'Notifications',
        onClick: () => this.toggleNotifications(),
        class: 'notification-btn',
        badge:
          this.unreadNotificationsCount > 0
            ? {
                count: this.unreadNotificationsCount,
                class: 'bg-red-500',
                animate: true,
              }
            : null,
      },
      {
        icon: 'fas fa-phone',
        title: 'Appel audio',
        onClick: () => this.startCall('AUDIO' as CallType),
        class: 'call-btn',
      },
      {
        icon: 'fas fa-video',
        title: 'Appel vidéo',
        onClick: () => this.startCall('VIDEO' as CallType),
        class: 'video-btn',
      },
    ];
  }

  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===

  goBackToConversations(): void {
    this.router.navigate(['/front/messages']);
  }

  openUserProfile(userId: string): void {
    // Ouvrir le profil utilisateur
  }

  formatLastActive(lastActive: any): string {
    if (!lastActive) return 'Hors ligne';
    const date = new Date(lastActive);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return 'En ligne';
    if (minutes < 60) return `Il y a ${minutes} min`;
    if (minutes < 1440) return `Il y a ${Math.floor(minutes / 60)} h`;
    return `Il y a ${Math.floor(minutes / 1440)} j`;
  }

  toggleConversationSettings(): void {
    this.showConversationSettings = !this.showConversationSettings;
  }

  shouldShowDateSeparator(index: number): boolean {
    if (index === 0) return true;
    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];

    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;

    const currentDate = new Date(currentMessage.timestamp).toDateString();
    const previousDate = new Date(previousMessage.timestamp).toDateString();

    return currentDate !== previousDate;
  }

  formatDateSeparator(timestamp: any): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) return "Aujourd'hui";
    if (date.toDateString() === yesterday.toDateString()) return 'Hier';

    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  getSystemMessageIcon(message: any): string {
    const icons: any = {
      user_joined: 'fas fa-user-plus',
      user_left: 'fas fa-user-minus',
      call_started: 'fas fa-phone',
      call_ended: 'fas fa-phone-slash',
      message_deleted: 'fas fa-trash',
    };
    return icons[message.systemType] || 'fas fa-info-circle';
  }

  onMessageClick(message: any, event: any): void {
    if (this.selectionMode) {
      this.toggleMessageSelection(message.id);
    }
  }

  onMessageContextMenu(message: any, event: any): void {
    event.preventDefault();
    this.toggleMessageOptions(message.id);
  }

  onMessageHover(messageId: string, isHovering: boolean): void {
    this.hoveredMessageId = isHovering ? messageId : '';
  }

  shouldShowAvatar(index: number): boolean {
    if (index === this.messages.length - 1) return true;
    const currentMessage = this.messages[index];
    const nextMessage = this.messages[index + 1];
    return currentMessage?.sender?.id !== nextMessage?.sender?.id;
  }

  onAvatarError(event: any): void {
    event.target.src = 'assets/images/default-avatar.png';
  }

  isGroupConversation(): boolean {
    return (this.conversation as any)?.type === 'group';
  }

  shouldShowSenderName(index: number): boolean {
    if (!this.isGroupConversation()) return false;
    if (index === 0) return true;
    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];
    return currentMessage?.sender?.id !== previousMessage?.sender?.id;
  }

  getUserColor(userId: string): string {
    const colors = [
      '#ff6b6b',
      '#4ecdc4',
      '#45b7d1',
      '#96ceb4',
      '#feca57',
      '#ff9ff3',
    ];
    const index = userId.charCodeAt(0) % colors.length;
    return colors[index];
  }

  // Propriété Math pour le template
  get Math(): typeof Math {
    return Math;
  }

  // Méthodes pour les fichiers
  downloadSelectedFiles(): void {
    // Télécharger les fichiers sélectionnés
  }

  shareSelectedFiles(): void {
    // Partager les fichiers sélectionnés
  }

  deleteSelectedFiles(): void {
    // Supprimer les fichiers sélectionnés
  }

  // Méthodes pour les analytics
  updateAnalytics(): void {
    // Mettre à jour les analytics
  }

  exportAnalytics(): void {
    // Exporter les analytics
  }

  getMessageTypeIcon(type: string): string {
    const icons: any = {
      text: 'fas fa-comment',
      image: 'fas fa-image',
      video: 'fas fa-video',
      voice: 'fas fa-microphone',
      file: 'fas fa-file',
      location: 'fas fa-map-marker-alt',
      contact: 'fas fa-user',
    };
    return icons[type] || 'fas fa-comment';
  }

  // Méthodes pour les intégrations
  createNewIntegration(): void {
    // Créer une nouvelle intégration
  }

  selectIntegrationCategory(category: string): void {
    this.selectedIntegrationCategory = category;
  }

  getIntegrationCategoryIcon(category: string): string {
    const icons: any = {
      CRM: 'fas fa-users',
      Productivité: 'fas fa-tasks',
      Communication: 'fas fa-comments',
      Analytics: 'fas fa-chart-bar',
      Sécurité: 'fas fa-shield-alt',
    };
    return icons[category] || 'fas fa-puzzle-piece';
  }

  getAvailableIntegrations(): any[] {
    return [
      { id: 1, name: 'Slack', category: 'Communication', icon: 'fab fa-slack' },
      {
        id: 2,
        name: 'Trello',
        category: 'Productivité',
        icon: 'fab fa-trello',
      },
      {
        id: 3,
        name: 'Google Analytics',
        category: 'Analytics',
        icon: 'fab fa-google',
      },
    ];
  }

  configureIntegration(integration: any): void {
    // Configurer l'intégration
  }

  toggleIntegration(integration: any): void {
    // Basculer l'intégration
  }

  getActiveIntegrations(): any[] {
    return this.getAvailableIntegrations().filter((i) => i.active);
  }

  getIntegrationStatusText(status: string): string {
    const texts: any = {
      active: 'Actif',
      inactive: 'Inactif',
      error: 'Erreur',
      pending: 'En attente',
    };
    return texts[status] || 'Inconnu';
  }

  formatLastActivity(lastActivity: any): string {
    return this.formatLastActive(lastActivity);
  }

  viewIntegrationLogs(integration: any): void {
    // Voir les logs d'intégration
  }

  editIntegration(integration: any): void {
    // Modifier l'intégration
  }

  testIntegration(integration: any): void {
    // Tester l'intégration
  }

  removeIntegration(integration: any): void {
    // Supprimer l'intégration
  }

  // Méthodes pour les webhooks
  editWebhook(webhook: any): void {
    // Modifier le webhook
  }

  testWebhook(webhook: any): void {
    // Tester le webhook
  }

  deleteWebhook(webhook: any): void {
    // Supprimer le webhook
  }

  createWebhook(): void {
    // Créer un webhook
  }

  // Méthodes pour les modales
  hasActiveModal(): boolean {
    return (
      this.showImageViewer ||
      this.showForwardModal ||
      this.showAnalyticsDashboard ||
      this.showIntegrations ||
      this.showEmojiPicker ||
      this.showCamera ||
      this.showDrawingTool ||
      this.showFileManager
    );
  }

  closeActiveModal(): void {
    this.showImageViewer = false;
    this.showForwardModal = false;
    this.showAnalyticsDashboard = false;
    this.showIntegrations = false;
    this.showEmojiPicker = false;
    this.showCamera = false;
    this.showDrawingTool = false;
    this.showFileManager = false;
  }

  // === MÉTHODES MANQUANTES POUR LE TEMPLATE HTML ===

  // Méthodes pour l'éditeur d'images
  flipImage(direction: string): void {
    console.log('Flip image:', direction);
  }

  applyImageFilter(filterName: string): void {
    this.selectedImageFilter = filterName;
  }

  updateImageAdjustments(): void {
    // Mettre à jour les ajustements d'image
  }

  resetImageAdjustments(): void {
    this.imageAdjustments = {
      brightness: 0,
      contrast: 0,
      saturation: 0,
      hue: 0,
    };
  }

  addTextToImage(): void {
    if (this.newTextContent.trim()) {
      this.imageTextElements.push({
        id: Date.now().toString(),
        content: this.newTextContent,
        x: 50,
        y: 50,
        fontFamily: this.textFontFamily,
        fontSize: this.textFontSize,
        color: this.textColor,
      });
      this.newTextContent = '';
    }
  }

  // Méthodes pour le gestionnaire de fichiers
  createNewFolder(): void {
    const folderName = prompt('Nom du nouveau dossier:');
    if (folderName) {
      this.fileFolders.push({
        id: Date.now().toString(),
        name: folderName,
        type: 'folder',
        size: 0,
        modifiedAt: new Date(),
      });
    }
  }

  uploadFiles(): void {
    this.openFileInput();
  }

  toggleFileView(): void {
    this.fileViewMode = this.fileViewMode === 'grid' ? 'list' : 'grid';
  }

  closeFileManager(): void {
    this.showFileManager = false;
  }

  selectFolder(folder: any): void {
    this.selectedFolder = folder;
  }

  renameFolder(folder: any): void {
    const newName = prompt('Nouveau nom:', folder.name);
    if (newName) {
      folder.name = newName;
    }
  }

  deleteFolder(folder: any): void {
    if (confirm('Supprimer ce dossier ?')) {
      this.fileFolders = this.fileFolders.filter((f) => f.id !== folder.id);
    }
  }

  getStorageUsagePercentage(): number {
    return 65; // Exemple
  }

  getStorageUsed(): string {
    return '6.5 GB';
  }

  getStorageTotal(): string {
    return '10 GB';
  }

  getTotalFilesCount(): number {
    return 1247;
  }

  navigateToFolder(crumb: any): void {
    this.selectedFolder = crumb;
  }

  onFileSearch(): void {
    // Rechercher dans les fichiers
  }

  applyFileFilters(): void {
    // Appliquer les filtres de fichiers
  }

  sortFiles(): void {
    // Trier les fichiers
  }

  getFilteredFiles(): any[] {
    return [
      {
        id: '1',
        name: 'Document.pdf',
        type: 'file',
        size: 1024000,
        modifiedAt: new Date(),
      },
      {
        id: '2',
        name: 'Image.jpg',
        type: 'image',
        size: 2048000,
        modifiedAt: new Date(),
      },
    ];
  }

  toggleFileSelection(file: any): void {
    const index = this.selectedFiles.indexOf(file.id);
    if (index > -1) {
      this.selectedFiles.splice(index, 1);
    } else {
      this.selectedFiles.push(file.id);
    }
  }

  openFile(file: any): void {
    console.log('Ouvrir fichier:', file.name);
  }

  formatFileDate(date: any): string {
    return new Date(date).toLocaleDateString('fr-FR');
  }

  shareFile(file: any): void {
    console.log('Partager fichier:', file.name);
  }

  deleteFile(file: any): void {
    if (confirm('Supprimer ce fichier ?')) {
      console.log('Fichier supprimé:', file.name);
    }
  }

  // Méthodes pour les propriétés manquantes dans le template
  get forwarded(): boolean {
    return false; // Propriété pour les messages transférés
  }

  // Méthodes pour corriger les erreurs du template
  getOnlineUsersCount(): number {
    return this.onlineUsersCount;
  }

  // Initialiser des données de test
  private initializeTestData(): void {
    // Créer une conversation de test
    this.conversation = {
      id: 'test-conversation',
      participants: [
        { id: 'user1', username: 'Alice' },
        { id: 'user2', username: 'Bob' },
      ],
    } as Conversation;

    // Créer des messages de test
    this.messages = [
      {
        id: '1',
        content: 'Salut ! Comment ça va ?',
        sender: { id: 'user2', username: 'Bob' },
        timestamp: new Date(Date.now() - 3600000),
        conversationId: 'test-conversation',
        type: 'text' as MessageType,
      },
      {
        id: '2',
        content: 'Ça va bien, merci ! Et toi ?',
        sender: { id: this.currentUserId!, username: this.currentUsername },
        timestamp: new Date(Date.now() - 3000000),
        conversationId: 'test-conversation',
        type: 'text' as MessageType,
      },
      {
        id: '3',
        content: "Super ! Tu veux qu'on se voit ce soir ?",
        sender: { id: 'user2', username: 'Bob' },
        timestamp: new Date(Date.now() - 1800000),
        conversationId: 'test-conversation',
        type: 'text' as MessageType,
      },
    ];

    this.otherParticipant = { id: 'user2', username: 'Bob' } as User;
    this.loading = false;
  }
}
