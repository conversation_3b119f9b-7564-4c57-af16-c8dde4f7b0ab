import {
  Component,
  On<PERSON>ni<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Child,
  ElementRef,
  ChangeDetectorRef,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../../services/message.service';
import { ToastService } from '../../../../services/toast.service';

@Component({
  selector: 'app-message-chat',
  templateUrl: './message-chat.component.html',
})
export class MessageChatComponent implements OnInit, OnDestroy {
  // === RÉFÉRENCES DOM ===
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  @ViewChild('fileInput', { static: false })
  fileInput!: ElementRef<HTMLInputElement>;

  // === DONNÉES PRINCIPALES ===
  conversation: any = null;
  messages: any[] = [];
  currentUserId: string | null = null;
  currentUsername = 'You';
  messageForm: FormGroup;
  otherParticipant: any = null;

  // === ÉTATS DE L'INTERFACE ===
  isLoading = false;
  isLoadingMore = false;
  hasMoreMessages = true;
  showEmojiPicker = false;
  showAttachmentMenu = false;
  showSearch = false;
  searchQuery = '';
  searchResults: any[] = [];
  searchMode = false;
  isSendingMessage = false;

  // === ENREGISTREMENT VOCAL ===
  isRecordingVoice = false;
  voiceRecordingDuration = 0;
  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private recordingTimer: any = null;

  // === APPELS ===
  isInCall = false;
  callType: 'VIDEO' | 'AUDIO' | null = null;
  callDuration = 0;
  private callTimer: any = null;

  // === ÉMOJIS ===
  emojiCategories: any[] = [
    {
      id: 'smileys',
      name: 'Smileys',
      icon: '😀',
      emojis: [
        { emoji: '😀', name: 'grinning face' },
        { emoji: '😃', name: 'grinning face with big eyes' },
        { emoji: '😄', name: 'grinning face with smiling eyes' },
        { emoji: '😁', name: 'beaming face with smiling eyes' },
        { emoji: '😆', name: 'grinning squinting face' },
        { emoji: '😅', name: 'grinning face with sweat' },
        { emoji: '😂', name: 'face with tears of joy' },
        { emoji: '🤣', name: 'rolling on the floor laughing' },
        { emoji: '😊', name: 'smiling face with smiling eyes' },
        { emoji: '😇', name: 'smiling face with halo' },
      ],
    },
    {
      id: 'people',
      name: 'People',
      icon: '👤',
      emojis: [
        { emoji: '👶', name: 'baby' },
        { emoji: '🧒', name: 'child' },
        { emoji: '👦', name: 'boy' },
        { emoji: '👧', name: 'girl' },
        { emoji: '🧑', name: 'person' },
        { emoji: '👨', name: 'man' },
        { emoji: '👩', name: 'woman' },
        { emoji: '👴', name: 'old man' },
        { emoji: '👵', name: 'old woman' },
      ],
    },
    {
      id: 'nature',
      name: 'Nature',
      icon: '🌿',
      emojis: [
        { emoji: '🐶', name: 'dog face' },
        { emoji: '🐱', name: 'cat face' },
        { emoji: '🐭', name: 'mouse face' },
        { emoji: '🐹', name: 'hamster' },
        { emoji: '🐰', name: 'rabbit face' },
        { emoji: '🦊', name: 'fox' },
        { emoji: '🐻', name: 'bear' },
        { emoji: '🐼', name: 'panda' },
      ],
    },
  ];
  selectedEmojiCategory = this.emojiCategories[0];

  // === PAGINATION ===
  private readonly MAX_MESSAGES_TO_LOAD = 10;
  private currentPage = 1;

  // === AUTRES ÉTATS ===
  isTyping = false;
  isUserTyping = false;
  private typingTimeout: any = null;
  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private MessageService: MessageService,
    private toastService: ToastService,
    private cdr: ChangeDetectorRef
  ) {
    this.messageForm = this.fb.group({
      content: ['', [Validators.required, Validators.minLength(1)]],
    });
  }

  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    this.loadCurrentUser();
    this.loadConversation();
    this.setupSubscriptions();
  }

  private loadCurrentUser(): void {
    try {
      const userString = localStorage.getItem('user');
      console.log('Raw user from localStorage:', userString);

      const user = JSON.parse(userString || '{}');
      console.log('Parsed user object:', user);

      if (user && user._id) {
        this.currentUserId = user._id;
        this.currentUsername = user.username || 'You';
        console.log('Current user loaded:', {
          id: this.currentUserId,
          username: this.currentUsername,
        });
      } else {
        console.warn('No valid user found in localStorage');
        this.currentUserId = null;
        this.currentUsername = 'You';
      }
    } catch (error) {
      console.error("Erreur lors du chargement de l'utilisateur:", error);
      this.currentUserId = null;
      this.currentUsername = 'You';
    }
  }

  private loadConversation(): void {
    const conversationId = this.route.snapshot.paramMap.get('id');
    console.log('Loading conversation with ID:', conversationId);

    if (!conversationId) {
      this.toastService.showError('ID de conversation manquant');
      return;
    }

    this.isLoading = true;
    this.MessageService.getConversation(conversationId).subscribe({
      next: (conversation) => {
        console.log('Conversation loaded successfully:', conversation);
        this.conversation = conversation;
        this.setOtherParticipant();
        this.loadMessages();
      },
      error: (error) => {
        console.error('Erreur lors du chargement de la conversation:', error);
        this.toastService.showError(
          'Erreur lors du chargement de la conversation'
        );
        this.isLoading = false;
      },
    });
  }

  private setOtherParticipant(): void {
    if (this.conversation?.participants) {
      console.log('Setting other participant...');
      console.log('Current user ID:', this.currentUserId);
      console.log('All participants:', this.conversation.participants);

      // Essayer différentes méthodes pour trouver l'autre participant
      this.otherParticipant = this.conversation.participants.find((p: any) => {
        const participantId = p.id || p._id;
        console.log(
          'Comparing participant ID:',
          participantId,
          'with current user ID:',
          this.currentUserId
        );
        // Comparaison stricte des IDs en string
        return String(participantId) !== String(this.currentUserId);
      });

      console.log('Other participant found:', this.otherParticipant);

      // Si otherParticipant n'est pas trouvé, essayons avec une logique différente
      if (!this.otherParticipant && this.conversation.participants.length > 0) {
        console.log('Fallback: using first participant');
        this.otherParticipant = this.conversation.participants[0];

        // Si le premier participant est l'utilisateur actuel, prendre le deuxième
        if (
          this.conversation.participants.length > 1 &&
          (String(this.otherParticipant.id) === String(this.currentUserId) ||
            String(this.otherParticipant._id) === String(this.currentUserId))
        ) {
          console.log(
            'First participant is current user, using second participant'
          );
          this.otherParticipant = this.conversation.participants[1];
        }
      }

      // Vérification finale
      if (this.otherParticipant) {
        console.log('Final other participant:', {
          id: this.otherParticipant.id || this.otherParticipant._id,
          username: this.otherParticipant.username,
          image: this.otherParticipant.image,
          isOnline: this.otherParticipant.isOnline,
        });
      } else {
        console.warn('No other participant found!');
      }
    }
  }

  private loadMessages(): void {
    if (!this.conversation?.id) return;

    // Les messages sont déjà chargés avec la conversation
    this.messages = this.conversation.messages || [];
    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;
    this.isLoading = false;
    this.scrollToBottom();
  }

  private setupSubscriptions(): void {
    // Abonnements GraphQL pour les mises à jour en temps réel
    // À implémenter selon vos besoins
  }

  // === ENVOI DE MESSAGES ===
  sendMessage(): void {
    if (!this.messageForm.valid || !this.conversation?.id) return;

    const content = this.messageForm.get('content')?.value?.trim();
    if (!content) return;

    const receiverId = this.conversation.participants?.find(
      (p: any) => p.id !== this.currentUserId
    )?.id;

    if (!receiverId) {
      this.toastService.showError('Destinataire introuvable');
      return;
    }

    this.MessageService.sendMessage(
      receiverId,
      content,
      undefined,
      'TEXT' as any,
      this.conversation.id
    ).subscribe({
      next: (message: any) => {
        this.messages.push(message);
        this.messageForm.reset();
        this.scrollToBottom();
      },
      error: (error: any) => {
        console.error("Erreur lors de l'envoi du message:", error);
        this.toastService.showError("Erreur lors de l'envoi du message");
      },
    });
  }

  scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  // === MÉTHODES POUR LE TEMPLATE ===
  formatLastActive(lastActive: string | Date | null): string {
    if (!lastActive) return 'Hors ligne';

    const date = new Date(lastActive);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return "À l'instant";
    if (diffMins < 60) return `Il y a ${diffMins} min`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `Il y a ${diffHours}h`;

    const diffDays = Math.floor(diffHours / 24);
    return `Il y a ${diffDays}j`;
  }

  formatMessageTime(timestamp: string | Date): string {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  formatDateSeparator(timestamp: string | Date): string {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Aujourd'hui";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Hier';
    } else {
      return date.toLocaleDateString('fr-FR');
    }
  }

  formatMessageContent(content: string): string {
    if (!content) return '';

    // Remplacer les URLs par des liens
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return content.replace(
      urlRegex,
      '<a href="$1" target="_blank" class="text-blue-500 underline">$1</a>'
    );
  }

  shouldShowDateSeparator(index: number): boolean {
    if (index === 0) return true;

    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];

    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;

    const currentDate = new Date(currentMessage.timestamp).toDateString();
    const previousDate = new Date(previousMessage.timestamp).toDateString();

    return currentDate !== previousDate;
  }

  shouldShowAvatar(index: number): boolean {
    const currentMessage = this.messages[index];
    const nextMessage = this.messages[index + 1];

    if (!nextMessage) return true;

    return currentMessage.sender?.id !== nextMessage.sender?.id;
  }

  shouldShowSenderName(index: number): boolean {
    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];

    if (!previousMessage) return true;

    return currentMessage.sender?.id !== previousMessage.sender?.id;
  }

  getMessageType(message: any): string {
    if (message.attachments && message.attachments.length > 0) {
      const attachment = message.attachments[0];
      if (attachment.type?.startsWith('image/')) return 'image';
      if (attachment.type?.startsWith('video/')) return 'video';
      if (attachment.type?.startsWith('audio/')) return 'audio';
      return 'file';
    }
    return 'text';
  }

  hasImage(message: any): boolean {
    return (
      message.attachments?.some((att: any) => att.type?.startsWith('image/')) ||
      false
    );
  }

  hasFile(message: any): boolean {
    return (
      message.attachments?.some(
        (att: any) => !att.type?.startsWith('image/')
      ) || false
    );
  }

  getImageUrl(message: any): string {
    const imageAttachment = message.attachments?.find((att: any) =>
      att.type?.startsWith('image/')
    );
    return imageAttachment?.url || '';
  }

  getFileName(message: any): string {
    const fileAttachment = message.attachments?.find(
      (att: any) => !att.type?.startsWith('image/')
    );
    return fileAttachment?.name || 'Fichier';
  }

  getFileSize(message: any): string {
    const fileAttachment = message.attachments?.find(
      (att: any) => !att.type?.startsWith('image/')
    );
    if (!fileAttachment?.size) return '';

    const bytes = fileAttachment.size;
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';
    return Math.round(bytes / 1048576) + ' MB';
  }

  getFileIcon(message: any): string {
    const fileAttachment = message.attachments?.find(
      (att: any) => !att.type?.startsWith('image/')
    );
    if (!fileAttachment?.type) return 'fas fa-file';

    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';
    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';
    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';
    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';
    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';
    return 'fas fa-file';
  }

  getUserColor(userId: string): string {
    // Générer une couleur basée sur l'ID utilisateur
    const colors = [
      '#FF6B6B',
      '#4ECDC4',
      '#45B7D1',
      '#96CEB4',
      '#FFEAA7',
      '#DDA0DD',
      '#98D8C8',
    ];
    const index = userId.charCodeAt(0) % colors.length;
    return colors[index];
  }

  // === MÉTHODES D'INTERACTION ===
  onMessageClick(message: any, event: any): void {
    console.log('Message clicked:', message);
  }

  onMessageContextMenu(message: any, event: any): void {
    event.preventDefault();
    console.log('Message context menu:', message);
  }

  onInputChange(event: any): void {
    // Gérer les changements dans le champ de saisie
  }

  onInputKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  onInputFocus(): void {
    // Gérer le focus sur le champ de saisie
  }

  onInputBlur(): void {
    // Gérer la perte de focus sur le champ de saisie
  }

  onScroll(event: any): void {
    // Gérer le scroll pour charger plus de messages
  }

  openUserProfile(userId: string): void {
    console.log('Opening user profile for:', userId);
  }

  openImageViewer(message: any): void {
    const imageAttachment = message.attachments?.find((att: any) =>
      att.type?.startsWith('image/')
    );
    if (imageAttachment?.url) {
      window.open(imageAttachment.url, '_blank');
    }
  }

  downloadFile(message: any): void {
    const fileAttachment = message.attachments?.find(
      (att: any) => !att.type?.startsWith('image/')
    );
    if (fileAttachment?.url) {
      window.open(fileAttachment.url, '_blank');
    }
  }

  toggleReaction(messageId: string, emoji: string): void {
    console.log('Toggle reaction:', messageId, emoji);
  }

  hasUserReacted(reaction: any, userId: string): boolean {
    return reaction.users?.includes(userId) || false;
  }

  toggleSearch(): void {
    this.searchMode = !this.searchMode;
    if (!this.searchMode) {
      this.searchQuery = '';
      this.searchResults = [];
    }
  }

  toggleMainMenu(): void {
    console.log('Toggle main menu');
  }

  toggleAttachmentMenu(): void {
    this.showAttachmentMenu = !this.showAttachmentMenu;
    this.showEmojiPicker = false;
  }

  toggleEmojiPicker(): void {
    this.showEmojiPicker = !this.showEmojiPicker;
    this.showAttachmentMenu = false;
  }

  triggerFileInput(type?: string): void {
    // Déclencher la sélection de fichier
    const input = document.createElement('input');
    input.type = 'file';

    if (type === 'image') {
      input.accept = 'image/*';
    } else if (type === 'video') {
      input.accept = 'video/*';
    } else if (type === 'document') {
      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';
    }

    input.onchange = (event: any) => {
      // Gérer la sélection de fichier
      console.log('File selected:', event.target.files);
    };

    input.click();
    this.showAttachmentMenu = false;
  }

  openCamera(): void {
    console.log('Opening camera...');
    this.showAttachmentMenu = false;
  }

  // === MÉTHODES POUR LES ÉMOJIS ===
  getEmojisForCategory(category: any): any[] {
    return category?.emojis || [];
  }

  selectEmojiCategory(category: any): void {
    this.selectedEmojiCategory = category;
  }

  insertEmoji(emoji: any): void {
    const currentContent = this.messageForm.get('content')?.value || '';
    const newContent = currentContent + (emoji.emoji || emoji);
    this.messageForm.patchValue({ content: newContent });

    this.showEmojiPicker = false;

    setTimeout(() => {
      const textarea = document.querySelector(
        'textarea[formControlName="content"]'
      ) as HTMLTextAreaElement;
      if (textarea) {
        textarea.focus();
      }
    }, 100);
  }

  // === MÉTHODES MANQUANTES ===
  goBackToConversations(): void {
    window.history.back();
  }

  startVideoCall(): void {
    console.log('Starting video call...');
    // Implémenter l'appel vidéo
  }

  startVoiceCall(): void {
    console.log('Starting voice call...');
    // Implémenter l'appel audio
  }

  trackByMessageId(index: number, message: any): any {
    return message.id || index;
  }

  isGroupConversation(): boolean {
    return this.conversation?.participants?.length > 2 || false;
  }

  startVoiceRecording(): void {
    console.log('Starting voice recording...');
    // Implémenter l'enregistrement vocal
  }

  stopVoiceRecording(): void {
    console.log('Stopping voice recording...');
    // Implémenter l'arrêt de l'enregistrement vocal
  }

  cancelVoiceRecording(): void {
    console.log('Cancelling voice recording...');
    // Implémenter l'annulation de l'enregistrement vocal
  }

  onFileSelected(event: any): void {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    for (let file of files) {
      console.log('File selected:', file.name);
      // Implémenter l'upload de fichier
    }
  }

  getFileAcceptTypes(): string {
    return '*/*';
  }

  closeAllMenus(): void {
    this.showEmojiPicker = false;
    this.showAttachmentMenu = false;
    this.showSearch = false;
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}
