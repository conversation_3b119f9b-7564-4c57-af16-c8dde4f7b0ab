import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON>t,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  ChangeDetectorRef,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../../services/message.service';
import { AuthUserService } from '../../../../services/auth-user.service';
import { ToastService } from '../../../../services/toast.service';
import { CallType } from '../../../../models/call.model';

@Component({
  selector: 'app-message-chat',
  templateUrl: './message-chat.component.html',
})
export class MessageChatComponent implements OnInit, OnDestroy {
  // === RÉFÉRENCES DOM ===
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  @ViewChild('fileInput', { static: false })
  fileInput!: ElementRef<HTMLInputElement>;

  // === DONNÉES PRINCIPALES ===
  conversation: any = null;
  messages: any[] = [];
  currentUserId: string | null = null;
  currentUsername = 'You';
  messageForm: FormGroup;

  // === ÉTATS DE L'INTERFACE ===
  isLoading = false;
  isLoadingMore = false;
  hasMoreMessages = true;
  showEmojiPicker = false;
  showAttachmentMenu = false;
  showSearch = false;
  searchQuery = '';
  searchResults: any[] = [];

  // === ENREGISTREMENT VOCAL ===
  isRecordingVoice = false;
  voiceRecordingDuration = 0;
  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private recordingTimer: any = null;

  // === APPELS ===
  isInCall = false;
  callType: CallType | null = null;
  callDuration = 0;
  private callTimer: any = null;

  // === ÉMOJIS ===
  emojiCategories: any[] = [
    {
      id: 'smileys',
      name: 'Smileys',
      icon: '😀',
      emojis: [
        { emoji: '😀', name: 'grinning face' },
        { emoji: '😃', name: 'grinning face with big eyes' },
        { emoji: '😄', name: 'grinning face with smiling eyes' },
        { emoji: '😁', name: 'beaming face with smiling eyes' },
        { emoji: '😆', name: 'grinning squinting face' },
        { emoji: '😅', name: 'grinning face with sweat' },
        { emoji: '😂', name: 'face with tears of joy' },
        { emoji: '🤣', name: 'rolling on the floor laughing' },
        { emoji: '😊', name: 'smiling face with smiling eyes' },
        { emoji: '😇', name: 'smiling face with halo' },
      ],
    },
    {
      id: 'people',
      name: 'People',
      icon: '👤',
      emojis: [
        { emoji: '👶', name: 'baby' },
        { emoji: '🧒', name: 'child' },
        { emoji: '👦', name: 'boy' },
        { emoji: '👧', name: 'girl' },
        { emoji: '🧑', name: 'person' },
        { emoji: '👨', name: 'man' },
        { emoji: '👩', name: 'woman' },
        { emoji: '👴', name: 'old man' },
        { emoji: '👵', name: 'old woman' },
      ],
    },
    {
      id: 'nature',
      name: 'Nature',
      icon: '🌿',
      emojis: [
        { emoji: '🐶', name: 'dog face' },
        { emoji: '🐱', name: 'cat face' },
        { emoji: '🐭', name: 'mouse face' },
        { emoji: '🐹', name: 'hamster' },
        { emoji: '🐰', name: 'rabbit face' },
        { emoji: '🦊', name: 'fox' },
        { emoji: '🐻', name: 'bear' },
        { emoji: '🐼', name: 'panda' },
      ],
    },
  ];
  selectedEmojiCategory = this.emojiCategories[0];

  // === PAGINATION ===
  private readonly MAX_MESSAGES_TO_LOAD = 10;
  private currentPage = 1;

  // === AUTRES ÉTATS ===
  isTyping = false;
  private typingTimeout: any = null;
  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private MessageService: MessageService,
    private authUserService: AuthUserService,
    private toastService: ToastService,
    private cdr: ChangeDetectorRef
  ) {
    this.messageForm = this.fb.group({
      content: ['', [Validators.required, Validators.minLength(1)]],
    });
  }

  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    this.loadCurrentUser();
    this.loadConversation();
    this.setupSubscriptions();
  }

  private loadCurrentUser(): void {
    try {
      const user = this.authUserService.getCurrentUser();
      if (user) {
        this.currentUserId = user.id || null;
        this.currentUsername = user.username || 'You';
      }
    } catch (error) {
      console.error("Erreur lors du chargement de l'utilisateur:", error);
      this.currentUserId = null;
      this.currentUsername = 'You';
    }
  }

  private loadConversation(): void {
    const conversationId = this.route.snapshot.paramMap.get('id');
    if (!conversationId) {
      this.toastService.showError('ID de conversation manquant');
      return;
    }

    this.isLoading = true;
    this.MessageService.getConversation(
      conversationId,
      this.currentUserId!
    ).subscribe({
      next: (conversation) => {
        this.conversation = conversation;
        this.loadMessages();
      },
      error: (error) => {
        console.error('Erreur lors du chargement de la conversation:', error);
        this.toastService.showError(
          'Erreur lors du chargement de la conversation'
        );
        this.isLoading = false;
      },
    });
  }

  private loadMessages(): void {
    if (!this.conversation?.id) return;

    this.MessageService.getMessages(
      this.conversation.id,
      undefined,
      undefined,
      this.currentPage,
      this.MAX_MESSAGES_TO_LOAD,
      this.currentUserId!
    ).subscribe({
      next: (messages) => {
        this.messages = messages || [];
        this.isLoading = false;
        this.scrollToBottom();
      },
      error: (error) => {
        console.error('Erreur lors du chargement des messages:', error);
        this.toastService.showError('Erreur lors du chargement des messages');
        this.isLoading = false;
      },
    });
  }

  private setupSubscriptions(): void {
    // Abonnements GraphQL pour les mises à jour en temps réel
    // À implémenter selon vos besoins
  }

  // === ENVOI DE MESSAGES ===
  sendMessage(): void {
    if (!this.messageForm.valid || !this.conversation?.id) return;

    const content = this.messageForm.get('content')?.value?.trim();
    if (!content) return;

    const receiverId = this.conversation.participants?.find(
      (p: any) => p.id !== this.currentUserId
    )?.id;

    if (!receiverId) {
      this.toastService.showError('Destinataire introuvable');
      return;
    }

    this.MessageService.sendMessage(
      receiverId,
      content,
      undefined,
      'TEXT' as any,
      this.conversation.id
    ).subscribe({
      next: (message: any) => {
        this.messages.push(message);
        this.messageForm.reset();
        this.scrollToBottom();
      },
      error: (error: any) => {
        console.error("Erreur lors de l'envoi du message:", error);
        this.toastService.showError("Erreur lors de l'envoi du message");
      },
    });
  }

  // === GESTION DES FICHIERS ===
  onFileSelected(event: any): void {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    for (let file of files) {
      this.uploadFile(file);
    }
  }

  private uploadFile(file: File): void {
    const receiverId = this.conversation?.participants?.find(
      (p: any) => p.id !== this.currentUserId
    )?.id;

    if (!receiverId) {
      this.toastService.showError('Destinataire introuvable');
      return;
    }

    this.MessageService.sendFileMessage(
      receiverId,
      file,
      this.conversation.id
    ).subscribe({
      next: (message: any) => {
        this.messages.push(message);
        this.scrollToBottom();
        this.toastService.showSuccess('Fichier envoyé avec succès');
      },
      error: (error: any) => {
        console.error("Erreur lors de l'envoi du fichier:", error);
        this.toastService.showError("Erreur lors de l'envoi du fichier");
      },
    });
  }

  // === ENREGISTREMENT VOCAL ===
  async startVoiceRecording(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      this.audioChunks = [];
      this.isRecordingVoice = true;
      this.voiceRecordingDuration = 0;
      this.voiceRecordingState = 'recording';

      this.recordingTimer = setInterval(() => {
        this.voiceRecordingDuration++;
        this.cdr.detectChanges();
      }, 1000);

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.processRecordedAudio();
      };

      this.mediaRecorder.start(100);
      this.toastService.showSuccess('Enregistrement vocal démarré');
    } catch (error) {
      console.error("Erreur lors du démarrage de l'enregistrement:", error);
      this.toastService.showError("Impossible d'accéder au microphone");
      this.cancelVoiceRecording();
    }
  }

  stopVoiceRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
    }

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }

    this.isRecordingVoice = false;
    this.voiceRecordingState = 'processing';
  }

  cancelVoiceRecording(): void {
    if (this.mediaRecorder) {
      if (this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop();
      }
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
      this.mediaRecorder = null;
    }

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }

    this.isRecordingVoice = false;
    this.voiceRecordingDuration = 0;
    this.voiceRecordingState = 'idle';
    this.audioChunks = [];
  }

  private async processRecordedAudio(): Promise<void> {
    try {
      if (this.audioChunks.length === 0) {
        this.toastService.showWarning('Aucun audio enregistré');
        this.cancelVoiceRecording();
        return;
      }

      const audioBlob = new Blob(this.audioChunks, {
        type: 'audio/webm;codecs=opus',
      });

      if (this.voiceRecordingDuration < 1) {
        this.toastService.showWarning(
          'Enregistrement trop court (minimum 1 seconde)'
        );
        this.cancelVoiceRecording();
        return;
      }

      const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {
        type: 'audio/webm;codecs=opus',
      });

      await this.sendVoiceMessage(audioFile);
      this.toastService.showSuccess('Message vocal envoyé');
    } catch (error) {
      console.error("Erreur lors du traitement de l'audio:", error);
      this.toastService.showError("Erreur lors de l'envoi du message vocal");
    } finally {
      this.voiceRecordingState = 'idle';
      this.voiceRecordingDuration = 0;
      this.audioChunks = [];
    }
  }

  private async sendVoiceMessage(audioFile: File): Promise<void> {
    const receiverId = this.conversation?.participants?.find(
      (p: any) => p.id !== this.currentUserId
    )?.id;

    if (!receiverId) {
      throw new Error('Destinataire introuvable');
    }

    return new Promise((resolve, reject) => {
      this.MessageService.sendFileMessage(
        receiverId,
        audioFile,
        this.conversation.id
      ).subscribe({
        next: (message: any) => {
          this.messages.push(message);
          this.scrollToBottom();
          resolve();
        },
        error: (error: any) => {
          console.error("Erreur lors de l'envoi du message vocal:", error);
          reject(error);
        },
      });
    });
  }

  formatRecordingDuration(duration: number): string {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  // === SYSTÈME D'ÉMOJIS ===
  getEmojisForCategory(category: any): any[] {
    return category?.emojis || [];
  }

  selectEmojiCategory(category: any): void {
    this.selectedEmojiCategory = category;
  }

  insertEmoji(emoji: any): void {
    const currentContent = this.messageForm.get('content')?.value || '';
    const newContent = currentContent + (emoji.emoji || emoji);
    this.messageForm.patchValue({ content: newContent });

    this.showEmojiPicker = false;

    setTimeout(() => {
      const textarea = document.querySelector(
        'textarea[formControlName="content"]'
      ) as HTMLTextAreaElement;
      if (textarea) {
        textarea.focus();
      }
    }, 100);
  }

  toggleEmojiPicker(): void {
    this.showEmojiPicker = !this.showEmojiPicker;
    this.showAttachmentMenu = false;
  }

  // === APPELS VIDÉO/AUDIO ===
  startCall(type: CallType): void {
    this.callType = type;
    this.isInCall = true;
    this.callDuration = 0;

    this.callTimer = setInterval(() => {
      this.callDuration++;
      this.cdr.detectChanges();
    }, 1000);

    this.toastService.showSuccess(
      `Appel ${type === 'VIDEO' ? 'vidéo' : 'audio'} démarré`
    );
  }

  endCall(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }

    this.isInCall = false;
    this.callType = null;
    this.callDuration = 0;
    this.toastService.showSuccess('Appel terminé');
  }

  startVideoCall(): void {
    this.startCall('VIDEO' as CallType);
  }

  startVoiceCall(): void {
    this.startCall('AUDIO' as CallType);
  }

  // === RECHERCHE ===
  toggleSearch(): void {
    this.showSearch = !this.showSearch;
    if (!this.showSearch) {
      this.searchQuery = '';
      this.searchResults = [];
    }
  }

  searchMessages(): void {
    if (!this.searchQuery.trim()) {
      this.searchResults = [];
      return;
    }

    this.searchResults = this.messages.filter(
      (message) =>
        message.content
          ?.toLowerCase()
          .includes(this.searchQuery.toLowerCase()) ||
        message.sender?.username
          ?.toLowerCase()
          .includes(this.searchQuery.toLowerCase())
    );
  }

  // === PAGINATION ===
  loadMoreMessages(): void {
    if (this.isLoadingMore || !this.hasMoreMessages) return;

    this.isLoadingMore = true;
    this.currentPage++;

    this.MessageService.getMessages(
      this.conversation.id,
      undefined,
      undefined,
      this.currentPage,
      this.MAX_MESSAGES_TO_LOAD,
      this.currentUserId!
    ).subscribe({
      next: (messages) => {
        if (messages && messages.length > 0) {
          this.messages = [...messages, ...this.messages];
        } else {
          this.hasMoreMessages = false;
        }
        this.isLoadingMore = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des messages:', error);
        this.isLoadingMore = false;
      },
    });
  }

  // === GÉOLOCALISATION ===
  async shareLocation(): Promise<void> {
    try {
      const position = await this.getCurrentPosition();
      const { latitude, longitude } = position.coords;

      const locationMessage = {
        type: 'location',
        latitude,
        longitude,
        address: await this.getAddressFromCoordinates(latitude, longitude),
        mapUrl: `https://maps.google.com/maps?q=${latitude},${longitude}&z=15`,
        timestamp: new Date(),
      };

      await this.sendLocationMessage(locationMessage);
      this.toastService.showSuccess('Position partagée avec succès');
    } catch (error) {
      console.error('Erreur lors du partage de localisation:', error);
      this.toastService.showError("Impossible d'obtenir votre position");
    }
  }

  private getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      });
    });
  }

  private async getAddressFromCoordinates(
    lat: number,
    lng: number
  ): Promise<string> {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
      );

      if (response.ok) {
        const data = await response.json();
        return data.display_name || `${lat}, ${lng}`;
      }
    } catch (error) {
      console.error('Erreur lors du géocodage inverse:', error);
    }

    return `${lat}, ${lng}`;
  }

  private async sendLocationMessage(locationData: any): Promise<void> {
    const receiverId = this.conversation?.participants?.find(
      (p: any) => p.id !== this.currentUserId
    )?.id;

    if (!receiverId) {
      throw new Error('Destinataire introuvable');
    }

    return new Promise((resolve, reject) => {
      this.MessageService.sendMessage(
        receiverId,
        `📍 Position partagée: ${locationData.address}`,
        undefined,
        'TEXT' as any,
        this.conversation.id
      ).subscribe({
        next: (message: any) => {
          this.messages.push(message);
          this.scrollToBottom();
          resolve();
        },
        error: (error: any) => {
          console.error(
            "Erreur lors de l'envoi du message de localisation:",
            error
          );
          reject(error);
        },
      });
    });
  }

  // === PARTAGE DE CONTACTS ===
  async shareContact(): Promise<void> {
    try {
      const contact = await this.selectContact();

      if (contact) {
        await this.sendContactMessage(contact);
        this.toastService.showSuccess('Contact partagé avec succès');
      }
    } catch (error) {
      console.error('Erreur lors du partage de contact:', error);
      this.toastService.showError('Erreur lors du partage du contact');
    }
  }

  private async selectContact(): Promise<any> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          name: 'John Doe',
          phone: '+33 6 12 34 56 78',
          email: '<EMAIL>',
          avatar: 'assets/images/default-avatar.png',
        });
      }, 1000);
    });
  }

  private async sendContactMessage(contactData: any): Promise<void> {
    const receiverId = this.conversation?.participants?.find(
      (p: any) => p.id !== this.currentUserId
    )?.id;

    if (!receiverId) {
      throw new Error('Destinataire introuvable');
    }

    return new Promise((resolve, reject) => {
      this.MessageService.sendMessage(
        receiverId,
        `👤 Contact partagé: ${contactData.name}\n📞 ${contactData.phone}\n📧 ${contactData.email}`,
        undefined,
        'TEXT' as any,
        this.conversation.id
      ).subscribe({
        next: (message: any) => {
          this.messages.push(message);
          this.scrollToBottom();
          resolve();
        },
        error: (error: any) => {
          console.error("Erreur lors de l'envoi du message de contact:", error);
          reject(error);
        },
      });
    });
  }

  // === UTILITAIRES ===
  scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs
        .toString()
        .padStart(2, '0')}`;
    }

    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  getFileAcceptTypes(): string {
    return '*/*';
  }

  closeAllMenus(): void {
    this.showEmojiPicker = false;
    this.showAttachmentMenu = false;
    this.showSearch = false;
  }

  toggleAttachmentMenu(): void {
    this.showAttachmentMenu = !this.showAttachmentMenu;
    this.showEmojiPicker = false;
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  // === GESTION DES ERREURS ===
  handleError(error: any, context: string): void {
    console.error(`Erreur dans ${context}:`, error);
    let message = "Une erreur inattendue s'est produite";

    if (error?.message) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }

    this.toastService.showError(message);
  }

  // === NETTOYAGE ===
  ngOnDestroy(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
    }

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
    }

    this.subscriptions.unsubscribe();
  }
}
