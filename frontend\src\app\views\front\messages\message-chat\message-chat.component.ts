import {
  Component,
  On<PERSON>ni<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hild,
  ElementRef,
  ChangeDetectorRef,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../../services/message.service';
import { ToastService } from '../../../../services/toast.service';

@Component({
  selector: 'app-message-chat',
  templateUrl: './message-chat.component.html',
})
export class MessageChatComponent implements OnInit, OnDestroy {
  // === RÉFÉRENCES DOM ===
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  @ViewChild('fileInput', { static: false })
  fileInput!: ElementRef<HTMLInputElement>;

  // === DONNÉES PRINCIPALES ===
  conversation: any = null;
  messages: any[] = [];
  currentUserId: string | null = null;
  currentUsername = 'You';
  messageForm: FormGroup;
  otherParticipant: any = null;

  // === ÉTATS DE L'INTERFACE ===
  isLoading = false;
  isLoadingMore = false;
  hasMoreMessages = true;
  showEmojiPicker = false;
  showAttachmentMenu = false;
  showSearch = false;
  searchQuery = '';
  searchResults: any[] = [];

  // === ENREGISTREMENT VOCAL ===
  isRecordingVoice = false;
  voiceRecordingDuration = 0;
  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private recordingTimer: any = null;

  // === APPELS ===
  isInCall = false;
  callType: 'VIDEO' | 'AUDIO' | null = null;
  callDuration = 0;
  private callTimer: any = null;

  // === ÉMOJIS ===
  emojiCategories: any[] = [
    {
      id: 'smileys',
      name: 'Smileys',
      icon: '😀',
      emojis: [
        { emoji: '😀', name: 'grinning face' },
        { emoji: '😃', name: 'grinning face with big eyes' },
        { emoji: '😄', name: 'grinning face with smiling eyes' },
        { emoji: '😁', name: 'beaming face with smiling eyes' },
        { emoji: '😆', name: 'grinning squinting face' },
        { emoji: '😅', name: 'grinning face with sweat' },
        { emoji: '😂', name: 'face with tears of joy' },
        { emoji: '🤣', name: 'rolling on the floor laughing' },
        { emoji: '😊', name: 'smiling face with smiling eyes' },
        { emoji: '😇', name: 'smiling face with halo' },
      ],
    },
    {
      id: 'people',
      name: 'People',
      icon: '👤',
      emojis: [
        { emoji: '👶', name: 'baby' },
        { emoji: '🧒', name: 'child' },
        { emoji: '👦', name: 'boy' },
        { emoji: '👧', name: 'girl' },
        { emoji: '🧑', name: 'person' },
        { emoji: '👨', name: 'man' },
        { emoji: '👩', name: 'woman' },
        { emoji: '👴', name: 'old man' },
        { emoji: '👵', name: 'old woman' },
      ],
    },
    {
      id: 'nature',
      name: 'Nature',
      icon: '🌿',
      emojis: [
        { emoji: '🐶', name: 'dog face' },
        { emoji: '🐱', name: 'cat face' },
        { emoji: '🐭', name: 'mouse face' },
        { emoji: '🐹', name: 'hamster' },
        { emoji: '🐰', name: 'rabbit face' },
        { emoji: '🦊', name: 'fox' },
        { emoji: '🐻', name: 'bear' },
        { emoji: '🐼', name: 'panda' },
      ],
    },
  ];
  selectedEmojiCategory = this.emojiCategories[0];

  // === PAGINATION ===
  private readonly MAX_MESSAGES_TO_LOAD = 10;
  private currentPage = 1;

  // === AUTRES ÉTATS ===
  isTyping = false;
  isUserTyping = false;
  searchMode = false;
  isSendingMessage = false;
  private typingTimeout: any = null;
  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private MessageService: MessageService,
    private toastService: ToastService,
    private cdr: ChangeDetectorRef
  ) {
    this.messageForm = this.fb.group({
      content: ['', [Validators.required, Validators.minLength(1)]],
    });
  }

  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    this.loadCurrentUser();
    this.loadConversation();
    this.setupSubscriptions();
  }

  private loadCurrentUser(): void {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user && user._id) {
        this.currentUserId = user._id;
        this.currentUsername = user.username || 'You';
      }
    } catch (error) {
      console.error("Erreur lors du chargement de l'utilisateur:", error);
      this.currentUserId = null;
      this.currentUsername = 'You';
    }
  }

  private loadConversation(): void {
    const conversationId = this.route.snapshot.paramMap.get('id');
    if (!conversationId) {
      this.toastService.showError('ID de conversation manquant');
      return;
    }

    this.isLoading = true;
    // Utiliser getConversation avec les paramètres de pagination
    this.MessageService.getConversation(
      conversationId,
      this.MAX_MESSAGES_TO_LOAD,
      this.currentPage
    ).subscribe({
      next: (conversation) => {
        this.conversation = conversation;
        this.setOtherParticipant();
        this.loadMessages();
      },
      error: (error) => {
        console.error('Erreur lors du chargement de la conversation:', error);
        this.toastService.showError(
          'Erreur lors du chargement de la conversation'
        );
        this.isLoading = false;
      },
    });
  }

  private loadMessages(): void {
    if (!this.conversation?.id) return;

    // Les messages sont déjà chargés avec la conversation
    this.messages = this.conversation.messages || [];
    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;
    this.isLoading = false;
    this.scrollToBottom();
  }

  private setOtherParticipant(): void {
    if (this.conversation?.participants) {
      this.otherParticipant = this.conversation.participants.find(
        (p: any) => (p.id || p._id) !== this.currentUserId
      );
    }
  }

  private setupSubscriptions(): void {
    // Abonnements GraphQL pour les mises à jour en temps réel
    // À implémenter selon vos besoins
  }

  // === ENVOI DE MESSAGES ===
  sendMessage(): void {
    if (!this.messageForm.valid || !this.conversation?.id) return;

    const content = this.messageForm.get('content')?.value?.trim();
    if (!content) return;

    const otherParticipant = this.conversation.participants?.find(
      (p: any) => (p.id || p._id) !== this.currentUserId
    );

    const receiverId = otherParticipant?.id || otherParticipant?._id;

    if (!receiverId) {
      this.toastService.showError('Destinataire introuvable');
      return;
    }

    this.MessageService.sendMessage(
      receiverId,
      content,
      undefined,
      'TEXT' as any,
      this.conversation.id
    ).subscribe({
      next: (message: any) => {
        this.messages.push(message);
        this.messageForm.reset();
        this.scrollToBottom();
      },
      error: (error: any) => {
        console.error("Erreur lors de l'envoi du message:", error);
        this.toastService.showError("Erreur lors de l'envoi du message");
      },
    });
  }

  // === GESTION DES FICHIERS ===
  onFileSelected(event: any): void {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    for (let file of files) {
      this.uploadFile(file);
    }
  }

  private uploadFile(file: File): void {
    const otherParticipant = this.conversation?.participants?.find(
      (p: any) => (p.id || p._id) !== this.currentUserId
    );

    const receiverId = otherParticipant?.id || otherParticipant?._id;

    if (!receiverId) {
      this.toastService.showError('Destinataire introuvable');
      return;
    }

    this.MessageService.sendMessage(
      receiverId,
      '',
      file,
      undefined,
      this.conversation.id
    ).subscribe({
      next: (message: any) => {
        this.messages.push(message);
        this.scrollToBottom();
        this.toastService.showSuccess('Fichier envoyé avec succès');
      },
      error: (error: any) => {
        console.error("Erreur lors de l'envoi du fichier:", error);
        this.toastService.showError("Erreur lors de l'envoi du fichier");
      },
    });
  }

  // === ENREGISTREMENT VOCAL ===
  async startVoiceRecording(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      this.audioChunks = [];
      this.isRecordingVoice = true;
      this.voiceRecordingDuration = 0;
      this.voiceRecordingState = 'recording';

      this.recordingTimer = setInterval(() => {
        this.voiceRecordingDuration++;
        this.cdr.detectChanges();
      }, 1000);

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.processRecordedAudio();
      };

      this.mediaRecorder.start(100);
      this.toastService.showSuccess('Enregistrement vocal démarré');
    } catch (error) {
      console.error("Erreur lors du démarrage de l'enregistrement:", error);
      this.toastService.showError("Impossible d'accéder au microphone");
      this.cancelVoiceRecording();
    }
  }

  stopVoiceRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
    }

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }

    this.isRecordingVoice = false;
    this.voiceRecordingState = 'processing';
  }

  cancelVoiceRecording(): void {
    if (this.mediaRecorder) {
      if (this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop();
      }
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
      this.mediaRecorder = null;
    }

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }

    this.isRecordingVoice = false;
    this.voiceRecordingDuration = 0;
    this.voiceRecordingState = 'idle';
    this.audioChunks = [];
  }

  private async processRecordedAudio(): Promise<void> {
    try {
      if (this.audioChunks.length === 0) {
        this.toastService.showWarning('Aucun audio enregistré');
        this.cancelVoiceRecording();
        return;
      }

      const audioBlob = new Blob(this.audioChunks, {
        type: 'audio/webm;codecs=opus',
      });

      if (this.voiceRecordingDuration < 1) {
        this.toastService.showWarning(
          'Enregistrement trop court (minimum 1 seconde)'
        );
        this.cancelVoiceRecording();
        return;
      }

      const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {
        type: 'audio/webm;codecs=opus',
      });

      await this.sendVoiceMessage(audioFile);
      this.toastService.showSuccess('Message vocal envoyé');
    } catch (error) {
      console.error("Erreur lors du traitement de l'audio:", error);
      this.toastService.showError("Erreur lors de l'envoi du message vocal");
    } finally {
      this.voiceRecordingState = 'idle';
      this.voiceRecordingDuration = 0;
      this.audioChunks = [];
    }
  }

  private async sendVoiceMessage(audioFile: File): Promise<void> {
    const otherParticipant = this.conversation?.participants?.find(
      (p: any) => (p.id || p._id) !== this.currentUserId
    );

    const receiverId = otherParticipant?.id || otherParticipant?._id;

    if (!receiverId) {
      throw new Error('Destinataire introuvable');
    }

    return new Promise((resolve, reject) => {
      this.MessageService.sendMessage(
        receiverId,
        '',
        audioFile,
        undefined,
        this.conversation.id
      ).subscribe({
        next: (message: any) => {
          this.messages.push(message);
          this.scrollToBottom();
          resolve();
        },
        error: (error: any) => {
          console.error("Erreur lors de l'envoi du message vocal:", error);
          reject(error);
        },
      });
    });
  }

  formatRecordingDuration(duration: number): string {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  // === SYSTÈME D'ÉMOJIS ===
  getEmojisForCategory(category: any): any[] {
    return category?.emojis || [];
  }

  selectEmojiCategory(category: any): void {
    this.selectedEmojiCategory = category;
  }

  insertEmoji(emoji: any): void {
    const currentContent = this.messageForm.get('content')?.value || '';
    const newContent = currentContent + (emoji.emoji || emoji);
    this.messageForm.patchValue({ content: newContent });

    this.showEmojiPicker = false;

    setTimeout(() => {
      const textarea = document.querySelector(
        'textarea[formControlName="content"]'
      ) as HTMLTextAreaElement;
      if (textarea) {
        textarea.focus();
      }
    }, 100);
  }

  toggleEmojiPicker(): void {
    this.showEmojiPicker = !this.showEmojiPicker;
    this.showAttachmentMenu = false;
  }

  // === APPELS VIDÉO/AUDIO ===
  startCall(type: 'VIDEO' | 'AUDIO'): void {
    this.callType = type;
    this.isInCall = true;
    this.callDuration = 0;

    this.callTimer = setInterval(() => {
      this.callDuration++;
      this.cdr.detectChanges();
    }, 1000);

    this.toastService.showSuccess(
      `Appel ${type === 'VIDEO' ? 'vidéo' : 'audio'} démarré`
    );
  }

  endCall(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }

    this.isInCall = false;
    this.callType = null;
    this.callDuration = 0;
    this.toastService.showSuccess('Appel terminé');
  }

  startVideoCall(): void {
    this.startCall('VIDEO');
  }

  startVoiceCall(): void {
    this.startCall('AUDIO');
  }

  // === RECHERCHE ===
  toggleSearch(): void {
    this.showSearch = !this.showSearch;
    if (!this.showSearch) {
      this.searchQuery = '';
      this.searchResults = [];
    }
  }

  searchMessages(): void {
    if (!this.searchQuery.trim()) {
      this.searchResults = [];
      return;
    }

    this.searchResults = this.messages.filter(
      (message) =>
        message.content
          ?.toLowerCase()
          .includes(this.searchQuery.toLowerCase()) ||
        message.sender?.username
          ?.toLowerCase()
          .includes(this.searchQuery.toLowerCase())
    );
  }

  // === PAGINATION ===
  loadMoreMessages(): void {
    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)
      return;

    this.isLoadingMore = true;
    this.currentPage++;

    this.MessageService.getConversation(
      this.conversation.id,
      this.MAX_MESSAGES_TO_LOAD,
      this.currentPage
    ).subscribe({
      next: (conversation) => {
        const newMessages = conversation.messages || [];
        if (newMessages.length > 0) {
          // Ajouter les nouveaux messages au début (messages plus anciens)
          this.messages = [...newMessages, ...this.messages];
          this.hasMoreMessages =
            newMessages.length === this.MAX_MESSAGES_TO_LOAD;
        } else {
          this.hasMoreMessages = false;
        }
        this.isLoadingMore = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des messages:', error);
        this.isLoadingMore = false;
        this.currentPage--; // Revenir à la page précédente en cas d'erreur
      },
    });
  }

  // === GÉOLOCALISATION ===
  async shareLocation(): Promise<void> {
    try {
      const position = await this.getCurrentPosition();
      const { latitude, longitude } = position.coords;

      const locationMessage = {
        type: 'location',
        latitude,
        longitude,
        address: await this.getAddressFromCoordinates(latitude, longitude),
        mapUrl: `https://maps.google.com/maps?q=${latitude},${longitude}&z=15`,
        timestamp: new Date(),
      };

      await this.sendLocationMessage(locationMessage);
      this.toastService.showSuccess('Position partagée avec succès');
    } catch (error) {
      console.error('Erreur lors du partage de localisation:', error);
      this.toastService.showError("Impossible d'obtenir votre position");
    }
  }

  private getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      });
    });
  }

  private async getAddressFromCoordinates(
    lat: number,
    lng: number
  ): Promise<string> {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
      );

      if (response.ok) {
        const data = await response.json();
        return data.display_name || `${lat}, ${lng}`;
      }
    } catch (error) {
      console.error('Erreur lors du géocodage inverse:', error);
    }

    return `${lat}, ${lng}`;
  }

  private async sendLocationMessage(locationData: any): Promise<void> {
    const otherParticipant = this.conversation?.participants?.find(
      (p: any) => (p.id || p._id) !== this.currentUserId
    );

    const receiverId = otherParticipant?.id || otherParticipant?._id;

    if (!receiverId) {
      throw new Error('Destinataire introuvable');
    }

    return new Promise((resolve, reject) => {
      this.MessageService.sendMessage(
        receiverId,
        `📍 Position partagée: ${locationData.address}`,
        undefined,
        'TEXT' as any,
        this.conversation.id
      ).subscribe({
        next: (message: any) => {
          this.messages.push(message);
          this.scrollToBottom();
          resolve();
        },
        error: (error: any) => {
          console.error(
            "Erreur lors de l'envoi du message de localisation:",
            error
          );
          reject(error);
        },
      });
    });
  }

  // === PARTAGE DE CONTACTS ===
  async shareContact(): Promise<void> {
    try {
      const contact = await this.selectContact();

      if (contact) {
        await this.sendContactMessage(contact);
        this.toastService.showSuccess('Contact partagé avec succès');
      }
    } catch (error) {
      console.error('Erreur lors du partage de contact:', error);
      this.toastService.showError('Erreur lors du partage du contact');
    }
  }

  private async selectContact(): Promise<any> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          name: 'John Doe',
          phone: '+33 6 12 34 56 78',
          email: '<EMAIL>',
          avatar: 'assets/images/default-avatar.png',
        });
      }, 1000);
    });
  }

  private async sendContactMessage(contactData: any): Promise<void> {
    const otherParticipant = this.conversation?.participants?.find(
      (p: any) => (p.id || p._id) !== this.currentUserId
    );

    const receiverId = otherParticipant?.id || otherParticipant?._id;

    if (!receiverId) {
      throw new Error('Destinataire introuvable');
    }

    return new Promise((resolve, reject) => {
      this.MessageService.sendMessage(
        receiverId,
        `👤 Contact partagé: ${contactData.name}\n📞 ${contactData.phone}\n📧 ${contactData.email}`,
        undefined,
        'TEXT' as any,
        this.conversation.id
      ).subscribe({
        next: (message: any) => {
          this.messages.push(message);
          this.scrollToBottom();
          resolve();
        },
        error: (error: any) => {
          console.error("Erreur lors de l'envoi du message de contact:", error);
          reject(error);
        },
      });
    });
  }

  // === UTILITAIRES ===
  scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs
        .toString()
        .padStart(2, '0')}`;
    }

    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  getFileAcceptTypes(): string {
    return '*/*';
  }

  closeAllMenus(): void {
    this.showEmojiPicker = false;
    this.showAttachmentMenu = false;
    this.showSearch = false;
  }

  toggleAttachmentMenu(): void {
    this.showAttachmentMenu = !this.showAttachmentMenu;
    this.showEmojiPicker = false;
  }

  triggerFileInput(type?: string): void {
    // Optionnel : configurer le type de fichier accepté selon le type
    if (type === 'image') {
      this.fileInput.nativeElement.accept = 'image/*';
    } else if (type === 'video') {
      this.fileInput.nativeElement.accept = 'video/*';
    } else if (type === 'document') {
      this.fileInput.nativeElement.accept =
        '.pdf,.doc,.docx,.txt,.xlsx,.ppt,.pptx';
    } else {
      this.fileInput.nativeElement.accept = '*/*';
    }

    this.fileInput.nativeElement.click();
  }

  // === MÉTHODES POUR LE TEMPLATE ===
  goBackToConversations(): void {
    // Navigation vers la liste des conversations
    window.history.back();
  }

  formatLastActive(lastActive: any): string {
    if (!lastActive) return 'Hors ligne';
    const now = new Date();
    const lastActiveDate = new Date(lastActive);
    const diffInMinutes = Math.floor(
      (now.getTime() - lastActiveDate.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return 'En ligne';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;
    if (diffInMinutes < 1440)
      return `Il y a ${Math.floor(diffInMinutes / 60)} h`;
    return `Il y a ${Math.floor(diffInMinutes / 1440)} j`;
  }

  toggleMainMenu(): void {
    // Toggle du menu principal
    this.showAttachmentMenu = false;
    this.showEmojiPicker = false;
  }

  onScroll(event: any): void {
    const element = event.target;
    if (
      element.scrollTop === 0 &&
      this.hasMoreMessages &&
      !this.isLoadingMore
    ) {
      this.loadMoreMessages();
    }
  }

  trackByMessageId(index: number, message: any): any {
    return message.id || index;
  }

  shouldShowDateSeparator(index: number): boolean {
    if (index === 0) return true;
    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];

    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;

    const currentDate = new Date(currentMessage.timestamp).toDateString();
    const previousDate = new Date(previousMessage.timestamp).toDateString();

    return currentDate !== previousDate;
  }

  formatDateSeparator(timestamp: any): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) return "Aujourd'hui";
    if (date.toDateString() === yesterday.toDateString()) return 'Hier';

    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  onMessageClick(message: any, event: any): void {
    // Gestion du clic sur un message
    console.log('Message clicked:', message);
  }

  onMessageContextMenu(message: any, event: any): void {
    event.preventDefault();
    // Gestion du menu contextuel
    console.log('Message context menu:', message);
  }

  shouldShowAvatar(index: number): boolean {
    if (index === this.messages.length - 1) return true;
    const currentMessage = this.messages[index];
    const nextMessage = this.messages[index + 1];

    return !nextMessage || currentMessage.sender?.id !== nextMessage.sender?.id;
  }

  openUserProfile(userId: string): void {
    // Navigation vers le profil utilisateur
    console.log('Open user profile:', userId);
  }

  isGroupConversation(): boolean {
    return this.conversation?.participants?.length > 2;
  }

  shouldShowSenderName(index: number): boolean {
    if (index === 0) return true;
    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];

    return currentMessage.sender?.id !== previousMessage.sender?.id;
  }

  getUserColor(userId: string): string {
    // Génère une couleur basée sur l'ID utilisateur
    const colors = [
      '#FF6B6B',
      '#4ECDC4',
      '#45B7D1',
      '#96CEB4',
      '#FFEAA7',
      '#DDA0DD',
    ];
    const hash = userId.split('').reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    return colors[Math.abs(hash) % colors.length];
  }

  getMessageType(message: any): string {
    if (message.file) {
      if (message.file.type?.startsWith('image/')) return 'image';
      if (message.file.type?.startsWith('video/')) return 'video';
      if (message.file.type?.startsWith('audio/')) return 'audio';
      return 'file';
    }
    return 'text';
  }

  formatMessageContent(content: string): string {
    if (!content) return '';
    // Formatage basique du contenu (liens, emojis, etc.)
    return content.replace(/\n/g, '<br>');
  }

  hasImage(message: any): boolean {
    return message.file && message.file.type?.startsWith('image/');
  }

  openImageViewer(message: any): void {
    // Ouvre la visionneuse d'images
    console.log('Open image viewer:', message);
  }

  getImageUrl(message: any): string {
    return message.file?.url || message.file?.path || '';
  }

  hasFile(message: any): boolean {
    return message.file && !message.file.type?.startsWith('image/');
  }

  downloadFile(message: any): void {
    if (message.file?.url) {
      const link = document.createElement('a');
      link.href = message.file.url;
      link.download = message.file.name || 'file';
      link.click();
    }
  }

  getFileIcon(message: any): string {
    if (!message.file?.type) return 'fas fa-file';

    if (message.file.type.startsWith('video/')) return 'fas fa-video';
    if (message.file.type.startsWith('audio/')) return 'fas fa-music';
    if (message.file.type.includes('pdf')) return 'fas fa-file-pdf';
    if (message.file.type.includes('word')) return 'fas fa-file-word';
    if (message.file.type.includes('excel')) return 'fas fa-file-excel';

    return 'fas fa-file';
  }

  getFileName(message: any): string {
    return message.file?.name || 'Fichier';
  }

  getFileSize(message: any): string {
    if (!message.file?.size) return '';
    return this.formatFileSize(message.file.size);
  }

  formatMessageTime(timestamp: any): string {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  // === MÉTHODES D'INTERACTION ===
  toggleReaction(messageId: string, emoji: string): void {
    // Gestion des réactions aux messages
    console.log('Toggle reaction:', messageId, emoji);
  }

  hasUserReacted(reaction: any, userId: string): boolean {
    return reaction.users?.includes(userId) || false;
  }

  onInputChange(event: any): void {
    // Gestion du changement de texte dans l'input
    const value = event.target.value;
    if (value.trim() && !this.isTyping) {
      this.isTyping = true;
      // Envoyer signal de frappe
    } else if (!value.trim() && this.isTyping) {
      this.isTyping = false;
      // Arrêter signal de frappe
    }
  }

  onInputKeyDown(event: any): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  onInputFocus(): void {
    // Marquer les messages comme lus
    this.markMessagesAsRead();
  }

  onInputBlur(): void {
    // Arrêter le signal de frappe
    this.isTyping = false;
  }

  private markMessagesAsRead(): void {
    // Marquer tous les messages non lus comme lus
    console.log('Mark messages as read');
  }

  openCamera(): void {
    // Ouvrir la caméra pour prendre une photo
    console.log('Open camera');
  }

  // === GESTION DES ERREURS ===
  handleError(error: any, context: string): void {
    console.error(`Erreur dans ${context}:`, error);
    let message = "Une erreur inattendue s'est produite";

    if (error?.message) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }

    this.toastService.showError(message);
  }

  // === NETTOYAGE ===
  ngOnDestroy(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
    }

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
    }

    this.subscriptions.unsubscribe();
  }
}
