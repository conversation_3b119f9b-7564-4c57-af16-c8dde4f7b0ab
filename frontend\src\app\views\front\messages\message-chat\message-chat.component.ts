import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ElementRef,
  AfterViewChecked,
  ChangeDetectorRef,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthuserService } from 'src/app/services/authuser.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { User } from '@app/models/user.model';
import { UserStatusService } from 'src/app/services/user-status.service';
import {
  Message,
  Conversation,
  MessageType,
  CallType,
} from 'src/app/models/message.model';
import { ToastService } from 'src/app/services/toast.service';
import { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';
import { MessageService } from '@app/services/message.service';

@Component({
  selector: 'app-message-chat',
  templateUrl: 'message-chat.component.html',
  styleUrls: ['./message-chat.component.css'],
})
export class MessageChatComponent
  implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, AfterViewChecked
{
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  @ViewChild('fileInput', { static: false })
  fileInput!: ElementRef<HTMLInputElement>;

  messages: Message[] = [];
  messageForm: FormGroup;
  conversation: Conversation | null = null;
  loading = true;
  error: any;
  currentUserId: string | null = null;
  currentUsername: string = 'You';
  otherParticipant: User | null = null;
  selectedFile: File | null = null;
  previewUrl: string | ArrayBuffer | null = null;
  isUploading = false;
  isTyping = false;
  typingTimeout: any;
  isRecordingVoice = false;
  voiceRecordingDuration = 0;

  private readonly MAX_MESSAGES_PER_SIDE = 5;
  private readonly MAX_MESSAGES_TO_LOAD = 10;
  private readonly MAX_TOTAL_MESSAGES = 100;
  private currentPage = 1;
  isLoadingMore = false;
  hasMoreMessages = true;
  private subscriptions: Subscription = new Subscription();

  // Interface
  selectedTheme: string = 'theme-default';

  // États
  showThemeSelector = false;
  showMainMenu = false;
  showEmojiPicker = false;
  showSearchBar = false;
  showPinnedMessages = false;
  showStatusSelector = false;
  showNotificationPanel = false;
  showNotificationSettings = false;
  showUserStatusPanel = false;
  showCallHistoryPanel = false;
  showCallStatsPanel = false;
  showVoiceMessagesPanel = false;

  // Appels
  incomingCall: any = null;
  activeCall: any = null;
  showCallModal = false;
  showActiveCallModal = false;
  isCallMuted = false;
  isVideoEnabled = true;
  callDuration = 0;
  callTimer: any = null;

  // Notifications et messages
  notifications: any[] = [];
  unreadNotificationCount: number = 0;
  selectedNotifications: Set<string> = new Set();
  showDeleteConfirmModal: boolean = false;
  editingMessageId: string | null = null;
  editingContent = '';
  replyingToMessage: any = null;

  // Recherche
  searchQuery = '';
  isSearching = false;
  searchMode = false;
  searchResults: any[] = [];

  // Panneaux
  pinnedMessages: any[] = [];
  showReactionPicker: { [key: string]: boolean } = {};
  showDeleteConfirm: { [key: string]: boolean } = {};
  showPinConfirm: { [key: string]: boolean } = {};
  isPinning: { [key: string]: boolean } = {};
  showMessageOptions: { [key: string]: boolean } = {};

  // Variables de transfert
  showForwardModal = false;
  forwardingMessage: any = null;
  selectedConversations: string[] = [];
  availableConversations: any[] = [];
  isForwarding = false;
  isLoadingConversations = false;

  // Constantes consolidées
  readonly constants = {
    reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],
    autoAwayDelay: 300000, // 5 minutes
    typingTimeout: 3000, // 3 secondes
    scrollDelay: 100, // 100ms
    animationDelay: 300, // 300ms
    errorMessages: {
      callInitiate: "Impossible d'initier l'appel. Veuillez réessayer.",
      callAccept: "Impossible d'accepter l'appel. Veuillez réessayer.",
      callGeneral: "Erreur lors de la gestion de l'appel.",
    },
    trackById: (index: number, item: any): string =>
      item?.id || index.toString(),
    notificationConfig: {
      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },
      FRIEND_REQUEST: { icon: 'fas fa-user-plus', color: 'text-green-500' },
      GROUP_INVITATION: { icon: 'fas fa-users', color: 'text-purple-500' },
      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },
      CALL_INCOMING: { icon: 'fas fa-phone', color: 'text-yellow-500' },
      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },
    },
    statusConfig: {
      online: {
        text: 'En ligne',
        color: 'text-green-500',
        icon: 'fas fa-circle',
        label: 'En ligne',
        description: 'Disponible pour discuter',
      },
      offline: {
        text: 'Hors ligne',
        color: 'text-gray-500',
        icon: 'far fa-circle',
        label: 'Hors ligne',
        description: 'Invisible pour tous',
      },
      away: {
        text: 'Absent',
        color: 'text-yellow-500',
        icon: 'fas fa-clock',
        label: 'Absent',
        description: 'Absent temporairement',
      },
      busy: {
        text: 'Occupé',
        color: 'text-red-500',
        icon: 'fas fa-minus-circle',
        label: 'Occupé',
        description: 'Ne pas déranger',
      },
    },
    themeConfig: [
      {
        key: 'theme-default',
        label: 'Par défaut',
        color: '[#4f5fad]',
        hoverColor: '[#4f5fad]',
      },
      {
        key: 'theme-feminine',
        label: 'Rose',
        color: '[#ff6b9d]',
        hoverColor: '[#ff6b9d]',
      },
      {
        key: 'theme-masculine',
        label: 'Bleu',
        color: '[#3d85c6]',
        hoverColor: '[#3d85c6]',
      },
      {
        key: 'theme-neutral',
        label: 'Vert',
        color: '[#6aa84f]',
        hoverColor: '[#6aa84f]',
      },
    ],
    callStatusColors: {
      COMPLETED: 'text-green-500',
      MISSED: 'text-red-500',
      REJECTED: 'text-orange-500',
    },
  };

  // Getter pour compatibilité
  get availableReactions(): string[] {
    return this.constants.reactions;
  }

  // Variables de notification
  notificationFilter = 'all';
  isLoadingNotifications = false;
  isMarkingAsRead = false;
  isDeletingNotifications = false;
  hasMoreNotifications = false;
  notificationSounds = true;
  notificationPreview = true;
  autoMarkAsRead = true;

  // Variables d'appel
  isCallMinimized = false;
  callQuality = 'connecting';
  showCallControls = false;

  // Variables de statut utilisateur
  onlineUsers: Map<string, User> = new Map();
  currentUserStatus: string = 'online';
  lastActivityTime: Date = new Date();
  autoAwayTimeout: any = null;
  isUpdatingStatus = false;
  callHistory: any[] = [];
  voiceMessages: any[] = [];
  localVideoElement: HTMLVideoElement | null = null;
  remoteVideoElement: HTMLVideoElement | null = null;
  statusFilterType = 'all';

  // Emojis du service
  get commonEmojis(): string[] {
    return this.MessageService.getCommonEmojis();
  }

  // Configuration des boutons d'action de l'en-tête
  getHeaderActions() {
    return [
      {
        class: 'btn-audio-call',
        icon: 'fas fa-phone-alt',
        title: 'Appel audio',
        onClick: () => this.initiateCall('AUDIO'),
        isActive: false,
      },
      {
        class: 'btn-video-call',
        icon: 'fas fa-video',
        title: 'Appel vidéo',
        onClick: () => this.initiateCall('VIDEO'),
        isActive: false,
      },
      {
        class: 'btn-search',
        icon: 'fas fa-search',
        title: 'Rechercher',
        onClick: () => this.toggleSearchBar(),
        isActive: this.showSearchBar,
        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },
      },
      {
        class: 'btn-pinned relative',
        icon: 'fas fa-thumbtack',
        title: `Messages épinglés (${this.getPinnedMessagesCount()})`,
        onClick: () => this.togglePinnedMessages(),
        isActive: this.showPinnedMessages,
        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },
        badge:
          this.getPinnedMessagesCount() > 0
            ? {
                count: this.getPinnedMessagesCount(),
                class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',
                animate: false,
              }
            : null,
      },
      {
        class: 'btn-notifications relative',
        icon: 'fas fa-bell',
        title: 'Notifications',
        onClick: () => this.toggleNotificationPanel(),
        isActive: this.showNotificationPanel,
        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },
        badge:
          this.unreadNotificationCount > 0
            ? {
                count: this.unreadNotificationCount,
                class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',
                animate: true,
              }
            : null,
      },
      {
        class: 'btn-history relative',
        icon: 'fas fa-history',
        title: 'Historique des appels',
        onClick: () => this.toggleCallHistoryPanel(),
        isActive: this.showCallHistoryPanel,
        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },
      },
      {
        class: 'btn-stats relative',
        icon: 'fas fa-chart-bar',
        title: "Statistiques d'appels",
        onClick: () => this.toggleCallStatsPanel(),
        isActive: this.showCallStatsPanel,
        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },
      },
      {
        class: 'btn-voice-messages relative',
        icon: 'fas fa-microphone',
        title: 'Messages vocaux',
        onClick: () => this.toggleVoiceMessagesPanel(),
        isActive: this.showVoiceMessagesPanel,
        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },
        badge:
          this.voiceMessages.length > 0
            ? {
                count: this.voiceMessages.length,
                class: 'bg-[#4f5fad]',
                animate: false,
              }
            : null,
      },
    ];
  }

  constructor(
    private MessageService: MessageService,
    public route: ActivatedRoute,
    private authService: AuthuserService,
    private fb: FormBuilder,
    public statusService: UserStatusService,
    public router: Router,
    private toastService: ToastService,

    private cdr: ChangeDetectorRef
  ) {
    this.messageForm = this.fb.group({
      content: ['', [Validators.maxLength(1000)]],
    });
  }
  ngOnInit(): void {
    this.currentUserId = this.authService.getCurrentUserId();

    const savedTheme = localStorage.getItem('chat-theme');
    if (savedTheme) {
      this.selectedTheme = savedTheme;
    }

    this.subscribeToNotifications();
    this.subscribeToUserStatus();
    this.initializeUserStatus();
    this.startActivityTracking();

    document.addEventListener('click', this.onDocumentClick.bind(this));

    const routeSub = this.route.params
      .pipe(
        filter((params) => params['id']),
        distinctUntilChanged(),
        switchMap((params) => {
          this.loading = true;
          this.messages = [];
          this.currentPage = 1;
          this.hasMoreMessages = true;

          return this.MessageService.getConversation(
            params['id'],
            this.MAX_MESSAGES_TO_LOAD,
            this.currentPage
          );
        })
      )
      .subscribe({
        next: (conversation) => {
          this.handleConversationLoaded(conversation);
        },
        error: (error) => {
          this.handleError('Failed to load conversation', error);
        },
      });
    this.subscriptions.add(routeSub);
  }

  // Gestion centralisée des erreurs
  private handleError(
    message: string,
    error: any,
    resetLoading: boolean = true
  ): void {
    console.error('MessageChat', message, error);
    if (resetLoading) {
      this.loading = false;
      this.isUploading = false;
      this.isLoadingMore = false;
    }
    this.error = error;
    this.toastService.showError(message);
  }

  // Gestion centralisée des succès
  private handleSuccess(message?: string, callback?: () => void): void {
    if (message) {
      this.toastService.showSuccess(message);
    }
    if (callback) {
      callback();
    }
  }

  // Services de fichiers consolidés
  readonly fileServiceMethods = {
    getFileIcon: (mimeType?: string) =>
      this.MessageService.getFileIcon(mimeType),
    getFileType: (mimeType?: string) =>
      this.MessageService.getFileType(mimeType),
  };

  getFileIcon = this.fileServiceMethods.getFileIcon;
  getFileType = this.fileServiceMethods.getFileType;

  private handleConversationLoaded(conversation: Conversation): void {
    this.conversation = conversation;

    if (!conversation?.messages || conversation.messages.length === 0) {
      this.otherParticipant =
        conversation?.participants?.find(
          (p) => p.id !== this.currentUserId && p._id !== this.currentUserId
        ) || null;
      this.messages = [];
    } else {
      const conversationMessages = [...(conversation?.messages || [])];

      conversationMessages.sort((a, b) => {
        const timeA =
          a.timestamp instanceof Date
            ? a.timestamp.getTime()
            : new Date(a.timestamp as string).getTime();
        const timeB =
          b.timestamp instanceof Date
            ? b.timestamp.getTime()
            : new Date(b.timestamp as string).getTime();
        return timeA - timeB;
      });

      this.messages = conversationMessages;
    }

    this.otherParticipant =
      conversation?.participants?.find(
        (p) => p.id !== this.currentUserId && p._id !== this.currentUserId
      ) || null;

    this.loading = false;
    setTimeout(() => this.scrollToBottom(), 100);

    this.markMessagesAsRead();

    if (this.conversation?.id) {
      this.subscribeToConversationUpdates(this.conversation.id);
      this.subscribeToNewMessages(this.conversation.id);
      this.subscribeToTypingIndicators(this.conversation.id);
    }
  }

  private subscribeToConversationUpdates(conversationId: string): void {
    const sub = this.MessageService.subscribeToConversationUpdates(
      conversationId
    ).subscribe({
      next: (updatedConversation) => {
        this.conversation = updatedConversation;
        this.messages = updatedConversation.messages
          ? [...updatedConversation.messages]
          : [];
        this.scrollToBottom();
      },
      error: (error) => {
        this.toastService.showError('Connection to conversation updates lost');
      },
    });
    this.subscriptions.add(sub);
  }

  private subscribeToNewMessages(conversationId: string): void {
    const sub = this.MessageService.subscribeToNewMessages(
      conversationId
    ).subscribe({
      next: (newMessage) => {
        if (newMessage?.conversationId === this.conversation?.id) {
          this.messages = [...this.messages, newMessage].sort((a, b) => {
            const timeA =
              a.timestamp instanceof Date
                ? a.timestamp.getTime()
                : new Date(a.timestamp as string).getTime();
            const timeB =
              b.timestamp instanceof Date
                ? b.timestamp.getTime()
                : new Date(b.timestamp as string).getTime();
            return timeA - timeB;
          });

          setTimeout(() => this.scrollToBottom(), 100);

          if (
            newMessage.sender?.id !== this.currentUserId &&
            newMessage.sender?._id !== this.currentUserId
          ) {
            if (newMessage.id) {
              this.MessageService.markMessageAsRead(newMessage.id).subscribe();
            }
          }
        }
      },
      error: (error) => {
        this.toastService.showError('Connection to new messages lost');
      },
    });
    this.subscriptions.add(sub);
  }

  private subscribeToTypingIndicators(conversationId: string): void {
    const sub = this.MessageService.subscribeToTypingIndicator(
      conversationId
    ).subscribe({
      next: (event) => {
        if (event.userId !== this.currentUserId) {
          this.isTyping = event.isTyping;
          if (this.isTyping) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = setTimeout(() => {
              this.isTyping = false;
            }, 2000);
          }
        }
      },
    });
    this.subscriptions.add(sub);
  }

  private markMessagesAsRead(): void {
    const unreadMessages = this.messages.filter(
      (msg) =>
        !msg.isRead &&
        (msg.receiver?.id === this.currentUserId ||
          msg.receiver?._id === this.currentUserId)
    );

    unreadMessages.forEach((msg) => {
      if (msg.id) {
        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({
          error: (error) => {},
        });
        this.subscriptions.add(sub);
      }
    });
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    if (file.size > 5 * 1024 * 1024) {
      this.toastService.showError('File size should be less than 5MB');
      return;
    }

    const validTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    if (!validTypes.includes(file.type)) {
      this.toastService.showError(
        'Invalid file type. Only images, PDFs and Word docs are allowed'
      );
      return;
    }

    this.selectedFile = file;
    const reader = new FileReader();
    reader.onload = () => {
      this.previewUrl = reader.result;
    };
    reader.readAsDataURL(file);
  }

  removeAttachment(): void {
    this.selectedFile = null;
    this.previewUrl = null;
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.value = '';
    }
  }

  private typingTimer: any;
  private isCurrentlyTyping = false;
  private readonly TYPING_DELAY = 500;
  private readonly TYPING_TIMEOUT = 3000;

  // Frappe
  onTyping(): void {
    if (!this.conversation?.id || !this.currentUserId) {
      return;
    }

    const conversationId = this.conversation.id;
    clearTimeout(this.typingTimer);

    if (!this.isCurrentlyTyping) {
      this.isCurrentlyTyping = true;
      this.MessageService.startTyping(conversationId).subscribe({
        next: () => {},
        error: (error) => {},
      });
    }

    this.typingTimer = setTimeout(() => {
      if (this.isCurrentlyTyping) {
        this.isCurrentlyTyping = false;
        this.MessageService.stopTyping(conversationId).subscribe({
          next: () => {},
          error: (error) => {},
        });
      }
    }, this.TYPING_TIMEOUT);
  }

  // Panneaux
  private togglePanel(panelName: string, closeOthers: boolean = true): void {
    const panels = {
      theme: 'showThemeSelector',
      menu: 'showMainMenu',
      emoji: 'showEmojiPicker',
      notification: 'showNotificationPanel',
      search: 'showSearchBar',
      status: 'showStatusSelector',
    };

    const currentPanel = panels[panelName as keyof typeof panels];
    if (currentPanel) {
      (this as any)[currentPanel] = !(this as any)[currentPanel];

      if (closeOthers && (this as any)[currentPanel]) {
        Object.values(panels).forEach((panel) => {
          if (panel !== currentPanel) {
            (this as any)[panel] = false;
          }
        });
      }
    }
  }

  // Toggle
  toggleThemeSelector(): void {
    this.togglePanel('theme');
  }

  toggleMainMenu(): void {
    this.togglePanel('menu');
  }

  toggleEmojiPicker(): void {
    this.togglePanel('emoji');
  }

  changeTheme(theme: string): void {
    this.selectedTheme = theme;
    this.showThemeSelector = false;
    localStorage.setItem('chat-theme', theme);
  }

  // Méthodes toggle consolidées
  private readonly toggleMethods = {
    pinnedMessages: () => (this.showPinnedMessages = !this.showPinnedMessages),
    searchBar: () => {
      this.togglePanel('search');
      if (!this.showSearchBar) this.clearSearch();
    },
    statusSelector: () => this.togglePanel('status'),
    notificationSettings: () =>
      (this.showNotificationSettings = !this.showNotificationSettings),
    userStatusPanel: () =>
      (this.showUserStatusPanel = !this.showUserStatusPanel),
    callMinimize: () => (this.isCallMinimized = !this.isCallMinimized),
    callHistoryPanel: () =>
      (this.showCallHistoryPanel = !this.showCallHistoryPanel),
    callStatsPanel: () => (this.showCallStatsPanel = !this.showCallStatsPanel),
    voiceMessagesPanel: () =>
      (this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel),
  };

  togglePinnedMessages = this.toggleMethods.pinnedMessages;
  toggleSearchBar = this.toggleMethods.searchBar;
  toggleStatusSelector = this.toggleMethods.statusSelector;
  toggleNotificationSettings = this.toggleMethods.notificationSettings;
  toggleUserStatusPanel = this.toggleMethods.userStatusPanel;
  toggleCallMinimize = this.toggleMethods.callMinimize;
  toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;
  toggleCallStatsPanel = this.toggleMethods.callStatsPanel;
  toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;

  // Conversation - méthode utilitaire
  private showDevelopmentFeature(featureName: string): void {
    this.showMainMenu = false;
    this.toastService.showInfo(`${featureName} en cours de développement`);
  }

  readonly conversationMethods = {
    showInfo: () => this.showDevelopmentFeature('Informations de conversation'),
    showSettings: () =>
      this.showDevelopmentFeature('Paramètres de conversation'),
    clear: (): void => {
      if (!this.conversation?.id || this.messages.length === 0) {
        this.toastService.showWarning('Aucune conversation à vider');
        return;
      }

      if (
        confirm(
          'Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.'
        )
      ) {
        this.messages = [];
        this.showMainMenu = false;
        this.toastService.showSuccess('Conversation vidée avec succès');
      }
    },
    export: (): void => {
      if (!this.conversation?.id || this.messages.length === 0) {
        this.toastService.showWarning('Aucune conversation à exporter');
        return;
      }

      const conversationName = this.conversation.isGroup
        ? this.conversation.groupName || 'Groupe sans nom'
        : this.otherParticipant?.username || 'Conversation privée';

      const exportData = {
        conversation: {
          id: this.conversation.id,
          name: conversationName,
          isGroup: this.conversation.isGroup,
          participants: this.conversation.participants,
          createdAt: this.conversation.createdAt,
        },
        messages: this.messages.map((msg) => ({
          id: msg.id,
          content: msg.content,
          sender: msg.sender,
          timestamp: msg.timestamp,
          type: msg.type,
        })),
        exportedAt: new Date().toISOString(),
        exportedBy: this.currentUserId,
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json',
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      const safeFileName = conversationName
        .replace(/[^a-z0-9]/gi, '_')
        .toLowerCase();
      const dateStr = new Date().toISOString().split('T')[0];
      link.download = `conversation-${safeFileName}-${dateStr}.json`;

      link.click();
      window.URL.revokeObjectURL(url);

      this.showMainMenu = false;
      this.toastService.showSuccess('Conversation exportée avec succès');
    },
  };

  // Template methods
  toggleConversationInfo = this.conversationMethods.showInfo;
  toggleConversationSettings = this.conversationMethods.showSettings;
  clearConversation = this.conversationMethods.clear;
  exportConversation = this.conversationMethods.export;

  sendMessage(): void {
    if (
      (this.messageForm.invalid && !this.selectedFile) ||
      !this.currentUserId ||
      !this.otherParticipant?.id
    ) {
      return;
    }

    this.cleanupMethods.stopTypingIndicator();

    const content = this.messageForm.get('content')?.value;

    const tempMessage: Message = {
      id: 'temp-' + new Date().getTime(),
      content: content || '',
      sender: {
        id: this.currentUserId || '',
        username: this.currentUsername,
      },
      receiver: {
        id: this.otherParticipant.id,
        username: this.otherParticipant.username || 'Recipient',
      },
      timestamp: new Date(),
      isRead: false,
      isPending: true,
    };

    if (this.selectedFile) {
      let fileType = 'file';
      if (this.selectedFile.type.startsWith('image/')) {
        fileType = 'image';

        if (this.previewUrl) {
          tempMessage.attachments = [
            {
              id: 'temp-attachment',
              url: this.previewUrl ? this.previewUrl.toString() : '',
              type: MessageType.IMAGE,
              name: this.selectedFile.name,
              size: this.selectedFile.size,
            },
          ];
        }
      }

      if (fileType === 'image') {
        tempMessage.type = MessageType.IMAGE;
      } else if (fileType === 'file') {
        tempMessage.type = MessageType.FILE;
      }
    }

    this.messages = [...this.messages, tempMessage];

    const fileToSend = this.selectedFile;
    this.messageForm.reset();
    this.removeAttachment();

    setTimeout(() => this.scrollToBottom(true), 50);

    this.isUploading = true;

    const sendSub = this.MessageService.sendMessage(
      this.otherParticipant.id,
      content,
      fileToSend || undefined,
      MessageType.TEXT,
      this.conversation?.id
    ).subscribe({
      next: (message) => {
        this.updateMessageState(tempMessage.id!, message);
        this.isUploading = false;
      },
      error: (error) => {
        this.updateMessageState(tempMessage.id!, null, true);
        this.isUploading = false;
        this.toastService.showError('Failed to send message');
      },
    });

    this.subscriptions.add(sendSub);
  }

  // Méthode consolidée pour mettre à jour l'état des messages
  private updateMessageState(
    tempId: string,
    newMessage?: Message | null,
    isError: boolean = false
  ): void {
    this.messages = this.messages.map((msg) => {
      if (msg.id === tempId) {
        if (newMessage) {
          return newMessage;
        } else if (isError) {
          return {
            ...msg,
            isPending: false,
            isError: true,
          };
        }
      }
      return msg;
    });
  }

  // Service - méthodes consolidées
  readonly serviceMethods = {
    formatMessageTime: (timestamp: string | Date | undefined) =>
      this.MessageService.formatMessageTime(timestamp),
    formatLastActive: (lastActive: string | Date | undefined) =>
      this.MessageService.formatLastActive(lastActive),
    formatMessageDate: (timestamp: string | Date | undefined) =>
      this.MessageService.formatMessageDate(timestamp),
    shouldShowDateHeader: (index: number) =>
      this.MessageService.shouldShowDateHeader(this.messages, index),
    getMessageType: (message: Message | null | undefined) =>
      this.MessageService.getMessageType(message),
    hasImage: (message: Message | null | undefined) =>
      this.MessageService.hasImage(message),
    isVoiceMessage: (message: Message | null | undefined) =>
      this.MessageService.isVoiceMessage(message),
    getVoiceMessageUrl: (message: Message | null | undefined) =>
      this.MessageService.getVoiceMessageUrl(message),
    getVoiceMessageDuration: (message: Message | null | undefined) =>
      this.MessageService.getVoiceMessageDuration(message),
    getVoiceBarHeight: (index: number) =>
      this.MessageService.getVoiceBarHeight(index),
    formatVoiceDuration: (seconds: number) =>
      this.MessageService.formatVoiceDuration(seconds),
    getImageUrl: (message: Message | null | undefined) =>
      this.MessageService.getImageUrl(message),
    getMessageTypeClass: (message: Message | null | undefined) =>
      this.MessageService.getMessageTypeClass(message, this.currentUserId),
  };

  // Méthodes exposées pour le template
  formatMessageTime = this.serviceMethods.formatMessageTime;
  formatLastActive = this.serviceMethods.formatLastActive;
  formatMessageDate = this.serviceMethods.formatMessageDate;
  shouldShowDateHeader = this.serviceMethods.shouldShowDateHeader;
  getMessageType = this.serviceMethods.getMessageType;
  hasImage = this.serviceMethods.hasImage;
  isVoiceMessage = this.serviceMethods.isVoiceMessage;
  getVoiceMessageUrl = this.serviceMethods.getVoiceMessageUrl;
  getVoiceMessageDuration = this.serviceMethods.getVoiceMessageDuration;
  getVoiceBarHeight = this.serviceMethods.getVoiceBarHeight;
  formatVoiceDuration = this.serviceMethods.formatVoiceDuration;
  getImageUrl = this.serviceMethods.getImageUrl;
  getMessageTypeClass = this.serviceMethods.getMessageTypeClass;

  // Défilement
  onScroll(event: any): void {
    const container = event.target;
    const scrollTop = container.scrollTop;

    if (
      scrollTop < 50 &&
      !this.isLoadingMore &&
      this.conversation?.id &&
      this.hasMoreMessages
    ) {
      this.showLoadingIndicator();

      const oldScrollHeight = container.scrollHeight;
      const firstVisibleMessage = this.getFirstVisibleMessage();

      this.isLoadingMore = true;

      this.loadMoreMessages();

      // Maintenir la position de défilement
      requestAnimationFrame(() => {
        const preserveScrollPosition = () => {
          if (firstVisibleMessage) {
            const messageElement = this.findMessageElement(
              firstVisibleMessage.id
            );
            if (messageElement) {
              // Faire défiler jusqu'à l'élément qui était visible avant
              messageElement.scrollIntoView({ block: 'center' });
            } else {
              // Fallback: utiliser la différence de hauteur
              const newScrollHeight = container.scrollHeight;
              const scrollDiff = newScrollHeight - oldScrollHeight;
              container.scrollTop = scrollTop + scrollDiff;
            }
          }

          // Masquer l'indicateur de chargement
          this.hideLoadingIndicator();
        };

        // Attendre que le DOM soit mis à jour
        setTimeout(preserveScrollPosition, 100);
      });
    }
  }

  // Méthode pour trouver le premier message visible dans la vue
  private getFirstVisibleMessage(): Message | null {
    if (!this.messagesContainer?.nativeElement || !this.messages.length)
      return null;

    const container = this.messagesContainer.nativeElement;
    const messageElements = container.querySelectorAll('.message-item');

    for (let i = 0; i < messageElements.length; i++) {
      const element = messageElements[i];
      const rect = element.getBoundingClientRect();

      // Si l'élément est visible dans la vue
      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {
        const messageId = element.getAttribute('data-message-id');
        return this.messages.find((m) => m.id === messageId) || null;
      }
    }

    return null;
  }

  // Méthode pour trouver un élément de message par ID
  private findMessageElement(
    messageId: string | undefined
  ): HTMLElement | null {
    if (!this.messagesContainer?.nativeElement || !messageId) return null;
    return this.messagesContainer.nativeElement.querySelector(
      `[data-message-id="${messageId}"]`
    );
  }

  // Indicateurs de chargement consolidés
  readonly loadingIndicatorMethods = {
    show: () => {
      if (!document.getElementById('message-loading-indicator')) {
        const indicator = document.createElement('div');
        indicator.id = 'message-loading-indicator';
        indicator.className = 'text-center py-2 text-gray-500 text-sm';
        indicator.innerHTML =
          '<i class="fas fa-spinner fa-spin mr-2"></i> Loading older messages...';
        this.messagesContainer?.nativeElement?.prepend(indicator);
      }
    },
    hide: () => {
      const indicator = document.getElementById('message-loading-indicator');
      indicator?.parentNode?.removeChild(indicator);
    },
  };

  private showLoadingIndicator = this.loadingIndicatorMethods.show;
  private hideLoadingIndicator = this.loadingIndicatorMethods.hide;

  // Charger plus de messages
  loadMoreMessages(): void {
    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages)
      return;

    // Marquer comme chargement en cours
    this.isLoadingMore = true;

    // Augmenter la page
    this.currentPage++;

    // Charger plus de messages
    this.MessageService.getConversation(
      this.conversation.id,
      this.MAX_MESSAGES_TO_LOAD,
      this.currentPage
    ).subscribe({
      next: (conversation) => {
        if (
          conversation &&
          conversation.messages &&
          conversation.messages.length > 0
        ) {
          // Sauvegarder les messages actuels
          const oldMessages = [...this.messages];

          // Créer un Set des IDs existants
          const existingIds = new Set(oldMessages.map((msg) => msg.id));

          // Filtrer et trier les nouveaux messages
          const newMessages = conversation.messages
            .filter((msg) => !existingIds.has(msg.id))
            .sort((a, b) => {
              const timeA = new Date(a.timestamp as string).getTime();
              const timeB = new Date(b.timestamp as string).getTime();
              return timeA - timeB;
            });

          if (newMessages.length > 0) {
            // Ajouter les nouveaux messages au début de la liste
            this.messages = [...newMessages, ...oldMessages];

            // Limiter le nombre total de messages pour éviter les problèmes de performance
            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {
              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);
            }

            // Vérifier s'il y a plus de messages à charger
            this.hasMoreMessages =
              newMessages.length >= this.MAX_MESSAGES_TO_LOAD;
          } else {
            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation
            this.hasMoreMessages = false;
          }
        } else {
          this.hasMoreMessages = false;
        }

        // Désactiver le flag de chargement après un court délai
        // pour permettre au DOM de se mettre à jour
        setTimeout(() => {
          this.isLoadingMore = false;
        }, 200);
      },
      error: (error) => {
        console.error('MessageChat', 'Error loading more messages:', error);
        this.isLoadingMore = false;
        this.hideLoadingIndicator();
        this.toastService.showError('Failed to load more messages');
      },
    });
  }

  // Méthode utilitaire pour comparer les timestamps
  private isSameTimestamp(
    timestamp1: string | Date | undefined,
    timestamp2: string | Date | undefined
  ): boolean {
    if (!timestamp1 || !timestamp2) return false;

    try {
      const time1 =
        timestamp1 instanceof Date
          ? timestamp1.getTime()
          : new Date(timestamp1 as string).getTime();
      const time2 =
        timestamp2 instanceof Date
          ? timestamp2.getTime()
          : new Date(timestamp2 as string).getTime();
      return Math.abs(time1 - time2) < 1000;
    } catch (error) {
      return false;
    }
  }

  scrollToBottom(force: boolean = false): void {
    try {
      if (!this.messagesContainer?.nativeElement) return;

      requestAnimationFrame(() => {
        const container = this.messagesContainer.nativeElement;
        const isScrolledToBottom =
          container.scrollHeight - container.clientHeight <=
          container.scrollTop + 150;

        if (force || isScrolledToBottom) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          });
        }
      });
    } catch (err) {
      console.error('MessageChat', 'Error scrolling to bottom:', err);
    }
  }

  // Méthodes d'enregistrement vocal consolidées
  readonly voiceRecordingMethods = {
    toggle: () => {
      this.isRecordingVoice = !this.isRecordingVoice;
      if (!this.isRecordingVoice) this.voiceRecordingDuration = 0;
    },
    complete: (audioBlob: Blob) => {
      if (!this.conversation?.id && !this.otherParticipant?.id) {
        this.toastService.showError('No conversation or recipient selected');
        this.isRecordingVoice = false;
        return;
      }
      const receiverId = this.otherParticipant?.id || '';
      this.MessageService.sendVoiceMessage(
        receiverId,
        audioBlob,
        this.conversation?.id,
        this.voiceRecordingDuration
      ).subscribe({
        next: (message) => {
          this.isRecordingVoice = false;
          this.voiceRecordingDuration = 0;
          this.scrollToBottom(true);
        },
        error: (error) => {
          this.toastService.showError('Failed to send voice message');
          this.isRecordingVoice = false;
        },
      });
    },
    cancel: () => {
      this.isRecordingVoice = false;
      this.voiceRecordingDuration = 0;
    },
  };

  toggleVoiceRecording = this.voiceRecordingMethods.toggle;
  onVoiceRecordingComplete = this.voiceRecordingMethods.complete;
  onVoiceRecordingCancelled = this.voiceRecordingMethods.cancel;

  /**
   * Ouvre une image en plein écran (méthode conservée pour compatibilité)
   * @param imageUrl URL de l'image à afficher
   */
  openImageFullscreen(imageUrl: string): void {
    window.open(imageUrl, '_blank');
  }

  /**
   * Détecte les changements après chaque vérification de la vue - Optimisé
   */
  ngAfterViewChecked(): void {
    // Faire défiler vers le bas si nécessaire
    this.scrollToBottom();

    // Détection des changements optimisée - Suppression de la vérification inutile
    // La détection automatique d'Angular gère déjà les messages vocaux
  }

  /**
   * Navigue vers la liste des conversations
   */
  goBackToConversations(): void {
    this.router.navigate(['/messages/conversations']);
  }

  /**
   * Insère un emoji dans le champ de message
   * @param emoji Emoji à insérer
   */
  insertEmoji(emoji: string): void {
    const control = this.messageForm.get('content');
    if (control) {
      const currentValue = control.value || '';
      control.setValue(currentValue + emoji);
      control.markAsDirty();
      // Garder le focus sur le champ de saisie
      setTimeout(() => {
        const inputElement = document.querySelector(
          '.whatsapp-input-field'
        ) as HTMLInputElement;
        if (inputElement) {
          inputElement.focus();
        }
      }, 0);
    }
  }

  /**
   * S'abonne aux notifications en temps réel
   */
  private subscribeToNotifications(): void {
    const notificationSub =
      this.MessageService.subscribeToNewNotifications().subscribe({
        next: (notification) => {
          this.notifications.unshift(notification);
          this.updateNotificationCount();

          this.MessageService.play('notification');

          if (
            notification.type === 'NEW_MESSAGE' &&
            notification.conversationId === this.conversation?.id
          ) {
            if (notification.id) {
              this.MessageService.markAsRead([notification.id]).subscribe();
            }
          }
        },
        error: (error) => {},
      });
    this.subscriptions.add(notificationSub);

    const notificationsListSub = this.MessageService.notifications$.subscribe({
      next: (notifications) => {
        this.notifications = notifications;
        this.updateNotificationCount();
      },
      error: (error) => {},
    });
    this.subscriptions.add(notificationsListSub);

    const notificationCountSub =
      this.MessageService.notificationCount$.subscribe({
        next: (count) => {
          this.unreadNotificationCount = count;
        },
      });
    this.subscriptions.add(notificationCountSub);

    const callSub = this.MessageService.incomingCall$.subscribe({
      next: (call) => {
        if (call) {
          this.incomingCall = call;
          this.showCallModal = true;
          this.MessageService.play('ringtone');
        } else {
          this.showCallModal = false;
          this.incomingCall = null;
        }
      },
    });
    this.subscriptions.add(callSub);

    const activeCallSub = this.MessageService.activeCall$.subscribe({
      next: (call) => {
        this.activeCall = call;
        if (call) {
          this.showActiveCallModal = true;
          this.startCallTimerMethod();
        } else {
          this.showActiveCallModal = false;
          this.stopCallTimerMethod();
          this.resetCallStateMethod();
        }
      },
    });
    this.subscriptions.add(activeCallSub);

    // S'abonner aux flux vidéo locaux
    const localStreamSub = this.MessageService.localStream$.subscribe({
      next: (stream: MediaStream | null) => {
        if (stream && this.localVideoElement) {
          this.localVideoElement.srcObject = stream;
        }
      },
    });
    this.subscriptions.add(localStreamSub);

    // S'abonner aux flux vidéo distants
    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({
      next: (stream: MediaStream | null) => {
        if (stream && this.remoteVideoElement) {
          this.remoteVideoElement.srcObject = stream;
        }
      },
    });
    this.subscriptions.add(remoteStreamSub);
  }

  // Méthodes d'appel consolidées
  readonly callMethods = {
    initiate: (type: 'AUDIO' | 'VIDEO') => {
      if (!this.otherParticipant?.id) return;
      this.MessageService.initiateCall(
        this.otherParticipant.id,
        type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO,
        this.conversation?.id
      ).subscribe({
        next: (call) => {},
        error: (error) =>
          this.toastService.showError(
            this.constants.errorMessages.callInitiate
          ),
      });
    },
    accept: () => {
      if (!this.incomingCall) return;
      this.MessageService.acceptCall(this.incomingCall.id).subscribe({
        next: (call) => {
          this.showCallModal = false;
          this.incomingCall = null;
          this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';
          this.callQuality = 'connecting';
          this.toastService.showSuccess('Appel connecté');
        },
        error: (error) => {
          this.toastService.showError(this.constants.errorMessages.callAccept);
          this.showCallModal = false;
          this.incomingCall = null;
        },
      });
    },
    reject: () => {
      if (!this.incomingCall) return;
      this.MessageService.rejectCall(this.incomingCall.id).subscribe({
        next: (call) => {
          this.showCallModal = false;
          this.incomingCall = null;
        },
        error: (error) => {
          this.showCallModal = false;
          this.incomingCall = null;
        },
      });
    },
    end: () => {
      const sub = this.MessageService.activeCall$.subscribe((call) => {
        if (call) {
          this.MessageService.endCall(call.id).subscribe({
            next: (call) => {},
            error: (error) => {},
          });
        }
      });
      sub.unsubscribe();
    },
  };

  initiateCall = this.callMethods.initiate;
  acceptCall = this.callMethods.accept;
  rejectCall = this.callMethods.reject;
  endCall = this.callMethods.end;

  // Méthodes de contrôle d'appel consolidées
  readonly callControlMethods = {
    toggleMute: () => {
      this.isCallMuted = !this.isCallMuted;
      this.callControlMethods.updateMedia();
      this.toastService.showInfo(
        this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé'
      );
    },
    toggleVideo: () => {
      this.isVideoEnabled = !this.isVideoEnabled;
      this.callControlMethods.updateMedia();
      this.toastService.showInfo(
        this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'
      );
    },
    updateMedia: () => {
      this.MessageService.toggleMedia(
        this.activeCall?.id,
        !this.isCallMuted,
        this.isVideoEnabled
      ).subscribe({
        next: () => {},
        error: (error) =>
          console.error('Erreur lors de la mise à jour des médias:', error),
      });
    },
  };

  toggleCallMute = this.callControlMethods.toggleMute;
  toggleCallVideo = this.callControlMethods.toggleVideo;
  private updateCallMedia = this.callControlMethods.updateMedia;

  // Méthodes de timer consolidées
  readonly timerMethods = {
    startCallTimer: () => {
      this.callDuration = 0;
      this.callTimer = setInterval(() => {
        this.callDuration++;
        if (this.callDuration === 3 && this.callQuality === 'connecting') {
          this.callQuality = 'excellent';
        }
      }, 1000);
    },
    stopCallTimer: () => {
      if (this.callTimer) {
        clearInterval(this.callTimer);
        this.callTimer = null;
      }
    },
    resetCallState: () => (this.callDuration = 0),
  };

  private startCallTimerMethod = this.timerMethods.startCallTimer;
  private stopCallTimerMethod = this.timerMethods.stopCallTimer;
  private resetCallStateMethod = this.timerMethods.resetCallState;

  // Notifications

  toggleNotificationPanel(): void {
    this.togglePanel('notification');
    if (this.showNotificationPanel) {
      this.loadNotifications();
    }
  }

  /**
   * Charge les notifications
   */
  loadNotifications(refresh: boolean = false): void {
    const loadSub = this.MessageService.getNotifications(
      refresh,
      1,
      20
    ).subscribe({
      next: (notifications) => {
        if (refresh) {
          this.notifications = notifications;
        } else {
          this.notifications = [...this.notifications, ...notifications];
        }

        this.updateNotificationCount();
      },
      error: (error) => {
        this.toastService.showError(
          'Erreur lors du chargement des notifications'
        );
      },
    });

    this.subscriptions.add(loadSub);
  }

  // Méthodes de notification consolidées
  readonly notificationMethods = {
    loadMore: () => this.loadNotifications(),
    updateCount: () =>
      (this.unreadNotificationCount = this.notifications.filter(
        (n) => !n.isRead
      ).length),
    getFiltered: () => this.notifications,
    toggleSelection: (notificationId: string) => {
      if (this.selectedNotifications.has(notificationId)) {
        this.selectedNotifications.delete(notificationId);
      } else {
        this.selectedNotifications.add(notificationId);
      }
    },
    toggleSelectAll: () => {
      const filteredNotifications = this.notificationMethods.getFiltered();
      const allSelected = filteredNotifications.every((n) =>
        this.selectedNotifications.has(n.id)
      );
      if (allSelected) {
        filteredNotifications.forEach((n) =>
          this.selectedNotifications.delete(n.id)
        );
      } else {
        filteredNotifications.forEach((n) =>
          this.selectedNotifications.add(n.id)
        );
      }
    },
    areAllSelected: () => {
      const filteredNotifications = this.notificationMethods.getFiltered();
      return (
        filteredNotifications.length > 0 &&
        filteredNotifications.every((n) => this.selectedNotifications.has(n.id))
      );
    },
  };

  loadMoreNotifications = this.notificationMethods.loadMore;
  private updateNotificationCount = this.notificationMethods.updateCount;
  getFilteredNotifications = this.notificationMethods.getFiltered;
  toggleNotificationSelection = this.notificationMethods.toggleSelection;
  toggleSelectAllNotifications = this.notificationMethods.toggleSelectAll;
  areAllNotificationsSelected = this.notificationMethods.areAllSelected;

  /**
   * Marque les notifications sélectionnées comme lues
   */
  markSelectedAsRead(): void {
    const selectedIds = Array.from(this.selectedNotifications);
    if (selectedIds.length === 0) {
      this.toastService.showWarning('Aucune notification sélectionnée');
      return;
    }

    const markSub = this.MessageService.markAsRead(selectedIds).subscribe({
      next: (result) => {
        this.notifications = this.notifications.map((n) =>
          selectedIds.includes(n.id)
            ? { ...n, isRead: true, readAt: new Date() }
            : n
        );

        this.selectedNotifications.clear();
        this.updateNotificationCount();

        this.toastService.showSuccess(
          `${result.readCount} notification(s) marquée(s) comme lue(s)`
        );
      },
      error: (error) => {
        this.toastService.showError(
          'Erreur lors du marquage des notifications'
        );
      },
    });

    this.subscriptions.add(markSub);
  }

  /**
   * Marque toutes les notifications comme lues
   */
  markAllAsRead(): void {
    const unreadNotifications = this.notifications.filter((n) => !n.isRead);
    if (unreadNotifications.length === 0) {
      this.toastService.showInfo('Aucune notification non lue');
      return;
    }

    const unreadIds = unreadNotifications.map((n) => n.id);

    const markSub = this.MessageService.markAsRead(unreadIds).subscribe({
      next: (result) => {
        this.notifications = this.notifications.map((n) =>
          unreadIds.includes(n.id)
            ? { ...n, isRead: true, readAt: new Date() }
            : n
        );

        this.updateNotificationCount();

        this.toastService.showSuccess(
          'Toutes les notifications ont été marquées comme lues'
        );
      },
      error: (error) => {
        this.toastService.showError(
          'Erreur lors du marquage des notifications'
        );
      },
    });

    this.subscriptions.add(markSub);
  }

  // Méthodes de suppression de notifications consolidées
  readonly notificationDeleteMethods = {
    showDeleteSelectedConfirmation: () => {
      if (this.selectedNotifications.size === 0) {
        this.toastService.showWarning('Aucune notification sélectionnée');
        return;
      }
      this.showDeleteConfirmModal = true;
    },
    deleteSelected: () => {
      const selectedIds = Array.from(this.selectedNotifications);
      if (selectedIds.length === 0) return;
      this.isDeletingNotifications = true;
      this.showDeleteConfirmModal = false;
      const deleteSub = this.MessageService.deleteMultipleNotifications(
        selectedIds
      ).subscribe({
        next: (result) => {
          this.notifications = this.notifications.filter(
            (n) => !selectedIds.includes(n.id)
          );
          this.selectedNotifications.clear();
          this.updateNotificationCount();
          this.isDeletingNotifications = false;
          this.toastService.showSuccess(
            `${result.count} notification(s) supprimée(s)`
          );
        },
        error: (error) => {
          this.isDeletingNotifications = false;
          this.toastService.showError(
            'Erreur lors de la suppression des notifications'
          );
        },
      });
      this.subscriptions.add(deleteSub);
    },
    deleteOne: (notificationId: string) => {
      const deleteSub = this.MessageService.deleteNotification(
        notificationId
      ).subscribe({
        next: (result) => {
          this.notifications = this.notifications.filter(
            (n) => n.id !== notificationId
          );
          this.selectedNotifications.delete(notificationId);
          this.updateNotificationCount();
          this.toastService.showSuccess('Notification supprimée');
        },
        error: (error) => {
          this.toastService.showError(
            'Erreur lors de la suppression de la notification'
          );
        },
      });
      this.subscriptions.add(deleteSub);
    },
    deleteAll: () => {
      if (this.notifications.length === 0) return;
      if (
        !confirm(
          'Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.'
        )
      ) {
        return;
      }
      this.isDeletingNotifications = true;
      const deleteAllSub =
        this.MessageService.deleteAllNotifications().subscribe({
          next: (result) => {
            this.notifications = [];
            this.selectedNotifications.clear();
            this.updateNotificationCount();
            this.isDeletingNotifications = false;
            this.toastService.showSuccess(
              `${result.count} notifications supprimées avec succès`
            );
          },
          error: (error) => {
            this.isDeletingNotifications = false;
            this.toastService.showError(
              'Erreur lors de la suppression de toutes les notifications'
            );
          },
        });
      this.subscriptions.add(deleteAllSub);
    },
    cancel: () => (this.showDeleteConfirmModal = false),
  };

  showDeleteSelectedConfirmation =
    this.notificationDeleteMethods.showDeleteSelectedConfirmation;
  deleteSelectedNotifications = this.notificationDeleteMethods.deleteSelected;
  deleteNotification = this.notificationDeleteMethods.deleteOne;
  deleteAllNotifications = this.notificationDeleteMethods.deleteAll;
  cancelDeleteNotifications = this.notificationDeleteMethods.cancel;

  // Méthodes utilitaires de notification consolidées
  readonly notificationUtilMethods = {
    formatDate: (date: string | Date | undefined) =>
      this.MessageService.formatLastActive(date),
    getIcon: (type: string) =>
      this.constants.notificationConfig[
        type as keyof typeof this.constants.notificationConfig
      ]?.icon || 'fas fa-bell',
    getColor: (type: string) =>
      this.constants.notificationConfig[
        type as keyof typeof this.constants.notificationConfig
      ]?.color || 'text-cyan-500',
    trackById: (index: number, notification: any) =>
      this.constants.trackById(0, notification),
  };

  formatNotificationDate = this.notificationUtilMethods.formatDate;
  getNotificationIcon = this.notificationUtilMethods.getIcon;
  getNotificationColor = this.notificationUtilMethods.getColor;
  trackByNotificationId = this.notificationUtilMethods.trackById;

  /**
   * S'abonne au statut utilisateur en temps réel
   */
  private subscribeToUserStatus(): void {
    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({
      next: (user: User) => {
        this.handleUserStatusUpdate(user);
      },
      error: (error) => {
        // Error handled silently
      },
    });

    this.subscriptions.add(statusSub);
  }

  /**
   * Gère la mise à jour du statut d'un utilisateur
   */
  private handleUserStatusUpdate(user: User): void {
    if (!user.id) return;

    if (user.isOnline) {
      this.onlineUsers.set(user.id, user);
    } else {
      this.onlineUsers.delete(user.id);
    }

    if (this.otherParticipant && this.otherParticipant.id === user.id) {
      this.otherParticipant = { ...this.otherParticipant, ...user };
    }
  }

  /**
   * Initialise le statut de l'utilisateur actuel
   */
  private initializeUserStatus(): void {
    if (!this.currentUserId) return;

    const setOnlineSub = this.MessageService.setUserOnline(
      this.currentUserId
    ).subscribe({
      next: (user: User) => {
        this.currentUserStatus = 'online';
        this.lastActivityTime = new Date();
      },
      error: (error) => {
        console.error(
          'MessageChat',
          "Erreur lors de l'initialisation du statut",
          error
        );
      },
    });

    this.subscriptions.add(setOnlineSub);
  }

  /**
   * Démarre le suivi d'activité automatique
   */
  private startActivityTracking(): void {
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ];

    events.forEach((event) => {
      document.addEventListener(event, this.onUserActivity.bind(this), true);
    });
  }

  /**
   * Gère l'activité de l'utilisateur
   */
  private onUserActivity(): void {
    this.lastActivityTime = new Date();

    // Réinitialiser le timer
    if (this.autoAwayTimeout) {
      clearTimeout(this.autoAwayTimeout);
    }

    // Remettre en ligne si absent
    if (
      this.currentUserStatus === 'away' ||
      this.currentUserStatus === 'offline'
    ) {
      this.updateUserStatus('online');
    }

    // Programmer la mise en absence
    this.autoAwayTimeout = setTimeout(() => {
      if (this.currentUserStatus === 'online') {
        this.updateUserStatus('away');
      }
    }, this.constants.autoAwayDelay);
  }

  /**
   * Met à jour le statut de l'utilisateur
   */
  updateUserStatus(status: string): void {
    if (!this.currentUserId) return;

    const previousStatus = this.currentUserStatus;

    let updateObservable;
    if (status === 'online') {
      updateObservable = this.MessageService.setUserOnline(this.currentUserId);
    } else {
      updateObservable = this.MessageService.setUserOffline(this.currentUserId);
    }

    const updateSub = updateObservable.subscribe({
      next: (user: User) => {
        this.currentUserStatus = status;

        if (status !== previousStatus) {
          const statusText = this.getStatusText(status);
          this.toastService.showInfo(`Statut : ${statusText}`);
        }
      },
      error: (error) => {
        console.error(
          'MessageChat',
          'Erreur lors de la mise à jour du statut',
          error
        );
      },
    });

    this.subscriptions.add(updateSub);
  }

  /**
   * Charge la liste des utilisateurs en ligne
   */
  loadOnlineUsers(): void {
    const usersSub = this.MessageService.getAllUsers(
      false,
      undefined,
      1,
      50,
      'username',
      'asc',
      true
    ).subscribe({
      next: (users: User[]) => {
        users.forEach((user) => {
          if (user.isOnline && user.id) {
            this.onlineUsers.set(user.id, user);
          }
        });
      },
      error: (error) => {
        console.error(
          'MessageChat',
          'Erreur lors du chargement des utilisateurs en ligne',
          error
        );
      },
    });

    this.subscriptions.add(usersSub);
  }

  // Méthode pour obtenir les options de statut pour le template
  getStatusOptions() {
    return Object.entries(this.constants.statusConfig).map(([key, config]) => ({
      key,
      ...config,
    }));
  }

  getThemeOptions() {
    return this.constants.themeConfig;
  }

  getActivePanels() {
    const panels = [];
    if (this.showUserStatusPanel)
      panels.push({
        key: 'userStatus',
        title: 'Utilisateurs',
        icon: 'fas fa-users',
        closeAction: () => (this.showUserStatusPanel = false),
      });
    if (this.showCallHistoryPanel)
      panels.push({
        key: 'callHistory',
        title: 'Historique des appels',
        icon: 'fas fa-history',
        closeAction: () => (this.showCallHistoryPanel = false),
      });
    if (this.showCallStatsPanel)
      panels.push({
        key: 'callStats',
        title: "Statistiques d'appels",
        icon: 'fas fa-chart-bar',
        closeAction: () => (this.showCallStatsPanel = false),
      });
    if (this.showVoiceMessagesPanel)
      panels.push({
        key: 'voiceMessages',
        title: 'Messages vocaux',
        icon: 'fas fa-microphone',
        closeAction: () => (this.showVoiceMessagesPanel = false),
      });
    return panels;
  }

  // Méthodes de statut consolidées
  readonly statusMethods = {
    getText: (status: string) =>
      this.constants.statusConfig[
        status as keyof typeof this.constants.statusConfig
      ]?.text || 'Inconnu',
    getColor: (status: string) =>
      this.constants.statusConfig[
        status as keyof typeof this.constants.statusConfig
      ]?.color || 'text-gray-400',
    getIcon: (status: string) =>
      this.constants.statusConfig[
        status as keyof typeof this.constants.statusConfig
      ]?.icon || 'fas fa-question-circle',
  };

  getStatusText = this.statusMethods.getText;
  getStatusColor = this.statusMethods.getColor;
  getStatusIcon = this.statusMethods.getIcon;

  // Méthodes utilitaires consolidées
  readonly utilityMethods = {
    formatLastSeen: (lastActive: Date | null) =>
      lastActive
        ? this.MessageService.formatLastActive(lastActive)
        : 'Jamais vu',
    getOnlineUsersCount: () =>
      Array.from(this.onlineUsers.values()).filter((user) => user.isOnline)
        .length,
    getFilteredUsers: () => Array.from(this.onlineUsers.values()),
    setStatusFilter: (filter: string) => (this.statusFilterType = filter),
  };

  formatLastSeen = this.utilityMethods.formatLastSeen;
  getOnlineUsersCount = this.utilityMethods.getOnlineUsersCount;
  setStatusFilter = this.utilityMethods.setStatusFilter;
  getFilteredUsers = this.utilityMethods.getFilteredUsers;

  // Méthodes de réponse et transfert consolidées
  readonly replyForwardMethods = {
    startReply: (message: any) => (this.replyingToMessage = message),
    cancelReply: () => (this.replyingToMessage = null),
    openForwardModal: (message: any) => {
      this.forwardingMessage = message;
      this.showForwardModal = true;
    },
    closeForwardModal: () => {
      this.showForwardModal = false;
      this.forwardingMessage = null;
      this.selectedConversations = [];
    },
  };

  startReplyToMessage = this.replyForwardMethods.startReply;
  cancelReply = this.replyForwardMethods.cancelReply;
  openForwardModal = this.replyForwardMethods.openForwardModal;
  closeForwardModal = this.replyForwardMethods.closeForwardModal;

  // Messages - méthodes consolidées
  readonly messageMethods = {
    getPinIcon: (message: any) =>
      this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack',
    getPinDisplayText: (message: any) =>
      this.isMessagePinned(message) ? 'Désépingler' : 'Épingler',
    canEditMessage: (message: any) => message.sender?.id === this.currentUserId,
    isMessagePinned: (message: any) => message.isPinned || false,
  };

  getPinIcon = this.messageMethods.getPinIcon;
  getPinDisplayText = this.messageMethods.getPinDisplayText;
  canEditMessage = this.messageMethods.canEditMessage;
  isMessagePinned = this.messageMethods.isMessagePinned;

  // Méthodes d'édition consolidées
  readonly editMethods = {
    startEditMessage: (message: any) => {
      this.editingMessageId = message.id;
      this.editingContent = message.content;
    },
    cancelEditMessage: () => {
      this.editingMessageId = null;
      this.editingContent = '';
    },
    saveEditMessage: (messageId: string) =>
      this.editMethods.cancelEditMessage(),
    onEditKeyPress: (event: KeyboardEvent, messageId: string) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        this.editMethods.saveEditMessage(messageId);
      } else if (event.key === 'Escape') {
        this.editMethods.cancelEditMessage();
      }
    },
  };

  startEditMessage = this.editMethods.startEditMessage;
  cancelEditMessage = this.editMethods.cancelEditMessage;
  saveEditMessage = this.editMethods.saveEditMessage;
  onEditKeyPress = this.editMethods.onEditKeyPress;

  togglePinMessage(message: any): void {
    this.isPinning[message.id] = true;
    setTimeout(() => {
      this.isPinning[message.id] = false;
      this.showPinConfirm[message.id] = false;
    }, 1000);
  }

  // Utilitaires d'appel consolidées
  readonly callUtilities = {
    getCallStatusColor: (status: string) =>
      this.constants.callStatusColors[
        status as keyof typeof this.constants.callStatusColors
      ] || 'text-gray-500',
    getCallTypeIcon: (type: string) =>
      type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',
    formatCallDuration: (duration: number) => {
      if (!duration) return '00:00';
      const minutes = Math.floor(duration / 60);
      const seconds = duration % 60;
      return `${minutes.toString().padStart(2, '0')}:${seconds
        .toString()
        .padStart(2, '0')}`;
    },
    formatCallDate: (timestamp: string | Date | undefined) =>
      this.MessageService.formatMessageDate(timestamp),
  };

  getCallStatusColor = this.callUtilities.getCallStatusColor;
  getCallTypeIcon = this.callUtilities.getCallTypeIcon;
  formatCallDuration = this.callUtilities.formatCallDuration;
  formatCallDate = this.callUtilities.formatCallDate;

  // Événements
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const closeConfigs = [
      {
        selectors: ['.theme-selector-menu', '.btn-theme'],
        property: 'showThemeSelector',
      },
      {
        selectors: ['.emoji-picker', '.btn-emoji'],
        property: 'showEmojiPicker',
      },
    ];

    closeConfigs.forEach((config) => {
      const isClickOutside = !config.selectors.some((selector) =>
        target.closest(selector)
      );
      if (isClickOutside) {
        (this as any)[config.property] = false;
      }
    });
  }

  // Méthodes de réaction consolidées
  readonly reactionMethods = {
    getUniqueReactions: (message: any) => message.reactions || [],
    onReactionClick: (messageId: string, emoji: string) => {
      // Implémentation des réactions
    },
    hasUserReacted: (message: any, emoji: string) => false,
  };

  getUniqueReactions = this.reactionMethods.getUniqueReactions;
  onReactionClick = this.reactionMethods.onReactionClick;
  hasUserReacted = this.reactionMethods.hasUserReacted;

  // Méthodes de conversation consolidées
  readonly conversationSelectionMethods = {
    areAllSelected: () =>
      this.selectedConversations.length === this.availableConversations.length,
    selectAll: () =>
      (this.selectedConversations = this.availableConversations.map(
        (c) => c.id
      )),
    deselectAll: () => (this.selectedConversations = []),
    toggle: (conversationId: string) => {
      const index = this.selectedConversations.indexOf(conversationId);
      index > -1
        ? this.selectedConversations.splice(index, 1)
        : this.selectedConversations.push(conversationId);
    },
    isSelected: (conversationId: string) =>
      this.selectedConversations.includes(conversationId),
    getDisplayImage: (conversation: any) =>
      conversation.image || 'assets/images/default-avatar.png',
    getDisplayName: (conversation: any) => conversation.name || 'Conversation',
    forwardMessage: () => {
      this.isForwarding = true;
      setTimeout(() => {
        this.isForwarding = false;
        this.closeForwardModal();
      }, 1000);
    },
  };

  areAllConversationsSelected =
    this.conversationSelectionMethods.areAllSelected;
  selectAllConversations = this.conversationSelectionMethods.selectAll;
  deselectAllConversations = this.conversationSelectionMethods.deselectAll;
  toggleConversationSelection = this.conversationSelectionMethods.toggle;
  isConversationSelected = this.conversationSelectionMethods.isSelected;
  getConversationDisplayImage =
    this.conversationSelectionMethods.getDisplayImage;
  getConversationDisplayName = this.conversationSelectionMethods.getDisplayName;
  forwardMessage = this.conversationSelectionMethods.forwardMessage;

  // Notifications - méthodes simplifiées
  onCallMouseMove = () => (this.showCallControls = true);
  saveNotificationSettings = () => {};
  setNotificationFilter = (filter: string) =>
    (this.notificationFilter = filter);

  // Méthodes de recherche consolidées
  readonly searchMethods = {
    onInput: (event: any) => {
      this.searchQuery = event.target.value;
      this.searchQuery.length >= 2
        ? this.searchMethods.perform()
        : this.searchMethods.clear();
    },
    onKeyPress: (event: KeyboardEvent) => {
      if (event.key === 'Enter') {
        this.searchMethods.perform();
      } else if (event.key === 'Escape') {
        this.searchMethods.clear();
      }
    },
    perform: () => {
      this.isSearching = true;
      this.searchMode = true;
      setTimeout(() => {
        this.searchResults = this.messages.filter((m) =>
          m.content?.toLowerCase().includes(this.searchQuery.toLowerCase())
        );
        this.isSearching = false;
      }, 500);
    },
    clear: () => {
      this.searchQuery = '';
      this.searchResults = [];
      this.isSearching = false;
      this.searchMode = false;
    },
  };

  onSearchInput = this.searchMethods.onInput;
  onSearchKeyPress = this.searchMethods.onKeyPress;
  performSearch = this.searchMethods.perform;
  clearSearch = this.searchMethods.clear;

  // Utilitaires finales simplifiées
  navigateToMessage = (messageId: string) => {
    // Navigation vers un message spécifique
  };
  scrollToPinnedMessage = (messageId: string) => {
    // Défilement vers un message épinglé
  };
  getPinnedMessagesCount = () => this.pinnedMessages.length;

  // Méthodes de recherche et réaction consolidées
  readonly searchAndReactionMethods = {
    highlightSearchTerms: (content: string, query: string) => {
      if (!query) return content;
      const regex = new RegExp(`(${query})`, 'gi');
      return content.replace(regex, '<mark>$1</mark>');
    },
    toggleReactionPicker: (messageId: string) =>
      (this.showReactionPicker[messageId] =
        !this.showReactionPicker[messageId]),
    reactToMessage: (messageId: string, emoji: string) =>
      (this.showReactionPicker[messageId] = false),
    toggleMessageOptions: (messageId: string) =>
      (this.showMessageOptions[messageId] =
        !this.showMessageOptions[messageId]),
  };

  highlightSearchTerms = this.searchAndReactionMethods.highlightSearchTerms;
  toggleReactionPicker = this.searchAndReactionMethods.toggleReactionPicker;
  reactToMessage = this.searchAndReactionMethods.reactToMessage;
  toggleMessageOptions = this.searchAndReactionMethods.toggleMessageOptions;

  // Confirmations consolidées
  readonly confirmationMethods = {
    showPinConfirmation: (messageId: string) =>
      (this.showPinConfirm[messageId] = true),
    cancelPinConfirmation: (messageId: string) =>
      (this.showPinConfirm[messageId] = false),
    showDeleteConfirmation: (messageId: string) =>
      (this.showDeleteConfirm[messageId] = true),
    cancelDeleteMessage: (messageId: string) =>
      (this.showDeleteConfirm[messageId] = false),
  };

  showPinConfirmation = this.confirmationMethods.showPinConfirmation;
  cancelPinConfirmation = this.confirmationMethods.cancelPinConfirmation;
  showDeleteConfirmation = this.confirmationMethods.showDeleteConfirmation;
  cancelDeleteMessage = this.confirmationMethods.cancelDeleteMessage;

  confirmDeleteMessage(messageId: string): void {
    this.showDeleteConfirm[messageId] = false;
  }

  // Méthodes de nettoyage consolidées
  readonly cleanupMethods = {
    clearTimeouts: () => {
      const timeouts = [
        this.typingTimeout,
        this.autoAwayTimeout,
        this.callTimer,
      ];
      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));
    },
    setUserOffline: () => {
      if (this.currentUserId) {
        this.MessageService.setUserOffline(this.currentUserId).subscribe();
      }
    },
    stopTypingIndicator: () => {
      if (this.isCurrentlyTyping && this.conversation?.id) {
        this.isCurrentlyTyping = false;
        clearTimeout(this.typingTimer);
        this.MessageService.stopTyping(this.conversation.id).subscribe({
          next: () => {},
          error: (error) => {},
        });
      }
    },
  };

  private clearTimeouts = this.cleanupMethods.clearTimeouts;
  private setUserOffline = this.cleanupMethods.setUserOffline;

  ngOnDestroy(): void {
    this.cleanupMethods.stopTypingIndicator();
    this.clearTimeouts();
    this.setUserOffline();
    this.subscriptions.unsubscribe();
  }
}
