import {
  Component,
  OnIni<PERSON>,
  On<PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  AfterViewChecked,
  ChangeDetectorRef,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthuserService } from 'src/app/services/authuser.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { User } from '@app/models/user.model';
import { UserStatusService } from 'src/app/services/user-status.service';
import {
  Message,
  Conversation,
  MessageType,
  CallType,
} from 'src/app/models/message.model';
import { ToastService } from 'src/app/services/toast.service';
import { MessageService } from '@app/services/message.service';

@Component({
  selector: 'app-message-chat',
  templateUrl: './message-chat.component.html',
})
export class MessageChatComponent
  implements OnInit, OnDestroy, AfterViewChecked
{
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  @ViewChild('fileInput', { static: false })
  fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('messageTextarea') messageTextarea!: ElementRef;

  // === PROPRIÉTÉS PRINCIPALES ===
  messages: Message[] = [];
  messageForm: FormGroup;
  conversation: Conversation | null = null;
  loading = true;
  error: any;
  currentUserId: string | null = null;
  currentUsername: string = 'You';
  otherParticipant: User | null = null;
  isAdmin = false;
  selectedFile: File | null = null;
  previewUrl: string | ArrayBuffer | null = null;
  isUploading = false;
  isTyping = false;
  typingTimeout: any;
  isRecordingVoice = false;
  voiceRecordingDuration = 0;

  // === PAGINATION ET CHARGEMENT ===
  private readonly MAX_MESSAGES_TO_LOAD = 10;
  private currentPage = 1;
  isLoadingMore = false;
  hasMoreMessages = true;
  private subscriptions = new Subscription();

  // === INTERFACE ET THÈMES ===
  selectedTheme: string = 'theme-default';
  showThemeSelector = false;
  showMainMenu = false;
  showEmojiPicker = false;
  showSearchBar = false;
  showPinnedMessages = false;
  showStatusSelector = false;
  showNotificationPanel = false;
  showNotificationSettings = false;
  showUserStatusPanel = false;
  showCallHistoryPanel = false;
  showCallStatsPanel = false;
  showVoiceMessagesPanel = false;

  // === APPELS ===
  incomingCall: any = null;
  activeCall: any = null;
  showCallModal = false;
  showActiveCallModal = false;
  isCallMuted = false;
  isVideoEnabled = true;
  callDuration = 0;
  callTimer: any = null;

  // === NOTIFICATIONS ET MESSAGES ===
  notifications: any[] = [];
  unreadNotificationCount: number = 0;
  selectedNotifications: Set<string> = new Set();
  showDeleteConfirmModal: boolean = false;
  editingMessageId: string | null = null;
  editingContent = '';
  replyingToMessage: any = null;

  // === RECHERCHE ===
  searchQuery = '';
  isSearching = false;
  searchMode = false;
  searchResults: any[] = [];
  showSearch = false;
  showAdvancedSearch = false;

  // === PANNEAUX ===
  pinnedMessages: any[] = [];
  showReactionPicker: any = {};
  showDeleteConfirm: any = {};
  showPinConfirm: any = {};
  isPinning: any = {};
  showMessageOptions: any = {};

  // === TRANSFERT ===
  showForwardModal = false;
  forwardingMessage: any = null;
  selectedConversations: string[] = [];
  availableConversations: any[] = [];
  isForwarding = false;
  isLoadingConversations = false;

  // === ÉMOJIS ET AUTOCOLLANTS ===
  emojiCategories: any[] = [
    { id: 'recent', name: 'Récents', icon: 'fas fa-clock' },
    { id: 'smileys', name: 'Smileys', icon: 'fas fa-smile' },
    { id: 'people', name: 'Personnes', icon: 'fas fa-user' },
    { id: 'animals', name: 'Animaux', icon: 'fas fa-paw' },
    { id: 'food', name: 'Nourriture', icon: 'fas fa-apple-alt' },
    { id: 'travel', name: 'Voyage', icon: 'fas fa-plane' },
    { id: 'activities', name: 'Activités', icon: 'fas fa-football-ball' },
    { id: 'objects', name: 'Objets', icon: 'fas fa-lightbulb' },
    { id: 'symbols', name: 'Symboles', icon: 'fas fa-heart' },
    { id: 'flags', name: 'Drapeaux', icon: 'fas fa-flag' },
  ];
  selectedEmojiCategory = 'recent';
  emojiSearchQuery = '';
  recentEmojis: any[] = [];
  previewedEmoji: any = null;
  showStickerPicker = false;
  stickerPacks: any[] = [];
  selectedStickerPack = '';

  // === GIFS ===
  showGifPicker = false;
  gifSearchQuery = '';
  gifCategories = ['Trending', 'Reactions', 'Animals', 'Sports', 'TV & Movies'];
  selectedGifCategory = 'Trending';

  // === OUTIL DE DESSIN ===
  showDrawingTool = false;
  selectedDrawingTool = 'pen';
  drawingColors = [
    '#000000',
    '#FF0000',
    '#00FF00',
    '#0000FF',
    '#FFFF00',
    '#FF00FF',
    '#00FFFF',
    '#FFFFFF',
  ];
  selectedDrawingColor = '#000000';
  customDrawingColor = '#000000';
  drawingSize = 5;

  // === CAMÉRA ===
  showCamera = false;
  cameraMode = 'photo';
  flashEnabled = false;
  showCameraGrid = false;
  showFocusIndicator = false;
  focusX = 0;
  focusY = 0;
  lastCapturedImage = '';
  isRecordingVideo = false;
  videoRecordingDuration = 0;

  // === ÉDITEUR D'IMAGES ===
  showImageEditor = false;
  imageEditorMode = 'crop';
  cropArea = { x: 0, y: 0, width: 100, height: 100 };
  cropRatio = 'free';
  imageFilters: any[] = [
    { name: 'none', label: 'Aucun', css: 'none' },
    { name: 'grayscale', label: 'Noir & Blanc', css: 'grayscale(100%)' },
    { name: 'sepia', label: 'Sépia', css: 'sepia(100%)' },
    { name: 'vintage', label: 'Vintage', css: 'sepia(50%) contrast(1.2)' },
    { name: 'bright', label: 'Lumineux', css: 'brightness(1.3)' },
    { name: 'contrast', label: 'Contraste', css: 'contrast(1.5)' },
  ];
  selectedImageFilter = 'none';
  imageAdjustments = { brightness: 0, contrast: 0, saturation: 0, hue: 0 };
  imageTextElements: any[] = [];
  newTextContent = '';
  textFontFamily = 'Arial';
  textFontSize = 24;
  textColor = '#000000';
  availableFonts = [
    { value: 'Arial', label: 'Arial' },
    { value: 'Helvetica', label: 'Helvetica' },
    { value: 'Times New Roman', label: 'Times New Roman' },
    { value: 'Courier New', label: 'Courier New' },
  ];

  // === GESTIONNAIRE DE FICHIERS ===
  showFileManager = false;
  fileViewMode = 'grid';
  fileFolders: any[] = [];
  selectedFolder: any = null;
  fileBreadcrumbs: any[] = [];
  fileSearchQuery = '';
  fileTypeFilter = '';
  fileSortBy = 'name';
  selectedFiles: string[] = [];

  // === ANALYTICS ===
  showAnalyticsDashboard = false;
  analyticsTimeRange = '7d';
  analyticsData: any = {
    totalMessages: 0,
    activeUsers: 0,
    avgResponseTime: '0s',
    filesShared: 0,
    messagesChange: 0,
    usersChange: 0,
    responseTimeChange: 0,
    filesChange: 0,
    topUsers: [],
    messageTypes: [],
    longestConversation: 0,
    avgConversationDuration: '0m',
    topEmojis: [],
  };

  // === INTÉGRATIONS ===
  showIntegrations = false;
  integrationCategories = [
    'CRM',
    'Productivité',
    'Communication',
    'Analytics',
    'Sécurité',
  ];
  selectedIntegrationCategory = 'CRM';
  webhooks: any[] = [];

  // === ZONE DE SAISIE AVANCÉE ===
  showQuickReplies = false;
  quickReplies: string[] = ['Merci !', "D'accord", 'Parfait', 'À bientôt'];
  showMentionSuggestions = false;
  mentionSuggestions: any[] = [];
  selectedMentionIndex = 0;
  activeMentions: any[] = [];
  showHashtagSuggestions = false;
  hashtagSuggestions: any[] = [];
  selectedHashtagIndex = 0;
  activeHashtags: any[] = [];
  showCommandSuggestions = false;
  commandSuggestions: any[] = [];
  selectedCommandIndex = 0;
  activeLinks: any[] = [];
  inputHeight = 40;
  showFormattingToolbar = false;
  detectedLanguage = 'fr';
  autoCorrections: any[] = [];
  showAutoCorrectSuggestions = false;
  autoCorrectSuggestions: any[] = [];

  // === TRADUCTION ===
  showTranslationPanel = false;
  translationFrom = 'auto';
  translationTo = 'fr';
  originalTextForTranslation = '';
  translatedText = '';
  isTranslating = false;
  supportedLanguages = [
    { code: 'fr', name: 'Français' },
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'de', name: 'Deutsch' },
    { code: 'it', name: 'Italiano' },
  ];

  // === SONDAGES ===
  showPollCreator = false;
  pollQuestion = '';
  pollOptions: any[] = [{ text: '' }, { text: '' }];
  pollSettings = {
    allowMultiple: false,
    anonymous: false,
    showResults: true,
    showVoters: true,
  };
  pollExpiry = '';
  customPollExpiry = '';

  // === LOCALISATION ===
  showLocationPicker = false;
  showLocationSearch = false;
  locationSearchQuery = '';
  locationSearchResults: any[] = [];
  selectedLocation: any = null;
  showLocationMessage = false;
  locationMessage = '';

  // === CONTACTS ===
  showContactPicker = false;
  contactSearchQuery = '';
  selectedContactForSharing: any = null;

  // === PROGRAMMATION D'ENVOI ===
  showScheduleMessage = false;
  customScheduleDate = '';
  customScheduleTime = '';
  scheduleTimezone = 'Europe/Paris';
  availableTimezones = [
    { value: 'Europe/Paris', label: 'Paris (CET)' },
    { value: 'America/New_York', label: 'New York (EST)' },
    { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
  ];

  // === ÉTATS DES MESSAGES ===
  highlightedMessageId = '';
  searchResultIds: string[] = [];
  selectionMode = false;
  selectedMessages: Set<string> = new Set();
  hoveredMessageId = '';
  playingVoiceId = '';
  showScrollToBottom = false;
  unreadMessagesCount = 0;

  // === VISUALISEUR D'IMAGES ===
  showImageViewer = false;
  currentImageIndex = 0;
  imageGallery: any[] = [];

  // === TOAST ===
  showToast = false;
  toastMessage = '';
  toastType = 'info';

  // === PANNEAUX D'INFORMATIONS ===
  showConversationInfo = false;
  showConversationSettings = false;
  conversationSettings = {
    notifications: true,
    soundNotifications: true,
    readReceipts: true,
    typingIndicators: true,
  };

  // === VARIABLES D'ÉTAT POUR LES NOUVELLES FONCTIONNALITÉS ===
  isSendingMessage = false;
  isUserTyping = false;
  voiceRecordingState = 'idle';
  voiceRecordingSize = 0;
  recordingWaveform: number[] = [];
  voiceRecordingQuality = 'medium';
  voiceEffects: string[] = [];
  selectedContacts: string[] = [];
  isUpdatingStatus = false;

  // === VARIABLES POUR LES PIÈCES JOINTES ===
  showAttachmentMenu = false;
  attachmentFiles: File[] = [];
  fileCaptions: string[] = [];
  fileUploadProgress: number[] = [];

  // === PROPRIÉTÉS POUR LE TEMPLATE ===
  messageContent = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authUserService: AuthuserService,
    private MessageService: MessageService,
    private toastService: ToastService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private userStatusService: UserStatusService
  ) {
    this.messageForm = this.fb.group({
      content: ['', [Validators.required, Validators.minLength(1)]],
    });
  }

  // === CONSTANTES OPTIMISÉES ===
  readonly c = {
    reactions: ['👍', '❤️', '😂', '😮', '😢', '😡'],
    delays: { away: 300000, typing: 3000, scroll: 100, anim: 300 },
    error: 'Erreur. Veuillez réessayer.',
    trackById: (i: number, item: any): string => item?.id || i.toString(),
    notifications: {
      NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },
      CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },
      SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },
    },
    status: {
      online: {
        text: 'En ligne',
        color: 'text-green-500',
        icon: 'fas fa-circle',
      },
      offline: {
        text: 'Hors ligne',
        color: 'text-gray-500',
        icon: 'far fa-circle',
      },
      away: { text: 'Absent', color: 'text-yellow-500', icon: 'fas fa-clock' },
      busy: {
        text: 'Occupé',
        color: 'text-red-500',
        icon: 'fas fa-minus-circle',
      },
    },
    themes: [
      { key: 'theme-default', label: 'Défaut', color: '#4f5fad' },
      { key: 'theme-feminine', label: 'Rose', color: '#ff6b9d' },
      { key: 'theme-masculine', label: 'Bleu', color: '#3d85c6' },
      { key: 'theme-neutral', label: 'Vert', color: '#6aa84f' },
    ],
    calls: {
      COMPLETED: 'text-green-500',
      MISSED: 'text-red-500',
      REJECTED: 'text-orange-500',
    },
  };

  // === GETTERS ===
  get availableReactions(): string[] {
    return this.c.reactions;
  }
  get commonEmojis(): string[] {
    return this.MessageService.getCommonEmojis();
  }
  get unreadNotificationsCount(): number {
    return this.unreadNotificationCount;
  }
  get currentUserStatus(): string {
    return 'online';
  }
  get onlineUsersCount(): number {
    return 5;
  }

  // === LIFECYCLE HOOKS ===
  ngOnInit(): void {
    this.initializeComponent();
  }

  // === NETTOYAGE (SUPPRIMÉ - DÉJÀ IMPLÉMENTÉ PLUS BAS) ===

  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  // === INITIALISATION ===
  private initializeComponent(): void {
    this.loadCurrentUser();
    this.loadConversation();
    this.setupSubscriptions();
    // Initialiser des données de test pour la démo
    this.initializeTestData();
  }

  private loadConversation(): void {
    this.route.params.subscribe((params) => {
      const conversationId = params['id'];
      if (conversationId) {
        this.getConversation(conversationId);
      }
    });
  }

  private setupSubscriptions(): void {
    // Configuration des abonnements WebSocket et autres
  }

  // === GESTION DES CONVERSATIONS ===
  getConversation(conversationId: string): void {
    this.loading = true;
    this.MessageService.getConversation(
      conversationId,
      this.MAX_MESSAGES_TO_LOAD,
      this.currentPage
    ).subscribe({
      next: (conversation) => {
        this.conversation = conversation;
        this.otherParticipant =
          conversation.participants?.find((p) => p.id !== this.currentUserId) ||
          null;

        // Utiliser les messages de la conversation
        if (conversation.messages) {
          this.messages = conversation.messages;
          this.hasMoreMessages =
            conversation.messages.length === this.MAX_MESSAGES_TO_LOAD;
        }

        this.loading = false;
        this.scrollToBottom();
      },
      error: (error) => {
        this.error = error;
        this.loading = false;
        console.error('Erreur lors du chargement de la conversation:', error);
      },
    });
  }

  // === GESTION DES MESSAGES ===

  sendMessage(): void {
    const content = this.messageForm.get('content')?.value?.trim();
    if (!content || !this.conversation?.id) return;

    this.isSendingMessage = true;

    // Créer un message temporaire pour l'affichage immédiat
    const tempMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: { id: this.currentUserId!, username: this.currentUsername },
      timestamp: new Date(),
      conversationId: this.conversation.id,
      type: 'text' as MessageType,
    };

    this.messages.push(tempMessage);
    this.messageForm.reset();
    this.scrollToBottom();
    this.isSendingMessage = false;

    // Simuler l'envoi réel (à remplacer par l'appel API réel)
    /*
    this.MessageService.sendMessage(
      this.conversation.id,
      content,
      'text' as MessageType,
      this.currentUserId || ''
    ).subscribe({
      next: (message) => {
        // Remplacer le message temporaire par le vrai message
        const index = this.messages.findIndex(m => m.id === tempMessage.id);
        if (index > -1) {
          this.messages[index] = message;
        }
      },
      error: (error) => {
        console.error("Erreur lors de l'envoi du message:", error);
        this.toastService.showError("Erreur lors de l'envoi du message");
        // Supprimer le message temporaire en cas d'erreur
        this.messages = this.messages.filter(m => m.id !== tempMessage.id);
      },
    });
    */
  }

  // === GESTION DES FICHIERS ===
  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.attachmentFiles = Array.from(files);
      this.generatePreviews();
    }
  }

  removeAttachment(index: number): void {
    this.attachmentFiles.splice(index, 1);
    if (this.attachmentFiles.length === 0) {
      this.previewUrl = null;
    }
  }

  // === GESTION DES RÉACTIONS ===
  toggleReaction(messageId: string, emoji: string): void {
    // Simulation de la réaction - à implémenter avec le vrai service
    const message = this.messages.find((m) => m.id === messageId);
    if (message) {
      if (!(message as any).reactions) (message as any).reactions = [];
      const existingReaction = (message as any).reactions.find(
        (r: any) => r.emoji === emoji
      );
      if (existingReaction) {
        existingReaction.count = (existingReaction.count || 0) + 1;
      } else {
        (message as any).reactions.push({
          emoji,
          count: 1,
          users: [this.currentUserId],
        });
      }
    }
  }

  addReaction(messageId: string, emoji: string): void {
    this.toggleReaction(messageId, emoji);
    this.showReactionPicker = {};
  }

  // === GESTION DE L'ÉDITION ===
  startEditMessage(message: any): void {
    this.editingMessageId = message.id;
    this.editingContent = message.content || '';
    this.showMessageOptions = {};
  }

  cancelEdit(): void {
    this.editingMessageId = null;
    this.editingContent = '';
  }

  saveEdit(messageId: string): void {
    if (this.editingContent?.trim()) {
      this.MessageService.editMessage(
        messageId,
        this.editingContent.trim()
      ).subscribe({
        next: () => {
          this.cancelEdit();
          // Recharger la conversation pour obtenir les messages mis à jour
          if (this.conversation?.id) {
            this.getConversation(this.conversation.id);
          }
        },
        error: (error) => {
          console.error('Erreur lors de la modification:', error);
        },
      });
    }
  }

  onEditKeyDown(event: KeyboardEvent, messageId: string): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.saveEdit(messageId);
    } else if (event.key === 'Escape') {
      this.cancelEdit();
    }
  }

  // === GESTION DE LA SUPPRESSION ===
  deleteMessage(messageId: string): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {
      this.MessageService.deleteMessage(messageId).subscribe({
        next: () => {
          // Recharger la conversation pour obtenir les messages mis à jour
          if (this.conversation?.id) {
            this.getConversation(this.conversation.id);
          }
          this.showMessageOptions = {};
        },
        error: (error) => {
          console.error('Erreur lors de la suppression:', error);
        },
      });
    }
  }

  // === GESTION DES APPELS ===
  startCall(type: CallType): void {
    if (!this.otherParticipant?.id) return;

    this.MessageService.initiateCall(this.otherParticipant.id, type).subscribe({
      next: (call) => {
        this.activeCall = call;
        this.showActiveCallModal = true;
        this.startCallTimer();
      },
      error: (error) => {
        console.error("Erreur lors de l'initiation de l'appel:", error);
        this.toastService.showError("Impossible d'initier l'appel");
      },
    });
  }

  private startCallTimer(): void {
    this.callDuration = 0;
    this.callTimer = setInterval(() => {
      this.callDuration++;
    }, 1000);
  }

  endCall(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
    this.activeCall = null;
    this.showActiveCallModal = false;
    this.callDuration = 0;
  }

  toggleMute(): void {
    this.isCallMuted = !this.isCallMuted;
  }

  toggleVideo(): void {
    this.isVideoEnabled = !this.isVideoEnabled;
  }

  // === MÉTHODES UTILITAIRES ===
  scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  formatMessageTime(timestamp: any): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  // === FORMATAGE FICHIER (SUPPRIMÉ - DÉJÀ IMPLÉMENTÉ PLUS BAS) ===

  trackByMessageId(index: number, message: any): string {
    return message?.id || index.toString();
  }

  // === GESTION DE L'INTERFACE ===
  toggleSearch(): void {
    this.showSearch = !this.showSearch;
    if (!this.showSearch) {
      this.clearSearch();
    }
  }

  toggleNotifications(): void {
    this.showNotificationPanel = !this.showNotificationPanel;
  }

  toggleConversationInfo(): void {
    this.showConversationInfo = !this.showConversationInfo;
  }

  toggleThemeSelector(): void {
    this.showThemeSelector = !this.showThemeSelector;
  }

  toggleMainMenu(): void {
    this.showMainMenu = !this.showMainMenu;
  }

  toggleAdvancedSearch(): void {
    this.showAdvancedSearch = !this.showAdvancedSearch;
  }

  toggleStatusSelector(): void {
    this.showStatusSelector = !this.showStatusSelector;
  }

  toggleUserStatusPanel(): void {
    this.showUserStatusPanel = !this.showUserStatusPanel;
  }

  // === GESTION DE LA RECHERCHE (SUPPRIMÉ - DÉJÀ IMPLÉMENTÉ PLUS BAS) ===

  highlightSearchTerm(content: string): string {
    if (!this.searchQuery || !content) return content;
    const regex = new RegExp(`(${this.searchQuery})`, 'gi');
    return content.replace(
      regex,
      '<mark class="whatsapp-search-highlight">$1</mark>'
    );
  }

  scrollToMessage(messageId: string): void {
    this.highlightedMessageId = messageId;
    // Logique pour faire défiler vers le message
    setTimeout(() => {
      this.highlightedMessageId = '';
    }, 3000);
  }

  // === GESTION DES MESSAGES ÉPINGLÉS ===
  showAllPinnedMessages(): void {
    // Afficher tous les messages épinglés
  }

  closePinnedMessages(): void {
    this.pinnedMessages = [];
  }

  togglePinMessage(messageId: string): void {
    const message = this.messages.find((m) => m.id === messageId);
    if (message) {
      message.pinned = !message.pinned;
      if (message.pinned) {
        this.pinnedMessages.push(message);
      } else {
        this.pinnedMessages = this.pinnedMessages.filter(
          (m) => m.id !== messageId
        );
      }
    }
    this.showMessageOptions = {};
  }

  // === GESTION DES OPTIONS DE MESSAGE ===
  toggleMessageOptions(messageId: string): void {
    if (this.showMessageOptions[messageId]) {
      this.showMessageOptions = {};
    } else {
      this.showMessageOptions = { [messageId]: true };
    }
  }

  toggleReactionPicker(messageId: string): void {
    if (this.showReactionPicker[messageId]) {
      this.showReactionPicker = {};
    } else {
      this.showReactionPicker = { [messageId]: true };
    }
  }

  // === GESTION DES ACTIONS DE MESSAGE ===
  replyToMessage(message: any): void {
    this.replyingToMessage = message;
    this.showMessageOptions = {};
  }

  forwardMessage(message: any): void {
    this.forwardingMessage = message;
    this.showForwardModal = true;
    this.showMessageOptions = {};
  }

  copyMessage(message: any): void {
    if (navigator.clipboard && message.content) {
      navigator.clipboard.writeText(message.content);
      this.toastService.showSuccess('Message copié');
    }
    this.showMessageOptions = {};
  }

  selectMessage(messageId: string): void {
    this.selectionMode = true;
    this.toggleMessageSelection(messageId);
    this.showMessageOptions = {};
  }

  toggleMessageSelection(messageId: string): void {
    if (this.selectedMessages.has(messageId)) {
      this.selectedMessages.delete(messageId);
    } else {
      this.selectedMessages.add(messageId);
    }

    if (this.selectedMessages.size === 0) {
      this.selectionMode = false;
    }
  }

  private _selectedMessages: string[] = [];

  // === GESTION DES ÉMOJIS ===
  openEmojiPicker(messageId?: string): void {
    this.showEmojiPicker = true;
    if (messageId) {
      this.showReactionPicker = { [messageId]: true };
    }
  }

  closeEmojiPicker(): void {
    this.showEmojiPicker = false;
    this.showReactionPicker = {};
  }

  selectEmojiCategory(categoryId: string): void {
    this.selectedEmojiCategory = categoryId;
  }

  addEmojiToMessage(emoji: string): void {
    const currentContent = this.messageForm.get('content')?.value || '';
    this.messageForm.get('content')?.setValue(currentContent + emoji);
  }

  // === GESTION DES PIÈCES JOINTES ===
  toggleAttachmentMenu(): void {
    this.showAttachmentMenu = !this.showAttachmentMenu;
  }

  openFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  // === GESTION DES MÉDIAS ===
  openImageViewer(message: any): void {
    this.showImageViewer = true;
    this.currentImageIndex = 0;
    this.imageGallery = [message];
  }

  closeImageViewer(): void {
    this.showImageViewer = false;
    this.imageGallery = [];
  }

  // === GESTION DES MESSAGES VOCAUX ===
  toggleVoicePlayback(messageId: string): void {
    if (this.playingVoiceId === messageId) {
      this.playingVoiceId = '';
    } else {
      this.playingVoiceId = messageId;
    }
  }

  // === ENREGISTREMENT VOCAL COMPLET ===
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private recordingTimer: any = null;

  async startVoiceRecording(): Promise<void> {
    try {
      // Demander permission pour le microphone
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      this.audioChunks = [];
      this.isRecordingVoice = true;
      this.voiceRecordingDuration = 0;
      this.voiceRecordingState = 'recording';

      // Démarrer le timer
      this.recordingTimer = setInterval(() => {
        this.voiceRecordingDuration++;
        this.cdr.detectChanges();
      }, 1000);

      // Gérer les données audio
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      // Gérer la fin d'enregistrement
      this.mediaRecorder.onstop = () => {
        this.processRecordedAudio();
      };

      // Démarrer l'enregistrement
      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms

      this.toastService.showSuccess('Enregistrement vocal démarré');
    } catch (error) {
      console.error("Erreur lors du démarrage de l'enregistrement:", error);
      this.toastService.showError("Impossible d'accéder au microphone");
      this.cancelVoiceRecording();
    }
  }

  stopVoiceRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();

      // Arrêter le stream
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
    }

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }

    this.isRecordingVoice = false;
    this.voiceRecordingState = 'processing';
  }

  cancelVoiceRecording(): void {
    if (this.mediaRecorder) {
      if (this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop();
      }
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
      this.mediaRecorder = null;
    }

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }

    this.isRecordingVoice = false;
    this.voiceRecordingDuration = 0;
    this.voiceRecordingState = 'idle';
    this.audioChunks = [];
  }

  private async processRecordedAudio(): Promise<void> {
    try {
      if (this.audioChunks.length === 0) {
        this.cancelVoiceRecording();
        return;
      }

      // Créer le blob audio
      const audioBlob = new Blob(this.audioChunks, {
        type: 'audio/webm;codecs=opus',
      });

      // Vérifier la durée minimale (1 seconde)
      if (this.voiceRecordingDuration < 1) {
        this.toastService.showWarning(
          'Enregistrement trop court (minimum 1 seconde)'
        );
        this.cancelVoiceRecording();
        return;
      }

      // Créer un fichier
      const audioFile = new File([audioBlob], `voice_${Date.now()}.webm`, {
        type: 'audio/webm;codecs=opus',
      });

      // Envoyer le message vocal
      await this.sendVoiceMessage(audioFile);

      this.toastService.showSuccess('Message vocal envoyé');
    } catch (error) {
      console.error("Erreur lors du traitement de l'audio:", error);
      this.toastService.showError("Erreur lors de l'envoi du message vocal");
    } finally {
      this.voiceRecordingState = 'idle';
      this.voiceRecordingDuration = 0;
      this.audioChunks = [];
    }
  }

  private async sendVoiceMessage(audioFile: File): Promise<void> {
    try {
      if (!this.conversation?.id) {
        throw new Error('Aucune conversation sélectionnée');
      }

      // Upload du fichier audio
      const formData = new FormData();
      formData.append('file', audioFile);
      formData.append('type', 'voice');
      formData.append('duration', this.voiceRecordingDuration.toString());

      // Envoyer via le service de messages
      this.MessageService.sendVoiceMessage(
        this.conversation.id,
        audioFile,
        this.voiceRecordingDuration.toString()
      ).subscribe({
        next: (message) => {
          // Ajouter le message à la liste
          this.messages.push(message);
          this.scrollToBottom();
        },
        error: (error) => {
          console.error("Erreur lors de l'envoi du message vocal:", error);
          throw error;
        },
      });
    } catch (error) {
      throw error;
    }
  }

  formatRecordingDuration(duration: number): string {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  // === GESTION DES THÈMES ===
  changeTheme(theme: string): void {
    this.selectedTheme = theme;
    this.showThemeSelector = false;
  }

  getThemeOptions(): any[] {
    return this.c.themes;
  }

  // === GESTION DU STATUT ===
  updateUserStatus(status: string): void {
    this.isUpdatingStatus = true;
    // Simuler une mise à jour
    setTimeout(() => {
      this.isUpdatingStatus = false;
    }, 1000);
  }

  getStatusOptions(): any[] {
    return Object.entries(this.c.status).map(([key, value]) => ({
      key,
      ...value,
    }));
  }

  getStatusIcon(status: string): string {
    return (
      this.c.status[status as keyof typeof this.c.status]?.icon ||
      'fas fa-circle'
    );
  }

  getStatusColor(status: string): string {
    return (
      this.c.status[status as keyof typeof this.c.status]?.color ||
      'text-gray-500'
    );
  }

  getStatusText(status: string): string {
    return (
      this.c.status[status as keyof typeof this.c.status]?.text || 'Inconnu'
    );
  }

  // === MÉTHODES POUR LE TEMPLATE ===
  formatMessageContent(content: string | undefined): string {
    if (!content) return '';
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>');
  }

  onImageLoad(event: any): void {
    // Gérer le chargement d'image
  }

  onImageError(event: any): void {
    event.target.src = 'assets/images/image-error.png';
  }

  onVideoLoaded(event: any, message?: any): void {
    // Gérer le chargement vidéo
  }

  // === MÉTHODES POUR LES ANALYTICS ===
  openAnalyticsDashboard(): void {
    this.showAnalyticsDashboard = true;
  }

  closeAnalyticsDashboard(): void {
    this.showAnalyticsDashboard = false;
  }

  // === MÉTHODES POUR LES INTÉGRATIONS ===
  openIntegrations(): void {
    this.showIntegrations = true;
  }

  closeIntegrations(): void {
    this.showIntegrations = false;
  }

  // === MÉTHODES POUR LES RÉPONSES RAPIDES ===
  sendQuickMessage(content: string): void {
    const control = this.messageForm.get('content');
    if (control) {
      control.setValue(content);
      this.sendMessage();
    }
  }

  // === MÉTHODES POUR LA COMPATIBILITÉ ===
  initiateCall(type: CallType): void {
    this.startCall(type);
  }

  // === CORRECTION DES MÉTHODES MANQUANTES ===
  private generatePreviews(): void {
    this.attachmentFiles.forEach((file) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.previewUrl = e.target?.result || null;
        };
        reader.readAsDataURL(file);
      }
    });
  }

  private loadCurrentUser(): void {
    try {
      // Utiliser getCurrentUser de manière synchrone
      const user = this.authUserService.getCurrentUser();
      if (user) {
        this.currentUserId = user.id || null;
        this.currentUsername = user.username || 'You';
      } else {
        // Fallback si pas d'utilisateur connecté
        this.currentUserId = null;
        this.currentUsername = 'You';
      }
    } catch (error) {
      console.error("Erreur lors du chargement de l'utilisateur:", error);
      this.currentUserId = null;
      this.currentUsername = 'You';
    }
  }

  // === MÉTHODES SUPPLÉMENTAIRES POUR LE TEMPLATE ===

  // Méthodes pour les types de messages
  getMessageType(message: any): string {
    if (message.content && !message.attachments?.length) return 'text';
    if (message.attachments?.some((att: any) => att.type === 'image'))
      return 'image';
    if (message.attachments?.some((att: any) => att.type === 'voice'))
      return 'voice';
    if (message.attachments?.some((att: any) => att.type === 'file'))
      return 'file';
    if (message.attachments?.some((att: any) => att.type === 'video'))
      return 'video';
    if (message.type === 'location') return 'location';
    if (message.type === 'contact') return 'contact';
    return 'text';
  }

  // Méthodes pour les médias
  getImageUrl(message: any): string {
    return (
      message.imageUrl ||
      message.attachments?.find((att: any) => att.type === 'image')?.url ||
      ''
    );
  }

  getVideoUrl(message: any): string {
    return (
      message.videoUrl ||
      message.attachments?.find((att: any) => att.type === 'video')?.url ||
      ''
    );
  }

  getVideoThumbnail(message: any): string {
    return message.thumbnailUrl || '';
  }

  getVideoDuration(message: any): string {
    return message.duration || '00:00';
  }

  // Méthodes pour les fichiers
  getFileSize(message: any): string {
    return this.formatFileSize(message.size || 0);
  }

  getFileIcon(message: any): string {
    const fileType = message.fileType || message.attachments?.[0]?.type || '';
    const icons: any = {
      'application/pdf': 'fas fa-file-pdf',
      'application/msword': 'fas fa-file-word',
      'application/vnd.ms-excel': 'fas fa-file-excel',
      'application/vnd.ms-powerpoint': 'fas fa-file-powerpoint',
      'text/': 'fas fa-file-alt',
      'image/': 'fas fa-file-image',
      'video/': 'fas fa-file-video',
      'audio/': 'fas fa-file-audio',
    };

    for (const [type, icon] of Object.entries(icons)) {
      if (fileType.startsWith(type)) return icon as string;
    }
    return 'fas fa-file';
  }

  getFileType(message: any): string {
    const fileType = message.fileType || message.attachments?.[0]?.type || '';
    return fileType.split('/')[1]?.toUpperCase() || 'FICHIER';
  }

  // Méthodes pour les messages vocaux
  formatVoiceDuration(current: number, total: number): string {
    const formatTime = (seconds: number) => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    };
    return `${formatTime(current)} / ${formatTime(total)}`;
  }

  getVoiceWaveform(message: any): any[] {
    return Array.from({ length: 20 }, (_, i) => ({
      height: Math.random() * 20 + 5,
      active: false,
      played: false,
    }));
  }

  getVoiceProgress(messageId: string): number {
    return 0;
  }

  getVoiceCurrentTime(messageId: string): number {
    return 0;
  }

  getVoiceTotalDuration(messageId: string): number {
    return 0;
  }

  getVoiceSpeed(messageId: string): string {
    return '1x';
  }

  // Méthodes pour les états
  isImageLoading(message: any): boolean {
    return false;
  }

  isVoiceLoading(messageId: string): boolean {
    return false;
  }

  isFileDownloading(message: any): boolean {
    return false;
  }

  getImageLoadingProgress(message: any): number {
    return 0;
  }

  getFileDownloadProgress(message: any): number {
    return 0;
  }

  getImageDimensions(message: any): string {
    return '';
  }

  // Méthodes pour les actions
  downloadFile(message: any): void {
    // Télécharger le fichier
  }

  previewFile(message: any): void {
    // Prévisualiser le fichier
  }

  canPreviewFile(message: any): boolean {
    const previewableTypes = [
      'image/',
      'video/',
      'audio/',
      'text/',
      'application/pdf',
    ];
    return previewableTypes.some((type) => message.type?.startsWith(type));
  }

  hasVideo(message: any): boolean {
    return (
      message.type === 'video' ||
      (message.attachments &&
        message.attachments.some((att: any) => att.type === 'video'))
    );
  }

  getEmojiName(emoji: string): string {
    const emojiNames: any = {
      '👍': 'Pouce levé',
      '❤️': 'Cœur',
      '😂': 'Rire',
      '😮': 'Surprise',
      '😢': 'Triste',
      '😡': 'Colère',
    };
    return emojiNames[emoji] || emoji;
  }

  // Méthodes pour la localisation
  openLocationViewer(message: any): void {
    // Ouvrir le visualiseur de localisation
  }

  getLocationMapUrl(message: any): string {
    return message.mapUrl || '';
  }

  getLocationName(message: any): string {
    return message.locationName || 'Localisation';
  }

  getLocationAddress(message: any): string {
    return message.address || '';
  }

  // Méthodes pour les contacts
  openContactViewer(message: any): void {
    // Ouvrir le visualiseur de contact
  }

  getContactAvatar(message: any): string {
    return message.contactAvatar || 'assets/images/default-avatar.png';
  }

  getContactName(message: any): string {
    return message.contactName || 'Contact';
  }

  getContactPhone(message: any): string {
    return message.contactPhone || '';
  }

  // Méthodes pour les lecteurs
  openVideoPlayer(message: any): void {
    // Ouvrir le lecteur vidéo
  }

  seekVoiceMessage(messageId: string, event: any): void {
    // Implémentation de la recherche dans le message vocal
  }

  changeVoiceSpeed(messageId: string): void {
    // Changer la vitesse de lecture
  }

  // Méthodes pour les événements
  onScroll(event: any): void {
    // Gérer le défilement
    const element = event.target;
    this.showScrollToBottom =
      element.scrollTop < element.scrollHeight - element.clientHeight - 100;
  }

  loadMoreMessages(): void {
    if (!this.conversation?.id || !this.hasMoreMessages) return;

    this.isLoadingMore = true;
    this.currentPage++;

    // Charger plus de messages en utilisant getConversation avec pagination
    this.MessageService.getConversation(
      this.conversation.id,
      this.MAX_MESSAGES_TO_LOAD,
      this.currentPage
    ).subscribe({
      next: (conversation) => {
        if (conversation.messages && conversation.messages.length > 0) {
          // Ajouter les nouveaux messages au début de la liste (messages plus anciens)
          this.messages = [...conversation.messages, ...this.messages];
          this.hasMoreMessages =
            conversation.messages.length === this.MAX_MESSAGES_TO_LOAD;
        } else {
          this.hasMoreMessages = false;
        }
        this.isLoadingMore = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement de plus de messages:', error);
        this.isLoadingMore = false;
      },
    });
  }

  clearConversation(): void {
    if (confirm('Êtes-vous sûr de vouloir vider cette conversation ?')) {
      this.messages = [];
    }
  }

  exportConversation(): void {
    // Exporter la conversation
  }

  // Méthodes pour les en-têtes
  getHeaderActions(): any[] {
    return [
      {
        icon: 'fas fa-search',
        title: 'Rechercher',
        onClick: () => this.toggleSearch(),
        class: 'search-btn',
        isActive: this.showSearch,
      },
      {
        icon: 'fas fa-bell',
        title: 'Notifications',
        onClick: () => this.toggleNotifications(),
        class: 'notification-btn',
        badge:
          this.unreadNotificationsCount > 0
            ? {
                count: this.unreadNotificationsCount,
                class: 'bg-red-500',
                animate: true,
              }
            : null,
      },
      {
        icon: 'fas fa-phone',
        title: 'Appel audio',
        onClick: () => this.startCall('AUDIO' as CallType),
        class: 'call-btn',
      },
      {
        icon: 'fas fa-video',
        title: 'Appel vidéo',
        onClick: () => this.startCall('VIDEO' as CallType),
        class: 'video-btn',
      },
    ];
  }

  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===

  goBackToConversations(): void {
    this.router.navigate(['/front/messages']);
  }

  openUserProfile(userId: string): void {
    // Ouvrir le profil utilisateur
  }

  formatLastActive(lastActive: any): string {
    if (!lastActive) return 'Hors ligne';
    const date = new Date(lastActive);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return 'En ligne';
    if (minutes < 60) return `Il y a ${minutes} min`;
    if (minutes < 1440) return `Il y a ${Math.floor(minutes / 60)} h`;
    return `Il y a ${Math.floor(minutes / 1440)} j`;
  }

  toggleConversationSettings(): void {
    this.showConversationSettings = !this.showConversationSettings;
  }

  shouldShowDateSeparator(index: number): boolean {
    if (index === 0) return true;
    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];

    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;

    const currentDate = new Date(currentMessage.timestamp).toDateString();
    const previousDate = new Date(previousMessage.timestamp).toDateString();

    return currentDate !== previousDate;
  }

  formatDateSeparator(timestamp: any): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) return "Aujourd'hui";
    if (date.toDateString() === yesterday.toDateString()) return 'Hier';

    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  getSystemMessageIcon(message: any): string {
    const icons: any = {
      user_joined: 'fas fa-user-plus',
      user_left: 'fas fa-user-minus',
      call_started: 'fas fa-phone',
      call_ended: 'fas fa-phone-slash',
      message_deleted: 'fas fa-trash',
    };
    return icons[message.systemType] || 'fas fa-info-circle';
  }

  onMessageClick(message: any, event: any): void {
    if (this.selectionMode) {
      this.toggleMessageSelection(message.id);
    }
  }

  onMessageContextMenu(message: any, event: any): void {
    event.preventDefault();
    this.toggleMessageOptions(message.id);
  }

  onMessageHover(messageId: string, isHovering: boolean): void {
    this.hoveredMessageId = isHovering ? messageId : '';
  }

  shouldShowAvatar(index: number): boolean {
    if (index === this.messages.length - 1) return true;
    const currentMessage = this.messages[index];
    const nextMessage = this.messages[index + 1];
    return currentMessage?.sender?.id !== nextMessage?.sender?.id;
  }

  onAvatarError(event: any): void {
    event.target.src = 'assets/images/default-avatar.png';
  }

  isGroupConversation(): boolean {
    return (this.conversation as any)?.type === 'group';
  }

  shouldShowSenderName(index: number): boolean {
    if (!this.isGroupConversation()) return false;
    if (index === 0) return true;
    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];
    return currentMessage?.sender?.id !== previousMessage?.sender?.id;
  }

  getUserColor(userId: string): string {
    const colors = [
      '#ff6b6b',
      '#4ecdc4',
      '#45b7d1',
      '#96ceb4',
      '#feca57',
      '#ff9ff3',
    ];
    const index = userId.charCodeAt(0) % colors.length;
    return colors[index];
  }

  // Propriété Math pour le template
  get Math(): typeof Math {
    return Math;
  }

  // Méthodes pour les fichiers
  downloadSelectedFiles(): void {
    // Télécharger les fichiers sélectionnés
  }

  shareSelectedFiles(): void {
    // Partager les fichiers sélectionnés
  }

  deleteSelectedFiles(): void {
    // Supprimer les fichiers sélectionnés
  }

  // Méthodes pour les analytics
  updateAnalytics(): void {
    // Mettre à jour les analytics
  }

  exportAnalytics(): void {
    // Exporter les analytics
  }

  getMessageTypeIcon(type: string): string {
    const icons: any = {
      text: 'fas fa-comment',
      image: 'fas fa-image',
      video: 'fas fa-video',
      voice: 'fas fa-microphone',
      file: 'fas fa-file',
      location: 'fas fa-map-marker-alt',
      contact: 'fas fa-user',
    };
    return icons[type] || 'fas fa-comment';
  }

  // Méthodes pour les intégrations
  createNewIntegration(): void {
    // Créer une nouvelle intégration
  }

  selectIntegrationCategory(category: string): void {
    this.selectedIntegrationCategory = category;
  }

  getIntegrationCategoryIcon(category: string): string {
    const icons: any = {
      CRM: 'fas fa-users',
      Productivité: 'fas fa-tasks',
      Communication: 'fas fa-comments',
      Analytics: 'fas fa-chart-bar',
      Sécurité: 'fas fa-shield-alt',
    };
    return icons[category] || 'fas fa-puzzle-piece';
  }

  getAvailableIntegrations(): any[] {
    return [
      { id: 1, name: 'Slack', category: 'Communication', icon: 'fab fa-slack' },
      {
        id: 2,
        name: 'Trello',
        category: 'Productivité',
        icon: 'fab fa-trello',
      },
      {
        id: 3,
        name: 'Google Analytics',
        category: 'Analytics',
        icon: 'fab fa-google',
      },
    ];
  }

  configureIntegration(integration: any): void {
    // Configurer l'intégration
  }

  toggleIntegration(integration: any): void {
    // Basculer l'intégration
  }

  getActiveIntegrations(): any[] {
    return this.getAvailableIntegrations().filter((i) => i.active);
  }

  getIntegrationStatusText(status: string): string {
    const texts: any = {
      active: 'Actif',
      inactive: 'Inactif',
      error: 'Erreur',
      pending: 'En attente',
    };
    return texts[status] || 'Inconnu';
  }

  formatLastActivity(lastActivity: any): string {
    return this.formatLastActive(lastActivity);
  }

  viewIntegrationLogs(integration: any): void {
    // Voir les logs d'intégration
  }

  editIntegration(integration: any): void {
    // Modifier l'intégration
  }

  testIntegration(integration: any): void {
    // Tester l'intégration
  }

  removeIntegration(integration: any): void {
    // Supprimer l'intégration
  }

  // Méthodes pour les webhooks
  editWebhook(webhook: any): void {
    // Modifier le webhook
  }

  testWebhook(webhook: any): void {
    // Tester le webhook
  }

  deleteWebhook(webhook: any): void {
    // Supprimer le webhook
  }

  createWebhook(): void {
    // Créer un webhook
  }

  // Méthodes pour les modales
  hasActiveModal(): boolean {
    return (
      this.showImageViewer ||
      this.showForwardModal ||
      this.showAnalyticsDashboard ||
      this.showIntegrations ||
      this.showEmojiPicker ||
      this.showCamera ||
      this.showDrawingTool ||
      this.showFileManager
    );
  }

  closeActiveModal(): void {
    this.showImageViewer = false;
    this.showForwardModal = false;
    this.showAnalyticsDashboard = false;
    this.showIntegrations = false;
    this.showEmojiPicker = false;
    this.showCamera = false;
    this.showDrawingTool = false;
    this.showFileManager = false;
  }

  // === MÉTHODES MANQUANTES POUR LE TEMPLATE HTML ===

  // Méthodes pour l'éditeur d'images
  flipImage(direction: string): void {
    console.log('Flip image:', direction);
  }

  applyImageFilter(filterName: string): void {
    this.selectedImageFilter = filterName;
  }

  updateImageAdjustments(): void {
    // Mettre à jour les ajustements d'image
  }

  resetImageAdjustments(): void {
    this.imageAdjustments = {
      brightness: 0,
      contrast: 0,
      saturation: 0,
      hue: 0,
    };
  }

  addTextToImage(): void {
    if (this.newTextContent.trim()) {
      this.imageTextElements.push({
        id: Date.now().toString(),
        content: this.newTextContent,
        x: 50,
        y: 50,
        fontFamily: this.textFontFamily,
        fontSize: this.textFontSize,
        color: this.textColor,
      });
      this.newTextContent = '';
    }
  }

  // Méthodes pour le gestionnaire de fichiers
  createNewFolder(): void {
    const folderName = prompt('Nom du nouveau dossier:');
    if (folderName) {
      this.fileFolders.push({
        id: Date.now().toString(),
        name: folderName,
        type: 'folder',
        size: 0,
        modifiedAt: new Date(),
      });
    }
  }

  uploadFiles(): void {
    this.openFileInput();
  }

  toggleFileView(): void {
    this.fileViewMode = this.fileViewMode === 'grid' ? 'list' : 'grid';
  }

  closeFileManager(): void {
    this.showFileManager = false;
  }

  selectFolder(folder: any): void {
    this.selectedFolder = folder;
  }

  renameFolder(folder: any): void {
    const newName = prompt('Nouveau nom:', folder.name);
    if (newName) {
      folder.name = newName;
    }
  }

  deleteFolder(folder: any): void {
    if (confirm('Supprimer ce dossier ?')) {
      this.fileFolders = this.fileFolders.filter((f) => f.id !== folder.id);
    }
  }

  getStorageUsagePercentage(): number {
    return 65; // Exemple
  }

  getStorageUsed(): string {
    return '6.5 GB';
  }

  getStorageTotal(): string {
    return '10 GB';
  }

  getTotalFilesCount(): number {
    return 1247;
  }

  navigateToFolder(crumb: any): void {
    this.selectedFolder = crumb;
  }

  onFileSearch(): void {
    // Rechercher dans les fichiers
  }

  applyFileFilters(): void {
    // Appliquer les filtres de fichiers
  }

  sortFiles(): void {
    // Trier les fichiers
  }

  getFilteredFiles(): any[] {
    return [
      {
        id: '1',
        name: 'Document.pdf',
        type: 'file',
        size: 1024000,
        modifiedAt: new Date(),
      },
      {
        id: '2',
        name: 'Image.jpg',
        type: 'image',
        size: 2048000,
        modifiedAt: new Date(),
      },
    ];
  }

  toggleFileSelection(file: any): void {
    const index = this.selectedFiles.indexOf(file.id);
    if (index > -1) {
      this.selectedFiles.splice(index, 1);
    } else {
      this.selectedFiles.push(file.id);
    }
  }

  openFile(file: any): void {
    console.log('Ouvrir fichier:', file.name);
  }

  formatFileDate(date: any): string {
    return new Date(date).toLocaleDateString('fr-FR');
  }

  shareFile(file: any): void {
    console.log('Partager fichier:', file.name);
  }

  deleteFile(file: any): void {
    if (confirm('Supprimer ce fichier ?')) {
      console.log('Fichier supprimé:', file.name);
    }
  }

  // Méthodes pour les propriétés manquantes dans le template
  get forwarded(): boolean {
    return false; // Propriété pour les messages transférés
  }

  // Méthodes pour corriger les erreurs du template
  getOnlineUsersCount(): number {
    return this.onlineUsersCount;
  }

  // Méthodes manquantes pour le template
  truncateText(text: string, maxLength: number): string {
    return text.length > maxLength
      ? text.substring(0, maxLength) + '...'
      : text;
  }

  hasImage(message: any): boolean {
    return (
      message.type === 'image' ||
      (message.attachments &&
        message.attachments.some((a: any) => a.type.startsWith('image/')))
    );
  }

  isVoiceMessage(message: any): boolean {
    return message.type === 'voice' || message.type === 'audio';
  }

  hasFile(message: any): boolean {
    return (
      message.type === 'file' ||
      (message.attachments && message.attachments.length > 0)
    );
  }

  getFileName(message: any): string {
    if (message.attachments && message.attachments.length > 0) {
      return message.attachments[0].name || 'Fichier';
    }
    return 'Fichier';
  }

  getMessageBubbleClass(message: any): string {
    const isOwn = message.sender?.id === this.currentUserId;
    return `whatsapp-message-bubble ${isOwn ? 'own' : 'other'}`;
  }

  isLocationMessage(message: any): boolean {
    return message.type === 'location';
  }

  isContactMessage(message: any): boolean {
    return message.type === 'contact';
  }

  // Méthodes pour la caméra
  closeCamera(): void {
    this.showCamera = false;
  }

  setCameraMode(mode: string): void {
    this.cameraMode = mode;
  }

  toggleCameraFlash(): void {
    this.flashEnabled = !this.flashEnabled;
  }

  switchCamera(): void {
    // Basculer entre caméra avant et arrière
  }

  openGallery(): void {
    this.openFileInput();
  }

  capturePhoto(): void {
    // Capturer une photo
  }

  startVideoRecording(): void {
    this.isRecordingVideo = true;
    this.videoRecordingDuration = 0;
  }

  stopVideoRecording(): void {
    this.isRecordingVideo = false;
  }

  toggleCameraGrid(): void {
    this.showCameraGrid = !this.showCameraGrid;
  }

  formatRecordingTime(duration: number): string {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds
      .toString()
      .padStart(2, '0')}`;
  }

  // Méthodes pour l'éditeur d'images
  closeImageEditor(): void {
    this.showImageEditor = false;
  }

  saveEditedImage(): void {
    // Sauvegarder l'image éditée
  }

  startCropResize(corner: string, event: any): void {
    // Commencer le redimensionnement de crop
  }

  editTextElement(index: number): void {
    // Éditer un élément de texte
  }

  setImageEditorMode(mode: string): void {
    this.imageEditorMode = mode;
  }

  setCropRatio(ratio: string): void {
    this.cropRatio = ratio;
  }

  rotateImage(angle: number): void {
    // Faire tourner l'image
  }

  // Initialiser des données de test
  private initializeTestData(): void {
    // Créer une conversation de test
    this.conversation = {
      id: 'test-conversation',
      participants: [
        { id: 'user1', username: 'Alice' },
        { id: 'user2', username: 'Bob' },
      ],
    } as Conversation;

    // Créer des messages de test
    this.messages = [
      {
        id: '1',
        content: 'Salut ! Comment ça va ?',
        sender: { id: 'user2', username: 'Bob' },
        timestamp: new Date(Date.now() - 3600000),
        conversationId: 'test-conversation',
        type: 'text' as MessageType,
      },
      {
        id: '2',
        content: 'Ça va bien, merci ! Et toi ?',
        sender: { id: this.currentUserId!, username: this.currentUsername },
        timestamp: new Date(Date.now() - 3000000),
        conversationId: 'test-conversation',
        type: 'text' as MessageType,
      },
      {
        id: '3',
        content: "Super ! Tu veux qu'on se voit ce soir ?",
        sender: { id: 'user2', username: 'Bob' },
        timestamp: new Date(Date.now() - 1800000),
        conversationId: 'test-conversation',
        type: 'text' as MessageType,
      },
    ];

    this.otherParticipant = { id: 'user2', username: 'Bob' } as User;
    this.loading = false;
  }

  // === MÉTHODES MANQUANTES POUR CORRIGER LES ERREURS DU TEMPLATE ===

  // Méthodes pour les emojis et stickers
  insertGif(gif: any): void {
    // Insérer un GIF dans le message
    const content = this.messageForm.get('content')?.value || '';
    this.messageForm.patchValue({ content: content + `[GIF:${gif.id}]` });
  }

  manageStickerPacks(): void {
    // Gérer les packs de stickers
  }

  toggleEmojiPicker(): void {
    this.showEmojiPicker = !this.showEmojiPicker;
  }

  // Méthodes pour l'outil de dessin
  openDrawingTool(): void {
    this.showDrawingTool = true;
  }

  closeDrawingTool(): void {
    this.showDrawingTool = false;
  }

  selectDrawingTool(tool: string): void {
    this.selectedDrawingTool = tool;
  }

  selectDrawingColor(color: string): void {
    this.selectedDrawingColor = color;
  }

  undoDrawing(): void {
    // Annuler le dernier trait
  }

  redoDrawing(): void {
    // Refaire le dernier trait annulé
  }

  clearDrawing(): void {
    // Effacer tout le dessin
  }

  startDrawing(event: any): void {
    // Commencer à dessiner
  }

  draw(event: any): void {
    // Dessiner
  }

  stopDrawing(): void {
    // Arrêter de dessiner
  }

  saveDrawing(): void {
    // Sauvegarder le dessin
  }

  // === MÉTHODES POUR LES RÉACTIONS ===
  hasUserReacted(reaction: any, userId: string | null): boolean {
    if (!userId) return false;
    return reaction.userId === userId;
  }

  getReactionTooltip(reaction: any): string {
    return `${reaction.user?.username || 'Utilisateur'} a réagi avec ${
      reaction.emoji
    }`;
  }

  // === MÉTHODES POUR LES PERMISSIONS ===
  canDeleteMessage(message: any): boolean {
    // L'utilisateur peut supprimer ses propres messages ou s'il est admin
    return message.sender?.id === this.currentUserId || this.isAdmin;
  }

  canEditMessage(message: any): boolean {
    // L'utilisateur peut modifier ses propres messages
    return message.sender?.id === this.currentUserId;
  }

  // === MÉTHODES POUR LA SÉLECTION DE MESSAGES ===
  exitSelectionMode(): void {
    this.selectionMode = false;
    this.selectedMessages.clear();
  }

  deleteSelectedMessages(): void {
    if (this.selectedMessages.size === 0) return;

    if (confirm(`Supprimer ${this.selectedMessages.size} message(s) ?`)) {
      // Supprimer les messages sélectionnés
      this.messages = this.messages.filter(
        (m) => !m.id || !this.selectedMessages.has(m.id)
      );
      this.exitSelectionMode();
      this.toastService.showSuccess('Messages supprimés');
    }
  }

  forwardSelectedMessages(): void {
    if (this.selectedMessages.size === 0) return;

    // Ouvrir le modal de transfert
    this.showForwardModal = true;
    this.forwardingMessage = Array.from(this.selectedMessages);
  }

  copySelectedMessages(): void {
    if (this.selectedMessages.size === 0) return;

    const selectedMessagesArray = this.messages.filter(
      (m) => m.id && this.selectedMessages.has(m.id)
    );
    const textToCopy = selectedMessagesArray
      .map((m) => `${m.sender?.username}: ${m.content}`)
      .join('\n');

    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        this.toastService.showSuccess('Messages copiés');
        this.exitSelectionMode();
      })
      .catch(() => {
        this.toastService.showError('Erreur lors de la copie');
      });
  }

  // === MÉTHODES POUR LES RÉPONSES ===
  cancelReply(): void {
    this.replyingToMessage = null;
  }

  // === MÉTHODES POUR LES FICHIERS ===
  removeSelectedFile(): void {
    this.selectedFile = null;
    this.previewUrl = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  // === MÉTHODES POUR L'ENREGISTREMENT VOCAL (SUPPRIMÉES - DÉJÀ IMPLÉMENTÉES PLUS HAUT) ===

  // === MÉTHODES POUR LES SONDAGES ===
  canCreatePoll(): boolean {
    return (
      this.pollQuestion.trim().length > 0 &&
      this.pollOptions.filter((opt) => opt.text.trim().length > 0).length >= 2
    );
  }

  // === MÉTHODES POUR LA LOCALISATION ===
  closeLocationPicker(): void {
    this.showLocationPicker = false;
    this.showLocationSearch = false;
    this.locationSearchQuery = '';
    this.locationSearchResults = [];
    this.selectedLocation = null;
  }

  getCurrentLocation(): void {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          this.selectedLocation = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            name: 'Ma position actuelle',
          };
        },
        (error) => {
          this.toastService.showError("Impossible d'obtenir votre position");
        }
      );
    } else {
      this.toastService.showError('Géolocalisation non supportée');
    }
  }

  searchLocation(): void {
    this.showLocationSearch = true;
    // Simuler une recherche de lieux
    this.locationSearchResults = [
      { name: 'Paris, France', latitude: 48.8566, longitude: 2.3522 },
      { name: 'Lyon, France', latitude: 45.764, longitude: 4.8357 },
      { name: 'Marseille, France', latitude: 43.2965, longitude: 5.3698 },
    ];
  }

  onLocationSearch(): void {
    // Filtrer les résultats selon la recherche
    if (this.locationSearchQuery.trim()) {
      this.locationSearchResults = this.locationSearchResults.filter(
        (location) =>
          location.name
            .toLowerCase()
            .includes(this.locationSearchQuery.toLowerCase())
      );
    }
  }

  selectLocationResult(result: any): void {
    this.selectedLocation = result;
    this.showLocationSearch = false;
    this.locationSearchQuery = '';
  }

  editLocationMessage(): void {
    this.showLocationMessage = true;
  }

  // === PARTAGE DE LOCALISATION (SUPPRIMÉ - DÉJÀ IMPLÉMENTÉ PLUS BAS) ===

  // === MÉTHODES POUR LES CONTACTS ===
  closeContactPicker(): void {
    this.showContactPicker = false;
    this.contactSearchQuery = '';
    this.selectedContactForSharing = null;
  }

  onContactSearch(): void {
    // Filtrer les contacts selon la recherche
    // Implémentation de la recherche de contacts
  }

  getFilteredContactsForSharing(): any[] {
    // Simuler une liste de contacts
    const contacts = [
      {
        id: '1',
        name: 'Alice Martin',
        phone: '+33123456789',
        email: '<EMAIL>',
      },
      {
        id: '2',
        name: 'Bob Dupont',
        phone: '+33987654321',
        email: '<EMAIL>',
      },
      {
        id: '3',
        name: 'Claire Durand',
        phone: '+33555666777',
        email: '<EMAIL>',
      },
    ];

    if (this.contactSearchQuery.trim()) {
      return contacts.filter(
        (contact) =>
          contact.name
            .toLowerCase()
            .includes(this.contactSearchQuery.toLowerCase()) ||
          contact.phone.includes(this.contactSearchQuery) ||
          contact.email
            .toLowerCase()
            .includes(this.contactSearchQuery.toLowerCase())
      );
    }
    return contacts;
  }

  selectContactForSharing(contact: any): void {
    this.selectedContactForSharing = contact;
  }

  viewContactDetails(contact: any): void {
    // Ouvrir les détails du contact
    this.toastService.showInfo(`Détails de ${contact.name}`);
  }

  createNewContact(): void {
    // Ouvrir le formulaire de création de contact
    this.toastService.showInfo("Création d'un nouveau contact");
  }

  // === PARTAGE DE CONTACT (SUPPRIMÉ - DÉJÀ IMPLÉMENTÉ PLUS BAS) ===

  // === MÉTHODES POUR LES EMOJIS ET STICKERS ===
  openStickerPicker(): void {
    this.showStickerPicker = true;
    this.showEmojiPicker = false;
  }

  openGifPicker(): void {
    this.showGifPicker = true;
    this.showEmojiPicker = false;
  }

  onEmojiSearch(): void {
    // Filtrer les emojis selon la recherche
    // Implémentation de la recherche d'emojis
  }

  // === MÉTHODE INSERTÉMOJI SUPPRIMÉE (DÉJÀ IMPLÉMENTÉE PLUS BAS) ===

  getFilteredEmojiCategories(): any[] {
    return this.emojiCategories.filter((category) => {
      if (!this.emojiSearchQuery.trim()) return true;
      return category.name
        .toLowerCase()
        .includes(this.emojiSearchQuery.toLowerCase());
    });
  }

  previewEmoji(emoji: any): void {
    this.previewedEmoji = emoji;
  }

  selectStickerPack(packId: string): void {
    this.selectedStickerPack = packId;
  }

  getSelectedStickerPack(): any {
    return this.stickerPacks.find(
      (pack) => pack.id === this.selectedStickerPack
    );
  }

  insertSticker(sticker: any): void {
    // Créer un message sticker
    const stickerMessage = {
      id: Date.now().toString(),
      type: 'sticker',
      sticker: sticker,
      sender: { id: this.currentUserId!, username: this.currentUsername },
      timestamp: new Date(),
      conversationId: this.conversation?.id,
    };

    this.messages.push(stickerMessage as any);
    this.showStickerPicker = false;
    this.toastService.showSuccess('Sticker envoyé');
  }

  onGifSearch(): void {
    // Filtrer les GIFs selon la recherche
    // Implémentation de la recherche de GIFs
  }

  selectGifCategory(category: string): void {
    this.selectedGifCategory = category;
  }

  getFilteredGifs(): any[] {
    // Simuler une liste de GIFs
    return [
      { id: '1', url: 'gif1.gif', title: 'Happy' },
      { id: '2', url: 'gif2.gif', title: 'Funny' },
      { id: '3', url: 'gif3.gif', title: 'Love' },
    ];
  }

  // === MÉTHODES POUR LES MODALS ===
  isCallMinimized = false;
  isInCall = false;
  inputFocused = false;

  cancelDelete(): void {
    this.showDeleteConfirmModal = false;
  }

  confirmDelete(): void {
    // Logique de suppression
    this.showDeleteConfirmModal = false;
    this.toastService.showSuccess('Message supprimé');
  }

  closeForwardModal(): void {
    this.showForwardModal = false;
    this.forwardingMessage = null;
    this.selectedConversations = [];
  }

  forwardSearchQuery = '';

  getFilteredContacts(): any[] {
    const contacts = [
      { id: '1', name: 'Alice Martin', avatar: 'avatar1.jpg' },
      { id: '2', name: 'Bob Dupont', avatar: 'avatar2.jpg' },
      { id: '3', name: 'Claire Durand', avatar: 'avatar3.jpg' },
    ];

    if (this.forwardSearchQuery.trim()) {
      return contacts.filter((contact) =>
        contact.name
          .toLowerCase()
          .includes(this.forwardSearchQuery.toLowerCase())
      );
    }
    return contacts;
  }

  toggleContactSelection(contactId: string): void {
    const index = this.selectedConversations.indexOf(contactId);
    if (index > -1) {
      this.selectedConversations.splice(index, 1);
    } else {
      this.selectedConversations.push(contactId);
    }
  }

  confirmForward(): void {
    if (this.selectedConversations.length > 0) {
      this.toastService.showSuccess(
        `Message transféré à ${this.selectedConversations.length} contact(s)`
      );
      this.closeForwardModal();
    }
  }

  // === MÉTHODES POUR LE VISUALISEUR D'IMAGES ===
  previousImage(): void {
    if (this.currentImageIndex > 0) {
      this.currentImageIndex--;
    }
  }

  nextImage(): void {
    if (this.currentImageIndex < this.imageGallery.length - 1) {
      this.currentImageIndex++;
    }
  }

  // === MÉTHODES POUR LES TOASTS ===
  getToastIcon(): string {
    const icons = {
      success: 'fas fa-check-circle',
      error: 'fas fa-exclamation-circle',
      warning: 'fas fa-exclamation-triangle',
      info: 'fas fa-info-circle',
    };
    return icons[this.toastType as keyof typeof icons] || 'fas fa-info-circle';
  }

  // === MÉTHODES POUR LES STATISTIQUES ===
  getTotalMessagesCount(): number {
    return this.messages.length;
  }

  getPhotosCount(): number {
    return this.messages.filter((m) =>
      m.attachments?.some((att) => att.type === 'image')
    ).length;
  }

  getFilesCount(): number {
    return this.messages.filter((m) =>
      m.attachments?.some((att) => att.type === 'file')
    ).length;
  }

  // === MÉTHODES POUR LES PARAMÈTRES DE CONVERSATION ===
  updateConversationSettings(): void {
    // Sauvegarder les paramètres de conversation
    this.toastService.showSuccess('Paramètres mis à jour');
  }

  // === MÉTHODES POUR LA TRADUCTION ===
  closeTranslationPanel(): void {
    this.showTranslationPanel = false;
    this.originalTextForTranslation = '';
    this.translatedText = '';
  }

  swapLanguages(): void {
    const temp = this.translationFrom;
    this.translationFrom = this.translationTo;
    this.translationTo = temp;
  }

  copyTranslation(): void {
    if (this.translatedText) {
      navigator.clipboard.writeText(this.translatedText).then(() => {
        this.toastService.showSuccess('Traduction copiée');
      });
    }
  }

  insertTranslation(): void {
    if (this.translatedText) {
      const currentContent = this.messageForm.get('content')?.value || '';
      this.messageForm.patchValue({
        content: currentContent + this.translatedText,
      });
      this.closeTranslationPanel();
    }
  }

  shareTranslation(): void {
    if (this.translatedText) {
      // Créer un message avec la traduction
      const translationMessage = {
        id: Date.now().toString(),
        content: this.translatedText,
        sender: { id: this.currentUserId!, username: this.currentUsername },
        timestamp: new Date(),
        conversationId: this.conversation?.id,
      };

      this.messages.push(translationMessage as any);
      this.closeTranslationPanel();
      this.toastService.showSuccess('Traduction partagée');
    }
  }

  // === MÉTHODES POUR LES SONDAGES ===
  closePollCreator(): void {
    this.showPollCreator = false;
    this.pollQuestion = '';
    this.pollOptions = [{ text: '' }, { text: '' }];
    this.pollSettings = {
      allowMultiple: false,
      anonymous: false,
      showResults: true,
      showVoters: true,
    };
  }

  addPollOption(): void {
    if (this.pollOptions.length < 10) {
      this.pollOptions.push({ text: '' });
    }
  }

  removePollOption(index: number): void {
    if (this.pollOptions.length > 2) {
      this.pollOptions.splice(index, 1);
    }
  }

  createPoll(): void {
    if (this.canCreatePoll()) {
      // Créer un message sondage
      const pollMessage = {
        id: Date.now().toString(),
        type: 'poll',
        poll: {
          question: this.pollQuestion,
          options: this.pollOptions.filter((opt) => opt.text.trim()),
          settings: this.pollSettings,
          votes: [],
          totalVotes: 0,
        },
        sender: { id: this.currentUserId!, username: this.currentUsername },
        timestamp: new Date(),
        conversationId: this.conversation?.id,
      };

      this.messages.push(pollMessage as any);
      this.closePollCreator();
      this.toastService.showSuccess('Sondage créé');
    }
  }

  // === MÉTHODES POUR LES FONCTIONNALITÉS DE BASE ===
  hasMessageContent(): boolean {
    const content = this.messageForm.get('content')?.value;
    return content && content.trim().length > 0;
  }

  canSendMessage(): boolean {
    return this.hasMessageContent() || this.selectedFile !== null;
  }

  searchEmojis(): void {
    // Rechercher des emojis selon emojiSearchQuery
  }

  getFilteredEmojis(): any[] {
    // Simuler une liste d'emojis
    const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'];
    if (this.emojiSearchQuery.trim()) {
      return emojis.filter((emoji) => emoji.includes(this.emojiSearchQuery));
    }
    return emojis;
  }

  // === MÉTHODES POUR LES NOTIFICATIONS ===
  toggleNotificationPanel(): void {
    this.showNotificationPanel = !this.showNotificationPanel;
  }

  onSearchKeyPress(event: any): void {
    if (event.key === 'Enter') {
      this.performSearch();
    }
  }

  toggleSearchBar(): void {
    this.showSearchBar = !this.showSearchBar;
    if (!this.showSearchBar) {
      this.searchQuery = '';
      this.searchResults = [];
    }
  }

  navigateToMessage(messageId: string): void {
    // Naviguer vers un message spécifique
    this.highlightedMessageId = messageId;
  }

  highlightSearchTerms(content: string, query: string): string {
    if (!query) return content;
    const regex = new RegExp(`(${query})`, 'gi');
    return content.replace(regex, '<mark>$1</mark>');
  }

  // === MÉTHODES POUR LES MESSAGES ÉPINGLÉS ===
  getPinnedMessagesCount(): number {
    return this.messages.filter((m) => m.pinned).length;
  }

  togglePinnedMessages(): void {
    this.showPinnedMessages = !this.showPinnedMessages;
  }

  scrollToPinnedMessage(messageId: string): void {
    this.scrollToMessage(messageId);
  }

  formatMessageDate(date: Date | string): string {
    return this.formatMessageTime(date);
  }

  // === MÉTHODES POUR LES APPELS ===
  declineCall(): void {
    this.incomingCall = null;
    this.toastService.showInfo('Appel refusé');
  }

  acceptCall(): void {
    this.incomingCall = null;
    this.isInCall = true;
    this.toastService.showSuccess('Appel accepté');
  }

  formatCallDuration(duration: number): string {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  getCallStatusText(): string {
    if (this.isInCall) return 'En cours...';
    return 'Connexion...';
  }

  toggleCallMinimize(): void {
    this.isCallMinimized = !this.isCallMinimized;
  }

  showCallControls = true;

  // === MÉTHODES POUR LE FORMATAGE DE TEXTE ===
  applyFormatting(type: string): void {
    const content = this.messageForm.get('content')?.value || '';
    let formattedContent = content;

    switch (type) {
      case 'bold':
        formattedContent = `*${content}*`;
        break;
      case 'italic':
        formattedContent = `_${content}_`;
        break;
      case 'strikethrough':
        formattedContent = `~${content}~`;
        break;
      case 'underline':
        formattedContent = `__${content}__`;
        break;
      case 'code':
        formattedContent = `\`${content}\``;
        break;
      case 'quote':
        formattedContent = `> ${content}`;
        break;
      case 'spoiler':
        formattedContent = `||${content}||`;
        break;
    }

    this.messageForm.patchValue({ content: formattedContent });
  }

  hasFormatting(type: string): boolean {
    const content = this.messageForm.get('content')?.value || '';
    switch (type) {
      case 'bold':
        return content.includes('*');
      case 'italic':
        return content.includes('_');
      case 'strikethrough':
        return content.includes('~');
      case 'underline':
        return content.includes('__');
      case 'code':
        return content.includes('`');
      case 'quote':
        return content.includes('>');
      case 'spoiler':
        return content.includes('||');
      default:
        return false;
    }
  }

  insertLink(): void {
    const url = prompt("Entrez l'URL du lien :");
    if (url) {
      const text = prompt('Entrez le texte du lien :') || url;
      const content = this.messageForm.get('content')?.value || '';
      this.messageForm.patchValue({
        content: content + `[${text}](${url})`,
      });
    }
  }

  insertTable(): void {
    const table =
      '\n| Colonne 1 | Colonne 2 |\n|-----------|----------|\n| Cellule 1 | Cellule 2 |\n';
    const content = this.messageForm.get('content')?.value || '';
    this.messageForm.patchValue({
      content: content + table,
    });
  }

  insertList(type: string): void {
    const content = this.messageForm.get('content')?.value || '';
    const listItem =
      type === 'ul' ? '• Élément de liste\n' : '1. Élément de liste\n';
    this.messageForm.patchValue({
      content: content + '\n' + listItem,
    });
  }

  insertMentionSymbol(): void {
    const content = this.messageForm.get('content')?.value || '';
    this.messageForm.patchValue({
      content: content + '@',
    });
  }

  insertHashtagSymbol(): void {
    const content = this.messageForm.get('content')?.value || '';
    this.messageForm.patchValue({
      content: content + '#',
    });
  }

  clearFormatting(): void {
    const content = this.messageForm.get('content')?.value || '';
    const cleanContent = content
      .replace(/\*([^*]+)\*/g, '$1') // Bold
      .replace(/_([^_]+)_/g, '$1') // Italic
      .replace(/~([^~]+)~/g, '$1') // Strikethrough
      .replace(/__([^_]+)__/g, '$1') // Underline
      .replace(/`([^`]+)`/g, '$1') // Code
      .replace(/> /g, '') // Quote
      .replace(/\|\|([^|]+)\|\|/g, '$1'); // Spoiler

    this.messageForm.patchValue({ content: cleanContent });
  }

  toggleFormattingToolbar(): void {
    this.showFormattingToolbar = !this.showFormattingToolbar;
  }

  // === MÉTHODES POUR LES CORRECTIONS AUTOMATIQUES ===
  hideAutoCorrectSuggestions(): void {
    this.autoCorrectSuggestions = [];
  }

  applyCorrection(original: string, correction: string): void {
    const content = this.messageForm.get('content')?.value || '';
    const correctedContent = content.replace(original, correction);
    this.messageForm.patchValue({ content: correctedContent });
    this.hideAutoCorrectSuggestions();
  }

  ignoreCorrection(original: string): void {
    this.autoCorrectSuggestions = this.autoCorrectSuggestions.filter(
      (s) => s.original !== original
    );
  }

  // === MÉTHODES POUR LES FICHIERS ===
  triggerFileInput(type: string): void {
    const input = document.createElement('input');
    input.type = 'file';

    switch (type) {
      case 'image':
        input.accept = 'image/*';
        break;
      case 'video':
        input.accept = 'video/*';
        break;
      case 'audio':
        input.accept = 'audio/*';
        break;
      case 'document':
        input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx';
        break;
    }

    input.onchange = (event: any) => {
      this.onFileSelected(event);
    };

    input.click();
  }

  // === MÉTHODES POUR L'INPUT DE MESSAGE ===
  getInputPlaceholder(): string {
    if (this.replyingToMessage) {
      return `Répondre à ${this.replyingToMessage.sender?.username}...`;
    }
    if (this.isRecordingVoice) {
      return 'Enregistrement en cours...';
    }
    return 'Tapez votre message...';
  }

  onInputChange(event: any): void {
    const content = event.target.value;
    this.messageForm.patchValue({ content });

    // Détecter les mentions, hashtags, etc.
    this.detectMentions(content);
    this.detectHashtags(content);
    this.detectCommands(content);

    // Gestion de la frappe
    this.handleTyping();
  }

  onInputKeyDown(event: any): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    } else if (event.key === 'ArrowUp' && this.mentionSuggestions.length > 0) {
      event.preventDefault();
      this.selectedMentionIndex = Math.max(0, this.selectedMentionIndex - 1);
    } else if (
      event.key === 'ArrowDown' &&
      this.mentionSuggestions.length > 0
    ) {
      event.preventDefault();
      this.selectedMentionIndex = Math.min(
        this.mentionSuggestions.length - 1,
        this.selectedMentionIndex + 1
      );
    } else if (event.key === 'Tab' && this.mentionSuggestions.length > 0) {
      event.preventDefault();
      this.insertMention(this.mentionSuggestions[this.selectedMentionIndex]);
    }
  }

  onInputKeyUp(event: any): void {
    // Gestion des suggestions en temps réel
    this.updateSuggestions();
  }

  onInputFocus(): void {
    this.inputFocused = true;
  }

  onInputBlur(): void {
    this.inputFocused = false;
    // Masquer les suggestions après un délai
    setTimeout(() => {
      this.mentionSuggestions = [];
      this.hashtagSuggestions = [];
      this.commandSuggestions = [];
    }, 200);
  }

  onInputPaste(event: any): void {
    const clipboardData = event.clipboardData || (window as any).clipboardData;
    const pastedData = clipboardData.getData('text');

    // Traiter le contenu collé
    if (pastedData.includes('http')) {
      // Détecter les liens
      this.detectLinks(pastedData);
    }
  }

  onInputScroll(event: any): void {
    // Gérer le défilement de l'input si nécessaire
  }

  // === MÉTHODES POUR LES MENTIONS ET SUGGESTIONS ===
  detectMentions(content: string): void {
    const mentionMatch = content.match(/@(\w*)$/);
    if (mentionMatch) {
      const query = mentionMatch[1];
      this.mentionSuggestions = this.getFilteredUsers(query);
      this.selectedMentionIndex = 0;
    } else {
      this.mentionSuggestions = [];
    }
  }

  detectHashtags(content: string): void {
    const hashtagMatch = content.match(/#(\w*)$/);
    if (hashtagMatch) {
      const query = hashtagMatch[1];
      this.hashtagSuggestions = this.getFilteredHashtags(query);
    } else {
      this.hashtagSuggestions = [];
    }
  }

  detectCommands(content: string): void {
    const commandMatch = content.match(/\/(\w*)$/);
    if (commandMatch) {
      const query = commandMatch[1];
      this.commandSuggestions = this.getFilteredCommands(query);
    } else {
      this.commandSuggestions = [];
    }
  }

  detectLinks(content: string): void {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const links = content.match(urlRegex);
    if (links) {
      // Traiter les liens détectés
      console.log('Liens détectés:', links);
    }
  }

  insertMention(user: any): void {
    const content = this.messageForm.get('content')?.value || '';
    const newContent = content.replace(/@\w*$/, `@${user.username} `);
    this.messageForm.patchValue({ content: newContent });
    this.mentionSuggestions = [];
  }

  insertHashtag(hashtag: any): void {
    const content = this.messageForm.get('content')?.value || '';
    const newContent = content.replace(/#\w*$/, `#${hashtag.name} `);
    this.messageForm.patchValue({ content: newContent });
    this.hashtagSuggestions = [];
  }

  insertCommand(command: any): void {
    const content = this.messageForm.get('content')?.value || '';
    const newContent = content.replace(/\/\w*$/, `/${command.name} `);
    this.messageForm.patchValue({ content: newContent });
    this.commandSuggestions = [];
  }

  setSelectedMentionIndex(index: number): void {
    this.selectedMentionIndex = index;
  }

  updateSuggestions(): void {
    const content = this.messageForm.get('content')?.value || '';
    this.detectMentions(content);
    this.detectHashtags(content);
    this.detectCommands(content);
  }

  // === MÉTHODES POUR LE COMPTAGE DE CARACTÈRES ===
  getCharacterCount(): number {
    const content = this.messageForm.get('content')?.value || '';
    return content.length;
  }

  hasFormattedText(): boolean {
    const content = this.messageForm.get('content')?.value || '';
    return (
      content.includes('*') ||
      content.includes('_') ||
      content.includes('`') ||
      content.includes('~')
    );
  }

  // === MÉTHODES POUR LA TRADUCTION ===
  getLanguageName(languageCode: string): string {
    const languages: { [key: string]: string } = {
      fr: 'Français',
      en: 'Anglais',
      es: 'Espagnol',
      de: 'Allemand',
      it: 'Italien',
    };
    return languages[languageCode] || languageCode;
  }

  translateMessage(): void {
    this.showTranslationPanel = true;
    const content = this.messageForm.get('content')?.value || '';
    this.originalTextForTranslation = content;
    // Simuler une traduction
    this.translatedText = `[Traduction] ${content}`;
  }

  showAutoCorrections(): void {
    // Afficher les corrections automatiques
    this.autoCorrectSuggestions = [
      { original: 'salut', corrections: ['Salut'] },
      { original: 'ca va', corrections: ['Ça va'] },
    ];
  }

  // === MÉTHODES UTILITAIRES MANQUANTES ===
  handleTyping(): void {
    // Gérer l'indicateur de frappe
    if (!this.isTyping) {
      this.isTyping = true;
      // Envoyer l'événement de début de frappe
    }

    // Réinitialiser le timer
    clearTimeout(this.typingTimeout);
    this.typingTimeout = setTimeout(() => {
      this.isTyping = false;
      // Envoyer l'événement de fin de frappe
    }, 1000);
  }

  getFilteredUsers(query: string): any[] {
    const users = [
      { id: '1', username: 'alice', name: 'Alice Martin' },
      { id: '2', username: 'bob', name: 'Bob Dupont' },
      { id: '3', username: 'claire', name: 'Claire Durand' },
    ];

    if (!query) return users;
    return users.filter(
      (user) =>
        user.username.toLowerCase().includes(query.toLowerCase()) ||
        user.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  getFilteredHashtags(query: string): any[] {
    const hashtags = [
      { id: '1', name: 'important', count: 15 },
      { id: '2', name: 'urgent', count: 8 },
      { id: '3', name: 'meeting', count: 12 },
    ];

    if (!query) return hashtags;
    return hashtags.filter((hashtag) =>
      hashtag.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  getFilteredCommands(query: string): any[] {
    const commands = [
      { id: '1', name: 'help', description: "Afficher l'aide" },
      { id: '2', name: 'clear', description: 'Effacer la conversation' },
      { id: '3', name: 'status', description: 'Changer le statut' },
    ];

    if (!query) return commands;
    return commands.filter((command) =>
      command.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  // === MÉTHODES MANQUANTES POUR LES MODALS ET FONCTIONNALITÉS ===
  openCamera(): void {
    // Ouvrir la caméra
    this.toastService.showInfo('Ouverture de la caméra...');
  }

  openLocationPicker(): void {
    this.showLocationPicker = true;
  }

  openContactPicker(): void {
    this.showContactPicker = true;
  }

  openPollCreator(): void {
    this.showPollCreator = true;
  }

  hideQuickReplies(): void {
    this.showQuickReplies = false;
  }

  insertQuickReply(reply: string): void {
    const content = this.messageForm.get('content')?.value || '';
    this.messageForm.patchValue({
      content: content + reply,
    });
    this.hideQuickReplies();
  }

  // === CORRECTION DES MÉTHODES AVEC PARAMÈTRES ===
  onSearchInput(event?: any): void {
    // Rechercher dans les messages
    if (event) {
      this.searchQuery = event.target.value;
    }
  }

  // === SYSTÈME DE RECHERCHE COMPLET ===
  performSearch(): void {
    if (!this.searchQuery.trim()) {
      this.searchResults = [];
      return;
    }

    const query = this.searchQuery.toLowerCase();
    this.searchResults = this.messages.filter((message) => {
      // Recherche dans le contenu du message
      if (message.content?.toLowerCase().includes(query)) {
        return true;
      }

      // Recherche dans les pièces jointes
      if (
        message.attachments?.some(
          (att) =>
            att.name?.toLowerCase().includes(query) ||
            att.url?.toLowerCase().includes(query)
        )
      ) {
        return true;
      }

      // Recherche dans le nom de l'expéditeur
      if (message.sender?.username?.toLowerCase().includes(query)) {
        return true;
      }

      return false;
    });

    this.toastService.showInfo(
      `${this.searchResults.length} résultat(s) trouvé(s)`
    );
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.searchResults = [];
    this.showSearch = false;
  }

  jumpToMessage(messageId: string): void {
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      messageElement.classList.add('highlight-message');
      setTimeout(() => {
        messageElement.classList.remove('highlight-message');
      }, 2000);
    }
  }

  // === SYSTÈME DE GÉOLOCALISATION COMPLET ===
  async shareLocation(): Promise<void> {
    try {
      if (!navigator.geolocation) {
        this.toastService.showError(
          "La géolocalisation n'est pas supportée par ce navigateur"
        );
        return;
      }

      this.toastService.showInfo('Obtention de votre position...');

      const position = await this.getCurrentPosition();
      const { latitude, longitude } = position.coords;

      // Créer un message de localisation
      const locationMessage = {
        type: 'location',
        latitude,
        longitude,
        address: await this.getAddressFromCoordinates(latitude, longitude),
        mapUrl: `https://maps.google.com/maps?q=${latitude},${longitude}&z=15`,
        timestamp: new Date(),
      };

      // Envoyer le message de localisation
      await this.sendLocationMessage(locationMessage);

      this.toastService.showSuccess('Position partagée avec succès');
    } catch (error) {
      console.error('Erreur lors du partage de localisation:', error);
      this.toastService.showError("Impossible d'obtenir votre position");
    }
  }

  private getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      });
    });
  }

  private async getAddressFromCoordinates(
    lat: number,
    lng: number
  ): Promise<string> {
    try {
      // Utiliser l'API de géocodage inverse (exemple avec OpenStreetMap)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
      );

      if (response.ok) {
        const data = await response.json();
        return data.display_name || `${lat}, ${lng}`;
      }
    } catch (error) {
      console.error('Erreur lors du géocodage inverse:', error);
    }

    return `${lat}, ${lng}`;
  }

  private async sendLocationMessage(locationData: any): Promise<void> {
    if (!this.conversation?.id) {
      throw new Error('Aucune conversation sélectionnée');
    }

    try {
      // Utiliser la méthode sendMessage existante avec un type spécial
      const receiverId = this.conversation?.participants?.find(
        (p) => p.id !== this.currentUserId
      )?.id;
      if (!receiverId) {
        throw new Error('Destinataire introuvable');
      }

      this.MessageService.sendMessage(
        receiverId,
        `📍 Position partagée: ${locationData.address}`,
        undefined,
        'TEXT' as any,
        this.conversation.id
      ).subscribe({
        next: (message: any) => {
          this.messages.push(message);
          this.scrollToBottom();
        },
        error: (error: any) => {
          console.error(
            "Erreur lors de l'envoi du message de localisation:",
            error
          );
          throw error;
        },
      });
    } catch (error) {
      throw error;
    }
  }

  // === SYSTÈME DE CONTACTS COMPLET ===
  async shareContact(): Promise<void> {
    try {
      // Simuler la sélection d'un contact
      const contact = await this.selectContact();

      if (contact) {
        await this.sendContactMessage(contact);
        this.toastService.showSuccess('Contact partagé avec succès');
      }
    } catch (error) {
      console.error('Erreur lors du partage de contact:', error);
      this.toastService.showError('Erreur lors du partage du contact');
    }
  }

  private async selectContact(): Promise<any> {
    // Simuler une sélection de contact
    return new Promise((resolve) => {
      // Dans une vraie application, ceci ouvrirait un sélecteur de contacts
      setTimeout(() => {
        resolve({
          name: 'John Doe',
          phone: '+33 6 12 34 56 78',
          email: '<EMAIL>',
          avatar: 'assets/images/default-avatar.png',
        });
      }, 1000);
    });
  }

  private async sendContactMessage(contactData: any): Promise<void> {
    if (!this.conversation?.id) {
      throw new Error('Aucune conversation sélectionnée');
    }

    try {
      // Utiliser la méthode sendMessage existante
      const receiverId = this.conversation?.participants?.find(
        (p) => p.id !== this.currentUserId
      )?.id;
      if (!receiverId) {
        throw new Error('Destinataire introuvable');
      }

      this.MessageService.sendMessage(
        receiverId,
        `👤 Contact partagé: ${contactData.name}\n📞 ${contactData.phone}\n📧 ${contactData.email}`,
        undefined,
        'TEXT' as any,
        this.conversation.id
      ).subscribe({
        next: (message: any) => {
          this.messages.push(message);
          this.scrollToBottom();
        },
        error: (error: any) => {
          console.error("Erreur lors de l'envoi du message de contact:", error);
          throw error;
        },
      });
    } catch (error) {
      throw error;
    }
  }

  // === MÉTHODES SUPPLÉMENTAIRES ===
  getFileAcceptTypes(): string {
    return '*/*';
  }

  closeAllMenus(): void {
    this.showEmojiPicker = false;
    this.showAttachmentMenu = false;
    this.showSearch = false;
    this.showLocationPicker = false;
    this.showContactPicker = false;
  }

  startVideoCall(): void {
    this.startCall('VIDEO' as CallType);
  }

  startVoiceCall(): void {
    this.startCall('AUDIO' as CallType);
  }

  // === MÉTHODES DE FORMATAGE ===
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs
        .toString()
        .padStart(2, '0')}`;
    }

    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  // === GESTION DES ERREURS ===
  handleError(error: any, context: string): void {
    console.error(`Erreur dans ${context}:`, error);

    let message = "Une erreur inattendue s'est produite";

    if (error?.message) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }

    this.toastService.showError(message);
  }

  // === NETTOYAGE ===
  ngOnDestroy(): void {
    // Nettoyer les timers
    if (this.callTimer) {
      clearInterval(this.callTimer);
    }

    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    // Arrêter l'enregistrement vocal si en cours
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());
    }

    // Nettoyer les abonnements
    this.subscriptions.unsubscribe();
  }
}
